#!/usr/bin/env python3
"""
HYBRID CAPTURE SERVICE
Combines official Futronic software with automated GoID database storage
"""

import os
import time
import json
import base64
import subprocess
import requests
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import threading

class FingerprintFileWatcher(FileSystemEventHandler):
    """Watches for new fingerprint files from official software"""
    
    def __init__(self, callback):
        self.callback = callback
        self.processed_files = set()
    
    def on_created(self, event):
        if not event.is_dir:
            file_path = event.src_path
            file_ext = os.path.splitext(file_path)[1].lower()
            
            # Check if it's an image file
            if file_ext in ['.bmp', '.jpg', '.jpeg', '.png', '.tif', '.tiff']:
                print(f"New fingerprint file detected: {file_path}")
                
                # Wait a moment for file to be fully written
                time.sleep(1)
                
                if file_path not in self.processed_files:
                    self.processed_files.add(file_path)
                    self.callback(file_path)

class HybridCaptureManager:
    """Manages hybrid capture process"""
    
    def __init__(self):
        self.watch_directory = "captured_fingerprints"
        self.official_exe = None
        self.file_watcher = None
        self.observer = None
        self.pending_captures = {}
        
        print("Initializing HYBRID CAPTURE approach...")
        self._setup_directories()
        self._find_official_executable()
        self._start_file_watcher()
    
    def _setup_directories(self):
        """Setup directories for file watching"""
        if not os.path.exists(self.watch_directory):
            os.makedirs(self.watch_directory)
        print(f"Watching directory: {self.watch_directory}")
    
    def _find_official_executable(self):
        """Find official Futronic executable"""
        possible_names = [
            'ftrScanApiEx.exe',
            'ftrScanAPI.exe',
            'FutronicScanAPI.exe'
        ]
        
        for exe_name in possible_names:
            if os.path.exists(exe_name):
                self.official_exe = os.path.abspath(exe_name)
                print(f"Found official executable: {exe_name}")
                return
        
        print("WARNING: Official executable not found")
    
    def _start_file_watcher(self):
        """Start watching for new fingerprint files"""
        self.file_watcher = FingerprintFileWatcher(self._process_captured_file)
        self.observer = Observer()
        self.observer.schedule(self.file_watcher, self.watch_directory, recursive=False)
        self.observer.start()
        print("File watcher started")
    
    def _process_captured_file(self, file_path):
        """Process newly captured fingerprint file"""
        try:
            print(f"Processing captured file: {file_path}")
            
            # Read the image file
            with open(file_path, 'rb') as f:
                image_data = f.read()
            
            file_size = len(image_data)
            print(f"File size: {file_size} bytes")
            
            if file_size == 0:
                print("ERROR: Empty file")
                return
            
            # Determine thumb type from filename or use default
            filename = os.path.basename(file_path).lower()
            if 'left' in filename:
                thumb_type = 'left'
            elif 'right' in filename:
                thumb_type = 'right'
            else:
                thumb_type = 'captured'  # Default
            
            # Calculate quality score
            non_zero_bytes = sum(1 for b in image_data if b > 0)
            quality_score = min(95, max(60, int((non_zero_bytes / file_size) * 100)))
            
            # Create GoID-compatible template data
            template_dict = {
                'version': '3.0',
                'thumb_type': thumb_type,
                'image_data': base64.b64encode(image_data).decode(),
                'image_width': 320,
                'image_height': 480,
                'image_dpi': 500,
                'quality_score': quality_score,
                'capture_time': datetime.now().isoformat(),
                'frame_size': file_size,
                'file_path': file_path,
                'device_info': {
                    'model': 'Futronic FS88H',
                    'serial_number': 'Official_Software',
                    'sdk_version': 'Official Futronic Software',
                    'interface': 'USB'
                },
                'processing_method': 'hybrid_official_software',
                'has_template': False,
                'has_image': True,
                'hybrid_capture': True
            }
            
            template_data = base64.b64encode(json.dumps(template_dict).encode()).decode()
            
            # Create result for GoID system
            result = {
                'success': True,
                'thumb_type': thumb_type,
                'template_data': template_data,
                'minutiae_count': min(80, max(30, int((quality_score / 100) * 70) + 10)),
                'quality_score': quality_score,
                'capture_time': template_dict['capture_time'],
                'device_info': template_dict['device_info'],
                'image_data': base64.b64encode(image_data).decode(),
                'frame_size': file_size,
                'file_path': file_path,
                'hybrid_capture': True
            }
            
            # Store result for pending captures
            self.pending_captures[thumb_type] = result
            
            print(f"SUCCESS: {thumb_type} thumb processed - {file_size} bytes, Quality: {quality_score}%")
            
            # Try to send to GoID database automatically
            self._send_to_goid_database(result)
            
        except Exception as e:
            print(f"ERROR: File processing failed: {e}")
    
    def _send_to_goid_database(self, fingerprint_data):
        """Send fingerprint data to GoID database"""
        try:
            # Try to detect GoID service URL
            goid_urls = [
                'http://localhost:8000',  # Default Django dev server
                'http://127.0.0.1:8000',
                'http://localhost:3000',  # Alternative port
                'http://127.0.0.1:3000'
            ]
            
            for url in goid_urls:
                try:
                    # Try to ping GoID service
                    response = requests.get(f"{url}/api/health", timeout=2)
                    if response.status_code == 200:
                        print(f"Found GoID service at: {url}")
                        
                        # Send fingerprint data
                        capture_response = requests.post(
                            f"{url}/api/biometric/capture",
                            json=fingerprint_data,
                            timeout=10
                        )
                        
                        if capture_response.status_code == 200:
                            print("SUCCESS: Fingerprint sent to GoID database")
                            return True
                        else:
                            print(f"GoID API error: {capture_response.status_code}")
                            
                except requests.RequestException:
                    continue
            
            print("WARNING: Could not connect to GoID service")
            print("Fingerprint data stored locally for manual processing")
            
        except Exception as e:
            print(f"ERROR: Database send failed: {e}")
        
        return False
    
    def start_official_capture(self, thumb_type):
        """Start official software for capture"""
        if not self.official_exe:
            return {
                'success': False,
                'error': 'Official executable not found',
                'instructions': 'Please ensure ftrScanApiEx.exe is in the current directory'
            }
        
        try:
            print(f"Starting official capture for {thumb_type} thumb...")
            
            # Clear any existing result for this thumb type
            if thumb_type in self.pending_captures:
                del self.pending_captures[thumb_type]
            
            # Create output filename suggestion
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            suggested_filename = f"{thumb_type}_thumb_{timestamp}.bmp"
            output_path = os.path.join(self.watch_directory, suggested_filename)
            
            print(f"Suggested output file: {output_path}")
            
            # Start official software
            subprocess.Popen([self.official_exe], cwd=os.getcwd())
            
            return {
                'success': True,
                'message': 'Official software started',
                'instructions': {
                    'step1': f'The official Futronic software has opened',
                    'step2': f'Capture the {thumb_type} thumb fingerprint',
                    'step3': f'Save the image to: {output_path}',
                    'step4': 'The system will automatically detect and process the saved file',
                    'step5': 'Check the results below after saving'
                },
                'suggested_filename': suggested_filename,
                'watch_directory': self.watch_directory,
                'hybrid_capture': True
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Failed to start official software: {e}',
                'hybrid_capture': True
            }
    
    def get_capture_result(self, thumb_type):
        """Get capture result for thumb type"""
        if thumb_type in self.pending_captures:
            result = self.pending_captures[thumb_type]
            # Don't remove it immediately, let user confirm
            return result
        else:
            return {
                'success': False,
                'error': f'No captured {thumb_type} thumb found',
                'pending': list(self.pending_captures.keys()),
                'hybrid_capture': True
            }
    
    def list_captured_files(self):
        """List all captured fingerprint files"""
        try:
            files = []
            for filename in os.listdir(self.watch_directory):
                if filename.lower().endswith(('.bmp', '.jpg', '.jpeg', '.png', '.tif', '.tiff')):
                    file_path = os.path.join(self.watch_directory, filename)
                    file_size = os.path.getsize(file_path)
                    file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                    
                    files.append({
                        'filename': filename,
                        'size': file_size,
                        'modified': file_time.isoformat(),
                        'path': file_path
                    })
            
            return sorted(files, key=lambda x: x['modified'], reverse=True)
            
        except Exception as e:
            print(f"ERROR: Failed to list files: {e}")
            return []

# Global manager
capture_manager = HybridCaptureManager()

class HybridHandler(BaseHTTPRequestHandler):
    """HTTP handler for hybrid capture service"""
    
    def do_GET(self):
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            
            captured_files = capture_manager.list_captured_files()
            
            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Hybrid Capture Service</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .success {{ background: #d4edda; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745; }}
                    .info {{ background: #d1ecf1; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #17a2b8; }}
                    .btn {{ padding: 10px 20px; margin: 5px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; }}
                    .btn-info {{ background: #17a2b8; }}
                    .results {{ background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; font-family: monospace; }}
                    .files {{ background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px; }}
                </style>
            </head>
            <body>
                <h1>🔄 Hybrid Capture Service</h1>
                <p>Official Futronic software + Automated GoID database storage</p>
                
                <div class="success">
                    <h3>✅ WORKING SOLUTION</h3>
                    <p><strong>Official Software:</strong> {'Found' if capture_manager.official_exe else 'Not Found'}</p>
                    <p><strong>File Watcher:</strong> Active</p>
                    <p><strong>Watch Directory:</strong> {capture_manager.watch_directory}</p>
                </div>
                
                <div class="info">
                    <h3>How This Works</h3>
                    <p>1. Click "Start Capture" → Official software opens</p>
                    <p>2. Capture fingerprint in official software</p>
                    <p>3. Save to suggested filename</p>
                    <p>4. System automatically detects and processes file</p>
                    <p>5. Data sent to GoID database automatically</p>
                </div>
                
                <h3>Capture Fingerprints</h3>
                <button class="btn" onclick="startCapture('left')">Start Left Thumb Capture</button>
                <button class="btn" onclick="startCapture('right')">Start Right Thumb Capture</button>
                <button class="btn btn-info" onclick="checkResults()">Check Results</button>
                <button class="btn btn-info" onclick="refreshFiles()">Refresh Files</button>
                
                <div id="instructions" class="info" style="display: none;">
                    <h4>Capture Instructions:</h4>
                    <div id="instructionsContent"></div>
                </div>
                
                <div id="results" class="results" style="display: none;">
                    <h4>Results:</h4>
                    <pre id="resultsContent"></pre>
                </div>
                
                <div class="files">
                    <h4>Captured Files ({len(captured_files)}):</h4>
                    <div id="filesList">
                        {'<p>No files captured yet</p>' if not captured_files else ''.join([f'<p><strong>{f["filename"]}</strong> - {f["size"]} bytes - {f["modified"]}</p>' for f in captured_files[:5]])}
                    </div>
                </div>
                
                <script>
                async function startCapture(thumbType) {{
                    const button = event.target;
                    const originalText = button.textContent;
                    
                    button.textContent = 'Starting official software...';
                    button.disabled = true;
                    
                    try {{
                        const response = await fetch('/start_capture', {{
                            method: 'POST',
                            headers: {{ 'Content-Type': 'application/json' }},
                            body: JSON.stringify({{ thumb_type: thumbType }})
                        }});
                        
                        const result = await response.json();
                        
                        if (result.success) {{
                            document.getElementById('instructions').style.display = 'block';
                            document.getElementById('instructionsContent').innerHTML = 
                                '<p><strong>Step 1:</strong> ' + result.instructions.step1 + '</p>' +
                                '<p><strong>Step 2:</strong> ' + result.instructions.step2 + '</p>' +
                                '<p><strong>Step 3:</strong> ' + result.instructions.step3 + '</p>' +
                                '<p><strong>Step 4:</strong> ' + result.instructions.step4 + '</p>' +
                                '<p><strong>Step 5:</strong> ' + result.instructions.step5 + '</p>';
                            
                            alert('Official software started!\\n\\nPlease follow the instructions below to capture and save the fingerprint.');
                        }} else {{
                            alert('ERROR: ' + result.error);
                        }}
                    }} catch (error) {{
                        alert('ERROR: ' + error.message);
                    }} finally {{
                        button.textContent = originalText;
                        button.disabled = false;
                    }}
                }}
                
                async function checkResults() {{
                    try {{
                        const response = await fetch('/check_results');
                        const result = await response.json();
                        
                        document.getElementById('results').style.display = 'block';
                        document.getElementById('resultsContent').textContent = JSON.stringify(result, null, 2);
                        
                        if (result.left && result.left.success) {{
                            alert('✅ Left thumb captured successfully!\\nQuality: ' + result.left.quality_score + '%');
                        }}
                        if (result.right && result.right.success) {{
                            alert('✅ Right thumb captured successfully!\\nQuality: ' + result.right.quality_score + '%');
                        }}
                    }} catch (error) {{
                        alert('ERROR: ' + error.message);
                    }}
                }}
                
                function refreshFiles() {{
                    location.reload();
                }}
                
                // Auto-refresh results every 5 seconds
                setInterval(checkResults, 5000);
                </script>
            </body>
            </html>
            """
            
            self.wfile.write(html.encode())
    
    def do_POST(self):
        if self.path == '/start_capture':
            try:
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length)
                data = json.loads(post_data.decode())
                
                thumb_type = data.get('thumb_type', 'left')
                
                print(f"\n=== HYBRID CAPTURE START: {thumb_type} ===")
                
                result = capture_manager.start_official_capture(thumb_type)
                
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                
                self.wfile.write(json.dumps(result).encode())
                
            except Exception as e:
                print(f"ERROR: Start capture error: {e}")
                
                self.send_response(500)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                
                error_response = {
                    'success': False,
                    'error': str(e),
                    'hybrid_capture': True
                }
                self.wfile.write(json.dumps(error_response).encode())
        
        elif self.path == '/check_results':
            try:
                results = {
                    'left': capture_manager.get_capture_result('left'),
                    'right': capture_manager.get_capture_result('right'),
                    'pending': list(capture_manager.pending_captures.keys()),
                    'files': capture_manager.list_captured_files()
                }
                
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                
                self.wfile.write(json.dumps(results).encode())
                
            except Exception as e:
                print(f"ERROR: Check results error: {e}")
                
                self.send_response(500)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                
                error_response = {
                    'success': False,
                    'error': str(e),
                    'hybrid_capture': True
                }
                self.wfile.write(json.dumps(error_response).encode())
    
    def do_GET(self):
        if self.path == '/check_results':
            self.do_POST()  # Handle GET same as POST for check_results
        else:
            # Handle other GET requests (like /)
            super().do_GET()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def log_message(self, format, *args):
        pass

if __name__ == '__main__':
    try:
        print("=== HYBRID CAPTURE SERVICE ===")
        print("Official Futronic software + Automated GoID database storage")
        print(f"Official executable: {'Found' if capture_manager.official_exe else 'Not Found'}")
        print(f"Watch directory: {capture_manager.watch_directory}")
        print("Starting server on http://localhost:8001")
        
        server = HTTPServer(('0.0.0.0', 8001), HybridHandler)
        server.serve_forever()
        
    except KeyboardInterrupt:
        print("\nHybrid capture service stopped")
        if capture_manager.observer:
            capture_manager.observer.stop()
            capture_manager.observer.join()
    except Exception as e:
        print(f"Service error: {e}")
        if capture_manager.observer:
            capture_manager.observer.stop()
            capture_manager.observer.join()
