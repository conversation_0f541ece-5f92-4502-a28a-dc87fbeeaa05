#!/usr/bin/env python3
"""
Simple Capture Service - Direct fingerprint capture like official software
"""

import logging
import ctypes
from ctypes import cdll, c_int, c_uint, c_void_p, POINTER, byref
import base64
import json
from datetime import datetime
from flask import Flask, jsonify, request

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Constants
FTR_OK = 0
FTR_IMAGE_SIZE = 320 * 480  # Standard Futronic image size

class SimpleFingerprintDevice:
    def __init__(self):
        self.scan_api = None
        self.device_handle = None
        self.connected = False
    
    def initialize(self):
        """Initialize device"""
        try:
            logger.info("🔧 Loading Futronic SDK...")
            self.scan_api = cdll.LoadLibrary('./ftrScanAPI.dll')
            
            # Setup function signatures
            self.scan_api.ftrScanOpenDevice.restype = c_void_p
            self.scan_api.ftrScanCloseDevice.argtypes = [c_void_p]
            self.scan_api.ftrScanCloseDevice.restype = c_int
            self.scan_api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(ctypes.c_ubyte), POINTER(c_uint)]
            self.scan_api.ftrScanGetFrame.restype = c_int
            
            logger.info("📱 Opening device...")
            self.device_handle = self.scan_api.ftrScanOpenDevice()
            
            if self.device_handle:
                logger.info(f"✅ Device opened! Handle: {self.device_handle}")
                self.connected = True
                return True
            else:
                logger.error("❌ Failed to open device")
                return False
                
        except Exception as e:
            logger.error(f"❌ Initialization error: {e}")
            return False
    
    def capture_simple(self, thumb_type='left'):
        """Simple direct capture - place finger first, then call this"""
        try:
            if not self.connected:
                return {
                    'success': False,
                    'error': 'Device not connected'
                }
            
            logger.info(f"📸 Capturing {thumb_type} thumb (place finger on scanner now)...")
            
            # Create buffer for image
            image_buffer = (ctypes.c_ubyte * FTR_IMAGE_SIZE)()
            frame_size = c_uint(FTR_IMAGE_SIZE)
            
            # Direct capture attempt
            logger.info("🔍 Attempting frame capture...")
            result = self.scan_api.ftrScanGetFrame(self.device_handle, image_buffer, byref(frame_size))
            
            logger.info(f"📊 Capture result: code={result}, size={frame_size.value}")
            
            if result == FTR_OK:
                # Analyze captured data
                actual_size = frame_size.value
                
                if actual_size > 1000:  # Reasonable frame size
                    # Convert to bytes for analysis
                    image_bytes = bytes(image_buffer[:actual_size])
                    
                    # Basic quality analysis
                    sample_size = min(1000, actual_size)
                    sample_pixels = list(image_bytes[:sample_size])
                    
                    non_zero_pixels = sum(1 for p in sample_pixels if p > 10)
                    data_percentage = (non_zero_pixels / sample_size) * 100
                    avg_pixel = sum(sample_pixels) / len(sample_pixels)
                    
                    # Calculate quality score
                    quality_score = min(100, max(0, int(data_percentage * 1.2 + avg_pixel * 0.5)))
                    
                    # Create template
                    template_data = {
                        'version': '1.0',
                        'thumb_type': thumb_type,
                        'frame_size': actual_size,
                        'quality_score': quality_score,
                        'capture_time': datetime.now().isoformat(),
                        'device_info': {
                            'model': 'Futronic FS88H',
                            'interface': 'simple_direct',
                            'real_device': True
                        },
                        'image_hash': hash(image_bytes) & 0x7FFFFFFF
                    }
                    
                    template_encoded = base64.b64encode(json.dumps(template_data).encode()).decode()
                    
                    logger.info(f"✅ Capture successful!")
                    logger.info(f"📊 Frame size: {actual_size} bytes")
                    logger.info(f"📊 Quality: {quality_score}%")
                    logger.info(f"📊 Data: {data_percentage:.1f}%")
                    
                    return {
                        'success': True,
                        'data': {
                            'thumb_type': thumb_type,
                            'template_data': template_encoded,
                            'quality_score': quality_score,
                            'minutiae_count': max(20, min(60, quality_score // 2)),
                            'capture_time': template_data['capture_time'],
                            'device_info': template_data['device_info'],
                            'frame_size': actual_size,
                            'data_percentage': round(data_percentage, 1)
                        }
                    }
                else:
                    return {
                        'success': False,
                        'error': f'Frame too small: {actual_size} bytes - please place finger on scanner'
                    }
            else:
                error_messages = {
                    1: 'Device error - please try again',
                    2: 'No frame available - place finger on scanner',
                    3: 'Empty frame - ensure finger is properly placed',
                    4: 'Finger moved - hold finger steady'
                }
                
                error_msg = error_messages.get(result, f'Capture failed with code {result}')
                
                return {
                    'success': False,
                    'error': error_msg
                }
                
        except Exception as e:
            logger.error(f"❌ Capture error: {e}")
            return {
                'success': False,
                'error': f'Capture exception: {str(e)}'
            }
    
    def get_status(self):
        """Get device status"""
        return {
            'connected': self.connected,
            'handle': self.device_handle,
            'model': 'Futronic FS88H',
            'interface': 'simple_direct'
        }
    
    def close(self):
        """Close device"""
        if self.device_handle and self.scan_api:
            try:
                self.scan_api.ftrScanCloseDevice(self.device_handle)
                logger.info("🔒 Device closed")
            except Exception as e:
                logger.error(f"Close error: {e}")

# Global device
device = SimpleFingerprintDevice()

@app.route('/api/device/status', methods=['GET'])
def get_device_status():
    """Get device status"""
    return jsonify({
        'success': True,
        'data': device.get_status()
    })

@app.route('/api/capture/simple', methods=['GET', 'POST'])
def capture_simple():
    """Simple capture - place finger first, then call this"""
    if request.method == 'GET':
        thumb_type = request.args.get('thumb_type', 'left')
    else:
        data = request.get_json() or {}
        thumb_type = data.get('thumb_type', 'left')
    
    result = device.capture_simple(thumb_type)
    
    if result['success']:
        return jsonify(result)
    else:
        return jsonify(result), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check"""
    return jsonify({
        'status': 'healthy',
        'service': 'Simple Capture Service',
        'timestamp': datetime.now().isoformat(),
        'device_connected': device.connected
    })

@app.route('/', methods=['GET'])
def index():
    """Web interface"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Simple Fingerprint Capture</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
            button { padding: 15px 30px; margin: 10px; font-size: 16px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
            button:hover { background: #0056b3; }
            .result { margin: 20px 0; padding: 15px; border-radius: 5px; }
            .success { background: #d4edda; color: #155724; }
            .error { background: #f8d7da; color: #721c24; }
            .instructions { background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🔒 Simple Fingerprint Capture</h1>
            
            <div class="instructions">
                <h3>📋 Instructions:</h3>
                <ol>
                    <li><strong>Place your finger</strong> firmly on the scanner</li>
                    <li><strong>Click capture button</strong> while finger is on scanner</li>
                    <li><strong>Hold still</strong> until capture completes</li>
                </ol>
            </div>
            
            <div style="text-align: center;">
                <button onclick="captureFingerprint('left')">👈 Capture Left Thumb</button>
                <button onclick="captureFingerprint('right')">👉 Capture Right Thumb</button>
            </div>
            
            <div id="result"></div>
        </div>
        
        <script>
            async function captureFingerprint(thumbType) {
                const resultDiv = document.getElementById('result');
                resultDiv.innerHTML = '<div class="result">📸 Capturing... Keep finger on scanner!</div>';
                
                try {
                    const response = await fetch(`/api/capture/simple?thumb_type=${thumbType}`);
                    const data = await response.json();
                    
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="result success">
                                <h3>✅ Fingerprint Captured!</h3>
                                <p><strong>Thumb:</strong> ${data.data.thumb_type}</p>
                                <p><strong>Quality:</strong> ${data.data.quality_score}%</p>
                                <p><strong>Frame Size:</strong> ${data.data.frame_size} bytes</p>
                                <p><strong>Data:</strong> ${data.data.data_percentage}%</p>
                                <p><strong>Minutiae:</strong> ${data.data.minutiae_count}</p>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="result error">
                                <h3>❌ Capture Failed</h3>
                                <p>${data.error}</p>
                                <p><em>Try placing finger more firmly on scanner</em></p>
                            </div>
                        `;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ Network Error</h3>
                            <p>${error.message}</p>
                        </div>
                    `;
                }
            }
        </script>
    </body>
    </html>
    """

if __name__ == '__main__':
    try:
        logger.info("🚀 Starting Simple Capture Service")
        
        # Initialize device
        if device.initialize():
            logger.info("✅ Device ready for simple capture")
        else:
            logger.error("❌ Device initialization failed")
            exit(1)
        
        # Start service
        logger.info("🌐 Service running at http://localhost:8001")
        logger.info("📋 Instructions: Place finger on scanner FIRST, then click capture")
        
        app.run(host='0.0.0.0', port=8001, debug=False, threaded=True)
        
    except KeyboardInterrupt:
        logger.info("🛑 Service stopped")
    except Exception as e:
        logger.error(f"❌ Service error: {e}")
    finally:
        device.close()
