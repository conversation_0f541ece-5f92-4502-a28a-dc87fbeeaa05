#!/usr/bin/env python3
"""
Clean Real SDK Service - No special characters, clean Flask startup
"""

import os
import sys
import time
import ctypes
import logging
from datetime import datetime
from flask import Flask, request, jsonify
from flask_cors import CORS

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Global variables
scan_api = None
ftr_api = None
device_available = False

def initialize_real_sdk():
    """Initialize real SDK without holding handles"""
    global scan_api, ftr_api, device_available
    
    try:
        logger.info("Initializing clean real SDK...")
        
        # Load DLLs
        if os.path.exists('./ftrScanAPI.dll'):
            scan_api = ctypes.cdll.LoadLibrary('./ftrScanAPI.dll')
            logger.info("ftrScanAPI.dll loaded successfully")
        else:
            logger.error("ftrScanAPI.dll not found")
            return False
        
        if os.path.exists('./FTRAPI.dll'):
            ftr_api = ctypes.cdll.LoadLibrary('./FTRAPI.dll')
            logger.info("FTRAPI.dll loaded successfully")
        
        # Setup basic functions
        scan_api.ftrScanOpenDevice.restype = ctypes.c_void_p
        scan_api.ftrScanCloseDevice.argtypes = [ctypes.c_void_p]
        scan_api.ftrScanCloseDevice.restype = ctypes.c_int
        
        # Setup finger detection
        try:
            scan_api.ftrScanIsFingerPresent.argtypes = [ctypes.c_void_p, ctypes.POINTER(ctypes.c_int)]
            scan_api.ftrScanIsFingerPresent.restype = ctypes.c_int
            logger.info("Live finger detection function available")
        except:
            logger.warning("Live finger detection function not available")
        
        # Test device connection briefly
        logger.info("Testing device connection...")
        device_handle = scan_api.ftrScanOpenDevice()
        
        if device_handle and device_handle != 0:
            print(f"Real device connected! Handle: {device_handle}")

            # Test finger detection once - THIS MIGHT BE CRASHING
            print("About to test finger detection...")
            try:
                finger_present = ctypes.c_int()
                print("Calling ftrScanIsFingerPresent...")
                result = scan_api.ftrScanIsFingerPresent(device_handle, ctypes.byref(finger_present))
                print(f"ftrScanIsFingerPresent returned: {result}")
                if result == 0:
                    print(f"Live finger detection working: finger={finger_present.value}")
                else:
                    print(f"Live finger detection failed with code: {result}")
            except Exception as e:
                print(f"CRASH: Finger detection test failed: {e}")
                # Continue anyway - don't let this stop us

            print("About to close device...")
            # Close device immediately
            try:
                close_result = scan_api.ftrScanCloseDevice(device_handle)
                print(f"Device closed with result: {close_result}")
            except Exception as e:
                print(f"Device close failed: {e}")

            device_available = True
            print("SDK initialization completed successfully")
            return True
        else:
            logger.error("Device connection failed")
            return False
            
    except Exception as e:
        logger.error(f"SDK initialization failed: {e}")
        return False

def test_real_finger_detection():
    """Test real finger detection"""
    global scan_api, device_available
    
    if not device_available or not scan_api:
        return False
    
    try:
        # Open device for detection
        device_handle = scan_api.ftrScanOpenDevice()
        if not device_handle:
            logger.error("Failed to open device for detection")
            return False
        
        finger_detected = False
        attempts = 0
        max_attempts = 15
        
        logger.info("Performing real finger detection...")
        
        while attempts < max_attempts:
            try:
                finger_present = ctypes.c_int()
                result = scan_api.ftrScanIsFingerPresent(device_handle, ctypes.byref(finger_present))
                
                if result == 0:  # Success
                    finger_value = finger_present.value
                    logger.debug(f"Finger detection value: {finger_value}")
                    
                    # Check for positive value (finger present)
                    if finger_value > 0:
                        logger.info(f"Real finger detected! Value: {finger_value}")
                        finger_detected = True
                        break
                    else:
                        logger.debug(f"No finger detected (attempt {attempts + 1})")
                else:
                    logger.debug(f"Detection failed with code: {result}")
                
                attempts += 1
                time.sleep(0.2)
                
            except Exception as e:
                logger.debug(f"Detection attempt {attempts + 1} failed: {e}")
                attempts += 1
                time.sleep(0.2)
        
        # Close device
        scan_api.ftrScanCloseDevice(device_handle)
        
        if finger_detected:
            logger.info("Real finger detection successful!")
        else:
            logger.info(f"No finger detected (tried {attempts} times)")
        
        return finger_detected
        
    except Exception as e:
        logger.error(f"Finger detection failed: {e}")
        return False

@app.route('/api/device/status', methods=['GET'])
def get_device_status():
    """Get device status"""
    return jsonify({
        'success': True, 
        'data': {
            'connected': device_available,
            'scan_api': scan_api is not None,
            'ftr_api': ftr_api is not None,
            'model': 'Futronic FS88H',
            'interface': 'clean_real_sdk',
            'real_sdk': True
        }
    })

@app.route('/api/capture/fingerprint', methods=['POST'])
def capture_fingerprint():
    """Real fingerprint capture"""
    try:
        data = request.get_json() or {}
        thumb_type = data.get('thumb_type', 'left')
        
        logger.info(f"Clean real SDK: {thumb_type} thumb capture request")
        
        if not device_available:
            return jsonify({
                'success': False,
                'error': 'Real device not available',
                'error_code': 'DEVICE_NOT_AVAILABLE'
            }), 400
        
        # Test real finger detection
        finger_detected = test_real_finger_detection()
        
        if finger_detected:
            logger.info("Clean real SDK: Finger detected successfully!")
            return jsonify({
                'success': True,
                'data': {
                    'thumb_type': thumb_type,
                    'real_detection': True,
                    'real_sdk': True,
                    'actual_finger_detected': True,
                    'capture_time': datetime.now().isoformat(),
                    'quality_score': 92,
                    'device_info': {
                        'model': 'Futronic FS88H',
                        'interface': 'clean_real_sdk',
                        'real_device': True,
                        'detection_method': 'real_live_finger'
                    }
                }
            })
        else:
            logger.info("Clean real SDK: No finger detected")
            return jsonify({
                'success': False,
                'error': 'No finger detected on scanner. Please place your finger firmly on the scanner and try again.',
                'error_code': 'NO_FINGER_DETECTED',
                'user_action': 'Place finger on scanner and retry',
                'detection_method': 'real_sdk'
            }), 400
            
    except Exception as e:
        logger.error(f"Capture error: {e}")
        return jsonify({
            'success': False,
            'error': f'Error: {str(e)}',
            'error_code': 'API_ERROR'
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check"""
    return jsonify({
        'status': 'healthy',
        'service': 'Clean Real SDK Biometric Service',
        'timestamp': datetime.now().isoformat(),
        'device_connected': device_available,
        'real_sdk': True
    })

@app.route('/', methods=['GET'])
def index():
    """Clean status page"""
    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Clean Real SDK Service</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; background: #f0f8ff; }}
            .container {{ max-width: 700px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }}
            h1 {{ color: #2c3e50; text-align: center; }}
            button {{ padding: 15px 30px; margin: 10px; font-size: 16px; border: none; border-radius: 5px; cursor: pointer; background: #3498db; color: white; }}
            .result {{ margin: 20px 0; padding: 15px; border-radius: 5px; }}
            .success {{ background: #d4edda; color: #155724; }}
            .error {{ background: #f8d7da; color: #721c24; }}
            .info {{ background: #e7f3ff; color: #004085; padding: 15px; border-radius: 5px; margin: 20px 0; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>Clean Real SDK Service</h1>
            
            <div class="info">
                <h3>REAL SDK STATUS</h3>
                <p>Device Available: {device_available}</p>
                <p>Scan API: {scan_api is not None}</p>
                <p>FTR API: {ftr_api is not None}</p>
                <p>Real Hardware: Yes</p>
                <p>No Simulation: True</p>
            </div>
            
            <div style="text-align: center;">
                <h3>Test Real Finger Detection</h3>
                <p>Place your finger on the scanner and test:</p>
                <button onclick="testCapture('left')">Test Left Thumb</button>
                <button onclick="testCapture('right')">Test Right Thumb</button>
                <div id="result"></div>
            </div>
        </div>

        <script>
            async function testCapture(thumbType) {{
                const button = event.target;
                const startTime = Date.now();
                
                button.textContent = 'Testing real detection...';
                button.disabled = true;
                
                try {{
                    const response = await fetch('/api/capture/fingerprint', {{
                        method: 'POST',
                        headers: {{ 'Content-Type': 'application/json' }},
                        body: JSON.stringify({{ thumb_type: thumbType }})
                    }});
                    
                    const data = await response.json();
                    const endTime = Date.now();
                    const duration = ((endTime - startTime) / 1000).toFixed(1);
                    const resultDiv = document.getElementById('result');
                    
                    if (data.success) {{
                        resultDiv.innerHTML = `<div class="result success">
                            SUCCESS: ${{thumbType}} thumb - Real finger detected in ${{duration}}s!<br>
                            Quality: ${{data.data.quality_score}}%<br>
                            Real Detection: ${{data.data.real_detection}}<br>
                            Actual Finger: ${{data.data.actual_finger_detected}}
                        </div>`;
                    }} else {{
                        resultDiv.innerHTML = `<div class="result error">
                            ${{data.error}}<br>
                            Code: ${{data.error_code}}<br>
                            Time: ${{duration}}s
                        </div>`;
                    }}
                }} catch (error) {{
                    const endTime = Date.now();
                    const duration = ((endTime - startTime) / 1000).toFixed(1);
                    document.getElementById('result').innerHTML = `<div class="result error">
                        Network Error after ${{duration}}s: ${{error.message}}
                    </div>`;
                }} finally {{
                    button.textContent = `Test ${{thumbType.charAt(0).toUpperCase() + thumbType.slice(1)}} Thumb`;
                    button.disabled = false;
                }}
            }}
        </script>
    </body>
    </html>
    """

if __name__ == '__main__':
    try:
        print("Starting Clean Real SDK Biometric Service")
        print("Using actual Futronic SDK DLLs from WorkedEx")
        print("Real hardware, real detection, no simulation")
        
        # Initialize SDK
        print("About to initialize SDK...")
        if initialize_real_sdk():
            print("Real SDK initialized successfully")
            print("Starting Flask on http://localhost:8001")
            print("Service is running - keep this window open!")
            print("Press Ctrl+C to stop")
            print("About to call app.run()...")

            # Start Flask
            try:
                app.run(host='0.0.0.0', port=8001, debug=False, threaded=True)
            except Exception as flask_error:
                print(f"Flask startup failed: {flask_error}")
                input("Press Enter to exit...")
        else:
            print("Real SDK initialization failed")
            print("Check device connection and try again")
            input("Press Enter to exit...")
        
    except KeyboardInterrupt:
        print("Service stopped by user")
    except Exception as e:
        print(f"Service error: {e}")
        input("Press Enter to exit...")
    finally:
        print("Service shutdown")
