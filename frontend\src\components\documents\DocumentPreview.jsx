import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Box,
  Typography,
  CircularProgress,
  Alert,
  IconButton,
  Chip,
  Divider,
  Grid,
  Paper
} from '@mui/material';
import {
  Close as CloseIcon,
  Download as DownloadIcon,
  Visibility as PreviewIcon,
  PictureAsPdf as PdfIcon,
  Image as ImageIcon,
  Description as DocumentIcon,
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  FullscreenExit as FullscreenExitIcon,
  Fullscreen as FullscreenIcon
} from '@mui/icons-material';
import axios from '../../utils/axios';

const DocumentPreview = ({ open, onClose, document: documentData, tenantId }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [fileUrl, setFileUrl] = useState('');
  const [fileType, setFileType] = useState('');
  const [zoom, setZoom] = useState(100);
  const [fullscreen, setFullscreen] = useState(false);
  const [pdfLoadFailed, setPdfLoadFailed] = useState(false);

  useEffect(() => {
    if (open && documentData) {
      loadDocument();
    }
    return () => {
      // Cleanup blob URL when component unmounts or dialog closes
      if (fileUrl && fileUrl.startsWith('blob:')) {
        URL.revokeObjectURL(fileUrl);
      }
    };
  }, [open, documentData]);

  const loadDocument = async () => {
    try {
      setLoading(true);
      setError('');

      console.log('🔍 Loading document for preview:', documentData);

      // Get file type from URL or document info
      const fileExtension = documentData.document_file.split('.').pop().toLowerCase();
      setFileType(fileExtension);

      // For PDFs, we can try direct URL first, then fallback to blob
      if (fileExtension === 'pdf') {
        // Convert absolute URL to relative URL for proxy
        const relativeUrl = documentData.document_file.replace('http://10.139.8.141:8000', '');
        console.log('🔍 Setting PDF URL for preview:', relativeUrl);
        setFileUrl(relativeUrl);
        setPdfLoadFailed(false);
        setLoading(false);

        // Test if the URL is accessible
        fetch(relativeUrl, { method: 'HEAD' })
          .then(response => {
            console.log('🔍 PDF URL accessibility test:', response.status, response.statusText);
            if (!response.ok) {
              console.warn('⚠️ PDF URL may not be accessible:', response.status);
              setPdfLoadFailed(true);
            }
          })
          .catch(error => {
            console.error('❌ PDF URL accessibility test failed:', error);
            setPdfLoadFailed(true);
          });

        // Set a timeout to detect if PDF fails to load in iframe
        setTimeout(() => {
          // This is a simple timeout - in a real app you might want more sophisticated detection
          console.log('🔍 PDF load timeout - checking if preview is working');
        }, 3000);

        return;
      }

      // For other file types, load as blob
      try {
        const relativeUrl = documentData.document_file.replace('http://10.139.8.141:8000', '');
        const response = await axios.get(relativeUrl, {
          responseType: 'blob',
          headers: {
            'Accept': '*/*'
          }
        });

        const blob = new Blob([response.data]);
        const url = URL.createObjectURL(blob);
        setFileUrl(url);
      } catch (blobError) {
        console.warn('Blob loading failed, trying direct URL:', blobError);
        // Fallback to relative URL
        const relativeUrl = documentData.document_file.replace('http://10.139.8.141:8000', '');
        setFileUrl(relativeUrl);
      }

    } catch (error) {
      console.error('Error loading document:', error);
      setError('Failed to load document for preview');
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async () => {
    try {
      const relativeUrl = documentData.document_file.replace('http://10.139.8.141:8000', '');
      console.log('🔍 Downloading document:', relativeUrl);

      // Try direct download first
      const link = document.createElement('a');
      link.href = relativeUrl;
      link.setAttribute('download', `${documentData.document_type_name}.${documentData.document_file.split('.').pop()}`);
      link.setAttribute('target', '_blank');
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

    } catch (error) {
      console.error('Error downloading document:', error);
      setError('Failed to download document');
    }
  };

  const getFileIcon = (type) => {
    if (type === 'pdf') return <PdfIcon color="error" />;
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(type)) return <ImageIcon color="primary" />;
    return <DocumentIcon color="action" />;
  };

  const getFileTypeChip = (type) => {
    const colors = {
      pdf: 'error',
      jpg: 'primary', jpeg: 'primary', png: 'primary', gif: 'primary', bmp: 'primary', webp: 'primary',
      doc: 'info', docx: 'info',
      txt: 'default'
    };
    return (
      <Chip
        icon={getFileIcon(type)}
        label={type.toUpperCase()}
        color={colors[type] || 'default'}
        size="small"
      />
    );
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Not specified';
    return new Date(dateString).toLocaleDateString();
  };

  const formatFileSize = (bytes) => {
    if (!bytes) return 'Unknown size';
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const renderPreview = () => {
    if (loading) {
      return (
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
          <Typography variant="body2" sx={{ ml: 2 }}>Loading document...</Typography>
        </Box>
      );
    }

    if (error) {
      return (
        <Alert severity="error" sx={{ m: 2 }}>
          {error}
        </Alert>
      );
    }

    if (!documentData) {
      return (
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <Typography variant="body2" color="text.secondary">
            No document data available
          </Typography>
        </Box>
      );
    }

    if (!fileUrl) {
      return (
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <Typography variant="body2" color="text.secondary">
            No preview available
          </Typography>
        </Box>
      );
    }

    // Image preview
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(fileType)) {
      return (
        <Box
          sx={{
            textAlign: 'center',
            p: 2,
            maxHeight: fullscreen ? '80vh' : '500px',
            overflow: 'auto'
          }}
        >
          <img
            src={fileUrl}
            alt={documentData?.document_type_name || 'Document'}
            style={{
              maxWidth: '100%',
              height: 'auto',
              transform: `scale(${zoom / 100})`,
              transition: 'transform 0.2s ease'
            }}
          />
        </Box>
      );
    }

    // PDF preview
    if (fileType === 'pdf') {
      // If PDF load failed, show fallback card
      if (pdfLoadFailed) {
        return (
          <Box sx={{ height: fullscreen ? '80vh' : '500px', width: '100%', position: 'relative' }}>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%',
                backgroundColor: 'grey.50',
                border: '2px dashed',
                borderColor: 'grey.300',
                borderRadius: 2,
                p: 4
              }}
            >
              <PdfIcon sx={{ fontSize: 80, color: 'error.main', mb: 2 }} />
              <Typography variant="h5" gutterBottom color="text.primary">
                {documentData?.document_type_name || 'PDF Document'}
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ mb: 1, textAlign: 'center' }}>
                PDF preview is not available in this modal.
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3, textAlign: 'center' }}>
                Use the buttons below to view or download the document.
              </Typography>

              {/* Document Info */}
              <Box sx={{ mb: 3, textAlign: 'center' }}>
                <Typography variant="body2" color="text.secondary">
                  <strong>Issue Date:</strong> {formatDate(documentData?.issue_date)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  <strong>Expiry Date:</strong> {formatDate(documentData?.expiry_date)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  <strong>Uploaded:</strong> {formatDate(documentData?.created_at)}
                </Typography>
              </Box>

              {/* Action Buttons */}
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', justifyContent: 'center' }}>
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<PreviewIcon />}
                  onClick={() => {
                    const relativeUrl = documentData.document_file.replace('http://10.139.8.141:8000', '');
                    console.log('🔍 Opening PDF in new tab:', relativeUrl);
                    window.open(relativeUrl, '_blank');
                  }}
                >
                  Open in New Tab
                </Button>
                <Button
                  variant="outlined"
                  size="large"
                  startIcon={<DownloadIcon />}
                  onClick={handleDownload}
                >
                  Download PDF
                </Button>
              </Box>
            </Box>
          </Box>
        );
      }

      // Try iframe preview first
      return (
        <Box sx={{ height: fullscreen ? '80vh' : '500px', width: '100%', position: 'relative' }}>
          {/* Try iframe first with the proxied URL */}
          <iframe
            src={fileUrl}
            width="100%"
            height="100%"
            style={{ border: 'none' }}
            title={documentData?.document_type_name || 'PDF Document'}
            onLoad={(e) => {
              console.log('🔍 PDF iframe loaded successfully');
              setPdfLoadFailed(false);
            }}
            onError={(e) => {
              console.log('❌ PDF iframe failed to load');
              setPdfLoadFailed(true);
            }}
          />

          {/* Floating action buttons */}
          <Box
            sx={{
              position: 'absolute',
              top: 16,
              right: 16,
              display: 'flex',
              gap: 1,
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              borderRadius: 1,
              p: 1,
              boxShadow: 2,
              zIndex: 1
            }}
          >
            <Button
              size="small"
              variant="contained"
              startIcon={<PreviewIcon />}
              onClick={() => {
                const relativeUrl = documentData.document_file.replace('http://10.139.8.141:8000', '');
                console.log('🔍 Opening PDF in new tab:', relativeUrl);
                window.open(relativeUrl, '_blank');
              }}
            >
              New Tab
            </Button>
            <Button
              size="small"
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={handleDownload}
            >
              Download
            </Button>
          </Box>
        </Box>
      );
    }

    // Fallback for other file types
    return (
      <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" minHeight="400px">
        {getFileIcon(fileType)}
        <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>
          {documentData?.document_type_name || 'Document'}
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Preview not available for this file type
        </Typography>
        <Button
          variant="contained"
          startIcon={<DownloadIcon />}
          onClick={handleDownload}
        >
          Download to View
        </Button>
      </Box>
    );
  };

  const canZoom = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(fileType);

  // Don't render if no document data
  if (!open) {
    return null;
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth={fullscreen ? false : "lg"}
      fullWidth
      fullScreen={fullscreen}
      PaperProps={{
        sx: {
          minHeight: fullscreen ? '100vh' : '600px',
          maxHeight: fullscreen ? '100vh' : '90vh'
        }
      }}
    >
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', pb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {fileType === 'pdf' ? <PdfIcon color="error" /> : <PreviewIcon color="primary" />}
          <Typography variant="h6">
            {fileType === 'pdf' ? 'PDF Document' : 'Document Preview'}
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {canZoom && fileType !== 'pdf' && (
            <>
              <IconButton size="small" onClick={() => setZoom(Math.max(25, zoom - 25))}>
                <ZoomOutIcon />
              </IconButton>
              <Typography variant="body2" sx={{ minWidth: '50px', textAlign: 'center' }}>
                {zoom}%
              </Typography>
              <IconButton size="small" onClick={() => setZoom(Math.min(200, zoom + 25))}>
                <ZoomInIcon />
              </IconButton>
            </>
          )}
          {fileType !== 'pdf' && (
            <IconButton size="small" onClick={() => setFullscreen(!fullscreen)}>
              {fullscreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
            </IconButton>
          )}
          <IconButton onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <Divider />

      {/* Document Info */}
      <Box sx={{ p: 2, backgroundColor: 'grey.50' }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="subtitle1" fontWeight="bold">
                {documentData?.document_type_name}
              </Typography>
              {getFileTypeChip(fileType)}
            </Box>
            <Typography variant="body2" color="text.secondary">
              Issue Date: {formatDate(documentData?.issue_date)} |
              Expiry Date: {formatDate(documentData?.expiry_date)}
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6} sx={{ textAlign: { sm: 'right' } }}>
            <Typography variant="body2" color="text.secondary">
              Uploaded: {formatDate(documentData?.created_at)}
            </Typography>
          </Grid>
        </Grid>
      </Box>

      <DialogContent sx={{ p: 0, flex: 1 }}>
        {renderPreview()}
      </DialogContent>

      <DialogActions sx={{ p: 2, gap: 1 }}>
        <Button
          variant="outlined"
          startIcon={<DownloadIcon />}
          onClick={handleDownload}
        >
          Download
        </Button>
        <Button onClick={onClose}>
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DocumentPreview;
