#!/usr/bin/env python3
"""
Test basic scanning functionality without template creation
"""

import ctypes
from ctypes import cdll, c_int, c_uint, c_void_p, POINTER, byref
import logging
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Constants
FTR_OK = 0
FTR_IMAGE_WIDTH = 320
FTR_IMAGE_HEIGHT = 480
FTR_IMAGE_SIZE = FTR_IMAGE_WIDTH * FTR_IMAGE_HEIGHT

def test_basic_device_operations():
    """Test basic device operations using scan API only"""
    try:
        logger.info("🔧 Testing basic Futronic device operations...")
        
        # Load scan API
        scan_api = cdll.LoadLibrary('./ftrScanAPI.dll')
        
        # Setup function signatures
        scan_api.ftrScanOpenDevice.restype = c_void_p
        scan_api.ftrScanCloseDevice.argtypes = [c_void_p]
        scan_api.ftrScanCloseDevice.restype = c_int
        scan_api.ftrScanIsFingerPresent.argtypes = [c_void_p, POINTER(c_int)]
        scan_api.ftrScanIsFingerPresent.restype = c_int
        scan_api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(ctypes.c_ubyte), POINTER(c_uint)]
        scan_api.ftrScanGetFrame.restype = c_int
        scan_api.ftrScanGetImageSize.argtypes = [c_void_p, POINTER(c_int), POINTER(c_int)]
        scan_api.ftrScanGetImageSize.restype = c_int
        
        # Step 1: Open device
        logger.info("📱 Opening device...")
        device_handle = scan_api.ftrScanOpenDevice()
        
        if not device_handle:
            logger.error("❌ Failed to open device")
            return False
        
        logger.info(f"✅ Device opened successfully! Handle: {device_handle}")
        
        # Step 2: Get image size
        logger.info("📏 Getting image dimensions...")
        width = c_int()
        height = c_int()
        result = scan_api.ftrScanGetImageSize(device_handle, byref(width), byref(height))
        
        if result == FTR_OK:
            logger.info(f"✅ Image size: {width.value} x {height.value}")
        else:
            logger.warning(f"⚠️ Could not get image size: {result}")
            width.value = FTR_IMAGE_WIDTH
            height.value = FTR_IMAGE_HEIGHT
        
        # Step 3: Test finger detection
        logger.info("👆 Testing finger detection...")
        logger.info("Please place your finger on the scanner...")
        
        finger_detected = False
        timeout = 10  # 10 second timeout
        start_time = time.time()
        
        while not finger_detected and (time.time() - start_time) < timeout:
            finger_present = c_int()
            result = scan_api.ftrScanIsFingerPresent(device_handle, byref(finger_present))
            
            if result == FTR_OK:
                if finger_present.value:
                    finger_detected = True
                    logger.info("✅ Finger detected!")
                else:
                    print(".", end="", flush=True)
                    time.sleep(0.5)
            else:
                logger.warning(f"⚠️ Finger detection error: {result}")
                break
        
        if not finger_detected:
            logger.info("\n⚠️ No finger detected within timeout - continuing with frame capture test")
        
        # Step 4: Try to capture a frame
        logger.info("📸 Attempting to capture frame...")
        
        image_size = width.value * height.value
        image_buffer = (ctypes.c_ubyte * image_size)()
        frame_size = c_uint(image_size)
        
        result = scan_api.ftrScanGetFrame(device_handle, image_buffer, byref(frame_size))
        
        if result == FTR_OK:
            logger.info(f"✅ Frame captured successfully! Size: {frame_size.value} bytes")
            
            # Basic image analysis
            pixel_sum = sum(image_buffer[:min(1000, frame_size.value)])  # Sample first 1000 pixels
            avg_pixel = pixel_sum / min(1000, frame_size.value)
            
            logger.info(f"📊 Image analysis:")
            logger.info(f"  - Frame size: {frame_size.value} bytes")
            logger.info(f"  - Average pixel value (sample): {avg_pixel:.1f}")
            
            if avg_pixel > 10:  # Non-zero pixels suggest real image data
                logger.info("✅ Image appears to contain real data!")
            else:
                logger.warning("⚠️ Image appears to be mostly empty")
                
        else:
            logger.error(f"❌ Frame capture failed: {result}")
            if result == 4306:  # FTR_ERROR_EMPTY_FRAME
                logger.info("💡 Empty frame - try placing finger on scanner")
            elif result == 4307:  # FTR_ERROR_MOVABLE_FINGER
                logger.info("💡 Finger moved - try holding still")
            elif result == 4308:  # FTR_ERROR_NO_FRAME
                logger.info("💡 No frame available - device might be busy")
        
        # Step 5: Close device
        logger.info("🔒 Closing device...")
        close_result = scan_api.ftrScanCloseDevice(device_handle)
        
        if close_result == FTR_OK:
            logger.info("✅ Device closed successfully")
        else:
            logger.warning(f"⚠️ Device close returned: {close_result}")
        
        return result == FTR_OK
        
    except Exception as e:
        logger.error(f"❌ Basic device test failed: {e}")
        return False

def main():
    """Main test function"""
    logger.info("🚀 Basic Futronic Device Test")
    logger.info("=" * 35)
    
    success = test_basic_device_operations()
    
    logger.info("\n📊 Test Results:")
    logger.info("=" * 20)
    
    if success:
        logger.info("✅ Basic device operations successful!")
        logger.info("🎉 Your Futronic device is working!")
        logger.info("\n💡 Next steps:")
        logger.info("1. Find template functions for full biometric processing")
        logger.info("2. Update biometric service with working functions")
        logger.info("3. Test fingerprint capture in the web application")
    else:
        logger.info("❌ Basic device operations failed")
        logger.info("\n💡 Troubleshooting:")
        logger.info("1. Check device connection")
        logger.info("2. Check device drivers")
        logger.info("3. Ensure device is not in use by another application")
    
    return success

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
