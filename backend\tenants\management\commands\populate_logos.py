from django.core.management.base import BaseCommand
from django.core.files import File
from tenants.models import <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SubCity, CityAdministration
import os
from django.conf import settings


class Command(BaseCommand):
    help = 'Populate tenant logos for testing'

    def handle(self, *args, **options):
        self.stdout.write('🔄 Populating tenant logos...')
        
        # Define logo paths using existing files
        kebele_logo_path = os.path.join(settings.MEDIA_ROOT, 'logos', 'kebele', 'logo_final_gondar_with_camelot1.png')
        subcity_logo_path = os.path.join(settings.MEDIA_ROOT, 'logos', 'subcity', 'logo_final_gondar_with_camelot1.png')
        city_logo_path = os.path.join(settings.MEDIA_ROOT, 'logos', 'logo_final_gondar_with_camelot1.png')

        # Define signature and pattern paths
        signature_path = os.path.join(settings.MEDIA_ROOT, 'signatures', 'subcity', 'signiture_Chale.png')
        pattern_path = os.path.join(settings.MEDIA_ROOT, 'patterns', 'subcity', 'Fasil_castel_black.png')
        
        # Check if logo files exist
        if not os.path.exists(kebele_logo_path):
            self.stdout.write(self.style.WARNING(f'Kebele logo not found: {kebele_logo_path}'))
            return
            
        if not os.path.exists(subcity_logo_path):
            self.stdout.write(self.style.WARNING(f'Subcity logo not found: {subcity_logo_path}'))
            return
            
        if not os.path.exists(city_logo_path):
            self.stdout.write(self.style.WARNING(f'City logo not found: {city_logo_path}'))
            return
        
        # Update kebele profiles
        kebele_count = 0
        for tenant in Tenant.objects.filter(type='kebele'):
            if hasattr(tenant, 'kebele_profile'):
                profile = tenant.kebele_profile
                if not profile.logo:
                    with open(kebele_logo_path, 'rb') as f:
                        profile.logo.save(
                            f'kebele_{tenant.id}_logo.png',
                            File(f),
                            save=True
                        )
                if not profile.mayor_signature and os.path.exists(signature_path):
                    with open(signature_path, 'rb') as f:
                        profile.mayor_signature.save(
                            f'kebele_{tenant.id}_signature.png',
                            File(f),
                            save=True
                        )
                if not profile.pattern_image and os.path.exists(pattern_path):
                    with open(pattern_path, 'rb') as f:
                        profile.pattern_image.save(
                            f'kebele_{tenant.id}_pattern.png',
                            File(f),
                            save=True
                        )
                kebele_count += 1
                self.stdout.write(f'✅ Updated kebele profile: {profile.name}')
        
        # Update subcity profiles
        subcity_count = 0
        for tenant in Tenant.objects.filter(type='subcity'):
            if hasattr(tenant, 'subcity_profile'):
                profile = tenant.subcity_profile
                if not profile.logo:
                    with open(subcity_logo_path, 'rb') as f:
                        profile.logo.save(
                            f'subcity_{tenant.id}_logo.png',
                            File(f),
                            save=True
                        )
                if not profile.mayor_signature and os.path.exists(signature_path):
                    with open(signature_path, 'rb') as f:
                        profile.mayor_signature.save(
                            f'subcity_{tenant.id}_signature.png',
                            File(f),
                            save=True
                        )
                if not profile.pattern_image and os.path.exists(pattern_path):
                    with open(pattern_path, 'rb') as f:
                        profile.pattern_image.save(
                            f'subcity_{tenant.id}_pattern.png',
                            File(f),
                            save=True
                        )
                subcity_count += 1
                self.stdout.write(f'✅ Updated subcity profile: {profile.name}')
        
        # Update city profiles
        city_count = 0
        for tenant in Tenant.objects.filter(type='city'):
            if hasattr(tenant, 'city_profile'):
                profile = tenant.city_profile
                if not profile.logo:
                    with open(city_logo_path, 'rb') as f:
                        profile.logo.save(
                            f'city_{tenant.id}_logo.png',
                            File(f),
                            save=True
                        )
                city_count += 1
                self.stdout.write(f'✅ Updated city profile: {profile.city_name}')
        
        self.stdout.write(
            self.style.SUCCESS(
                f'🎉 Logo population complete!\n'
                f'   Kebele logos: {kebele_count}\n'
                f'   Subcity logos: {subcity_count}\n'
                f'   City logos: {city_count}'
            )
        )
