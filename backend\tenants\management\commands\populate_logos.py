from django.core.management.base import BaseCommand
from django.core.files import File
from tenants.models import <PERSON>ant, <PERSON><PERSON><PERSON>, SubCity, CityAdministration
import os
from django.conf import settings


class Command(BaseCommand):
    help = 'Populate tenant logos for testing'

    def handle(self, *args, **options):
        self.stdout.write('🔄 Populating tenant logos...')
        
        # Define logo paths
        kebele_logo_path = os.path.join(settings.MEDIA_ROOT, 'logos', 'kebele', 'gondar-logo.jpg')
        subcity_logo_path = os.path.join(settings.MEDIA_ROOT, 'logos', 'subcity', 'gondar-logo.jpg')
        city_logo_path = os.path.join(settings.MEDIA_ROOT, 'logos', 'gondar-logo.jpg')
        
        # Check if logo files exist
        if not os.path.exists(kebele_logo_path):
            self.stdout.write(self.style.WARNING(f'Kebele logo not found: {kebele_logo_path}'))
            return
            
        if not os.path.exists(subcity_logo_path):
            self.stdout.write(self.style.WARNING(f'Subcity logo not found: {subcity_logo_path}'))
            return
            
        if not os.path.exists(city_logo_path):
            self.stdout.write(self.style.WARNING(f'City logo not found: {city_logo_path}'))
            return
        
        # Update kebele profiles
        kebele_count = 0
        for tenant in Tenant.objects.filter(type='kebele'):
            if hasattr(tenant, 'kebele_profile'):
                profile = tenant.kebele_profile
                if not profile.logo:
                    with open(kebele_logo_path, 'rb') as f:
                        profile.logo.save(
                            f'kebele_{tenant.id}_logo.jpg',
                            File(f),
                            save=True
                        )
                    kebele_count += 1
                    self.stdout.write(f'✅ Updated kebele logo: {profile.name}')
        
        # Update subcity profiles
        subcity_count = 0
        for tenant in Tenant.objects.filter(type='subcity'):
            if hasattr(tenant, 'subcity_profile'):
                profile = tenant.subcity_profile
                if not profile.logo:
                    with open(subcity_logo_path, 'rb') as f:
                        profile.logo.save(
                            f'subcity_{tenant.id}_logo.jpg',
                            File(f),
                            save=True
                        )
                    subcity_count += 1
                    self.stdout.write(f'✅ Updated subcity logo: {profile.name}')
        
        # Update city profiles
        city_count = 0
        for tenant in Tenant.objects.filter(type='city'):
            if hasattr(tenant, 'city_profile'):
                profile = tenant.city_profile
                if not profile.logo:
                    with open(city_logo_path, 'rb') as f:
                        profile.logo.save(
                            f'city_{tenant.id}_logo.jpg',
                            File(f),
                            save=True
                        )
                    city_count += 1
                    self.stdout.write(f'✅ Updated city logo: {profile.city_name}')
        
        self.stdout.write(
            self.style.SUCCESS(
                f'🎉 Logo population complete!\n'
                f'   Kebele logos: {kebele_count}\n'
                f'   Subcity logos: {subcity_count}\n'
                f'   City logos: {city_count}'
            )
        )
