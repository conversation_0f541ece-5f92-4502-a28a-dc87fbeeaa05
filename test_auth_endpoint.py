#!/usr/bin/env python3
"""
Test script to verify authentication endpoint is working.
"""

import requests
import json

def test_auth_endpoint():
    """Test the authentication endpoint."""
    
    base_url = "http://************:8000"
    
    print("🧪 Testing Authentication Endpoint")
    print("=" * 40)
    
    # Test 1: Health check
    print("📍 Step 1: Testing health check...")
    try:
        response = requests.get(f"{base_url}/", timeout=10)
        if response.status_code == 200:
            print(f"✅ Health check OK: {response.text}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Health check error: {e}")
    
    # Test 2: Check if auth endpoint exists
    print("\n📍 Step 2: Testing auth endpoint availability...")
    try:
        # Test with invalid data to see if endpoint exists
        response = requests.post(
            f"{base_url}/api/auth/token/",
            json={"test": "data"},
            timeout=10
        )
        if response.status_code == 404:
            print("❌ Auth endpoint not found (404)")
        else:
            print(f"✅ Auth endpoint exists (Status: {response.status_code})")
            if response.status_code == 400:
                print("   (400 is expected for invalid data)")
    except Exception as e:
        print(f"❌ Auth endpoint error: {e}")
    
    # Test 3: Test with superuser credentials
    print("\n📍 Step 3: Testing superuser login...")
    try:
        response = requests.post(
            f"{base_url}/api/auth/token/",
            json={
                "email": "<EMAIL>",
                "password": "admin123"
            },
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if 'access' in data:
                print("✅ Superuser login successful!")
                print(f"   Access token: {data['access'][:50]}...")
            else:
                print("❌ Login response missing access token")
        else:
            print(f"❌ Superuser login failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Superuser login error: {e}")
    
    # Test 4: Test shared API endpoint
    print("\n📍 Step 4: Testing shared API endpoint...")
    try:
        response = requests.get(f"{base_url}/api/shared/countries/", timeout=10)
        print(f"Shared API Status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Shared API working")
        else:
            print(f"❌ Shared API failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Shared API error: {e}")
    
    # Test 5: Test with domain-based login
    print("\n📍 Step 5: Testing domain-based login...")
    try:
        response = requests.post(
            f"{base_url}/api/auth/token/",
            json={
                "username": "<EMAIL>",
                "password": "password123"
            },
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"Domain login Status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Domain-based login successful!")
        else:
            print(f"❌ Domain-based login failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Domain-based login error: {e}")

if __name__ == "__main__":
    test_auth_endpoint()
    
    print("\n" + "=" * 40)
    print("🔧 If auth endpoint is still not working:")
    print("1. Restart Django server: docker-compose restart backend")
    print("2. Check Django logs: docker-compose logs backend")
    print("3. Verify middleware changes are applied")
    print("4. Check if public tenant exists with proper domains")
    print("\n🌐 Expected working URLs:")
    print("   Health: http://************:8000/")
    print("   Auth: http://************:8000/api/auth/token/")
    print("   Shared: http://************:8000/api/shared/countries/")
    print("   Admin: http://************:8000/admin/")
