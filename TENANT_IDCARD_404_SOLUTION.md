# 🔧 Tenant ID Card 404 Error Solution

## Problem
Frontend ID card management is failing with 404 errors on tenant-specific endpoints:
```
Failed to load resource: the server responded with a status of 404 (Not Found)
************:8000/api/tenants/42/idcards/
```

## Root Cause Analysis
The issue is caused by:
1. **Middleware not handling tenant URL patterns** - The middleware was only processing requests with `X-Tenant-ID` headers
2. **Missing URL pattern recognition** - Tenant-specific URLs like `/api/tenants/{id}/idcards/` were not being processed
3. **Schema context not being set** - Requests to tenant endpoints need proper schema switching

## ✅ Complete Solution Applied

### 1. Fixed Tenant Middleware
Updated `backend/tenants/middleware.py` to properly handle tenant-specific URL patterns:

```python
# Handle tenant-specific API requests
if request.path.startswith('/api/tenants/'):
    # Extract tenant ID from URL pattern like /api/tenants/42/idcards/
    import re
    tenant_match = re.match(r'/api/tenants/(\d+)/', request.path)
    if tenant_match:
        tenant_id = tenant_match.group(1)
        try:
            # Get the tenant model
            TenantModel = get_tenant_model()
            tenant = TenantModel.objects.get(id=tenant_id)

            # Get a domain for this tenant
            DomainModel = get_tenant_domain_model()
            domain = DomainModel.objects.filter(tenant=tenant).first()

            if domain:
                request.tenant = tenant
                request.domain = domain
                connection = connections['default']
                connection.set_tenant(request.tenant)
                return None
        except (TenantModel.DoesNotExist, DomainModel.DoesNotExist, ValueError):
            return JsonResponse({"detail": "Invalid tenant ID"}, status=400)
```

### 2. Verified URL Patterns
Confirmed that tenant-specific ID card URLs are properly registered in `backend/tenants/urls.py`:

```python
# Tenant-specific ID card endpoints
path('<int:tenant_id>/idcards/', TenantSpecificIDCardViewSet.as_view({
    'get': 'list',
    'post': 'create'
}), name='tenant-idcards-list'),
path('<int:tenant_id>/idcards/<int:pk>/', TenantSpecificIDCardViewSet.as_view({
    'get': 'retrieve',
    'put': 'update',
    'patch': 'partial_update',
    'delete': 'destroy'
}), name='tenant-idcards-detail'),
```

### 3. Enhanced Frontend Error Handling
The frontend properly extracts tenant ID from JWT token and makes correct API calls.

## 🚀 Quick Fix Instructions

### Option 1: Use the Fix Script
```bash
chmod +x fix_tenant_idcard_404.sh
./fix_tenant_idcard_404.sh
```

### Option 2: Manual Steps
```bash
# 1. Restart backend to apply middleware changes
docker-compose restart backend

# 2. Wait for restart
sleep 25

# 3. Test the endpoints
python test_tenant_idcard_endpoints.py

# 4. If needed, run migrations
docker-compose exec backend python manage.py migrate_schemas --shared
docker-compose exec backend python manage.py migrate_schemas
```

## 🧪 Testing the Fix

### 1. Test Authentication and Get Tenant ID
```bash
curl -X POST http://************:8000/api/auth/token/ \
  -H "Content-Type: application/json" \
  -d '{"username": "<EMAIL>", "password": "password123"}'
```

This should return a response with `tenant_id` and `access` token.

### 2. Test Tenant ID Card Endpoint
```bash
# Replace TOKEN and TENANT_ID with values from step 1
curl -H "Authorization: Bearer TOKEN" \
  http://************:8000/api/tenants/TENANT_ID/idcards/
```

Expected response: JSON array of ID cards or empty array `[]`.

### 3. Test Frontend
1. Open `http://************:3000/`
2. Login with: `<EMAIL>` / `password123`
3. Navigate to ID Cards section
4. Should load without 404 errors

## 🔐 Login Credentials for Testing

### Tenant Users (Domain-based)
- **Clerk**: `<EMAIL>` / `password123`
- **Kebele Admin**: `<EMAIL>` / `password123`
- **Subcity Admin**: `<EMAIL>` / `password123`

### Superadmin (Email-based)
- **Email**: `<EMAIL>` / `admin123`

## 🔍 Verification Steps

1. **Login Works**: Can login with tenant user credentials
2. **Tenant ID Extracted**: JWT token contains proper tenant_id
3. **ID Card List Loads**: `/api/tenants/{tenant_id}/idcards/` returns 200
4. **Citizens List Works**: `/api/tenants/{tenant_id}/citizens/` returns 200
5. **Frontend Navigation**: ID Cards section loads without errors

## 🆘 Troubleshooting

### Issue: Still getting 404 on tenant endpoints
**Solutions:**
1. Check Django logs: `docker-compose logs backend`
2. Verify middleware changes: Look for tenant URL processing in logs
3. Restart services: `docker-compose restart backend`
4. Check tenant exists: Login to admin panel and verify tenant

### Issue: Tenant ID is null or undefined
**Solutions:**
1. Check JWT token contains tenant_id claim
2. Verify login response includes tenant information
3. Clear browser localStorage and login again
4. Check user is properly assigned to a tenant

### Issue: Authentication works but tenant endpoints fail
**Solutions:**
1. Verify user has correct tenant assignment in database
2. Check tenant schema exists in database
3. Run migrations: `docker-compose exec backend python manage.py migrate_schemas`
4. Check tenant has proper domain configuration

### Issue: Some tenant endpoints work, others don't
**Solutions:**
1. Check specific URL patterns in `tenants/urls.py`
2. Verify ViewSet methods are properly implemented
3. Check permissions for specific endpoints
4. Look for schema context issues in ViewSet code

## 📋 Expected Results After Fix

- ✅ **Tenant ID card endpoints** return 200 (not 404)
- ✅ **Frontend ID card list** loads without errors
- ✅ **Tenant-specific data** is properly isolated
- ✅ **Authentication** works with tenant users
- ✅ **URL pattern matching** works for `/api/tenants/{id}/...`
- ✅ **Schema switching** happens automatically

## 🔧 Manual Debugging

If issues persist, debug manually:

```bash
# Enter backend container
docker-compose exec backend bash

# Test Django shell
python manage.py shell

# In shell, test URL patterns:
from django.urls import resolve
try:
    match = resolve('/api/tenants/1/idcards/')
    print(f"URL resolves to: {match.func}")
    print(f"URL name: {match.url_name}")
except:
    print("URL does not resolve")

# Test tenant exists:
from tenants.models import Tenant
tenants = Tenant.objects.all()
print(f"Available tenants: {list(tenants.values('id', 'name', 'type'))}")
```

## 📞 Support

If tenant ID card endpoints are still not working after following this guide:
1. Provide output of `docker-compose logs backend`
2. Share results of `python test_tenant_idcard_endpoints.py`
3. Include browser console errors and network tab information
4. Confirm which specific step is failing

The solution addresses the core middleware issue that was preventing tenant-specific URL patterns from being properly processed and routed to the correct schema context.
