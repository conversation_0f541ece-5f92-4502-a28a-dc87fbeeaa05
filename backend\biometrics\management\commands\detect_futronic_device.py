"""
Management command to detect and diagnose Futronic FS88H device connectivity.
"""

import os
import subprocess
import platform
from django.core.management.base import BaseCommand


class Command(BaseCommand):
    help = 'Detect and diagnose Futronic FS88H device connectivity'

    def add_arguments(self, parser):
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed diagnostic information',
        )

    def handle(self, *args, **options):
        self.verbose = options['verbose']
        
        self.stdout.write(
            self.style.SUCCESS('🔍 Futronic FS88H Device Detection & Diagnostics')
        )
        self.stdout.write('=' * 60)

        # System information
        self.check_system_info()
        
        # USB device detection
        self.check_usb_devices()
        
        # Docker environment checks
        self.check_docker_environment()
        
        # SDK availability
        self.check_sdk_availability()
        
        # Device permissions
        self.check_device_permissions()
        
        # Test device communication
        self.test_device_communication()

        self.stdout.write(
            self.style.SUCCESS('\n✅ Device detection completed!')
        )

    def check_system_info(self):
        """Check basic system information."""
        self.stdout.write('\n📋 System Information:')
        
        try:
            system = platform.system()
            self.stdout.write(f'  OS: {system} {platform.release()}')
            self.stdout.write(f'  Architecture: {platform.machine()}')
            self.stdout.write(f'  Python: {platform.python_version()}')
            
            # Check if running in Docker
            if os.path.exists('/.dockerenv'):
                self.stdout.write('  Environment: Docker Container')
            else:
                self.stdout.write('  Environment: Host System')
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  Error getting system info: {e}'))

    def check_usb_devices(self):
        """Check for USB devices, specifically looking for Futronic devices."""
        self.stdout.write('\n🔌 USB Device Detection:')
        
        try:
            # Try lsusb command (Linux)
            result = subprocess.run(['lsusb'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                usb_devices = result.stdout.strip().split('\n')
                self.stdout.write(f'  Found {len(usb_devices)} USB devices')
                
                # Look for Futronic devices
                futronic_devices = [d for d in usb_devices if 'futronic' in d.lower() or '1491' in d]
                
                if futronic_devices:
                    self.stdout.write(self.style.SUCCESS('  ✅ Futronic device(s) detected:'))
                    for device in futronic_devices:
                        self.stdout.write(f'    {device}')
                else:
                    self.stdout.write(self.style.WARNING('  ⚠️ No Futronic devices detected'))
                
                if self.verbose:
                    self.stdout.write('  All USB devices:')
                    for device in usb_devices:
                        self.stdout.write(f'    {device}')
                        
            else:
                self.stdout.write(self.style.ERROR('  ❌ lsusb command failed'))
                
        except subprocess.TimeoutExpired:
            self.stdout.write(self.style.ERROR('  ❌ lsusb command timed out'))
        except FileNotFoundError:
            self.stdout.write(self.style.WARNING('  ⚠️ lsusb command not available (Windows?)'))
            
            # Try alternative methods for Windows
            self.check_windows_devices()
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ Error checking USB devices: {e}'))

    def check_windows_devices(self):
        """Check for devices on Windows systems."""
        try:
            # Try PowerShell command to list USB devices
            result = subprocess.run([
                'powershell', 
                'Get-WmiObject -Class Win32_USBHub | Select-Object Name, DeviceID'
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                self.stdout.write('  Windows USB devices detected via PowerShell')
                if 'futronic' in result.stdout.lower():
                    self.stdout.write(self.style.SUCCESS('  ✅ Futronic device detected'))
                else:
                    self.stdout.write(self.style.WARNING('  ⚠️ No Futronic devices detected'))
            else:
                self.stdout.write(self.style.WARNING('  ⚠️ PowerShell device check failed'))
                
        except Exception as e:
            self.stdout.write(self.style.WARNING(f'  ⚠️ Windows device check failed: {e}'))

    def check_docker_environment(self):
        """Check Docker-specific configurations."""
        self.stdout.write('\n🐳 Docker Environment:')
        
        if not os.path.exists('/.dockerenv'):
            self.stdout.write('  Not running in Docker container')
            return
        
        # Check USB device mounts
        usb_paths = ['/dev/bus/usb', '/dev/usb']
        for path in usb_paths:
            if os.path.exists(path):
                self.stdout.write(f'  ✅ USB path mounted: {path}')
                try:
                    items = os.listdir(path)
                    self.stdout.write(f'    Contains {len(items)} items')
                    if self.verbose:
                        for item in items[:5]:  # Show first 5 items
                            self.stdout.write(f'      {item}')
                except PermissionError:
                    self.stdout.write(self.style.WARNING(f'    ⚠️ Permission denied accessing {path}'))
            else:
                self.stdout.write(self.style.WARNING(f'  ⚠️ USB path not mounted: {path}'))

    def check_sdk_availability(self):
        """Check for Futronic SDK availability."""
        self.stdout.write('\n📚 SDK Availability:')
        
        sdk_paths = [
            '/opt/futronic-sdk',
            '/usr/local/futronic-sdk',
            '/app/futronic-sdk'
        ]
        
        sdk_found = False
        for path in sdk_paths:
            if os.path.exists(path):
                self.stdout.write(f'  ✅ SDK found at: {path}')
                sdk_found = True
                
                # Check SDK contents
                try:
                    contents = os.listdir(path)
                    self.stdout.write(f'    Contains: {", ".join(contents)}')
                    
                    # Look for library files
                    lib_path = os.path.join(path, 'lib')
                    if os.path.exists(lib_path):
                        lib_files = [f for f in os.listdir(lib_path) if f.endswith('.so')]
                        if lib_files:
                            self.stdout.write(f'    Library files: {", ".join(lib_files)}')
                        else:
                            self.stdout.write(self.style.WARNING('    ⚠️ No .so library files found'))
                            
                except Exception as e:
                    self.stdout.write(self.style.WARNING(f'    ⚠️ Error reading SDK directory: {e}'))
                break
        
        if not sdk_found:
            self.stdout.write(self.style.WARNING('  ⚠️ Futronic SDK not found in standard locations'))
            self.stdout.write('    This is normal for simulation mode')

    def check_device_permissions(self):
        """Check device file permissions."""
        self.stdout.write('\n🔐 Device Permissions:')
        
        if not os.path.exists('/dev/bus/usb'):
            self.stdout.write('  ⚠️ /dev/bus/usb not available')
            return
        
        try:
            # Check permissions on USB bus
            stat_info = os.stat('/dev/bus/usb')
            self.stdout.write(f'  USB bus permissions: {oct(stat_info.st_mode)[-3:]}')
            
            # Check if we can list USB devices
            usb_buses = os.listdir('/dev/bus/usb')
            self.stdout.write(f'  USB buses available: {len(usb_buses)}')
            
            # Check specific bus permissions
            for bus in usb_buses[:3]:  # Check first 3 buses
                bus_path = f'/dev/bus/usb/{bus}'
                try:
                    devices = os.listdir(bus_path)
                    self.stdout.write(f'    Bus {bus}: {len(devices)} devices')
                except PermissionError:
                    self.stdout.write(self.style.WARNING(f'    Bus {bus}: Permission denied'))
                    
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ Error checking permissions: {e}'))

    def test_device_communication(self):
        """Test basic device communication."""
        self.stdout.write('\n📡 Device Communication Test:')
        
        try:
            # Import our device integration
            from biometrics.device_integration import get_device
            
            device = get_device()
            self.stdout.write('  Device instance created')
            
            # Test initialization
            success = device.initialize()
            if success:
                self.stdout.write(self.style.SUCCESS('  ✅ Device initialization successful'))
                
                # Get device status
                status = device.get_device_status()
                self.stdout.write(f'    Connected: {status["connected"]}')
                self.stdout.write(f'    Model: {status["model"]}')
                self.stdout.write(f'    Serial: {status["serial_number"]}')
                
                # Test capture (this will be simulation if no real device)
                try:
                    template = device.capture_fingerprint('left')
                    if template:
                        self.stdout.write(self.style.SUCCESS('  ✅ Fingerprint capture test successful'))
                        self.stdout.write(f'    Quality: {template.quality_score}%')
                        self.stdout.write(f'    Minutiae: {template.minutiae_count}')
                    else:
                        self.stdout.write(self.style.WARNING('  ⚠️ Fingerprint capture returned None'))
                except Exception as e:
                    self.stdout.write(self.style.WARNING(f'  ⚠️ Fingerprint capture test failed: {e}'))
                    
            else:
                self.stdout.write(self.style.WARNING('  ⚠️ Device initialization failed'))
                if device.last_error:
                    self.stdout.write(f'    Error: {device.last_error}')
                    
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ Device communication test failed: {e}'))

    def style_status(self, status, message):
        """Helper to style status messages."""
        if status == 'success':
            return self.style.SUCCESS(f'✅ {message}')
        elif status == 'warning':
            return self.style.WARNING(f'⚠️ {message}')
        else:
            return self.style.ERROR(f'❌ {message}')
