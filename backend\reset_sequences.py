#!/usr/bin/env python3
"""
Reset sequences to ultra-safe values.
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
sys.path.append('/app')
django.setup()

from django.db import connection

def reset_sequences():
    """Reset sequences to ultra-safe values."""
    cursor = connection.cursor()
    
    print('=== RESETTING SEQUENCES ===')
    
    # Reset sequences to very high values
    cursor.execute("SELECT setval('tenants_subcity_id_seq', 20000);")
    cursor.execute("SELECT setval('tenants_kebele_id_seq', 15000);")
    
    print('✅ Sequences reset to ultra-safe values:')
    print('   SubCity sequence: 20000 (next ID: 20001)')
    print('   Kebele sequence: 15000 (next ID: 15001)')
    print('🎯 Ready for testing!')
    
    return True

if __name__ == "__main__":
    reset_sequences()
