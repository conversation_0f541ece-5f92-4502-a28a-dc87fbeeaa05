#!/usr/bin/env python3
"""
Test the tenant API endpoint directly to debug frontend issues.
"""

import requests
import json

def test_tenant_api_directly():
    """Test tenant API endpoint step by step."""
    
    base_url = "http://10.139.8.141:8000"
    
    print("🧪 Testing Tenant API Directly")
    print("=" * 40)
    
    # Step 1: Test authentication
    print("📍 Step 1: Testing authentication...")
    
    auth_data = {
        "email": "<EMAIL>",
        "password": "admin123"
    }
    
    try:
        auth_response = requests.post(
            f"{base_url}/api/auth/token/",
            json=auth_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"Auth Status: {auth_response.status_code}")
        
        if auth_response.status_code == 200:
            auth_result = auth_response.json()
            access_token = auth_result.get('access')
            print("✅ Authentication successful")
            print(f"Token length: {len(access_token) if access_token else 0}")
        else:
            print(f"❌ Authentication failed: {auth_response.text}")
            return
            
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return
    
    # Step 2: Test tenant endpoint without auth
    print(f"\n📍 Step 2: Testing tenant endpoint without authentication...")
    try:
        response = requests.get(f"{base_url}/api/tenants/", timeout=10)
        print(f"No Auth Status: {response.status_code}")
        print(f"No Auth Response: {response.text[:200]}...")
    except Exception as e:
        print(f"No Auth Error: {e}")
    
    # Step 3: Test tenant endpoint with auth
    print(f"\n📍 Step 3: Testing tenant endpoint with authentication...")
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{base_url}/api/tenants/", headers=headers, timeout=10)
        print(f"With Auth Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("✅ API call successful")
                print(f"Response type: {type(data)}")
                
                if isinstance(data, list):
                    print(f"Tenant count: {len(data)}")
                    if data:
                        print(f"First tenant: {data[0]}")
                elif isinstance(data, dict):
                    if 'results' in data:
                        print(f"Paginated response - count: {data.get('count', 'unknown')}")
                        print(f"Results count: {len(data.get('results', []))}")
                        if data.get('results'):
                            print(f"First tenant: {data['results'][0]}")
                    else:
                        print(f"Dict response: {data}")
                        
            except json.JSONDecodeError:
                print(f"❌ Response not valid JSON: {response.text}")
        else:
            print(f"❌ API call failed: {response.text}")
            
    except Exception as e:
        print(f"❌ API call error: {e}")
    
    # Step 4: Test other endpoints
    print(f"\n📍 Step 4: Testing related endpoints...")
    
    endpoints = [
        "/api/tenants/subcities/",
        "/api/tenants/kebeles/",
        "/api/shared/countries/"
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", headers=headers, timeout=10)
            print(f"{endpoint}: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    count = len(data) if isinstance(data, list) else len(data.get('results', []))
                    print(f"  ✅ Success - {count} items")
                except:
                    print(f"  ⚠️  Success but response not JSON")
            else:
                print(f"  ❌ Failed: {response.text[:100]}")
                
        except Exception as e:
            print(f"  ❌ Error: {e}")
    
    # Step 5: Test the exact same call the frontend makes
    print(f"\n📍 Step 5: Simulating exact frontend call...")
    
    # Simulate the exact headers the frontend would send
    frontend_headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
        "Accept": "application/json",
        "User-Agent": "Mozilla/5.0 (Frontend Test)"
    }
    
    try:
        response = requests.get(
            f"{base_url}/api/tenants/",
            headers=frontend_headers,
            timeout=10
        )
        
        print(f"Frontend Simulation Status: {response.status_code}")
        print(f"Response Size: {len(response.content)} bytes")
        
        if response.status_code == 200:
            print("✅ Frontend simulation successful")
            try:
                data = response.json()
                print(f"Data structure matches frontend expectation")
                
                # Check the exact structure the frontend expects
                if isinstance(data, dict) and 'results' in data:
                    print(f"✅ Paginated response structure correct")
                    print(f"Count: {data.get('count')}")
                    print(f"Results: {len(data.get('results', []))}")
                elif isinstance(data, list):
                    print(f"✅ Direct array response")
                    print(f"Length: {len(data)}")
                    
            except json.JSONDecodeError:
                print(f"❌ Invalid JSON response")
        else:
            print(f"❌ Frontend simulation failed")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Frontend simulation error: {e}")

if __name__ == "__main__":
    test_tenant_api_directly()
    
    print("\n" + "=" * 40)
    print("🔍 Analysis:")
    print("Since you can see tenants in Django admin, the issue is likely:")
    print("1. API endpoint permissions/authentication")
    print("2. Frontend making incorrect API calls")
    print("3. CORS issues")
    print("4. Token/authentication problems")
    print("")
    print("💡 Next steps:")
    print("1. If API works here but not in frontend:")
    print("   - Check browser console for exact error")
    print("   - Check network tab for request details")
    print("   - Verify frontend is sending correct headers")
    print("")
    print("2. If API fails here too:")
    print("   - Check permissions on TenantViewSet")
    print("   - Verify URL routing")
    print("   - Check middleware issues")
    print("")
    print("3. Compare working Django admin vs failing API")
