#!/usr/bin/env python3
"""
Test script to debug JWT token issues and check user tenant assignments.
"""

import requests
import json
import jwt
import base64

def decode_jwt_token(token):
    """Decode JWT token without verification for debugging."""
    try:
        # Split the token
        header, payload, signature = token.split('.')
        
        # Add padding if needed
        payload += '=' * (4 - len(payload) % 4)
        
        # Decode the payload
        decoded_bytes = base64.urlsafe_b64decode(payload)
        decoded_payload = json.loads(decoded_bytes)
        
        return decoded_payload
    except Exception as e:
        print(f"Error decoding token: {e}")
        return None

def test_jwt_token_issue():
    """Test JWT token generation and tenant information."""
    
    base_url = "http://************:8000"
    
    print("🧪 Testing JWT Token Issue")
    print("=" * 40)
    
    # Test different login scenarios
    login_scenarios = [
        {
            "name": "Superadmin Login (Email)",
            "credentials": {
                "email": "<EMAIL>",
                "password": "admin123"
            }
        },
        {
            "name": "Tenant User Login (Domain-based)",
            "credentials": {
                "username": "<EMAIL>",
                "password": "password123"
            }
        },
        {
            "name": "Alternative Superadmin",
            "credentials": {
                "email": "<EMAIL>",
                "password": "admin123"
            }
        }
    ]
    
    for scenario in login_scenarios:
        print(f"\n📍 Testing: {scenario['name']}")
        print("-" * 30)
        
        try:
            response = requests.post(
                f"{base_url}/api/auth/token/",
                json=scenario['credentials'],
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            print(f"  Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                access_token = data.get('access')
                
                if access_token:
                    print("  ✅ Token received successfully")
                    
                    # Decode the token
                    decoded = decode_jwt_token(access_token)
                    if decoded:
                        print(f"  📋 Token Contents:")
                        print(f"    User ID: {decoded.get('user_id')}")
                        print(f"    Email: {decoded.get('email')}")
                        print(f"    Username: {decoded.get('username')}")
                        print(f"    Role: {decoded.get('role')}")
                        print(f"    Is Superuser: {decoded.get('is_superuser')}")
                        print(f"    Tenant ID: {decoded.get('tenant_id')}")
                        print(f"    Tenant Name: {decoded.get('tenant_name')}")
                        print(f"    Tenant Type: {decoded.get('tenant_type')}")
                        print(f"    Tenant Schema: {decoded.get('tenant_schema')}")
                        print(f"    Domain: {decoded.get('domain')}")
                        
                        # Check if tenant info is missing
                        if not decoded.get('tenant_id') and not decoded.get('is_superuser'):
                            print("  ⚠️  WARNING: User has no tenant assigned and is not superuser")
                        elif decoded.get('is_superuser'):
                            print("  ✅ Superuser - tenant info not required")
                        else:
                            print("  ✅ Tenant info present")
                    else:
                        print("  ❌ Failed to decode token")
                else:
                    print("  ❌ No access token in response")
                    
            elif response.status_code == 401:
                print("  ❌ Authentication failed - Invalid credentials")
            elif response.status_code == 400:
                print("  ❌ Bad request - Check request format")
                print(f"  Response: {response.text}")
            else:
                print(f"  ❌ Unexpected status: {response.status_code}")
                print(f"  Response: {response.text}")
                
        except Exception as e:
            print(f"  ❌ Error: {e}")
    
    # Test user creation if needed
    print(f"\n📍 Checking if we need to create users...")
    print("-" * 30)
    
    # Try to create superuser if login failed
    print("  💡 To create a superuser, run:")
    print("    docker-compose exec backend python manage.py createsuperuser")
    print("    Email: <EMAIL>")
    print("    Password: admin123")
    
    # Test tenant endpoints with any working token
    print(f"\n📍 Testing tenant endpoints...")
    print("-" * 30)
    
    # Try to get a working token
    working_token = None
    for scenario in login_scenarios:
        try:
            response = requests.post(
                f"{base_url}/api/auth/token/",
                json=scenario['credentials'],
                timeout=5
            )
            if response.status_code == 200:
                working_token = response.json().get('access')
                print(f"  ✅ Using token from: {scenario['name']}")
                break
        except:
            continue
    
    if working_token:
        headers = {"Authorization": f"Bearer {working_token}"}
        
        # Test tenant endpoints
        endpoints = [
            "/api/tenants/",
            "/api/tenants/subcities/",
            "/api/tenants/kebeles/"
        ]
        
        for endpoint in endpoints:
            try:
                response = requests.get(f"{base_url}{endpoint}", headers=headers, timeout=5)
                print(f"  {endpoint}: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    count = len(data) if isinstance(data, list) else len(data.get('results', []))
                    print(f"    ✅ Success - {count} items")
                else:
                    print(f"    ❌ Failed")
                    
            except Exception as e:
                print(f"    ❌ Error: {e}")
    else:
        print("  ❌ No working token available to test endpoints")

if __name__ == "__main__":
    test_jwt_token_issue()
    
    print("\n" + "=" * 40)
    print("🔧 Solutions for JWT Token Issues:")
    print("1. If superuser doesn't exist:")
    print("   docker-compose exec backend python manage.py createsuperuser")
    print("   Email: <EMAIL>, Password: admin123")
    print("")
    print("2. If tenant user has no tenant assigned:")
    print("   - Create tenant first in Django admin")
    print("   - Assign user to tenant")
    print("")
    print("3. If pagination error persists:")
    print("   - Clear browser cache")
    print("   - Restart frontend: docker-compose restart frontend")
    print("")
    print("4. Check backend logs:")
    print("   docker-compose logs backend")
    print("")
    print("💡 Expected: Superusers should have is_superuser=true")
    print("💡 Expected: Tenant users should have tenant_id and tenant_name")
