#!/usr/bin/env python
"""
Test script to verify that migrations work correctly and the citizen_clearance_requests table exists.
This script can be run inside the Docker container.
"""

import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append('/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.core.management import call_command
from django.db import connection
from django_tenants.utils import schema_context
from tenants.models import Tenant

def test_migrations():
    """Test if migrations can be run successfully."""
    
    print("🔍 Testing migrations...")
    
    try:
        # Run migrations for shared apps
        print("📋 Running shared migrations...")
        call_command('migrate_schemas', '--shared', verbosity=1)
        print("✅ Shared migrations completed successfully")
        
        # Run migrations for tenant apps
        print("📋 Running tenant migrations...")
        call_command('migrate_schemas', verbosity=1)
        print("✅ Tenant migrations completed successfully")
        
    except Exception as e:
        print(f"❌ Error running migrations: {e}")
        return False
    
    return True

def test_clearance_table():
    """Test if the citizen_clearance_requests table exists."""
    
    print("\n🔍 Testing citizen_clearance_requests table...")
    
    try:
        # Check if we can import the model
        from workflows.models import CitizenClearanceRequest
        print("✅ CitizenClearanceRequest model imported successfully")
        
        # Test in tenant schemas
        tenants = Tenant.objects.filter(schema_name__in=['city_addis_ababa', 'subcity_bole', 'kebele_bole_01'])
        
        for tenant in tenants:
            print(f"\n🏢 Testing tenant: {tenant.name} (schema: {tenant.schema_name})")
            
            try:
                with schema_context(tenant.schema_name):
                    # Try to query the table
                    count = CitizenClearanceRequest.objects.count()
                    print(f"✅ citizen_clearance_requests table exists in {tenant.schema_name} with {count} records")
                    
            except Exception as e:
                print(f"❌ Error testing {tenant.schema_name}: {e}")
                return False
                
    except Exception as e:
        print(f"❌ Error importing or testing CitizenClearanceRequest model: {e}")
        return False
    
    return True

def test_init_data():
    """Test if the init_data command works."""
    
    print("\n🔍 Testing init_data command...")
    
    try:
        call_command('init_data', verbosity=1)
        print("✅ init_data command completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error running init_data: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting migration and clearance table test...")
    
    # Test migrations first
    migrations_ok = test_migrations()
    
    if migrations_ok:
        # Test clearance table
        table_ok = test_clearance_table()
        
        if table_ok:
            # Test init_data
            init_ok = test_init_data()
            
            if init_ok:
                print("\n✅ All tests passed! The citizen clearance system is working correctly.")
            else:
                print("\n❌ init_data test failed.")
        else:
            print("\n❌ Clearance table test failed.")
    else:
        print("\n❌ Migration test failed.")
    
    print("\n🏁 Test completed!")
