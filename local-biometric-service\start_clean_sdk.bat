@echo off
echo ========================================
echo    CLEAN REAL SDK SERVICE
echo ========================================
echo.
echo CLEAN APPROACH - NO SPECIAL CHARACTERS
echo.
echo WHAT THIS FIXES:
echo   - Removes all special characters that might cause encoding issues
echo   - Uses simple print statements instead of logger with emojis
echo   - Clean Flask startup without character encoding problems
echo   - Same real SDK functionality as before
echo.
echo REAL SDK FEATURES:
echo   - Uses actual Futronic SDK DLLs from WorkedEx
echo   - Real device connection and finger detection
echo   - No simulation - 100%% real hardware interaction
echo   - Clean, stable Flask web service
echo.

python clean_real_sdk.py

echo.
echo Clean SDK service stopped.
pause
