import dayjs from 'dayjs';

// Create simple plugin implementations if the real ones aren't available
const weekOfYearPlugin = (option, dayjsClass) => {
  dayjsClass.prototype.week = function() {
    const date = this.toDate();
    const start = new Date(date.getFullYear(), 0, 1);
    const diff = date - start;
    const oneDay = 1000 * 60 * 60 * 24;
    return Math.floor(diff / oneDay / 7) + 1;
  };
};

const customParseFormatPlugin = (option, dayjsClass) => {
  // Simple implementation that doesn't do much
  dayjsClass.prototype.customParse = function(format) {
    return this;
  };
};

const localizedFormatPlugin = (option, dayjsClass) => {
  // Simple implementation that doesn't do much
  dayjsClass.prototype.formatLocalized = function() {
    return this.format('YYYY-MM-DD');
  };
};

const isBetweenPlugin = (option, dayjsClass) => {
  dayjsClass.prototype.isBetween = function(start, end) {
    const time = this.valueOf();
    const startTime = dayjs(start).valueOf();
    const endTime = dayjs(end).valueOf();
    return time >= startTime && time <= endTime;
  };
};

// Extend dayjs with our custom plugins
dayjs.extend(weekOfYearPlugin);
dayjs.extend(customParseFormatPlugin);
dayjs.extend(localizedFormatPlugin);
dayjs.extend(isBetweenPlugin);

export default dayjs;
