@echo off
echo ========================================
echo   GoID Biometric Tray Service Installer
echo ========================================
echo.
echo This will install the GoID Biometric Service as a system tray application
echo that starts automatically with Windows.
echo.

REM Get current directory
set SERVICE_DIR=%~dp0

echo Installing to: %SERVICE_DIR%
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

echo ✅ Python is available
echo.

REM Install required packages
echo Installing required Python packages...
python -m pip install pystray Pillow flask flask-cors

if %errorLevel% neq 0 (
    echo ❌ Failed to install required packages
    pause
    exit /b 1
)

echo ✅ Required packages installed
echo.

REM Check if service files exist
if not exist "%SERVICE_DIR%tray_service.py" (
    echo ❌ tray_service.py not found in current directory
    pause
    exit /b 1
)

if not exist "%SERVICE_DIR%improved_biometric_service.py" (
    echo ❌ improved_biometric_service.py not found in current directory
    pause
    exit /b 1
)

if not exist "%SERVICE_DIR%ftrScanAPI.dll" (
    echo ❌ ftrScanAPI.dll not found in current directory
    echo Please ensure Futronic SDK DLLs are present
    pause
    exit /b 1
)

if not exist "%SERVICE_DIR%ftrMathAPI.dll" (
    echo ❌ ftrMathAPI.dll not found in current directory
    echo Please ensure Futronic SDK DLLs are present
    pause
    exit /b 1
)

echo ✅ All required files found
echo.

REM Create logs directory
if not exist "%SERVICE_DIR%logs" mkdir "%SERVICE_DIR%logs"

REM Add to Windows startup registry
echo Adding to Windows startup...
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "GoID_Biometric_Service" /t REG_SZ /d "\"%SERVICE_DIR%start_tray_silent.pyw\"" /f

if %errorLevel% equ 0 (
    echo ✅ Added to Windows startup
) else (
    echo ❌ Failed to add to Windows startup
    pause
    exit /b 1
)

REM Create desktop shortcut
echo Creating desktop shortcut...
set DESKTOP=%USERPROFILE%\Desktop
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\CreateShortcut.vbs"
echo sLinkFile = "%DESKTOP%\GoID Biometric Service.lnk" >> "%TEMP%\CreateShortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\CreateShortcut.vbs"
echo oLink.TargetPath = "%SERVICE_DIR%start_tray_silent.pyw" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.WorkingDirectory = "%SERVICE_DIR%" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.Description = "GoID Biometric Service" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.Save >> "%TEMP%\CreateShortcut.vbs"
cscript "%TEMP%\CreateShortcut.vbs" >nul
del "%TEMP%\CreateShortcut.vbs"

echo ✅ Desktop shortcut created
echo.

REM Start the service now
echo Starting GoID Biometric Service...
start "" "%SERVICE_DIR%start_tray_silent.pyw"

REM Wait a moment for service to start
timeout /t 3 /nobreak >nul

echo.
echo ========================================
echo   Installation Complete!
echo ========================================
echo.
echo ✅ GoID Biometric Service has been installed successfully!
echo.
echo Features:
echo - Starts automatically with Windows
echo - Runs in system tray (look for fingerprint icon)
echo - Auto-restarts if service crashes
echo - Web interface at http://localhost:8001
echo.
echo Usage:
echo - Right-click tray icon for options
echo - Left-click tray icon for status
echo - Green icon = Service running
echo - Red icon = Service stopped
echo - Gray icon = Starting/stopping
echo.
echo The service is now running in your system tray.
echo You can close this window safely.
echo.
pause
