#!/usr/bin/env python3
"""
Debug why the tenant API returns empty data despite tenants existing in database.
"""

import requests
import json

def debug_empty_tenant_response():
    """Debug the empty tenant API response issue."""
    
    base_url = "http://10.139.8.141:8000"
    
    print("🔍 Debugging Empty Tenant API Response")
    print("=" * 45)
    
    # Step 1: Get authentication token
    print("📍 Step 1: Getting authentication token...")
    
    try:
        auth_response = requests.post(
            f"{base_url}/api/auth/token/",
            json={"email": "<EMAIL>", "password": "admin123"},
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if auth_response.status_code == 200:
            auth_data = auth_response.json()
            access_token = auth_data.get('access')
            print("✅ Authentication successful")
            
            # Decode token to see user info
            import base64
            try:
                # Split token and decode payload
                header, payload, signature = access_token.split('.')
                payload += '=' * (4 - len(payload) % 4)  # Add padding
                decoded_payload = base64.urlsafe_b64decode(payload)
                token_data = json.loads(decoded_payload)
                
                print(f"Token user info:")
                print(f"  User ID: {token_data.get('user_id')}")
                print(f"  Email: {token_data.get('email')}")
                print(f"  Is Superuser: {token_data.get('is_superuser')}")
                print(f"  Role: {token_data.get('role')}")
                print(f"  Tenant ID: {token_data.get('tenant_id')}")
                
            except Exception as e:
                print(f"Could not decode token: {e}")
                
        else:
            print(f"❌ Authentication failed: {auth_response.text}")
            return
            
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return
    
    # Step 2: Test tenant API with detailed response analysis
    print(f"\n📍 Step 2: Testing tenant API with detailed analysis...")
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{base_url}/api/tenants/", headers=headers, timeout=10)
        
        print(f"API Response Status: {response.status_code}")
        print(f"Response Headers:")
        for key, value in response.headers.items():
            print(f"  {key}: {value}")
        
        print(f"Response Size: {len(response.content)} bytes")
        print(f"Raw Response: {response.text}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"\n✅ Valid JSON Response")
                print(f"Response Type: {type(data)}")
                print(f"Response Structure: {data}")
                
                if isinstance(data, list):
                    print(f"Direct array with {len(data)} items")
                    if len(data) == 0:
                        print("❌ ISSUE: API returning empty array")
                    else:
                        print(f"First tenant: {data[0]}")
                        
                elif isinstance(data, dict):
                    if 'results' in data:
                        results = data.get('results', [])
                        count = data.get('count', 0)
                        print(f"Paginated response:")
                        print(f"  Count: {count}")
                        print(f"  Results: {len(results)} items")
                        
                        if count == 0 or len(results) == 0:
                            print("❌ ISSUE: API returning empty results")
                        else:
                            print(f"  First tenant: {results[0]}")
                    else:
                        print(f"Dict response: {data}")
                        
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error: {e}")
                
        else:
            print(f"❌ API failed with status {response.status_code}")
            print(f"Error response: {response.text}")
            
    except Exception as e:
        print(f"❌ API request error: {e}")
    
    # Step 3: Compare with Django admin data
    print(f"\n📍 Step 3: Checking Django admin for comparison...")
    
    try:
        admin_response = requests.get(f"{base_url}/admin/tenants/tenant/", timeout=10)
        
        if admin_response.status_code == 200:
            admin_html = admin_response.text
            
            # Look for tenant indicators in HTML
            if "0 tenants" in admin_html or "No tenants" in admin_html:
                print("❌ Django admin shows no tenants")
            elif "tenant" in admin_html.lower() and ("Test" in admin_html or "Addis" in admin_html or "Bole" in admin_html):
                print("✅ Django admin shows tenant data exists")
                
                # Try to extract tenant names from HTML
                import re
                tenant_matches = re.findall(r'<a[^>]*>([^<]*(?:Test|Addis|Bole)[^<]*)</a>', admin_html)
                if tenant_matches:
                    print(f"Found tenant names in admin: {tenant_matches[:5]}")
            else:
                print("? Django admin response unclear")
                
        else:
            print(f"❌ Cannot access Django admin: {admin_response.status_code}")
            
    except Exception as e:
        print(f"❌ Django admin check failed: {e}")

def check_database_directly():
    """Check the database directly via Django shell."""
    
    print(f"\n📍 Step 4: Checking database directly...")
    
    import subprocess
    
    # Check tenants in database
    django_script = '''
from tenants.models import Tenant
from django.contrib.auth import get_user_model

print("=== DATABASE CHECK ===")

# Check all tenants
all_tenants = Tenant.objects.all()
print(f"Total tenants in database: {all_tenants.count()}")

if all_tenants.exists():
    print("\\nTenants in database:")
    for tenant in all_tenants:
        print(f"- ID: {tenant.id}, Name: {tenant.name}, Type: {tenant.type}")
        print(f"  Schema: {tenant.schema_name}, Active: {tenant.is_active}")
else:
    print("❌ NO TENANTS IN DATABASE!")

print("\\n=== USER CHECK ===")

# Check admin user
User = get_user_model()
try:
    admin_user = User.objects.get(email='<EMAIL>')
    print(f"Admin user: {admin_user.email}")
    print(f"Is superuser: {admin_user.is_superuser}")
    print(f"Is staff: {admin_user.is_staff}")
    print(f"Is active: {admin_user.is_active}")
except User.DoesNotExist:
    print("❌ Admin user not found!")

print("\\n=== VIEWSET TEST ===")

# Test the viewset directly
try:
    from tenants.views.tenant_views import TenantViewSet
    from django.test import RequestFactory
    from django.contrib.auth.models import AnonymousUser
    
    factory = RequestFactory()
    request = factory.get('/api/tenants/')
    request.user = admin_user
    
    viewset = TenantViewSet()
    viewset.request = request
    
    # Test queryset
    queryset = viewset.get_queryset()
    print(f"ViewSet queryset count: {queryset.count()}")
    
    if queryset.count() == 0:
        print("❌ ISSUE: ViewSet queryset returns no results!")
        print("This explains why API returns empty data.")
        
        # Check if it's a filtering issue
        all_tenants_qs = Tenant.objects.all()
        print(f"Raw Tenant.objects.all() count: {all_tenants_qs.count()}")
        
        if all_tenants_qs.count() > 0:
            print("❌ ISSUE: ViewSet is filtering out all tenants!")
            print("Check the get_queryset() method in TenantViewSet.")
    else:
        print("✅ ViewSet queryset returns tenants")
        for tenant in queryset[:3]:
            print(f"  - {tenant.name} ({tenant.type})")
            
except Exception as e:
    print(f"❌ ViewSet test failed: {e}")
    import traceback
    traceback.print_exc()
'''
    
    try:
        result = subprocess.run(
            ["docker-compose", "exec", "-T", "backend", "python", "manage.py", "shell", "-c", django_script],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        print("Database check output:")
        print(result.stdout)
        
        if result.stderr:
            print("Errors:")
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ Database check failed: {e}")

if __name__ == "__main__":
    debug_empty_tenant_response()
    check_database_directly()
    
    print("\n" + "=" * 45)
    print("🔧 Analysis Summary:")
    print("")
    print("If API returns 200 OK but empty data:")
    print("1. ✅ Authentication is working")
    print("2. ✅ API endpoint is accessible") 
    print("3. ❌ ViewSet is filtering out all tenants")
    print("")
    print("Most likely causes:")
    print("- ViewSet get_queryset() method has incorrect filtering")
    print("- Permission check is failing silently")
    print("- User doesn't have access to view tenants")
    print("- Tenant model queryset is being filtered by user context")
    print("")
    print("💡 Solutions to try:")
    print("1. Check TenantViewSet.get_queryset() method")
    print("2. Verify user permissions in CanManageTenants")
    print("3. Test with different user or role")
    print("4. Check if ViewSet is using tenant-specific filtering")
    print("")
    print("🔍 Next steps:")
    print("- Look at the ViewSet get_queryset() implementation")
    print("- Check if it's filtering by user.tenant or similar")
    print("- Verify superuser should see all tenants")
    print("- Test with raw Tenant.objects.all() in Django shell")
