#!/usr/bin/env python3
"""
Comprehensive diagnostic script for tenant list issues.
"""

import requests
import json
import subprocess
import sys

def run_docker_command(command):
    """Run a docker-compose command and return output."""
    try:
        result = subprocess.run(
            f"docker-compose {command}",
            shell=True,
            capture_output=True,
            text=True,
            timeout=30
        )
        return result.stdout, result.stderr, result.returncode
    except subprocess.TimeoutExpired:
        return "", "Command timed out", 1
    except Exception as e:
        return "", str(e), 1

def test_tenant_list_comprehensive():
    """Comprehensive test of tenant list functionality."""
    
    base_url = "http://************:8000"
    
    print("🔍 Comprehensive Tenant List Diagnostic")
    print("=" * 50)
    
    # Step 1: Check container status
    print("📍 Step 1: Checking container status...")
    stdout, stderr, code = run_docker_command("ps")
    print("Container Status:")
    print(stdout)
    if code != 0:
        print(f"Error: {stderr}")
    
    # Step 2: Check backend logs
    print("\n📍 Step 2: Checking recent backend logs...")
    stdout, stderr, code = run_docker_command("logs --tail=20 backend")
    print("Recent Backend Logs:")
    print(stdout)
    if stderr:
        print("Errors:")
        print(stderr)
    
    # Step 3: Test basic connectivity
    print("\n📍 Step 3: Testing basic connectivity...")
    try:
        response = requests.get(f"{base_url}/admin/", timeout=10)
        print(f"Admin endpoint: {response.status_code}")
        if response.status_code in [200, 302]:
            print("✅ Backend is responding")
        else:
            print("❌ Backend not responding properly")
            return
    except Exception as e:
        print(f"❌ Cannot connect to backend: {e}")
        return
    
    # Step 4: Test authentication
    print("\n📍 Step 4: Testing authentication...")
    auth_scenarios = [
        {"email": "<EMAIL>", "password": "admin123"},
        {"email": "<EMAIL>", "password": "admin123"},
        {"username": "<EMAIL>", "password": "admin123"}
    ]
    
    working_token = None
    for i, creds in enumerate(auth_scenarios):
        try:
            response = requests.post(
                f"{base_url}/api/auth/token/",
                json=creds,
                timeout=10
            )
            print(f"Auth scenario {i+1}: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                working_token = data.get('access')
                print(f"✅ Authentication successful with: {creds}")
                break
            else:
                print(f"❌ Auth failed: {response.text[:100]}")
                
        except Exception as e:
            print(f"❌ Auth error: {e}")
    
    if not working_token:
        print("\n❌ No working authentication found!")
        print("💡 Creating superuser...")
        
        # Try to create superuser
        stdout, stderr, code = run_docker_command(
            'exec -T backend python manage.py shell -c "'
            'from django.contrib.auth import get_user_model; '
            'User = get_user_model(); '
            'user, created = User.objects.get_or_create('
            'email=\"<EMAIL>\", '
            'defaults={\"username\": \"admin\", \"is_superuser\": True, \"is_staff\": True}); '
            'user.set_password(\"admin123\"); '
            'user.save(); '
            'print(f\"User created/updated: {user.email}, Superuser: {user.is_superuser}\")"'
        )
        print("Superuser creation result:")
        print(stdout)
        if stderr:
            print("Errors:")
            print(stderr)
        
        # Try auth again
        try:
            response = requests.post(
                f"{base_url}/api/auth/token/",
                json={"email": "<EMAIL>", "password": "admin123"},
                timeout=10
            )
            if response.status_code == 200:
                working_token = response.json().get('access')
                print("✅ Authentication now working after superuser creation")
            else:
                print(f"❌ Still can't authenticate: {response.text}")
        except Exception as e:
            print(f"❌ Auth still failing: {e}")
    
    if not working_token:
        print("\n❌ Cannot proceed without authentication")
        return
    
    # Step 5: Test tenant endpoints
    print(f"\n📍 Step 5: Testing tenant endpoints with token...")
    headers = {"Authorization": f"Bearer {working_token}"}
    
    endpoints = [
        "/api/tenants/",
        "/api/tenants/subcities/",
        "/api/tenants/kebeles/",
        "/api/shared/countries/"
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", headers=headers, timeout=10)
            print(f"{endpoint}: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if isinstance(data, list):
                        count = len(data)
                    elif isinstance(data, dict) and 'results' in data:
                        count = len(data['results'])
                    else:
                        count = "unknown"
                    print(f"  ✅ Success - {count} items")
                    
                    # Show first item if available
                    if isinstance(data, list) and data:
                        print(f"  📋 Sample item: {data[0]}")
                    elif isinstance(data, dict) and data.get('results'):
                        print(f"  📋 Sample item: {data['results'][0]}")
                        
                except json.JSONDecodeError:
                    print(f"  ⚠️  Response not JSON: {response.text[:100]}")
            else:
                print(f"  ❌ Failed: {response.text[:100]}")
                
        except Exception as e:
            print(f"  ❌ Error: {e}")
    
    # Step 6: Check database directly
    print(f"\n📍 Step 6: Checking database directly...")
    
    # Check if tenants exist in database
    stdout, stderr, code = run_docker_command(
        'exec -T backend python manage.py shell -c "'
        'from tenants.models import Tenant; '
        'tenants = Tenant.objects.all(); '
        'print(f\"Total tenants in database: {tenants.count()}\"); '
        'for t in tenants[:5]: print(f\"- {t.name} ({t.type}) - Schema: {t.schema_name}\")"'
    )
    print("Database tenant check:")
    print(stdout)
    if stderr:
        print("Errors:")
        print(stderr)
    
    # Step 7: Check URL patterns
    print(f"\n📍 Step 7: Testing URL patterns...")
    stdout, stderr, code = run_docker_command(
        'exec -T backend python manage.py shell -c "'
        'from django.urls import resolve; '
        'try: '
        '    match = resolve(\"/api/tenants/\"); '
        '    print(f\"Tenants URL resolves to: {match.func}\"); '
        'except Exception as e: '
        '    print(f\"Tenants URL resolution failed: {e}\"); '
        'try: '
        '    from tenants.views import TenantViewSet; '
        '    print(f\"TenantViewSet found: {TenantViewSet}\"); '
        'except Exception as e: '
        '    print(f\"TenantViewSet import failed: {e}\")"'
    )
    print("URL pattern check:")
    print(stdout)
    if stderr:
        print("Errors:")
        print(stderr)

if __name__ == "__main__":
    test_tenant_list_comprehensive()
    
    print("\n" + "=" * 50)
    print("🔧 Next Steps Based on Results:")
    print("1. If containers not running: docker-compose up -d")
    print("2. If auth failing: Check superuser creation above")
    print("3. If 404 on tenants: Check URL patterns and middleware")
    print("4. If empty response: Check database and permissions")
    print("5. If database empty: Create test tenants")
    print("")
    print("💡 Quick fixes to try:")
    print("- docker-compose restart backend")
    print("- docker-compose exec backend python manage.py migrate")
    print("- docker-compose exec backend python manage.py collectstatic --noinput")
    print("")
    print("🚨 If still failing:")
    print("- Share the output above for detailed analysis")
    print("- Check: docker-compose logs backend | grep ERROR")
    print("- Try: docker-compose down && docker-compose up -d")
