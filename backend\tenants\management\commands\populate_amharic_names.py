from django.core.management.base import BaseCommand
from tenants.models import Tenant
from tenants.models.kebele import Kebele
from tenants.models.subcity import SubCity
from tenants.models.city import CityAdministration


class Command(BaseCommand):
    help = 'Populate Amharic names in tenant profile tables'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting to populate Amharic names in tenant profile tables...'))
        
        # Mapping of English names to Amharic names
        name_mappings = {
            # Cities
            'gondar': 'ጎንደር',
            'addis ababa': 'አዲስ አበባ',
            'bahir dar': 'ባሕር ዳር',
            'dire dawa': 'ድሬ ዳዋ',
            'mekelle': 'መቀሌ',
            'hawassa': 'ሐዋሳ',
            'adama': 'አዳማ',
            'jimma': 'ጅማ',
            
            # Subcities/Areas
            'zoble': 'ዞብል',
            'bole': 'ቦሌ',
            'gabriel': 'ገብርኤል',
            'arada': 'አራዳ',
            'kirkos': 'ቂርቆስ',
            'yeka': 'የካ',
            'gulele': 'ጉለሌ',
            'addis ketema': 'አዲስ ከተማ',
            'akaki kality': 'አቃቂ ቃሊቲ',
            'nifas silk-lafto': 'ንፋስ ስልክ ላፍቶ',
            'kolfe keranio': 'ኮልፌ ቀራኒዮ',
            'lideta': 'ልደታ',
            
            # Kebeles (numbered)
            'kebele01': 'ቀበሌ 01',
            'kebele02': 'ቀበሌ 02',
            'kebele03': 'ቀበሌ 03',
            'kebele04': 'ቀበሌ 04',
            'kebele05': 'ቀበሌ 05',
            'kebele06': 'ቀበሌ 06',
            'kebele07': 'ቀበሌ 07',
            'kebele08': 'ቀበሌ 08',
            'kebele09': 'ቀበሌ 09',
            'kebele10': 'ቀበሌ 10',
            'kebele11': 'ቀበሌ 11',
            'kebele12': 'ቀበሌ 12',
            'kebele13': 'ቀበሌ 13',
            'kebele14': 'ቀበሌ 14',
            'kebele15': 'ቀበሌ 15',
            'kebele16': 'ቀበሌ 16',
            'kebele17': 'ቀበሌ 17',
            'kebele18': 'ቀበሌ 18',
            'kebele19': 'ቀበሌ 19',
            'kebele20': 'ቀበሌ 20',
            
            # Alternative kebele formats
            'kebele 01': 'ቀበሌ 01',
            'kebele 02': 'ቀበሌ 02',
            'kebele 03': 'ቀበሌ 03',
            'kebele 04': 'ቀበሌ 04',
            'kebele 05': 'ቀበሌ 05',
            'kebele 06': 'ቀበሌ 06',
            'kebele 07': 'ቀበሌ 07',
            'kebele 08': 'ቀበሌ 08',
            'kebele 09': 'ቀበሌ 09',
            'kebele 10': 'ቀበሌ 10',
            'kebele 11': 'ቀበሌ 11',
            'kebele 12': 'ቀበሌ 12',
            'kebele 13': 'ቀበሌ 13',
            'kebele 14': 'ቀበሌ 14',
            'kebele 15': 'ቀበሌ 15',
            'kebele 16': 'ቀበሌ 16',
            'kebele 17': 'ቀበሌ 17',
            'kebele 18': 'ቀበሌ 18',
            'kebele 19': 'ቀበሌ 19',
            'kebele 20': 'ቀበሌ 20',
        }
        
        def get_amharic_name(english_name):
            """Convert English name to Amharic using mappings or intelligent conversion."""
            if not english_name:
                return None
                
            # Try exact match first
            lower_name = english_name.lower().strip()
            if lower_name in name_mappings:
                return name_mappings[lower_name]
            
            # Try intelligent conversion for numbered kebeles
            if 'kebele' in lower_name:
                # Extract number from kebele name
                import re
                number_match = re.search(r'\d+', english_name)
                if number_match:
                    number = number_match.group()
                    return f'ቀበሌ {number.zfill(2)}'  # Pad with zero if single digit
            
            # Return None if no conversion found
            return None
        
        # Update City Administration Amharic names
        self.stdout.write('Updating City Administration Amharic names...')
        city_count = 0
        for city_admin in CityAdministration.objects.all():
            amharic_name = get_amharic_name(city_admin.city_name)
            if amharic_name and not city_admin.city_name_am:
                city_admin.city_name_am = amharic_name
                city_admin.save()
                city_count += 1
                self.stdout.write(f'  ✅ {city_admin.city_name} → {amharic_name}')
        
        # Update SubCity Amharic names
        self.stdout.write('Updating SubCity Amharic names...')
        subcity_count = 0
        for subcity in SubCity.objects.all():
            amharic_name = get_amharic_name(subcity.name)
            if amharic_name and not subcity.name_am:
                subcity.name_am = amharic_name
                subcity.save()
                subcity_count += 1
                self.stdout.write(f'  ✅ {subcity.name} → {amharic_name}')
        
        # Update Kebele Amharic names
        self.stdout.write('Updating Kebele Amharic names...')
        kebele_count = 0
        for kebele in Kebele.objects.all():
            amharic_name = get_amharic_name(kebele.name)
            if amharic_name and not kebele.name_am:
                kebele.name_am = amharic_name
                kebele.save()
                kebele_count += 1
                self.stdout.write(f'  ✅ {kebele.name} → {amharic_name}')
        
        # Also update Tenant table Amharic names for consistency
        self.stdout.write('Updating Tenant table Amharic names...')
        tenant_count = 0
        for tenant in Tenant.objects.all():
            amharic_name = get_amharic_name(tenant.name)
            if amharic_name and not tenant.name_am:
                tenant.name_am = amharic_name
                tenant.save()
                tenant_count += 1
                self.stdout.write(f'  ✅ {tenant.name} → {amharic_name}')
        
        self.stdout.write(
            self.style.SUCCESS(
                f'\n✅ Amharic names population completed!\n'
                f'   Cities updated: {city_count}\n'
                f'   Subcities updated: {subcity_count}\n'
                f'   Kebeles updated: {kebele_count}\n'
                f'   Tenants updated: {tenant_count}'
            )
        )
