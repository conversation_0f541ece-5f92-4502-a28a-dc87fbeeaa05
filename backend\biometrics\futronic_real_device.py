"""
Real Futronic FS88H Device Integration for GoID Backend
"""

import os
import sys
import ctypes
from ctypes import cdll, c_int, c_uint, c_void_p, POINTER, byref
import logging
import base64
import json
import time
from datetime import datetime
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

# Constants
FTR_OK = 0
FTR_IMAGE_WIDTH = 320
FTR_IMAGE_HEIGHT = 480
FTR_IMAGE_SIZE = FTR_IMAGE_WIDTH * FTR_IMAGE_HEIGHT

class FingerprintTemplate:
    """Fingerprint template data structure"""
    def __init__(self, thumb_type: str, template_data: str, quality_score: int, 
                 minutiae_count: int, capture_time: str, device_info: dict):
        self.thumb_type = thumb_type
        self.template_data = template_data
        self.quality_score = quality_score
        self.minutiae_count = minutiae_count
        self.capture_time = capture_time
        self.device_info = device_info

class RealFutronicDevice:
    """Real Futronic FS88H device integration for GoID backend"""
    
    def __init__(self):
        self.scan_api = None
        self.device_handle = None
        self.connected = False
        self.initialized = False
        
        # SDK DLL location - fingerPrint folder contains all required SDK files
        self.dll_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
            'local-biometric-service', 
            'fingerPrint'
        )
    
    def initialize(self) -> bool:
        """Initialize the real Futronic device"""
        try:
            logger.info("🔧 Initializing real Futronic FS88H device for GoID backend...")
            
            # Load the SDK DLL from fingerPrint folder
            dll_file = os.path.join(self.dll_path, 'ftrScanAPI.dll')
            
            if not os.path.exists(dll_file):
                logger.error(f"❌ ftrScanAPI.dll not found at: {dll_file}")
                logger.error("Please ensure Futronic SDK files are in local-biometric-service/fingerPrint folder")
                return False
            
            logger.info(f"📁 Loading SDK DLL from: {dll_file}")
            self.scan_api = cdll.LoadLibrary(dll_file)
            logger.info("✅ SDK DLL loaded successfully")
            
            # Setup function signatures
            self.scan_api.ftrScanOpenDevice.restype = c_void_p
            self.scan_api.ftrScanCloseDevice.argtypes = [c_void_p]
            self.scan_api.ftrScanCloseDevice.restype = c_int
            self.scan_api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(ctypes.c_ubyte), POINTER(c_uint)]
            self.scan_api.ftrScanGetFrame.restype = c_int
            
            logger.info("✅ SDK function signatures configured")
            
            # Open device
            logger.info("📱 Opening Futronic device...")
            self.device_handle = self.scan_api.ftrScanOpenDevice()
            
            if self.device_handle:
                logger.info(f"✅ Real Futronic device opened! Handle: {self.device_handle}")
                self.connected = True
                self.initialized = True
                return True
            else:
                logger.warning("❌ Failed to open real Futronic device")
                return False
                
        except Exception as e:
            logger.error(f"❌ Real device initialization error: {e}")
            return False
    
    def capture_fingerprint(self, thumb_type: str) -> Optional[FingerprintTemplate]:
        """Capture fingerprint from real device"""
        try:
            if not self.connected:
                logger.warning("⚠️ Device not connected - cannot capture")
                return None
            
            logger.info(f"📸 Capturing {thumb_type} thumb from real device...")
            
            # Create buffer for image
            buffer_size = FTR_IMAGE_SIZE
            image_buffer = (ctypes.c_ubyte * buffer_size)()
            frame_size = c_uint(buffer_size)
            
            # Wait for finger placement and capture
            logger.info("👆 Please place your finger on the scanner...")
            
            # Wait for finger presence (up to 30 seconds)
            finger_detected = False
            start_time = time.time()
            timeout = 30
            
            # Check if device supports finger detection
            try:
                finger_present = c_int()
                self.scan_api.ftrScanIsFingerPresent.argtypes = [c_void_p, POINTER(c_int)]
                self.scan_api.ftrScanIsFingerPresent.restype = c_int
                
                while (time.time() - start_time) < timeout and not finger_detected:
                    result = self.scan_api.ftrScanIsFingerPresent(self.device_handle, byref(finger_present))
                    if result == FTR_OK and finger_present.value > 0:
                        logger.info("🔍 Finger detected!")
                        finger_detected = True
                        time.sleep(0.2)  # Allow finger to settle
                        break
                    time.sleep(0.1)
                    
                if not finger_detected:
                    logger.warning("⚠️ No finger detected within timeout period")
                    return None
                    
            except Exception as e:
                logger.warning(f"Finger detection not available: {e}. Proceeding with direct capture...")
            
            # Capture frame
            result = self.scan_api.ftrScanGetFrame(self.device_handle, image_buffer, byref(frame_size))
            actual_size = frame_size.value
            
            logger.info(f"📊 Capture result: code={result}, size={actual_size}")
            
            if result != FTR_OK:
                logger.error(f"❌ Capture failed with error code: {result}")
                return None
            
            if actual_size > 0:
                # Process captured data
                image_bytes = bytes(image_buffer[:actual_size])
                
                # Enhanced quality analysis for real fingerprint data
                quality_score = self._analyze_fingerprint_quality(image_bytes, actual_size)
                
                # Create real fingerprint template with actual image data
                template_data = {
                    'version': '2.0',
                    'thumb_type': thumb_type,
                    'frame_size': actual_size,
                    'image_width': FTR_IMAGE_WIDTH,
                    'image_height': FTR_IMAGE_HEIGHT,
                    'quality_score': quality_score,
                    'fingerprint_image': base64.b64encode(image_bytes).decode(),
                    'capture_time': datetime.now().isoformat(),
                    'device_info': {
                        'model': 'Futronic FS88H',
                        'real_device': True,
                        'interface': 'futronic_sdk',
                        'sdk_version': '4.2.1',
                        'result_code': result,
                        'dll_path': self.dll_path
                    }
                }
                
                # Encode template
                template_encoded = base64.b64encode(json.dumps(template_data).encode()).decode()
                
                # Calculate minutiae count based on quality and image characteristics
                minutiae_count = self._estimate_minutiae_count(image_bytes, quality_score)
                
                # Create fingerprint template
                template = FingerprintTemplate(
                    thumb_type=thumb_type,
                    template_data=template_encoded,
                    quality_score=quality_score,
                    minutiae_count=minutiae_count,
                    capture_time=template_data['capture_time'],
                    device_info=template_data['device_info']
                )
                
                logger.info(f"✅ Real fingerprint captured: Quality {quality_score}%, Size {actual_size} bytes, Minutiae: {minutiae_count}")
                return template
            else:
                logger.warning("⚠️ No fingerprint data captured from real device")
                return None
                
        except Exception as e:
            logger.error(f"❌ Real device capture error: {e}")
            return None
    
    def get_device_status(self) -> Dict[str, Any]:
        """Get real device status"""
        return {
            'connected': self.connected,
            'initialized': self.initialized,
            'device_handle': self.device_handle,
            'model': 'Futronic FS88H',
            'interface': 'real_device',
            'real_device': True
        }
    
    def disconnect(self):
        """Disconnect from real device"""
        if self.device_handle and self.scan_api:
            try:
                self.scan_api.ftrScanCloseDevice(self.device_handle)
                logger.info("🔒 Real device disconnected")
            except Exception as e:
                logger.error(f"❌ Error disconnecting real device: {e}")
            finally:
                self.device_handle = None
                self.connected = False
                self.initialized = False
    
    def _analyze_fingerprint_quality(self, image_bytes: bytes, image_size: int) -> int:
        """Analyze real fingerprint image quality"""
        try:
            # Basic quality metrics for fingerprint image
            total_pixels = len(image_bytes)
            
            # Count non-zero pixels (actual fingerprint data)
            non_zero_pixels = sum(1 for byte in image_bytes if byte > 0)
            data_ratio = non_zero_pixels / total_pixels if total_pixels > 0 else 0
            
            # Analyze pixel intensity distribution
            high_intensity_pixels = sum(1 for byte in image_bytes if byte > 128)
            medium_intensity_pixels = sum(1 for byte in image_bytes if 64 < byte <= 128)
            low_intensity_pixels = sum(1 for byte in image_bytes if 0 < byte <= 64)
            
            # Calculate contrast (distribution of intensities)
            total_non_zero = non_zero_pixels if non_zero_pixels > 0 else 1
            contrast_score = (high_intensity_pixels + medium_intensity_pixels) / total_non_zero
            
            # Calculate base quality score
            base_quality = min(100, int(data_ratio * 100))
            contrast_bonus = min(20, int(contrast_score * 20))
            
            # Final quality score
            quality_score = min(100, max(10, base_quality + contrast_bonus))
            
            logger.info(f"📊 Quality analysis: Data ratio: {data_ratio:.2f}, Contrast: {contrast_score:.2f}, Score: {quality_score}%")
            return quality_score
            
        except Exception as e:
            logger.warning(f"Quality analysis failed: {e}, using default score")
            return 75  # Default quality score
    
    def _estimate_minutiae_count(self, image_bytes: bytes, quality_score: int) -> int:
        """Estimate minutiae count based on image characteristics"""
        try:
            # Basic minutiae estimation based on quality and image complexity
            base_minutiae = 30  # Minimum expected minutiae for a valid fingerprint
            
            # Quality-based adjustment
            quality_factor = quality_score / 100.0
            quality_minutiae = int(base_minutiae * quality_factor)
            
            # Image complexity analysis (simple edge detection approximation)
            complexity_score = 0
            for i in range(1, min(1000, len(image_bytes))):  # Sample first 1000 bytes
                if abs(image_bytes[i] - image_bytes[i-1]) > 50:  # Significant intensity change
                    complexity_score += 1
            
            complexity_factor = min(1.5, complexity_score / 500.0)  # Normalize
            complexity_minutiae = int(base_minutiae * complexity_factor)
            
            # Final minutiae count
            total_minutiae = min(80, max(15, quality_minutiae + complexity_minutiae))
            
            logger.info(f"📊 Minutiae estimation: Quality factor: {quality_factor:.2f}, Complexity: {complexity_factor:.2f}, Count: {total_minutiae}")
            return total_minutiae
            
        except Exception as e:
            logger.warning(f"Minutiae estimation failed: {e}, using default count")
            return max(15, min(60, quality_score // 2))  # Fallback based on quality

# Global device instance
_real_device_instance = None

def get_real_device() -> RealFutronicDevice:
    """Get the global real device instance"""
    global _real_device_instance
    if _real_device_instance is None:
        _real_device_instance = RealFutronicDevice()
        _real_device_instance.initialize()
    return _real_device_instance
