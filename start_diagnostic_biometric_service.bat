@echo off
echo ========================================
echo   GoID Diagnostic Biometric Service
echo ========================================
echo.
echo This version includes extensive diagnostics
echo and progressive error handling to identify
echo the root cause of SDK crashes.
echo.
echo 🔍 Pre-flight checks...
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed or not in PATH
    pause
    exit /b 1
)
echo ✅ Python found

REM Check directory
if not exist "local-biometric-service" (
    echo ❌ local-biometric-service directory not found
    pause
    exit /b 1
)
echo ✅ Service directory found

REM Check for DLL files
cd local-biometric-service
if exist "ftrScanAPI.dll" (
    echo ✅ ftrScanAPI.dll found
) else (
    echo ⚠️ ftrScanAPI.dll not found
)

echo.
echo 🚀 Starting DIAGNOSTIC service on port 8001...
echo 📱 Service URL: http://localhost:8001
echo 🧪 Device Test: http://localhost:8001/api/device/test
echo 🌐 Health Check: http://localhost:8001/api/health
echo.
echo ⚠️  DIAGNOSTIC MODE FEATURES:
echo    - Extensive error logging and diagnostics
echo    - Progressive buffer size testing
echo    - Device function validation
echo    - Safe capture with multiple fallbacks
echo    - Service continues even if device fails
echo.
echo 📋 TROUBLESHOOTING ENDPOINTS:
echo    - GET /api/device/status - Check device state
echo    - POST /api/device/test - Test device functions
echo    - GET /api/health - Service health check
echo.
echo ❌ Close this window to stop the service
echo ⌨️  Press Ctrl+C to stop the service gracefully
echo.

python diagnostic_fingerprint_service.py

echo.
echo Diagnostic service stopped.
echo.
echo 📋 If the service crashed, check the logs above for:
echo    - DLL loading errors
echo    - Device handle issues
echo    - Function signature problems
echo    - Buffer size failures
echo    - SDK compatibility issues
echo.
echo 🔧 Next steps:
echo    1. Try different DLL versions if available
echo    2. Check if device is used by other software
echo    3. Restart the device (unplug/replug USB)
echo    4. Try running as Administrator
echo.
pause
