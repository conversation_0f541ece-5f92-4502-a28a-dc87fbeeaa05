import React, { useRef } from 'react';
import { <PERSON>ton, CircularProgress } from '@mui/material';
import { Print as PrintIcon, FlashOn as DirectPrintIcon } from '@mui/icons-material';
import html2canvas from 'html2canvas';

/**
 * OneClickPrint - Component for direct printing without dialogs
 * Supports multiple print methods including Windows Photo Viewer
 */
const OneClickPrint = ({
  children,
  onPrintComplete,
  onPrintError,
  disabled = false,
  loading = false,
  buttonText = "🖨️ Direct Print",
  variant = "contained",
  color = "primary",
  size = "medium",
  printMethod = "browser" // "browser", "photo-viewer", "download-and-open"
}) => {
  const printRef = useRef();

  // Open image in new window for easy saving and photo viewer access
  const handleImageWindowPrint = async () => {
    try {
      const element = printRef.current;
      if (!element) {
        const error = 'Print element not found';
        console.error(error);
        if (onPrintError) onPrintError(error);
        return;
      }

      // Find the actual ID card element
      const idCardElement = element.querySelector('.security-pattern-container') ||
                           element.querySelector('[class*="card"]') ||
                           element.firstElementChild ||
                           element;

      if (!idCardElement) {
        const error = 'ID card element not found';
        console.error(error);
        if (onPrintError) onPrintError(error);
        return;
      }

      // Wait for any images or content to load
      await new Promise(resolve => setTimeout(resolve, 300));

      // Generate high-resolution canvas
      const canvas = await html2canvas(idCardElement, {
        scale: 4, // Very high resolution for photo viewer printing
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: idCardElement.offsetWidth,
        height: idCardElement.offsetHeight,
        scrollX: 0,
        scrollY: 0,
        logging: false,
        removeContainer: true,
        foreignObjectRendering: true,
        imageTimeout: 10000,
      });

      // Check if canvas is valid
      if (canvas.width === 0 || canvas.height === 0) {
        const error = 'Failed to generate print canvas';
        console.error(error);
        if (onPrintError) onPrintError(error);
        return;
      }

      // Get image data URL
      const imageDataUrl = canvas.toDataURL('image/png', 1.0);
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const filename = `ID_Card_${timestamp}.png`;

      // Open image in new window with save and print options
      const imageWindow = window.open('', '_blank', 'width=900,height=700');
      imageWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>ID Card - Ready for Photo Viewer</title>
          <style>
            body {
              margin: 0;
              padding: 20px;
              background: #f5f5f5;
              font-family: Arial, sans-serif;
              display: flex;
              flex-direction: column;
              align-items: center;
              min-height: 100vh;
            }

            .controls {
              background: white;
              padding: 20px;
              border-radius: 8px;
              box-shadow: 0 2px 10px rgba(0,0,0,0.1);
              margin-bottom: 20px;
              text-align: center;
              max-width: 600px;
            }

            .controls h2 {
              color: #ff8f00;
              margin-top: 0;
            }

            .button-group {
              display: flex;
              gap: 10px;
              justify-content: center;
              flex-wrap: wrap;
              margin: 15px 0;
            }

            .btn {
              background: #ff8f00;
              color: white;
              border: none;
              padding: 12px 24px;
              border-radius: 4px;
              cursor: pointer;
              font-size: 14px;
              transition: background 0.2s;
              text-decoration: none;
              display: inline-block;
            }

            .btn:hover {
              background: #ef6c00;
            }

            .btn-secondary {
              background: #666;
            }

            .btn-secondary:hover {
              background: #555;
            }

            .image-container {
              background: white;
              padding: 20px;
              border-radius: 8px;
              box-shadow: 0 4px 8px rgba(0,0,0,0.15);
              display: inline-block;
            }

            .id-card-image {
              max-width: 100%;
              height: auto;
              border: 1px solid #ddd;
              border-radius: 4px;
            }

            .instructions {
              background: #e3f2fd;
              padding: 15px;
              border-radius: 4px;
              margin: 15px 0;
              font-size: 14px;
              line-height: 1.5;
            }

            .step {
              margin: 8px 0;
            }

            .step-number {
              font-weight: bold;
              color: #ff8f00;
            }
          </style>
        </head>
        <body>
          <div class="controls">
            <h2>📸 ID Card - Photo Viewer Print</h2>

            <div class="instructions">
              <div class="step"><span class="step-number">Option 1:</span> Right-click the image below → "Save image as..." → Open saved file with Photo Viewer → Print</div>
              <div class="step"><span class="step-number">Option 2:</span> Click "Download Image" → Open downloaded file → Print from Photo Viewer</div>
              <div class="step"><span class="step-number">Option 3:</span> Click "Print from Browser" for direct browser printing</div>
            </div>

            <div class="button-group">
              <button class="btn" onclick="downloadImage()">💾 Download Image</button>
              <button class="btn" onclick="window.print()">🖨️ Print from Browser</button>
              <button class="btn btn-secondary" onclick="window.close()">❌ Close</button>
            </div>
          </div>

          <div class="image-container">
            <img src="${imageDataUrl}" alt="ID Card" class="id-card-image" id="idCardImage" />
          </div>

          <script>
            function downloadImage() {
              const link = document.createElement('a');
              link.href = '${imageDataUrl}';
              link.download = '${filename}';
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);

              // Show success message
              alert('Image downloaded! Check your Downloads folder, then open the file with Photo Viewer to print.');
            }

            // Handle right-click context menu for saving
            document.getElementById('idCardImage').addEventListener('contextmenu', function(e) {
              // Allow default context menu for saving image
            });

            // Print styles
            const printStyles = \`
              @media print {
                body { margin: 0; padding: 0; background: white; }
                .controls { display: none !important; }
                .image-container {
                  background: transparent !important;
                  padding: 0 !important;
                  box-shadow: none !important;
                }
                .id-card-image {
                  width: 85.6mm !important;
                  height: 53.98mm !important;
                  border: none !important;
                  page-break-inside: avoid !important;
                }
              }
            \`;

            const styleSheet = document.createElement('style');
            styleSheet.textContent = printStyles;
            document.head.appendChild(styleSheet);
          </script>
        </body>
        </html>
      `);

      imageWindow.document.close();
      imageWindow.focus();

      // Call completion callback
      if (onPrintComplete) {
        onPrintComplete();
      }

    } catch (error) {
      console.error('Error with image window print:', error);
      if (onPrintError) {
        onPrintError(error.message || 'Failed to open image window');
      }
    }
  };

  // Open in Windows Photo Viewer for direct printing (download method)
  const handlePhotoViewerPrint = async () => {
    try {
      const element = printRef.current;
      if (!element) {
        const error = 'Print element not found';
        console.error(error);
        if (onPrintError) onPrintError(error);
        return;
      }

      // Find the actual ID card element
      const idCardElement = element.querySelector('.security-pattern-container') ||
                           element.querySelector('[class*="card"]') ||
                           element.firstElementChild ||
                           element;

      if (!idCardElement) {
        const error = 'ID card element not found';
        console.error(error);
        if (onPrintError) onPrintError(error);
        return;
      }

      // Wait for any images or content to load
      await new Promise(resolve => setTimeout(resolve, 300));

      // Generate high-resolution canvas for photo viewer
      const canvas = await html2canvas(idCardElement, {
        scale: 4, // Very high resolution for photo viewer printing
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: idCardElement.offsetWidth,
        height: idCardElement.offsetHeight,
        scrollX: 0,
        scrollY: 0,
        logging: false,
        removeContainer: true,
        foreignObjectRendering: true,
        imageTimeout: 10000,
      });

      // Check if canvas is valid
      if (canvas.width === 0 || canvas.height === 0) {
        const error = 'Failed to generate print canvas';
        console.error(error);
        if (onPrintError) onPrintError(error);
        return;
      }

      // Convert canvas to blob
      canvas.toBlob(async (blob) => {
        try {
          // Create a temporary file URL
          const imageUrl = URL.createObjectURL(blob);

          // Create a temporary link to trigger download with specific filename
          const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
          const filename = `ID_Card_${timestamp}.png`;

          // Create download link
          const link = document.createElement('a');
          link.href = imageUrl;
          link.download = filename;
          link.style.display = 'none';

          // Add to DOM and trigger download
          document.body.appendChild(link);
          link.click();

          // Clean up
          document.body.removeChild(link);

          // Show instructions to user
          const instructionWindow = window.open('', '_blank', 'width=500,height=400');
          instructionWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
              <title>Print Instructions</title>
              <style>
                body {
                  font-family: Arial, sans-serif;
                  padding: 20px;
                  background: #f5f5f5;
                  margin: 0;
                }
                .container {
                  background: white;
                  padding: 30px;
                  border-radius: 8px;
                  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                  max-width: 400px;
                  margin: 0 auto;
                }
                h2 {
                  color: #ff8f00;
                  margin-top: 0;
                  text-align: center;
                }
                .step {
                  margin: 15px 0;
                  padding: 10px;
                  background: #f8f9fa;
                  border-left: 4px solid #ff8f00;
                  border-radius: 4px;
                }
                .step-number {
                  font-weight: bold;
                  color: #ff8f00;
                }
                .close-btn {
                  background: #ff8f00;
                  color: white;
                  border: none;
                  padding: 10px 20px;
                  border-radius: 4px;
                  cursor: pointer;
                  width: 100%;
                  margin-top: 20px;
                  font-size: 16px;
                }
                .close-btn:hover {
                  background: #ef6c00;
                }
                .filename {
                  background: #e3f2fd;
                  padding: 5px 10px;
                  border-radius: 4px;
                  font-family: monospace;
                  font-size: 12px;
                  word-break: break-all;
                }
              </style>
            </head>
            <body>
              <div class="container">
                <h2>📸 Print with Photo Viewer</h2>

                <div class="step">
                  <span class="step-number">Step 1:</span>
                  The ID card image has been downloaded to your Downloads folder as:
                  <div class="filename">${filename}</div>
                </div>

                <div class="step">
                  <span class="step-number">Step 2:</span>
                  Navigate to your Downloads folder and double-click the image file
                </div>

                <div class="step">
                  <span class="step-number">Step 3:</span>
                  The image will open in Windows Photo Viewer (or your default photo app)
                </div>

                <div class="step">
                  <span class="step-number">Step 4:</span>
                  Press <strong>Ctrl+P</strong> or click the Print button in Photo Viewer
                </div>

                <div class="step">
                  <span class="step-number">Step 5:</span>
                  Select your printer and print settings, then click Print
                </div>

                <button class="close-btn" onclick="window.close()">
                  ✅ Got it! Close Instructions
                </button>
              </div>

              <script>
                // Auto-close after 30 seconds if user doesn't close manually
                setTimeout(function() {
                  window.close();
                }, 30000);
              </script>
            </body>
            </html>
          `);

          instructionWindow.document.close();
          instructionWindow.focus();

          // Clean up the blob URL after a delay
          setTimeout(() => {
            URL.revokeObjectURL(imageUrl);
          }, 60000);

          // Call completion callback
          if (onPrintComplete) {
            onPrintComplete();
          }

        } catch (error) {
          console.error('Error creating download for photo viewer:', error);
          if (onPrintError) {
            onPrintError('Failed to prepare image for photo viewer');
          }
        }
      }, 'image/png', 1.0);

    } catch (error) {
      console.error('Error with photo viewer print:', error);
      if (onPrintError) {
        onPrintError(error.message || 'Failed to prepare for photo viewer printing');
      }
    }
  };

  const handleDirectPrint = async () => {
    // Route to appropriate print method
    if (printMethod === "photo-viewer") {
      return handlePhotoViewerPrint();
    } else if (printMethod === "image-window") {
      return handleImageWindowPrint();
    }

    // Original browser print method
    try {
      const element = printRef.current;
      if (!element) {
        const error = 'Print element not found';
        console.error(error);
        if (onPrintError) onPrintError(error);
        return;
      }

      // Find the actual ID card element
      const idCardElement = element.querySelector('.security-pattern-container') || 
                           element.querySelector('[class*="card"]') || 
                           element.firstElementChild || 
                           element;
      
      if (!idCardElement) {
        const error = 'ID card element not found';
        console.error(error);
        if (onPrintError) onPrintError(error);
        return;
      }

      // Wait for any images or content to load
      await new Promise(resolve => setTimeout(resolve, 300));

      // Generate high-resolution canvas
      const canvas = await html2canvas(idCardElement, {
        scale: 3, // High resolution for crisp printing
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: idCardElement.offsetWidth,
        height: idCardElement.offsetHeight,
        scrollX: 0,
        scrollY: 0,
        logging: false,
        removeContainer: true,
        foreignObjectRendering: true,
        imageTimeout: 10000,
      });

      // Check if canvas is valid
      if (canvas.width === 0 || canvas.height === 0) {
        const error = 'Failed to generate print canvas';
        console.error(error);
        if (onPrintError) onPrintError(error);
        return;
      }

      // Create print window with auto-print functionality
      const printWindow = window.open('', '_blank', 'width=800,height=600');
      const imageDataUrl = canvas.toDataURL('image/png', 1.0);

      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>ID Card - Direct Print</title>
          <style>
            @page {
              size: 85.6mm 53.98mm;
              margin: 0;
            }
            
            body {
              margin: 0;
              padding: 0;
              display: flex;
              justify-content: center;
              align-items: center;
              min-height: 100vh;
              background: white;
            }
            
            .id-card-image {
              width: 85.6mm;
              height: 53.98mm;
              object-fit: contain;
              display: block;
            }
            
            .loading-message {
              position: fixed;
              top: 20px;
              left: 50%;
              transform: translateX(-50%);
              background: #ff8f00;
              color: white;
              padding: 10px 20px;
              border-radius: 4px;
              font-family: Arial, sans-serif;
              font-size: 14px;
              z-index: 1000;
            }
            
            @media print {
              .loading-message {
                display: none !important;
              }
              body {
                margin: 0 !important;
                padding: 0 !important;
              }
              .id-card-image {
                width: 85.6mm !important;
                height: 53.98mm !important;
                page-break-inside: avoid !important;
              }
            }
          </style>
        </head>
        <body>
          <div class="loading-message">Preparing to print... Please wait</div>
          <img src="${imageDataUrl}" alt="ID Card" class="id-card-image" />
          <script>
            window.onload = function() {
              // Hide loading message
              setTimeout(function() {
                const loadingMsg = document.querySelector('.loading-message');
                if (loadingMsg) loadingMsg.style.display = 'none';
              }, 1000);
              
              // Auto-print after a short delay
              setTimeout(function() {
                window.print();
                
                // Auto-close after printing (with delay for print dialog)
                setTimeout(function() {
                  window.close();
                }, 2000);
              }, 1500);
            };
            
            // Handle print dialog events
            window.addEventListener('beforeprint', function() {
              console.log('Print dialog opened');
            });
            
            window.addEventListener('afterprint', function() {
              console.log('Print dialog closed');
              setTimeout(function() {
                window.close();
              }, 500);
            });
          </script>
        </body>
        </html>
      `);

      printWindow.document.close();
      printWindow.focus();

      // Call completion callback
      if (onPrintComplete) {
        onPrintComplete();
      }

    } catch (error) {
      console.error('Error with direct print:', error);
      if (onPrintError) {
        onPrintError(error.message || 'Failed to print ID card');
      }
    }
  };

  return (
    <>
      {/* Print Button */}
      <Button
        variant={variant}
        color={color}
        size={size}
        startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <DirectPrintIcon />}
        onClick={handleDirectPrint}
        disabled={disabled || loading}
        sx={{
          minWidth: 140,
          '&:hover': {
            transform: 'translateY(-1px)',
            boxShadow: 2
          }
        }}
      >
        {loading ? 'Printing...' : buttonText}
      </Button>

      {/* Hidden container for rendering the ID card */}
      <div
        ref={printRef}
        style={{
          position: 'absolute',
          left: '-9999px',
          top: '-9999px',
          width: '85.6mm',
          height: '53.98mm',
          visibility: 'hidden',
          pointerEvents: 'none'
        }}
      >
        {children}
      </div>
    </>
  );
};

export default OneClickPrint;
