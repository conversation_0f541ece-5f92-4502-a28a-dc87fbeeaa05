import React, { useState, useEffect } from 'react';
import { Box, Typography, Avatar } from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';
import axios from '../../utils/axios';
import {
    gregorianToEthiopian,
    formatEthiopianDate
} from '../../utils/ethiopianCalendar';
import QRCodeReact from 'qrcode.react';
import JsBarcode from 'jsbarcode';

// Dynamic API URL function (same logic as axios.js)
const getApiUrl = () => {
    // If VITE_API_URL is set, use it
    if (import.meta.env.VITE_API_URL) {
        return import.meta.env.VITE_API_URL;
    }

    // Otherwise, determine based on current location
    const currentHost = window.location.hostname;

    if (currentHost === 'localhost' || currentHost === '127.0.0.1') {
        return 'http://localhost:8000';
    } else {
        // For any other host, assume the backend is on the same host with port 8000
        return `http://${currentHost}:8000`;
    }
};

// Simple working barcode component
const SimpleBarcode = ({ value, width = 100, height = 20 }) => {
    const canvasRef = React.useRef(null);

    React.useEffect(() => {
        if (canvasRef.current && value) {
            const canvas = canvasRef.current;
            const ctx = canvas.getContext('2d');

            canvas.width = width;
            canvas.height = height;

            ctx.clearRect(0, 0, width, height);
            ctx.fillStyle = '#FFFFFF';
            ctx.fillRect(0, 0, width, height);

            try {
                // Extract just the digital ID for reliable barcode
                const parts = value.split('|');
                const digitalId = parts[1] || 'GOID123456';
                const cleanId = digitalId.replace(/[^A-Z0-9]/gi, '').toUpperCase();

                if (cleanId.length >= 3) {
                    JsBarcode(canvas, cleanId, {
                        format: 'CODE128',
                        width: 1,
                        height: height - 2,
                        displayValue: false,
                        margin: 1,
                        background: '#FFFFFF',
                        lineColor: '#000000'
                    });
                } else {
                    throw new Error('ID too short');
                }
            } catch (error) {
                // Fallback barcode
                JsBarcode(canvas, 'GOID123456', {
                    format: 'CODE128',
                    width: 1,
                    height: height - 2,
                    displayValue: false,
                    margin: 1,
                    background: '#FFFFFF',
                    lineColor: '#000000'
                });
            }
        }
    }, [value, width, height]);

    return (
        <canvas
            ref={canvasRef}
            width={width}
            height={height}
            style={{
                backgroundColor: 'white',
                border: '0.5px solid #ddd',
                display: 'block'
            }}
        />
    );
};

// Small citizen photo component
const SmallPhoto = ({ citizen, idCard, size = 30 }) => {
    const photoSrc = citizen?.photo || idCard?.citizen_photo || idCard?.citizen?.photo;

    return (
        <div style={{
            width: size + 'px',
            height: size + 'px',
            borderRadius: '3px',
            overflow: 'hidden',
            border: '1px solid #ddd',
            backgroundColor: '#f5f5f5'
        }}>
            {photoSrc ? (
                <img
                    src={photoSrc}
                    alt="Small ID Photo"
                    style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover',
                        objectPosition: 'center'
                    }}
                />
            ) : (
                <div style={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '8px',
                    color: '#888'
                }}>
                    📷
                </div>
            )}
        </div>
    );
};

// Small processed photo component - shows enhanced/processed version
const SmallProcessedPhoto = ({ citizen, idCard, size = 30 }) => {
    const photoSrc = citizen?.photo || idCard?.citizen_photo || idCard?.citizen?.photo;

    return (
        <div style={{
            width: size + 'px',
            height: size + 'px',
            borderRadius: '3px',
            overflow: 'hidden',
            border: '1px solid #0066cc',
            backgroundColor: '#f0f8ff'
        }}>
            {photoSrc ? (
                <img
                    src={photoSrc}
                    alt="Small Processed Photo"
                    style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover',
                        objectPosition: 'center',
                        // Enhanced processing filters
                        filter: 'contrast(1.2) brightness(1.1) saturate(1.1)',
                        // Additional processing effects
                        imageRendering: 'crisp-edges'
                    }}
                />
            ) : (
                <div style={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '7px',
                    color: '#0066cc',
                    fontWeight: 'bold'
                }}>
                    Enhanced
                </div>
            )}
        </div>
    );
};

const IDCardTemplate = ({ idCard, side = 'front', preview = false }) => {
    const { user } = useAuth();
    // Helper function to convert English names to Amharic
    const convertToAmharic = (englishName, tenantType = 'unknown') => {
        if (!englishName) {
            const defaults = {
                'city': 'ጎንደር',
                'subcity': 'ዞብል',
                'kebele': 'ቀበሌ 14',
                'unknown': 'አስተዳደር'
            };
            return defaults[tenantType] || defaults.unknown;
        }

        // Smart conversion for common English names
        const nameConversions = {
            'gondar': 'ጎንደር',
            'zoble': 'ዞብል',
            'gabriel': 'ገብርኤል',
            'kebele14': 'ቀበሌ 14',
            'kebele 14': 'ቀበሌ 14',
            'city': 'ከተማ',
            'subcity': 'ክ/ከተማ',
            'kebele': 'ቀበሌ'
        };

        const lowerEnglishName = englishName.toLowerCase();

        // Check for exact matches first
        if (nameConversions[lowerEnglishName]) {
            return nameConversions[lowerEnglishName];
        }

        // Check for numbered kebeles
        if (lowerEnglishName.includes('kebele')) {
            const number = englishName.replace(/[^0-9]/g, '');
            return number ? `ቀበሌ ${number}` : 'ቀበሌ';
        }

        // Return original name if no conversion found
        return englishName;
    };

    const [tenantNames, setTenantNames] = useState({
        city_name_am: convertToAmharic(user?.city_tenant_name_am || user?.city_tenant_name, 'city'),
        subcity_name_am: convertToAmharic(user?.parent_tenant_name_am || user?.parent_tenant_name, 'subcity'),
        kebele_name_am: convertToAmharic(user?.tenant_name_am || user?.tenant_name, 'kebele'),
        // Add English names for header
        city_name: user?.city_tenant_name || 'Gondar',
        subcity_name: user?.parent_tenant_name || user?.tenant_name || 'Subcity',
        kebele_name: user?.tenant_name || 'Kebele'
    });
    const [mayorSignature, setMayorSignature] = useState(null);
    const [tenantLogo, setTenantLogo] = useState(null);
    const [logoError, setLogoError] = useState(false);
    const [subcityPatternImage, setSubcityPatternImage] = useState(null);

    // Add state for individual Amharic names to be used in header (from database only)
    const [cityNameAm, setCityNameAm] = useState('');
    const [subcityNameAm, setSubcityNameAm] = useState('');
    const [kebeleNameAm, setKebeleNameAm] = useState('');

    // Get city name from token for microprint
    const getCityName = () => {
        // Get city name from user token (parent of subcity)
        return user?.city_tenant_name || user?.parent_tenant_name || 'GONDAR';
    };

    // Generate random positions for microprint text
    const generateMicroprintPositions = () => {
        const positions = [];
        const cityName = getCityName();

        // Create a grid-based approach for better distribution
        const gridSize = 3; // 3x3 grid for 9 positions
        const cellWidth = 80 / gridSize;
        const cellHeight = 80 / gridSize;

        for (let i = 0; i < 9; i++) {
            const row = Math.floor(i / gridSize);
            const col = i % gridSize;

            // Add some randomness within each grid cell
            const baseTop = row * cellHeight + 10;
            const baseLeft = col * cellWidth + 10;

            positions.push({
                id: i,
                text: cityName,
                top: baseTop + Math.random() * (cellHeight * 0.8), // Random within cell
                left: baseLeft + Math.random() * (cellWidth * 0.8), // Random within cell
                rotation: Math.random() * 360, // Random rotation
                opacity: 0.3 + Math.random() * 0.2 // 0.3 to 0.5 opacity for better visibility
            });
        }
        return positions;
    };

    const microprintPositions = generateMicroprintPositions();

    // Determine current user's tenant type for display logic
    const getCurrentTenantType = () => {
        let currentTenantType = user?.tenant_type || user?.tenant?.type;

        if (!currentTenantType) {
            if (user?.role === 'subcity_admin' || user?.role === 'subcity_clerk') {
                currentTenantType = 'subcity';
            } else if (user?.role === 'city_admin' || user?.role === 'city_clerk') {
                currentTenantType = 'city';
            } else {
                currentTenantType = 'kebele';
            }
        }

        return currentTenantType;
    };

    const currentTenantType = getCurrentTenantType();

    if (!idCard) {
        return (
            <Box
                sx={{
                    width: '100%',
                    height: '300px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    border: '1px dashed #ccc',
                    borderRadius: 2
                }}
            >
                <Typography color="text.secondary">No ID card data</Typography>
            </Box>
        );
    }

    const citizen = idCard.citizen || {};
    const tenantHierarchy = idCard.tenant_hierarchy || {};

    // Helper function to get Amharic name with intelligent fallbacks
    const getAmharicName = (amharicName, englishName, tenantType = 'unknown') => {
        // First priority: Use Amharic name from database (via JWT token)
        if (amharicName && amharicName !== null && amharicName.trim() !== '') {
            return amharicName;
        }

        // Second priority: Intelligent conversion for numbered kebeles
        if (englishName?.toLowerCase().startsWith('kebele')) {
            const number = englishName.replace(/[^0-9]/g, '');
            return number ? `ቀበሌ ${number}` : englishName;
        }

        // Third priority: Smart conversion for common English names
        const nameConversions = {
            'gondar': 'ጎንደር',
            'zoble': 'ዞብል',
            'gabriel': 'ገብርኤል',
            'kebele14': 'ቀበሌ 14',
            'kebele 14': 'ቀበሌ 14',
            'city': 'ከተማ',
            'subcity': 'ክ/ከተማ',
            'kebele': 'ቀበሌ'
        };

        const lowerEnglishName = englishName?.toLowerCase();
        if (lowerEnglishName && nameConversions[lowerEnglishName]) {
            return nameConversions[lowerEnglishName];
        }

        // Fourth priority: Type-based defaults with English name
        const defaults = {
            'city': englishName ? `${englishName} ከተማ` : 'ጎንደር ከተማ',
            'subcity': englishName ? `${englishName} ክ/ከተማ` : 'ዞብል ክ/ከተማ',
            'kebele': englishName ? `${englishName} ቀበሌ` : 'ቀበሌ 14',
            'unknown': englishName || 'አስተዳደር'
        };

        // Fifth priority: English name as fallback
        return defaults[tenantType] || englishName || defaults.unknown;
    };

    // Set tenant names from token and fetch Amharic names
    useEffect(() => {
        const setTenantData = async () => {
            try {
                console.log('🔍 Setting tenant data from token:', {
                    cityTenantId: user?.city_tenant_id,
                    cityTenantName: user?.city_tenant_name,
                    parentTenantId: user?.parent_tenant_id,
                    parentTenantName: user?.parent_tenant_name,
                    tenantId: user?.tenant_id,
                    tenantName: user?.tenant_name,
                    tenantType: user?.tenant_type
                });

                // Set initial names from token as fallback
                setTenantNames(prev => ({
                    ...prev,
                    city_name_am: user?.city_tenant_name || 'ጎንደር',
                    subcity_name_am: user?.parent_tenant_name || 'ዞብል',
                    kebele_name_am: user?.tenant_name || 'ገብርኤል'
                }));

                // Use Amharic names directly from JWT token database values
                console.log('🔍 Setting tenant names using database values from JWT token...');
                console.log('🔍 User tenant type:', user?.tenant_type);
                console.log('🔍 User token data:', {
                    tenant_name_am: user?.tenant_name_am,
                    parent_tenant_name_am: user?.parent_tenant_name_am,
                    city_tenant_name_am: user?.city_tenant_name_am
                });
                console.log('🔍 Full user object:', user);
                console.log('🔍 Citizen data:', citizen);
                console.log('🔍 ID card data:', idCard);

                let cityNameAm, subcityNameAm, kebeleNameAm;

                // Always use citizen's kebele information for dynamic header
                console.log('🔍 Fetching citizen kebele information for dynamic header');

                // Get kebele tenant info from citizen data
                // citizen.kebele contains the tenant ID as an integer
                const kebeleId = citizen?.kebele;
                const kebeleInfo = { id: kebeleId };

                console.log('🔍 Kebele info from ID card:', kebeleInfo);
                console.log('🔍 Kebele ID for fetching:', kebeleId);

                if (user?.tenant_type === 'kebele') {
                    // In kebele context: Use logged-in user's tenant hierarchy (existing working logic)
                    console.log('🔍 Kebele context: Using user tenant info');
                    cityNameAm = getAmharicName(user?.city_tenant_name_am, user?.city_tenant_name, 'city');
                    subcityNameAm = getAmharicName(user?.parent_tenant_name_am, user?.parent_tenant_name, 'subcity');
                    kebeleNameAm = getAmharicName(user?.tenant_name_am, user?.tenant_name, 'kebele');

                } else {
                    // For subcity, city, and other contexts: Always use citizen's kebele information
                    console.log('🔍 Non-kebele context: Using citizen kebele info for dynamic header');

                    // Fetch kebele tenant data to get the full hierarchy
                    if (kebeleId) {
                        try {
                            console.log(`🔍 Fetching kebele tenant data for hierarchy, kebele ID: ${kebeleId}`);
                            const kebeleResponse = await axios.get(`/api/tenants/${kebeleId}/`);
                            const kebeleData = kebeleResponse.data;

                            console.log('🔍 Kebele tenant data for hierarchy:', kebeleData);

                            // Get kebele Amharic and English names
                            if (kebeleData.profile_data?.name_am) {
                                kebeleNameAm = kebeleData.profile_data.name_am;
                                console.log('🔍 Kebele Amharic name from profile_data:', kebeleNameAm);
                            } else if (kebeleData.name_am) {
                                kebeleNameAm = kebeleData.name_am;
                                console.log('🔍 Kebele Amharic name from tenant data:', kebeleNameAm);
                            } else {
                                kebeleNameAm = getAmharicName(null, kebeleData.name || kebeleInfo?.name, 'kebele');
                                console.log('🔍 Kebele Amharic name from intelligent conversion:', kebeleNameAm);
                            }



                            // Get subcity information from kebele's parent
                            const subcityId = kebeleData.parent;
                            if (subcityId) {
                                try {
                                    console.log(`🔍 Fetching subcity tenant data, subcity ID: ${subcityId}`);
                                    const subcityResponse = await axios.get(`/api/tenants/${subcityId}/`);
                                    const subcityData = subcityResponse.data;

                                    console.log('🔍 Subcity tenant data:', subcityData);

                                    // Get subcity Amharic and English names
                                    if (subcityData.profile_data?.name_am) {
                                        subcityNameAm = subcityData.profile_data.name_am;
                                        console.log('🔍 Subcity Amharic name from profile_data:', subcityNameAm);
                                    } else if (subcityData.name_am) {
                                        subcityNameAm = subcityData.name_am;
                                        console.log('🔍 Subcity Amharic name from tenant data:', subcityNameAm);
                                    } else {
                                        subcityNameAm = getAmharicName(null, subcityData.name, 'subcity');
                                        console.log('🔍 Subcity Amharic name from intelligent conversion:', subcityNameAm);
                                    }



                                    // Get city information from subcity's parent
                                    const cityId = subcityData.parent;
                                    if (cityId) {
                                        try {
                                            console.log(`🔍 Fetching city tenant data, city ID: ${cityId}`);
                                            const cityResponse = await axios.get(`/api/tenants/${cityId}/`);
                                            const cityData = cityResponse.data;

                                            console.log('🔍 City tenant data:', cityData);

                                            // Get city Amharic and English names
                                            if (cityData.profile_data?.city_name_am) {
                                                cityNameAm = cityData.profile_data.city_name_am;
                                                console.log('🔍 City Amharic name from profile_data:', cityNameAm);
                                            } else if (cityData.name_am) {
                                                cityNameAm = cityData.name_am;
                                                console.log('🔍 City Amharic name from tenant data:', cityNameAm);
                                            } else {
                                                cityNameAm = getAmharicName(null, cityData.name, 'city');
                                                console.log('🔍 City Amharic name from intelligent conversion:', cityNameAm);
                                            }


                                        } catch (error) {
                                            console.error('❌ Error fetching city data:', error);
                                            // Fallback to user's city info
                                            cityNameAm = getAmharicName(user?.city_tenant_name_am, user?.city_tenant_name, 'city');
                                        }
                                    } else {
                                        // Fallback to user's city info
                                        cityNameAm = getAmharicName(user?.city_tenant_name_am, user?.city_tenant_name, 'city');
                                    }
                                } catch (error) {
                                    console.error('❌ Error fetching subcity data:', error);
                                    // Fallback to user's subcity info
                                    subcityNameAm = getAmharicName(user?.parent_tenant_name_am || user?.tenant_name_am, user?.parent_tenant_name || user?.tenant_name, 'subcity');
                                    cityNameAm = getAmharicName(user?.city_tenant_name_am, user?.city_tenant_name, 'city');
                                }
                            } else {
                                // Fallback to user's tenant info
                                subcityNameAm = getAmharicName(user?.parent_tenant_name_am || user?.tenant_name_am, user?.parent_tenant_name || user?.tenant_name, 'subcity');
                                cityNameAm = getAmharicName(user?.city_tenant_name_am, user?.city_tenant_name, 'city');
                            }
                        } catch (error) {
                            console.error('❌ Error fetching kebele data:', error);
                            // Fallback to user's tenant info
                            kebeleNameAm = getAmharicName(null, kebeleInfo?.name, 'kebele');
                            subcityNameAm = getAmharicName(user?.parent_tenant_name_am || user?.tenant_name_am, user?.parent_tenant_name || user?.tenant_name, 'subcity');
                            cityNameAm = getAmharicName(user?.city_tenant_name_am, user?.city_tenant_name, 'city');
                        }
                    } else {
                        // Fallback: use user's tenant info when no kebele ID available
                        console.log('⚠️ No kebele ID available, using user tenant info as fallback');
                        kebeleNameAm = getAmharicName(user?.tenant_name_am, user?.tenant_name, 'kebele');
                        subcityNameAm = getAmharicName(user?.parent_tenant_name_am || user?.tenant_name_am, user?.parent_tenant_name || user?.tenant_name, 'subcity');
                        cityNameAm = getAmharicName(user?.city_tenant_name_am, user?.city_tenant_name, 'city');
                    }
                }

                console.log('🔍 Final Amharic name mapping (from database):', {
                    city: `${user?.city_tenant_name || 'N/A'} → ${cityNameAm} (DB: ${user?.city_tenant_name_am || 'None'})`,
                    subcity: `${user?.parent_tenant_name || user?.tenant_name || 'N/A'} → ${subcityNameAm} (DB: ${user?.parent_tenant_name_am || user?.tenant_name_am || 'None'})`,
                    kebele: `${user?.tenant_name || idCard?.kebele_tenant?.name || 'N/A'} → ${kebeleNameAm} (DB: ${user?.tenant_name_am || 'None'})`
                });

                // Determine English names for header using dynamically fetched data or fallbacks
                let cityNameEn, subcityNameEn, kebeleNameEn;

                if (user?.tenant_type === 'kebele') {
                    // For kebele context: use user's tenant hierarchy (existing working logic)
                    cityNameEn = user?.city_tenant_name || 'Gondar';
                    subcityNameEn = user?.parent_tenant_name || 'Subcity';
                    kebeleNameEn = user?.tenant_name || 'Kebele';
                } else {
                    // For non-kebele contexts: use citizen's kebele information with fallbacks
                    kebeleNameEn = idCard?.kebele_tenant?.name ||
                                   (typeof citizen.kebele === 'object' && citizen.kebele?.name) ||
                                   (typeof citizen.kebele === 'string' ? citizen.kebele : null) ||
                                   'Kebele';

                    subcityNameEn = (typeof citizen.subcity === 'object' && citizen.subcity?.name) ||
                                    (typeof citizen.subcity === 'string' ? citizen.subcity : null) ||
                                    user?.parent_tenant_name ||
                                    user?.tenant_name ||
                                    'Subcity';

                    cityNameEn = tenantHierarchy?.city_tenant ||
                                 user?.city_tenant_name ||
                                 'Gondar';
                }

                console.log('🔍 English names determined:', {
                    city: `${cityNameEn} (context: ${user?.tenant_type})`,
                    subcity: `${subcityNameEn} (context: ${user?.tenant_type})`,
                    kebele: `${kebeleNameEn} (context: ${user?.tenant_type})`
                });

                setTenantNames(prev => ({
                    ...prev,
                    city_name_am: cityNameAm,
                    subcity_name_am: subcityNameAm,
                    kebele_name_am: kebeleNameAm,
                    // Set English names for header
                    city_name: cityNameEn,
                    subcity_name: subcityNameEn,
                    kebele_name: kebeleNameEn
                }));

                // Set individual state variables for header use (from database only)
                setCityNameAm(cityNameAm || '');
                setSubcityNameAm(subcityNameAm || '');
                setKebeleNameAm(kebeleNameAm || '');

                // Fetch tenant data including logo, signature, and pattern
                await fetchTenantData();

            } catch (error) {
                console.error('❌ Error setting tenant data:', error);
            }
        };

        const fetchTenantData = async () => {
            try {
                console.log('🔍 Fetching tenant data for ID card...');
                console.log('🔍 Citizen data:', citizen);
                console.log('🔍 User data:', user);

                // Fetch Amharic names for ID card header
                await fetchAmharicNames();

                // Fetch tenant profile data for logo, signature, and pattern
                await fetchTenantProfileData();

            } catch (error) {
                console.error('❌ Error fetching tenant data:', error);
            }
        };

        const fetchAmharicNames = async () => {
            try {
                let cityNameAm, subcityNameAm, kebeleNameAm;
                let cityNameEn, subcityNameEn, kebeleNameEn;

                // Always use citizen's kebele information for dynamic header
                console.log('🔍 Fetching citizen kebele information for dynamic header');

                // Get kebele tenant info from citizen data
                // citizen.kebele contains the tenant ID as an integer
                const kebeleId = citizen?.kebele;
                const kebeleInfo = { id: kebeleId };

                console.log('🔍 Kebele info from ID card:', kebeleInfo);
                console.log('🔍 Kebele ID for fetching:', kebeleId);

                if (user?.tenant_type === 'kebele') {
                    // In kebele context: Use logged-in user's tenant hierarchy (existing working logic)
                    console.log('🔍 Kebele context: Using user tenant info');
                    cityNameAm = getAmharicName(user?.city_tenant_name_am, user?.city_tenant_name, 'city');
                    subcityNameAm = getAmharicName(user?.parent_tenant_name_am, user?.parent_tenant_name, 'subcity');
                    kebeleNameAm = getAmharicName(user?.tenant_name_am, user?.tenant_name, 'kebele');

                    // Set English names for kebele context
                    cityNameEn = user?.city_tenant_name || 'City';
                    subcityNameEn = user?.parent_tenant_name || 'Subcity';
                    kebeleNameEn = user?.tenant_name || 'Kebele';

                } else {
                    // For subcity, city, and other contexts: Always use citizen's kebele information
                    console.log('🔍 Non-kebele context: Using citizen kebele info for dynamic header');

                    // Fetch kebele tenant data to get the full hierarchy
                    if (kebeleId) {
                        try {
                            console.log(`🔍 Fetching kebele tenant data for hierarchy, kebele ID: ${kebeleId}`);
                            const kebeleResponse = await axios.get(`/api/tenants/${kebeleId}/`);
                            const kebeleData = kebeleResponse.data;

                            console.log('🔍 Kebele tenant data for hierarchy:', kebeleData);

                            // Get kebele Amharic and English names
                            if (kebeleData.profile_data?.name_am) {
                                kebeleNameAm = kebeleData.profile_data.name_am;
                                console.log('🔍 Kebele Amharic name from profile_data:', kebeleNameAm);
                            } else if (kebeleData.name_am) {
                                kebeleNameAm = kebeleData.name_am;
                                console.log('🔍 Kebele Amharic name from tenant data:', kebeleNameAm);
                            } else {
                                kebeleNameAm = getAmharicName(null, kebeleData.name || kebeleInfo?.name, 'kebele');
                                console.log('🔍 Kebele Amharic name from intelligent conversion:', kebeleNameAm);
                            }

                            // Get kebele English name from fetched data
                            kebeleNameEn = kebeleData.name || kebeleInfo?.name || 'Kebele';
                            console.log('🔍 Kebele English name from fetched data:', kebeleNameEn);

                            // Get subcity information from kebele's parent
                            const subcityId = kebeleData.parent;
                            if (subcityId) {
                                try {
                                    console.log(`🔍 Fetching subcity tenant data, subcity ID: ${subcityId}`);
                                    const subcityResponse = await axios.get(`/api/tenants/${subcityId}/`);
                                    const subcityData = subcityResponse.data;

                                    console.log('🔍 Subcity tenant data:', subcityData);

                                    // Get subcity Amharic and English names
                                    if (subcityData.profile_data?.name_am) {
                                        subcityNameAm = subcityData.profile_data.name_am;
                                        console.log('🔍 Subcity Amharic name from profile_data:', subcityNameAm);
                                    } else if (subcityData.name_am) {
                                        subcityNameAm = subcityData.name_am;
                                        console.log('🔍 Subcity Amharic name from tenant data:', subcityNameAm);
                                    } else {
                                        subcityNameAm = getAmharicName(null, subcityData.name, 'subcity');
                                        console.log('🔍 Subcity Amharic name from intelligent conversion:', subcityNameAm);
                                    }

                                    // Get subcity English name from fetched data
                                    subcityNameEn = subcityData.name || 'Subcity';
                                    console.log('🔍 Subcity English name from fetched data:', subcityNameEn);

                                    // Get city information from subcity's parent
                                    const cityId = subcityData.parent;
                                    if (cityId) {
                                        try {
                                            console.log(`🔍 Fetching city tenant data, city ID: ${cityId}`);
                                            const cityResponse = await axios.get(`/api/tenants/${cityId}/`);
                                            const cityData = cityResponse.data;

                                            console.log('🔍 City tenant data:', cityData);

                                            // Get city Amharic and English names
                                            if (cityData.profile_data?.city_name_am) {
                                                cityNameAm = cityData.profile_data.city_name_am;
                                                console.log('🔍 City Amharic name from profile_data:', cityNameAm);
                                            } else if (cityData.name_am) {
                                                cityNameAm = cityData.name_am;
                                                console.log('🔍 City Amharic name from tenant data:', cityNameAm);
                                            } else {
                                                cityNameAm = getAmharicName(null, cityData.name, 'city');
                                                console.log('🔍 City Amharic name from intelligent conversion:', cityNameAm);
                                            }

                                            // Get city English name from fetched data
                                            cityNameEn = cityData.name || 'City';
                                            console.log('🔍 City English name from fetched data:', cityNameEn);
                                        } catch (error) {
                                            console.error('❌ Error fetching city data:', error);
                                            // Fallback to user's city info
                                            cityNameAm = getAmharicName(user?.city_tenant_name_am, user?.city_tenant_name, 'city');
                                            cityNameEn = user?.city_tenant_name || 'City';
                                        }
                                    } else {
                                        // Fallback to user's city info
                                        cityNameAm = getAmharicName(user?.city_tenant_name_am, user?.city_tenant_name, 'city');
                                        cityNameEn = user?.city_tenant_name || 'City';
                                    }
                                } catch (error) {
                                    console.error('❌ Error fetching subcity data:', error);
                                    // Fallback to user's subcity info
                                    subcityNameAm = getAmharicName(user?.parent_tenant_name_am || user?.tenant_name_am, user?.parent_tenant_name || user?.tenant_name, 'subcity');
                                    cityNameAm = getAmharicName(user?.city_tenant_name_am, user?.city_tenant_name, 'city');
                                    subcityNameEn = user?.parent_tenant_name || 'Subcity';
                                    cityNameEn = user?.city_tenant_name || 'City';
                                }
                            } else {
                                // Fallback to user's tenant info
                                subcityNameAm = getAmharicName(user?.parent_tenant_name_am || user?.tenant_name_am, user?.parent_tenant_name || user?.tenant_name, 'subcity');
                                cityNameAm = getAmharicName(user?.city_tenant_name_am, user?.city_tenant_name, 'city');
                                subcityNameEn = user?.parent_tenant_name || 'Subcity';
                                cityNameEn = user?.city_tenant_name || 'City';
                            }
                        } catch (error) {
                            console.error('❌ Error fetching kebele data:', error);
                            // Fallback to user's tenant info
                            kebeleNameAm = getAmharicName(null, kebeleInfo?.name, 'kebele');
                            subcityNameAm = getAmharicName(user?.parent_tenant_name_am || user?.tenant_name_am, user?.parent_tenant_name || user?.tenant_name, 'subcity');
                            cityNameAm = getAmharicName(user?.city_tenant_name_am, user?.city_tenant_name, 'city');
                            kebeleNameEn = kebeleInfo?.name || 'Kebele';
                            subcityNameEn = user?.parent_tenant_name || 'Subcity';
                            cityNameEn = user?.city_tenant_name || 'City';
                        }
                    } else {
                        // Fallback: use user's tenant info when no kebele ID available
                        console.log('⚠️ No kebele ID available, using user tenant info as fallback');
                        kebeleNameAm = getAmharicName(user?.tenant_name_am, user?.tenant_name, 'kebele');
                        subcityNameAm = getAmharicName(user?.parent_tenant_name_am || user?.tenant_name_am, user?.parent_tenant_name || user?.tenant_name, 'subcity');
                        cityNameAm = getAmharicName(user?.city_tenant_name_am, user?.city_tenant_name, 'city');
                        kebeleNameEn = user?.tenant_name || 'Kebele';
                        subcityNameEn = user?.parent_tenant_name || 'Subcity';
                        cityNameEn = user?.city_tenant_name || 'City';
                    }
                }

                console.log('🔍 Final Amharic name mapping (from database):', {
                    city: `${user?.city_tenant_name || 'N/A'} → ${cityNameAm} (DB: ${user?.city_tenant_name_am || 'None'})`,
                    subcity: `${user?.parent_tenant_name || user?.tenant_name || 'N/A'} → ${subcityNameAm} (DB: ${user?.parent_tenant_name_am || user?.tenant_name_am || 'None'})`,
                    kebele: `${user?.tenant_name || idCard?.kebele_tenant?.name || 'N/A'} → ${kebeleNameAm} (DB: ${user?.tenant_name_am || 'None'})`
                });

                // Note: English names are now set within the dynamic fetching logic above
                // This ensures consistency between Amharic and English names from the same source

                console.log('🔍 English names determined:', {
                    city: `${cityNameEn} (context: ${user?.tenant_type})`,
                    subcity: `${subcityNameEn} (context: ${user?.tenant_type})`,
                    kebele: `${kebeleNameEn} (context: ${user?.tenant_type})`
                });

                setTenantNames(prev => ({
                    ...prev,
                    city_name_am: cityNameAm,
                    subcity_name_am: subcityNameAm,
                    kebele_name_am: kebeleNameAm,
                    // Set English names for header
                    city_name: cityNameEn,
                    subcity_name: subcityNameEn,
                    kebele_name: kebeleNameEn
                }));

                // Set individual state variables for header use (from database only)
                setCityNameAm(cityNameAm || '');
                setSubcityNameAm(subcityNameAm || '');
                setKebeleNameAm(kebeleNameAm || '');

            } catch (error) {
                console.error('❌ Error fetching Amharic names:', error);
            }
        };

        const fetchTenantProfileData = async () => {
            try {
                console.log('🔍 fetchTenantProfileData() called - Using citizen kebele hierarchy for dynamic images...');

                // Get kebele information from citizen data
                // citizen.kebele contains the tenant ID as an integer
                const kebeleId = citizen?.kebele;
                const kebeleInfo = { id: kebeleId };

                console.log('🔍 Kebele info for images:', { kebeleInfo, kebeleId });
                console.log('🔍 Current tenantLogo state before fetch:', tenantLogo);
                console.log('🔍 Current user tenant info:', {
                    user_tenant_id: user?.tenant_id,
                    user_tenant_type: user?.tenant_type,
                    user_tenant_name: user?.tenant_name
                });
                console.log('🔍 Citizen kebele info:', {
                    citizen_kebele: citizen?.kebele,
                    citizen_first_name: citizen?.first_name,
                    citizen_last_name: citizen?.last_name
                });

                let kebeleData = null;
                let subcityData = null;
                let cityData = null;

                if (user?.tenant_type === 'kebele') {
                    // In kebele context: Use logged-in user's tenant hierarchy (existing working logic)
                    console.log('🔍 Kebele context: Using user tenant info for images');

                    try {
                        const tenantDataResponse = await axios.post('/api/tenants/get-tenant-data/', {
                            kebele_tenant_id: user?.tenant_id,
                            subcity_tenant_id: user?.parent_tenant_id,
                            city_tenant_id: user?.city_tenant_id
                        }, {
                            headers: {
                                'Authorization': `Bearer ${token}`,
                                'Content-Type': 'application/json'
                            }
                        });

                        console.log('🔍 Kebele context database query response:', tenantDataResponse.data);

                        if (tenantDataResponse.data && tenantDataResponse.status === 200) {
                            kebeleData = tenantDataResponse.data.kebele_data;
                            subcityData = tenantDataResponse.data.subcity_data;
                            cityData = tenantDataResponse.data.city_data;

                        console.log('🔍 Extracted tenant data from database:');
                        console.log('🔍 Kebele data:', kebeleData);
                        console.log('🔍 Subcity data:', subcityData);
                        console.log('🔍 City data:', cityData);

                        // DETAILED DATABASE LOGO ANALYSIS
                        console.log('🔍 ANALYZING LOGO URLs FROM DATABASE:');
                        if (kebeleData) {
                            console.log('🔍 Kebele logo from DB:', kebeleData.logo);
                            console.log('🔍 Kebele logo type:', typeof kebeleData.logo);
                            console.log('🔍 Kebele logo is null/undefined:', kebeleData.logo == null);
                            console.log('🔍 Kebele pattern:', kebeleData.pattern_image);
                            console.log('🔍 Kebele signature:', kebeleData.mayor_signature);
                        }
                        if (subcityData) {
                            console.log('🔍 Subcity logo from DB:', subcityData.logo);
                            console.log('🔍 Subcity logo type:', typeof subcityData.logo);
                            console.log('🔍 Subcity logo is null/undefined:', subcityData.logo == null);
                            console.log('🔍 Subcity pattern:', subcityData.pattern_image);
                            console.log('🔍 Subcity signature:', subcityData.mayor_signature);
                        }
                        if (cityData) {
                            console.log('🔍 City logo:', cityData.logo);
                            console.log('🔍 City signature:', cityData.mayor_signature);
                        }
                    } else {
                        console.log('⚠️ API response was not successful or empty');
                    }
                } catch (error) {
                    console.error('❌ Error fetching tenant data from database:', error);
                    console.log('🔍 Database fetch failed - will use default fallback behavior without hardcoded URLs');
                    // Let the logo resolution continue without database data

                    // Create fallback data using JWT token information
                    if (user?.tenant_type === 'kebele' && user?.tenant_id) {
                        kebeleData = {
                            id: user.tenant_id,
                            name: user.tenant_name,
                            name_am: user.tenant_name_am,
                            logo: '/media/logos/kebele/logo.jpg', // Static logo filename
                            mayor_signature: null,
                            pattern_image: null,
                            tenant_id: user.tenant_id
                        };
                    }

                    if (user?.parent_tenant_id) {
                        subcityData = {
                            id: user.parent_tenant_id,
                            name: user.parent_tenant_name,
                            name_am: user.parent_tenant_name_am,
                            logo: '/media/logos/subcity/logo.jpg', // Static logo filename
                            mayor_signature: '/media/signatures/subcity/signiture_Chale_Mt9tbTk.png', // Frontend public media URL
                            pattern_image: '/media/patterns/subcity/Fasil_castel_black_ofwvi9n.png', // Frontend public media URL
                            tenant_id: user.parent_tenant_id
                        };
                    }

                    if (user?.city_tenant_id) {
                        cityData = {
                            id: user.city_tenant_id,
                            city_name: user.city_tenant_name,
                            city_name_am: user.city_tenant_name_am,
                            logo: '/media/logos/logo_final_gondar_with_camelot1_YXOdyge.png', // Direct media URL
                            mayor_signature: '/media/signatures/subcity/signiture_Chale_Mt9tbTk.png', // Correct signature file path
                            tenant_id: user.city_tenant_id
                        };
                    }

                    console.log('🔍 Created fallback data from JWT token:');
                    console.log('🔍 Kebele fallback:', kebeleData);
                    console.log('🔍 Subcity fallback:', subcityData);
                    console.log('🔍 City fallback:', cityData);

                    // Fallback: Try to access media files directly
                    if (user?.tenant_type === 'kebele' && user?.tenant_id) {
                        console.log('🔍 Attempting to find kebele images in media folder...');

                        // Try common logo paths for kebeles based on database structure
                        const possibleLogoPaths = [
                            `/media/logos/kebele/kebele_${user.tenant_id}_logo.jpg`,
                            `/media/logos/kebele/kebele_${user.tenant_id}_logo.png`,
                            `/media/logos/kebele/${user.tenant_name.toLowerCase()}_logo.jpg`,
                            `/media/logos/kebele/${user.tenant_name.toLowerCase()}_logo.png`,
                            `/media/logos/kebele/logo.jpg`,
                            `/media/logos/kebele/logo.png`
                        ];

                        // Try to find a working logo
                        for (const logoPath of possibleLogoPaths) {
                            try {
                                const testResponse = await fetch(`${getApiUrl()}${logoPath}`, { method: 'HEAD' });
                                if (testResponse.ok) {
                                    console.log('🔍 Found working kebele logo:', logoPath);
                                    kebeleData = { logo: logoPath };
                                    break;
                                }
                            } catch (error) {
                                // Continue to next path
                            }
                        }
                    }

                    // Try to find subcity logos with static names
                    if (user?.parent_tenant_id) {
                        console.log('🔍 Attempting to find subcity images in media folder...');

                        const possibleSubcityLogoPaths = [
                            `/media/logos/subcity/logo.jpg`,
                            `/media/logos/subcity/logo.png`,
                            `/media/logos/subcity/subcity_${user.parent_tenant_id}_logo.jpg`,
                            `/media/logos/subcity/subcity_${user.parent_tenant_id}_logo.png`,
                            `/media/logos/subcity/${user.parent_tenant_name?.toLowerCase()}_logo.jpg`,
                            `/media/logos/subcity/${user.parent_tenant_name?.toLowerCase()}_logo.png`
                        ];

                        // Try to find a working subcity logo
                        for (const logoPath of possibleSubcityLogoPaths) {
                            try {
                                const testResponse = await fetch(`${getApiUrl()}${logoPath}`, { method: 'HEAD' });
                                if (testResponse.ok) {
                                    console.log('🔍 Found working subcity logo:', logoPath);
                                    subcityData = { ...subcityData, logo: logoPath };
                                    break;
                                }
                            } catch (error) {
                                // Continue to next path
                            }
                        }
                    }
                }

                } else {
                    // For subcity, city, and other contexts: Use citizen's kebele information for dynamic images
                    console.log('🔍 Non-kebele context: Using citizen kebele info for dynamic images');

                    // Fetch kebele tenant data to get the full hierarchy for images
                    if (kebeleId) {
                        try {
                            console.log(`🔍 Fetching kebele tenant data for images, kebele ID: ${kebeleId}`);
                            const kebeleResponse = await axios.get(`/api/tenants/${kebeleId}/`);
                            const rawKebeleData = kebeleResponse.data;

                            console.log('🔍 Raw kebele tenant data for images:', rawKebeleData);

                            // Extract profile data which contains the image fields
                            kebeleData = rawKebeleData.profile_data || {};
                            console.log('🔍 Extracted kebele profile data:', kebeleData);
                            console.log('🔍 Kebele profile data logo field:', kebeleData.logo);
                            console.log('🔍 Kebele profile data logo type:', typeof kebeleData.logo);
                            console.log('🔍 Kebele profile data is empty object?', Object.keys(kebeleData).length === 0);

                            // Get subcity information from kebele's parent (use raw tenant data for parent ID)
                            const subcityId = rawKebeleData.parent;
                            if (subcityId) {
                                try {
                                    console.log(`🔍 Fetching subcity tenant data for images, subcity ID: ${subcityId}`);
                                    const subcityResponse = await axios.get(`/api/tenants/${subcityId}/`);
                                    const rawSubcityData = subcityResponse.data;

                                    console.log('🔍 Raw subcity tenant data for images:', rawSubcityData);

                                    // Extract profile data which contains the image fields
                                    subcityData = rawSubcityData.profile_data || {};
                                    console.log('🔍 Extracted subcity profile data:', subcityData);

                                    // Get city information from subcity's parent (use raw tenant data for parent ID)
                                    const cityId = rawSubcityData.parent;
                                    if (cityId) {
                                        try {
                                            console.log(`🔍 Fetching city tenant data for images, city ID: ${cityId}`);
                                            const cityResponse = await axios.get(`/api/tenants/${cityId}/`);
                                            const rawCityData = cityResponse.data;

                                            console.log('🔍 Raw city tenant data for images:', rawCityData);

                                            // Extract profile data which contains the image fields
                                            cityData = rawCityData.profile_data || {};
                                            console.log('🔍 Extracted city profile data:', cityData);
                                        } catch (error) {
                                            console.error('❌ Error fetching city data for images:', error);
                                        }
                                    } else {
                                        console.log('⚠️ No city ID found in subcity data for images');
                                    }
                                } catch (error) {
                                    console.error('❌ Error fetching subcity data for images:', error);
                                }
                            } else {
                                console.log('⚠️ No subcity ID found in kebele data for images');
                            }
                        } catch (error) {
                            console.error('❌ Error fetching kebele data for images:', error);
                        }
                    } else {
                        console.log('⚠️ No kebele ID available for dynamic image fetching - falling back to user tenant data');

                        // Fallback to original database API when dynamic fetching fails
                        try {
                            console.log('🔍 Calling get-tenant-data API with user tenant context...');
                            const tenantDataResponse = await axios.post('/api/tenants/get-tenant-data/', {
                                kebele_tenant_id: user?.tenant_type === 'kebele' ? user?.tenant_id : null,
                                subcity_tenant_id: user?.parent_tenant_id,
                                city_tenant_id: user?.city_tenant_id
                            }, {
                                headers: {
                                    'Authorization': `Bearer ${token}`,
                                    'Content-Type': 'application/json'
                                }
                            });

                            console.log('🔍 Fallback database query response:', tenantDataResponse.data);

                            if (tenantDataResponse.data && tenantDataResponse.status === 200) {
                                kebeleData = tenantDataResponse.data.kebele_data;
                                subcityData = tenantDataResponse.data.subcity_data;
                                cityData = tenantDataResponse.data.city_data;
                                console.log('🔍 Using fallback database tenant data for images:', {
                                    kebele_logo: kebeleData?.logo,
                                    subcity_logo: subcityData?.logo,
                                    subcity_signature: subcityData?.mayor_signature,
                                    subcity_pattern: subcityData?.pattern_image,
                                    city_logo: cityData?.logo,
                                    city_signature: cityData?.mayor_signature
                                });
                            }
                        } catch (error) {
                            console.error('❌ Fallback tenant data fetch also failed:', error);
                        }
                    }
                }

                // Dynamic logo fetching from database enabled - logos now fetched from tenant profile tables
                console.log('🔍 Tenant logos now fetched dynamically from database tenant profile tables');
                
                // If database returns null/invalid logos, try actual existing filenames
                if (subcityData && (!subcityData.logo || subcityData.logo === '/media/logos/subcity/logo.jpg')) {
                    console.log('🔍 Database subcity logo is null or pointing to non-existent file, trying actual filenames...');
                    
                    const actualSubcityLogos = [
                        '/media/logos/subcity/logo_final_gondar_with_camelot1.png',
                        '/media/logos/subcity/logo_final_gondar_with_camelot1_2sMQZII.png',
                        '/media/logos/subcity/logo_final_gondar_with_camelot1_5eRTVGF.png',
                        '/media/logos/subcity/logo_final_gondar_with_camelot1_bDEEIJy.png'
                    ];
                    
                    for (const logoPath of actualSubcityLogos) {
                        try {
                            const testResponse = await fetch(`${getApiUrl()}${logoPath}`, { method: 'HEAD' });
                            if (testResponse.ok) {
                                console.log('🔍 Found working subcity logo:', logoPath);
                                subcityData.logo = logoPath;
                                break;
                            }
                        } catch (error) {
                            console.log('🔍 Logo not found:', logoPath);
                        }
                    }
                }

                // Check kebele logos too
                if (kebeleData && (!kebeleData.logo || kebeleData.logo === '/media/logos/kebele/logo.jpg')) {
                    console.log('🔍 Database kebele logo is null or pointing to non-existent file, trying actual filenames...');
                    
                    const actualKebeleLogos = [
                        `/media/logos/kebele/kebele_${kebeleData?.id || user?.tenant_id}_logo.jpg`, // Try specific tenant ID first
                        '/media/logos/kebele/logo_final_gondar_with_camelot1.png',
                        '/media/logos/kebele/logo_final_gondar_with_camelot1_494xzQi.png',
                        '/media/logos/kebele/logo_final_gondar_with_camelot1_OqrJKun.png',
                        '/media/logos/kebele/logo_final_gondar_with_camelot1_UtsdZHA.png',
                        '/media/logos/kebele/fasil.jpg'
                    ];
                    
                    for (const logoPath of actualKebeleLogos) {
                        try {
                            const testResponse = await fetch(`${getApiUrl()}${logoPath}`, { method: 'HEAD' });
                            if (testResponse.ok) {
                                console.log('🔍 Found working kebele logo:', logoPath);
                                kebeleData.logo = logoPath;
                                break;
                            }
                        } catch (error) {
                            console.log('🔍 Logo not found:', logoPath);
                        }
                    }
                }

                // Set logo with priority: kebele > subcity > city
                console.log('🔍 Setting logo with priority system...');
                console.log('🔍 Available data for logo:', {
                    kebeleData: kebeleData ? { logo: kebeleData.logo } : null,
                    subcityData: subcityData ? { logo: subcityData.logo } : null,
                    cityData: cityData ? { logo: cityData.logo } : null
                });

                // Set tenant logo, pattern image, and signature from database data
                let logoUrl = null;
                let patternImageUrl = null;
                let signatureUrl = null;

                // Priority: Citizen's kebele > Citizen's subcity > Citizen's city (always use citizen's hierarchy)
                // Only use database-sourced images, no static fallbacks
                if (kebeleData?.logo) {
                    console.log('🔍 Raw kebele logo from database:', kebeleData.logo);
                    console.log('🔍 Logo starts with http?', kebeleData.logo.startsWith('http'));
                    console.log('🔍 Logo starts with /media/?', kebeleData.logo.startsWith('/media/'));
                    logoUrl = kebeleData.logo.startsWith('http') ? kebeleData.logo :
                             kebeleData.logo.startsWith('/media/') ? `${getApiUrl()}${kebeleData.logo}` : `${getApiUrl()}/media/${kebeleData.logo}`;
                    console.log('🔍 Using citizen kebele logo from database:', logoUrl);
                } else if (subcityData?.logo) {
                    logoUrl = subcityData.logo.startsWith('http') ? subcityData.logo :
                             subcityData.logo.startsWith('/media/') ? `${getApiUrl()}${subcityData.logo}` : `${getApiUrl()}/media/${subcityData.logo}`;
                    console.log('🔍 Using citizen subcity logo from database:', logoUrl);
                } else if (cityData?.logo) {
                    logoUrl = cityData.logo.startsWith('http') ? cityData.logo :
                             cityData.logo.startsWith('/media/') ? `${getApiUrl()}${cityData.logo}` : `${getApiUrl()}/media/${cityData.logo}`;
                    console.log('🔍 Using citizen city logo from database:', logoUrl);
                } else {
                    console.log('⚠️ No tenant logo found in database - logo will be null (admin needs to upload logo)');
                    logoUrl = null;
                }

                // Check if ID card is approved by subcity admin and ready for printing
                const isApprovedForPrinting = idCard?.status === 'approved' || idCard?.status === 'printed' || idCard?.status === 'issued';
                console.log('🔍 ID Card status:', idCard?.status);
                console.log('🔍 Is approved for printing (pattern/signature):', isApprovedForPrinting);

                // Set pattern image only if subcity admin approved (priority: kebele > subcity > city)
                // Only use database-sourced images, no static fallbacks
                if (isApprovedForPrinting) {
                    if (kebeleData?.pattern_image) {
                        patternImageUrl = kebeleData.pattern_image.startsWith('http') ? kebeleData.pattern_image :
                                         kebeleData.pattern_image.startsWith('/media/') ? `${getApiUrl()}${kebeleData.pattern_image}` : `${getApiUrl()}/media/${kebeleData.pattern_image}`;
                        console.log('🔍 Using kebele pattern image from database (approved):', patternImageUrl);
                    } else if (subcityData?.pattern_image) {
                        patternImageUrl = subcityData.pattern_image.startsWith('http') ? subcityData.pattern_image :
                                         subcityData.pattern_image.startsWith('/media/') ? `${getApiUrl()}${subcityData.pattern_image}` : `${getApiUrl()}/media/${subcityData.pattern_image}`;
                        console.log('🔍 Using subcity pattern image from database (approved):', patternImageUrl);
                    } else {
                        console.log('⚠️ No pattern image found in database - pattern will be null (admin needs to upload pattern)');
                    }

                    // Set mayor signature only if subcity admin approved (priority: city > subcity > kebele)
                    // Only use database-sourced signatures, no static fallbacks
                    if (cityData?.mayor_signature) {
                        signatureUrl = cityData.mayor_signature.startsWith('http') ? cityData.mayor_signature :
                                      cityData.mayor_signature.startsWith('/media/') ? `${getApiUrl()}${cityData.mayor_signature}` : `${getApiUrl()}/media/${cityData.mayor_signature}`;
                        console.log('🔍 Using city mayor signature from database (approved):', signatureUrl);
                    } else if (subcityData?.mayor_signature) {
                        signatureUrl = subcityData.mayor_signature.startsWith('http') ? subcityData.mayor_signature :
                                      subcityData.mayor_signature.startsWith('/media/') ? `${getApiUrl()}${subcityData.mayor_signature}` : `${getApiUrl()}/media/${subcityData.mayor_signature}`;
                        console.log('🔍 Using subcity mayor signature from database (approved):', signatureUrl);
                    } else if (kebeleData?.mayor_signature) {
                        signatureUrl = kebeleData.mayor_signature.startsWith('http') ? kebeleData.mayor_signature :
                                      kebeleData.mayor_signature.startsWith('/media/') ? `${getApiUrl()}${kebeleData.mayor_signature}` : `${getApiUrl()}/media/${kebeleData.mayor_signature}`;
                        console.log('🔍 Using kebele mayor signature from database (approved):', signatureUrl);
                    } else {
                        console.log('⚠️ No mayor signature found in database - signature will be null (admin needs to upload signature)');
                    }
                } else {
                    console.log('🔍 ID card not approved by subcity admin - pattern and signature will not be displayed');
                    console.log('🔍 Current status:', idCard?.status, '- Required: approved, printed, or issued');
                }

                // Update state with fetched data
                // Logo should always be displayed regardless of approval status
                if (logoUrl) {
                    console.log('🔍 Setting tenantLogo to (always displayed):', logoUrl);
                    console.log('🔍 Logo source tenant info:', {
                        kebele_id: kebeleId,
                        kebele_name: kebeleData?.name,
                        subcity_name: subcityData?.name,
                        city_name: cityData?.name,
                        logo_from: kebeleData?.logo ? 'kebele' : subcityData?.logo ? 'subcity' : 'city'
                    });
                    setTenantLogo(logoUrl);
                    setLogoError(false); // Reset error state when setting new logo
                } else {
                    console.log('🔍 No logo available in tenant profiles, tenantLogo will remain null');
                    setTenantLogo(null);
                    setLogoError(false);
                }

                if (patternImageUrl) {
                    console.log('🔍 Setting pattern image to:', patternImageUrl);
                    setSubcityPatternImage(patternImageUrl);
                } else {
                    console.log('🔍 No pattern image to set (not approved or not found)');
                    setSubcityPatternImage(null);
                }

                if (signatureUrl) {
                    console.log('🔍 Setting mayor signature to:', signatureUrl);
                    setMayorSignature(signatureUrl);
                } else {
                    console.log('🔍 No mayor signature to set (not approved or not found)');
                    setMayorSignature(null);
                }



                // Note: Amharic names are now handled by fetchAmharicNames() function only
                // This function focuses only on logos, signatures, and pattern images

                // Note: Images require API permissions or alternative access method
                console.log('� Logo, signature, and pattern images require API access to tenant data');
                console.log('� Currently showing "No Logo Available" placeholder until API permissions are resolved');

                // Final debug summary
                console.log('🔍 fetchTenantProfileData() completed. Final state:');
                console.log('🔍 tenantLogo will be:', logoUrl || 'null (will show fallback)');
                console.log('🔍 mayorSignature will be:', signatureUrl || 'null');
                console.log('🔍 subcityPatternImage will be:', patternImageUrl || 'null');

            } catch (error) {
                console.error('❌ Error fetching tenant profile data:', error);
                console.error('❌ Full error:', error);
            }
        };

        if (user) {
            setTenantData();
        }
    }, [user, idCard, citizen, idCard.subcity_approved_at]);

    // Debug: Log the actual data structure to help with field mapping
    console.log('🔍 ID Card Template Data Debug:', {
        idCard: idCard,
        citizen: citizen,
        tenantHierarchy: tenantHierarchy,
        tenantNames: tenantNames,
        mayorSignature: mayorSignature,
        citizenFields: {
            phone: citizen.phone,
            phone_number: citizen.phone_number,
            blood_type: citizen.blood_type,
            employment: citizen.employment,
            occupation: citizen.occupation,
            emergency_contacts: citizen.emergency_contacts,
            ketena: citizen.ketena,
            place_of_birth: citizen.place_of_birth,
            subcity: citizen.subcity,
            kebele: citizen.kebele
        }
    });

    // Additional debug for logo rendering
    console.log('🔍 Logo rendering debug:', {
        tenantLogo,
        isNull: tenantLogo === null,
        isUndefined: tenantLogo === undefined,
        urlType: typeof tenantLogo,
        urlLength: tenantLogo ? tenantLogo.length : 0
    });

    // Log the logo URL for debugging
    if (tenantLogo) {
        console.log('🔍 Logo URL set:', tenantLogo);
    }

    // Standard ID card dimensions: 3.375" x 2.125" (exact measurements)
    // 3.375" = 243px at 72 DPI, 2.125" = 153px at 72 DPI
    const cardStyle = {
        width: preview ? '486px' : '243px', // 3.375" at 144 DPI for preview, 72 DPI for normal
        height: preview ? '306px' : '153px', // 2.125" at 144 DPI for preview, 72 DPI for normal
        position: 'relative',
        backgroundColor: '#ffffff',
        border: '1px solid #ddd',
        borderRadius: '6px',
        overflow: 'hidden',
        fontFamily: '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif',
        boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
        aspectRatio: '3.375/2.125' // Exact standard ID card ratio
    };



    if (side === 'front') {
        return (
            <Box sx={cardStyle}>

                {/* New SVG Security Pattern Background */}
                <Box
                    sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        opacity: 0.8,
                        backgroundImage: `url("data:image/svg+xml;charset=utf-8,%3Csvg width='800' height='500' viewBox='0 0 800 500' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3ClinearGradient id='base' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' stop-color='%23fefcf5'/%3E%3Cstop offset='50%25' stop-color='%23f2e9b3'/%3E%3Cstop offset='100%25' stop-color='%23bfa945'/%3E%3C/linearGradient%3E%3ClinearGradient id='headerWhite' x1='0%25' y1='0%25' x2='0%25' y2='100%25'%3E%3Cstop offset='0%25' stop-color='%23fffefb' stop-opacity='0.97'/%3E%3Cstop offset='100%25' stop-color='%23e9e7df' stop-opacity='0.2'/%3E%3C/linearGradient%3E%3Cpattern id='guilloche' width='120' height='120' patternUnits='userSpaceOnUse'%3E%3Cpath d='M60,0 C80,30 40,90 60,120 M0,60 C30,80 90,40 120,60 M30,0 C45,30 75,30 90,0 M30,120 C45,90 75,90 90,120' stroke='%23c9a034' fill='none' stroke-width='1.4' opacity='0.7'/%3E%3Ccircle cx='60' cy='60' r='20' stroke='%23d9b94b' fill='none' stroke-width='1.1' opacity='0.55'/%3E%3C/pattern%3E%3Cpattern id='microtext' width='400' height='40' patternUnits='userSpaceOnUse'%3E%3Ctext x='0' y='15' font-size='8' fill='%23f9f7f1' opacity='0.85' font-family='Courier New, monospace' letter-spacing='2px' font-weight='700'%3E• SECURE DOCUMENT • FEDERAL IDENTITY • REPUBLIC OF ETHIOPIA • OFFICIAL USE ONLY •%3C/text%3E%3Ctext x='0' y='32' font-size='8' fill='%23f9f7f1' opacity='0.85' font-family='Courier New, monospace' letter-spacing='2px' font-weight='700'%3E• ID-ETH-2024 • ANTI-COUNTERFEIT • UV-SECURITY • HOLOGRAM •%3C/text%3E%3C/pattern%3E%3Cpattern id='hexgrid' width='50' height='43.3' patternUnits='userSpaceOnUse'%3E%3Cpolygon points='25,0 50,21.65 50,65 25,86.6 0,65 0,21.65' stroke='%23a87f17' fill='none' stroke-width='1' opacity='0.3'/%3E%3Ccircle cx='25' cy='21.65' r='3' fill='%23e9c85b' opacity='0.8'/%3E%3Ccircle cx='25' cy='65' r='3' fill='%23e9c85b' opacity='0.8'/%3E%3C/pattern%3E%3Cpattern id='waveCross' width='300' height='300' patternUnits='userSpaceOnUse'%3E%3Cpath d='M0 150 Q75 0 150 150 T300 150' fill='none' stroke='%239a8c42' stroke-width='1.6' opacity='0.55'/%3E%3Cpath d='M150 0 Q0 75 150 150 T150 300' fill='none' stroke='%23d1a924' stroke-width='1.7' opacity='0.6'/%3E%3Cpath d='M0 75 Q150 225 300 75' fill='none' stroke='%23ffd54c' stroke-width='1.3' opacity='0.5'/%3E%3C/pattern%3E%3Cpattern id='securityDots' width='12' height='12' patternUnits='userSpaceOnUse'%3E%3Ccircle cx='6' cy='6' r='2.4' fill='%23fadb5f' opacity='0.9'/%3E%3Ccircle cx='6' cy='6' r='1.0' fill='%23fffbee' opacity='0.95'/%3E%3C/pattern%3E%3Cpattern id='goldSecurity' width='20' height='20' patternUnits='userSpaceOnUse'%3E%3Crect width='20' height='20' fill='%23b99218' opacity='0.15'/%3E%3Cpath d='M0,0 L20,20 M20,0 L0,20' stroke='%23f7df48' stroke-width='1.6' opacity='0.8'/%3E%3C/pattern%3E%3CradialGradient id='burst' cx='50%25' cy='50%25' r='50%25'%3E%3Cstop offset='0%25' stop-color='%23fffde7' stop-opacity='0.92'/%3E%3Cstop offset='60%25' stop-color='%23fbd13e' stop-opacity='0.45'/%3E%3Cstop offset='90%25' stop-color='%23ab8635' stop-opacity='0.1'/%3E%3Cstop offset='100%25' stop-color='%236f5c26' stop-opacity='0'/%3E%3C/radialGradient%3E%3Cpattern id='holoPattern' width='500' height='500' patternUnits='userSpaceOnUse'%3E%3ClinearGradient id='holoGradient' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' stop-color='%23d9cb8a'/%3E%3Cstop offset='25%25' stop-color='%23eee2a8'/%3E%3Cstop offset='50%25' stop-color='%23f4e285'/%3E%3Cstop offset='75%25' stop-color='%23e6c55c'/%3E%3Cstop offset='100%25' stop-color='%23f9f4d1'/%3E%3C/linearGradient%3E%3Cg stroke='url(%23holoGradient)' stroke-width='2' fill='none' opacity='0.37'%3E%3Cpath d='M0 100 Q125 0 250 100 T500 100'/%3E%3Cpath d='M0 300 Q125 200 250 300 T500 300'/%3E%3Cpath d='M100 0 Q0 125 100 250 T100 500'/%3E%3Cpath d='M300 0 Q200 125 300 250 T300 500'/%3E%3Ccircle cx='250' cy='250' r='200' stroke-dasharray='12,12'/%3E%3C/g%3E%3C/pattern%3E%3Cpattern id='uvPattern' width='80' height='80' patternUnits='userSpaceOnUse'%3E%3Crect width='80' height='80' fill='%23000' opacity='0'/%3E%3Cpath d='M0 40 L80 40 M40 0 L40 80' stroke='%237fffd4' stroke-width='1.3' opacity='0.15'/%3E%3Ctext x='40' y='45' font-size='16' fill='%237fffd4' opacity='0.12' font-family='Arial' text-anchor='middle' font-weight='800'%3EUV%3C/text%3E%3C/pattern%3E%3Cfilter id='emboss'%3E%3CfeGaussianBlur in='SourceAlpha' stdDeviation='1.5' result='blur'/%3E%3CfeSpecularLighting in='blur' surfaceScale='6' specularConstant='0.7' specularExponent='15' lighting-color='%23ffffff' result='spec'%3E%3CfePointLight x='-5000' y='-10000' z='20000'/%3E%3C/feSpecularLighting%3E%3CfeComposite in='spec' in2='SourceGraphic' operator='in' result='specOut'/%3E%3CfeComposite in='SourceGraphic' in2='specOut' operator='arithmetic' k1='0' k2='1' k3='1' k4='0'/%3E%3C/filter%3E%3Cpattern id='borderGuilloche' width='30' height='30' patternUnits='userSpaceOnUse'%3E%3Cpath d='M15,0 C20,7.5 10,22.5 15,30 M0,15 C7.5,20 22.5,10 30,15' stroke='%23a88510' fill='none' stroke-width='1.6' opacity='0.65'/%3E%3C/pattern%3E%3CradialGradient id='sparkleGlow' cx='50%25' cy='50%25' r='50%25'%3E%3Cstop offset='0%25' stop-color='%23fff9cc' stop-opacity='1'/%3E%3Cstop offset='80%25' stop-color='%23fff9cc' stop-opacity='0'/%3E%3C/radialGradient%3E%3Ccircle id='sparkle' cx='0' cy='0' r='6' fill='url(%23sparkleGlow)'/%3E%3C/defs%3E%3Crect width='100%25' height='100%25' fill='url(%23base)'/%3E%3Crect width='100%25' height='120' fill='url(%23headerWhite)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23holoPattern)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23guilloche)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23hexgrid)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23microtext)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23waveCross)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23uvPattern)'/%3E%3Ccircle cx='700' cy='420' r='110' fill='url(%23burst)' filter='url(%23emboss)' opacity='0.9'/%3E%3Cuse href='%23sparkle' x='670' y='370' opacity='0.6'/%3E%3Cuse href='%23sparkle' x='720' y='450' opacity='0.5'/%3E%3Cuse href='%23sparkle' x='685' y='410' opacity='0.45'/%3E%3Cuse href='%23sparkle' x='730' y='390' opacity='0.4'/%3E%3Cpath d='M0 450 C150 350 650 500 800 450' fill='none' stroke='%23a88510' stroke-width='4' opacity='0.45' stroke-dasharray='14 8'/%3E%3Cpath d='M0 100 C200 200 600 0 800 100' fill='none' stroke='%23927a2e' stroke-width='3' opacity='0.55'/%3E%3Crect x='650' y='350' width='150' height='150' fill='url(%23securityDots)' rx='20' ry='20'/%3E%3Cuse href='%23sparkle' x='680' y='380' opacity='0.6'/%3E%3Cuse href='%23sparkle' x='740' y='420' opacity='0.55'/%3E%3Cuse href='%23sparkle' x='720' y='390' opacity='0.5'/%3E%3Cuse href='%23sparkle' x='700' y='430' opacity='0.45'/%3E%3Crect x='5' y='5' width='790' height='490' fill='none' stroke='url(%23borderGuilloche)' stroke-width='7' rx='20' ry='20'/%3E%3Ccircle cx='20' cy='20' r='18' stroke='%23d8b924' fill='none' stroke-width='3' opacity='0.8'/%3E%3Ccircle cx='780' cy='20' r='18' stroke='%23d8b924' fill='none' stroke-width='3' opacity='0.8'/%3E%3Ccircle cx='20' cy='480' r='18' stroke='%23d8b924' fill='none' stroke-width='3' opacity='0.8'/%3E%3Ccircle cx='780' cy='480' r='18' stroke='%23d8b924' fill='none' stroke-width='3' opacity='0.8'/%3E%3C/svg%3E")`,
                        backgroundSize: 'cover',
                        backgroundRepeat: 'no-repeat',
                        backgroundPosition: 'center',
                        zIndex: 1
                    }}
                />





                {/* City Name Microprint Text - 9 random positions */}
                {microprintPositions.map((position) => (
                    <Typography
                        key={position.id}
                        sx={{
                            position: 'absolute',
                            top: `${position.top}%`,
                            left: `${position.left}%`,
                            fontSize: preview ? '8px' : '6px', // Increased font size for visibility
                            fontWeight: 'bold',
                            color: '#0d47a1', // Darker blue for better visibility
                            opacity: 0.4, // Increased opacity for better visibility
                            transform: `rotate(${position.rotation}deg)`,
                            transformOrigin: 'center',
                            pointerEvents: 'none',
                            zIndex: 4, // Above pattern but below main content
                            fontFamily: 'monospace',
                            letterSpacing: '0.3px',
                            userSelect: 'none',
                            textShadow: '0 0 1px rgba(255, 255, 255, 0.5)', // Add subtle shadow for contrast
                            WebkitTextStroke: '0.2px rgba(13, 71, 161, 0.8)' // Add text stroke for definition
                        }}
                    >
                        {position.text}
                    </Typography>
                ))}

                {/* Pattern Image Overlay - Full pattern coverage */}
                {(idCard.subcity_approved_at || currentTenantType === 'subcity' || currentTenantType === 'kebele') && subcityPatternImage && (
                    <Box
                        sx={{
                            position: 'absolute',
                            top: 0, // Cover the entire card
                            left: 0,
                            right: 0,
                            bottom: 0,
                            backgroundImage: `url(${subcityPatternImage})`,
                            backgroundSize: 'cover', // Cover the entire area
                            backgroundRepeat: 'no-repeat',
                            backgroundPosition: 'center',
                            opacity: 0.15, // Clear visibility without overwhelming content
                            zIndex: 2, // Above SVG pattern, below content
                            pointerEvents: 'none'
                        }}
                    />
                )}


                {/* Header with Ethiopian flag and title - Completely transparent */}
                <Box
                    sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        px: preview ? 1.5 : 1,
                        py: preview ? 0.2 : 0.1, // Reduced margins
                        backgroundColor: 'transparent', // Completely transparent to eliminate white line
                        position: 'relative',
                        zIndex: 10, // Higher z-index to ensure visibility over pattern
                        margin: 0, // Remove any margin
                        mb: preview ? 0.3 : 0.2 // Small margin bottom to move line up
                    }}
                >
                    {/* Golden line with star pattern as header's bottom border */}
                    {/* <Box
            sx={{
              position: 'absolute',
              bottom: preview ? -1.5 : -1, // Position line as bottom border of header
              left: 0,
              right: 0,
              height: preview ? '1.5px' : '1px', // Thicker line
              background: `
                linear-gradient(to right,
                  #DAA520 0%,
                  #DAA520 45%,
                  transparent 45%,
                  transparent 55%,
                  #DAA520 55%,
                  #DAA520 100%
                )
              `,
              zIndex: 15 // Higher z-index to be above everything
            }}
          /> */}

                    {/* Star pattern in the middle of the header's bottom border */}
                    {/* <Box
            sx={{
              position: 'absolute',
              bottom: preview ? -4 : -3, // Position star on the bottom border line
              left: '50%',
              transform: 'translateX(-50%)',
              fontSize: preview ? '12px' : '9px',
              color: '#DAA520',
              zIndex: 16, // Highest z-index
              textShadow: '0 0 2px #B8860B'
            }}
          >
            ★
          </Box> */}
                    {/* Dynamic Tenant Logo */}
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, ml: preview ? 1 : 0.5, mt: preview ? -0.5 : -0.3 }}>
                        <Box
                            sx={{
                                width: preview ? 100 : 70, // 2x larger: 50*2=100, 35*2=70
                                height: preview ? 50 : 35,  // 2x larger: 35*2=70, 25*2=50
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                backgroundColor: 'transparent' // Make background transparent
                            }}
                        >
                            {tenantLogo && (
                                <img
                                    key={tenantLogo} // Force re-render when logo changes
                                    src={tenantLogo}
                                    alt={`${user?.tenant_type || 'Tenant'} Logo`}
                                    crossOrigin="anonymous" // Add CORS support
                                    onLoad={() => {
                                        console.log('✅ Tenant logo loaded successfully:', tenantLogo);
                                        setLogoError(false); // Clear error on successful load
                                    }}
                                    style={{
                                        width: preview ? 100 : 70,
                                        height: preview ? 50 : 35,
                                        objectFit: 'contain',
                                        display: 'block',
                                        backgroundColor: 'transparent',
                                        border: 'none',
                                        boxShadow: 'none'
                                    }}
                                    onError={() => {
                                        console.error('❌ Tenant logo failed to load:', tenantLogo);
                                        console.log('🔄 Keeping logo URL for debugging, not clearing it');
                                        setLogoError(true);
                                        // Don't clear the logo - keep it for debugging
                                    }}
                                />
                            )}

                        </Box>

                        {/* Ethiopian Flag in top-right */}
                        <Box
                            sx={{
                                position: 'absolute',
                                top: preview ? '12px' : '8px',
                                right: preview ? '12px' : '8px',
                                zIndex: 4,
                                width: preview ? '40px' : '32px',
                                height: preview ? '27px' : '22px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                            }}
                        >
                            <Box
                                sx={{
                                    width: '100%',
                                    height: '100%',
                                    background: 'linear-gradient(to bottom, #009639 33.33%, #FEDD00 33.33%, #FEDD00 66.66%, #DA020E 66.66%)',
                                    borderRadius: '2px',
                                    border: '1px solid #ddd',
                                    position: 'relative',
                                    boxShadow: '0 1px 3px rgba(0,0,0,0.2)'
                                }}
                            >
                                {/* Ethiopian coat of arms center */}
                                <Box
                                    sx={{
                                        position: 'absolute',
                                        top: '50%',
                                        left: '50%',
                                        transform: 'translate(-50%, -50%)',
                                        width: preview ? '14px' : '12px',
                                        height: preview ? '14px' : '12px',
                                        backgroundColor: '#1565C0',
                                        borderRadius: '50%',
                                        border: '1px solid white',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        fontSize: preview ? '8px' : '6px',
                                        color: 'white',
                                        fontWeight: 'bold'
                                    }}
                                >
                                    ★
                                </Box>
                            </Box>
                        </Box>
                    </Box>

                    {/* Title Section */}
                    <Box sx={{ textAlign: 'center', flex: 1 }}>
                        <Typography
                            sx={{
                                fontSize: preview ? '12px' : '9px', // Slightly smaller for two lines
                                fontWeight: 'bold',
                                color: '#000000', // Black color
                                lineHeight: 1.1,
                                margin: 0,
                                padding: 0
                            }}
                        >
                            {cityNameAm && subcityNameAm && kebeleNameAm ? (
                                `በ${cityNameAm} ከተማ አስተዳደር በ${subcityNameAm} ክ/ከተማ የ${kebeleNameAm} ቀበሌ`
                            ) : (
                                'የመታወቂያ ካርድ'
                            )}
                        </Typography>
                        <Typography
                            sx={{
                                fontSize: preview ? '11px' : '8px', // Same size as first line
                                fontWeight: '700',
                                color: '#000000', // Black color
                                lineHeight: 1.1,
                                margin: 0,
                                padding: 0,
                                mt: preview ? 0.1 : 0.05 // Small spacing between lines
                            }}
                        >
                            የነዋሪዎች መታወቂያ ካርድ
                        </Typography>

                        {/* Additional text under Amharic header */}
                        <Typography
                            sx={{
                                fontSize: preview ? '10px' : '8px',
                                fontWeight: '600',
                                color: '#5a4a1a',
                                lineHeight: 1.1,
                                margin: 0,
                                padding: 0,
                                mt: preview ? 0.2 : 0.1,
                                textAlign: 'center',
                                letterSpacing: '0.5px'
                            }}
                        >
                            {tenantNames.city_name || 'Gondar'} City Administration • {tenantNames.subcity_name || 'Subcity'} • {tenantNames.kebele_name || 'Kebele'}
                        </Typography>
                    </Box>
                </Box>

                {/* Main Content */}
                <Box sx={{
                    display: 'flex',
                    px: preview ? 1.5 : 1, // Only horizontal padding
                    pt: preview ? 0.5 : 0.3, // Small top padding for spacing without white line
                    pb: preview ? 3 : 2, // Extra bottom padding for footer space
                    gap: preview ? 1.5 : 1,
                    position: 'relative',
                    zIndex: 100, // Much higher z-index to ensure all content is in front
                    alignItems: 'center', // Center content vertically
                    minHeight: preview ? '180px' : '100px', // Adjusted height for footer
                    backgroundColor: 'transparent' // Ensure no background color
                }}>
                    {/* Photo Section */}
                    <Box sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: '100%'
                    }}>
                        <Box
                            sx={{
                                width: preview ? 140 : 110, // Maximized photo size for better visualization
                                height: preview ? 180 : 140, // Maximized height maintaining 3:4 ratio
                                overflow: 'hidden',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                mb: 0, // No margin bottom - barcode will be directly underneath
                                position: 'relative',
                                zIndex: 100, // Much higher z-index to ensure photo is in front
                                boxShadow: preview ? '0 4px 8px rgba(0, 0, 0, 0.15)' : '0 2px 4px rgba(0, 0, 0, 0.15)', // Added shadow
                                borderRadius: '2px' // Slight border radius for professional look
                            }}
                        >
                            {(citizen.photo || idCard.citizen_photo || idCard.citizen?.photo) ? (
                                <img
                                    src={citizen.photo || idCard.citizen_photo || idCard.citizen?.photo}
                                    alt="Citizen Photo"
                                    style={{
                                        width: '100%',
                                        height: '100%',
                                        objectFit: 'cover',
                                        position: 'relative',
                                        zIndex: 10
                                    }}
                                />
                            ) : (
                                <Typography sx={{ fontSize: preview ? '12px' : '10px', color: '#7f8c8d', fontWeight: 'bold' }}>
                                    PHOTO
                                </Typography>
                            )}
                        </Box>

                        {/* Real Scannable Barcode under photo with all required data */}
                        <Box sx={{ mt: 0.2 }}>
                            <SimpleBarcode
                                value={(() => {
                                    // Create barcode data with citizen information (NO PHOTO)
                                    const fullName = `${citizen.first_name || ''} ${citizen.middle_name || ''} ${citizen.last_name || ''}`.trim();
                                    const digitalId = citizen.digital_id || idCard.citizen_digital_id || idCard.uuid || 'Unknown';
                                    const issueDateStr = idCard.issue_date || '01/01/2024';
                                    const expiryDateStr = idCard.expiry_date || '01/01/2026';
                                    const kebeleName = citizen.kebele || 'Unknown';
                                    const subcityName = citizen.subcity || 'Unknown';
                                    const dobStr = citizen.date_of_birth || '01/01/1990';
                                    const phoneNumber = citizen.phone || 'N/A';

                                    // Create barcode data string (NO PHOTO - just text data)
                                    const barcodeData = `${fullName}|${digitalId}|${issueDateStr}|${expiryDateStr}|${kebeleName}|${subcityName}|${dobStr}|${phoneNumber}`;

                                    console.log('🔍 Barcode Data (no photo):', barcodeData);
                                    return barcodeData;
                                })()}
                                width={preview ? 120 : 100}
                                height={preview ? 25 : 20}
                            />
                        </Box>



                        <Typography
                            sx={{
                                fontSize: preview ? '13px' : '11px',
                                fontFamily: 'monospace',
                                textAlign: 'center',
                                mt: 0.3,
                                fontWeight: 'bold',
                                color: '#000000'
                            }}
                        >
                            {citizen.digital_id || idCard.citizen_digital_id || idCard.card_number || '9572431481361091'}
                        </Typography>




                    </Box>

                    {/* Citizen Information */}
                    <Box sx={{
                        flex: 1,
                        display: 'flex',
                        flexDirection: 'column',
                        gap: preview ? 0.8 : 0.6,
                        justifyContent: 'center', // Center content vertically
                        position: 'relative',
                        zIndex: 100 // Much higher z-index to ensure text is in front
                    }}>

                        {/* Name Section */}
                        <Box>
                            <Typography
                                sx={{
                                    fontSize: preview ? '10px' : '8px',
                                    color: '#3d320f',  // Updated label color
                                    fontWeight: 'bold',
                                    mb: 0.05
                                }}
                            >
                                ሙሉ ስም | Full Name
                            </Typography>
                            <Typography
                                sx={{
                                    fontSize: preview ? '13px' : '10px',
                                    fontWeight: 'bold',
                                    color: '#000000',  // Black value color
                                    lineHeight: 1.1
                                }}
                            >
                                {citizen.first_name_am && citizen.middle_name_am && citizen.last_name_am
                                    ? `${citizen.first_name_am} ${citizen.middle_name_am} ${citizen.last_name_am}`
                                    : idCard.citizen_name ||
                                    (citizen.first_name && citizen.middle_name && citizen.last_name
                                        ? `${citizen.first_name} ${citizen.middle_name} ${citizen.last_name}`
                                        : 'ተዎድሮስ አበበው ቸኮል')}
                            </Typography>
                            <Typography
                                sx={{
                                    fontSize: preview ? '10px' : '8px',
                                    color: '#000000',  // Black value color
                                    fontWeight: 'bold'  // Made bold
                                }}
                            >
                                {citizen.first_name && citizen.middle_name && citizen.last_name
                                    ? `${citizen.first_name} ${citizen.middle_name} ${citizen.last_name}`
                                    : idCard.citizen_name || 'Tewodros Abebaw Chekol'}
                            </Typography>
                        </Box>

                        {/* Date of Birth */}
                        <Box>
                            <Typography
                                sx={{
                                    fontSize: preview ? '11px' : '9px',
                                    color: '#3d320f',  // Updated label color
                                    fontWeight: 'bold',
                                    mb: 0.05
                                }}
                            >
                                የትውልድ ቀን | Date of Birth
                            </Typography>
                            <Typography
                                sx={{
                                    fontSize: preview ? '13px' : '10px',
                                    fontWeight: 'bold',
                                    color: '#000000'  // Black value color
                                }}
                            >
                                {(() => {
                                    // Get the date of birth
                                    const dobDate = citizen.date_of_birth || idCard.citizen_date_of_birth;

                                    if (dobDate) {
                                        // Convert Gregorian date to Ethiopian
                                        const gregorianDate = new Date(dobDate);
                                        const ethiopianDate = gregorianToEthiopian(gregorianDate);

                                        if (ethiopianDate) {
                                            return formatEthiopianDate(ethiopianDate, 'DD/MM/YYYY') + ' (EC)';
                                        }
                                    }

                                    // Fallback
                                    return '23/07/2002 (EC)';
                                })()}
                            </Typography>
                            <Typography
                                sx={{
                                    fontSize: preview ? '13px' : '10px',
                                    color: '#000000',  // Black value color
                                    fontWeight: 'bold'  // Made bold
                                }}
                            >
                                {(() => {
                                    const dobDate = citizen.date_of_birth || idCard.citizen_date_of_birth;

                                    if (dobDate) {
                                        return new Date(dobDate).toLocaleDateString('en-CA') + ' (GC)';
                                    }

                                    // Fallback
                                    return '2002-07-23 (GC)';
                                })()}
                            </Typography>
                        </Box>

                        {/* Gender */}
                        <Box>
                            <Typography
                                sx={{
                                    fontSize: preview ? '11px' : '9px',
                                    color: '#3d320f',  // Updated label color
                                    fontWeight: 'bold',
                                    mb: 0.05
                                }}
                            >
                                ጾታ | Sex
                            </Typography>
                            <Typography
                                sx={{
                                    fontSize: preview ? '13px' : '10px',
                                    fontWeight: 'bold',
                                    color: '#000000'  // Black value color
                                }}
                            >
                                {(citizen.gender || idCard.citizen_gender) === 'M' ||
                                    (citizen.gender || idCard.citizen_gender) === 'male' ? 'ወንድ' :
                                    (citizen.gender || idCard.citizen_gender) === 'F' ||
                                        (citizen.gender || idCard.citizen_gender) === 'female' ? 'ሴት' : 'ወንድ'}
                            </Typography>
                            <Typography
                                sx={{
                                    fontSize: preview ? '13px' : '10px',
                                    color: '#000000',  // Black value color
                                    fontWeight: 'bold'  // Made bold
                                }}
                            >
                                {(citizen.gender || idCard.citizen_gender) === 'M' ||
                                    (citizen.gender || idCard.citizen_gender) === 'male' ? 'Male' :
                                    (citizen.gender || idCard.citizen_gender) === 'F' ||
                                        (citizen.gender || idCard.citizen_gender) === 'female' ? 'Female' : 'Male'}
                            </Typography>
                        </Box>

                        {/* Nationality */}
                        <Box>
                            <Typography
                                sx={{
                                    fontSize: preview ? '11px' : '9px',
                                    color: '#3d320f',  // Updated label color
                                    fontWeight: 'bold',
                                    mb: 0.05
                                }}
                            >
                                ዜግነት | Nationality
                            </Typography>
                            <Typography
                                sx={{
                                    fontSize: preview ? '13px' : '10px',
                                    fontWeight: 'bold',
                                    color: '#000000'  // Black value color
                                }}
                            >
                                ኢትዮጵያዊ | Ethiopian
                            </Typography>
                            {/* <Typography
                sx={{
                  fontSize: preview ? '12px' : '10px',
                  color: '#000000',  // Changed to black for better print visibility
                  fontWeight: 'bold'  // Made bold
                }}
              >
                
              </Typography> */}
                        </Box>
                    </Box>

                </Box>

                {/* Signature Section - Closer to right bottom corner - Show when subcity admin approved OR for subcity users */}
                {/* Small citizen photo above signature
                <Box
                    sx={{
                        position: 'absolute',
                        bottom: preview ? '100px' : '85px', // Moved higher
                        right: preview ? '15px' : '12px',
                        zIndex: 5,
                        opacity: 0.7 // Made transparent
                    }}
                >
                    <SmallPhoto
                        citizen={citizen}
                        idCard={idCard}
                        size={preview ? 35 : 30}
                    />
                </Box> */}

                {/* Small processed photo next to the regular photo */}
                <Box
                    sx={{
                        position: 'absolute',
                        bottom: preview ? '100px' : '85px', // Same level as regular photo
                        right: preview ? '15px' : '12px',
                        zIndex: 5,
                        opacity: 0.8 // Slightly more visible for processed version
                    }}
                >
                    <SmallProcessedPhoto
                        citizen={citizen}
                        idCard={idCard}
                        size={preview ? 35 : 30}
                    />
                </Box>

                {(idCard.subcity_approved_at || currentTenantType === 'subcity' || currentTenantType === 'kebele') && mayorSignature && (
                    <Box
                        sx={{
                            position: 'absolute',
                            bottom: preview ? 25 : 18, // More space from bottom for footer
                            right: preview ? 2 : 1, // Even closer to right corner
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            zIndex: 3
                        }}
                    >
                        <Box
                            sx={{
                                width: preview ? 180 : 135, // 3x larger: 60*3=180, 45*3=135
                                height: preview ? 90 : 66,  // 3x larger: 30*3=90, 22*3=66
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                backgroundColor: 'transparent', // Make signature background transparent
                                mb: 0.1
                            }}
                        >
                            <img
                                src={mayorSignature}
                                alt="Mayor Signature"
                                crossOrigin="anonymous"
                                style={{
                                    width: preview ? 180 : 135,
                                    height: preview ? 90 : 66,
                                    objectFit: 'contain',
                                    opacity: 0.9, // Make signature transparent like the sample
                                    filter: 'contrast(1.3) brightness(0.8)' // Enhance contrast for better visibility
                                }}
                                onLoad={(e) => {
                                    console.log('✅ Mayor signature loaded successfully:', mayorSignature);
                                }}
                                onError={(e) => {
                                    console.error('❌ Mayor signature failed to load:', mayorSignature);
                                    // Fallback to placeholder if signature fails to load
                                    e.target.style.display = 'none';
                                    if (e.target.nextSibling) {
                                        e.target.nextSibling.style.display = 'flex';
                                    }
                                }}
                            />
                            {/* Fallback placeholder */}
                            <Typography sx={{
                                fontSize: preview ? '8px' : '6px',
                                textAlign: 'center',
                                color: '#7f8c8d',
                                fontWeight: 'bold',
                                display: 'none'
                            }}>
                                MAYOR SIGNATURE
                            </Typography>
                        </Box>

                        <Typography
                            sx={{
                                fontSize: preview ? '13px' : '11px',
                                fontWeight: 'bold',
                                color: '#09122C',
                                textAlign: 'center'
                            }}
                        >
                            ፊርማ | Signature
                        </Typography>
                    </Box>
                )}



                {/* Footer with Issue/Expiry Dates - More transparent to show geometric pattern */}
                <Box
                    sx={{
                        position: 'absolute',
                        bottom: 0,
                        left: 0,
                        right: 0,
                        backgroundColor: 'rgba(248, 249, 250, 0.75)', // More transparent to show pattern clearly
                        borderTop: '1px solid rgba(233, 236, 239, 0.8)',
                        px: preview ? 1.5 : 1,
                        py: preview ? 0.4 : 0.3,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        zIndex: 10, // Higher z-index to ensure visibility over pattern
                        backdropFilter: 'blur(0.5px)' // Minimal blur to maintain pattern visibility
                    }}
                >
                    <Typography
                        sx={{
                            fontSize: preview ? '10px' : '8px',
                            color: '#2c3e50',
                            fontWeight: 'bold',
                            letterSpacing: '0.2px',
                            textAlign: 'center'
                        }}
                    >
                        {(() => {
                            // Get issue date (current date if not set)
                            const issueDate = idCard.issue_date ? new Date(idCard.issue_date) : new Date();
                            const issueEthiopian = gregorianToEthiopian(issueDate);

                            // Calculate expiry date (2 years from issue in Ethiopian calendar)
                            const expiryEthiopian = {
                                year: issueEthiopian.year + 2,
                                month: issueEthiopian.month,
                                day: issueEthiopian.day
                            };

                            const issueEthStr = formatEthiopianDate(issueEthiopian, 'DD/MM/YYYY');
                            const expiryEthStr = formatEthiopianDate(expiryEthiopian, 'DD/MM/YYYY');

                            return `ISSUE/የተሰጠበት: ${issueEthStr} (EC) | EXPIRY/የሚያበቃበት: ${expiryEthStr} (EC)`;
                        })()}
                    </Typography>
                </Box>
            </Box>
        );
    } else {
        // Back side
        return (
            <Box sx={cardStyle}>

                {/* New SVG Security Pattern Background */}
                <Box
                    sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        opacity: 0.8,
                        backgroundImage: `url("data:image/svg+xml;charset=utf-8,%3Csvg width='800' height='500' viewBox='0 0 800 500' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3ClinearGradient id='base' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' stop-color='%23fefcf5'/%3E%3Cstop offset='50%25' stop-color='%23f2e9b3'/%3E%3Cstop offset='100%25' stop-color='%23bfa945'/%3E%3C/linearGradient%3E%3ClinearGradient id='headerWhite' x1='0%25' y1='0%25' x2='0%25' y2='100%25'%3E%3Cstop offset='0%25' stop-color='%23fffefb' stop-opacity='0.97'/%3E%3Cstop offset='100%25' stop-color='%23e9e7df' stop-opacity='0.2'/%3E%3C/linearGradient%3E%3Cpattern id='guilloche' width='120' height='120' patternUnits='userSpaceOnUse'%3E%3Cpath d='M60,0 C80,30 40,90 60,120 M0,60 C30,80 90,40 120,60 M30,0 C45,30 75,30 90,0 M30,120 C45,90 75,90 90,120' stroke='%23c9a034' fill='none' stroke-width='1.4' opacity='0.7'/%3E%3Ccircle cx='60' cy='60' r='20' stroke='%23d9b94b' fill='none' stroke-width='1.1' opacity='0.55'/%3E%3C/pattern%3E%3Cpattern id='microtext' width='400' height='40' patternUnits='userSpaceOnUse'%3E%3Ctext x='0' y='15' font-size='8' fill='%23f9f7f1' opacity='0.85' font-family='Courier New, monospace' letter-spacing='2px' font-weight='700'%3E• SECURE DOCUMENT • FEDERAL IDENTITY • REPUBLIC OF ETHIOPIA • OFFICIAL USE ONLY •%3C/text%3E%3Ctext x='0' y='32' font-size='8' fill='%23f9f7f1' opacity='0.85' font-family='Courier New, monospace' letter-spacing='2px' font-weight='700'%3E• ID-ETH-2024 • ANTI-COUNTERFEIT • UV-SECURITY • HOLOGRAM •%3C/text%3E%3C/pattern%3E%3Cpattern id='hexgrid' width='50' height='43.3' patternUnits='userSpaceOnUse'%3E%3Cpolygon points='25,0 50,21.65 50,65 25,86.6 0,65 0,21.65' stroke='%23a87f17' fill='none' stroke-width='1' opacity='0.3'/%3E%3Ccircle cx='25' cy='21.65' r='3' fill='%23e9c85b' opacity='0.8'/%3E%3Ccircle cx='25' cy='65' r='3' fill='%23e9c85b' opacity='0.8'/%3E%3C/pattern%3E%3Cpattern id='waveCross' width='300' height='300' patternUnits='userSpaceOnUse'%3E%3Cpath d='M0 150 Q75 0 150 150 T300 150' fill='none' stroke='%239a8c42' stroke-width='1.6' opacity='0.55'/%3E%3Cpath d='M150 0 Q0 75 150 150 T150 300' fill='none' stroke='%23d1a924' stroke-width='1.7' opacity='0.6'/%3E%3Cpath d='M0 75 Q150 225 300 75' fill='none' stroke='%23ffd54c' stroke-width='1.3' opacity='0.5'/%3E%3C/pattern%3E%3Cpattern id='securityDots' width='12' height='12' patternUnits='userSpaceOnUse'%3E%3Ccircle cx='6' cy='6' r='2.4' fill='%23fadb5f' opacity='0.9'/%3E%3Ccircle cx='6' cy='6' r='1.0' fill='%23fffbee' opacity='0.95'/%3E%3C/pattern%3E%3Cpattern id='goldSecurity' width='20' height='20' patternUnits='userSpaceOnUse'%3E%3Crect width='20' height='20' fill='%23b99218' opacity='0.15'/%3E%3Cpath d='M0,0 L20,20 M20,0 L0,20' stroke='%23f7df48' stroke-width='1.6' opacity='0.8'/%3E%3C/pattern%3E%3CradialGradient id='burst' cx='50%25' cy='50%25' r='50%25'%3E%3Cstop offset='0%25' stop-color='%23fffde7' stop-opacity='0.92'/%3E%3Cstop offset='60%25' stop-color='%23fbd13e' stop-opacity='0.45'/%3E%3Cstop offset='90%25' stop-color='%23ab8635' stop-opacity='0.1'/%3E%3Cstop offset='100%25' stop-color='%236f5c26' stop-opacity='0'/%3E%3C/radialGradient%3E%3Cpattern id='holoPattern' width='500' height='500' patternUnits='userSpaceOnUse'%3E%3ClinearGradient id='holoGradient' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' stop-color='%23d9cb8a'/%3E%3Cstop offset='25%25' stop-color='%23eee2a8'/%3E%3Cstop offset='50%25' stop-color='%23f4e285'/%3E%3Cstop offset='75%25' stop-color='%23e6c55c'/%3E%3Cstop offset='100%25' stop-color='%23f9f4d1'/%3E%3C/linearGradient%3E%3Cg stroke='url(%23holoGradient)' stroke-width='2' fill='none' opacity='0.37'%3E%3Cpath d='M0 100 Q125 0 250 100 T500 100'/%3E%3Cpath d='M0 300 Q125 200 250 300 T500 300'/%3E%3Cpath d='M100 0 Q0 125 100 250 T100 500'/%3E%3Cpath d='M300 0 Q200 125 300 250 T300 500'/%3E%3Ccircle cx='250' cy='250' r='200' stroke-dasharray='12,12'/%3E%3C/g%3E%3C/pattern%3E%3Cpattern id='uvPattern' width='80' height='80' patternUnits='userSpaceOnUse'%3E%3Crect width='80' height='80' fill='%23000' opacity='0'/%3E%3Cpath d='M0 40 L80 40 M40 0 L40 80' stroke='%237fffd4' stroke-width='1.3' opacity='0.15'/%3E%3Ctext x='40' y='45' font-size='16' fill='%237fffd4' opacity='0.12' font-family='Arial' text-anchor='middle' font-weight='800'%3EUV%3C/text%3E%3C/pattern%3E%3Cfilter id='emboss'%3E%3CfeGaussianBlur in='SourceAlpha' stdDeviation='1.5' result='blur'/%3E%3CfeSpecularLighting in='blur' surfaceScale='6' specularConstant='0.7' specularExponent='15' lighting-color='%23ffffff' result='spec'%3E%3CfePointLight x='-5000' y='-10000' z='20000'/%3E%3C/feSpecularLighting%3E%3CfeComposite in='spec' in2='SourceGraphic' operator='in' result='specOut'/%3E%3CfeComposite in='SourceGraphic' in2='specOut' operator='arithmetic' k1='0' k2='1' k3='1' k4='0'/%3E%3C/filter%3E%3Cpattern id='borderGuilloche' width='30' height='30' patternUnits='userSpaceOnUse'%3E%3Cpath d='M15,0 C20,7.5 10,22.5 15,30 M0,15 C7.5,20 22.5,10 30,15' stroke='%23a88510' fill='none' stroke-width='1.6' opacity='0.65'/%3E%3C/pattern%3E%3CradialGradient id='sparkleGlow' cx='50%25' cy='50%25' r='50%25'%3E%3Cstop offset='0%25' stop-color='%23fff9cc' stop-opacity='1'/%3E%3Cstop offset='80%25' stop-color='%23fff9cc' stop-opacity='0'/%3E%3C/radialGradient%3E%3Ccircle id='sparkle' cx='0' cy='0' r='6' fill='url(%23sparkleGlow)'/%3E%3C/defs%3E%3Crect width='100%25' height='100%25' fill='url(%23base)'/%3E%3Crect width='100%25' height='120' fill='url(%23headerWhite)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23holoPattern)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23guilloche)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23hexgrid)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23microtext)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23waveCross)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23uvPattern)'/%3E%3Ccircle cx='700' cy='420' r='110' fill='url(%23burst)' filter='url(%23emboss)' opacity='0.9'/%3E%3Cuse href='%23sparkle' x='670' y='370' opacity='0.6'/%3E%3Cuse href='%23sparkle' x='720' y='450' opacity='0.5'/%3E%3Cuse href='%23sparkle' x='685' y='410' opacity='0.45'/%3E%3Cuse href='%23sparkle' x='730' y='390' opacity='0.4'/%3E%3Cpath d='M0 450 C150 350 650 500 800 450' fill='none' stroke='%23a88510' stroke-width='4' opacity='0.45' stroke-dasharray='14 8'/%3E%3Cpath d='M0 100 C200 200 600 0 800 100' fill='none' stroke='%23927a2e' stroke-width='3' opacity='0.55'/%3E%3Crect x='650' y='350' width='150' height='150' fill='url(%23securityDots)' rx='20' ry='20'/%3E%3Cuse href='%23sparkle' x='680' y='380' opacity='0.6'/%3E%3Cuse href='%23sparkle' x='740' y='420' opacity='0.55'/%3E%3Cuse href='%23sparkle' x='720' y='390' opacity='0.5'/%3E%3Cuse href='%23sparkle' x='700' y='430' opacity='0.45'/%3E%3Crect x='5' y='5' width='790' height='490' fill='none' stroke='url(%23borderGuilloche)' stroke-width='7' rx='20' ry='20'/%3E%3Ccircle cx='20' cy='20' r='18' stroke='%23d8b924' fill='none' stroke-width='3' opacity='0.8'/%3E%3Ccircle cx='780' cy='20' r='18' stroke='%23d8b924' fill='none' stroke-width='3' opacity='0.8'/%3E%3Ccircle cx='20' cy='480' r='18' stroke='%23d8b924' fill='none' stroke-width='3' opacity='0.8'/%3E%3Ccircle cx='780' cy='480' r='18' stroke='%23d8b924' fill='none' stroke-width='3' opacity='0.8'/%3E%3C/svg%3E")`,
                        backgroundSize: 'cover',
                        backgroundRepeat: 'no-repeat',
                        backgroundPosition: 'center',
                        zIndex: 1
                    }}
                />





                {/* City Name Microprint Text - Back side with different positions */}
                {microprintPositions.map((position) => (
                    <Typography
                        key={`back-${position.id}`}
                        sx={{
                            position: 'absolute',
                            top: `${(position.top + 20) % 80 + 10}%`, // Offset positions for back side
                            left: `${(position.left + 30) % 80 + 10}%`,
                            fontSize: preview ? '8px' : '6px', // Increased font size for visibility
                            fontWeight: 'bold',
                            color: '#0d47a1', // Darker blue for better visibility
                            opacity: 0.35, // Slightly more transparent on back but still visible
                            transform: `rotate(${position.rotation + 45}deg)`, // Different rotation
                            transformOrigin: 'center',
                            pointerEvents: 'none',
                            zIndex: 3, // Above pattern but below main content
                            fontFamily: 'monospace',
                            letterSpacing: '0.3px',
                            userSelect: 'none',
                            textShadow: '0 0 1px rgba(255, 255, 255, 0.4)', // Add subtle shadow for contrast
                            WebkitTextStroke: '0.2px rgba(13, 71, 161, 0.7)' // Add text stroke for definition
                        }}
                    >
                        {position.text}
                    </Typography>
                ))}

                {/* Pattern Image Overlay - Back side full pattern coverage */}
                {(idCard.subcity_approved_at || currentTenantType === 'subcity' || currentTenantType === 'kebele') && subcityPatternImage && (
                    <Box
                        sx={{
                            position: 'absolute',
                            top: 0, // Cover the entire card
                            left: 0,
                            right: 0,
                            bottom: 0,
                            backgroundImage: `url(${subcityPatternImage})`,
                            backgroundSize: 'cover', // Cover the entire area
                            backgroundRepeat: 'no-repeat',
                            backgroundPosition: 'center',
                            opacity: 0.12, // Slightly lower opacity for back side
                            zIndex: 2, // Above SVG pattern, below content
                            pointerEvents: 'none'
                        }}
                    />
                )}



                {/* Content */}
                <Box sx={{
                    p: preview ? 1.5 : 1,
                    pb: preview ? 3 : 2, // Extra bottom padding for footer space
                    display: 'flex',
                    gap: preview ? 1.5 : 1,
                    position: 'relative',
                    zIndex: 2,
                    alignItems: 'center', // Vertically center align the content
                    minHeight: 'calc(100% - 60px)' // Account for padding and footer
                }}>
                    {/* Left side - Additional info */}
                    <Box sx={{ flex: 1 }}>
                        {/* <Typography
              sx={{
                fontSize: preview ? '12px' : '10px',
                mb: 1,
                fontWeight: 'bold',
                color: '#091057' // Dark blue color
              }}
            >
              ተጨማሪ መረጃ | Additional Information
            </Typography> */}

                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: preview ? 0.6 : 0.4 }}>
                            <Box>
                                <Typography sx={{ fontSize: preview ? '10px' : '8px', color: '#3d320f', fontWeight: 'bold', mb: 0.05 }}>
                                    ስልክ ቁጥር | Phone Number
                                </Typography>
                                <Typography sx={{ fontSize: preview ? '12px' : '10px', fontWeight: 'bold', color: '#000000' }}>
                                    {/* Use same field structure as CitizenDetails page */}
                                    {citizen.phone || citizen.phone_number || 'Not provided'}
                                </Typography>
                            </Box>

                            <Box sx={{ backgroundColor: 'rgba(255, 255, 255, 0.1)', p: 0.5, borderRadius: 1, border: '1px solid rgba(61, 50, 15, 0.2)' }}>
                                <Typography sx={{ fontSize: preview ? '10px' : '8px', color: '#3d320f', fontWeight: 'bold', mb: 0.05 }}>
                                    የአደጋ ጊዜ ተጠሪ | Emergency Contact
                                </Typography>
                                <Typography sx={{ fontSize: preview ? '12px' : '10px', fontWeight: 'bold', color: '#000000' }}>
                                    {(() => {
                                        // Debug emergency contact data
                                        console.log('🔍 Emergency Contact Debug:', {
                                            familyData: idCard.familyData,
                                            emergencyContacts: idCard.familyData?.emergencyContacts,
                                            citizenEmergencyContacts: citizen.emergency_contacts,
                                            citizen: citizen,
                                            allCitizenKeys: Object.keys(citizen)
                                        });

                                        // Try multiple sources for emergency contact
                                        if (idCard.familyData?.emergencyContacts && idCard.familyData.emergencyContacts.length > 0) {
                                            const contact = idCard.familyData.emergencyContacts[0];
                                            const name = `${contact.first_name || ''} ${contact.middle_name || ''} ${contact.last_name || ''}`.trim();
                                            console.log('🔍 Using familyData emergency contact:', name);
                                            return name || 'Emergency Contact Name';
                                        } else if (citizen.emergency_contacts && citizen.emergency_contacts.length > 0) {
                                            const contact = citizen.emergency_contacts[0];
                                            const name = `${contact.first_name || ''} ${contact.middle_name || ''} ${contact.last_name || ''}`.trim();
                                            console.log('🔍 Using citizen emergency contact:', name);
                                            return name || 'Emergency Contact Name';
                                        } else if (citizen.emergency_contact_name) {
                                            console.log('🔍 Using citizen.emergency_contact_name:', citizen.emergency_contact_name);
                                            return citizen.emergency_contact_name;
                                        } else {
                                            console.log('🔍 Using default emergency contact');
                                            return 'Abebe Kebede'; // Default emergency contact for display
                                        }
                                    })()}
                                </Typography>
                                <Typography sx={{ fontSize: preview ? '12px' : '10px', color: '#000000', fontWeight: '500' }}>
                                    {(() => {
                                        // Try multiple sources for emergency contact phone
                                        if (idCard.familyData?.emergencyContacts && idCard.familyData.emergencyContacts.length > 0) {
                                            const phone = idCard.familyData.emergencyContacts[0].phone || '+251911234567';
                                            console.log('🔍 Using familyData emergency phone:', phone);
                                            return phone;
                                        } else if (citizen.emergency_contacts && citizen.emergency_contacts.length > 0) {
                                            const phone = citizen.emergency_contacts[0].phone || '+251911234567';
                                            console.log('🔍 Using citizen emergency phone:', phone);
                                            return phone;
                                        } else if (citizen.emergency_contact_phone) {
                                            console.log('🔍 Using citizen.emergency_contact_phone:', citizen.emergency_contact_phone);
                                            return citizen.emergency_contact_phone;
                                        } else {
                                            console.log('🔍 Using default emergency phone');
                                            return '+251911234567'; // Default emergency contact phone for display
                                        }
                                    })()}
                                </Typography>
                            </Box>

                            <Box>
                                <Typography sx={{ fontSize: preview ? '10px' : '8px', color: '#3d320f', fontWeight: 'bold', mb: 0.05 }}>
                                    የደም አይነት | Blood Type
                                </Typography>
                                <Typography sx={{ fontSize: preview ? '12px' : '10px', fontWeight: 'bold', color: '#000000' }}>
                                    {citizen.blood_type || 'Not specified'}
                                </Typography>
                            </Box>

                            <Box>
                                <Typography sx={{ fontSize: preview ? '10px' : '8px', color: '#3d320f', fontWeight: 'bold', mb: 0.05 }}>
                                    ቀጠና | Ketena
                                </Typography>
                                <Typography sx={{ fontSize: preview ? '12px' : '10px', fontWeight: 'bold', color: '#000000' }}>
                                    {/* Use ketena_name from serializer, fallback to parsing ketena field */}
                                    {citizen.ketena_name ||
                                        (typeof citizen.ketena === 'object' && citizen.ketena?.name
                                            ? citizen.ketena.name
                                            : typeof citizen.ketena === 'string'
                                                ? citizen.ketena
                                                : 'Not specified')}
                                </Typography>
                            </Box>

                            <Box>
                                <Typography sx={{ fontSize: preview ? '10px' : '8px', color: '#3d320f', fontWeight: 'bold', mb: 0.05 }}>
                                    የትውልድ ቦታ | Place of Birth
                                </Typography>
                                <Typography sx={{ fontSize: preview ? '12px' : '10px', fontWeight: 'bold', color: '#000000' }}>
                                    {/* Use subcity as place of birth like CitizenDetails */}
                                    {typeof citizen.subcity === 'object' && citizen.subcity?.name
                                        ? citizen.subcity.name
                                        : typeof citizen.subcity === 'string'
                                            ? citizen.subcity
                                            : 'Gondar'}
                                </Typography>
                            </Box>


                        </Box>


                    </Box>

                    {/* Right side - QR Code */}
                    <Box sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mt: preview ? -2 : -1
                    }}>
                        <Typography
                            sx={{
                                fontSize: preview ? '9px' : '7px',
                                mb: 0.5,
                                fontWeight: 'bold',
                                color: '#000000'
                            }}
                        >
                            ማረጋገጫ ኮድ | Verification Code
                        </Typography>

                        {/* QR Code */}
                        <Box
                            sx={{
                                width: preview ? 85 : 57,
                                height: preview ? 85 : 57,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                backgroundColor: '#ffffff',
                                mb: 0.5,
                                border: '1px solid #ddd'
                            }}
                        >
                            {idCard.uuid || citizen.digital_id || idCard.citizen_digital_id ? (
                                <QRCodeReact
                                    value={(() => {
                                        // Create QR code data with citizen information (NO PHOTO)
                                        const fullName = `${citizen.first_name || ''} ${citizen.middle_name || ''} ${citizen.last_name || ''}`.trim();
                                        const digitalId = idCard.uuid || citizen.digital_id || idCard.citizen_digital_id || 'Unknown';
                                        const issueDateStr = idCard.issue_date || '01/01/2024';
                                        const expiryDateStr = idCard.expiry_date || '01/01/2026';
                                        const kebeleName = citizen.kebele || 'Unknown';
                                        const subcityName = citizen.subcity || 'Unknown';
                                        const dobStr = citizen.date_of_birth || '01/01/1990';
                                        const phoneNumber = citizen.phone || 'N/A';

                                        // Create QR code data string (NO PHOTO - just text data)
                                        const qrData = `${fullName}|${digitalId}|${issueDateStr}|${expiryDateStr}|${kebeleName}|${subcityName}|${dobStr}|${phoneNumber}`;

                                        console.log('🔍 QR Code Data (no photo):', qrData);
                                        return qrData;
                                    })()}
                                    size={preview ? 85 : 57}
                                    level="M"
                                />
                            ) : (
                                <Typography sx={{ fontSize: preview ? '12px' : '8px', textAlign: 'center', color: '#7f8c8d', fontWeight: 'bold' }}>
                                    QR<br />CODE
                                </Typography>
                            )}
                        </Box>

                        <Typography
                            sx={{
                                fontSize: preview ? '6px' : '5px',
                                textAlign: 'center',
                                color: '#7f8c8d'
                            }}
                        >
                            goid.gov.et/verify
                        </Typography>
                    </Box>

                </Box>

                {/* Footer with Lost Card Instructions */}
                <Box
                    sx={{
                        position: 'absolute',
                        bottom: 0,
                        left: 0,
                        right: 0,
                        backgroundColor: 'rgba(248, 249, 250, 0.85)', // Light background for readability
                        borderTop: '1px solid rgba(233, 236, 239, 0.8)',
                        px: preview ? 1.5 : 1,
                        py: preview ? 0.6 : 0.4,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        zIndex: 10, // Higher z-index to ensure visibility over pattern
                        backdropFilter: 'blur(0.5px)' // Minimal blur to maintain pattern visibility
                    }}
                >
                    <Typography
                        sx={{
                            fontSize: preview ? '8px' : '6px',
                            color: '#2c3e50',
                            fontWeight: 'bold',
                            lineHeight: 1.3,
                            textAlign: 'center'
                        }}
                    >
                        ይህንን ካርድ ጠፍቶ ካገኙት ለተቋሙ ወይም በአቅራቢያዎ ወደሚገኝ ፖሊስ ጣቢያ ያስረክቡ።<br />
                        If found, please return to the issuing office or the nearest police station.
                    </Typography>
                </Box>
            </Box>
        );
    }
};

export default IDCardTemplate;
