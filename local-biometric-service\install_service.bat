@echo off
echo ========================================
echo    GoID JAR Bridge Service Installer
echo ========================================
echo.

REM Change to the directory where this batch file is located
cd /d "%~dp0"
echo Current directory: %CD%

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Running as Administrator
) else (
    echo ❌ Administrator privileges required
    echo 💡 Right-click and "Run as Administrator"
    pause
    exit /b 1
)

echo.
echo Installing Python dependencies...
pip install pywin32

echo.
echo Checking requirements...
python "%~dp0install_windows_service.py" check

echo.
echo Installing Windows service...
python "%~dp0install_windows_service.py" install

echo.
echo Starting service...
python "%~dp0install_windows_service.py" start

echo.
echo ========================================
echo    Installation Complete!
echo ========================================
echo.
echo Service Status:
sc query GoIDJarBridge

echo.
echo Test the service:
echo   http://localhost:8001/api/device/status
echo.
echo Service Management:
echo   Start:   python "%~dp0install_windows_service.py" start
echo   Stop:    python "%~dp0install_windows_service.py" stop
echo   Restart: python "%~dp0install_windows_service.py" restart
echo.
pause
