#!/usr/bin/env python3
"""
Test FMatcher with actual fingerprint templates from the last 4 records.
These records appear to contain real binary fingerprint data.
"""

import os
import sys
import django
import base64
import json
import time

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.db import connection
from biometrics.duplicate_detection_service import EnhancedDuplicateDetectionService

def extract_real_fingerprint_data():
    """Extract actual fingerprint template data from the last 4 records."""
    print("🔍 Extracting Real Fingerprint Template Data")
    print("=" * 50)
    
    with connection.cursor() as cursor:
        cursor.execute('''
            SELECT id, citizen_id, left_thumb_fingerprint, right_thumb_fingerprint
            FROM kebele_kebele14.citizens_biometric 
            WHERE left_thumb_fingerprint IS NOT NULL 
            ORDER BY id DESC
            LIMIT 4
        ''')
        records = cursor.fetchall()
        
        fingerprint_data = []
        
        for record in records:
            record_id, citizen_id, left_thumb, right_thumb = record
            print(f"\n📊 Processing Record ID: {record_id}, Citizen: {citizen_id}")
            
            # Process left thumb
            left_template = None
            if left_thumb:
                try:
                    # Parse the JSON structure first
                    if left_thumb.startswith('{"version"'):
                        # Find the template data within the JSON
                        json_data = json.loads(left_thumb)
                        print(f"  📋 Left thumb JSON keys: {list(json_data.keys())}")
                        
                        # Look for template data fields
                        template_field = None
                        for field in ['ansi_iso_template', 'template_data', 'fingerprint_template', 'raw_template']:
                            if field in json_data:
                                template_field = field
                                break
                        
                        if template_field:
                            template_data = json_data[template_field]
                            if isinstance(template_data, str):
                                # Decode base64 template
                                left_template = base64.b64decode(template_data)
                                print(f"  ✅ Left thumb template extracted: {len(left_template)} bytes")
                            else:
                                print(f"  ❌ Template data is not string: {type(template_data)}")
                        else:
                            print(f"  ❌ No template field found in JSON")
                            # Try to extract binary data directly
                            if 'capture_method' in json_data and json_data['capture_method'] == 'java_bridge':
                                # This might contain embedded binary data
                                json_str = json.dumps(json_data)
                                # Look for base64 patterns in the JSON
                                import re
                                base64_pattern = r'"([A-Za-z0-9+/]{100,}={0,2})"'
                                matches = re.findall(base64_pattern, json_str)
                                if matches:
                                    # Try the longest match as it's likely the template
                                    longest_match = max(matches, key=len)
                                    try:
                                        left_template = base64.b64decode(longest_match)
                                        print(f"  ✅ Left thumb template found in JSON: {len(left_template)} bytes")
                                    except:
                                        print(f"  ❌ Could not decode found base64 data")
                    else:
                        # Try direct base64 decode
                        left_template = base64.b64decode(left_thumb)
                        print(f"  ✅ Left thumb direct decode: {len(left_template)} bytes")
                        
                except Exception as e:
                    print(f"  ❌ Error processing left thumb: {e}")
            
            # Process right thumb (similar logic)
            right_template = None
            if right_thumb:
                try:
                    if right_thumb.startswith('{"version"'):
                        json_data = json.loads(right_thumb)
                        print(f"  📋 Right thumb JSON keys: {list(json_data.keys())}")
                        
                        # Look for template data fields
                        template_field = None
                        for field in ['ansi_iso_template', 'template_data', 'fingerprint_template', 'raw_template']:
                            if field in json_data:
                                template_field = field
                                break
                        
                        if template_field:
                            template_data = json_data[template_field]
                            if isinstance(template_data, str):
                                right_template = base64.b64decode(template_data)
                                print(f"  ✅ Right thumb template extracted: {len(right_template)} bytes")
                        else:
                            # Try to find base64 data in JSON
                            json_str = json.dumps(json_data)
                            import re
                            base64_pattern = r'"([A-Za-z0-9+/]{100,}={0,2})"'
                            matches = re.findall(base64_pattern, json_str)
                            if matches:
                                longest_match = max(matches, key=len)
                                try:
                                    right_template = base64.b64decode(longest_match)
                                    print(f"  ✅ Right thumb template found in JSON: {len(right_template)} bytes")
                                except:
                                    print(f"  ❌ Could not decode found base64 data")
                    else:
                        right_template = base64.b64decode(right_thumb)
                        print(f"  ✅ Right thumb direct decode: {len(right_template)} bytes")
                        
                except Exception as e:
                    print(f"  ❌ Error processing right thumb: {e}")
            
            fingerprint_data.append({
                'record_id': record_id,
                'citizen_id': citizen_id,
                'left_template': left_template,
                'right_template': right_template
            })
        
        return fingerprint_data

def test_fmatcher_with_real_templates(fingerprint_data):
    """Test FMatcher with real fingerprint templates."""
    print("\n🧪 Testing FMatcher with Real Fingerprint Templates")
    print("=" * 55)
    
    detection_service = EnhancedDuplicateDetectionService()
    
    print(f"✅ FMatcher available: {detection_service.fmatcher_available}")
    print(f"📁 FMatcher path: {detection_service.fmatcher_jar_path}")
    
    # Filter records that have valid templates
    valid_templates = []
    for data in fingerprint_data:
        if data['left_template'] and len(data['left_template']) > 100:
            valid_templates.append(('left', data['record_id'], data['citizen_id'], data['left_template']))
        if data['right_template'] and len(data['right_template']) > 100:
            valid_templates.append(('right', data['record_id'], data['citizen_id'], data['right_template']))
    
    print(f"\n📊 Found {len(valid_templates)} valid templates for testing")
    
    if len(valid_templates) < 2:
        print("❌ Need at least 2 valid templates for comparison testing")
        return
    
    # Test comparisons between different templates
    comparison_count = 0
    successful_comparisons = 0
    
    print(f"\n🔍 Running Real Template Comparisons:")
    
    for i in range(len(valid_templates)):
        for j in range(i + 1, min(i + 3, len(valid_templates))):  # Limit to avoid too many tests
            template1_info = valid_templates[i]
            template2_info = valid_templates[j]
            
            thumb1, record1, citizen1, template1 = template1_info
            thumb2, record2, citizen2, template2 = template2_info
            
            comparison_count += 1
            
            print(f"\n  🧪 Test {comparison_count}: Citizen {citizen1} ({thumb1}) vs Citizen {citizen2} ({thumb2})")
            print(f"    Template sizes: {len(template1)} vs {len(template2)} bytes")
            
            try:
                start_time = time.time()
                score = detection_service._compare_with_fmatcher(template1, template2)
                comparison_time = time.time() - start_time
                
                if score is not None:
                    successful_comparisons += 1
                    confidence, quality, risk_level = detection_service._assess_match_quality(score)
                    
                    print(f"    ✅ Score: {score}")
                    print(f"    ✅ Confidence: {confidence:.1%}")
                    print(f"    ✅ Quality: {quality}")
                    print(f"    ✅ Risk Level: {risk_level}")
                    print(f"    ✅ Comparison Time: {comparison_time:.3f}s")
                    
                    # Interpret results
                    if score >= detection_service.very_high_confidence_threshold:
                        print(f"    🚨 CRITICAL MATCH DETECTED - Potential duplicate citizen!")
                        if citizen1 == citizen2:
                            print(f"    ✅ Same citizen - This is expected for same person's fingerprints")
                        else:
                            print(f"    ⚠️  Different citizens - FRAUD ALERT!")
                    elif score >= detection_service.high_confidence_threshold:
                        print(f"    ⚠️  HIGH CONFIDENCE MATCH - Manual review required")
                    elif score >= detection_service.match_threshold:
                        print(f"    📋 MEDIUM CONFIDENCE MATCH - Flag for review")
                    else:
                        print(f"    ✅ LOW MATCH SCORE - No duplicate detected")
                        
                else:
                    print(f"    ❌ FMatcher comparison failed")
                    
            except Exception as e:
                print(f"    ❌ Error: {e}")
                import traceback
                traceback.print_exc()
    
    # Show final statistics
    print(f"\n📊 Real Template Testing Results:")
    print(f"  🎯 Total Comparisons: {comparison_count}")
    print(f"  ✅ Successful Comparisons: {successful_comparisons}")
    print(f"  📈 Success Rate: {(successful_comparisons/comparison_count*100):.1f}%" if comparison_count > 0 else "N/A")
    
    # Show enhanced statistics
    stats = detection_service.get_enhanced_statistics()
    print(f"\n📊 Enhanced FMatcher Statistics:")
    print(f"  🎯 Total Calls: {stats['performance_metrics']['total_calls']}")
    print(f"  ✅ Successful Calls: {stats['performance_metrics']['successful_calls']}")
    print(f"  ❌ Failed Calls: {stats['performance_metrics']['failed_calls']}")
    print(f"  📈 Success Rate: {stats['fmatcher_status']['success_rate']}")
    print(f"  ⏱️  Average Time: {stats['performance_metrics']['average_comparison_time']}s")

def main():
    """Run real fingerprint template testing."""
    print("🎯 Real Fingerprint Template Testing")
    print("=" * 40)
    print("Testing FMatcher with actual fingerprint templates from database")
    print()
    
    try:
        # Extract real fingerprint data
        fingerprint_data = extract_real_fingerprint_data()
        
        # Test FMatcher with real templates
        test_fmatcher_with_real_templates(fingerprint_data)
        
        print("\n🎉 Real Template Testing Complete!")
        print("=" * 40)
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
