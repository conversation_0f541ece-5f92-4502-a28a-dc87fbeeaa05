import { Box, Typography } from '@mui/material';
import { CreditCard as CardIcon } from '@mui/icons-material';

const Logo = ({ height = 40, showText = true }) => {
  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      <CardIcon 
        sx={{ 
          fontSize: height, 
          color: 'primary.main',
          mr: showText ? 1 : 0
        }} 
      />
      {showText && (
        <Typography 
          variant="h4" 
          component="span" 
          sx={{ 
            fontWeight: 'bold',
            fontSize: height * 0.6,
            color: 'primary.main'
          }}
        >
          GoID
        </Typography>
      )}
    </Box>
  );
};

export default Logo;
