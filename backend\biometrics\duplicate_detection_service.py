"""
Enhanced Duplicate Detection Service for Fingerprint Biometrics

This service provides comprehensive duplicate detection across all tenants
with improved performance, accuracy, and fraud prevention capabilities.
"""

import json
import base64
import logging
import os
import subprocess
import tempfile
import hashlib
import time
from typing import Dict, List, Tuple, Optional, Set
from datetime import datetime, timedelta
from django.db import connection
from django_tenants.utils import schema_context, get_tenant_model
from django.core.cache import cache
from django.conf import settings
from .performance_monitor import performance_monitor

logger = logging.getLogger(__name__)


class EnhancedDuplicateDetectionService:
    """
    Enhanced service for detecting duplicate fingerprints across all tenants.
    
    Features:
    - Cross-tenant duplicate detection
    - Performance optimization with caching
    - Batch processing capabilities
    - Fraud prevention integration
    - Real-time duplicate checking
    """
    
    def __init__(self):
        # Enhanced FMatcher thresholds for better accuracy
        self.match_threshold = 35  # Lowered for better sensitivity
        self.high_confidence_threshold = 60  # High confidence matches
        self.very_high_confidence_threshold = 100  # Very high confidence matches

        # Performance optimization settings
        self.cache_timeout = 7200  # 2 hour cache timeout (increased)
        self.batch_size = 50  # Reduced for better memory management
        self.max_concurrent_comparisons = 10  # Limit concurrent FMatcher processes

        # FMatcher configuration with validation
        self.fmatcher_jar_path = os.path.join(
            os.path.dirname(__file__), '..', '..', 'fpmatcher', 'FMatcher.jar'
        )
        self.fmatcher_dir = os.path.join(
            os.path.dirname(__file__), '..', '..', 'fpmatcher'
        )

        # Validate FMatcher availability at startup
        self.fmatcher_available = self._validate_fmatcher_setup()

        # Enhanced performance tracking
        self.stats = {
            'total_checks': 0,
            'duplicates_found': 0,
            'cache_hits': 0,
            'processing_time': 0,
            'fmatcher_calls': 0,
            'fmatcher_successes': 0,
            'fmatcher_failures': 0,
            'average_comparison_time': 0,
            'template_extraction_failures': 0
        }

    def _validate_fmatcher_setup(self) -> bool:
        """
        Validate that FMatcher.jar is available and working properly.

        Returns:
            bool: True if FMatcher is available and functional
        """
        try:
            # Check if JAR file exists
            if not os.path.exists(self.fmatcher_jar_path):
                logger.warning(f"FMatcher.jar not found at {self.fmatcher_jar_path}")
                return False

            # Check if Java is available
            java_check = subprocess.run(
                ['java', '-version'],
                capture_output=True,
                text=True,
                timeout=5
            )

            if java_check.returncode != 0:
                logger.warning("Java runtime not available for FMatcher")
                return False

            # Test FMatcher with sample templates if they exist
            template1_path = os.path.join(self.fmatcher_dir, 'template1.iso')
            template2_path = os.path.join(self.fmatcher_dir, 'template2.iso')

            if os.path.exists(template1_path) and os.path.exists(template2_path):
                test_result = subprocess.run(
                    ['java', '-jar', os.path.basename(self.fmatcher_jar_path),
                     'template1.iso', 'template2.iso'],
                    cwd=self.fmatcher_dir,
                    capture_output=True,
                    text=True,
                    timeout=10
                )

                if test_result.returncode == 0:
                    try:
                        score = float(test_result.stdout.strip())
                        logger.info(f"FMatcher validation successful. Test score: {score:.2f}")
                        return True
                    except ValueError:
                        logger.warning("FMatcher returned invalid score format")
                        return False
                else:
                    logger.warning(f"FMatcher test failed: {test_result.stderr}")
                    return False
            else:
                logger.info("FMatcher JAR and Java available, but no test templates found")
                return True

        except Exception as e:
            logger.warning(f"FMatcher validation failed: {e}")
            return False

    def check_duplicate_registration(self, fingerprint_data: Dict, exclude_citizen_id: Optional[int] = None) -> Dict:
        """
        Check for duplicate fingerprints during citizen registration.

        Args:
            fingerprint_data (Dict): Dictionary containing fingerprint templates
            exclude_citizen_id (Optional[int]): Citizen ID to exclude from check (for updates)

        Returns:
            Dict: Comprehensive duplicate check results
        """
        start_time = datetime.now()
        self.stats['total_checks'] += 1

        try:
            with performance_monitor.measure_operation('duplicate_check_full'):
                logger.info("🔍 Starting enhanced duplicate detection check")

                left_thumb = fingerprint_data.get('left_thumb_fingerprint')
                right_thumb = fingerprint_data.get('right_thumb_fingerprint')

                if not left_thumb and not right_thumb:
                    return self._create_result(False, [], "No fingerprint data provided")

                # Get all stored templates with caching
                with performance_monitor.measure_operation('get_cached_templates'):
                    stored_templates = self._get_cached_templates()

                if exclude_citizen_id:
                    # Filter out the citizen being updated
                    stored_templates = [
                        t for t in stored_templates
                        if t['citizen_id'] != exclude_citizen_id
                    ]

                logger.info(f"🔍 Checking against {len(stored_templates)} stored templates")

                # Check for duplicates
                matches = []

                if left_thumb:
                    with performance_monitor.measure_operation('check_left_thumb'):
                        left_matches = self._check_template_batch(left_thumb, stored_templates, 'left')
                        matches.extend(left_matches)

                if right_thumb:
                    with performance_monitor.measure_operation('check_right_thumb'):
                        right_matches = self._check_template_batch(right_thumb, stored_templates, 'right')
                        matches.extend(right_matches)

                # Remove duplicates and sort by score
                unique_matches = self._deduplicate_matches(matches)

                # Calculate processing time
                processing_time = (datetime.now() - start_time).total_seconds()
                self.stats['processing_time'] += processing_time

                # Record performance metrics
                has_duplicates = len(unique_matches) > 0
                performance_monitor.record_duplicate_check(has_duplicates, processing_time)

                if unique_matches:
                    self.stats['duplicates_found'] += 1
                    logger.warning(f"🚨 DUPLICATE DETECTION: Found {len(unique_matches)} matches")

                return self._create_result(
                    has_duplicates,
                    unique_matches,
                    f"Checked {len(stored_templates)} templates in {processing_time:.2f}s"
                )

        except Exception as e:
            logger.error(f"Error in duplicate detection: {e}")
            return self._create_result(False, [], f"Error: {str(e)}")
    
    def _get_cached_templates(self) -> List[Dict]:
        """Get all stored templates with caching for performance"""
        cache_key = "biometric_templates_all"
        
        # Try to get from cache first
        cached_templates = cache.get(cache_key)
        if cached_templates:
            self.stats['cache_hits'] += 1
            logger.info(f"📋 Using cached templates: {len(cached_templates)} templates")
            return cached_templates
        
        # Get fresh data from database
        templates = self._get_all_stored_templates()
        
        # Cache the results
        cache.set(cache_key, templates, self.cache_timeout)
        logger.info(f"📋 Cached {len(templates)} templates for {self.cache_timeout}s")
        
        return templates
    
    def _get_all_stored_templates(self) -> List[Dict]:
        """Get all fingerprint templates from all tenants"""
        stored_templates = []
        
        try:
            Tenant = get_tenant_model()
            tenants = Tenant.objects.filter(type='kebele')
            
            for tenant in tenants:
                try:
                    with schema_context(tenant.schema_name):
                        from citizens.models import Biometric
                        
                        biometric_records = Biometric.objects.select_related('citizen').all()
                        
                        for record in biometric_records:
                            # Add left thumb template
                            if record.left_thumb_fingerprint:
                                template_hash = self._get_template_hash(record.left_thumb_fingerprint)
                                stored_templates.append({
                                    'template_data': record.left_thumb_fingerprint,
                                    'template_hash': template_hash,
                                    'thumb_type': 'left',
                                    'tenant_id': tenant.id,
                                    'tenant_name': tenant.name,
                                    'citizen_id': record.citizen.id,
                                    'citizen_name': f"{record.citizen.first_name} {record.citizen.last_name}",
                                    'citizen_digital_id': record.citizen.digital_id,
                                    'biometric_id': record.id
                                })
                            
                            # Add right thumb template
                            if record.right_thumb_fingerprint:
                                template_hash = self._get_template_hash(record.right_thumb_fingerprint)
                                stored_templates.append({
                                    'template_data': record.right_thumb_fingerprint,
                                    'template_hash': template_hash,
                                    'thumb_type': 'right',
                                    'tenant_id': tenant.id,
                                    'tenant_name': tenant.name,
                                    'citizen_id': record.citizen.id,
                                    'citizen_name': f"{record.citizen.first_name} {record.citizen.last_name}",
                                    'citizen_digital_id': record.citizen.digital_id,
                                    'biometric_id': record.id
                                })
                        
                        logger.info(f"📊 Loaded {len([t for t in stored_templates if t['tenant_name'] == tenant.name])} templates from {tenant.name}")
                        
                except Exception as e:
                    logger.error(f"Error loading templates from {tenant.name}: {e}")
                    continue
            
            return stored_templates
            
        except Exception as e:
            logger.error(f"Error getting stored templates: {e}")
            return []
    
    def _get_template_hash(self, template_data: str) -> str:
        """Generate a hash for quick template comparison"""
        return hashlib.md5(template_data.encode()).hexdigest()
    
    def _check_template_batch(self, new_template: str, stored_templates: List[Dict], thumb_type: str) -> List[Dict]:
        """Check one template against stored templates in batches"""
        matches = []
        new_template_hash = self._get_template_hash(new_template)
        
        # First, check for exact hash matches (very fast)
        exact_matches = [
            t for t in stored_templates 
            if t['template_hash'] == new_template_hash
        ]
        
        if exact_matches:
            logger.warning(f"🚨 EXACT TEMPLATE MATCH found for {thumb_type} thumb!")
            for match in exact_matches:
                matches.append({
                    'tenant_id': match['tenant_id'],
                    'tenant_name': match['tenant_name'],
                    'citizen_id': match['citizen_id'],
                    'citizen_name': match['citizen_name'],
                    'citizen_digital_id': match['citizen_digital_id'],
                    'biometric_id': match['biometric_id'],
                    'match_score': 1000.0,  # Perfect match
                    'confidence': 1.0,
                    'match_quality': 'Perfect',
                    'matched_thumb': match['thumb_type'],
                    'new_thumb': thumb_type,
                    'match_details': ['Exact template match (identical data)']
                })
        
        # Then check remaining templates with FMatcher
        remaining_templates = [
            t for t in stored_templates 
            if t['template_hash'] != new_template_hash
        ]
        
        # Process in batches for better performance
        for i in range(0, len(remaining_templates), self.batch_size):
            batch = remaining_templates[i:i + self.batch_size]
            batch_matches = self._process_template_batch(new_template, batch, thumb_type)
            matches.extend(batch_matches)
        
        return matches
    
    def _process_template_batch(self, new_template: str, template_batch: List[Dict], thumb_type: str) -> List[Dict]:
        """Process a batch of templates for comparison"""
        matches = []
        
        try:
            # Extract ANSI template from new template
            new_ansi_bytes = self._extract_ansi_template(new_template)
            if not new_ansi_bytes:
                return matches
            
            for stored in template_batch:
                try:
                    stored_ansi_bytes = self._extract_ansi_template(stored['template_data'])
                    if not stored_ansi_bytes:
                        continue
                    
                    # Use FMatcher for comparison
                    score = self._compare_with_fmatcher(new_ansi_bytes, stored_ansi_bytes)
                    
                    if score is not None and score > self.match_threshold:
                        # Enhanced confidence and quality assessment
                        confidence, quality, risk_level = self._assess_match_quality(score)

                        match_details = [
                            f"FMatcher score: {score:.1f}",
                            f"Confidence: {confidence:.1%}",
                            f"Quality: {quality}",
                            f"Risk: {risk_level}"
                        ]

                        matches.append({
                            'tenant_id': stored['tenant_id'],
                            'tenant_name': stored['tenant_name'],
                            'citizen_id': stored['citizen_id'],
                            'citizen_name': stored['citizen_name'],
                            'citizen_digital_id': stored['citizen_digital_id'],
                            'biometric_id': stored['biometric_id'],
                            'match_score': score,
                            'confidence': confidence,
                            'match_quality': quality,
                            'risk_level': risk_level,
                            'matched_thumb': stored['thumb_type'],
                            'new_thumb': thumb_type,
                            'match_details': match_details,
                            'requires_manual_review': score < self.high_confidence_threshold,
                            'automatic_block': score >= self.very_high_confidence_threshold
                        })

                        # Enhanced logging with risk assessment
                        risk_emoji = "🔴" if risk_level == "CRITICAL" else "🟡" if risk_level == "HIGH" else "🟠"
                        logger.warning(f"{risk_emoji} DUPLICATE DETECTED: {stored['citizen_name']} in {stored['tenant_name']} "
                                     f"(Score: {score:.1f}, Confidence: {confidence:.1%}, Risk: {risk_level})")
                
                except Exception as template_error:
                    logger.debug(f"Error comparing template: {template_error}")
                    continue
        
        except Exception as e:
            logger.error(f"Error processing template batch: {e}")
        
        return matches

    def _extract_ansi_template(self, template_data: str) -> Optional[bytes]:
        """Extract ANSI/ISO template bytes from template data"""
        try:
            # Try to decode as JSON first
            try:
                template_json = json.loads(base64.b64decode(template_data).decode())
                ansi_template = template_json.get('ansi_iso_template')
                if ansi_template:
                    return base64.b64decode(ansi_template)
            except Exception:
                # Try to use template data directly
                return base64.b64decode(template_data)
        except Exception as e:
            logger.debug(f"Could not extract ANSI template: {e}")
            return None

    def _compare_with_fmatcher(self, template1_bytes: bytes, template2_bytes: bytes) -> Optional[float]:
        """
        Enhanced FMatcher comparison with improved performance and error handling.

        Args:
            template1_bytes: First fingerprint template in ANSI/ISO format
            template2_bytes: Second fingerprint template in ANSI/ISO format

        Returns:
            Optional[float]: Match score or None if comparison failed
        """
        start_time = time.time()
        self.stats['fmatcher_calls'] += 1

        try:
            # Pre-flight checks
            if not self.fmatcher_available:
                logger.debug("FMatcher not available, skipping comparison")
                self.stats['fmatcher_failures'] += 1
                return None

            # Enhanced template validation
            if not self._validate_template_data(template1_bytes, template2_bytes):
                self.stats['template_extraction_failures'] += 1
                return None

            # Quick identical template check (performance optimization)
            if template1_bytes == template2_bytes:
                processing_time = time.time() - start_time
                self.stats['fmatcher_successes'] += 1
                self._update_average_time(processing_time)
                logger.debug("Identical templates detected, returning perfect match")
                return 1000.0  # Perfect match score

            # Create secure temporary files with better naming
            temp1_path = None
            temp2_path = None

            try:
                with tempfile.NamedTemporaryFile(
                    suffix='.iso',
                    prefix='fmatcher_t1_',
                    delete=False,
                    dir=self.fmatcher_dir
                ) as temp1:
                    temp1.write(template1_bytes)
                    temp1_path = temp1.name

                with tempfile.NamedTemporaryFile(
                    suffix='.iso',
                    prefix='fmatcher_t2_',
                    delete=False,
                    dir=self.fmatcher_dir
                ) as temp2:
                    temp2.write(template2_bytes)
                    temp2_path = temp2.name

                # Enhanced FMatcher execution with better error handling
                score = self._execute_fmatcher(temp1_path, temp2_path)

                processing_time = time.time() - start_time

                if score is not None:
                    self.stats['fmatcher_successes'] += 1
                    self._update_average_time(processing_time)
                    logger.debug(f"FMatcher comparison successful: {score:.2f} (took {processing_time:.3f}s)")
                    return score
                else:
                    self.stats['fmatcher_failures'] += 1
                    return None

            finally:
                # Secure cleanup
                self._cleanup_temp_files(temp1_path, temp2_path)

        except Exception as e:
            processing_time = time.time() - start_time
            self.stats['fmatcher_failures'] += 1
            logger.warning(f"FMatcher comparison error: {e}")
            return None

    def _validate_template_data(self, template1_bytes: bytes, template2_bytes: bytes) -> bool:
        """
        Validate template data before FMatcher comparison.

        Args:
            template1_bytes: First template data
            template2_bytes: Second template data

        Returns:
            bool: True if templates are valid for comparison
        """
        # Check if templates exist and have minimum size
        if not template1_bytes or not template2_bytes:
            logger.debug("One or both templates are empty")
            return False

        # ANSI/ISO templates should be at least 20 bytes
        if len(template1_bytes) < 20 or len(template2_bytes) < 20:
            logger.debug(f"Templates too small: {len(template1_bytes)}, {len(template2_bytes)} bytes")
            return False

        # Check for valid ANSI/ISO header (basic validation)
        try:
            # ANSI templates typically start with specific byte patterns
            # This is a basic check - more sophisticated validation could be added
            if template1_bytes[:4] == b'\x00\x00\x00\x00' and template2_bytes[:4] == b'\x00\x00\x00\x00':
                logger.debug("Templates appear to be placeholder data")
                return False

        except Exception as e:
            logger.debug(f"Template validation error: {e}")
            return False

        return True

    def _execute_fmatcher(self, temp1_path: str, temp2_path: str) -> Optional[float]:
        """
        Execute FMatcher with enhanced error handling and timeout management.

        Args:
            temp1_path: Path to first template file
            temp2_path: Path to second template file

        Returns:
            Optional[float]: Match score or None if execution failed
        """
        try:
            # Use relative paths for better cross-platform compatibility
            temp1_name = os.path.basename(temp1_path)
            temp2_name = os.path.basename(temp2_path)

            # Enhanced subprocess execution with better timeout handling
            result = subprocess.run(
                ['java', '-jar', os.path.basename(self.fmatcher_jar_path), temp1_name, temp2_name],
                cwd=self.fmatcher_dir,
                capture_output=True,
                text=True,
                timeout=20,  # Increased timeout for complex templates
                env=dict(os.environ, JAVA_OPTS='-Xmx256m')  # Limit memory usage
            )

            if result.returncode == 0:
                try:
                    score_str = result.stdout.strip()
                    score = float(score_str)

                    # Validate score range (FMatcher typically returns 0-1000+)
                    if 0 <= score <= 2000:  # Extended range for very high matches
                        return score
                    else:
                        logger.warning(f"FMatcher returned out-of-range score: {score}")
                        return None

                except ValueError as e:
                    logger.warning(f"FMatcher returned invalid score format: '{result.stdout}' - {e}")
                    return None
            else:
                # Log detailed error information for debugging
                stderr_msg = result.stderr.strip() if result.stderr else "No error message"
                logger.debug(f"FMatcher execution failed (code {result.returncode}): {stderr_msg}")
                return None

        except subprocess.TimeoutExpired:
            logger.warning("FMatcher comparison timed out")
            return None
        except Exception as e:
            logger.warning(f"FMatcher execution error: {e}")
            return None

    def _cleanup_temp_files(self, *file_paths):
        """
        Safely clean up temporary files.

        Args:
            *file_paths: Variable number of file paths to clean up
        """
        for file_path in file_paths:
            if file_path and os.path.exists(file_path):
                try:
                    os.unlink(file_path)
                    logger.debug(f"Cleaned up temp file: {os.path.basename(file_path)}")
                except Exception as e:
                    logger.debug(f"Failed to clean up {file_path}: {e}")

    def _update_average_time(self, processing_time: float):
        """
        Update the average comparison time statistic.

        Args:
            processing_time: Time taken for the comparison
        """
        if self.stats['fmatcher_successes'] > 0:
            current_avg = self.stats['average_comparison_time']
            new_avg = ((current_avg * (self.stats['fmatcher_successes'] - 1)) + processing_time) / self.stats['fmatcher_successes']
            self.stats['average_comparison_time'] = new_avg
        else:
            self.stats['average_comparison_time'] = processing_time

    def _assess_match_quality(self, score: float) -> Tuple[float, str, str]:
        """
        Assess the quality and risk level of a fingerprint match.

        Args:
            score: FMatcher comparison score

        Returns:
            Tuple[float, str, str]: (confidence, quality, risk_level)
        """
        # Enhanced confidence calculation based on FMatcher score ranges
        if score >= self.very_high_confidence_threshold:  # 100+
            confidence = min(0.99, 0.85 + (score - self.very_high_confidence_threshold) / 1000)
            quality = "EXCELLENT"
            risk_level = "CRITICAL"
        elif score >= self.high_confidence_threshold:  # 60-99
            confidence = 0.70 + (score - self.high_confidence_threshold) / (self.very_high_confidence_threshold - self.high_confidence_threshold) * 0.15
            quality = "HIGH"
            risk_level = "HIGH"
        elif score >= self.match_threshold:  # 35-59
            confidence = 0.50 + (score - self.match_threshold) / (self.high_confidence_threshold - self.match_threshold) * 0.20
            quality = "MEDIUM"
            risk_level = "MEDIUM"
        else:  # Below threshold
            confidence = min(0.49, score / self.match_threshold * 0.49)
            quality = "LOW"
            risk_level = "LOW"

        return confidence, quality, risk_level

    def get_enhanced_statistics(self) -> Dict:
        """
        Get enhanced statistics about FMatcher performance and accuracy.

        Returns:
            Dict: Comprehensive statistics
        """
        total_calls = self.stats['fmatcher_calls']
        success_rate = (self.stats['fmatcher_successes'] / total_calls * 100) if total_calls > 0 else 0

        return {
            'fmatcher_status': {
                'available': self.fmatcher_available,
                'jar_path': self.fmatcher_jar_path,
                'success_rate': f"{success_rate:.1f}%"
            },
            'performance_metrics': {
                'total_calls': total_calls,
                'successful_calls': self.stats['fmatcher_successes'],
                'failed_calls': self.stats['fmatcher_failures'],
                'template_extraction_failures': self.stats['template_extraction_failures'],
                'average_comparison_time': f"{self.stats['average_comparison_time']:.3f}s"
            },
            'thresholds': {
                'match_threshold': self.match_threshold,
                'high_confidence_threshold': self.high_confidence_threshold,
                'very_high_confidence_threshold': self.very_high_confidence_threshold
            },
            'cache_performance': {
                'cache_hits': self.stats['cache_hits'],
                'cache_timeout': f"{self.cache_timeout}s"
            }
        }

    def _deduplicate_matches(self, matches: List[Dict]) -> List[Dict]:
        """Remove duplicate matches and sort by score"""
        unique_matches = []
        seen_citizens = set()

        # Sort by score (highest first)
        sorted_matches = sorted(matches, key=lambda x: x['match_score'], reverse=True)

        for match in sorted_matches:
            citizen_key = f"{match['tenant_id']}_{match['citizen_id']}"
            if citizen_key not in seen_citizens:
                unique_matches.append(match)
                seen_citizens.add(citizen_key)

        return unique_matches

    def _create_result(self, has_duplicates: bool, matches: List[Dict], message: str) -> Dict:
        """Create standardized result dictionary"""
        return {
            'has_duplicates': has_duplicates,
            'matches': matches,
            'total_matches': len(matches),
            'message': message,
            'check_time': datetime.now().isoformat(),
            'matcher': 'Enhanced FMatcher Service',
            'stats': self.stats.copy()
        }

    def clear_cache(self):
        """Clear the template cache"""
        cache.delete("biometric_templates_all")
        logger.info("🗑️ Template cache cleared")

    def get_statistics(self) -> Dict:
        """Get service statistics"""
        return {
            'service_stats': self.stats.copy(),
            'cache_info': {
                'timeout': self.cache_timeout,
                'batch_size': self.batch_size
            },
            'configuration': {
                'match_threshold': self.match_threshold,
                'fmatcher_available': os.path.exists(self.fmatcher_jar_path)
            }
        }

    def bulk_duplicate_check(self, tenant_schema: str = None) -> Dict:
        """
        Perform bulk duplicate detection across all tenants or within a specific tenant.
        Useful for auditing and cleanup operations.
        """
        try:
            logger.info("🔍 Starting bulk duplicate detection")

            all_templates = self._get_all_stored_templates()
            duplicates_found = []

            # Group templates by hash for quick exact match detection
            hash_groups = {}
            for template in all_templates:
                template_hash = template['template_hash']
                if template_hash not in hash_groups:
                    hash_groups[template_hash] = []
                hash_groups[template_hash].append(template)

            # Find exact duplicates
            for template_hash, templates in hash_groups.items():
                if len(templates) > 1:
                    # Found exact duplicates
                    for i, template in enumerate(templates):
                        for j, other_template in enumerate(templates[i+1:], i+1):
                            duplicates_found.append({
                                'type': 'exact_duplicate',
                                'template1': template,
                                'template2': other_template,
                                'match_score': 1000.0,
                                'confidence': 1.0
                            })

            # TODO: Add fuzzy duplicate detection for bulk operations
            # This would require more sophisticated batching and optimization

            return {
                'total_templates_checked': len(all_templates),
                'exact_duplicates_found': len(duplicates_found),
                'duplicates': duplicates_found,
                'check_time': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error in bulk duplicate check: {e}")
            return {
                'error': str(e),
                'check_time': datetime.now().isoformat()
            }
