# Generated by Django 4.2.7 on 2025-06-12 09:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('citizens', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='citizen',
            name='photo_processing_date',
            field=models.DateTimeField(blank=True, help_text='When the photo was last processed', null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='photo_processing_metadata',
            field=models.JSONField(blank=True, default=dict, help_text='Metadata about photo processing (background_removed, enhanced, style, etc.)'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='processed_photo',
            field=models.TextField(blank=True, help_text='Base64 encoded processed photo with background removed', null=True),
        ),
    ]
