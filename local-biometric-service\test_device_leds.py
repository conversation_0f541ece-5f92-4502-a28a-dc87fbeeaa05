#!/usr/bin/env python3
"""
Test script to help verify Futronic FS88H LED behavior
"""

import time
import ctypes
import logging
from ctypes import c_void_p, c_uint, c_int, byref, POINTER

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_device_leds():
    """Test device LED behavior"""
    try:
        logger.info("=== Futronic FS88H LED Test ===")
        logger.info("")
        
        # Load SDK
        scan_api = ctypes.cdll.LoadLibrary('./ftrScanAPI.dll')
        scan_api.ftrScanOpenDevice.restype = c_void_p
        scan_api.ftrScanCloseDevice.argtypes = [c_void_p]
        
        # Open device
        device_handle = scan_api.ftrScanOpenDevice()
        
        if not device_handle:
            logger.error("Device not found!")
            return
        
        logger.info(f"Device connected! Handle: {device_handle}")
        logger.info("")
        logger.info("LED BEHAVIOR TEST:")
        logger.info("1. Look at your Futronic FS88H device")
        logger.info("2. You should see a RED LED (device ready)")
        logger.info("")
        
        input("Press Enter when you can see the RED LED...")
        
        logger.info("")
        logger.info("Now we'll test finger detection...")
        logger.info("Watch the LEDs on your device!")
        logger.info("")
        
        for test_round in range(3):
            logger.info(f"=== Test Round {test_round + 1} ===")
            logger.info("STEP 1: Remove finger from scanner")
            logger.info("        - Should see: RED LED only")
            input("Press Enter when finger is removed...")
            
            logger.info("")
            logger.info("STEP 2: Place finger on scanner")
            logger.info("        - Should see: RED + GREEN LEDs")
            logger.info("        - Green LED = finger detected")
            input("Press Enter when finger is placed and GREEN LED is on...")
            
            # Try a quick detection test
            try:
                scan_api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(ctypes.c_ubyte), POINTER(c_uint)]
                scan_api.ftrScanGetFrame.restype = c_int
                
                buffer_size = 100
                image_buffer = (ctypes.c_ubyte * buffer_size)()
                frame_size = c_uint(buffer_size)
                
                logger.info("Testing SDK finger detection...")
                result = scan_api.ftrScanGetFrame(device_handle, image_buffer, byref(frame_size))
                
                if result == 0 and frame_size.value > 0:
                    data_sum = sum(image_buffer[i] for i in range(min(10, frame_size.value)))
                    logger.info(f"✅ SDK detected finger! Result: {result}, Frame: {frame_size.value}, Data: {data_sum}")
                    
                    if data_sum > 50:
                        logger.info("✅ Good finger data detected!")
                    else:
                        logger.info("⚠️ Weak finger signal")
                else:
                    logger.info(f"❌ No finger detected by SDK. Result: {result}, Frame: {frame_size.value}")
                    
            except Exception as e:
                logger.info(f"❌ SDK test failed: {e}")
            
            logger.info("")
            time.sleep(1)
        
        logger.info("=== LED Test Complete ===")
        logger.info("")
        logger.info("EXPECTED LED BEHAVIOR:")
        logger.info("- RED LED: Always on when device connected")
        logger.info("- GREEN LED: On when finger detected")
        logger.info("- BLUE LED: May flash during capture (some models)")
        logger.info("")
        logger.info("If LEDs behave as expected, finger detection should work!")
        
        # Close device
        scan_api.ftrScanCloseDevice(device_handle)
        logger.info("Device closed.")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")

if __name__ == '__main__':
    test_device_leds()
