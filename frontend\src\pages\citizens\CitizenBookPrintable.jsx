import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  CircularProgress,
  Alert,
  Paper,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Grid
} from '@mui/material';
import {
  Print as PrintIcon,
  PictureAsPdf as PdfIcon,
  Book as BookIcon,
  LocationCity as CityIcon,
  Business as SubcityIcon,
  LocationOn as KebeleIcon,
  People as PeopleIcon,
  Person as PersonIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useLocalization } from '../../contexts/LocalizationContext';
import axios from '../../utils/axios';

const CitizenBookPrintable = () => {
  const { user } = useAuth();
  const { t } = useLocalization();
  const [bookData, setBookData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Function to organize flat citizen data into hierarchical structure
  const organizeDataForBook = (data) => {
    if (!data.citizens || data.citizens.length === 0) {
      return {
        ...data,
        subcity_directory: [],
        statistics: {
          ...data.statistics,
          total_kebeles: 0,
          total_male: 0,
          total_female: 0
        }
      };
    }

    // Group citizens by subcity and kebele
    const subcityMap = {};
    let totalMale = 0;
    let totalFemale = 0;

    data.citizens.forEach(citizen => {
      const subcityName = citizen.subcity_name;
      const kebeleName = citizen.kebele_name;

      // Count gender
      if (citizen.gender === 'male') totalMale++;
      if (citizen.gender === 'female') totalFemale++;

      // Initialize subcity if not exists
      if (!subcityMap[subcityName]) {
        subcityMap[subcityName] = {
          subcity_name: subcityName,
          subcity_stats: {
            total_citizens: 0,
            male_count: 0,
            female_count: 0,
            kebeles_count: 0
          },
          kebeles: {}
        };
      }

      // Initialize kebele if not exists
      if (!subcityMap[subcityName].kebeles[kebeleName]) {
        subcityMap[subcityName].kebeles[kebeleName] = {
          kebele_name: kebeleName,
          kebele_stats: {
            total_citizens: 0,
            male_count: 0,
            female_count: 0
          },
          citizens: []
        };
        subcityMap[subcityName].subcity_stats.kebeles_count++;
      }

      // Add citizen to kebele
      subcityMap[subcityName].kebeles[kebeleName].citizens.push(citizen);
      subcityMap[subcityName].kebeles[kebeleName].kebele_stats.total_citizens++;
      if (citizen.gender === 'male') {
        subcityMap[subcityName].kebeles[kebeleName].kebele_stats.male_count++;
      } else if (citizen.gender === 'female') {
        subcityMap[subcityName].kebeles[kebeleName].kebele_stats.female_count++;
      }

      // Update subcity stats
      subcityMap[subcityName].subcity_stats.total_citizens++;
      if (citizen.gender === 'male') {
        subcityMap[subcityName].subcity_stats.male_count++;
      } else if (citizen.gender === 'female') {
        subcityMap[subcityName].subcity_stats.female_count++;
      }
    });

    // Convert to array and sort
    const subcity_directory = Object.values(subcityMap).map(subcity => ({
      ...subcity,
      kebeles: Object.values(subcity.kebeles).sort((a, b) => a.kebele_name.localeCompare(b.kebele_name))
    })).sort((a, b) => a.subcity_name.localeCompare(b.subcity_name));

    // Calculate total kebeles
    const totalKebeles = subcity_directory.reduce((sum, subcity) => sum + subcity.kebeles.length, 0);

    return {
      ...data,
      subcity_directory,
      statistics: {
        ...data.statistics,
        total_kebeles: totalKebeles,
        total_male: totalMale,
        total_female: totalFemale
      },
      city_info: {
        ...data.city_info,
        generated_date: new Date().toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }),
        generated_time: new Date().toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit'
        })
      }
    };
  };

  useEffect(() => {
    if (user) {
      fetchBookData();
    }
  }, [user]);

  const fetchBookData = async () => {
    try {
      setLoading(true);

      // Get tenant ID
      let tenantId = user?.tenant_id;
      if (!tenantId) {
        const token = localStorage.getItem('accessToken');
        if (token) {
          const payload = JSON.parse(atob(token.split('.')[1]));
          tenantId = payload.tenant_id;
        }
      }

      if (!tenantId) {
        throw new Error('No tenant ID available');
      }

      const response = await axios.get(`/api/tenants/${tenantId}/citizens/citizen-book/data/`);

      // Organize flat citizen data into hierarchical structure for the book
      const organizedData = organizeDataForBook(response.data);
      setBookData(organizedData);
      setError(null);
    } catch (error) {
      console.error('Error fetching citizen book data:', error);
      setError(`Failed to load citizen book: ${error.response?.data?.error || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const handleExportPDF = () => {
    // This would integrate with a PDF generation library
    // For now, we'll use the browser's print to PDF functionality
    window.print();
  };

  if (!user || loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <CircularProgress size={60} />
          <Typography variant="h6" className="mt-4">
            Generating Citizen Book...
          </Typography>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert severity="error" className="m-4">
        {error}
      </Alert>
    );
  }

  if (!bookData) {
    return (
      <Alert severity="info" className="m-4">
        {t('no_citizen_data_available', 'No citizen book data available')}
      </Alert>
    );
  }

  return (
    <div className="citizen-book-container">
      {/* Print Controls - Hidden when printing */}
      <div className="print-controls no-print p-4 bg-gray-100 border-b">
        <div className="flex items-center justify-between">
          <Typography variant="h5" className="font-bold">
            Citizen Book - Print Preview
          </Typography>
          <div className="space-x-2">
            <Button
              variant="contained"
              startIcon={<PrintIcon />}
              onClick={handlePrint}
              className="bg-blue-600 hover:bg-blue-700"
            >
              Print Book
            </Button>
            <Button
              variant="outlined"
              startIcon={<PdfIcon />}
              onClick={handleExportPDF}
              className="border-blue-600 text-blue-600 hover:bg-blue-50"
            >
              Export PDF
            </Button>
          </div>
        </div>
      </div>

      {/* Book Content - This will be printed */}
      <div className="book-content bg-white">
        {/* Enhanced Cover Page */}
        <div className="cover-page page-break-after text-center p-12 relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="w-full h-full bg-gradient-to-br from-orange-100 via-yellow-50 to-orange-100"></div>
          </div>

          {/* Ethiopian Flag Colors Border */}
          <div className="absolute top-0 left-0 right-0 h-2 bg-gradient-to-r from-green-500 via-yellow-400 to-red-500"></div>
          <div className="absolute bottom-0 left-0 right-0 h-2 bg-gradient-to-r from-green-500 via-yellow-400 to-red-500"></div>

          <div className="relative z-10">
            {/* Header with Ethiopian Flag */}
            <div className="cover-header mb-8">
              <div className="flex justify-center items-center mb-6">
                <img
                  src="/images/ethiopian-flag.svg"
                  alt="Ethiopian Flag"
                  className="w-20 h-14 mr-4 border-2 border-gray-300 rounded shadow-lg"
                />
                <CityIcon style={{ fontSize: '4rem', color: '#ff8f00' }} />
                <img
                  src="/images/ethiopian-flag.svg"
                  alt="Ethiopian Flag"
                  className="w-20 h-14 ml-4 border-2 border-gray-300 rounded shadow-lg"
                />
              </div>

              <Typography variant="h1" className="font-bold mt-6 mb-4" style={{
                color: '#ff8f00',
                fontSize: '3.5rem',
                textShadow: '2px 2px 4px rgba(0,0,0,0.1)',
                letterSpacing: '2px'
              }}>
                CITIZEN DIRECTORY
              </Typography>

              <div className="border-t-4 border-b-4 border-orange-400 py-4 my-6">
                <Typography variant="h2" className="font-bold text-gray-800" style={{
                  fontSize: '2.5rem',
                  letterSpacing: '1px'
                }}>
                  {bookData.city_info?.name} City
                </Typography>
                <Typography variant="h4" className="font-semibold text-gray-600 mt-2">
                  Federal Democratic Republic of Ethiopia
                </Typography>
              </div>
            </div>

            {/* Enhanced Statistics */}
            <div className="cover-stats mt-12 p-8 bg-white bg-opacity-80 rounded-xl shadow-lg border-2 border-orange-200">
              <Typography variant="h4" className="font-bold mb-6 text-orange-600">
                DIRECTORY STATISTICS
              </Typography>
              <Grid container spacing={4} justifyContent="center">
                <Grid item xs={6} md={3}>
                  <div className="text-center p-4 bg-blue-50 rounded-lg border-2 border-blue-200">
                    <Typography variant="h2" className="font-bold text-blue-700" style={{ fontSize: '2.5rem' }}>
                      {bookData.statistics?.total_citizens?.toLocaleString() || 0}
                    </Typography>
                    <Typography variant="h6" className="text-blue-600 font-semibold">
                      Total Citizens
                    </Typography>
                  </div>
                </Grid>
                <Grid item xs={6} md={3}>
                  <div className="text-center p-4 bg-purple-50 rounded-lg border-2 border-purple-200">
                    <Typography variant="h2" className="font-bold text-purple-700" style={{ fontSize: '2.5rem' }}>
                      {bookData.statistics?.total_subcities || 0}
                    </Typography>
                    <Typography variant="h6" className="text-purple-600 font-semibold">
                      Sub-Cities
                    </Typography>
                  </div>
                </Grid>
                <Grid item xs={6} md={3}>
                  <div className="text-center p-4 bg-green-50 rounded-lg border-2 border-green-200">
                    <Typography variant="h2" className="font-bold text-green-700" style={{ fontSize: '2.5rem' }}>
                      {bookData.statistics?.total_kebeles || 0}
                    </Typography>
                    <Typography variant="h6" className="text-green-600 font-semibold">
                      Kebeles
                    </Typography>
                  </div>
                </Grid>
                <Grid item xs={6} md={3}>
                  <div className="text-center p-4 bg-pink-50 rounded-lg border-2 border-pink-200">
                    <Typography variant="h3" className="font-bold text-pink-700" style={{ fontSize: '1.8rem' }}>
                      {bookData.statistics?.total_male || 0}M / {bookData.statistics?.total_female || 0}F
                    </Typography>
                    <Typography variant="h6" className="text-pink-600 font-semibold">
                      Gender Distribution
                    </Typography>
                  </div>
                </Grid>
              </Grid>
            </div>

            {/* Official Seal/Footer */}
            <div className="cover-footer mt-12 p-6 bg-gray-50 bg-opacity-80 rounded-lg border-2 border-gray-300">
              <Typography variant="h5" className="font-bold text-gray-800 mb-2">
                OFFICIAL DOCUMENT
              </Typography>
              <Typography variant="h6" className="text-gray-700 mb-3">
                Generated on {bookData.city_info?.generated_date}
              </Typography>
              <Typography variant="body1" className="text-gray-600 mb-2">
                Time: {bookData.city_info?.generated_time}
              </Typography>
              <div className="mt-4 pt-4 border-t border-gray-400">
                <Typography variant="body2" className="text-gray-500 italic">
                  "This document contains official citizen registry information"
                </Typography>
                <Typography variant="caption" className="text-gray-400 block mt-2">
                  Confidential - Handle in accordance with data protection regulations
                </Typography>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Table of Contents */}
        <div className="table-of-contents page-break-after p-8 relative">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-3">
            <div className="w-full h-full bg-gradient-to-br from-orange-50 via-yellow-25 to-orange-50"></div>
          </div>

          {/* Header */}
          <div className="relative z-10">
            <div className="text-center mb-8 pb-6 border-b-4 border-orange-400">
              <Typography variant="h2" className="font-bold mb-4" style={{
                color: '#ff8f00',
                fontSize: '3rem',
                textShadow: '1px 1px 2px rgba(0,0,0,0.1)',
                letterSpacing: '2px'
              }}>
                TABLE OF CONTENTS
              </Typography>
              <Typography variant="h5" className="text-gray-600 font-semibold">
                {bookData.city_info?.name} City Administrative Divisions
              </Typography>
            </div>

            {/* TOC Content */}
            <div className="toc-content space-y-6">
              {bookData.subcity_directory?.map((subcity, subcityIndex) => (
                <div key={subcity.subcity_name} className="toc-entry bg-white bg-opacity-70 rounded-lg p-6 border-2 border-orange-200 shadow-md">
                  {/* Subcity Header */}
                  <div className="subcity-toc-header mb-4 pb-3 border-b-2 border-orange-300">
                    <div className="flex items-center justify-between">
                      <Typography variant="h4" className="font-bold flex items-center text-orange-700">
                        <span className="bg-orange-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">
                          {subcityIndex + 1}
                        </span>
                        <SubcityIcon className="mr-2" style={{ color: '#ff8f00' }} />
                        {subcity.subcity_name} Sub-City
                      </Typography>
                      <div className="text-right">
                        <Typography variant="h6" className="font-bold text-blue-600">
                          {subcity.subcity_stats.total_citizens.toLocaleString()}
                        </Typography>
                        <Typography variant="caption" className="text-gray-500">
                          Total Citizens
                        </Typography>
                      </div>
                    </div>

                    {/* Subcity Statistics */}
                    <div className="mt-3 grid grid-cols-3 gap-4 text-center">
                      <div className="bg-blue-50 rounded p-2 border border-blue-200">
                        <Typography variant="body2" className="font-bold text-blue-700">
                          {subcity.subcity_stats.male_count}
                        </Typography>
                        <Typography variant="caption" className="text-blue-600">Male</Typography>
                      </div>
                      <div className="bg-pink-50 rounded p-2 border border-pink-200">
                        <Typography variant="body2" className="font-bold text-pink-700">
                          {subcity.subcity_stats.female_count}
                        </Typography>
                        <Typography variant="caption" className="text-pink-600">Female</Typography>
                      </div>
                      <div className="bg-green-50 rounded p-2 border border-green-200">
                        <Typography variant="body2" className="font-bold text-green-700">
                          {subcity.subcity_stats.kebeles_count}
                        </Typography>
                        <Typography variant="caption" className="text-green-600">Kebeles</Typography>
                      </div>
                    </div>
                  </div>

                  {/* Kebeles List */}
                  <div className="kebele-list grid grid-cols-1 md:grid-cols-2 gap-3">
                    {subcity.kebeles?.map((kebele, kebeleIndex) => (
                      <div key={kebele.kebele_name} className="kebele-toc-item bg-gray-50 rounded-lg p-3 border border-gray-200 hover:bg-gray-100 transition-colors">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <span className="bg-gray-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-2">
                              {kebeleIndex + 1}
                            </span>
                            <KebeleIcon className="mr-2 text-sm" style={{ color: '#666' }} />
                            <Typography variant="body1" className="font-semibold text-gray-800">
                              {kebele.kebele_name}
                            </Typography>
                          </div>
                          <div className="text-right">
                            <Typography variant="body2" className="font-bold text-purple-600">
                              {kebele.kebele_stats.total_citizens}
                            </Typography>
                            <Typography variant="caption" className="text-gray-500">
                              citizens
                            </Typography>
                          </div>
                        </div>

                        {/* Kebele Gender Stats */}
                        <div className="mt-2 flex justify-center space-x-4 text-xs">
                          <span className="text-blue-600">
                            ♂ {kebele.kebele_stats.male_count}
                          </span>
                          <span className="text-pink-600">
                            ♀ {kebele.kebele_stats.female_count}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            {/* TOC Footer */}
            <div className="toc-footer mt-8 pt-6 border-t-2 border-gray-300 text-center">
              <Typography variant="h6" className="font-bold text-gray-700 mb-2">
                Directory Summary
              </Typography>
              <div className="grid grid-cols-4 gap-4 max-w-2xl mx-auto">
                <div className="text-center">
                  <Typography variant="h5" className="font-bold text-blue-600">
                    {bookData.statistics?.total_citizens?.toLocaleString() || 0}
                  </Typography>
                  <Typography variant="caption" className="text-gray-600">Total Citizens</Typography>
                </div>
                <div className="text-center">
                  <Typography variant="h5" className="font-bold text-purple-600">
                    {bookData.statistics?.total_subcities || 0}
                  </Typography>
                  <Typography variant="caption" className="text-gray-600">Sub-Cities</Typography>
                </div>
                <div className="text-center">
                  <Typography variant="h5" className="font-bold text-green-600">
                    {bookData.statistics?.total_kebeles || 0}
                  </Typography>
                  <Typography variant="caption" className="text-gray-600">Kebeles</Typography>
                </div>
                <div className="text-center">
                  <Typography variant="body1" className="font-bold text-orange-600">
                    {((bookData.statistics?.total_male || 0) / (bookData.statistics?.total_citizens || 1) * 100).toFixed(1)}%
                  </Typography>
                  <Typography variant="caption" className="text-gray-600">Male Ratio</Typography>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Citizen Directory by Subcity and Kebele */}
        {bookData.subcity_directory?.map((subcity) => (
          <div key={subcity.subcity_name} className="subcity-section">
            {/* Subcity Header */}
            <div className="subcity-header page-break-before p-6 bg-orange-50 border-l-4 border-orange-500">
              <Typography variant="h3" className="font-bold flex items-center" style={{ color: '#ff8f00' }}>
                <SubcityIcon className="mr-3" />
                {subcity.subcity_name} Sub-City
              </Typography>
              <div className="subcity-stats mt-4 grid grid-cols-4 gap-4">
                <div className="text-center">
                  <Typography variant="h5" className="font-bold text-blue-600">
                    {subcity.subcity_stats.total_citizens}
                  </Typography>
                  <Typography variant="body2" className="text-gray-600">Total Citizens</Typography>
                </div>
                <div className="text-center">
                  <Typography variant="h5" className="font-bold text-green-600">
                    {subcity.subcity_stats.male_count}
                  </Typography>
                  <Typography variant="body2" className="text-gray-600">Male</Typography>
                </div>
                <div className="text-center">
                  <Typography variant="h5" className="font-bold text-pink-600">
                    {subcity.subcity_stats.female_count}
                  </Typography>
                  <Typography variant="body2" className="text-gray-600">Female</Typography>
                </div>
                <div className="text-center">
                  <Typography variant="h5" className="font-bold text-purple-600">
                    {subcity.subcity_stats.kebeles_count}
                  </Typography>
                  <Typography variant="body2" className="text-gray-600">Kebeles</Typography>
                </div>
              </div>
            </div>

            {/* Kebeles in this Subcity */}
            {subcity.kebeles?.map((kebele) => (
              <div key={kebele.kebele_name} className="kebele-section mt-6">
                {/* Kebele Header */}
                <div className="kebele-header p-4 bg-blue-50 border-l-4 border-blue-500">
                  <Typography variant="h4" className="font-semibold flex items-center text-blue-700">
                    <KebeleIcon className="mr-2" />
                    {kebele.kebele_name} Kebele
                  </Typography>
                  <div className="kebele-stats mt-2 flex space-x-6">
                    <span className="text-sm">
                      <strong>Total:</strong> {kebele.kebele_stats.total_citizens}
                    </span>
                    <span className="text-sm">
                      <strong>Male:</strong> {kebele.kebele_stats.male_count}
                    </span>
                    <span className="text-sm">
                      <strong>Female:</strong> {kebele.kebele_stats.female_count}
                    </span>
                  </div>
                </div>

                {/* Enhanced Professional Citizens Table */}
                {kebele.citizens?.length > 0 ? (
                  <div className="citizens-table-enhanced mt-6 relative bg-white rounded-lg shadow-lg overflow-hidden border-3 border-gray-800">
                    {/* Watermark Background */}
                    <div className="watermark-bg"></div>

                    {/* Table Header Banner */}
                    <div className="table-header-banner bg-gradient-to-r from-orange-600 to-orange-500 text-white p-4 relative z-10">
                      <Typography variant="h6" className="font-bold text-center">
                        CITIZEN REGISTRY - {kebele.kebele_name} KEBELE
                      </Typography>
                      <Typography variant="caption" className="text-center block text-orange-100">
                        Total Registered Citizens: {kebele.kebele_stats.total_citizens} |
                        Male: {kebele.kebele_stats.male_count} |
                        Female: {kebele.kebele_stats.female_count}
                      </Typography>
                    </div>

                    <table className="w-full border-collapse bg-white bg-opacity-95 relative z-10">
                      <thead>
                        <tr className="bg-gradient-to-r from-orange-100 via-orange-50 to-orange-100 border-b-3 border-gray-800">
                          <th className="border-2 border-gray-700 p-3 text-xs font-bold text-gray-800 bg-orange-200">#</th>
                          <th className="border-2 border-gray-700 p-3 text-xs font-bold text-gray-800 bg-orange-200">PHOTO</th>
                          <th className="border-2 border-gray-700 p-3 text-xs font-bold text-gray-800 bg-orange-200">FULL NAME</th>
                          <th className="border-2 border-gray-700 p-3 text-xs font-bold text-gray-800 bg-orange-200">DIGITAL ID</th>
                          <th className="border-2 border-gray-700 p-3 text-xs font-bold text-gray-800 bg-orange-200">DATE OF BIRTH</th>
                          <th className="border-2 border-gray-700 p-3 text-xs font-bold text-gray-800 bg-orange-200">AGE</th>
                          <th className="border-2 border-gray-700 p-3 text-xs font-bold text-gray-800 bg-orange-200">GENDER</th>
                          <th className="border-2 border-gray-700 p-3 text-xs font-bold text-gray-800 bg-orange-200">PHONE</th>
                          <th className="border-2 border-gray-700 p-3 text-xs font-bold text-gray-800 bg-orange-200">KEBELE</th>
                        </tr>
                      </thead>
                      <tbody>
                        {kebele.citizens.map((citizen, index) => {
                          // Determine photo source
                          const getPhotoSrc = () => {
                            if (citizen.photo_base64) {
                              // Handle base64 photo
                              if (citizen.photo_base64.startsWith('data:')) {
                                return citizen.photo_base64;
                              } else {
                                return `data:image/jpeg;base64,${citizen.photo_base64}`;
                              }
                            } else if (citizen.photo_url) {
                              // Handle photo URL
                              return citizen.photo_url;
                            }
                            return null;
                          };

                          const photoSrc = getPhotoSrc();

                          // Calculate age
                          const calculateAge = (dateOfBirth) => {
                            if (!dateOfBirth) return 'N/A';
                            const today = new Date();
                            const birthDate = new Date(dateOfBirth);
                            let age = today.getFullYear() - birthDate.getFullYear();
                            const monthDiff = today.getMonth() - birthDate.getMonth();
                            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                              age--;
                            }
                            return age;
                          };

                          return (
                            <tr key={citizen.id} className={`${index % 2 === 0 ? 'bg-white bg-opacity-95' : 'bg-orange-25 bg-opacity-95'} hover:bg-orange-100 transition-colors border-b border-gray-300`}>
                              {/* Row Number */}
                              <td className="border-2 border-gray-600 p-3 text-center font-bold text-sm text-gray-800 bg-gray-50">
                                <div className="bg-orange-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mx-auto">
                                  {index + 1}
                                </div>
                              </td>

                              {/* Enhanced Photo */}
                              <td className="border-2 border-gray-600 p-3 text-center">
                                <div className="citizen-photo-professional mx-auto" style={{width: '50px', height: '50px'}}>
                                  {photoSrc ? (
                                    <img
                                      src={photoSrc}
                                      alt={citizen.full_name}
                                      className="w-full h-full object-cover rounded-lg border-3 border-gray-500 shadow-md"
                                      style={{objectFit: 'cover'}}
                                    />
                                  ) : (
                                    <div className={`w-full h-full rounded-lg border-3 border-gray-500 flex items-center justify-center shadow-md ${
                                      citizen.gender === 'male'
                                        ? 'bg-gradient-to-br from-blue-200 to-blue-300'
                                        : 'bg-gradient-to-br from-pink-200 to-pink-300'
                                    }`}>
                                      <PersonIcon style={{
                                        fontSize: '24px',
                                        color: citizen.gender === 'male' ? '#1e40af' : '#be185d'
                                      }} />
                                    </div>
                                  )}
                                </div>
                              </td>

                              {/* Enhanced Full Name */}
                              <td className="border-2 border-gray-600 p-3">
                                <div className="font-bold text-sm text-gray-900 leading-tight mb-1">
                                  {citizen.full_name}
                                </div>
                                {citizen.first_name_am && (
                                  <div className="text-xs text-orange-700 italic bg-orange-50 px-2 py-1 rounded">
                                    {citizen.first_name_am} {citizen.middle_name_am} {citizen.last_name_am}
                                  </div>
                                )}
                              </td>

                              {/* Enhanced Digital ID */}
                              <td className="border-2 border-gray-600 p-3 text-center">
                                <div className="text-xs font-mono font-bold text-blue-900 bg-blue-100 px-3 py-2 rounded-lg border border-blue-300 shadow-sm">
                                  {citizen.digital_id || 'PENDING'}
                                </div>
                              </td>

                              {/* Enhanced Date of Birth */}
                              <td className="border-2 border-gray-600 p-3 text-center">
                                <div className="text-sm font-semibold text-gray-800 bg-gray-100 px-2 py-1 rounded">
                                  {citizen.date_of_birth ? new Date(citizen.date_of_birth).toLocaleDateString('en-GB') : 'N/A'}
                                </div>
                              </td>

                              {/* Enhanced Age */}
                              <td className="border-2 border-gray-600 p-3 text-center">
                                <div className="text-sm font-bold text-purple-800 bg-purple-100 px-3 py-2 rounded-lg border border-purple-300 shadow-sm">
                                  {calculateAge(citizen.date_of_birth)}
                                </div>
                              </td>

                              {/* Enhanced Gender */}
                              <td className="border-2 border-gray-600 p-3 text-center">
                                <span className={`inline-flex items-center px-3 py-2 rounded-lg text-sm font-bold text-white shadow-md border-2 ${
                                  citizen.gender === 'male'
                                    ? 'bg-gradient-to-r from-blue-600 to-blue-700 border-blue-800'
                                    : 'bg-gradient-to-r from-pink-600 to-pink-700 border-pink-800'
                                }`}>
                                  {citizen.gender === 'male' ? '♂ MALE' : '♀ FEMALE'}
                                </span>
                              </td>

                              {/* Enhanced Phone */}
                              <td className="border-2 border-gray-600 p-3">
                                <div className="text-sm text-gray-800 font-mono bg-gray-100 px-2 py-1 rounded border">
                                  {citizen.phone || 'N/A'}
                                </div>
                              </td>

                              {/* Enhanced Kebele Information */}
                              <td className="border-2 border-gray-600 p-3 text-center">
                                <div className="text-sm font-bold text-green-800 bg-green-100 px-2 py-1 rounded-lg border border-green-300 shadow-sm">
                                  {kebele.kebele_name}
                                </div>
                                <div className="text-xs text-gray-600 mt-1">
                                  {subcity.subcity_name}
                                </div>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <PeopleIcon className="text-4xl mb-2" />
                    <Typography variant="body1">No citizens registered in this kebele</Typography>
                  </div>
                )}
              </div>
            ))}
          </div>
        ))}

        {/* Footer */}
        <div className="book-footer page-break-before p-8 text-center border-t-2 border-gray-300">
          <Typography variant="h5" className="font-bold mb-4" style={{ color: '#ff8f00' }}>
            {bookData.city_info?.name} City Administration
          </Typography>
          <Typography variant="body1" className="text-gray-600 mb-2">
            Official Citizen Directory
          </Typography>
          <Typography variant="body2" className="text-gray-500">
            Generated on {bookData.city_info?.generated_date} at {bookData.city_info?.generated_time}
          </Typography>
          <Typography variant="caption" className="text-gray-400 mt-4 block">
            This document contains confidential information. Handle with care and in accordance with data protection regulations.
          </Typography>
        </div>
      </div>

      {/* Enhanced Print Styles */}
      <style>{`
        @media print {
          .no-print {
            display: none !important;
          }

          .page-break-before {
            page-break-before: always;
          }

          .page-break-after {
            page-break-after: always;
          }

          .book-content {
            font-size: 12px;
            line-height: 1.4;
          }

          /* Enhanced Cover Page */
          .cover-page {
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background: linear-gradient(135deg, #fff7ed 0%, #ffedd5 50%, #fff7ed 100%);
            border: 4px solid #ea580c;
          }

          /* Enhanced Table of Contents */
          .table-of-contents {
            min-height: 100vh;
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 50%, #fef3c7 100%);
          }

          /* Professional Citizens Table */
          .citizens-table-enhanced {
            margin-top: 20px;
            break-inside: avoid;
            position: relative;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
            border: 4px solid #1f2937;
          }

          .table-header-banner {
            background: linear-gradient(135deg, #ea580c 0%, #dc2626 100%) !important;
            color: white !important;
            padding: 12px !important;
            text-align: center;
            border-bottom: 3px solid #1f2937;
          }

          .watermark-bg {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url('/images/ethiopian-flag.svg');
            background-size: 200px 133px;
            background-repeat: repeat;
            background-position: center;
            opacity: 0.03;
            z-index: 1;
            pointer-events: none;
          }

          .citizens-table-enhanced table {
            width: 100%;
            border-collapse: collapse;
            font-size: 9px;
            background: rgba(255, 255, 255, 0.98);
            position: relative;
            z-index: 10;
          }

          .citizens-table-enhanced th {
            background: linear-gradient(135deg, #fed7aa 0%, #fdba74 50%, #fb923c 100%) !important;
            border: 2px solid #1f2937 !important;
            padding: 10px 8px;
            font-size: 8px;
            font-weight: bold;
            text-align: center;
            color: #1f2937 !important;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
          }

          .citizens-table-enhanced td {
            border: 2px solid #4b5563 !important;
            padding: 8px 6px;
            font-size: 8px;
            vertical-align: middle;
            position: relative;
          }

          .citizens-table-enhanced tr {
            break-inside: avoid;
            page-break-inside: avoid;
            min-height: 60px;
          }

          .citizens-table-enhanced tbody tr:nth-child(even) {
            background: rgba(255, 247, 237, 0.95) !important;
          }

          .citizens-table-enhanced tbody tr:nth-child(odd) {
            background: rgba(255, 255, 255, 0.95) !important;
          }

          /* Professional Photo Styling */
          .citizen-photo-professional {
            width: 45px !important;
            height: 45px !important;
            border: 3px solid #374151 !important;
            border-radius: 8px;
            background: #f9fafb;
            overflow: hidden;
            display: inline-block;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
          }

          .citizen-photo-professional img {
            width: 100% !important;
            height: 100% !important;
            object-fit: cover;
          }

          /* Enhanced Data Cell Styling */
          .citizens-table-enhanced .bg-blue-100 {
            background-color: rgba(219, 234, 254, 0.9) !important;
            border: 1px solid #3b82f6 !important;
          }

          .citizens-table-enhanced .bg-purple-100 {
            background-color: rgba(237, 233, 254, 0.9) !important;
            border: 1px solid #8b5cf6 !important;
          }

          .citizens-table-enhanced .bg-green-100 {
            background-color: rgba(220, 252, 231, 0.9) !important;
            border: 1px solid #10b981 !important;
          }

          .citizens-table-enhanced .bg-gray-100 {
            background-color: rgba(243, 244, 246, 0.9) !important;
            border: 1px solid #6b7280 !important;
          }

          .citizens-table-enhanced .bg-orange-100 {
            background-color: rgba(254, 215, 170, 0.9) !important;
            border: 1px solid #f97316 !important;
          }

          .citizens-table-enhanced .text-blue-900 {
            color: #1e3a8a !important;
          }

          .citizens-table-enhanced .text-purple-800 {
            color: #5b21b6 !important;
          }

          .citizens-table-enhanced .text-green-800 {
            color: #166534 !important;
          }

          .citizens-table-enhanced .text-gray-800 {
            color: #1f2937 !important;
          }

          .citizens-table-enhanced .text-orange-700 {
            color: #c2410c !important;
          }

          /* Gender Badge Styling */
          .citizens-table-enhanced .bg-gradient-to-r {
            background: linear-gradient(135deg, var(--tw-gradient-from), var(--tw-gradient-to)) !important;
          }

          /* Section Headers */
          .subcity-header,
          .kebele-header {
            break-inside: avoid;
            page-break-inside: avoid;
          }

          .kebele-section {
            break-inside: avoid;
            page-break-inside: avoid;
            margin-bottom: 30px;
          }

          /* Enhanced Typography */
          .font-mono {
            font-family: 'Courier New', monospace !important;
          }

          .font-bold {
            font-weight: 700 !important;
          }

          .font-semibold {
            font-weight: 600 !important;
          }
        }

        @page {
          margin: 0.75in;
          size: A4;
        }

        /* Screen styles for better preview */
        @media screen {
          .citizen-photo-professional {
            transition: transform 0.2s ease;
          }

          .citizen-photo-professional:hover {
            transform: scale(1.1);
          }

          .citizens-table-enhanced tr:hover {
            background-color: rgba(251, 146, 60, 0.1) !important;
          }
        }
      `}</style>
    </div>
  );
};

export default CitizenBookPrintable;
