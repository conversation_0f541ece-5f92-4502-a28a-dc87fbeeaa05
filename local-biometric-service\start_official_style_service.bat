@echo off
echo ========================================
echo    OFFICIAL-STYLE BIOMETRIC SERVICE
echo ========================================
echo.
echo FEATURES:
echo   - Mimics ftrScanApiEx.exe behavior
echo   - Should activate LEDs like official software
echo   - Uses full SDK pattern
echo   - Real finger detection
echo.
echo SERVICE URL: http://localhost:8001
echo.
echo TESTING:
echo   1. Watch your Futronic device LEDs during testing
echo   2. LEDs should activate when checking for finger
echo   3. No finger = Quick error (3 seconds)
echo   4. With finger = Success with quality score
echo.
echo Since your device works with ftrScanApiEx.exe,
echo this service should work the same way!
echo.

echo Starting official-style biometric service...
echo.
echo Watch your device LEDs during testing!
echo Keep this window open while using GoID system
echo.

python official_style_biometric_service.py

echo.
echo Service stopped.
pause
