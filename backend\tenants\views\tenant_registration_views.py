from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db import transaction
from django.shortcuts import get_object_or_404
from ..models.city import CityAdministration, Region, Country
from ..models.subcity import SubCity
from ..models.kebele import <PERSON><PERSON><PERSON>, Ketena
from ..models.tenant import Tenant, TenantType
from ..serializers import (
    CityAdministrationSerializer, CityRegistrationSerializer,
    SubCitySerializer, SubCityRegistrationSerializer,
    KebeleSerializer, KebeleRegistrationSerializer,
    KetenaSerializer, KetenaCreateSerializer,
    CountrySerializer, RegionSerializer
)
from ..permissions import IsSuperUser, CanManageTenants, CanRegisterTenants

class CountryViewSet(viewsets.ReadOnlyModelViewSet):
    """API endpoint for retrieving countries."""
    queryset = Country.objects.all()
    serializer_class = CountrySerializer
    permission_classes = [permissions.IsAuthenticated]

class RegionViewSet(viewsets.ReadOnlyModelViewSet):
    """API endpoint for retrieving regions."""
    queryset = Region.objects.all()
    serializer_class = RegionSerializer
    permission_classes = [permissions.IsAuthenticated]
    filterset_fields = ['country']

class CityViewSet(viewsets.ModelViewSet):
    """API endpoint for managing cities."""
    queryset = CityAdministration.objects.all()
    serializer_class = CityAdministrationSerializer
    permission_classes = [permissions.IsAuthenticated, CanRegisterTenants]

    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'create':
            return CityRegistrationSerializer
        return CityAdministrationSerializer

    @transaction.atomic
    def update(self, request, *args, **kwargs):
        """Enhanced update method for city administration with file handling."""
        try:
            partial = kwargs.pop('partial', False)
            instance = self.get_object()

            print(f"🔍 CityViewSet update - Request data keys: {list(request.data.keys())}")
            print(f"🔍 CityViewSet update - Request files: {list(request.FILES.keys())}")

            # Handle file uploads
            if 'logo' in request.FILES:
                # Delete old logo if exists
                if instance.logo:
                    instance.logo.delete(save=False)
                instance.logo = request.FILES['logo']

            if 'mayor_signature' in request.FILES:
                # Delete old signature if exists
                if instance.mayor_signature:
                    instance.mayor_signature.delete(save=False)
                instance.mayor_signature = request.FILES['mayor_signature']

            serializer = self.get_serializer(instance, data=request.data, partial=partial)

            if not serializer.is_valid():
                print(f"❌ CityViewSet serializer errors: {serializer.errors}")
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            self.perform_update(serializer)
            return Response(serializer.data)

        except Exception as e:
            print(f"❌ CityViewSet update error: {str(e)}")
            import traceback
            print(f"❌ Traceback: {traceback.format_exc()}")
            return Response(
                {"detail": f"Update failed: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST
            )

    @transaction.atomic
    def perform_create(self, serializer):
        serializer.save()

class SubcityViewSet(viewsets.ModelViewSet):
    """API endpoint for managing subcities."""
    queryset = SubCity.objects.all()
    serializer_class = SubCitySerializer
    permission_classes = [permissions.IsAuthenticated, CanRegisterTenants]
    filterset_fields = ['city']

    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'create':
            return SubCityRegistrationSerializer
        return SubCitySerializer

    @transaction.atomic
    def update(self, request, *args, **kwargs):
        """Enhanced update method for subcity with validation."""
        try:
            partial = kwargs.pop('partial', False)
            instance = self.get_object()

            print(f"🔍 SubcityViewSet update - Request data keys: {list(request.data.keys())}")
            print(f"🔍 SubcityViewSet update - Request files: {list(request.FILES.keys())}")

            # Handle file uploads
            if 'logo' in request.FILES:
                # Delete old logo if exists
                if instance.logo:
                    instance.logo.delete(save=False)
                instance.logo = request.FILES['logo']

            if 'mayor_signature' in request.FILES:
                # Delete old signature if exists
                if instance.mayor_signature:
                    instance.mayor_signature.delete(save=False)
                instance.mayor_signature = request.FILES['mayor_signature']

            if 'pattern_image' in request.FILES:
                # Delete old pattern if exists
                if instance.pattern_image:
                    instance.pattern_image.delete(save=False)
                instance.pattern_image = request.FILES['pattern_image']

            serializer = self.get_serializer(instance, data=request.data, partial=partial)

            if not serializer.is_valid():
                print(f"❌ SubcityViewSet serializer errors: {serializer.errors}")
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            self.perform_update(serializer)
            return Response(serializer.data)

        except Exception as e:
            print(f"❌ SubcityViewSet update error: {str(e)}")
            import traceback
            print(f"❌ Traceback: {traceback.format_exc()}")
            return Response(
                {"detail": f"Update failed: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST
            )

    def get_serializer_class(self):
        if self.action == 'create':
            return SubCityRegistrationSerializer
        return SubCitySerializer

    @transaction.atomic
    def perform_create(self, serializer):
        serializer.save()

class KebeleViewSet(viewsets.ModelViewSet):
    """API endpoint for managing kebeles."""
    queryset = Kebele.objects.all()
    serializer_class = KebeleSerializer
    permission_classes = [permissions.IsAuthenticated, CanRegisterTenants]
    filterset_fields = ['sub_city']

    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'create':
            return KebeleRegistrationSerializer
        return KebeleSerializer

    @transaction.atomic
    def update(self, request, *args, **kwargs):
        """Enhanced update method for kebele with validation."""
        try:
            partial = kwargs.pop('partial', False)
            instance = self.get_object()

            print(f"🔍 KebeleViewSet update - Request data keys: {list(request.data.keys())}")
            print(f"🔍 KebeleViewSet update - Request files: {list(request.FILES.keys())}")

            # Handle file uploads
            if 'logo' in request.FILES:
                # Delete old logo if exists
                if instance.logo:
                    instance.logo.delete(save=False)
                instance.logo = request.FILES['logo']

            if 'mayor_signature' in request.FILES:
                # Delete old signature if exists
                if instance.mayor_signature:
                    instance.mayor_signature.delete(save=False)
                instance.mayor_signature = request.FILES['mayor_signature']

            if 'pattern_image' in request.FILES:
                # Delete old pattern if exists
                if instance.pattern_image:
                    instance.pattern_image.delete(save=False)
                instance.pattern_image = request.FILES['pattern_image']

            serializer = self.get_serializer(instance, data=request.data, partial=partial)

            if not serializer.is_valid():
                print(f"❌ KebeleViewSet serializer errors: {serializer.errors}")
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            self.perform_update(serializer)
            return Response(serializer.data)

        except Exception as e:
            print(f"❌ KebeleViewSet update error: {str(e)}")
            import traceback
            print(f"❌ Traceback: {traceback.format_exc()}")
            return Response(
                {"detail": f"Update failed: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST
            )

    def get_serializer_class(self):
        if self.action == 'create':
            return KebeleRegistrationSerializer
        return KebeleSerializer

    @transaction.atomic
    def perform_create(self, serializer):
        serializer.save()

class KetenaViewSet(viewsets.ModelViewSet):
    """API endpoint for managing ketenas."""
    queryset = Ketena.objects.all()
    serializer_class = KetenaSerializer
    permission_classes = [permissions.IsAuthenticated, CanManageTenants]
    filterset_fields = ['kebele']

    def get_serializer_class(self):
        if self.action == 'create':
            return KetenaCreateSerializer
        return KetenaSerializer

    @transaction.atomic
    def perform_create(self, serializer):
        serializer.save()

class TenantRegistrationViewSet(viewsets.ViewSet):
    """API endpoint for tenant registration."""
    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=['get'])
    def available_cities(self, request):
        """Get all available cities for subcity registration."""
        from ..models.tenant import Tenant, TenantType
        cities = Tenant.objects.filter(type=TenantType.CITY)
        data = [{'id': city.id, 'name': city.name} for city in cities]
        return Response(data)

    @action(detail=False, methods=['get'])
    def available_subcities(self, request):
        """Get all available subcities for kebele registration."""
        from ..models.tenant import Tenant, TenantType
        city_id = request.query_params.get('city_id')
        queryset = Tenant.objects.filter(type=TenantType.SUBCITY)

        if city_id:
            queryset = queryset.filter(parent_id=city_id)

        subcities = queryset
        data = [{'id': subcity.id, 'name': subcity.name, 'parent_id': subcity.parent_id} for subcity in subcities]
        return Response(data)

    @action(detail=False, methods=['get'])
    def available_kebeles(self, request):
        """Get all available kebeles for ketena registration."""
        from ..models.tenant import Tenant, TenantType
        subcity_id = request.query_params.get('subcity_id')
        queryset = Tenant.objects.filter(type=TenantType.KEBELE)

        if subcity_id:
            queryset = queryset.filter(parent_id=subcity_id)

        kebeles = queryset
        data = [{'id': kebele.id, 'name': kebele.name, 'parent_id': kebele.parent_id} for kebele in kebeles]
        return Response(data)
