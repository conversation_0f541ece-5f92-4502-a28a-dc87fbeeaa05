from rest_framework import serializers
from django.utils import timezone
from workflows.models import CitizenClearanceRequest
from tenants.models import Tenant
from users.models import User


class CitizenClearanceRequestSerializer(serializers.ModelSerializer):
    """Serializer for citizen clearance requests."""
    
    # Read-only fields for display
    clearance_reason_display = serializers.CharField(source='get_clearance_reason_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    # Source kebele information
    source_kebele_info = serializers.SerializerMethodField()
    
    # User information
    requested_by_info = serializers.SerializerMethodField()
    reviewed_by_info = serializers.SerializerMethodField()
    issued_by_info = serializers.SerializerMethodField()
    
    # Action permissions
    can_be_approved = serializers.ReadOnlyField()
    can_be_rejected = serializers.ReadOnlyField()
    can_be_issued = serializers.ReadOnlyField()
    can_be_cancelled = serializers.ReadOnlyField()
    
    class Meta:
        model = CitizenClearanceRequest
        fields = [
            'id', 'clearance_id', 'citizen_id', 'citizen_name', 'citizen_digital_id',
            'source_kebele', 'source_kebele_info', 'destination_location',
            'clearance_reason', 'clearance_reason_display', 'reason_description',
            'status', 'status_display', 'requested_by', 'requested_by_info',
            'reviewed_by', 'reviewed_by_info', 'issued_by', 'issued_by_info',
            'created_at', 'reviewed_at', 'issued_at', 'review_notes',
            'application_letter', 'current_kebele_id', 'supporting_documents',
            'documents_uploaded_at', 'clearance_letter_path',
            'can_be_approved', 'can_be_rejected', 'can_be_issued', 'can_be_cancelled'
        ]
        read_only_fields = [
            'id', 'clearance_id', 'requested_by', 'reviewed_by', 'issued_by',
            'created_at', 'reviewed_at', 'issued_at', 'clearance_letter_path'
        ]
    
    def get_source_kebele_info(self, obj):
        """Get source kebele information."""
        if obj.source_kebele:
            return {
                'id': obj.source_kebele.id,
                'name': obj.source_kebele.name,
                'type': obj.source_kebele.type,
                'parent_name': obj.source_kebele.parent.name if obj.source_kebele.parent else None
            }
        return None
    
    def get_requested_by_info(self, obj):
        """Get information about who requested the clearance."""
        if obj.requested_by:
            return {
                'id': obj.requested_by.id,
                'email': obj.requested_by.email,
                'first_name': obj.requested_by.first_name,
                'last_name': obj.requested_by.last_name,
                'role': obj.requested_by.role
            }
        return None
    
    def get_reviewed_by_info(self, obj):
        """Get information about who reviewed the clearance."""
        if obj.reviewed_by:
            return {
                'id': obj.reviewed_by.id,
                'email': obj.reviewed_by.email,
                'first_name': obj.reviewed_by.first_name,
                'last_name': obj.reviewed_by.last_name,
                'role': obj.reviewed_by.role
            }
        return None
    
    def get_issued_by_info(self, obj):
        """Get information about who issued the clearance letter."""
        if obj.issued_by:
            return {
                'id': obj.issued_by.id,
                'email': obj.issued_by.email,
                'first_name': obj.issued_by.first_name,
                'last_name': obj.issued_by.last_name,
                'role': obj.issued_by.role
            }
        return None


class CitizenClearanceRequestCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating clearance requests."""
    
    class Meta:
        model = CitizenClearanceRequest
        fields = [
            'citizen_id', 'citizen_name', 'citizen_digital_id',
            'destination_location', 'clearance_reason', 'reason_description'
        ]
    
    def validate_destination_location(self, value):
        """Validate destination location is provided."""
        if not value or not value.strip():
            raise serializers.ValidationError(
                "Destination location is required."
            )
        return value.strip()
    
    def validate_reason_description(self, value):
        """Validate reason description is provided."""
        if not value or not value.strip():
            raise serializers.ValidationError(
                "Detailed reason description is required."
            )
        return value.strip()


class CitizenClearanceReviewSerializer(serializers.ModelSerializer):
    """Serializer for reviewing clearance requests (approve/reject)."""
    
    action = serializers.ChoiceField(
        choices=['approve', 'reject'],
        write_only=True
    )
    review_notes = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Optional notes about the review decision"
    )
    
    class Meta:
        model = CitizenClearanceRequest
        fields = ['action', 'review_notes']
    
    def validate(self, data):
        """Validate review action."""
        action = data.get('action')
        review_notes = data.get('review_notes', '')
        
        # Require notes for rejection
        if action == 'reject' and not review_notes.strip():
            raise serializers.ValidationError({
                'review_notes': 'Review notes are required when rejecting a clearance request.'
            })
        
        return data


class CitizenClearanceDocumentUploadSerializer(serializers.ModelSerializer):
    """Serializer for uploading clearance documents."""
    
    class Meta:
        model = CitizenClearanceRequest
        fields = ['application_letter', 'current_kebele_id', 'supporting_documents']
    
    def validate(self, data):
        """Validate that required documents are provided."""
        if not data.get('application_letter'):
            raise serializers.ValidationError({
                'application_letter': 'Application letter is required.'
            })
        
        if not data.get('current_kebele_id'):
            raise serializers.ValidationError({
                'current_kebele_id': 'Current kebele ID card is required.'
            })
        
        return data
