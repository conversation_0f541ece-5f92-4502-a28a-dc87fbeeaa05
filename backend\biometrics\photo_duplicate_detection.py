"""
Photo Duplicate Detection System

This module provides photo duplicate detection capabilities to identify
citizens registered with identical or very similar photos.
"""

import hashlib
import base64
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from PIL import Image
import io
import numpy as np
from django.core.cache import cache
from django_tenants.utils import schema_context, get_tenant_model
from citizens.models import Citizen

logger = logging.getLogger(__name__)


class PhotoDuplicateDetector:
    """
    Detects duplicate photos in citizen registrations using multiple methods:
    1. Exact hash matching (identical files)
    2. Perceptual hashing (similar images)
    3. Image similarity analysis
    """
    
    def __init__(self):
        self.cache_timeout = 3600  # 1 hour
        self.similarity_threshold = 0.95  # 95% similarity threshold
        
    def check_photo_duplicates(self, photo_data: str, exclude_citizen_id: Optional[int] = None) -> Dict:
        """
        Check for duplicate photos across all tenants
        
        Args:
            photo_data (str): Base64 encoded photo data
            exclude_citizen_id (Optional[int]): Citizen ID to exclude from check
            
        Returns:
            Dict: Duplicate detection results
        """
        try:
            logger.info("🔍 Starting photo duplicate detection")
            
            if not photo_data:
                return self._create_result(False, [], "No photo data provided")
            
            # Calculate photo hash
            photo_hash = self._calculate_photo_hash(photo_data)
            if not photo_hash:
                return self._create_result(False, [], "Could not process photo data")
            
            # Get all stored photos
            stored_photos = self._get_all_stored_photos()
            
            if exclude_citizen_id:
                stored_photos = [
                    p for p in stored_photos 
                    if p['citizen_id'] != exclude_citizen_id
                ]
            
            logger.info(f"🔍 Checking against {len(stored_photos)} stored photos")
            
            # Check for duplicates
            matches = []
            
            # 1. Exact hash matching (fastest)
            exact_matches = self._find_exact_matches(photo_hash, stored_photos)
            matches.extend(exact_matches)
            
            # 2. Perceptual hash matching (for similar images)
            if not exact_matches:  # Only if no exact matches found
                perceptual_matches = self._find_perceptual_matches(photo_data, stored_photos)
                matches.extend(perceptual_matches)
            
            # Remove duplicates and sort by similarity
            unique_matches = self._deduplicate_photo_matches(matches)
            
            has_duplicates = len(unique_matches) > 0
            
            if has_duplicates:
                logger.warning(f"🚨 PHOTO DUPLICATE DETECTED: Found {len(unique_matches)} matches")
            
            return self._create_result(
                has_duplicates,
                unique_matches,
                f"Checked {len(stored_photos)} photos, found {len(unique_matches)} duplicates"
            )
            
        except Exception as e:
            logger.error(f"Error in photo duplicate detection: {e}")
            return self._create_result(False, [], f"Error: {str(e)}")
    
    def _calculate_photo_hash(self, photo_data: str) -> Optional[str]:
        """Calculate MD5 hash of photo data"""
        try:
            # Remove data URL prefix if present
            if photo_data.startswith('data:'):
                photo_data = photo_data.split(',')[1]
            
            # Calculate MD5 hash
            photo_bytes = base64.b64decode(photo_data)
            return hashlib.md5(photo_bytes).hexdigest()
            
        except Exception as e:
            logger.error(f"Error calculating photo hash: {e}")
            return None
    
    def _calculate_perceptual_hash(self, photo_data: str) -> Optional[str]:
        """Calculate perceptual hash for similar image detection"""
        try:
            # Remove data URL prefix if present
            if photo_data.startswith('data:'):
                photo_data = photo_data.split(',')[1]
            
            # Decode image
            photo_bytes = base64.b64decode(photo_data)
            image = Image.open(io.BytesIO(photo_bytes))
            
            # Convert to grayscale and resize to 8x8
            image = image.convert('L').resize((8, 8), Image.Resampling.LANCZOS)
            
            # Get pixel values
            pixels = list(image.getdata())
            
            # Calculate average
            avg = sum(pixels) / len(pixels)
            
            # Create hash based on whether each pixel is above/below average
            hash_bits = []
            for pixel in pixels:
                hash_bits.append('1' if pixel > avg else '0')
            
            # Convert to hex
            hash_binary = ''.join(hash_bits)
            hash_hex = hex(int(hash_binary, 2))[2:].zfill(16)
            
            return hash_hex
            
        except Exception as e:
            logger.error(f"Error calculating perceptual hash: {e}")
            return None
    
    def _get_all_stored_photos(self) -> List[Dict]:
        """Get all citizen photos from all tenants"""
        cache_key = "citizen_photos_all"
        
        # Try cache first
        cached_photos = cache.get(cache_key)
        if cached_photos:
            logger.info(f"📋 Using cached photos: {len(cached_photos)} photos")
            return cached_photos
        
        stored_photos = []
        
        try:
            Tenant = get_tenant_model()
            tenants = Tenant.objects.filter(type='kebele')
            
            for tenant in tenants:
                try:
                    with schema_context(tenant.schema_name):
                        citizens = Citizen.objects.filter(
                            photo__isnull=False
                        ).exclude(photo='')
                        
                        for citizen in citizens:
                            if citizen.photo:
                                photo_hash = self._calculate_photo_hash(citizen.photo)
                                perceptual_hash = self._calculate_perceptual_hash(citizen.photo)
                                
                                stored_photos.append({
                                    'citizen_id': citizen.id,
                                    'citizen_name': f"{citizen.first_name} {citizen.last_name}",
                                    'citizen_digital_id': citizen.digital_id,
                                    'tenant_id': tenant.id,
                                    'tenant_name': tenant.name,
                                    'photo_hash': photo_hash,
                                    'perceptual_hash': perceptual_hash,
                                    'photo_data': citizen.photo[:100] + "..." if len(citizen.photo) > 100 else citizen.photo  # Preview only
                                })
                        
                        logger.info(f"📊 Loaded {len([p for p in stored_photos if p['tenant_name'] == tenant.name])} photos from {tenant.name}")
                        
                except Exception as e:
                    logger.error(f"Error loading photos from {tenant.name}: {e}")
                    continue
            
            # Cache the results
            cache.set(cache_key, stored_photos, self.cache_timeout)
            logger.info(f"📋 Cached {len(stored_photos)} photos for {self.cache_timeout}s")
            
            return stored_photos
            
        except Exception as e:
            logger.error(f"Error getting stored photos: {e}")
            return []
    
    def _find_exact_matches(self, photo_hash: str, stored_photos: List[Dict]) -> List[Dict]:
        """Find exact photo matches using hash comparison"""
        matches = []
        
        for stored_photo in stored_photos:
            if stored_photo['photo_hash'] == photo_hash:
                matches.append({
                    'tenant_id': stored_photo['tenant_id'],
                    'tenant_name': stored_photo['tenant_name'],
                    'citizen_id': stored_photo['citizen_id'],
                    'citizen_name': stored_photo['citizen_name'],
                    'citizen_digital_id': stored_photo['citizen_digital_id'],
                    'match_type': 'exact',
                    'similarity_score': 1.0,
                    'confidence': 1.0,
                    'match_details': ['Identical photo hash (exact duplicate)']
                })
                
                logger.warning(f"🚨 EXACT PHOTO MATCH: {stored_photo['citizen_name']} in {stored_photo['tenant_name']}")
        
        return matches
    
    def _find_perceptual_matches(self, photo_data: str, stored_photos: List[Dict]) -> List[Dict]:
        """Find similar photos using perceptual hashing"""
        matches = []
        
        try:
            new_perceptual_hash = self._calculate_perceptual_hash(photo_data)
            if not new_perceptual_hash:
                return matches
            
            for stored_photo in stored_photos:
                stored_hash = stored_photo.get('perceptual_hash')
                if not stored_hash:
                    continue
                
                # Calculate Hamming distance between hashes
                similarity = self._calculate_hash_similarity(new_perceptual_hash, stored_hash)
                
                if similarity >= self.similarity_threshold:
                    matches.append({
                        'tenant_id': stored_photo['tenant_id'],
                        'tenant_name': stored_photo['tenant_name'],
                        'citizen_id': stored_photo['citizen_id'],
                        'citizen_name': stored_photo['citizen_name'],
                        'citizen_digital_id': stored_photo['citizen_digital_id'],
                        'match_type': 'perceptual',
                        'similarity_score': similarity,
                        'confidence': similarity,
                        'match_details': [f'Perceptual similarity: {similarity:.1%}']
                    })
                    
                    logger.warning(f"🚨 SIMILAR PHOTO: {stored_photo['citizen_name']} in {stored_photo['tenant_name']} (Similarity: {similarity:.1%})")
        
        except Exception as e:
            logger.error(f"Error in perceptual matching: {e}")
        
        return matches
    
    def _calculate_hash_similarity(self, hash1: str, hash2: str) -> float:
        """Calculate similarity between two perceptual hashes"""
        try:
            if len(hash1) != len(hash2):
                return 0.0
            
            # Convert to binary and calculate Hamming distance
            bin1 = bin(int(hash1, 16))[2:].zfill(64)
            bin2 = bin(int(hash2, 16))[2:].zfill(64)
            
            differences = sum(c1 != c2 for c1, c2 in zip(bin1, bin2))
            similarity = 1.0 - (differences / len(bin1))
            
            return similarity
            
        except Exception as e:
            logger.error(f"Error calculating hash similarity: {e}")
            return 0.0
    
    def _deduplicate_photo_matches(self, matches: List[Dict]) -> List[Dict]:
        """Remove duplicate matches and sort by similarity"""
        unique_matches = []
        seen_citizens = set()
        
        # Sort by similarity score (highest first)
        sorted_matches = sorted(matches, key=lambda x: x['similarity_score'], reverse=True)
        
        for match in sorted_matches:
            citizen_key = f"{match['tenant_id']}_{match['citizen_id']}"
            if citizen_key not in seen_citizens:
                unique_matches.append(match)
                seen_citizens.add(citizen_key)
        
        return unique_matches
    
    def _create_result(self, has_duplicates: bool, matches: List[Dict], message: str) -> Dict:
        """Create standardized result dictionary"""
        return {
            'has_duplicates': has_duplicates,
            'matches': matches,
            'total_matches': len(matches),
            'message': message,
            'check_time': datetime.now().isoformat(),
            'detector': 'Photo Duplicate Detection Service'
        }
    
    def clear_cache(self):
        """Clear the photo cache"""
        cache.delete("citizen_photos_all")
        logger.info("🗑️ Photo cache cleared")
    
    def bulk_photo_audit(self) -> Dict:
        """Perform bulk photo duplicate audit"""
        try:
            logger.info("🔍 Starting bulk photo duplicate audit")
            
            all_photos = self._get_all_stored_photos()
            duplicates_found = []
            
            # Group photos by hash for quick exact match detection
            hash_groups = {}
            for photo in all_photos:
                photo_hash = photo['photo_hash']
                if photo_hash not in hash_groups:
                    hash_groups[photo_hash] = []
                hash_groups[photo_hash].append(photo)
            
            # Find exact duplicates
            for photo_hash, photos in hash_groups.items():
                if len(photos) > 1:
                    # Found exact duplicates
                    for i, photo in enumerate(photos):
                        for j, other_photo in enumerate(photos[i+1:], i+1):
                            duplicates_found.append({
                                'type': 'exact_duplicate',
                                'photo1': photo,
                                'photo2': other_photo,
                                'similarity_score': 1.0,
                                'confidence': 1.0
                            })
            
            return {
                'total_photos_checked': len(all_photos),
                'exact_duplicates_found': len(duplicates_found),
                'duplicates': duplicates_found,
                'check_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in bulk photo audit: {e}")
            return {
                'error': str(e),
                'check_time': datetime.now().isoformat()
            }


# Global instance
photo_duplicate_detector = PhotoDuplicateDetector()
