#!/usr/bin/env python3
"""
Test Tenant API Endpoints

This script tests the tenant API endpoints to verify data structure and permissions.
"""

import requests
import json
from datetime import datetime


def test_tenant_endpoints():
    """Test tenant API endpoints with authentication"""
    print("🧪 TESTING TENANT API ENDPOINTS")
    print("=" * 80)
    print(f"Test started at: {datetime.now().isoformat()}")
    print()
    
    # Base URL
    base_url = "http://localhost:8000"
    
    # Test authentication first
    print("🔐 TESTING AUTHENTICATION")
    print("-" * 50)
    
    # Login credentials (adjust as needed)
    login_data = {
        "username": "<EMAIL>",
        "password": "your_password_here"  # Replace with actual password
    }
    
    try:
        # Get authentication token
        auth_response = requests.post(f"{base_url}/api/auth/login/", json=login_data)
        
        if auth_response.status_code == 200:
            token_data = auth_response.json()
            access_token = token_data.get('access')
            
            print(f"   ✅ Authentication successful")
            print(f"   🔑 Token obtained")
            
            # Headers for authenticated requests
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            
            # Test tenant endpoints
            test_endpoints = [
                {
                    "name": "Tenant List",
                    "url": "/api/tenants/",
                    "description": "List all tenants"
                },
                {
                    "name": "Kebeles List", 
                    "url": "/api/tenants/kebeles/",
                    "description": "List all kebeles"
                },
                {
                    "name": "Subcities List",
                    "url": "/api/tenants/subcities/",
                    "description": "List all subcities"
                },
                {
                    "name": "Cities List",
                    "url": "/api/tenants/cities/",
                    "description": "List all cities"
                }
            ]
            
            print("\n📡 TESTING API ENDPOINTS")
            print("-" * 50)
            
            for endpoint in test_endpoints:
                print(f"\n🔍 Testing: {endpoint['name']}")
                print(f"   URL: {endpoint['url']}")
                print(f"   Description: {endpoint['description']}")
                
                try:
                    response = requests.get(f"{base_url}{endpoint['url']}", headers=headers)
                    
                    print(f"   Status: {response.status_code}")
                    
                    if response.status_code == 200:
                        data = response.json()
                        
                        if isinstance(data, list):
                            print(f"   ✅ Success: {len(data)} items returned")
                            
                            # Show sample data structure
                            if data:
                                sample = data[0]
                                print(f"   📋 Sample fields: {list(sample.keys())}")
                                
                                # Check for specific fields we need
                                required_fields = ['logo', 'mayor_signature', 'pattern_image', 'name_am']
                                found_fields = [field for field in required_fields if field in sample]
                                missing_fields = [field for field in required_fields if field not in sample]
                                
                                if found_fields:
                                    print(f"   ✅ Found required fields: {found_fields}")
                                if missing_fields:
                                    print(f"   ⚠️ Missing fields: {missing_fields}")
                                    
                                # Show sample values for image fields
                                for field in ['logo', 'mayor_signature', 'pattern_image']:
                                    if field in sample and sample[field]:
                                        print(f"   🖼️ {field}: {sample[field]}")
                                    elif field in sample:
                                        print(f"   📭 {field}: (empty)")
                                        
                                # Show Amharic name
                                if 'name_am' in sample and sample['name_am']:
                                    print(f"   🏷️ name_am: {sample['name_am']}")
                                elif 'name_am' in sample:
                                    print(f"   📭 name_am: (empty)")
                                    
                                if 'city_name_am' in sample and sample['city_name_am']:
                                    print(f"   🏷️ city_name_am: {sample['city_name_am']}")
                                elif 'city_name_am' in sample:
                                    print(f"   📭 city_name_am: (empty)")
                                    
                        elif isinstance(data, dict) and 'results' in data:
                            results = data['results']
                            print(f"   ✅ Success: {len(results)} items in results")
                            
                            if results:
                                sample = results[0]
                                print(f"   📋 Sample fields: {list(sample.keys())}")
                        else:
                            print(f"   ✅ Success: Response received")
                            print(f"   📋 Response type: {type(data)}")
                            
                    elif response.status_code == 403:
                        print(f"   ❌ Forbidden: User doesn't have permission")
                    elif response.status_code == 401:
                        print(f"   ❌ Unauthorized: Authentication failed")
                    else:
                        print(f"   ❌ Error: {response.status_code}")
                        try:
                            error_data = response.json()
                            print(f"   📋 Error details: {error_data}")
                        except:
                            print(f"   📋 Error text: {response.text[:200]}")
                            
                except Exception as e:
                    print(f"   ❌ Request failed: {e}")
            
            # Test token data structure
            print("\n🔍 ANALYZING TOKEN DATA")
            print("-" * 50)
            
            # Decode token to see user data
            try:
                import jwt
                decoded_token = jwt.decode(access_token, options={"verify_signature": False})
                
                print("   📋 Token contains:")
                for key, value in decoded_token.items():
                    if key.endswith('_am'):
                        print(f"   🏷️ {key}: {value}")
                    elif key in ['tenant_id', 'tenant_name', 'tenant_type', 'parent_tenant_id', 'city_tenant_id']:
                        print(f"   🏢 {key}: {value}")
                    else:
                        print(f"   📝 {key}: {value}")
                        
            except ImportError:
                print("   ⚠️ PyJWT not available, cannot decode token")
            except Exception as e:
                print(f"   ⚠️ Token decode error: {e}")
                
        else:
            print(f"   ❌ Authentication failed: {auth_response.status_code}")
            try:
                error_data = auth_response.json()
                print(f"   📋 Error: {error_data}")
            except:
                print(f"   📋 Error text: {auth_response.text}")
                
    except Exception as e:
        print(f"   ❌ Authentication request failed: {e}")
    
    # Provide recommendations
    print("\n💡 RECOMMENDATIONS")
    print("=" * 50)
    print("1. **For Logo Display:**")
    print("   - Ensure tenants have uploaded logo images")
    print("   - Check that logo URLs are accessible")
    print("   - Verify CORS settings for media files")
    print()
    
    print("2. **For Amharic Names:**")
    print("   - Add Amharic names to tenant profiles")
    print("   - Update token generation to include Amharic names")
    print("   - Use token data as fallback when database is empty")
    print()
    
    print("3. **For API Permissions:**")
    print("   - Ensure user has proper permissions for tenant endpoints")
    print("   - Check tenant hierarchy access rules")
    print("   - Verify authentication token is valid")
    print()
    
    print("4. **For Frontend Integration:**")
    print("   - Use the working endpoints identified above")
    print("   - Handle both array and paginated responses")
    print("   - Implement proper error handling for 403/401 errors")


if __name__ == '__main__':
    test_tenant_endpoints()
