#!/usr/bin/env python
"""
Cleanup script to remove all transfer requests from all tenant schemas
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django_tenants.utils import schema_context
from tenants.models import Tenant
from workflows.models import CitizenTransferRequest

def cleanup_transfer_requests():
    """Remove all transfer requests from all tenant schemas"""
    print('🧹 Cleaning up transfer requests from all tenant schemas...')
    
    # Get all kebele tenants
    tenants = Tenant.objects.filter(type='kebele')
    print(f'Found {tenants.count()} kebele tenants')
    
    total_deleted = 0
    
    for tenant in tenants:
        print(f'Cleaning {tenant.name} ({tenant.schema_name})...')
        with schema_context(tenant.schema_name):
            transfer_count = CitizenTransferRequest.objects.count()
            if transfer_count > 0:
                CitizenTransferRequest.objects.all().delete()
                print(f'  ✅ Deleted {transfer_count} transfer requests from {tenant.name}')
                total_deleted += transfer_count
            else:
                print(f'  ✅ No transfer requests found in {tenant.name}')
    
    print(f'🎉 Cleanup complete! Total deleted: {total_deleted} transfer requests.')

if __name__ == '__main__':
    cleanup_transfer_requests()
