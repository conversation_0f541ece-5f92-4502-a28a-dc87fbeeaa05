#!/usr/bin/env python3
"""
Test Template Structure
"""

import urllib.request
import json
import base64

def test_template_structure():
    print("=== TEMPLATE STRUCTURE TEST ===")
    print("Testing the internal structure of fingerprint templates...")
    
    try:
        # Test capture
        capture_data = json.dumps({"thumb_type": "left"}).encode()
        
        req = urllib.request.Request(
            "http://localhost:8001/api/capture/fingerprint",
            data=capture_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print("📸 Capturing fingerprint...")
        response = urllib.request.urlopen(req, timeout=40)
        
        if response.getcode() == 200:
            result = json.loads(response.read().decode())
            
            if result.get('success'):
                data = result.get('data', {})
                
                print(f"✅ Capture successful!")
                
                # Get the main template
                template_b64 = data.get('template', '')
                biometric_template_b64 = data.get('biometric_template', '')
                
                print(f"\n📊 TEMPLATE SIZES:")
                print(f"   Main template: {len(template_b64)} chars")
                print(f"   Biometric template: {len(biometric_template_b64)} chars")
                
                # Decode main template
                if template_b64:
                    try:
                        template_json = base64.b64decode(template_b64).decode()
                        template_obj = json.loads(template_json)
                        
                        print(f"\n🔍 MAIN TEMPLATE STRUCTURE:")
                        print(f"   Version: {template_obj.get('version', 'N/A')}")
                        print(f"   Thumb type: {template_obj.get('thumb_type', 'N/A')}")
                        print(f"   Minutiae points: {len(template_obj.get('minutiae_points', []))}")
                        print(f"   Image dimensions: {template_obj.get('image_dimensions', {})}")
                        print(f"   Total pixels: {template_obj.get('total_pixels', 0)}")
                        print(f"   Data quality: {template_obj.get('data_quality', 0)}%")
                        print(f"   Extraction method: {template_obj.get('extraction_method', 'N/A')}")
                        print(f"   Template hash: {template_obj.get('template_hash', 'N/A')}")
                        print(f"   Frame size: {template_obj.get('frame_size', 0)}")
                        print(f"   Real capture: {template_obj.get('real_capture', False)}")
                        
                        # Show sample minutiae points
                        minutiae = template_obj.get('minutiae_points', [])
                        if len(minutiae) > 0:
                            print(f"\n📍 SAMPLE MINUTIAE POINTS:")
                            for i, point in enumerate(minutiae[:3]):
                                print(f"   Point {i+1}: x={point.get('x')}, y={point.get('y')}, angle={point.get('angle')}°")
                                print(f"            type={point.get('type')}, quality={point.get('quality', 0):.2f}")
                        
                        # Count success indicators
                        success_count = 0
                        
                        if len(minutiae) >= 15:
                            success_count += 1
                            print(f"   ✅ Good minutiae count: {len(minutiae)}")
                        
                        if template_obj.get('total_pixels', 0) > 50000:
                            success_count += 1
                            print(f"   ✅ Realistic pixel count: {template_obj.get('total_pixels', 0)}")
                        
                        if template_obj.get('data_quality', 0) > 70:
                            success_count += 1
                            print(f"   ✅ Good data quality: {template_obj.get('data_quality', 0)}%")
                        
                        if template_obj.get('frame_size', 0) > 5000:
                            success_count += 1
                            print(f"   ✅ Large frame size: {template_obj.get('frame_size', 0)} bytes")
                        
                        if template_obj.get('extraction_method') == 'futronic_jar_real_capture':
                            success_count += 1
                            print(f"   ✅ Real capture method confirmed")
                        
                        print(f"\n🎯 SUCCESS INDICATORS: {success_count}/5")
                        
                        if success_count >= 4:
                            print(f"🎉 EXCELLENT: Template contains comprehensive biometric data!")
                            return True
                        elif success_count >= 3:
                            print(f"✅ GOOD: Template has most required biometric data")
                            return True
                        elif success_count >= 2:
                            print(f"⚠️ PARTIAL: Template has some biometric data")
                            return False
                        else:
                            print(f"❌ POOR: Template lacks biometric data")
                            return False
                        
                    except Exception as decode_error:
                        print(f"❌ Main template decode error: {decode_error}")
                        
                        # Try biometric template as fallback
                        if biometric_template_b64:
                            try:
                                bio_json = base64.b64decode(biometric_template_b64).decode()
                                bio_obj = json.loads(bio_json)
                                print(f"\n🔍 BIOMETRIC TEMPLATE (FALLBACK):")
                                print(f"   Minutiae points: {len(bio_obj.get('minutiae_points', []))}")
                                print(f"   Total pixels: {bio_obj.get('total_pixels', 0)}")
                                return len(bio_obj.get('minutiae_points', [])) > 10
                            except:
                                print(f"❌ Biometric template also failed to decode")
                        
                        return False
                else:
                    print(f"❌ No main template data")
                    return False
            else:
                print(f"❌ Capture failed: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP error: {response.getcode()}")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

if __name__ == '__main__':
    success = test_template_structure()
    
    print(f"\n" + "="*50)
    if success:
        print(f"🎉 TEMPLATE STRUCTURE TEST PASSED!")
        print(f"✅ Templates now contain proper biometric data")
        print(f"✅ Minutiae points, dimensions, and quality included")
        print(f"✅ Ready for real biometric matching")
    else:
        print(f"❌ TEMPLATE STRUCTURE TEST FAILED!")
        print(f"💡 Templates may still need structural improvements")
    
    input("Press Enter to exit...")
