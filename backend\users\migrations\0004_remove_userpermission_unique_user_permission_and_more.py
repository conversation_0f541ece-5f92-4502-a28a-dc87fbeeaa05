# Generated by Django 4.2.7 on 2025-06-14 17:04

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0003_add_dynamic_roles'),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name='userpermission',
            name='unique_user_permission',
        ),
        migrations.AlterUniqueTogether(
            name='userpermission',
            unique_together={('user', 'permission')},
        ),
    ]
