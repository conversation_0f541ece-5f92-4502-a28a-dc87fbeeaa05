import { useState } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActionArea,
  alpha,
  useTheme,
} from '@mui/material';
import {
  LocationCity as CityIcon,
  Apartment as SubcityIcon,
  Home as KebeleIcon,
} from '@mui/icons-material';

const TenantTypeSelection = ({ selectedType, onSelect }) => {
  const theme = useTheme();

  const tenantTypes = [
    {
      id: 'city',
      name: 'City',
      description: 'Top level administration',
      icon: <CityIcon fontSize="large" />,
    },
    {
      id: 'subcity',
      name: 'Subcity',
      description: 'Middle level administration',
      icon: <SubcityIcon fontSize="large" />,
    },
    {
      id: 'kebele',
      name: '<PERSON><PERSON><PERSON>',
      description: 'Local level administration',
      icon: <KebeleIcon fontSize="large" />,
    },
  ];

  return (
    <Box>
      <Typography variant="h6" gutterBottom fontWeight="bold" sx={{ mb: 3 }}>
        Select Tenant Type
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Choose the type of tenant you want to register in the system. This determines the hierarchy level and permissions.
      </Typography>

      <Grid container spacing={3} sx={{ mt: 2 }}>
        {tenantTypes.map((type) => (
          <Grid item xs={12} sm={4} key={type.id}>
            <Card
              sx={{
                height: '100%',
                transition: 'all 0.3s ease',
                transform: selectedType === type.id ? 'scale(1.02)' : 'scale(1)',
                border: selectedType === type.id 
                  ? `2px solid ${theme.palette.primary.main}` 
                  : `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                boxShadow: selectedType === type.id ? theme.shadows[8] : theme.shadows[1],
                '&:hover': {
                  boxShadow: theme.shadows[6],
                  transform: 'scale(1.02)',
                },
              }}
            >
              <CardActionArea
                onClick={() => onSelect(type.id)}
                sx={{ 
                  height: '100%', 
                  display: 'flex', 
                  flexDirection: 'column',
                  p: 2,
                }}
              >
                <Box
                  sx={{
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mb: 2,
                    backgroundColor: selectedType === type.id 
                      ? alpha(theme.palette.primary.main, 0.1) 
                      : alpha(theme.palette.grey[500], 0.1),
                    color: selectedType === type.id 
                      ? theme.palette.primary.main 
                      : theme.palette.grey[700],
                    mx: 'auto',
                  }}
                >
                  {type.icon}
                </Box>
                <Typography variant="h6" component="h3" align="center" fontWeight="bold">
                  {type.name}
                </Typography>
                <Typography variant="body2" color="text.secondary" align="center" sx={{ mt: 1 }}>
                  {type.description}
                </Typography>
              </CardActionArea>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default TenantTypeSelection;
