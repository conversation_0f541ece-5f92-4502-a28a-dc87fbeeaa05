@echo off
echo 🔧 GoID Biometric Service - Windows LibUSB Setup
echo ================================================
echo.

echo 📋 This script will help you set up LibUSB for Futronic FS88H
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ This script must be run as Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo ✅ Running as Administrator
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo ✅ Python found
echo.

REM Install Python dependencies
echo 📦 Installing Python dependencies...
pip install Flask Flask-CORS requests libusb1 pyusb

echo.
echo 🔍 Checking for Futronic FS88H device...
echo.

REM Check if device is connected
python -c "
import usb.core
import usb.util

# Futronic FS88H VID:PID
VENDOR_ID = 0x1491
PRODUCT_ID = 0x0020

try:
    device = usb.core.find(idVendor=VENDOR_ID, idProduct=PRODUCT_ID)
    if device is None:
        print('❌ Futronic FS88H device not found')
        print('Please check:')
        print('1. Device is connected via USB')
        print('2. Device drivers are installed')
        print('3. Device is not being used by another application')
    else:
        print('✅ Futronic FS88H device detected!')
        print(f'Device: {device}')
        print(f'Vendor ID: 0x{device.idVendor:04x}')
        print(f'Product ID: 0x{device.idProduct:04x}')
except Exception as e:
    print(f'⚠️ Error checking device: {e}')
    print('This might be normal if drivers are not installed yet')
"

echo.
echo 📋 LibUSB Driver Installation:
echo ==============================
echo.
echo For Futronic FS88H to work with LibUSB, you need to:
echo.
echo 1. Download Zadig from: https://zadig.akeo.ie/
echo 2. Run Zadig as Administrator
echo 3. Connect your Futronic FS88H device
echo 4. In Zadig:
echo    - Select "Futronic FS88H" from the device list
echo    - Choose "libusb-win32" or "WinUSB" driver
echo    - Click "Install Driver" or "Replace Driver"
echo.
echo 5. After driver installation, restart this service
echo.

REM Create a test script
echo 🧪 Creating device test script...
echo import sys > test_device.py
echo import logging >> test_device.py
echo logging.basicConfig(level=logging.INFO) >> test_device.py
echo. >> test_device.py
echo try: >> test_device.py
echo     from libusb_futronic import LibUSBFutronicFS88H >> test_device.py
echo     device = LibUSBFutronicFS88H() >> test_device.py
echo     if device.initialize(): >> test_device.py
echo         status = device.get_device_status() >> test_device.py
echo         print(f"Device Status: {status}") >> test_device.py
echo         if status['real_device']: >> test_device.py
echo             print("✅ Real device detected and working!") >> test_device.py
echo         else: >> test_device.py
echo             print("⚠️ Running in simulation mode") >> test_device.py
echo     else: >> test_device.py
echo         print("❌ Device initialization failed") >> test_device.py
echo except Exception as e: >> test_device.py
echo     print(f"❌ Error: {e}") >> test_device.py

echo.
echo ✅ Setup complete!
echo.
echo 📋 Next steps:
echo 1. Install LibUSB driver using Zadig (see instructions above)
echo 2. Test device: python test_device.py
echo 3. Start service: python biometric_service.py
echo.
echo 🔗 Useful links:
echo - Zadig (driver installer): https://zadig.akeo.ie/
echo - LibUSB documentation: https://libusb.info/
echo.
pause
