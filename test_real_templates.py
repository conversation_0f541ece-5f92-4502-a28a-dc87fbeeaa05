#!/usr/bin/env python3
"""
Test FMatcher with real template data
"""

import requests
import base64
import os

def test_with_real_templates():
    """Test FMatcher with actual template files"""
    print("🧪 Testing FMatcher with real templates...")
    
    iso_file = os.path.join("local-biometric-service", "fingerPrint", ".iso")
    
    if not os.path.exists(iso_file):
        print("❌ No real template file found at", iso_file)
        return False
    
    try:
        # Read the actual ANSI/ISO template
        with open(iso_file, 'rb') as f:
            real_template_bytes = f.read()
        
        print(f"📄 Real template size: {len(real_template_bytes)} bytes")
        
        # Convert to base64 for API
        real_template_b64 = base64.b64encode(real_template_bytes).decode()
        
        print(f"📄 Base64 template size: {len(real_template_b64)} chars")
        
        # Test 1: Same template vs itself (should be 100% match)
        response = requests.post(
            "http://localhost:8002/api/duplicate/check",
            json={
                'template': real_template_b64,
                'stored_templates': [real_template_b64],  # Same template
                'threshold': 40
            },
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Real template self-comparison:", result)
            
            if result['success'] and result['data']['has_duplicates']:
                highest_score = result['data']['highest_score']
                print(f"🎉 SUCCESS! Real template matched itself with score: {highest_score}")
                return True
            else:
                print("❌ FAILED: Real template should match itself but didn't")
                return False
        else:
            print(f"❌ API call failed: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_fmatcher_jar_directly():
    """Test FMatcher.jar directly with real templates"""
    print("\n🧪 Testing FMatcher.jar directly...")
    
    iso_file = os.path.join("local-biometric-service", "fingerPrint", ".iso")
    
    if not os.path.exists(iso_file):
        print("❌ No real template file found")
        return False
    
    try:
        import subprocess
        import shutil
        
        fmatcher_dir = os.path.join("local-biometric-service", "fpmatcher")
        temp1 = os.path.join(fmatcher_dir, "temp_test1.iso")
        temp2 = os.path.join(fmatcher_dir, "temp_test2.iso")
        
        # Copy the same template twice
        shutil.copy2(iso_file, temp1)
        shutil.copy2(iso_file, temp2)
        
        # Run FMatcher.jar directly
        result = subprocess.run(
            ['java', '-jar', 'FMatcher.jar', 'temp_test1.iso', 'temp_test2.iso'],
            cwd=fmatcher_dir,
            capture_output=True,
            text=True,
            timeout=10
        )
        
        # Clean up
        try:
            os.remove(temp1)
            os.remove(temp2)
        except:
            pass
        
        if result.returncode == 0:
            score = float(result.stdout.strip())
            print(f"✅ FMatcher.jar direct test score: {score}")
            
            if score > 100:  # High score indicates good match
                print(f"🎉 SUCCESS! FMatcher.jar can match real templates (score: {score})")
                return True
            else:
                print(f"⚠️ Low score for identical templates: {score}")
                return False
        else:
            print(f"❌ FMatcher.jar failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Testing Real Template Matching")
    print("=" * 50)
    
    # Test with API
    api_ok = test_with_real_templates()
    
    # Test FMatcher.jar directly
    jar_ok = test_fmatcher_jar_directly()
    
    print("\n" + "=" * 50)
    print("📊 Results:")
    print(f"API Test: {'✅ OK' if api_ok else '❌ FAILED'}")
    print(f"JAR Test: {'✅ OK' if jar_ok else '❌ FAILED'}")
    
    if api_ok and jar_ok:
        print("\n🎉 FMatcher is working with real templates!")
        print("   The issue might be in the Django integration or data format.")
    elif jar_ok and not api_ok:
        print("\n⚠️ FMatcher.jar works, but API integration has issues.")
    elif not jar_ok:
        print("\n❌ FMatcher.jar itself is not working with real templates.")
        print("   This might be a template format issue.")
