from django.core.management.base import BaseCommand
from django.utils import timezone
from django_tenants.utils import schema_context
from tenants.models import Tenant
from workflows.models import CitizenClearanceRequest
import os
from django.conf import settings
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT


class Command(BaseCommand):
    help = 'Regenerate PDF clearance letters for existing text-based clearances'

    def handle(self, *args, **options):
        self.stdout.write('Starting clearance PDF regeneration...')
        
        # Get all kebele tenants
        kebele_tenants = Tenant.objects.filter(type='kebele')
        
        total_updated = 0
        
        for tenant in kebele_tenants:
            self.stdout.write(f'Processing tenant: {tenant.name}')
            
            with schema_context(tenant.schema_name):
                # Find clearances with .txt files
                txt_clearances = CitizenClearanceRequest.objects.filter(
                    clearance_letter_path__endswith='.txt',
                    status='issued'
                )
                
                for clearance in txt_clearances:
                    try:
                        # Generate new PDF
                        new_pdf_path = self.generate_clearance_pdf(clearance)
                        
                        # Update the record
                        clearance.clearance_letter_path = new_pdf_path
                        clearance.save()
                        
                        self.stdout.write(f'  ✅ Updated {clearance.clearance_id}: {new_pdf_path}')
                        total_updated += 1
                        
                    except Exception as e:
                        self.stdout.write(f'  ❌ Failed to update {clearance.clearance_id}: {e}')
        
        self.stdout.write(f'✅ Completed! Updated {total_updated} clearance records.')

    def generate_clearance_pdf(self, clearance_request):
        """Generate professional PDF clearance letter."""
        # Create clearance letters directory if it doesn't exist
        clearance_dir = os.path.join(settings.MEDIA_ROOT, 'clearance_letters')
        os.makedirs(clearance_dir, exist_ok=True)

        # Generate filename
        filename = f"{clearance_request.clearance_id}_clearance_letter.pdf"
        file_path = os.path.join(clearance_dir, filename)

        # Create PDF document
        doc = SimpleDocTemplate(
            file_path,
            pagesize=A4,
            rightMargin=0.75*inch,
            leftMargin=0.75*inch,
            topMargin=1*inch,
            bottomMargin=1*inch
        )

        # Get styles
        styles = getSampleStyleSheet()

        # Custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.darkblue,
            fontName='Helvetica-Bold'
        )

        header_style = ParagraphStyle(
            'CustomHeader',
            parent=styles['Heading2'],
            fontSize=14,
            spaceAfter=20,
            alignment=TA_CENTER,
            textColor=colors.darkred,
            fontName='Helvetica-Bold'
        )

        body_style = ParagraphStyle(
            'CustomBody',
            parent=styles['Normal'],
            fontSize=12,
            spaceAfter=12,
            alignment=TA_LEFT,
            fontName='Helvetica'
        )

        signature_style = ParagraphStyle(
            'SignatureStyle',
            parent=styles['Normal'],
            fontSize=11,
            spaceAfter=8,
            alignment=TA_LEFT,
            fontName='Helvetica'
        )

        # Build content
        story = []

        # Government Header
        gov_header = f"""
        <para align=center>
        <b>FEDERAL DEMOCRATIC REPUBLIC OF ETHIOPIA</b><br/>
        <b>{clearance_request.source_kebele.parent.parent.name.upper() if clearance_request.source_kebele.parent and clearance_request.source_kebele.parent.parent else 'CITY ADMINISTRATION'}</b><br/>
        <b>{clearance_request.source_kebele.parent.name.upper() if clearance_request.source_kebele.parent else 'SUBCITY ADMINISTRATION'}</b><br/>
        <b>{clearance_request.source_kebele.name.upper()}</b>
        </para>
        """
        story.append(Paragraph(gov_header, header_style))
        story.append(Spacer(1, 20))

        # Title
        story.append(Paragraph("CITIZEN CLEARANCE LETTER", title_style))
        story.append(Spacer(1, 20))

        # Document info table
        doc_info_data = [
            ['Clearance ID:', clearance_request.clearance_id],
            ['Date of Issue:', timezone.now().strftime('%B %d, %Y')],
            ['Reference No:', f"CL-{clearance_request.clearance_id}-{timezone.now().year}"]
        ]

        doc_info_table = Table(doc_info_data, colWidths=[2*inch, 3*inch])
        doc_info_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
        ]))
        story.append(doc_info_table)
        story.append(Spacer(1, 30))

        # Main content
        story.append(Paragraph("<b>TO WHOM IT MAY CONCERN:</b>", body_style))
        story.append(Spacer(1, 15))

        main_content = f"""
        This is to certify that <b>{clearance_request.citizen_name}</b> 
        (Digital ID: <b>{clearance_request.citizen_digital_id or 'N/A'}</b>) 
        has been officially cleared to relocate from <b>{clearance_request.source_kebele.name}</b> 
        to <b>{clearance_request.destination_location}</b>.
        """
        story.append(Paragraph(main_content, body_style))
        story.append(Spacer(1, 15))

        # Reason details
        reason_content = f"""
        <b>Reason for clearance:</b> {clearance_request.get_clearance_reason_display()}<br/>
        <b>Details:</b> {clearance_request.reason_description}
        """
        story.append(Paragraph(reason_content, body_style))
        story.append(Spacer(1, 15))

        # Timeline
        timeline_content = f"""
        This clearance was requested on <b>{clearance_request.created_at.strftime('%B %d, %Y')}</b> 
        and approved on <b>{clearance_request.reviewed_at.strftime('%B %d, %Y') if clearance_request.reviewed_at else 'N/A'}</b>.
        """
        story.append(Paragraph(timeline_content, body_style))
        story.append(Spacer(1, 15))

        # Certification statement
        cert_content = """
        The citizen is in good standing and has no pending obligations 
        with the local administration. This clearance letter is issued 
        in accordance with the applicable laws and regulations.
        """
        story.append(Paragraph(cert_content, body_style))
        story.append(Spacer(1, 40))

        # Signature section
        signature_data = [
            ['', ''],
            ['Issued by:', ''],
            [f'{clearance_request.issued_by.first_name} {clearance_request.issued_by.last_name}', ''],
            [f'Position: {clearance_request.issued_by.role.replace("_", " ").title()}', ''],
            [f'Date: {timezone.now().strftime("%B %d, %Y")}', ''],
            ['', ''],
            ['_________________________', 'OFFICIAL SEAL'],
            ['Signature', ''],
        ]

        signature_table = Table(signature_data, colWidths=[3*inch, 2*inch])
        signature_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('FONTNAME', (0, 1), (0, 4), 'Helvetica-Bold'),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('ALIGN', (1, 6), (1, 7), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('FONTSIZE', (1, 6), (1, 7), 10),
            ('FONTNAME', (1, 6), (1, 6), 'Helvetica-Bold'),
        ]))
        story.append(signature_table)
        story.append(Spacer(1, 30))

        # Footer
        footer_content = f"""
        <para align=center>
        <i>This document is computer generated and does not require a physical signature when accompanied by official seal.</i><br/>
        <b>{clearance_request.source_kebele.name}</b> | 
        <b>{clearance_request.source_kebele.parent.name if clearance_request.source_kebele.parent else ''}</b> | 
        <b>{clearance_request.source_kebele.parent.parent.name if clearance_request.source_kebele.parent and clearance_request.source_kebele.parent.parent else ''}</b>
        </para>
        """
        story.append(Paragraph(footer_content, signature_style))

        # Build PDF
        doc.build(story)

        # Return relative path for storage in database
        return f"clearance_letters/{filename}"
