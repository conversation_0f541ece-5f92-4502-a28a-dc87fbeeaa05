#!/bin/bash

# Debug Clerk Submit for Approval 403 Error <PERSON>
# This script diagnoses why clerks are getting 403 when submitting ID cards for approval

echo "🔧 Debugging Clerk Submit for Approval 403 Error"
echo "================================================"

echo "📍 Issue Description:"
echo "- Clerk finishes ID card registration"
echo "- Tries to send ID card to kebele leader for approval"
echo "- Gets 403 Forbidden error"
echo "- Error occurs in both IDCardView.jsx and IDCardList.jsx"
echo ""

# Step 1: Check current authentication and user role
echo "📍 Step 1: Checking current user authentication..."

AUTH_RESPONSE=$(curl -s -X POST http://************:8000/api/auth/token/ \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}')

if echo "$AUTH_RESPONSE" | grep -q "access"; then
    echo "✅ Authentication successful"
    
    TOKEN=$(echo "$AUTH_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin)['access'])" 2>/dev/null)
    
    if [ ! -z "$TOKEN" ]; then
        echo "✅ Token extracted successfully"
        
        # Decode token to check user role
        echo ""
        echo "📍 Step 2: Checking current user role..."
        python3 -c "
import base64
import json

token = '$TOKEN'
try:
    # Decode JWT payload
    payload = token.split('.')[1]
    # Add padding if needed
    payload += '=' * (4 - len(payload) % 4)
    decoded = base64.b64decode(payload)
    data = json.loads(decoded)
    
    print('Current user details:')
    print(f'  User ID: {data.get(\"user_id\")}')
    print(f'  Email: {data.get(\"email\")}')
    print(f'  Role: {data.get(\"role\")}')
    print(f'  Tenant ID: {data.get(\"tenant_id\")}')
    print(f'  Is Superuser: {data.get(\"is_superuser\")}')
    
    role = data.get('role')
    is_superuser = data.get('is_superuser', False)
    
    # Check if this user should be able to submit for approval
    allowed_roles = ['clerk', 'superadmin']
    can_submit = role in allowed_roles or is_superuser
    
    print(f'')
    print(f'Permission check:')
    print(f'  Allowed roles for submit: {allowed_roles}')
    print(f'  User role: {role}')
    print(f'  Is superuser: {is_superuser}')
    print(f'  Should be able to submit: {can_submit}')
    
except Exception as e:
    print(f'Error decoding token: {e}')
"
        
        # Step 3: Test the actual submit for approval endpoint
        echo ""
        echo "📍 Step 3: Testing submit for approval endpoint..."
        
        # Get a kebele tenant
        KEBELE_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
          "http://************:8000/api/tenants/?type=kebele")
        
        KEBELE_ID=$(echo "$KEBELE_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, dict) and 'results' in data and len(data['results']) > 0:
        print(data['results'][0]['id'])
    else:
        print('none')
except:
    print('none')
" 2>/dev/null)
        
        if [ "$KEBELE_ID" != "none" ]; then
            echo "Using kebele ID: $KEBELE_ID"
            
            # Get ID cards in this kebele
            echo "Getting ID cards..."
            IDCARDS_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
              http://************:8000/api/tenants/$KEBELE_ID/idcards/)
            
            echo "ID cards response preview:"
            echo "$IDCARDS_RESPONSE" | head -c 200
            echo ""
            
            # Try to find a draft ID card
            DRAFT_IDCARD_ID=$(echo "$IDCARDS_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    cards = []
    if isinstance(data, list):
        cards = data
    elif isinstance(data, dict) and 'results' in data:
        cards = data['results']
    
    # Look for draft cards first
    for card in cards:
        if card.get('status') == 'draft':
            print(card['id'])
            exit()
    
    # If no draft cards, use any card
    if cards:
        print(cards[0]['id'])
    else:
        print('none')
except Exception as e:
    print('none')
" 2>/dev/null)
            
            if [ "$DRAFT_IDCARD_ID" != "none" ]; then
                echo "Testing submit for approval with ID card: $DRAFT_IDCARD_ID"
                
                # Test submit for approval
                SUBMIT_RESPONSE=$(curl -s -X POST \
                  -H "Authorization: Bearer $TOKEN" \
                  -H "Content-Type: application/json" \
                  -d '{"action":"submit_for_approval","comment":"Test submission by clerk"}' \
                  http://************:8000/api/tenants/$KEBELE_ID/idcards/$DRAFT_IDCARD_ID/approval_action/ \
                  -w "HTTP_STATUS:%{http_code}")
                
                HTTP_STATUS=$(echo "$SUBMIT_RESPONSE" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
                RESPONSE_BODY=$(echo "$SUBMIT_RESPONSE" | sed 's/HTTP_STATUS:[0-9]*$//')
                
                echo "Submit for approval status: $HTTP_STATUS"
                echo "Response: $RESPONSE_BODY"
                
                if [ "$HTTP_STATUS" = "200" ]; then
                    echo "✅ SUCCESS! Submit for approval working"
                elif [ "$HTTP_STATUS" = "403" ]; then
                    echo "❌ 403 Forbidden - Permission issue confirmed"
                    echo "This is the exact error the clerk is experiencing"
                elif [ "$HTTP_STATUS" = "400" ]; then
                    echo "⚠️  400 Bad Request - Check ID card status or data"
                else
                    echo "⚠️  Unexpected status: $HTTP_STATUS"
                fi
                
            else
                echo "No ID cards found for testing"
            fi
        else
            echo "No kebele tenant found"
        fi
        
    else
        echo "❌ Could not extract token"
    fi
else
    echo "❌ Authentication failed"
    echo "Response: $AUTH_RESPONSE"
fi

# Step 4: Create a proper clerk user for testing
echo ""
echo "📍 Step 4: Creating/checking clerk user..."

docker-compose exec -T backend python manage.py shell << 'EOF'
from django.contrib.auth import get_user_model
from tenants.models import Tenant
from django_tenants.utils import schema_context

User = get_user_model()

print("=== CHECKING/CREATING CLERK USER ===")

# Get a kebele tenant
try:
    kebele_tenant = Tenant.objects.filter(type='kebele').first()
    if not kebele_tenant:
        print("❌ No kebele tenant found!")
        exit()
    
    print(f"✅ Found kebele tenant: {kebele_tenant.name} (ID: {kebele_tenant.id})")
    
    # Check/create clerk user in the kebele's schema
    with schema_context(kebele_tenant.schema_name):
        # Check if clerk already exists
        existing_clerk = User.objects.filter(role='clerk').first()
        
        if existing_clerk:
            print(f"✅ Clerk already exists: {existing_clerk.email}")
            clerk_user = existing_clerk
        else:
            # Create new clerk
            clerk_user = User.objects.create_user(
                username='clerk',
                email='<EMAIL>',
                password='clerk123',
                first_name='Test',
                last_name='Clerk',
                role='clerk',
                is_active=True
            )
            print(f"✅ Created clerk: {clerk_user.email}")
        
        print(f"   User ID: {clerk_user.id}")
        print(f"   Role: {clerk_user.role}")
        print(f"   Active: {clerk_user.is_active}")
        print(f"   Superuser: {clerk_user.is_superuser}")
        
        # Check if there are draft ID cards
        from idcards.models import IDCard
        draft_cards = IDCard.objects.filter(status='draft')
        print(f"   Draft ID cards in this tenant: {draft_cards.count()}")
        
        if draft_cards.count() == 0:
            print("   ⚠️  No draft ID cards available for testing")
        else:
            print(f"   ✅ Found {draft_cards.count()} draft ID cards for testing")

except Exception as e:
    print(f"❌ Error checking/creating clerk: {e}")
    import traceback
    traceback.print_exc()

EOF

# Step 5: Test with clerk credentials
echo ""
echo "📍 Step 5: Testing with clerk credentials..."

CLERK_AUTH_RESPONSE=$(curl -s -X POST http://************:8000/api/auth/token/ \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"clerk123"}')

if echo "$CLERK_AUTH_RESPONSE" | grep -q "access"; then
    echo "✅ Clerk authentication successful"
    
    CLERK_TOKEN=$(echo "$CLERK_AUTH_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin)['access'])" 2>/dev/null)
    
    if [ ! -z "$CLERK_TOKEN" ]; then
        echo "✅ Clerk token extracted"
        
        # Test submit for approval with clerk token
        KEBELE_RESPONSE=$(curl -s -H "Authorization: Bearer $CLERK_TOKEN" \
          "http://************:8000/api/tenants/?type=kebele")
        
        KEBELE_ID=$(echo "$KEBELE_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, dict) and 'results' in data and len(data['results']) > 0:
        print(data['results'][0]['id'])
    else:
        print('none')
except:
    print('none')
" 2>/dev/null)
        
        if [ "$KEBELE_ID" != "none" ]; then
            # Get ID cards with clerk token
            CLERK_IDCARDS_RESPONSE=$(curl -s -H "Authorization: Bearer $CLERK_TOKEN" \
              http://************:8000/api/tenants/$KEBELE_ID/idcards/)
            
            CLERK_IDCARD_ID=$(echo "$CLERK_IDCARDS_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    cards = []
    if isinstance(data, list):
        cards = data
    elif isinstance(data, dict) and 'results' in data:
        cards = data['results']
    
    if cards:
        print(cards[0]['id'])
    else:
        print('none')
except:
    print('none')
" 2>/dev/null)
            
            if [ "$CLERK_IDCARD_ID" != "none" ]; then
                echo "Testing clerk submit for approval..."
                
                CLERK_SUBMIT_RESPONSE=$(curl -s -X POST \
                  -H "Authorization: Bearer $CLERK_TOKEN" \
                  -H "Content-Type: application/json" \
                  -d '{"action":"submit_for_approval","comment":"Clerk test submission"}' \
                  http://************:8000/api/tenants/$KEBELE_ID/idcards/$CLERK_IDCARD_ID/approval_action/ \
                  -w "HTTP_STATUS:%{http_code}")
                
                CLERK_STATUS=$(echo "$CLERK_SUBMIT_RESPONSE" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
                CLERK_RESPONSE_BODY=$(echo "$CLERK_SUBMIT_RESPONSE" | sed 's/HTTP_STATUS:[0-9]*$//')
                
                echo "Clerk submit status: $CLERK_STATUS"
                
                if [ "$CLERK_STATUS" = "200" ]; then
                    echo "✅ SUCCESS! Clerk can submit for approval"
                elif [ "$CLERK_STATUS" = "403" ]; then
                    echo "❌ Clerk still getting 403 - deeper issue"
                    echo "Response: $CLERK_RESPONSE_BODY"
                else
                    echo "⚠️  Clerk status: $CLERK_STATUS"
                    echo "Response: $CLERK_RESPONSE_BODY"
                fi
            fi
        fi
    fi
else
    echo "❌ Clerk authentication failed"
fi

echo ""
echo "✅ Clerk submit for approval debugging completed!"
echo ""
echo "🧪 Manual testing steps:"
echo "1. Login as clerk: <EMAIL> / clerk123"
echo "2. Go to ID Cards list"
echo "3. Find ID card with 'draft' status"
echo "4. Click 'Send for Approval' button"
echo "5. Should work without 403 error"
echo ""
echo "💡 If still getting 403:"
echo "- Check if user role is actually 'clerk'"
echo "- Verify ID card status is 'draft'"
echo "- Check backend logs for detailed error"
echo "- Ensure user is in correct tenant schema"
