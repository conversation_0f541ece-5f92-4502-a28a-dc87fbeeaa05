<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test JWT Groups and Navigation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .nav-item {
            padding: 8px;
            margin: 4px 0;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
        .group-test {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 JWT Groups and Navigation Test</h1>
        
        <div class="test-section info">
            <h3>📋 Group-Based Navigation Test</h3>
            <p>This page tests the new group-based navigation system:</p>
            <ul>
                <li><strong>Super Admin:</strong> Dashboard, Tenants, System Users, System Settings</li>
                <li><strong>City Admin:</strong> Dashboard, Citizen Directory, Subcity Users, Reports</li>
                <li><strong>SubCity Admin:</strong> Dashboard, Citizens, ID Cards, Print Queue, Service Requests, Kebele Users, Reports</li>
                <li><strong>Kebele Leader:</strong> Dashboard, Citizens, ID Cards, Clearance, Transfer</li>
                <li><strong>Clerk:</strong> Dashboard, Citizens, ID Cards</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🔐 Test Login and JWT Token</h3>
            <div class="group-test">
                <h4>Super Admin Test</h4>
                <button onclick="testLogin('<EMAIL>', 'admin123', 'Super Admin')">Test Super Admin Login</button>
                <div id="superadmin-result"></div>
            </div>
            
            <div class="group-test">
                <h4>Custom Test User</h4>
                <input type="email" id="test-email" placeholder="Email" value="<EMAIL>">
                <input type="password" id="test-password" placeholder="Password" value="password123">
                <input type="text" id="expected-group" placeholder="Expected Group" value="city_admin">
                <button onclick="testCustomLogin()">Test Custom Login</button>
                <div id="custom-result"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>🧭 Navigation Simulation</h3>
            <button onclick="simulateNavigation()">Simulate Frontend Navigation</button>
            <div id="navigation-result"></div>
        </div>

        <div class="test-section">
            <h3>🔍 Current Token Analysis</h3>
            <button onclick="analyzeCurrentToken()">Analyze Current Token</button>
            <div id="token-analysis"></div>
        </div>
    </div>

    <script>
        let currentToken = null;
        let currentUser = null;

        async function testLogin(email, password, userType) {
            const resultDiv = document.getElementById('superadmin-result');
            resultDiv.innerHTML = '<p>🔄 Testing login...</p>';

            try {
                const response = await fetch('http://localhost:8000/api/auth/token/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });

                if (response.ok) {
                    const data = await response.json();
                    currentToken = data.access;
                    
                    // Decode JWT token
                    const payload = JSON.parse(atob(currentToken.split('.')[1]));
                    currentUser = payload;
                    
                    const groups = payload.groups || [];
                    const permissions = payload.permissions || [];
                    
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ ${userType} Login Successful!</h4>
                            <p><strong>Email:</strong> ${payload.email}</p>
                            <p><strong>Role:</strong> ${payload.role}</p>
                            <p><strong>Is Superuser:</strong> ${payload.is_superuser}</p>
                            <p><strong>Groups:</strong> ${groups.join(', ') || 'None'}</p>
                            <p><strong>Permissions Count:</strong> ${permissions.length}</p>
                            
                            <details>
                                <summary>View All Permissions</summary>
                                <pre>${JSON.stringify(permissions, null, 2)}</pre>
                            </details>
                        </div>
                    `;
                } else {
                    const error = await response.text();
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Login Failed</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${error}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Network Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function testCustomLogin() {
            const email = document.getElementById('test-email').value;
            const password = document.getElementById('test-password').value;
            const expectedGroup = document.getElementById('expected-group').value;
            
            await testLogin(email, password, 'Custom User');
            
            // Update result to show group check
            const resultDiv = document.getElementById('custom-result');
            if (currentUser) {
                const groups = currentUser.groups || [];
                const hasExpectedGroup = groups.includes(expectedGroup);
                
                resultDiv.innerHTML = `
                    <div class="${hasExpectedGroup ? 'success' : 'error'}">
                        <h4>${hasExpectedGroup ? '✅' : '❌'} Group Check</h4>
                        <p><strong>Expected Group:</strong> ${expectedGroup}</p>
                        <p><strong>User Groups:</strong> ${groups.join(', ') || 'None'}</p>
                        <p><strong>Has Expected Group:</strong> ${hasExpectedGroup}</p>
                    </div>
                `;
            }
        }

        function simulateNavigation() {
            const resultDiv = document.getElementById('navigation-result');
            
            if (!currentUser) {
                resultDiv.innerHTML = '<div class="error">Please login first</div>';
                return;
            }

            const groups = currentUser.groups || [];
            const permissions = currentUser.permissions || [];
            const isSuper = currentUser.is_superuser;

            // Simulate navigation logic
            const navigation = simulateNavigationLogic(groups, permissions, isSuper);
            
            resultDiv.innerHTML = `
                <div class="success">
                    <h4>🧭 Simulated Navigation</h4>
                    <p><strong>User Groups:</strong> ${groups.join(', ') || 'None'}</p>
                    <p><strong>Generated Navigation:</strong></p>
                    <div>
                        ${navigation.map(item => `<div class="nav-item">${item}</div>`).join('')}
                    </div>
                </div>
            `;
        }

        function simulateNavigationLogic(groups, permissions, isSuper) {
            const navigation = ['Dashboard']; // Always present
            
            function hasPermission(perm) {
                return permissions.includes(perm);
            }

            // Super Admin Navigation
            if (isSuper || groups.includes('super_admin')) {
                if (hasPermission('manage_tenants')) navigation.push('Tenants');
                if (hasPermission('manage_all_users')) navigation.push('System Users');
                if (hasPermission('manage_system_settings')) navigation.push('System Settings');
                return navigation;
            }

            // City Admin Navigation
            if (groups.includes('city_admin')) {
                if (hasPermission('view_child_subcities_data')) navigation.push('Citizen Directory');
                if (hasPermission('create_subcity_users')) navigation.push('Subcity Users');
                if (hasPermission('view_city_reports')) navigation.push('Reports');
                return navigation;
            }

            // SubCity Admin Navigation
            if (groups.includes('subcity_admin')) {
                if (hasPermission('view_child_kebeles_data')) navigation.push('Citizens');
                if (hasPermission('view_id_cards_list')) navigation.push('ID Cards');
                if (hasPermission('print_id_cards')) navigation.push('Print Queue');
                if (hasPermission('view_service_requests')) navigation.push('Service Requests');
                if (hasPermission('create_kebele_users')) navigation.push('Kebele Users');
                if (hasPermission('view_subcity_reports')) navigation.push('Reports');
                return navigation;
            }

            // Kebele Leader Navigation
            if (groups.includes('kebele_leader')) {
                if (hasPermission('view_citizens_list')) navigation.push('Citizens');
                if (hasPermission('view_id_cards_list')) navigation.push('ID Cards');
                if (hasPermission('create_clearances')) navigation.push('Clearance');
                if (hasPermission('create_transfers')) navigation.push('Transfer');
                return navigation;
            }

            // Clerk Navigation
            if (groups.includes('clerk')) {
                if (hasPermission('register_citizens')) navigation.push('Citizens');
                if (hasPermission('generate_id_cards')) navigation.push('ID Cards');
                return navigation;
            }

            return navigation;
        }

        function analyzeCurrentToken() {
            const resultDiv = document.getElementById('token-analysis');
            
            if (!currentToken) {
                resultDiv.innerHTML = '<div class="error">No token available. Please login first.</div>';
                return;
            }

            try {
                const payload = JSON.parse(atob(currentToken.split('.')[1]));
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>🔍 JWT Token Analysis</h4>
                        <pre>${JSON.stringify(payload, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Token Decode Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
