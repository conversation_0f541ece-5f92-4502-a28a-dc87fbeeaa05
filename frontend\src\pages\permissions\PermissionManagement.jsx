import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Grid,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch,
  FormControlLabel,
  Alert,
  Tabs,
  Tab,
  Autocomplete
} from '@mui/material';
import { Add, Edit, Delete, Security } from '@mui/icons-material';

const PermissionManagement = () => {
  const [tabValue, setTabValue] = useState(0);
  const [permissions, setPermissions] = useState([]);
  const [users, setUsers] = useState([]);
  const [userPermissions, setUserPermissions] = useState([]);
  const [rolePermissions, setRolePermissions] = useState({});
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState(''); // 'grant', 'revoke', 'role'
  const [selectedUser, setSelectedUser] = useState(null);
  const [selectedPermissions, setSelectedPermissions] = useState([]);
  const [selectedRole, setSelectedRole] = useState('');
  const [reason, setReason] = useState('');
  const [loading, setLoading] = useState(false);
  const [alert, setAlert] = useState({ show: false, message: '', severity: 'info' });

  const roles = [
    { value: 'superadmin', label: 'Super Admin' },
    { value: 'city_admin', label: 'City Admin' },
    { value: 'subcity_admin', label: 'Sub City Admin' },
    { value: 'kebele_admin', label: 'Kebele Admin' },
    { value: 'kebele_leader', label: 'Kebele Leader' },
    { value: 'clerk', label: 'Clerk' }
  ];

  const permissionCategories = {
    'citizens': 'Citizen Management',
    'id_cards': 'ID Card Management',
    'users': 'User Management',
    'tenants': 'Tenant Management',
    'reports': 'Reports & Analytics',
    'system': 'System Administration'
  };

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {
      // Simulate API calls - replace with actual API calls
      const mockPermissions = [
        { id: 1, codename: 'view_citizens', name: 'View Citizens', category: 'citizens' },
        { id: 2, codename: 'create_citizens', name: 'Create Citizens', category: 'citizens' },
        { id: 3, codename: 'edit_citizens', name: 'Edit Citizens', category: 'citizens' },
        { id: 4, codename: 'view_id_cards', name: 'View ID Cards', category: 'id_cards' },
        { id: 5, codename: 'create_id_cards', name: 'Create ID Cards', category: 'id_cards' },
        { id: 6, codename: 'approve_id_cards', name: 'Approve ID Cards', category: 'id_cards' },
        { id: 7, codename: 'print_id_cards', name: 'Print ID Cards', category: 'id_cards' },
        { id: 8, codename: 'manage_permissions', name: 'Manage Permissions', category: 'users' }
      ];

      const mockUsers = [
        { id: 1, email: '<EMAIL>', first_name: 'John', last_name: 'Doe', role: 'clerk' },
        { id: 2, email: '<EMAIL>', first_name: 'Jane', last_name: 'Smith', role: 'kebele_leader' },
        { id: 3, email: '<EMAIL>', first_name: 'Bob', last_name: 'Johnson', role: 'subcity_admin' }
      ];

      const mockRolePermissions = {
        'clerk': ['view_citizens', 'create_citizens', 'edit_citizens', 'view_id_cards', 'create_id_cards'],
        'kebele_leader': ['view_citizens', 'view_id_cards', 'approve_id_cards'],
        'subcity_admin': ['view_citizens', 'view_id_cards', 'approve_id_cards', 'print_id_cards']
      };

      setPermissions(mockPermissions);
      setUsers(mockUsers);
      setRolePermissions(mockRolePermissions);
    } catch (error) {
      showAlert('Error fetching data', 'error');
    } finally {
      setLoading(false);
    }
  };

  const showAlert = (message, severity = 'info') => {
    setAlert({ show: true, message, severity });
    setTimeout(() => setAlert({ show: false, message: '', severity: 'info' }), 5000);
  };

  const handleGrantPermission = async () => {
    if (!selectedUser || selectedPermissions.length === 0) {
      showAlert('Please select a user and at least one permission', 'error');
      return;
    }

    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      showAlert(`Successfully granted ${selectedPermissions.length} permission(s) to ${selectedUser.email}`, 'success');
      setOpenDialog(false);
      resetDialog();
    } catch (error) {
      showAlert('Error granting permissions', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateRolePermissions = async () => {
    if (!selectedRole || selectedPermissions.length === 0) {
      showAlert('Please select a role and at least one permission', 'error');
      return;
    }

    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setRolePermissions(prev => ({
        ...prev,
        [selectedRole]: selectedPermissions.map(p => p.codename)
      }));
      
      showAlert(`Successfully updated permissions for ${selectedRole}`, 'success');
      setOpenDialog(false);
      resetDialog();
    } catch (error) {
      showAlert('Error updating role permissions', 'error');
    } finally {
      setLoading(false);
    }
  };

  const resetDialog = () => {
    setSelectedUser(null);
    setSelectedPermissions([]);
    setSelectedRole('');
    setReason('');
  };

  const openGrantDialog = () => {
    setDialogType('grant');
    setOpenDialog(true);
  };

  const openRoleDialog = () => {
    setDialogType('role');
    setOpenDialog(true);
  };

  const getPermissionsByCategory = () => {
    const grouped = {};
    permissions.forEach(permission => {
      if (!grouped[permission.category]) {
        grouped[permission.category] = [];
      }
      grouped[permission.category].push(permission);
    });
    return grouped;
  };

  const TabPanel = ({ children, value, index }) => (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        <Security sx={{ mr: 1, verticalAlign: 'middle' }} />
        Permission Management
      </Typography>

      {alert.show && (
        <Alert severity={alert.severity} sx={{ mb: 2 }}>
          {alert.message}
        </Alert>
      )}

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
          <Tab label="User Permissions" />
          <Tab label="Role Permissions" />
          <Tab label="Available Permissions" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <Box sx={{ mb: 2 }}>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={openGrantDialog}
          >
            Grant Permission to User
          </Button>
        </Box>

        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>User</TableCell>
                <TableCell>Role</TableCell>
                <TableCell>Custom Permissions</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {users.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    {user.first_name} {user.last_name}
                    <br />
                    <Typography variant="caption" color="textSecondary">
                      {user.email}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip label={user.role} size="small" />
                  </TableCell>
                  <TableCell>
                    {/* This would show actual custom permissions */}
                    <Typography variant="caption">
                      No custom permissions
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Button size="small" startIcon={<Edit />}>
                      Edit
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Box sx={{ mb: 2 }}>
          <Button
            variant="contained"
            startIcon={<Edit />}
            onClick={openRoleDialog}
          >
            Update Role Permissions
          </Button>
        </Box>

        <Grid container spacing={3}>
          {roles.map((role) => (
            <Grid item xs={12} md={6} key={role.value}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    {role.label}
                  </Typography>
                  <Box sx={{ mt: 2 }}>
                    {(rolePermissions[role.value] || []).map((permission) => (
                      <Chip
                        key={permission}
                        label={permission.replace('_', ' ')}
                        size="small"
                        sx={{ mr: 1, mb: 1 }}
                      />
                    ))}
                    {(!rolePermissions[role.value] || rolePermissions[role.value].length === 0) && (
                      <Typography variant="caption" color="textSecondary">
                        No permissions assigned
                      </Typography>
                    )}
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        {Object.entries(getPermissionsByCategory()).map(([category, perms]) => (
          <Card key={category} sx={{ mb: 2 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {permissionCategories[category] || category}
              </Typography>
              <Grid container spacing={2}>
                {perms.map((permission) => (
                  <Grid item xs={12} sm={6} md={4} key={permission.id}>
                    <Box sx={{ p: 1, border: 1, borderColor: 'grey.300', borderRadius: 1 }}>
                      <Typography variant="subtitle2">
                        {permission.name}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        {permission.codename}
                      </Typography>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        ))}
      </TabPanel>

      {/* Grant Permission Dialog */}
      <Dialog open={openDialog && dialogType === 'grant'} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Grant Permissions to User</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Autocomplete
              options={users}
              getOptionLabel={(option) => `${option.first_name} ${option.last_name} (${option.email})`}
              value={selectedUser}
              onChange={(event, newValue) => setSelectedUser(newValue)}
              renderInput={(params) => <TextField {...params} label="Select User" fullWidth />}
              sx={{ mb: 2 }}
            />

            <Autocomplete
              multiple
              options={permissions}
              getOptionLabel={(option) => option.name}
              value={selectedPermissions}
              onChange={(event, newValue) => setSelectedPermissions(newValue)}
              renderInput={(params) => <TextField {...params} label="Select Permissions" />}
              sx={{ mb: 2 }}
            />

            <TextField
              fullWidth
              label="Reason (optional)"
              multiline
              rows={3}
              value={reason}
              onChange={(e) => setReason(e.target.value)}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button onClick={handleGrantPermission} variant="contained" disabled={loading}>
            Grant Permissions
          </Button>
        </DialogActions>
      </Dialog>

      {/* Role Permission Dialog */}
      <Dialog open={openDialog && dialogType === 'role'} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Update Role Permissions</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Select Role</InputLabel>
              <Select
                value={selectedRole}
                onChange={(e) => setSelectedRole(e.target.value)}
                label="Select Role"
              >
                {roles.map((role) => (
                  <MenuItem key={role.value} value={role.value}>
                    {role.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <Autocomplete
              multiple
              options={permissions}
              getOptionLabel={(option) => option.name}
              value={selectedPermissions}
              onChange={(event, newValue) => setSelectedPermissions(newValue)}
              renderInput={(params) => <TextField {...params} label="Select Permissions" />}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button onClick={handleUpdateRolePermissions} variant="contained" disabled={loading}>
            Update Permissions
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default PermissionManagement;
