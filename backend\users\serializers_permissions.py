from rest_framework import serializers
from .models import Permission, RolePermission, UserPermission, User, UserRole


class PermissionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Permission
        fields = ['id', 'codename', 'name', 'description', 'category', 'is_active', 'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at']


class RolePermissionSerializer(serializers.ModelSerializer):
    permission_name = serializers.CharField(source='permission.name', read_only=True)
    permission_codename = serializers.CharField(source='permission.codename', read_only=True)
    permission_description = serializers.CharField(source='permission.description', read_only=True)
    role_display = serializers.CharField(source='get_role_display', read_only=True)

    class Meta:
        model = RolePermission
        fields = ['id', 'role', 'role_display', 'permission', 'permission_name', 
                  'permission_codename', 'permission_description', 'is_active', 'created_at']
        read_only_fields = ['created_at']


class UserPermissionSerializer(serializers.ModelSerializer):
    permission_name = serializers.Char<PERSON>ield(source='permission.name', read_only=True)
    permission_codename = serializers.CharField(source='permission.codename', read_only=True)
    permission_description = serializers.CharField(source='permission.description', read_only=True)
    granted_by_email = serializers.CharField(source='granted_by.email', read_only=True)
    user_email = serializers.CharField(source='user.email', read_only=True)
    is_expired = serializers.BooleanField(read_only=True)

    class Meta:
        model = UserPermission
        fields = ['id', 'user', 'user_email', 'permission', 'permission_name', 
                  'permission_codename', 'permission_description', 'granted', 
                  'granted_by', 'granted_by_email', 'granted_at', 'expires_at', 
                  'reason', 'is_expired']
        read_only_fields = ['granted_at', 'is_expired']


class UserPermissionCreateSerializer(serializers.ModelSerializer):
    permission_codename = serializers.CharField(write_only=True)
    user_email = serializers.EmailField(write_only=True)

    class Meta:
        model = UserPermission
        fields = ['user_email', 'permission_codename', 'granted', 'reason', 'expires_at']

    def validate_permission_codename(self, value):
        try:
            permission = Permission.objects.get(codename=value, is_active=True)
            return value
        except Permission.DoesNotExist:
            raise serializers.ValidationError(f"Permission '{value}' does not exist or is not active.")

    def validate_user_email(self, value):
        try:
            user = User.objects.get(email=value)
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError(f"User with email '{value}' does not exist.")

    def create(self, validated_data):
        permission_codename = validated_data.pop('permission_codename')
        user_email = validated_data.pop('user_email')
        
        user = User.objects.get(email=user_email)
        permission = Permission.objects.get(codename=permission_codename, is_active=True)
        
        # Get the user who is granting this permission
        granted_by = self.context['request'].user if 'request' in self.context else None
        
        user_permission, created = UserPermission.objects.update_or_create(
            user=user,
            permission=permission,
            defaults={
                'granted': validated_data.get('granted', True),
                'granted_by': granted_by,
                'reason': validated_data.get('reason', ''),
                'expires_at': validated_data.get('expires_at')
            }
        )
        return user_permission


class UserPermissionSummarySerializer(serializers.ModelSerializer):
    """Serializer for showing user's permission summary"""
    role_display = serializers.CharField(source='get_role_display', read_only=True)
    tenant_name = serializers.CharField(source='tenant.name', read_only=True)
    all_permissions = serializers.SerializerMethodField()
    role_permissions = serializers.SerializerMethodField()
    custom_permissions = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['id', 'email', 'username', 'first_name', 'last_name', 'role', 
                  'role_display', 'tenant', 'tenant_name', 'all_permissions', 
                  'role_permissions', 'custom_permissions']

    def get_all_permissions(self, obj):
        """Get all permissions (role + custom) for this user"""
        return list(obj.get_all_permissions())

    def get_role_permissions(self, obj):
        """Get permissions granted by role"""
        role_permissions = RolePermission.objects.filter(
            role=obj.role,
            is_active=True,
            permission__is_active=True
        ).select_related('permission')
        
        return [
            {
                'codename': rp.permission.codename,
                'name': rp.permission.name,
                'category': rp.permission.category
            }
            for rp in role_permissions
        ]

    def get_custom_permissions(self, obj):
        """Get custom permissions (granted/denied) for this user"""
        custom_permissions = obj.custom_permissions.filter(
            permission__is_active=True
        ).select_related('permission')
        
        return [
            {
                'codename': cp.permission.codename,
                'name': cp.permission.name,
                'category': cp.permission.category,
                'granted': cp.granted,
                'granted_by': cp.granted_by.email if cp.granted_by else None,
                'granted_at': cp.granted_at,
                'expires_at': cp.expires_at,
                'is_expired': cp.is_expired,
                'reason': cp.reason
            }
            for cp in custom_permissions
        ]


class RolePermissionBulkUpdateSerializer(serializers.Serializer):
    """Serializer for bulk updating role permissions"""
    role = serializers.ChoiceField(choices=UserRole.choices)
    permission_codenames = serializers.ListField(
        child=serializers.CharField(),
        help_text="List of permission codenames to assign to this role"
    )

    def validate_permission_codenames(self, value):
        # Check that all permissions exist
        existing_permissions = set(
            Permission.objects.filter(
                codename__in=value, 
                is_active=True
            ).values_list('codename', flat=True)
        )
        
        invalid_permissions = set(value) - existing_permissions
        if invalid_permissions:
            raise serializers.ValidationError(
                f"The following permissions do not exist or are not active: {', '.join(invalid_permissions)}"
            )
        
        return value

    def save(self):
        role = self.validated_data['role']
        permission_codenames = self.validated_data['permission_codenames']
        
        # Get permission objects
        permissions = Permission.objects.filter(
            codename__in=permission_codenames,
            is_active=True
        )
        
        # Remove existing role permissions
        RolePermission.objects.filter(role=role).delete()
        
        # Create new role permissions
        role_permissions = [
            RolePermission(role=role, permission=permission)
            for permission in permissions
        ]
        RolePermission.objects.bulk_create(role_permissions)
        
        return {
            'role': role,
            'permissions_assigned': len(role_permissions)
        }
