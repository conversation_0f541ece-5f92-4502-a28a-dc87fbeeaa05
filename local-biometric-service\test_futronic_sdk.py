#!/usr/bin/env python3
"""
Test script for Futronic SDK integration
"""

import sys
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_sdk_dlls():
    """Test if SDK DLL files are available"""
    import os
    
    logger.info("🔍 Checking for Futronic SDK DLL files...")
    
    required_dlls = [
        'ftrScanAPI.dll',
        'ftrMathAPI.dll'
    ]
    
    optional_dlls = [
        'ftrWSQ.dll',
        'LiveFinger2.dll',
        'ftrScanApiEx.exe'
    ]
    
    found_dlls = []
    missing_dlls = []
    
    # Check required DLLs
    for dll in required_dlls:
        if os.path.exists(dll):
            size = os.path.getsize(dll)
            logger.info(f"✅ {dll} found ({size:,} bytes)")
            found_dlls.append(dll)
        else:
            logger.error(f"❌ {dll} not found")
            missing_dlls.append(dll)
    
    # Check optional DLLs
    for dll in optional_dlls:
        if os.path.exists(dll):
            size = os.path.getsize(dll)
            logger.info(f"✅ {dll} found ({size:,} bytes)")
            found_dlls.append(dll)
    
    return len(missing_dlls) == 0, found_dlls, missing_dlls

def test_sdk_loading():
    """Test SDK DLL loading"""
    try:
        import ctypes
        from ctypes import cdll
        
        logger.info("🔧 Testing SDK DLL loading...")
        
        # Try to load ftrScanAPI.dll
        try:
            scan_api = cdll.LoadLibrary('./ftrScanAPI.dll')
            logger.info("✅ ftrScanAPI.dll loaded successfully")
        except Exception as e:
            logger.error(f"❌ Failed to load ftrScanAPI.dll: {e}")
            return False
        
        # Try to load ftrMathAPI.dll
        try:
            math_api = cdll.LoadLibrary('./ftrMathAPI.dll')
            logger.info("✅ ftrMathAPI.dll loaded successfully")
        except Exception as e:
            logger.error(f"❌ Failed to load ftrMathAPI.dll: {e}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ SDK loading test failed: {e}")
        return False

def test_futronic_sdk():
    """Test Futronic SDK integration"""
    try:
        logger.info("🧪 Testing Futronic SDK integration...")
        
        from futronic_sdk import FutronicSDK
        
        device = FutronicSDK()
        
        if device.initialize():
            status = device.get_device_status()
            
            logger.info("📊 Device Status:")
            for key, value in status.items():
                logger.info(f"  {key}: {value}")
            
            if status['real_device']:
                logger.info("✅ Real device detected and working!")
                
                # Test fingerprint capture
                logger.info("🧪 Testing fingerprint capture...")
                logger.info("👆 Please place finger on scanner when prompted...")
                
                try:
                    template = device.capture_fingerprint('left')
                    if template:
                        logger.info(f"✅ Fingerprint capture successful!")
                        logger.info(f"Quality: {template.quality_score}%")
                        logger.info(f"Minutiae: {template.minutiae_count}")
                        logger.info(f"Real device: {template.device_info.get('real_device', False)}")
                    else:
                        logger.warning("⚠️ Fingerprint capture returned None")
                except Exception as e:
                    logger.error(f"❌ Fingerprint capture failed: {e}")
                
            else:
                logger.warning("⚠️ Running in simulation mode")
                logger.warning("Real device not detected or SDK issue")
            
            return status['real_device']
        else:
            logger.error("❌ Device initialization failed")
            return False
            
    except ImportError as e:
        logger.error(f"❌ SDK module import failed: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ SDK integration error: {e}")
        return False

def test_usb_detection():
    """Test USB device detection (optional)"""
    try:
        logger.info("🔍 Testing USB device detection...")
        
        # Try to install pyusb if not available
        try:
            import usb.core
        except ImportError:
            logger.info("📦 pyusb not installed, installing...")
            import subprocess
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyusb'])
            import usb.core
        
        # Futronic FS88H VID:PID
        VENDOR_ID = 0x1491
        PRODUCT_ID = 0x0020
        
        device = usb.core.find(idVendor=VENDOR_ID, idProduct=PRODUCT_ID)
        
        if device is None:
            logger.warning("❌ Futronic FS88H not found via USB")
            
            # Check for any Futronic devices
            futronic_devices = usb.core.find(find_all=True, idVendor=VENDOR_ID)
            futronic_list = list(futronic_devices)
            
            if futronic_list:
                logger.info(f"Found {len(futronic_list)} other Futronic device(s):")
                for dev in futronic_list:
                    logger.info(f"  - VID: 0x{dev.idVendor:04x}, PID: 0x{dev.idProduct:04x}")
            
            return False
        else:
            logger.info("✅ Futronic FS88H detected via USB!")
            logger.info(f"Vendor ID: 0x{device.idVendor:04x}")
            logger.info(f"Product ID: 0x{device.idProduct:04x}")
            return True
            
    except Exception as e:
        logger.warning(f"⚠️ USB detection failed: {e}")
        return False

def main():
    """Main test function"""
    logger.info("🚀 Futronic SDK Device Test")
    logger.info("=" * 40)
    
    # Test 1: Check DLL files
    logger.info("\n📋 Test 1: SDK DLL Files")
    dlls_ok, found_dlls, missing_dlls = test_sdk_dlls()
    
    if not dlls_ok:
        logger.error("❌ Required SDK DLL files missing!")
        logger.info("Please ensure ftrScanAPI.dll and ftrMathAPI.dll are in this directory")
        return False
    
    # Test 2: DLL loading
    logger.info("\n📋 Test 2: SDK DLL Loading")
    loading_ok = test_sdk_loading()
    
    if not loading_ok:
        logger.error("❌ SDK DLL loading failed!")
        return False
    
    # Test 3: USB detection (optional)
    logger.info("\n📋 Test 3: USB Device Detection")
    usb_detected = test_usb_detection()
    
    # Test 4: SDK integration
    logger.info("\n📋 Test 4: Futronic SDK Integration")
    sdk_working = test_futronic_sdk()
    
    # Summary
    logger.info("\n📊 Test Summary")
    logger.info("=" * 30)
    logger.info(f"DLL Files: {'✅ PASS' if dlls_ok else '❌ FAIL'}")
    logger.info(f"DLL Loading: {'✅ PASS' if loading_ok else '❌ FAIL'}")
    logger.info(f"USB Detection: {'✅ PASS' if usb_detected else '⚠️ SKIP'}")
    logger.info(f"SDK Integration: {'✅ PASS' if sdk_working else '❌ FAIL'}")
    
    if dlls_ok and loading_ok and sdk_working:
        logger.info("\n🎉 All critical tests passed! Device is ready for use.")
        if not usb_detected:
            logger.info("Note: USB detection failed, but SDK integration works.")
    elif dlls_ok and loading_ok and not sdk_working:
        logger.info("\n⚠️ SDK loads but device integration failed.")
        logger.info("This might be a device connection or driver issue.")
    else:
        logger.info("\n❌ Critical tests failed. Please check SDK installation.")
    
    return dlls_ok and loading_ok and sdk_working

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
