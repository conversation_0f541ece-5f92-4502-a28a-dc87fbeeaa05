import { Outlet, useLocation } from 'react-router-dom';
import { Box, Grid, Paper, useTheme, useMediaQuery, Toolbar, CssBaseline, alpha } from '@mui/material';
import { LockOpen as LockIcon, PersonAdd as RegisterIcon } from '@mui/icons-material';
import Banner from '../components/common/Banner';
import AuthNavbar from '../components/navigation/AuthNavbar';
import Footer from '../components/common/Footer';

const AuthLayout = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const location = useLocation();

  // Get page title based on current route
  const getPageInfo = () => {
    const path = location.pathname;

    if (path === '/login') {
      return {
        title: 'Login',
        subtitle: '', // Removed welcome text
        icon: <LockIcon fontSize="inherit" />
      };
    } else if (path === '/register') {
      return {
        title: 'Create Account',
        subtitle: 'Register a new account to access the system',
        icon: <RegisterIcon fontSize="inherit" />
      };
    } else {
      return {
        title: 'GoID Authentication',
        subtitle: 'Digital ID Card Management System',
        icon: <LockIcon fontSize="inherit" />
      };
    }
  };

  const { title, subtitle, icon } = getPageInfo();

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        minHeight: '100vh',
        backgroundColor: (theme) => theme.palette.background.default,
      }}
    >
      <CssBaseline />
      <AuthNavbar />
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        {/* No Toolbar spacing to connect navbar with content */}
        <Grid container component={Paper} square elevation={0} sx={{ flexGrow: 1 }}>
        {/* Left side - Illustration */}
        {!isMobile && (
          <Grid
            item
            xs={false}
            sm={false}
            md={6}
            sx={{
              background: theme.palette.mode === 'light'
                ? 'linear-gradient(135deg, #f0f7ff 0%, #e8f4fc 100%)'
                : 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              position: 'relative',
              overflow: 'hidden',
              p: 4,
              boxShadow: 'inset 0 0 100px rgba(0,0,0,0.03)',
            }}
          >
            {/* Decorative elements */}
            <Box
              sx={{
                position: 'absolute',
                top: -50,
                right: -30,
                width: 200,
                height: 200,
                borderRadius: '50%',
                background: `radial-gradient(circle, ${alpha(theme.palette.primary.main, 0.15)} 0%, ${alpha(theme.palette.primary.main, 0)} 70%)`,
                filter: 'blur(5px)',
              }}
            />
            <Box
              sx={{
                position: 'absolute',
                bottom: -60,
                left: -40,
                width: 250,
                height: 250,
                borderRadius: '50%',
                background: `radial-gradient(circle, ${alpha(theme.palette.primary.main, 0.15)} 0%, ${alpha(theme.palette.primary.main, 0)} 70%)`,
                filter: 'blur(5px)',
              }}
            />

            {/* Floating particles */}
            <Box
              sx={{
                position: 'absolute',
                top: '20%',
                right: '10%',
                width: 8,
                height: 8,
                borderRadius: '50%',
                background: alpha(theme.palette.primary.main, 0.6),
                boxShadow: `0 0 20px 2px ${alpha(theme.palette.primary.main, 0.3)}`,
                animation: 'float 6s ease-in-out infinite',
                '@keyframes float': {
                  '0%, 100%': { transform: 'translateY(0px)' },
                  '50%': { transform: 'translateY(-20px)' },
                },
              }}
            />
            <Box
              sx={{
                position: 'absolute',
                top: '60%',
                right: '20%',
                width: 12,
                height: 12,
                borderRadius: '50%',
                background: alpha(theme.palette.primary.main, 0.4),
                boxShadow: `0 0 15px 1px ${alpha(theme.palette.primary.main, 0.2)}`,
                animation: 'float 8s ease-in-out infinite 1s',
              }}
            />
            <Box
              sx={{
                position: 'absolute',
                top: '30%',
                left: '15%',
                width: 6,
                height: 6,
                borderRadius: '50%',
                background: alpha(theme.palette.primary.main, 0.5),
                boxShadow: `0 0 10px 1px ${alpha(theme.palette.primary.main, 0.25)}`,
                animation: 'float 7s ease-in-out infinite 0.5s',
              }}
            />

            <Box
              component="img"
              src="/images/id-card-login.svg"
              alt="ID Card Illustration"
              sx={{
                width: '100%',
                maxWidth: 400,
                height: 'auto',
                mb: 4,
                position: 'relative',
                zIndex: 1,
                filter: 'drop-shadow(0 10px 20px rgba(0,0,0,0.15))',
                animation: 'pulse 3s ease-in-out infinite',
                '@keyframes pulse': {
                  '0%, 100%': { transform: 'scale(1)' },
                  '50%': { transform: 'scale(1.03)' },
                },
              }}
            />
          </Grid>
        )}

        {/* Right side - Login Form */}
        <Grid
          item
          xs={12}
          sm={12}
          md={6}
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            p: { xs: 2, sm: 4, md: 6 }, // Reduced padding for more compact layout
            background: theme.palette.mode === 'light'
              ? 'white'
              : alpha(theme.palette.background.paper, 0.8),
            position: 'relative',
            overflow: 'hidden',
          }}
        >
          {/* Subtle background pattern */}
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              opacity: 0.03,
              backgroundImage: `radial-gradient(${theme.palette.primary.main} 2px, transparent 2px)`,
              backgroundSize: '30px 30px',
              pointerEvents: 'none',
            }}
          />

          <Box
            sx={{
              width: '100%',
              maxWidth: 400, // Reduced from 450 to 400 for more compact form
              position: 'relative',
              zIndex: 1,
            }}
          >
            <Banner
              title={title}
              subtitle={subtitle}
              icon={icon}
            />

            <Outlet />
          </Box>
        </Grid>
      </Grid>
        <Footer />
      </Box>
    </Box>
  );
};

export default AuthLayout;
