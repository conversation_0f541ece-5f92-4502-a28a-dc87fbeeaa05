#!/usr/bin/env python3
"""
Test script to verify that user roles are correctly assigned based on tenant context
and don't persist across different tenant logins.
"""
import os
import sys
import django
import requests
import json

# Add the backend directory to Python path
sys.path.append('/app')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

def test_role_persistence_fix():
    """Test that user roles don't persist across different tenant logins."""
    
    base_url = "http://localhost:8000"
    
    print("🧪 TESTING ROLE PERSISTENCE FIX")
    print("=" * 50)
    
    # Test scenario: Login to subcity as subcity_admin, then login to kebele as clerk
    
    # Step 1: Login to subcity tenant as subcity_admin
    print("\n1️⃣ STEP 1: Login to subcity tenant")
    subcity_login_data = {
        "username": "<EMAIL>",  # Assuming this is the subcity admin
        "password": "password123"  # Replace with actual password
    }
    
    try:
        response = requests.post(f"{base_url}/api/auth/token/", json=subcity_login_data)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            subcity_token_data = response.json()
            subcity_access_token = subcity_token_data.get('access')
            
            # Decode the token to check role
            import jwt
            subcity_decoded = jwt.decode(subcity_access_token, options={"verify_signature": False})
            print(f"   ✅ Subcity login successful")
            print(f"   Role in JWT: {subcity_decoded.get('role')}")
            print(f"   Tenant: {subcity_decoded.get('tenant_name')} ({subcity_decoded.get('tenant_type')})")
            
            if subcity_decoded.get('role') != 'subcity_admin':
                print(f"   ❌ Expected 'subcity_admin', got '{subcity_decoded.get('role')}'")
            else:
                print(f"   ✅ Correct role for subcity tenant")
        else:
            print(f"   ❌ Subcity login failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error during subcity login: {e}")
        return False
    
    # Step 2: Login to kebele tenant as clerk
    print("\n2️⃣ STEP 2: Login to kebele tenant")
    kebele_login_data = {
        "username": "<EMAIL>",  # Same user, different tenant
        "password": "password123"  # Replace with actual password
    }
    
    try:
        response = requests.post(f"{base_url}/api/auth/token/", json=kebele_login_data)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            kebele_token_data = response.json()
            kebele_access_token = kebele_token_data.get('access')
            
            # Decode the token to check role
            kebele_decoded = jwt.decode(kebele_access_token, options={"verify_signature": False})
            print(f"   ✅ Kebele login successful")
            print(f"   Role in JWT: {kebele_decoded.get('role')}")
            print(f"   Tenant: {kebele_decoded.get('tenant_name')} ({kebele_decoded.get('tenant_type')})")
            
            # This is the critical test - the role should be 'clerk' for kebele, not 'subcity_admin'
            expected_role = 'clerk'  # or whatever role the user should have in kebele
            actual_role = kebele_decoded.get('role')
            
            if actual_role == 'subcity_admin':
                print(f"   ❌ ROLE PERSISTENCE BUG: Still showing 'subcity_admin' in kebele tenant!")
                print(f"   ❌ This indicates the fix didn't work")
                return False
            elif actual_role == expected_role:
                print(f"   ✅ ROLE FIX SUCCESSFUL: Correct role '{actual_role}' for kebele tenant")
                return True
            else:
                print(f"   ⚠️  Unexpected role '{actual_role}', expected '{expected_role}'")
                return False
        else:
            print(f"   ❌ Kebele login failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error during kebele login: {e}")
        return False

def main():
    """Main test function."""
    print("🔧 Role Persistence Fix Test")
    print("This test verifies that user roles are correctly assigned based on tenant context")
    print("and don't persist across different tenant logins.\n")
    
    success = test_role_persistence_fix()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ TEST PASSED: Role persistence fix is working correctly!")
        print("Users now get the correct role for each tenant they log into.")
    else:
        print("❌ TEST FAILED: Role persistence issue still exists.")
        print("Check the debug logs in the Django console for more information.")
    print("=" * 50)

if __name__ == "__main__":
    main()
