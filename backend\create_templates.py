"""
Django shell script to create ID card templates for all tenants.
Run this in Django shell: python manage.py shell < create_templates.py
"""

from django_tenants.utils import schema_context
from tenants.models import Tenant
from idcards.models import IDCardTemplate

print("🌱 Creating ID card templates for all tenants...")

# Get all tenants
tenants = Tenant.objects.all()
print(f"📋 Found {tenants.count()} tenants")

for tenant in tenants:
    print(f"\n🏢 Processing tenant: {tenant.name} ({tenant.schema_name})")
    
    try:
        with schema_context(tenant.schema_name):
            # Check if templates already exist
            existing_count = IDCardTemplate.objects.count()
            if existing_count > 0:
                print(f"  ✅ Templates already exist ({existing_count} templates), skipping...")
                continue
            
            # Create default template
            default_template = IDCardTemplate.objects.create(
                name="Default Template",
                description="Standard ID card template with official government styling",
                background_color="#FFFFFF",
                text_color="#000000",
                accent_color="#1976D2",
                logo_position="top_left",
                photo_position="left",
                header_text="Federal Democratic Republic of Ethiopia",
                subtitle_text="National ID Card",
                footer_text="",
                is_active=True,
                is_default=True
            )
            print(f"  ✅ Created: {default_template.name}")
            
            # Create modern template
            modern_template = IDCardTemplate.objects.create(
                name="Modern Template",
                description="Modern ID card template with contemporary design",
                background_color="#F5F5F5",
                text_color="#212121",
                accent_color="#2196F3",
                logo_position="top_center",
                photo_position="right",
                header_text="Federal Democratic Republic of Ethiopia",
                subtitle_text="Digital ID Card",
                footer_text="Valid for official identification",
                is_active=True,
                is_default=False
            )
            print(f"  ✅ Created: {modern_template.name}")
            
            # Create classic template
            classic_template = IDCardTemplate.objects.create(
                name="Classic Template",
                description="Traditional ID card template with classic styling",
                background_color="#FAFAFA",
                text_color="#333333",
                accent_color="#4CAF50",
                logo_position="top_left",
                photo_position="left",
                header_text="Federal Democratic Republic of Ethiopia",
                subtitle_text="Official ID Card",
                footer_text="Government of Ethiopia",
                is_active=True,
                is_default=False
            )
            print(f"  ✅ Created: {classic_template.name}")
            
            total_created = IDCardTemplate.objects.count()
            print(f"  🎉 Successfully created {total_created} templates for {tenant.name}")
            
    except Exception as e:
        print(f"  ❌ Error processing tenant {tenant.name}: {str(e)}")
        continue

print("\n🎉 Template creation completed!")
print("\n📊 Summary:")

# Print summary for each tenant
for tenant in tenants:
    try:
        with schema_context(tenant.schema_name):
            count = IDCardTemplate.objects.count()
            default_template = IDCardTemplate.objects.filter(is_default=True).first()
            print(f"  {tenant.name}: {count} templates (Default: {default_template.name if default_template else 'None'})")
    except Exception as e:
        print(f"  {tenant.name}: Error - {str(e)}")

print("\n✅ All done! Templates are now available for ID card creation.")
