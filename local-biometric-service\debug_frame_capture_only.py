#!/usr/bin/env python3
"""
Debug Frame Capture ONLY
Focus specifically on making ftrScanGetFrame() work
This is the CORE function - we MUST make it work to get real fingerprints
"""

import ctypes
from ctypes import cdll, c_int, c_uint, c_void_p, c_ubyte, POINTER, byref
import sys
import os
import time

def debug_frame_capture_step_by_step():
    """Debug ftrScanGetFrame step by step to identify the exact issue"""
    
    try:
        print("=== DEBUGGING ftrScanGetFrame() STEP BY STEP ===")
        print("This function is ESSENTIAL - we MUST make it work!")
        print()
        
        # Step 1: Load SDK
        print("Step 1: Loading SDK...")
        dll_path = os.path.abspath('./ftrScanAPI.dll')
        scan_api = cdll.LoadLibrary(dll_path)
        print("✅ SDK loaded successfully")
        
        # Step 2: Configure function signatures
        print("\nStep 2: Configuring function signatures...")
        scan_api.ftrScanOpenDevice.restype = c_void_p
        scan_api.ftrScanOpenDevice.argtypes = []
        
        scan_api.ftrScanCloseDevice.restype = c_int
        scan_api.ftrScanCloseDevice.argtypes = [c_void_p]
        
        scan_api.ftrScanGetFrame.restype = c_int
        scan_api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(c_ubyte), POINTER(c_uint)]
        print("✅ Function signatures configured")
        
        # Step 3: Initialize device
        print("\nStep 3: Initializing device...")
        device_handle = scan_api.ftrScanOpenDevice()
        if not device_handle:
            print("❌ ERROR: Failed to open device")
            return
        print(f"✅ Device initialized - Handle: {device_handle}")
        
        # Step 4: Test different approaches for ftrScanGetFrame
        print("\nStep 4: Testing ftrScanGetFrame with different approaches...")
        
        # Approach 1: Minimal buffer
        print("\n--- Approach 1: Minimal Buffer (1KB) ---")
        try:
            buffer_size = 1024
            image_buffer = (c_ubyte * buffer_size)()
            frame_size = c_uint(buffer_size)
            
            print(f"Buffer created: {buffer_size} bytes")
            print(f"Initial frame_size: {frame_size.value}")
            print("Calling ftrScanGetFrame...")
            
            result = scan_api.ftrScanGetFrame(device_handle, image_buffer, byref(frame_size))
            
            print(f"✅ SUCCESS: ftrScanGetFrame returned {result}")
            print(f"Frame size after call: {frame_size.value}")
            
            if frame_size.value > 0:
                print(f"✅ Got {frame_size.value} bytes of data!")
                # Analyze first few bytes
                first_bytes = [image_buffer[i] for i in range(min(10, frame_size.value))]
                print(f"First 10 bytes: {first_bytes}")
            else:
                print("⚠️ Frame size is 0 - no data captured")
                
        except Exception as e:
            print(f"❌ Approach 1 failed: {e}")
        
        # Approach 2: Standard buffer
        print("\n--- Approach 2: Standard Buffer (320x480) ---")
        try:
            buffer_size = 320 * 480
            image_buffer = (c_ubyte * buffer_size)()
            frame_size = c_uint(buffer_size)
            
            print(f"Buffer created: {buffer_size} bytes")
            print("Calling ftrScanGetFrame...")
            
            result = scan_api.ftrScanGetFrame(device_handle, image_buffer, byref(frame_size))
            
            print(f"✅ SUCCESS: ftrScanGetFrame returned {result}")
            print(f"Frame size after call: {frame_size.value}")
            
            if frame_size.value > 0:
                print(f"✅ Got {frame_size.value} bytes of REAL fingerprint data!")
                # Analyze data quality
                image_data = bytes(image_buffer[:frame_size.value])
                non_zero_bytes = sum(1 for b in image_data if b > 0)
                avg_value = sum(image_data) / len(image_data)
                
                print(f"Data analysis:")
                print(f"  - Non-zero bytes: {non_zero_bytes}/{frame_size.value} ({(non_zero_bytes/frame_size.value)*100:.1f}%)")
                print(f"  - Average value: {avg_value:.2f}")
                print(f"  - Data quality: {'Good' if non_zero_bytes > frame_size.value * 0.1 else 'Low'}")
            else:
                print("⚠️ Frame size is 0 - no fingerprint detected")
                
        except Exception as e:
            print(f"❌ Approach 2 failed: {e}")
        
        # Approach 3: Large buffer
        print("\n--- Approach 3: Large Buffer (1MB) ---")
        try:
            buffer_size = 1024 * 1024
            image_buffer = (c_ubyte * buffer_size)()
            frame_size = c_uint(buffer_size)
            
            print(f"Buffer created: {buffer_size} bytes")
            print("Calling ftrScanGetFrame...")
            
            result = scan_api.ftrScanGetFrame(device_handle, image_buffer, byref(frame_size))
            
            print(f"✅ SUCCESS: ftrScanGetFrame returned {result}")
            print(f"Frame size after call: {frame_size.value}")
            
            if frame_size.value > 0:
                print(f"✅ Got {frame_size.value} bytes of REAL fingerprint data!")
            else:
                print("⚠️ Frame size is 0")
                
        except Exception as e:
            print(f"❌ Approach 3 failed: {e}")
        
        # Approach 4: Multiple rapid calls
        print("\n--- Approach 4: Multiple Rapid Calls ---")
        try:
            buffer_size = 320 * 480
            
            for i in range(3):
                print(f"\nCall {i+1}:")
                image_buffer = (c_ubyte * buffer_size)()
                frame_size = c_uint(buffer_size)
                
                result = scan_api.ftrScanGetFrame(device_handle, image_buffer, byref(frame_size))
                print(f"  Result: {result}, Size: {frame_size.value}")
                
                time.sleep(0.5)  # Small delay between calls
                
        except Exception as e:
            print(f"❌ Approach 4 failed: {e}")
        
        # Approach 5: With finger detection first
        print("\n--- Approach 5: With Finger Detection First ---")
        try:
            # Configure finger detection
            scan_api.ftrScanIsFingerPresent.restype = c_int
            scan_api.ftrScanIsFingerPresent.argtypes = [c_void_p, POINTER(c_int)]
            
            # Check for finger
            finger_status = c_int(0)
            finger_result = scan_api.ftrScanIsFingerPresent(device_handle, byref(finger_status))
            print(f"Finger detection: result={finger_result}, status={finger_status.value}")
            
            if finger_result == 1 and finger_status.value > 0:
                print("✅ Finger detected - proceeding with capture")
                
                buffer_size = 320 * 480
                image_buffer = (c_ubyte * buffer_size)()
                frame_size = c_uint(buffer_size)
                
                result = scan_api.ftrScanGetFrame(device_handle, image_buffer, byref(frame_size))
                print(f"Frame capture after finger detection: result={result}, size={frame_size.value}")
                
                if frame_size.value > 0:
                    print(f"✅ SUCCESS: Captured {frame_size.value} bytes of REAL fingerprint!")
                
            else:
                print("⚠️ No finger detected - trying capture anyway")
                
                buffer_size = 320 * 480
                image_buffer = (c_ubyte * buffer_size)()
                frame_size = c_uint(buffer_size)
                
                result = scan_api.ftrScanGetFrame(device_handle, image_buffer, byref(frame_size))
                print(f"Frame capture without finger: result={result}, size={frame_size.value}")
                
        except Exception as e:
            print(f"❌ Approach 5 failed: {e}")
        
        # Close device
        print("\nStep 5: Closing device...")
        scan_api.ftrScanCloseDevice(device_handle)
        print("✅ Device closed")
        
        print("\n=== FRAME CAPTURE DEBUG COMPLETE ===")
        print("ftrScanGetFrame() is ESSENTIAL for real fingerprints!")
        print("Review the results above to see which approach works.")
        
    except Exception as e:
        print(f"❌ CRITICAL ERROR: {e}")
        print("ftrScanGetFrame() MUST work to get real fingerprints!")

def test_frame_capture_with_user_input():
    """Interactive test with user placing finger"""
    
    try:
        print("\n=== INTERACTIVE FRAME CAPTURE TEST ===")
        print("This will test ftrScanGetFrame() with your finger on the scanner")
        
        # Load and initialize
        dll_path = os.path.abspath('./ftrScanAPI.dll')
        scan_api = cdll.LoadLibrary(dll_path)
        
        scan_api.ftrScanOpenDevice.restype = c_void_p
        scan_api.ftrScanOpenDevice.argtypes = []
        scan_api.ftrScanCloseDevice.restype = c_int
        scan_api.ftrScanCloseDevice.argtypes = [c_void_p]
        scan_api.ftrScanGetFrame.restype = c_int
        scan_api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(c_ubyte), POINTER(c_uint)]
        
        device_handle = scan_api.ftrScanOpenDevice()
        if not device_handle:
            print("❌ ERROR: Cannot open device")
            return
        
        print(f"✅ Device opened - Handle: {device_handle}")
        
        # Interactive test
        input("\n🖐️ PLACE YOUR FINGER ON THE SCANNER and press Enter...")
        
        buffer_size = 320 * 480
        image_buffer = (c_ubyte * buffer_size)()
        frame_size = c_uint(buffer_size)
        
        print("📸 Capturing fingerprint with ftrScanGetFrame()...")
        result = scan_api.ftrScanGetFrame(device_handle, image_buffer, byref(frame_size))
        
        print(f"Result: {result}")
        print(f"Frame size: {frame_size.value}")
        
        if result == 0 and frame_size.value > 0:  # FTR_OK
            print(f"🎉 SUCCESS! Captured {frame_size.value} bytes of REAL fingerprint data!")
            
            # Analyze the real fingerprint data
            image_data = bytes(image_buffer[:frame_size.value])
            non_zero_bytes = sum(1 for b in image_data if b > 0)
            unique_values = len(set(image_data))
            avg_value = sum(image_data) / len(image_data)
            
            print(f"\n📊 REAL FINGERPRINT DATA ANALYSIS:")
            print(f"  - Total bytes: {frame_size.value}")
            print(f"  - Non-zero bytes: {non_zero_bytes} ({(non_zero_bytes/frame_size.value)*100:.1f}%)")
            print(f"  - Unique values: {unique_values}/256")
            print(f"  - Average intensity: {avg_value:.2f}")
            print(f"  - Data complexity: {(unique_values/256)*100:.1f}%")
            
            if non_zero_bytes > frame_size.value * 0.1:
                print("✅ EXCELLENT: This is high-quality real fingerprint data!")
            else:
                print("⚠️ WARNING: Low data quality - may need better finger placement")
                
        elif result == 1:  # FTR_ERROR_EMPTY_FRAME
            print("⚠️ Empty frame - no finger detected")
        else:
            print(f"❌ Capture failed with result code: {result}")
        
        # Try again with better placement
        input("\n🖐️ Try again - PRESS FINGER FIRMLY and press Enter...")
        
        frame_size.value = buffer_size  # Reset
        result = scan_api.ftrScanGetFrame(device_handle, image_buffer, byref(frame_size))
        
        print(f"Second attempt - Result: {result}, Size: {frame_size.value}")
        
        if result == 0 and frame_size.value > 0:
            print(f"🎉 SUCCESS on second attempt! {frame_size.value} bytes captured!")
        
        scan_api.ftrScanCloseDevice(device_handle)
        print("✅ Device closed")
        
    except Exception as e:
        print(f"❌ Interactive test failed: {e}")

if __name__ == '__main__':
    print("=== ftrScanGetFrame() DEBUG TOOL ===")
    print("This function is ESSENTIAL for real fingerprint capture!")
    print("We MUST make it work - there's no alternative!")
    print()
    
    # Step-by-step debugging
    debug_frame_capture_step_by_step()
    
    # Interactive test
    test_frame_capture_with_user_input()
    
    print("\n=== CONCLUSION ===")
    print("ftrScanGetFrame() is the ONLY way to get real fingerprints.")
    print("We must fix this function - bypassing it means NO real biometric data!")
    print("Review the test results to identify the working approach.")
