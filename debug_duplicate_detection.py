#!/usr/bin/env python3
"""
Debug duplicate detection issue
"""

import requests
import json

def test_with_browser_data():
    """Test with the exact data format from browser console"""
    print("🧪 Testing with Real Browser Data Format")
    print("=" * 50)
    
    # This simulates the exact data format your browser sends
    # Based on the console log: "length: 38686" and "java_bridge" format
    sample_template = {
        "thumb_type": "left",
        "capture_method": "java_bridge", 
        "capture_time": "2025-06-26T14:36:17.256000",
        "ansi_iso_template": "VGVzdCBBTlNJL0lTTyB0ZW1wbGF0ZSBkYXRhIC0gdGhpcyBzaG91bGQgYmUgcmVhbCBkYXRh",
        "fingerprint_image": "VGVzdCBpbWFnZSBkYXRhIGZyb20gY2FwdHVyZQ==",
        "data_size": 35000,
        "device_info": {
            "model": "Futronic FS88H",
            "interface": "java_bridge", 
            "real_device": True,
            "ansi_sdk": True
        }
    }
    
    test_data = {
        "left_thumb_fingerprint": json.dumps(sample_template),
        "right_thumb_fingerprint": None
    }
    
    print("📊 Sending test data to Django...")
    print(f"📊 Template data size: {len(test_data['left_thumb_fingerprint'])} chars")
    
    try:
        # Try with no auth first (should get 401)
        response = requests.post(
            "http://localhost:8000/api/biometrics/check-duplicates/",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 401:
            print("✅ Django endpoint reachable (requires auth as expected)")
            return True
        elif response.status_code == 200:
            result = response.json()
            print("✅ Success Response:")
            print(json.dumps(result, indent=2))
            return True
        else:
            print(f"❌ Unexpected response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Django - is it running?")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def check_django_logs():
    """Instructions for checking Django logs"""
    print("\n🔍 Django Backend Debugging Steps:")
    print("=" * 50)
    print("1. Check your Django backend console/terminal for these messages:")
    print("   📋 When you register a citizen, look for:")
    print("   - '🔍 DUPLICATE CHECK STARTED'")
    print("   - '🔍 Using FMatcher service for duplicate detection'")
    print("   - '📊 Found X templates in [kebele name]'")
    print("   - Any error messages about FMatcher or templates")
    print()
    print("2. If you DON'T see these messages:")
    print("   - Django is using old code without FMatcher integration")
    print("   - Restart Django backend to load updated fingerprint_processing.py")
    print()
    print("3. If you see error messages like:")
    print("   - '❌ FMatcher.jar not found' - Copy FMatcher files to backend")
    print("   - '❌ No ANSI/ISO template found' - Template format issue")
    print("   - '❌ Error running FMatcher' - Java/path issue")
    print()
    print("4. Database check:")
    print("   - Verify templates in kebele_kebele14.citizens_biometric have 'ansi_iso_template' field")
    print("   - Check if templates are base64 encoded ANSI/ISO data")

def test_authentication_flow():
    """Test the exact authentication flow the browser uses"""
    print("\n🔐 Frontend Authentication Test:")
    print("=" * 40)
    print("In your browser's Network tab when registering a citizen:")
    print("1. Look for POST request to '/api/biometrics/check-duplicates/'")
    print("2. Check Request Headers for 'Authorization: Bearer [token]'")
    print("3. Check Response - should be 200 OK with duplicate result")
    print("4. If you see 401 Unauthorized - frontend auth issue")
    print("5. If you see 500 Internal Server Error - Django FMatcher issue")

if __name__ == "__main__":
    print("🔍 Debugging GoID Duplicate Detection Issue")
    print("=" * 60)
    
    # Test 1: Basic connectivity 
    endpoint_ok = test_with_browser_data()
    
    # Test 2: Instructions for further debugging
    check_django_logs()
    test_authentication_flow()
    
    print("\n" + "=" * 60)
    print("🎯 Most Likely Issues:")
    print("1. **Django backend not restarted** with FMatcher integration")
    print("2. **Template format mismatch** - stored templates missing 'ansi_iso_template'")
    print("3. **FMatcher integration errors** - check Django logs")
    print("4. **Database connection issues** - FMatcher can't access stored templates")
    
    print("\n🚀 Quick Fix Steps:")
    print("1. Restart Django backend to load updated fingerprint_processing.py")
    print("2. Check Django logs during citizen registration") 
    print("3. Verify database templates have correct format")
    print("4. Test with same finger on different citizens")
    
    if endpoint_ok:
        print("\n✅ Django endpoint is working - issue is in FMatcher integration")
    else:
        print("\n❌ Django backend connection issues")
