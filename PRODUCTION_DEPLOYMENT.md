# GoID Production Deployment Guide

## 🏗️ Architecture Overview

The GoID system uses a **distributed biometric architecture** for production deployment:

- **Central Server**: Runs the main application in Docker containers
- **Local Workstations**: Each kebele/subcity has computers with biometric devices
- **Web Access**: Clerks access the system through web browsers
- **Local Biometric Service**: Handles device communication on each workstation

```
┌─────────────────────────────────────────────────────────────────┐
│                    Central Server (Docker)                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Web App       │  │   Database      │  │   File Storage  │ │
│  │   (Backend)     │  │   (PostgreSQL)  │  │   (Media)       │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                              │
                              │ HTTP API
                              ▼
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│   Kebele 1      │  │   Kebele 2      │  │   SubCity       │
│   ┌───────────┐ │  │   ┌───────────┐ │  │   ┌───────────┐ │
│   │ Browser   │ │  │   │ Browser   │ │  │   │ Browser   │ │
│   │ + Local   │ │  │   │ + Local   │ │  │   │ + Local   │ │
│   │ Biometric │ │  │   │ Biometric │ │  │   │ Biometric │ │
│   │ Service   │ │  │   │ Service   │ │  │   │ Service   │ │
│   └───────────┘ │  │   └───────────┘ │  │   └───────────┘ │
│   Futronic      │  │   Futronic      │  │   Futronic      │
│   FS88H         │  │   FS88H         │  │   FS88H         │
└─────────────────┘  └─────────────────┘  └─────────────────┘
```

## 🚀 Deployment Steps

### Step 1: Central Server Setup

1. **Prepare the server:**
```bash
# Install Docker and Docker Compose
sudo apt update
sudo apt install docker.io docker-compose

# Clone the repository
git clone <your-repo-url>
cd GoID
```

2. **Configure environment:**
```bash
# Create environment file
cp .env.example .env

# Edit environment variables
nano .env
```

3. **Deploy with Docker:**
```bash
# Start the application
docker-compose -f docker-compose.production.yml up -d

# Check status
docker-compose ps
```

### Step 2: Workstation Setup (Each Kebele/SubCity)

1. **Download biometric service:**
```bash
# Download the local-biometric-service folder to each workstation
# Copy to: C:\GoID\BiometricService\
```

2. **Install the service:**
```cmd
# Run as Administrator
cd C:\GoID\BiometricService
install_biometric_service.bat
```

3. **Connect Futronic device:**
- Connect Futronic FS88H via USB
- Install device drivers
- Test device connection

4. **Start biometric service:**
```cmd
# Start the service
start_service.bat

# Or use desktop shortcut: "Start_GoID_Biometric.bat"
```

5. **Configure web browser:**
- Open browser to: `http://[SERVER_IP]:3000`
- Login with tenant credentials
- Test biometric capture in citizen registration

## 🔧 Configuration

### Environment Variables (.env)

```env
# Database
DB_PASSWORD=your_secure_password_here

# Security
SECRET_KEY=your_very_long_secret_key_here

# Network
SERVER_IP=*************
ALLOWED_HOSTS=*

# Optional: SSL
SSL_ENABLED=false
SSL_CERT_PATH=/path/to/cert.pem
SSL_KEY_PATH=/path/to/key.pem
```

### Network Configuration

1. **Server Network:**
   - Ensure port 3000 (frontend) and 8000 (backend) are accessible
   - Configure firewall rules
   - Set up SSL certificates (recommended)

2. **Workstation Network:**
   - Each workstation needs internet access to reach the server
   - Local biometric service runs on port 8001
   - No incoming connections required

## 🔍 Testing

### Test Central Server

```bash
# Health check
curl http://[SERVER_IP]:8000/api/health/

# Database connection
docker-compose exec backend python manage.py check --database default
```

### Test Workstation Biometric Service

```bash
# Health check
curl http://localhost:8001/api/health

# Device status
curl http://localhost:8001/api/device/status
```

### Test End-to-End

1. Open browser: `http://[SERVER_IP]:3000`
2. Login as clerk
3. Start citizen registration
4. Test biometric capture on step 7
5. Verify fingerprint data is saved

## 🚨 Troubleshooting

### Common Issues

1. **Biometric device not detected:**
   - Check USB connection
   - Install/update device drivers
   - Restart biometric service
   - Check Windows Device Manager

2. **Cannot access server from workstation:**
   - Check network connectivity: `ping [SERVER_IP]`
   - Verify firewall settings
   - Check server status: `docker-compose ps`

3. **Biometric service connection failed:**
   - Ensure service is running: check `http://localhost:8001/api/health`
   - Check Windows Firewall
   - Restart service: `start_service.bat`

4. **Browser security warnings:**
   - Allow mixed content (HTTP biometric service + HTTPS server)
   - Add localhost exception in browser settings

### Log Files

- **Server logs:** `docker-compose logs backend`
- **Biometric service logs:** Check console output in `start_service.bat`
- **Browser logs:** Open Developer Tools > Console

## 📋 Maintenance

### Regular Tasks

1. **Database backup:**
```bash
docker-compose exec db pg_dump -U goid_user goid_db > backup_$(date +%Y%m%d).sql
```

2. **Update application:**
```bash
git pull
docker-compose -f docker-compose.production.yml down
docker-compose -f docker-compose.production.yml up -d --build
```

3. **Monitor disk space:**
```bash
df -h
docker system prune -f
```

### Security

1. **Regular updates:**
   - Update Docker images
   - Update biometric service
   - Update device drivers

2. **Access control:**
   - Use strong passwords
   - Enable SSL/HTTPS
   - Restrict network access
   - Regular security audits

## 📞 Support

For technical support:
1. Check logs first
2. Verify network connectivity
3. Test individual components
4. Contact system administrator

---

**Note:** This deployment guide assumes a local network setup. For internet-facing deployments, additional security measures are required including SSL certificates, VPN access, and enhanced authentication.
