import React, { useState, useEffect } from 'react';
import {
  Autocomplete,
  TextField,
  Box,
  Typography,
  Avatar,
  Chip,
  CircularProgress,
  InputAdornment,
} from '@mui/material';
import {
  Search as SearchIcon,
  Person as PersonIcon,
  Male as MaleIcon,
  Female as FemaleIcon,
} from '@mui/icons-material';
import citizenService from '../../services/citizenService';

const ResidentAutocomplete = ({
  label,
  placeholder = "Type to search residents...",
  value,
  onChange,
  onInputChange,
  gender = null, // 'male', 'female', or null for all
  disabled = false,
  required = false,
  error = false,
  helperText = '',
  sx = {},
  ...props
}) => {
  const [options, setOptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [inputValue, setInputValue] = useState('');

  // Debounced search function
  useEffect(() => {
    if (!inputValue || inputValue.length < 2) {
      setOptions([]);
      return;
    }

    const timeoutId = setTimeout(async () => {
      await searchResidents(inputValue);
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [inputValue, gender]);

  const searchResidents = async (searchTerm) => {
    if (!searchTerm || searchTerm.length < 2) {
      setOptions([]);
      return;
    }

    try {
      setLoading(true);
      const results = await citizenService.searchResidents(searchTerm, gender);

      // Transform results to consistent format
      const transformedResults = results.map(citizen => ({
        id: citizen.id,
        first_name: citizen.first_name || '',
        middle_name: citizen.middle_name || '',
        last_name: citizen.last_name || '',
        digital_id: citizen.digital_id || citizen.id_number || '',
        gender: citizen.gender || '',
        phone: citizen.phone || '',
        full_name: `${citizen.first_name || ''} ${citizen.middle_name || ''} ${citizen.last_name || ''}`.trim()
      }));

      setOptions(transformedResults);
    } catch (error) {
      console.error('Error searching residents:', error);
      setOptions([]);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (event, newInputValue, reason) => {
    setInputValue(newInputValue);
    if (onInputChange) {
      onInputChange(event, newInputValue, reason);
    }
  };

  const handleChange = (event, newValue, reason) => {
    if (onChange) {
      onChange(event, newValue, reason);
    }
  };

  const getOptionLabel = (option) => {
    if (!option) return '';
    if (typeof option === 'string') return option;
    return `${option.full_name} (ID: ${option.digital_id})`;
  };

  const getGenderIcon = (genderValue) => {
    if (genderValue === 'male') return <MaleIcon sx={{ color: 'primary.main', fontSize: 16 }} />;
    if (genderValue === 'female') return <FemaleIcon sx={{ color: 'secondary.main', fontSize: 16 }} />;
    return <PersonIcon sx={{ color: 'text.secondary', fontSize: 16 }} />;
  };

  const getGenderChip = (genderValue) => {
    if (genderValue === 'male') {
      return <Chip label="Male" size="small" color="primary" variant="outlined" />;
    }
    if (genderValue === 'female') {
      return <Chip label="Female" size="small" color="secondary" variant="outlined" />;
    }
    return null;
  };

  return (
    <Autocomplete
      {...props}
      options={options}
      value={value}
      onChange={handleChange}
      onInputChange={handleInputChange}
      getOptionLabel={getOptionLabel}
      loading={loading}
      disabled={disabled}
      filterOptions={(x) => x} // Disable client-side filtering since we do server-side search
      isOptionEqualToValue={(option, value) => {
        if (!option || !value) return false;
        // Compare using database id since that's what we store in the form
        return option.id === value.id;
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          label={label}
          placeholder={placeholder}
          required={required}
          error={error}
          helperText={helperText}
          InputProps={{
            ...params.InputProps,
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
            endAdornment: (
              <>
                {loading ? <CircularProgress color="inherit" size={20} /> : null}
                {params.InputProps.endAdornment}
              </>
            ),
          }}
        />
      )}
      renderOption={(props, option) => (
        <Box component="li" {...props}>
          <Avatar
            sx={{
              mr: 2,
              bgcolor: option.gender === 'male' ? 'primary.main' :
                       option.gender === 'female' ? 'secondary.main' : 'grey.500',
              width: 32,
              height: 32
            }}
          >
            {option.first_name?.charAt(0)?.toUpperCase() || 'U'}
          </Avatar>
          <Box sx={{ flexGrow: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="body2" fontWeight="medium">
                {option.full_name}
              </Typography>
              {getGenderIcon(option.gender)}
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
              <Typography variant="caption" color="text.secondary">
                ID: {option.digital_id}
              </Typography>
              {option.phone && (
                <Typography variant="caption" color="text.secondary">
                  • {option.phone}
                </Typography>
              )}
              {getGenderChip(option.gender)}
            </Box>
          </Box>
        </Box>
      )}
      noOptionsText={
        inputValue.length < 2
          ? "Type at least 2 characters to search"
          : loading
            ? "Searching..."
            : "No residents found"
      }
      loadingText="Searching residents..."
      sx={{
        '& .MuiAutocomplete-listbox': {
          maxHeight: 300,
        },
        ...sx
      }}
    />
  );
};

export default ResidentAutocomplete;
