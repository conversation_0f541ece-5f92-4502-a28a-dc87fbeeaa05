import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  TextField,
  InputAdornment,
  IconButton,
  Grid,
  Chip,
  Pagination,
  CircularProgress,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  SupervisorAccount as AdminIcon,
  Person as UserIcon,
} from '@mui/icons-material';
import axios from '../../utils/axios';
import { useLocalization } from '../../contexts/LocalizationContext';

// Mock data for users
const mockUsers = [
  {
    id: 1,
    email: '<EMAIL>',
    username: 'admin',
    first_name: 'Admin',
    last_name: 'User',
    role: 'superadmin',
    tenant: null,
    tenant_name: null,
    is_active: true,
    date_joined: '2023-11-15T12:00:00Z'
  },
  {
    id: 2,
    email: '<EMAIL>',
    username: 'city_admin',
    first_name: 'City',
    last_name: 'Admin',
    role: 'city_admin',
    tenant: 1,
    tenant_name: 'Addis Ababa',
    is_active: true,
    date_joined: '2023-11-15T12:00:00Z'
  },
  {
    id: 3,
    email: '<EMAIL>',
    username: 'subcity_admin',
    first_name: 'Subcity',
    last_name: 'Admin',
    role: 'subcity_admin',
    tenant: 2,
    tenant_name: 'Bole',
    is_active: true,
    date_joined: '2023-11-15T12:00:00Z'
  },
  {
    id: 4,
    email: '<EMAIL>',
    username: 'kebele_admin',
    first_name: 'Kebele',
    last_name: 'Admin',
    role: 'kebele_admin',
    tenant: 3,
    tenant_name: 'Bole 01',
    is_active: true,
    date_joined: '2023-11-15T12:00:00Z'
  },
  {
    id: 5,
    email: '<EMAIL>',
    username: 'clerk',
    first_name: 'Clerk',
    last_name: 'User',
    role: 'clerk',
    tenant: 3,
    tenant_name: 'Bole 01',
    is_active: true,
    date_joined: '2023-11-15T12:00:00Z'
  }
];

const UserList = () => {
  const navigate = useNavigate();
  const { t } = useLocalization();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [roleFilter, setRoleFilter] = useState('');

  useEffect(() => {
    // In a real implementation, this would fetch data from the API
    // For now, we'll just use mock data
    const timer = setTimeout(() => {
      setUsers(mockUsers);
      setTotalPages(Math.ceil(mockUsers.length / 10));
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    // In a real implementation, this would trigger an API call with the search term
  };

  const handlePageChange = (event, value) => {
    setPage(value);
    // In a real implementation, this would fetch the next page of data
  };

  const handleRoleFilterChange = (role) => {
    setRoleFilter(role === roleFilter ? '' : role);
    // In a real implementation, this would trigger an API call with the filter
  };

  const handleViewUser = (id) => {
    navigate(`/users/${id}`);
  };

  const handleEditUser = (id) => {
    navigate(`/users/${id}`);
  };

  const handleDeleteUser = (id) => {
    // In a real implementation, this would call an API to delete the user
    console.log(`Delete user with ID: ${id}`);
  };

  const getRoleChip = (role) => {
    switch (role) {
      case 'superadmin':
        return <Chip icon={<AdminIcon />} label="Super Admin" color="error" size="small" />;
      case 'city_admin':
        return <Chip icon={<AdminIcon />} label="City Admin" color="primary" size="small" />;
      case 'subcity_admin':
        return <Chip icon={<AdminIcon />} label="Subcity Admin" color="secondary" size="small" />;
      case 'kebele_admin':
        return <Chip icon={<AdminIcon />} label="Kebele Admin" color="info" size="small" />;
      case 'clerk':
        return <Chip icon={<UserIcon />} label="Clerk" color="success" size="small" />;
      default:
        return <Chip label={role} size="small" />;
    }
  };

  const filteredUsers = users.filter(user => 
    (roleFilter ? user.role === roleFilter : true) &&
    (searchTerm ? 
      user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (user.first_name && user.first_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (user.last_name && user.last_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (user.tenant_name && user.tenant_name.toLowerCase().includes(searchTerm.toLowerCase()))
      : true
    )
  );

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          {t('users', 'Users')}
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => navigate('/users/create')}
        >
          {t('add_new_user', 'Add New User')}
        </Button>
      </Box>

      {/* Search and Filter Bar */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder={t('search_users_by_name_email_tenant', 'Search users by name, email, or tenant...')}
                value={searchTerm}
                onChange={handleSearch}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                <Chip
                  icon={<AdminIcon />}
                  label="Super Admin"
                  color={roleFilter === 'superadmin' ? 'primary' : 'default'}
                  onClick={() => handleRoleFilterChange('superadmin')}
                  clickable
                />
                <Chip
                  icon={<AdminIcon />}
                  label="City Admin"
                  color={roleFilter === 'city_admin' ? 'primary' : 'default'}
                  onClick={() => handleRoleFilterChange('city_admin')}
                  clickable
                />
                <Chip
                  icon={<AdminIcon />}
                  label="Subcity Admin"
                  color={roleFilter === 'subcity_admin' ? 'primary' : 'default'}
                  onClick={() => handleRoleFilterChange('subcity_admin')}
                  clickable
                />
                <Chip
                  icon={<AdminIcon />}
                  label="Kebele Admin"
                  color={roleFilter === 'kebele_admin' ? 'primary' : 'default'}
                  onClick={() => handleRoleFilterChange('kebele_admin')}
                  clickable
                />
                <Chip
                  icon={<UserIcon />}
                  label="Clerk"
                  color={roleFilter === 'clerk' ? 'primary' : 'default'}
                  onClick={() => handleRoleFilterChange('clerk')}
                  clickable
                />
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Users Table */}
      <TableContainer component={Paper} sx={{ mb: 4 }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>{t('user', 'User')}</TableCell>
              <TableCell>{t('email', 'Email')}</TableCell>
              <TableCell>{t('role', 'Role')}</TableCell>
              <TableCell>{t('tenant', 'Tenant')}</TableCell>
              <TableCell>{t('status', 'Status')}</TableCell>
              <TableCell align="right">{t('actions', 'Actions')}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredUsers.map((user) => (
              <TableRow key={user.id}>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Avatar sx={{ mr: 2 }}>
                      {user.first_name ? user.first_name.charAt(0) : user.username.charAt(0)}
                    </Avatar>
                    <Box>
                      <Typography variant="body1">
                        {user.first_name && user.last_name 
                          ? `${user.first_name} ${user.last_name}` 
                          : user.username}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {user.username}
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>
                <TableCell>{user.email}</TableCell>
                <TableCell>{getRoleChip(user.role)}</TableCell>
                <TableCell>{user.tenant_name || '-'}</TableCell>
                <TableCell>
                  <Chip 
                    label={user.is_active ? t('active', 'Active') : t('inactive', 'Inactive')}
                    color={user.is_active ? 'success' : 'default'}
                    size="small"
                  />
                </TableCell>
                <TableCell align="right">
                  <Tooltip title="View User">
                    <IconButton
                      color="primary"
                      onClick={() => handleViewUser(user.id)}
                    >
                      <ViewIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Edit User">
                    <IconButton
                      color="secondary"
                      onClick={() => handleEditUser(user.id)}
                    >
                      <EditIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Delete User">
                    <IconButton
                      color="error"
                      onClick={() => handleDeleteUser(user.id)}
                      disabled={user.role === 'superadmin'} // Prevent deleting superadmin
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination */}
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <Pagination
          count={totalPages}
          page={page}
          onChange={handlePageChange}
          color="primary"
        />
      </Box>
    </Box>
  );
};

export default UserList;
