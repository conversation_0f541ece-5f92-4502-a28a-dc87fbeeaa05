/**
 * Translation Loader Hook
 * Provides utilities for loading and managing translations dynamically
 */

import { useState, useEffect, useCallback } from 'react';
import { useLocalization } from '../contexts/LocalizationContext';

/**
 * Hook for loading translations with loading states and error handling
 */
export const useTranslationLoader = () => {
  const { currentLanguage, changeLanguage } = useLocalization();
  const [loadingStates, setLoadingStates] = useState({});
  const [errors, setErrors] = useState({});

  /**
   * Load translations for a specific language
   */
  const loadLanguage = useCallback(async (languageCode) => {
    if (languageCode === currentLanguage) {
      return true;
    }

    const loadingKey = `load_${languageCode}`;
    
    setLoadingStates(prev => ({ ...prev, [loadingKey]: true }));
    setErrors(prev => ({ ...prev, [loadingKey]: null }));

    try {
      const success = await changeLanguage(languageCode);
      
      if (success) {
        setLoadingStates(prev => ({ ...prev, [loadingKey]: false }));
        return true;
      } else {
        throw new Error(`Failed to load language: ${languageCode}`);
      }
    } catch (error) {
      console.error(`Error loading language ${languageCode}:`, error);
      setErrors(prev => ({ ...prev, [loadingKey]: error.message }));
      setLoadingStates(prev => ({ ...prev, [loadingKey]: false }));
      return false;
    }
  }, [currentLanguage, changeLanguage]);

  /**
   * Preload multiple languages
   */
  const preloadLanguages = useCallback(async (languageCodes) => {
    const results = {};
    
    for (const langCode of languageCodes) {
      if (langCode !== currentLanguage) {
        try {
          // In a real implementation, this would preload without switching
          // For now, we'll just mark as available for quick switching
          results[langCode] = { preloaded: true, error: null };
        } catch (error) {
          results[langCode] = { preloaded: false, error: error.message };
        }
      }
    }
    
    return results;
  }, [currentLanguage]);

  /**
   * Check if a language is currently loading
   */
  const isLanguageLoading = useCallback((languageCode) => {
    return loadingStates[`load_${languageCode}`] || false;
  }, [loadingStates]);

  /**
   * Get error for a specific language
   */
  const getLanguageError = useCallback((languageCode) => {
    return errors[`load_${languageCode}`] || null;
  }, [errors]);

  /**
   * Clear error for a specific language
   */
  const clearLanguageError = useCallback((languageCode) => {
    setErrors(prev => ({ ...prev, [`load_${languageCode}`]: null }));
  }, []);

  return {
    loadLanguage,
    preloadLanguages,
    isLanguageLoading,
    getLanguageError,
    clearLanguageError,
    loadingStates,
    errors
  };
};

/**
 * Hook for translation validation and missing key detection
 */
export const useTranslationValidator = () => {
  const { t, translationStats } = useLocalization();
  const [missingKeys, setMissingKeys] = useState(new Set());
  const [validationEnabled, setValidationEnabled] = useState(
    process.env.NODE_ENV === 'development'
  );

  /**
   * Validate a translation key and track if missing
   */
  const validateTranslation = useCallback((key, defaultValue = null) => {
    const translation = t(key, defaultValue);
    
    if (validationEnabled) {
      // Check if translation is missing (returned key or default)
      const isMissing = translation === key || 
                       (defaultValue && translation === defaultValue);
      
      if (isMissing) {
        setMissingKeys(prev => new Set([...prev, key]));
      } else {
        setMissingKeys(prev => {
          const newSet = new Set(prev);
          newSet.delete(key);
          return newSet;
        });
      }
    }
    
    return translation;
  }, [t, validationEnabled]);

  /**
   * Get all missing translation keys
   */
  const getMissingKeys = useCallback(() => {
    return Array.from(missingKeys);
  }, [missingKeys]);

  /**
   * Clear missing keys tracking
   */
  const clearMissingKeys = useCallback(() => {
    setMissingKeys(new Set());
  }, []);

  /**
   * Export missing keys for translation work
   */
  const exportMissingKeys = useCallback(() => {
    const missing = Array.from(missingKeys);
    const exportData = {
      timestamp: new Date().toISOString(),
      language: 'current',
      missingKeys: missing.map(key => ({
        key,
        context: 'frontend',
        priority: key.length < 20 ? 'high' : 'medium'
      }))
    };
    
    // Create downloadable file
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `missing-translations-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    return exportData;
  }, [missingKeys]);

  return {
    validateTranslation,
    getMissingKeys,
    clearMissingKeys,
    exportMissingKeys,
    missingKeysCount: missingKeys.size,
    validationEnabled,
    setValidationEnabled
  };
};

/**
 * Hook for translation performance monitoring
 */
export const useTranslationPerformance = () => {
  const [metrics, setMetrics] = useState({
    translationCalls: 0,
    cacheHits: 0,
    cacheMisses: 0,
    averageTime: 0
  });

  const recordTranslationCall = useCallback((key, time, fromCache = false) => {
    setMetrics(prev => ({
      translationCalls: prev.translationCalls + 1,
      cacheHits: prev.cacheHits + (fromCache ? 1 : 0),
      cacheMisses: prev.cacheMisses + (fromCache ? 0 : 1),
      averageTime: (prev.averageTime * prev.translationCalls + time) / (prev.translationCalls + 1)
    }));
  }, []);

  const resetMetrics = useCallback(() => {
    setMetrics({
      translationCalls: 0,
      cacheHits: 0,
      cacheMisses: 0,
      averageTime: 0
    });
  }, []);

  const getCacheHitRate = useCallback(() => {
    const total = metrics.cacheHits + metrics.cacheMisses;
    return total > 0 ? (metrics.cacheHits / total) * 100 : 0;
  }, [metrics]);

  return {
    metrics,
    recordTranslationCall,
    resetMetrics,
    getCacheHitRate
  };
};

export default useTranslationLoader;
