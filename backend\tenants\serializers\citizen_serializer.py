from rest_framework import serializers
# Import shared models from tenants app (SHARED_APPS)
from ..models.citizen import (
    Religion, CitizenStatus, MaritalStatus,
    EmploymentType, Relationship, CurrentStatus
)
from ..models.city import Country
# Import tenant-specific models from citizens app (TENANT_APPS) - these are the actual models used in tenant schemas
from citizens.models import Citizen, Parent, Child, EmergencyContact, Spouse, Biometric, Photo, Document

class ReligionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Religion
        fields = ('id', 'name', 'is_active', 'created_at', 'updated_at')
        read_only_fields = ('created_at', 'updated_at')

class TenantCitizenStatusSerializer(serializers.ModelSerializer):
    class Meta:
        model = CitizenStatus
        fields = ('id', 'name', 'is_active', 'created_at', 'updated_at')
        read_only_fields = ('created_at', 'updated_at')

class MaritalStatusSerializer(serializers.ModelSerializer):
    class Meta:
        model = MaritalStatus
        fields = ('id', 'name', 'is_active', 'created_at', 'updated_at')
        read_only_fields = ('created_at', 'updated_at')

# DocumentType is now handled by shared schema - no need for tenant-specific serializer

class EmploymentTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = EmploymentType
        fields = ('id', 'name', 'description', 'is_active', 'created_at', 'updated_at')
        read_only_fields = ('created_at', 'updated_at')

class ParentSerializer(serializers.ModelSerializer):
    nationality_name = serializers.CharField(source='nationality.name', read_only=True)

    class Meta:
        model = Parent
        fields = (
            'id', 'citizen', 'first_name', 'middle_name', 'last_name',
            'first_name_am', 'middle_name_am', 'last_name_am',
            'gender', 'phone', 'email', 'is_resident', 'nationality',
            'nationality_name', 'linked_citizen', 'is_active',
            'created_at', 'updated_at'
        )
        read_only_fields = ('created_at', 'updated_at')

class ChildSerializer(serializers.ModelSerializer):
    nationality_name = serializers.CharField(source='nationality.name', read_only=True)

    class Meta:
        model = Child
        fields = (
            'id', 'citizen', 'first_name', 'middle_name', 'last_name',
            'first_name_am', 'middle_name_am', 'last_name_am',
            'date_of_birth', 'nationality', 'nationality_name', 'is_active',
            'created_at', 'updated_at'
        )
        read_only_fields = ('created_at', 'updated_at')

class EmergencyContactSerializer(serializers.ModelSerializer):
    nationality_name = serializers.CharField(source='nationality.name', read_only=True)

    class Meta:
        model = EmergencyContact
        fields = (
            'id', 'citizen', 'first_name', 'middle_name', 'last_name',
            'relationship', 'phone', 'email', 'nationality', 'nationality_name',
            'primary_contact', 'is_active', 'created_at', 'updated_at'
        )
        read_only_fields = ('created_at', 'updated_at')

    def validate(self, data):
        if not data.get('phone') and not data.get('email'):
            raise serializers.ValidationError("At least one contact method (phone or email) must be provided.")
        return data

class SpouseSerializer(serializers.ModelSerializer):
    nationality_name = serializers.CharField(source='nationality.name', read_only=True)

    class Meta:
        model = Spouse
        fields = (
            'id', 'citizen', 'first_name', 'middle_name', 'last_name',
            'phone', 'email', 'nationality', 'nationality_name',
            'primary_contact', 'is_active', 'created_at', 'updated_at'
        )
        read_only_fields = ('created_at', 'updated_at')

    def validate(self, data):
        if not data.get('phone') and not data.get('email'):
            raise serializers.ValidationError("At least one contact method (phone or email) must be provided.")
        return data

class DocumentSerializer(serializers.ModelSerializer):
    document_type_name = serializers.SerializerMethodField()

    class Meta:
        model = Document
        fields = (
            'id', 'document_type', 'document_type_name', 'citizen',
            'document_file', 'issue_date', 'expiry_date', 'is_active',
            'created_at', 'updated_at'
        )
        read_only_fields = ('created_at', 'updated_at')

    def get_document_type_name(self, obj):
        """Get document type name from shared schema"""
        try:
            from shared.models import DocumentType
            doc_type = DocumentType.objects.get(id=obj.document_type)
            return doc_type.name
        except:
            return "Unknown"

    def to_internal_value(self, data):
        """Convert external representation to internal value"""
        print(f"🔍 DocumentSerializer.to_internal_value called with data: {data}")

        # Handle document_type_id from frontend
        if 'document_type_id' in data:
            document_type_id = data['document_type_id']
            print(f"🔍 Processing document_type_id: {document_type_id}")

            try:
                from shared.models import DocumentType as SharedDocumentType
                # Validate that the shared document type exists
                shared_doc_type = SharedDocumentType.objects.get(id=document_type_id)
                print(f"🔍 Found shared DocumentType: {shared_doc_type.name}")

                # Replace document_type_id with document_type
                data = data.copy()  # Make a copy to avoid modifying original
                data['document_type'] = document_type_id
                del data['document_type_id']

            except SharedDocumentType.DoesNotExist:
                print(f"❌ SharedDocumentType with ID {document_type_id} does not exist")
                raise serializers.ValidationError({"document_type": "Invalid document type selected."})
            except Exception as e:
                print(f"❌ Error processing document type: {str(e)}")
                raise serializers.ValidationError({"document_type": f"Error processing document type: {str(e)}"})

        # Handle citizen_id from frontend
        if 'citizen_id' in data:
            citizen_id = data['citizen_id']
            print(f"🔍 Processing citizen_id: {citizen_id}")

            try:
                from citizens.models import Citizen
                citizen = Citizen.objects.get(id=citizen_id)
                print(f"🔍 Found citizen: {citizen.first_name} {citizen.last_name}")

                # Replace citizen_id with citizen
                data = data.copy()  # Make a copy to avoid modifying original
                data['citizen'] = citizen.id  # Store the ID for the model
                del data['citizen_id']

            except Citizen.DoesNotExist:
                print(f"❌ Citizen with ID {citizen_id} does not exist in tenant schema")
                raise serializers.ValidationError({"citizen": "Invalid citizen selected."})
            except Exception as e:
                print(f"❌ Error validating citizen: {str(e)}")
                raise serializers.ValidationError({"citizen": f"Error validating citizen: {str(e)}"})

        print(f"🔍 Final data before super().to_internal_value(): {data}")
        return super().to_internal_value(data)

    def create(self, validated_data):
        """Create document with validated data"""
        print(f"🔍 DocumentSerializer.create called with validated_data: {validated_data}")

        try:
            result = super().create(validated_data)
            print(f"🔍 Document created successfully: {result}")
            return result
        except Exception as e:
            print(f"❌ Error in super().create(): {str(e)}")
            import traceback
            print(f"❌ Traceback: {traceback.format_exc()}")
            raise

class RelationshipSerializer(serializers.ModelSerializer):
    class Meta:
        model = Relationship
        fields = ('id', 'name', 'description', 'created_at', 'updated_at')
        read_only_fields = ('created_at', 'updated_at')

class CurrentStatusSerializer(serializers.ModelSerializer):
    class Meta:
        model = CurrentStatus
        fields = ('id', 'name', 'created_at', 'updated_at')
        read_only_fields = ('created_at', 'updated_at')

class BiometricSerializer(serializers.ModelSerializer):
    class Meta:
        model = Biometric
        fields = (
            'id', 'citizen', 'left_hand_fingerprint', 'right_hand_fingerprint',
            'left_thumb_fingerprint', 'right_thumb_fingerprint',
            'left_eye_iris_scan', 'right_eye_iris_scan',
            'created_at', 'updated_at'
        )
        read_only_fields = ('created_at', 'updated_at')

class PhotoSerializer(serializers.ModelSerializer):
    class Meta:
        model = Photo
        fields = ('id', 'citizen', 'photo', 'upload_date', 'created_at', 'updated_at')
        read_only_fields = ('upload_date', 'created_at', 'updated_at')

class TenantCitizenSerializer(serializers.ModelSerializer):
    children = ChildSerializer(many=True, read_only=True)
    parents = ParentSerializer(many=True, read_only=True)
    emergency_contacts = EmergencyContactSerializer(many=True, read_only=True)
    spouse_records = SpouseSerializer(many=True, read_only=True)
    biometric_record = BiometricSerializer(read_only=True)
    photo_record = PhotoSerializer(read_only=True)
    documents = DocumentSerializer(many=True, read_only=True)
    religion_name = serializers.CharField(source='religion.name', read_only=True)
    status_name = serializers.CharField(source='status.name', read_only=True)
    marital_status_name = serializers.CharField(source='marital_status.name', read_only=True)
    # ✅ Removed current_status_name since current_status field doesn't exist in citizens.models.Citizen
    ketena_name = serializers.SerializerMethodField()
    has_id_card = serializers.SerializerMethodField()

    class Meta:
        model = Citizen
        fields = (
            'id', 'uuid', 'digital_id',  # ✅ Added uuid field back for transfers
            'first_name', 'middle_name', 'last_name',
            'first_name_am', 'middle_name_am', 'last_name_am',
            'date_of_birth', 'gender', 'blood_type', 'disability', 'photo',  # ✅ Added blood_type and disability
            'religion', 'religion_name', 'subcity', 'kebele', 'ketena', 'ketena_name',
            'status', 'status_name', 'house_number', 'phone', 'email',
            'nationality', 'region',  # ✅ Removed id_issue_date, id_expiry_date (don't exist in citizens.models.Citizen)
            'employment', 'employee_type', 'organization_name',
            'marital_status', 'marital_status_name',
            'is_resident', 'is_active',  # ✅ Removed current_status, current_status_name (don't exist in citizens.models.Citizen)
            'has_id_card',  # ✅ Added has_id_card field
            'children', 'parents', 'emergency_contacts', 'spouse_records',
            'biometric_record', 'photo_record', 'documents',
            'created_at', 'updated_at'
        )
        read_only_fields = ('created_at', 'updated_at')

    def get_has_id_card(self, obj):
        """Check if the citizen has any ID cards."""
        try:
            from idcards.models import IDCard
            return IDCard.objects.filter(citizen=obj).exists()
        except:
            return False

    def get_ketena_name(self, obj):
        """Get the ketena name from the shared Ketena model."""
        if obj.ketena:
            try:
                from shared.models import Ketena
                ketena = Ketena.objects.get(id=obj.ketena)
                return ketena.name
            except Ketena.DoesNotExist:
                return None
        return None

    def validate(self, data):
        # Validate age is greater than 18 based on date_of_birth
        if 'date_of_birth' in data and data['date_of_birth']:
            from django.utils import timezone
            from datetime import timedelta

            today = timezone.now().date()
            min_birth_date = today - timedelta(days=18*365)  # Approximate 18 years
            if data['date_of_birth'] > min_birth_date:
                raise serializers.ValidationError("Citizen must be at least 18 years old.")
        return data


class EmergencyContactCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating emergency contacts (without citizen field)"""

    class Meta:
        model = EmergencyContact
        exclude = ['citizen', 'created_at', 'updated_at']

    def validate(self, data):
        if not data.get('phone') and not data.get('email'):
            raise serializers.ValidationError("At least one contact method (phone or email) must be provided.")
        return data


class ParentCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating parents (without citizen field)"""

    class Meta:
        model = Parent
        exclude = ['citizen', 'created_at', 'updated_at']


class ChildCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating children (without citizen field)"""

    class Meta:
        model = Child
        exclude = ['citizen', 'created_at', 'updated_at']


class SpouseCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating spouse (without citizen field)"""

    class Meta:
        model = Spouse
        exclude = ['citizen', 'created_at', 'updated_at']

    def validate(self, data):
        if not data.get('phone') and not data.get('email'):
            raise serializers.ValidationError("At least one contact method (phone or email) must be provided.")
        return data


class BiometricCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating biometric data (without citizen field)"""

    class Meta:
        model = Biometric
        exclude = ['citizen', 'created_at', 'updated_at']


class CitizenCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating citizens with all related family information
    """
    # Nested serializers for related models (using create serializers without citizen field)
    children_data = ChildCreateSerializer(many=True, required=False, write_only=True)
    parents_data = ParentCreateSerializer(many=True, required=False, write_only=True)
    emergency_contacts_data = EmergencyContactCreateSerializer(many=True, required=False, write_only=True)
    spouse_data = SpouseCreateSerializer(required=False, write_only=True)
    biometric_data = BiometricCreateSerializer(required=False, write_only=True)

    # Read-only nested data for response
    children = ChildSerializer(many=True, read_only=True)
    parents = ParentSerializer(many=True, read_only=True)
    emergency_contacts = EmergencyContactSerializer(many=True, read_only=True)
    spouse_records = SpouseSerializer(many=True, read_only=True)
    biometric_record = BiometricSerializer(read_only=True)

    class Meta:
        model = Citizen
        fields = (
            'id', 'uuid', 'digital_id',  # ✅ Added uuid field back for transfers
            'first_name', 'middle_name', 'last_name',
            'first_name_am', 'middle_name_am', 'last_name_am',
            'date_of_birth', 'gender', 'blood_type', 'disability', 'photo',  # ✅ Added blood_type and disability
            'religion', 'subcity', 'kebele', 'ketena',
            'status', 'house_number', 'phone', 'email',
            'nationality', 'region',  # ✅ Removed id_issue_date, id_expiry_date (don't exist in citizens.models.Citizen)
            'employment', 'employee_type', 'organization_name',
            'marital_status', 'is_resident', 'is_active',  # ✅ Removed current_status (doesn't exist in citizens.models.Citizen)
            # Write-only nested data
            'children_data', 'parents_data', 'emergency_contacts_data', 'spouse_data', 'biometric_data',
            # Read-only nested data
            'children', 'parents', 'emergency_contacts', 'spouse_records', 'biometric_record',
            'created_at', 'updated_at'
        )
        read_only_fields = ('created_at', 'updated_at', 'uuid', 'digital_id')  # ✅ Added uuid back to read-only fields

    def create(self, validated_data):
        # Extract nested data
        children_data = validated_data.pop('children_data', [])
        parents_data = validated_data.pop('parents_data', [])
        emergency_contacts_data = validated_data.pop('emergency_contacts_data', [])
        spouse_data = validated_data.pop('spouse_data', None)
        biometric_data = validated_data.pop('biometric_data', None)

        # Debug logging
        print(f"🔍 Creating citizen with family data:")
        print(f"  - Children: {len(children_data)} records")
        print(f"  - Parents: {len(parents_data)} records")
        print(f"  - Emergency Contacts: {len(emergency_contacts_data)} records")
        print(f"  - Spouse: {'Yes' if spouse_data else 'No'}")
        print(f"  - Biometric: {'Yes' if biometric_data else 'No'}")
        if spouse_data:
            print(f"  - Spouse data: {spouse_data}")
        if biometric_data:
            print(f"  - Biometric data: {biometric_data}")

        # Don't set digital_id here - let the model's save() method generate it automatically
        # The Citizen model has a generate_digital_id() method that creates proper format

        # Create the citizen
        citizen = Citizen.objects.create(**validated_data)
        print(f"✅ Citizen created with ID: {citizen.id}")

        # Create related objects
        self._create_children(citizen, children_data)
        self._create_parents(citizen, parents_data)
        self._create_emergency_contacts(citizen, emergency_contacts_data)
        if spouse_data:
            print(f"🔍 Creating spouse for citizen {citizen.id}")
            self._create_spouse(citizen, spouse_data)
            print(f"✅ Spouse created successfully")
        if biometric_data:
            print(f"🔍 Creating biometric record for citizen {citizen.id}")
            print(f"🔍 Biometric data keys: {list(biometric_data.keys())}")
            print(f"🔍 Left thumb present: {'left_thumb_fingerprint' in biometric_data and biometric_data['left_thumb_fingerprint']}")
            print(f"🔍 Right thumb present: {'right_thumb_fingerprint' in biometric_data and biometric_data['right_thumb_fingerprint']}")

            # Note: Duplicate detection is now handled during biometric capture step
            # This provides immediate feedback to clerks instead of waiting until submission
            print(f"✅ Biometric data validated during capture - proceeding with creation")

            self._create_biometric(citizen, biometric_data)
            print(f"✅ Biometric record created successfully")

        return citizen

    def _create_children(self, citizen, children_data):
        for child_data in children_data:
            Child.objects.create(citizen=citizen, **child_data)

    def _create_parents(self, citizen, parents_data):
        for parent_data in parents_data:
            Parent.objects.create(citizen=citizen, **parent_data)

    def _create_emergency_contacts(self, citizen, emergency_contacts_data):
        for contact_data in emergency_contacts_data:
            EmergencyContact.objects.create(citizen=citizen, **contact_data)

    def _create_spouse(self, citizen, spouse_data):
        Spouse.objects.create(citizen=citizen, **spouse_data)

    def _check_biometric_duplicates(self, biometric_data):
        """Check for duplicate fingerprints across all tenants"""
        try:
            # Import here to avoid circular imports
            from biometrics.fingerprint_processing import FingerprintProcessor

            processor = FingerprintProcessor()

            # Prepare fingerprint data for duplicate checking
            fingerprint_check_data = {
                'left_thumb_fingerprint': biometric_data.get('left_thumb_fingerprint'),
                'right_thumb_fingerprint': biometric_data.get('right_thumb_fingerprint')
            }

            # Only check if we have at least one fingerprint
            if not fingerprint_check_data['left_thumb_fingerprint'] and not fingerprint_check_data['right_thumb_fingerprint']:
                return {'has_duplicates': False, 'matches': [], 'total_matches': 0}

            print(f"🔍 Checking for duplicate fingerprints across all kebeles...")
            duplicate_result = processor.check_duplicates(fingerprint_check_data)

            if duplicate_result['has_duplicates']:
                print(f"⚠️ FRAUD ALERT: Found {duplicate_result['total_matches']} potential duplicate fingerprint(s)")
                for match in duplicate_result['matches'][:3]:  # Log first 3 matches
                    print(f"   - {match['citizen_name']} in {match['tenant_name']} (Score: {match['match_score']:.2f})")
            else:
                print(f"✅ No duplicate fingerprints found - Registration can proceed")

            return duplicate_result

        except Exception as e:
            print(f"❌ Error checking biometric duplicates: {e}")
            # In case of error, allow registration but log the issue
            return {'has_duplicates': False, 'matches': [], 'total_matches': 0, 'error': str(e)}

    def _create_biometric(self, citizen, biometric_data):
        Biometric.objects.create(citizen=citizen, **biometric_data)
