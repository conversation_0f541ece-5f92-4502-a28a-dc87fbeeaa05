<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .nav-item {
            padding: 8px;
            margin: 4px 0;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            border-left: 3px solid #007bff;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <h1>🔍 Navigation Debug Test</h1>
    
    <div class="container info">
        <h3>📋 Debug Steps</h3>
        <p>This page will help debug why navigation menus are not showing for city admin users:</p>
        <ol>
            <li>Test login and check JWT token structure</li>
            <li>Verify groups are included in JWT token</li>
            <li>Test navigation logic with actual user data</li>
            <li>Check permission filtering</li>
        </ol>
    </div>

    <div class="container">
        <h3>🔐 Step 1: Login Test</h3>
        <div class="step">
            <h4>City Admin Login</h4>
            <button onclick="testCityAdminLogin()">Test City Admin Login</button>
            <div id="login-result"></div>
        </div>
        
        <div class="step">
            <h4>Superadmin Login (for comparison)</h4>
            <button onclick="testSuperadminLogin()">Test Superadmin Login</button>
            <div id="superadmin-result"></div>
        </div>
    </div>

    <div class="container">
        <h3>🔍 Step 2: JWT Token Analysis</h3>
        <button onclick="analyzeCurrentToken()">Analyze Current JWT Token</button>
        <div id="token-analysis"></div>
    </div>

    <div class="container">
        <h3>🧭 Step 3: Navigation Logic Test</h3>
        <button onclick="testNavigationLogic()">Test Navigation Logic</button>
        <div id="navigation-test"></div>
    </div>

    <div class="container">
        <h3>🔧 Step 4: Permission Check Test</h3>
        <button onclick="testPermissionChecks()">Test Permission Checks</button>
        <div id="permission-test"></div>
    </div>

    <script>
        let currentUser = null;
        let currentToken = null;

        async function testCityAdminLogin() {
            const resultDiv = document.getElementById('login-result');
            resultDiv.innerHTML = '<p>🔄 Testing city admin login...</p>';

            try {
                const response = await fetch('http://localhost:8000/api/auth/token/', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'testpass123'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    currentToken = data.access;
                    
                    // Decode JWT token
                    const payload = JSON.parse(atob(currentToken.split('.')[1]));
                    currentUser = payload;
                    
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ City Admin Login Successful!</h4>
                            <p><strong>Email:</strong> ${payload.email}</p>
                            <p><strong>Role:</strong> ${payload.role}</p>
                            <p><strong>Groups:</strong> ${JSON.stringify(payload.groups || [])}</p>
                            <p><strong>Permissions Count:</strong> ${(payload.permissions || []).length}</p>
                            <p><strong>Is Superuser:</strong> ${payload.is_superuser}</p>
                        </div>
                    `;
                } else {
                    const error = await response.text();
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Login Failed</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${error}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Network Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function testSuperadminLogin() {
            const resultDiv = document.getElementById('superadmin-result');
            resultDiv.innerHTML = '<p>🔄 Testing superadmin login...</p>';

            try {
                const response = await fetch('http://localhost:8000/api/auth/token/', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    const payload = JSON.parse(atob(data.access.split('.')[1]));
                    
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Superadmin Login Successful!</h4>
                            <p><strong>Email:</strong> ${payload.email}</p>
                            <p><strong>Role:</strong> ${payload.role}</p>
                            <p><strong>Groups:</strong> ${JSON.stringify(payload.groups || [])}</p>
                            <p><strong>Permissions Count:</strong> ${(payload.permissions || []).length}</p>
                            <p><strong>Is Superuser:</strong> ${payload.is_superuser}</p>
                        </div>
                    `;
                } else {
                    const error = await response.text();
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Superadmin Login Failed</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${error}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Network Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        function analyzeCurrentToken() {
            const resultDiv = document.getElementById('token-analysis');
            
            if (!currentToken || !currentUser) {
                resultDiv.innerHTML = `
                    <div class="warning">
                        <h4>⚠️ No Token Available</h4>
                        <p>Please login first to analyze the token</p>
                    </div>
                `;
                return;
            }

            const groups = currentUser.groups || [];
            const permissions = currentUser.permissions || [];
            
            // Check for key city admin permissions
            const cityAdminPermissions = [
                'view_child_subcities_data',
                'create_subcity_users',
                'view_city_reports',
                'view_citizens_list',
                'view_id_cards_list'
            ];

            const permissionStatus = cityAdminPermissions.map(perm => {
                const hasPermission = permissions.includes(perm);
                return `<li>${hasPermission ? '✅' : '❌'} ${perm}</li>`;
            }).join('');

            resultDiv.innerHTML = `
                <div class="info">
                    <h4>🔍 JWT Token Analysis</h4>
                    <p><strong>User Email:</strong> ${currentUser.email}</p>
                    <p><strong>Role:</strong> ${currentUser.role}</p>
                    <p><strong>Is Superuser:</strong> ${currentUser.is_superuser}</p>
                    
                    <h5>Groups (${groups.length}):</h5>
                    <pre>${JSON.stringify(groups, null, 2)}</pre>
                    
                    <h5>Key City Admin Permissions:</h5>
                    <ul>${permissionStatus}</ul>
                    
                    <h5>All Permissions (${permissions.length}):</h5>
                    <details>
                        <summary>Click to view all permissions</summary>
                        <pre>${JSON.stringify(permissions, null, 2)}</pre>
                    </details>
                </div>
            `;
        }

        function testNavigationLogic() {
            const resultDiv = document.getElementById('navigation-test');
            
            if (!currentUser) {
                resultDiv.innerHTML = `
                    <div class="warning">
                        <h4>⚠️ No User Data</h4>
                        <p>Please login first to test navigation logic</p>
                    </div>
                `;
                return;
            }

            // Simulate the frontend navigation logic
            const userGroups = currentUser.groups || [];
            const permissions = currentUser.permissions || [];
            
            function hasPermission(perm) {
                return permissions.includes(perm);
            }
            
            function isInGroup(groupName) {
                return userGroups.includes(groupName);
            }

            const navigationItems = ['Dashboard']; // Always present
            let navigationPath = 'Unknown';

            // Test navigation logic
            if (currentUser.is_superuser || currentUser.role === 'superadmin' || isInGroup('super_admin')) {
                navigationPath = 'Super Admin Path';
                if (hasPermission('manage_tenants')) navigationItems.push('Tenants');
                if (hasPermission('manage_all_users')) navigationItems.push('System Users');
                if (hasPermission('manage_system_settings')) navigationItems.push('System Settings');
            } else if (isInGroup('city_admin')) {
                navigationPath = 'City Admin Path';
                if (hasPermission('view_child_subcities_data')) navigationItems.push('Citizen Directory');
                if (hasPermission('create_subcity_users')) navigationItems.push('Subcity Users');
                if (hasPermission('view_city_reports')) navigationItems.push('Reports');
            } else if (isInGroup('subcity_admin')) {
                navigationPath = 'SubCity Admin Path';
                // Add subcity navigation items
            } else {
                navigationPath = 'Dynamic/Custom Path';
                // Add dynamic navigation based on permissions
            }

            resultDiv.innerHTML = `
                <div class="info">
                    <h4>🧭 Navigation Logic Test</h4>
                    <p><strong>Navigation Path:</strong> ${navigationPath}</p>
                    <p><strong>User Groups:</strong> ${JSON.stringify(userGroups)}</p>
                    <p><strong>Group Checks:</strong></p>
                    <ul>
                        <li>isInGroup('super_admin'): ${isInGroup('super_admin')}</li>
                        <li>isInGroup('city_admin'): ${isInGroup('city_admin')}</li>
                        <li>isInGroup('subcity_admin'): ${isInGroup('subcity_admin')}</li>
                        <li>isInGroup('kebele_leader'): ${isInGroup('kebele_leader')}</li>
                        <li>isInGroup('clerk'): ${isInGroup('clerk')}</li>
                    </ul>
                    
                    <h5>Generated Navigation Items:</h5>
                    <div>
                        ${navigationItems.map(item => `<div class="nav-item">${item}</div>`).join('')}
                    </div>
                </div>
            `;
        }

        function testPermissionChecks() {
            const resultDiv = document.getElementById('permission-test');
            
            if (!currentUser) {
                resultDiv.innerHTML = `
                    <div class="warning">
                        <h4>⚠️ No User Data</h4>
                        <p>Please login first to test permissions</p>
                    </div>
                `;
                return;
            }

            const permissions = currentUser.permissions || [];
            
            // Test key permissions for city admin
            const testPermissions = [
                'view_dashboard',
                'view_child_subcities_data',
                'create_subcity_users', 
                'view_city_reports',
                'manage_tenants',
                'manage_all_users',
                'manage_system_settings'
            ];

            const permissionResults = testPermissions.map(perm => {
                const hasIt = permissions.includes(perm);
                return `<li>${hasIt ? '✅' : '❌'} ${perm}</li>`;
            }).join('');

            resultDiv.innerHTML = `
                <div class="info">
                    <h4>🔧 Permission Check Results</h4>
                    <p><strong>Total Permissions:</strong> ${permissions.length}</p>
                    
                    <h5>Key Permission Tests:</h5>
                    <ul>${permissionResults}</ul>
                    
                    <h5>Expected for City Admin:</h5>
                    <ul>
                        <li>✅ view_dashboard</li>
                        <li>✅ view_child_subcities_data</li>
                        <li>✅ create_subcity_users</li>
                        <li>✅ view_city_reports</li>
                        <li>❌ manage_tenants (superadmin only)</li>
                        <li>❌ manage_all_users (superadmin only)</li>
                        <li>❌ manage_system_settings (superadmin only)</li>
                    </ul>
                </div>
            `;
        }
    </script>
</body>
</html>
