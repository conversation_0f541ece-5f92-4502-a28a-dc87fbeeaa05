"""
Group-based permission classes that replace hardcoded role-based permissions.
These permissions use the group system instead of checking user.role directly.
"""
from rest_framework import permissions
from django.contrib.auth.models import Permission


class GroupBasedPermission(permissions.BasePermission):
    """
    Base permission class for group-based permissions.
    Checks if user has specific permissions through their groups.
    """
    required_permissions = []  # List of permission codenames required
    
    def has_permission(self, request, view):
        user = request.user
        
        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False
        
        # Superusers have all permissions
        if user.is_superuser:
            return True
        
        # If no specific permissions required, just check authentication
        if not self.required_permissions:
            return True
        
        # Check if user has any of the required permissions through groups
        for permission_codename in self.required_permissions:
            if user.has_group_permission(permission_codename):
                return True
        
        return False


class CanManageCitizensGroup(GroupBasedPermission):
    """
    Group-based permission for managing citizens using general workflow permissions.
    """

    def has_permission(self, request, view):
        user = request.user

        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False

        # Superusers and full system access have all permissions
        if user.is_superuser or user.has_group_permission('full_system_access'):
            return True

        # For safe methods (GET, HEAD, OPTIONS), check view permissions
        if request.method in permissions.SAFE_METHODS:
            return (user.has_group_permission('view_citizens_list') or
                   user.has_group_permission('view_citizen_details') or
                   user.has_group_permission('view_own_kebele_data') or
                   user.has_group_permission('view_child_kebeles_data') or
                   user.has_group_permission('view_child_subcities_data') or
                   user.has_group_permission('view_all_tenants_data'))

        # For unsafe methods, check specific workflow permissions
        if view.action == 'create':
            return user.has_group_permission('register_citizens')

        elif view.action in ['update', 'partial_update']:
            return user.has_group_permission('register_citizens')

        elif view.action == 'destroy':
            # Only super admin can delete citizens
            return user.has_group_permission('full_system_access')

        elif view.action in ['update_status', 'approval_action']:
            return (user.has_group_permission('approve_id_cards') or
                   user.has_group_permission('verify_documents'))

        elif view.action == 'check_registration_fraud':
            # Fraud checking is part of citizen registration workflow
            return user.has_group_permission('register_citizens')

        # Default to checking citizen registration permission
        return user.has_group_permission('register_citizens')


class CanManageUsersGroup(GroupBasedPermission):
    """
    Group-based permission for managing users using general workflow permissions.
    """

    def has_permission(self, request, view):
        user = request.user

        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False

        # Superusers and full system access have all permissions
        if user.is_superuser or user.has_group_permission('full_system_access'):
            return True

        # For safe methods (GET, HEAD, OPTIONS), check view permissions
        if request.method in permissions.SAFE_METHODS:
            return (user.has_group_permission('create_kebele_users') or
                   user.has_group_permission('create_subcity_users') or
                   user.has_group_permission('manage_all_users'))

        # For unsafe methods, check hierarchical user creation permissions
        if view.action == 'create':
            return (user.has_group_permission('create_kebele_users') or
                   user.has_group_permission('create_subcity_users') or
                   user.has_group_permission('manage_all_users'))

        elif view.action in ['update', 'partial_update']:
            return (user.has_group_permission('create_kebele_users') or
                   user.has_group_permission('create_subcity_users') or
                   user.has_group_permission('manage_all_users'))

        elif view.action == 'destroy':
            return (user.has_group_permission('manage_all_users'))

        # Default to checking user management permissions
        return (user.has_group_permission('create_kebele_users') or
               user.has_group_permission('create_subcity_users') or
               user.has_group_permission('manage_all_users'))


class CanManageIDCardsGroup(GroupBasedPermission):
    """
    Group-based permission for managing ID cards using general workflow permissions.
    """

    def has_permission(self, request, view):
        user = request.user

        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False

        # Superusers and full system access have all permissions
        if user.is_superuser or user.has_group_permission('full_system_access'):
            return True

        # For safe methods, check view permissions
        # Allow users who can generate, approve, or view ID cards
        if request.method in permissions.SAFE_METHODS:
            return (user.has_group_permission('view_id_cards_list') or
                   user.has_group_permission('generate_id_cards') or
                   user.has_group_permission('approve_id_cards'))

        # For unsafe methods, check specific workflow permissions
        if view.action == 'create':
            return user.has_group_permission('generate_id_cards')

        elif view.action in ['update', 'partial_update']:
            return (user.has_group_permission('generate_id_cards') or
                   user.has_group_permission('approve_id_cards'))

        elif view.action == 'destroy':
            # Only super admin can delete ID cards
            return user.has_group_permission('full_system_access')

        elif view.action in ['approve', 'print']:
            return user.has_group_permission('approve_id_cards')

        elif view.action == 'send_for_approval':
            return user.has_group_permission('send_id_cards_for_approval')

        elif view.action == 'send_to_higher_level':
            return user.has_group_permission('send_id_cards_to_higher_level')

        # Default to checking ID card workflow permissions
        return (user.has_group_permission('generate_id_cards') or
               user.has_group_permission('view_id_cards_list'))


class CanManageGroupsPermission(GroupBasedPermission):
    """
    Group-based permission for managing groups and permissions using general workflow permissions.
    """

    def has_permission(self, request, view):
        user = request.user

        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False

        # Superusers and full system access have all permissions
        if user.is_superuser or user.has_group_permission('full_system_access'):
            return True

        # Group management is part of user creation workflow
        return (user.has_group_permission('create_kebele_users') or
               user.has_group_permission('create_subcity_users') or
               user.has_group_permission('manage_all_users'))


class IsClerkOrAdminGroup(GroupBasedPermission):
    """
    Group-based replacement for IsClerkOrAdmin using general workflow permissions.
    Checks if user has basic operational permissions.
    """

    def has_permission(self, request, view):
        user = request.user

        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False

        # Superusers and full system access have all permissions
        if user.is_superuser or user.has_group_permission('full_system_access'):
            return True

        # Check if user has any basic operational permissions
        basic_permissions = [
            'register_citizens', 'view_citizens_list', 'view_citizen_details',
            'generate_id_cards', 'view_id_cards_list', 'approve_id_cards',
            'view_kebele_dashboard', 'view_subcity_dashboard', 'view_city_dashboard'
        ]

        for permission in basic_permissions:
            if user.has_group_permission(permission):
                return True

        return False


class IsKebeleAdminOrHigherGroup(GroupBasedPermission):
    """
    Group-based replacement for IsKebeleAdminOrHigher using general workflow permissions.
    Checks if user has administrative permissions.
    """

    def has_permission(self, request, view):
        user = request.user

        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False

        # Superusers and full system access have all permissions
        if user.is_superuser or user.has_group_permission('full_system_access'):
            return True

        # Check if user has administrative permissions
        admin_permissions = [
            'approve_id_cards', 'verify_documents', 'create_kebele_users',
            'create_subcity_users', 'manage_all_users', 'view_kebele_reports',
            'view_subcity_reports', 'view_city_reports', 'view_all_reports'
        ]

        for permission in admin_permissions:
            if user.has_group_permission(permission):
                return True

        return False
