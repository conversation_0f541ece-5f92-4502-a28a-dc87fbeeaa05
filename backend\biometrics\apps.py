from django.apps import AppConfig


class BiometricsConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'biometrics'
    verbose_name = 'Biometrics'

    def ready(self):
        """Initialize biometric device connections when app starts"""
        try:
            # Biometric device is now handled by external Java bridge service
            # No need to initialize device directly in Django
            import logging
            logger = logging.getLogger(__name__)
            logger.info("Biometric device integration via external Java bridge service (port 8002)")
        except Exception as e:
            # Log error but don't crash the app
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Biometric device info: {e}")
