#!/usr/bin/env python
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append('/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from django_tenants.utils import schema_context, get_public_schema_name
from users.models_groups import TenantGroup, GroupMembership
import json

User = get_user_model()

def test_group_users():
    print('=== Testing Group Users ===')
    
    # Test in public schema first to see groups
    with schema_context(get_public_schema_name()):
        print('\n--- Groups in Public Schema ---')
        groups = TenantGroup.objects.filter(is_active=True)
        for group in groups:
            print(f'Group: {group.name} (ID: {group.id}) - Tenant: {group.tenant.name if group.tenant else "None"}')
            
            # Get memberships for this group
            memberships = GroupMembership.objects.filter(group=group, is_active=True)
            print(f'  Memberships: {memberships.count()}')
            
            for membership in memberships:
                print(f'    User: {membership.user_email} (Primary: {membership.is_primary})')
                
                # Try to get the actual user
                try:
                    user = membership.user  # This uses our custom property
                    if user:
                        print(f'      User found: {user.email} in schema {user._state.db}')
                    else:
                        print(f'      User not found for email: {membership.user_email}')
                except Exception as e:
                    print(f'      Error getting user: {e}')
    
    # Test specific kebele
    print('\n--- Testing Kebele15 Users ---')
    with schema_context('kebele_kebele15'):
        users = User.objects.filter(is_active=True)
        print(f'Users in kebele15: {users.count()}')
        
        for user in users:
            print(f'User: {user.email}')
            
            # Check their group memberships
            with schema_context(get_public_schema_name()):
                memberships = GroupMembership.objects.filter(user_email=user.email, is_active=True)
                print(f'  Group memberships: {memberships.count()}')
                
                for membership in memberships:
                    print(f'    Group: {membership.group.name} (ID: {membership.group.id}) - Primary: {membership.is_primary}')
    
    # Test the groups_with_users endpoint logic
    print('\n--- Testing Groups with Users Logic ---')
    
    # Simulate the API endpoint logic
    tenant_id = 15  # kebele15 ID
    
    with schema_context(get_public_schema_name()):
        # Get groups for this tenant
        groups = TenantGroup.objects.filter(
            tenant_id=tenant_id,
            is_active=True
        ).order_by('level', 'group__name')
        
        groups_data = []
        for group in groups:
            # Get users for this group
            memberships = GroupMembership.objects.filter(group=group, is_active=True)
            users_data = []
            
            for membership in memberships:
                try:
                    user = membership.user
                    if user and user.is_active:
                        users_data.append({
                            'id': user.id,
                            'email': user.email,
                            'first_name': user.first_name,
                            'last_name': user.last_name,
                            'username': user.username,
                            'is_primary': membership.is_primary
                        })
                except Exception as e:
                    print(f'Error getting user for membership {membership.user_email}: {e}')
            
            group_data = {
                'id': group.id,
                'name': group.group.name,
                'description': group.description,
                'group_type': group.group_type,
                'level': group.level,
                'users_count': len(users_data),
                'users': users_data
            }
            groups_data.append(group_data)
            
            print(f'Group: {group.group.name} - Users: {len(users_data)}')
            for user_data in users_data:
                print(f'  - {user_data["email"]} (Primary: {user_data["is_primary"]})')
    
    print('\n--- Summary ---')
    print(f'Total groups for tenant {tenant_id}: {len(groups_data)}')
    total_users = sum(group['users_count'] for group in groups_data)
    print(f'Total user assignments: {total_users}')

if __name__ == '__main__':
    test_group_users()
