from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    CountryViewSet,
    RegionViewSet,
    ReligionViewSet,
    CitizenStatusViewSet,
    MaritalStatusViewSet,
    DocumentTypeViewSet,
    EmploymentTypeViewSet,
    RelationshipViewSet,
    CurrentStatusViewSet,
    BiometricTypeViewSet,
    KetenaViewSet
)

router = DefaultRouter()
router.register(r'countries', CountryViewSet)
router.register(r'regions', RegionViewSet)
router.register(r'religions', ReligionViewSet)
router.register(r'citizen-statuses', CitizenStatusViewSet)
router.register(r'marital-statuses', MaritalStatusViewSet)
router.register(r'document-types', DocumentTypeViewSet)
router.register(r'employment-types', EmploymentTypeViewSet)
router.register(r'relationships', RelationshipViewSet)
router.register(r'current-statuses', CurrentStatusViewSet)
router.register(r'biometric-types', BiometricTypeViewSet)
router.register(r'ketenas', KetenaViewSet)

urlpatterns = [
    path('', include(router.urls)),
]
