import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Divider,
  Paper
} from '@mui/material';
import {
  Refresh as RenewIcon,
  Build as RepairIcon,
  Print as ReprintIcon,
  CloudUpload as UploadIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Schedule as PendingIcon
} from '@mui/icons-material';
import axios from '../../utils/axios';
import { useSharedData } from '../../contexts/SharedDataContext';
import { useTheme as useAppTheme } from '../../contexts/ThemeContext';
import FraudDetectionAlert from '../../components/FraudDetectionAlert';

const IDCardServices = () => {
  const { sharedData } = useSharedData();
  const { tenantColors } = useAppTheme();
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedService, setSelectedService] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [kebeles, setKebeles] = useState([]);
  const [loadingKebeles, setLoadingKebeles] = useState(false);
  const [tenantAccess, setTenantAccess] = useState(null);
  const [checkingAccess, setCheckingAccess] = useState(true);
  const [formData, setFormData] = useState({
    digital_id: '',
    reason: '',
    application_method: 'online',
    kebele_id: '',
    damage_description: '',
    police_report_number: '',
    loss_date: '',
    loss_location: ''
  });
  const [renewalEligibility, setRenewalEligibility] = useState(null);
  const [alert, setAlert] = useState({ show: false, type: 'info', message: '' });
  const [fraudResults, setFraudResults] = useState(null);

  // Fetch kebeles data and check tenant access when component mounts
  useEffect(() => {
    fetchKebeles();
    checkTenantAccess();
  }, []);

  const checkTenantAccess = async () => {
    try {
      setCheckingAccess(true);
      const urlParams = new URLSearchParams(window.location.search);
      const tenantId = urlParams.get('tenant');

      if (tenantId) {
        // Check specific tenant access
        const response = await axios.get(`/api/tenants/${tenantId}/`);
        const tenant = response.data;

        if (!tenant.public_id_application_enabled) {
          setTenantAccess({
            allowed: false,
            message: `${tenant.name} has not enabled public ID card application services.`,
            tenant: tenant
          });
          return;
        }

        setTenantAccess({
          allowed: true,
          tenant: tenant
        });
      } else {
        // Check if any tenant has public access enabled
        const response = await axios.get('/api/tenants/');
        const tenants = response.data.results || response.data;
        const publicTenants = tenants.filter(t => t.public_id_application_enabled);

        if (publicTenants.length === 0) {
          setTenantAccess({
            allowed: false,
            message: 'No administrative areas have enabled public ID card application services.',
            tenants: tenants
          });
        } else {
          setTenantAccess({
            allowed: true,
            tenants: publicTenants
          });
        }
      }
    } catch (error) {
      console.error('Error checking tenant access:', error);
      setTenantAccess({
        allowed: false,
        message: 'Unable to verify service availability. Please try again later.'
      });
    } finally {
      setCheckingAccess(false);
    }
  };

  const fetchKebeles = async () => {
    try {
      setLoadingKebeles(true);
      const response = await axios.get('/api/public/kebeles/');
      setKebeles(response.data.kebeles || []);
    } catch (error) {
      console.error('Error fetching kebeles:', error);
      setKebeles([]);
    } finally {
      setLoadingKebeles(false);
    }
  };

  // Generate theme-based colors for service cards
  const getServiceCardColors = () => {
    const primary = tenantAccess?.tenant?.primary_color || tenantColors.primary;
    const secondary = tenantAccess?.tenant?.secondary_color || tenantColors.secondary;

    // Convert hex to RGB for shadow colors
    const hexToRgb = (hex) => {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
      return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
      } : null;
    };

    const primaryRgb = hexToRgb(primary);
    const secondaryRgb = hexToRgb(secondary);

    // Create variations of the theme colors for different services
    const darkenColor = (hex, amount = 20) => {
      const rgb = hexToRgb(hex);
      if (!rgb) return hex;

      const darken = (value) => Math.max(0, value - amount);
      const toHex = (value) => value.toString(16).padStart(2, '0');

      return `#${toHex(darken(rgb.r))}${toHex(darken(rgb.g))}${toHex(darken(rgb.b))}`;
    };

    return [
      {
        color: primary,
        gradient: `linear-gradient(135deg, ${primary} 0%, ${darkenColor(primary)} 100%)`,
        shadowColor: primaryRgb ? `rgba(${primaryRgb.r}, ${primaryRgb.g}, ${primaryRgb.b}, 0.3)` : 'rgba(255, 143, 0, 0.3)'
      },
      {
        color: secondary,
        gradient: `linear-gradient(135deg, ${secondary} 0%, ${darkenColor(secondary)} 100%)`,
        shadowColor: secondaryRgb ? `rgba(${secondaryRgb.r}, ${secondaryRgb.g}, ${secondaryRgb.b}, 0.3)` : 'rgba(239, 108, 0, 0.3)'
      },
      {
        color: primary,
        gradient: `linear-gradient(135deg, ${darkenColor(primary, 10)} 0%, ${darkenColor(primary, 30)} 100%)`,
        shadowColor: primaryRgb ? `rgba(${primaryRgb.r}, ${primaryRgb.g}, ${primaryRgb.b}, 0.25)` : 'rgba(255, 143, 0, 0.25)'
      }
    ];
  };

  const serviceCardColors = getServiceCardColors();

  const serviceTypes = [
    {
      id: 'renewal',
      title: 'ID Card Renewal',
      description: 'Renew your expired or expiring ID card with ease',
      icon: <RenewIcon sx={{ fontSize: 48 }} />,
      ...serviceCardColors[0]
    },
    {
      id: 'replacement',
      title: 'ID Card Replacement',
      description: 'Replace your damaged or worn-out ID card',
      icon: <RepairIcon sx={{ fontSize: 48 }} />,
      ...serviceCardColors[1]
    },
    {
      id: 'reprint',
      title: 'ID Card Reprint',
      description: 'Reprint your lost or stolen ID card',
      icon: <ReprintIcon sx={{ fontSize: 48 }} />,
      ...serviceCardColors[2]
    }
  ];

  const showAlert = (type, message) => {
    setAlert({ show: true, type, message });
    setTimeout(() => setAlert({ show: false, type: 'info', message: '' }), 5000);
  };

  const handleServiceClick = async (serviceType) => {
    setSelectedService(serviceType);
    setDialogOpen(true);
    
    // If renewal service, check eligibility when digital ID is entered
    if (serviceType === 'renewal' && formData.digital_id) {
      await checkRenewalEligibility(formData.digital_id);
    }
  };

  const checkRenewalEligibility = async (digitalId) => {
    try {
      const response = await axios.get(`/api/idcards/services/renewal/check/${digitalId}/`);
      setRenewalEligibility(response.data);
    } catch (error) {
      console.error('Error checking renewal eligibility:', error);
      setRenewalEligibility(null);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Check renewal eligibility when digital ID changes
    if (field === 'digital_id' && selectedService === 'renewal' && value.length >= 10) {
      checkRenewalEligibility(value);
    }

    // Run fraud detection when enough data is available
    if (field === 'digital_id' && value.length >= 10) {
      checkFraudIndicators(value);
    }
  };

  const checkFraudIndicators = async (digitalId) => {
    try {
      // Get citizen data first
      const citizenResponse = await axios.get(`/api/citizens/?digital_id=${digitalId}`);
      if (citizenResponse.data.results && citizenResponse.data.results.length > 0) {
        const citizen = citizenResponse.data.results[0];

        // Run fraud detection
        const fraudResponse = await axios.post('/api/idcards/services/fraud/check/', {
          digital_id: citizen.digital_id,
          first_name: citizen.first_name,
          last_name: citizen.last_name,
          date_of_birth: citizen.date_of_birth,
          phone: citizen.phone,
          email: citizen.email
        });

        if (fraudResponse.data.success) {
          const fraudData = fraudResponse.data.fraud_detection;
          if (fraudData.risk_level !== 'low') {
            setFraudResults(fraudData);
          } else {
            setFraudResults(null);
          }
        }
      }
    } catch (error) {
      console.error('Error checking fraud indicators:', error);
      // Don't show error to user for fraud detection failures
    }
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      
      let endpoint = '';
      let payload = { ...formData };
      
      switch (selectedService) {
        case 'renewal':
          endpoint = '/api/idcards/services/renewal/apply/';
          break;
        case 'replacement':
          endpoint = '/api/idcards/services/replacement/apply/';
          break;
        case 'reprint':
          endpoint = '/api/idcards/services/reprint/apply/';
          break;
        default:
          throw new Error('Invalid service type');
      }
      
      const response = await axios.post(endpoint, payload);

      if (response.data.success) {
        let message = response.data.message;

        // Check if there are fraud warnings
        if (response.data.fraud_warning) {
          const warning = response.data.fraud_warning;
          message += ` (⚠️ ${warning.risk_level.toUpperCase()} fraud risk detected - manual review required)`;
        }

        showAlert('success', message);
        setDialogOpen(false);
        setFormData({
          digital_id: '',
          reason: '',
          application_method: 'online',
          damage_description: '',
          police_report_number: '',
          loss_date: '',
          loss_location: ''
        });
        setRenewalEligibility(null);
        setFraudResults(null);
      } else {
        // Check if it's a fraud detection block
        if (response.data.fraud_detected) {
          showAlert('error', `Application blocked: ${response.data.error}`);
          // Keep the fraud results visible for review
        } else {
          showAlert('error', response.data.error || 'Failed to submit request');
        }
      }
    } catch (error) {
      console.error('Error submitting service request:', error);
      showAlert('error', error.response?.data?.error || 'Failed to submit request');
    } finally {
      setLoading(false);
    }
  };

  const renderServiceCard = (service) => (
    <Grid item xs={12} md={4} key={service.id}>
      <Card
        sx={{
          height: '100%',
          background: service.gradient,
          color: 'white',
          cursor: 'pointer',
          borderRadius: 4,
          position: 'relative',
          overflow: 'hidden',
          transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
          boxShadow: `0 8px 32px ${service.shadowColor}`,
          '&:hover': {
            transform: 'translateY(-12px) scale(1.03)',
            boxShadow: `0 24px 48px ${service.shadowColor}`,
          },
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%)',
            pointerEvents: 'none',
          }
        }}
        onClick={() => handleServiceClick(service.id)}
      >
        <CardContent sx={{ textAlign: 'center', p: 4, position: 'relative', zIndex: 1 }}>
          <Box
            sx={{
              mb: 3,
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              width: 100,
              height: 100,
              borderRadius: '50%',
              backgroundColor: 'rgba(255,255,255,0.2)',
              margin: '0 auto',
              backdropFilter: 'blur(15px)',
              border: '2px solid rgba(255,255,255,0.3)',
              boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
              transition: 'all 0.3s ease',
              '&:hover': {
                transform: 'scale(1.1) rotate(5deg)',
                backgroundColor: 'rgba(255,255,255,0.25)',
              }
            }}
          >
            {service.icon}
          </Box>
          <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold', mb: 2, textShadow: '0 2px 4px rgba(0,0,0,0.2)' }}>
            {service.title}
          </Typography>
          <Typography variant="body1" sx={{ opacity: 0.95, mb: 4, lineHeight: 1.7, fontSize: '1.1rem' }}>
            {service.description}
          </Typography>
          <Button
            variant="contained"
            size="large"
            sx={{
              mt: 2,
              backgroundColor: 'rgba(255,255,255,0.25)',
              backdropFilter: 'blur(15px)',
              border: '2px solid rgba(255,255,255,0.4)',
              borderRadius: 3,
              px: 5,
              py: 2,
              fontWeight: 'bold',
              fontSize: '1.1rem',
              textTransform: 'none',
              boxShadow: '0 4px 16px rgba(0,0,0,0.1)',
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.35)',
                transform: 'translateY(-3px)',
                boxShadow: '0 8px 24px rgba(0,0,0,0.2)',
                border: '2px solid rgba(255,255,255,0.6)',
              }
            }}
          >
            Apply Now
          </Button>
        </CardContent>
      </Card>
    </Grid>
  );

  const renderRenewalEligibility = () => {
    if (!renewalEligibility) return null;

    const { eligible, is_expired, is_expiring_soon, days_to_expiry, expiry_date } = renewalEligibility;

    return (
      <Paper sx={{ p: 2, mb: 2, backgroundColor: eligible ? '#e8f5e8' : '#fff3e0' }}>
        <Box display="flex" alignItems="center" mb={1}>
          {eligible ? (
            <CheckIcon sx={{ color: 'success.main', mr: 1 }} />
          ) : (
            <ErrorIcon sx={{ color: 'warning.main', mr: 1 }} />
          )}
          <Typography variant="subtitle1" fontWeight="bold">
            Renewal Eligibility Status
          </Typography>
        </Box>
        
        {eligible ? (
          <Box>
            <Typography variant="body2" color="success.main">
              ✅ This ID card is eligible for renewal
            </Typography>
            {is_expired && (
              <Typography variant="body2">
                • ID card expired on {expiry_date}
              </Typography>
            )}
            {is_expiring_soon && !is_expired && (
              <Typography variant="body2">
                • ID card expires in {days_to_expiry} days ({expiry_date})
              </Typography>
            )}
          </Box>
        ) : (
          <Typography variant="body2" color="warning.main">
            ❌ This ID card is not eligible for renewal yet
          </Typography>
        )}
      </Paper>
    );
  };

  // Show loading while checking access
  if (checkingAccess) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '70vh' }}>
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ ml: 2 }}>
          Checking service availability...
        </Typography>
      </Box>
    );
  }

  // Show access denied message
  if (tenantAccess && !tenantAccess.allowed) {
    return (
      <Box sx={{ minHeight: '70vh', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Paper sx={{ p: 4, textAlign: 'center', maxWidth: 600 }}>
          <ErrorIcon sx={{ fontSize: 80, color: 'error.main', mb: 2 }} />
          <Typography variant="h4" gutterBottom>
            Service Not Available
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
            {tenantAccess.message}
          </Typography>

          {tenantAccess.tenant && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                {tenantAccess.tenant.name}
              </Typography>
              <Chip
                label={`${tenantAccess.tenant.type.toUpperCase()} - Public Access Disabled`}
                color="error"
                sx={{ mb: 2 }}
              />
            </Box>
          )}

          <Button
            variant="contained"
            onClick={() => window.location.href = '/'}
            sx={{ mt: 2 }}
          >
            Return to Home
          </Button>
        </Paper>
      </Box>
    );
  }

  return (
    <Box sx={{ minHeight: '70vh' }}>
      {/* Tenant Info Banner */}
      {tenantAccess?.tenant && (
        <Paper
          sx={{
            p: 3,
            mb: 4,
            background: `linear-gradient(135deg, ${tenantAccess.tenant.primary_color || tenantColors.primary} 0%, ${tenantAccess.tenant.secondary_color || tenantColors.secondary} 100%)`,
            color: 'white',
            borderRadius: 3
          }}
        >
          <Typography variant="h5" fontWeight="bold" gutterBottom>
            {tenantAccess.tenant.name} - ID Card Services
          </Typography>
          <Typography variant="body1">
            You are applying for ID card services through {tenantAccess.tenant.name}.
            All applications will be processed by their administration.
          </Typography>
        </Paper>
      )}

      {alert.show && (
        <Alert
          severity={alert.type}
          sx={{
            mb: 4,
            borderRadius: 3,
            boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
            border: '1px solid rgba(255,255,255,0.1)'
          }}
        >
          {alert.message}
        </Alert>
      )}

      {/* Enhanced intro section */}
      <Box sx={{ mb: 6, textAlign: 'center' }}>
        <Typography variant="h5" sx={{
          fontWeight: 600,
          mb: 3,
          color: 'primary.main'
        }}>
          Choose Your Service
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{
          fontSize: '1.2rem',
          lineHeight: 1.7,
          maxWidth: 700,
          mx: 'auto',
          fontWeight: 400
        }}>
          Apply for ID card services online or visit your kebele office. All applications will be reviewed by your kebele leader for approval.
        </Typography>
      </Box>

      <Grid container spacing={5} sx={{ mb: 6 }}>
        {serviceTypes.map(renderServiceCard)}
      </Grid>

      {/* Information Section */}
      <Box sx={{
        mt: 8,
        p: 4,
        backgroundColor: 'background.paper',
        borderRadius: 3,
        boxShadow: '0 4px 16px rgba(0,0,0,0.1)',
        border: '1px solid rgba(0,0,0,0.05)'
      }}>
        <Typography variant="h6" sx={{ mb: 3, fontWeight: 600, color: 'primary.main' }}>
          Important Information
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
              Required Documents:
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              • Current ID card (for renewal/replacement)<br/>
              • Police report (for reprint of lost/stolen cards)<br/>
              • Recent passport-size photograph<br/>
              • Proof of residence
            </Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
              Processing Time:
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              • Online applications: 3-5 business days<br/>
              • Walk-in applications: 1-2 business days<br/>
              • Emergency cases: Same day (with additional fee)
            </Typography>
          </Grid>
        </Grid>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 2, fontStyle: 'italic' }}>
          Note: All applications require approval from your kebele leader. You will be notified via SMS/email once your application is processed.
        </Typography>
      </Box>

      {/* Service Application Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          Apply for {serviceTypes.find(s => s.id === selectedService)?.title}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <TextField
              fullWidth
              label="Digital ID Number"
              value={formData.digital_id}
              onChange={(e) => handleInputChange('digital_id', e.target.value)}
              sx={{ mb: 2 }}
              required
            />

            {/* Fraud Detection Alert */}
            {fraudResults && (
              <FraudDetectionAlert
                fraudResults={fraudResults}
                onClose={() => setFraudResults(null)}
              />
            )}

            {selectedService === 'renewal' && renderRenewalEligibility()}

            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Application Method</InputLabel>
              <Select
                value={formData.application_method}
                onChange={(e) => handleInputChange('application_method', e.target.value)}
                label="Application Method"
              >
                <MenuItem value="online">Online Application</MenuItem>
                <MenuItem value="physical">Physical Visit</MenuItem>
              </Select>
            </FormControl>

            {formData.application_method === 'online' && (
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Select Kebele</InputLabel>
                <Select
                  value={formData.kebele_id}
                  onChange={(e) => handleInputChange('kebele_id', e.target.value)}
                  label="Select Kebele"
                  required
                  disabled={loadingKebeles}
                >
                  {loadingKebeles ? (
                    <MenuItem disabled>Loading kebeles...</MenuItem>
                  ) : (
                    kebeles.map((kebele) => (
                      <MenuItem key={kebele.id} value={kebele.id}>
                        {kebele.name}
                      </MenuItem>
                    ))
                  )}
                </Select>
              </FormControl>
            )}

            <TextField
              fullWidth
              label="Reason for Request"
              multiline
              rows={3}
              value={formData.reason}
              onChange={(e) => handleInputChange('reason', e.target.value)}
              sx={{ mb: 2 }}
              required
            />

            {selectedService === 'replacement' && (
              <TextField
                fullWidth
                label="Damage Description"
                multiline
                rows={2}
                value={formData.damage_description}
                onChange={(e) => handleInputChange('damage_description', e.target.value)}
                sx={{ mb: 2 }}
                placeholder="Describe the damage to your ID card"
              />
            )}

            {selectedService === 'reprint' && (
              <>
                <TextField
                  fullWidth
                  label="Police Report Number"
                  value={formData.police_report_number}
                  onChange={(e) => handleInputChange('police_report_number', e.target.value)}
                  sx={{ mb: 2 }}
                  required
                />
                <TextField
                  fullWidth
                  label="Date of Loss"
                  type="date"
                  value={formData.loss_date}
                  onChange={(e) => handleInputChange('loss_date', e.target.value)}
                  sx={{ mb: 2 }}
                  InputLabelProps={{ shrink: true }}
                  required
                />
                <TextField
                  fullWidth
                  label="Location of Loss"
                  value={formData.loss_location}
                  onChange={(e) => handleInputChange('loss_location', e.target.value)}
                  sx={{ mb: 2 }}
                  placeholder="Where did you lose your ID card?"
                />
              </>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={loading || !formData.digital_id || !formData.reason}
          >
            {loading ? <CircularProgress size={20} /> : 'Submit Application'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default IDCardServices;
