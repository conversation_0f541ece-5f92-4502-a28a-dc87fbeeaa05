from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from django.db import transaction, connection
from django.contrib.auth import get_user_model
from django_tenants.utils import schema_context
from django_tenants.management.commands.migrate_schemas import Command as MigrateCommand
from django.core.management import call_command
from django.shortcuts import get_object_or_404
from ..models.tenant import Tenant, Domain, TenantType
from ..models.city import CityAdministration
from ..models.subcity import SubCity
from ..models.kebele import Kebele
from ..serializers import TenantSerializer, DomainSerializer, TenantTreeSerializer
from ..permissions import IsSuperUser, CanManageTenants

User = get_user_model()


class TenantViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing tenants.

    Permissions:
    - City admins can create and manage subcity tenants
    - Subcity admins can create and manage kebele tenants
    - Super admins can manage all tenants
    """
    queryset = Tenant.objects.all()
    serializer_class = TenantSerializer
    permission_classes = [permissions.AllowAny]  # Allow public access for reading tenant info
    filterset_fields = ['type', 'parent']

    def get_permissions(self):
        """
        Override permissions based on action.
        - list/retrieve: Allow public access
        - create/update/delete: Require authentication and permissions
        """
        if self.action in ['list', 'retrieve']:
            return [permissions.AllowAny()]
        else:
            return [permissions.IsAuthenticated(), CanManageTenants()]

    def get_queryset(self):
        """
        Filter tenants based on user's role and tenant.

        - City admins can see their city and child subcities/kebeles
        - Subcity admins can see their subcity and child kebeles
        - Super admins can see all tenants
        """
        queryset = super().get_queryset()
        user = self.request.user

        # Apply query params filters
        tenant_type = self.request.query_params.get('type')
        parent_id = self.request.query_params.get('parent')

        if tenant_type:
            queryset = queryset.filter(type=tenant_type)

        if parent_id:
            if parent_id == 'null':
                queryset = queryset.filter(parent__isnull=True)
            else:
                queryset = queryset.filter(parent_id=parent_id)

        # For public access (unauthenticated users), return all tenants
        if not user.is_authenticated:
            print(f"🔍 Public access - returning all {queryset.count()} tenants")
            return queryset

        # Filter based on user's role and tenant for authenticated users
        print(f"🔍 TenantViewSet Debug: user={user.email}, is_superuser={user.is_superuser}, role={getattr(user, 'role', None)}, tenant={getattr(user, 'tenant', None)}")
        print(f"🔍 Query params: type={tenant_type}, parent={parent_id}")

        if user.is_superuser or getattr(user, 'role', None) == 'superadmin':
            print(f"✅ Superuser access granted - returning {queryset.count()} tenants (after filtering)")
            return queryset
        elif user.tenant:
            if user.role == 'city_admin':
                # City admin can see their city and child subcities/kebeles
                city = user.tenant
                subcities = city.children.all()
                subcity_ids = [subcity.id for subcity in subcities]
                kebeles = []
                for subcity in subcities:
                    kebeles.extend(subcity.children.all())
                kebele_ids = [kebele.id for kebele in kebeles]
                return queryset.filter(id__in=[city.id] + subcity_ids + kebele_ids)
            elif user.role == 'subcity_admin':
                # Subcity admin can see their subcity and child kebeles
                subcity = user.tenant
                kebele_ids = [kebele.id for kebele in subcity.children.all()]
                return queryset.filter(id__in=[subcity.id] + kebele_ids)
            elif user.role == 'kebele_admin':
                # Kebele admin can only see their kebele
                return queryset.filter(id=user.tenant.id)
            else:
                # Clerks can't see any tenants
                print(f"❌ Clerk role - returning no tenants")
                return queryset.none()
        else:
            print(f"❌ User has no tenant assigned - returning no tenants")
            return queryset.none()

        print(f"❌ Fallback - returning all tenants")
        return queryset

    @transaction.atomic
    def create(self, request, *args, **kwargs):
        data = request.data.copy()

        # Generate schema name based on tenant type and name
        tenant_type = data.get('type')
        tenant_name = data.get('name', '').lower().replace(' ', '_')
        schema_name = f"{tenant_type}_{tenant_name}"

        # Ensure schema name is unique
        count = Tenant.objects.filter(schema_name__startswith=schema_name).count()
        if count > 0:
            schema_name = f"{schema_name}_{count}"

        data['schema_name'] = schema_name

        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        tenant = serializer.save()

        # Create domain for the tenant
        domain_name = f"{schema_name}.goid.local"
        Domain.objects.create(domain=domain_name, tenant=tenant, is_primary=True)

        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    @transaction.atomic
    def destroy(self, request, *args, **kwargs):
        """
        Delete a tenant and its associated schema.
        """
        tenant = self.get_object()

        # Check if tenant has children
        if tenant.children.exists():
            return Response(
                {"detail": "Cannot delete tenant with child tenants. Delete child tenants first."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Store schema name and info before deletion
        schema_name = tenant.schema_name
        tenant_id = tenant.id
        tenant_name = tenant.name

        try:
            # Step 1: Clean up foreign key references BEFORE deleting anything
            print(f"Cleaning up foreign key references for tenant {tenant_id}")

            from django.contrib.auth import get_user_model
            User = get_user_model()

            # Get all users that belong to this tenant
            tenant_users = User.objects.filter(tenant=tenant)
            tenant_user_ids = list(tenant_users.values_list('id', flat=True))

            if tenant_user_ids:
                with connection.cursor() as cursor:
                    # Clean up workflow logs
                    cursor.execute(
                        "UPDATE workflows_workflowlog SET performed_by_id = NULL WHERE performed_by_id = ANY(%s)",
                        [tenant_user_ids]
                    )

                    # Clean up admin logs
                    cursor.execute(
                        "UPDATE django_admin_log SET user_id = NULL WHERE user_id = ANY(%s)",
                        [tenant_user_ids]
                    )

                    # Clean up citizens created_by references
                    cursor.execute(
                        "UPDATE citizens_citizen SET created_by_id = NULL WHERE created_by_id = ANY(%s)",
                        [tenant_user_ids]
                    )

                    # Clean up ID card references
                    cursor.execute(
                        "UPDATE idcards_idcard SET approved_by_id = NULL WHERE approved_by_id = ANY(%s)",
                        [tenant_user_ids]
                    )
                    cursor.execute(
                        "UPDATE idcards_idcard SET issued_by_id = NULL WHERE issued_by_id = ANY(%s)",
                        [tenant_user_ids]
                    )
                    cursor.execute(
                        "UPDATE idcards_idcard SET created_by_id = NULL WHERE created_by_id = ANY(%s)",
                        [tenant_user_ids]
                    )

            # Step 2: Delete users that belong to this tenant (now safe to delete)
            tenant_users.delete()

            # Step 3: Delete the schema from database
            with connection.cursor() as cursor:
                print(f"Dropping schema: {schema_name}")
                cursor.execute(f'DROP SCHEMA IF EXISTS "{schema_name}" CASCADE;')

            # Step 4: Delete related domain records
            Domain.objects.filter(tenant=tenant).delete()

            # Step 5: Delete the tenant record
            tenant.delete()

            return Response(
                {"detail": f"Tenant '{tenant_name}' and its schema '{schema_name}' have been successfully deleted."},
                status=status.HTTP_204_NO_CONTENT
            )

        except Exception as e:
            print(f"Error deleting tenant {tenant_name}: {str(e)}")
            import traceback
            traceback.print_exc()

            return Response(
                {"detail": f"Error deleting tenant: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @transaction.atomic
    def update(self, request, *args, **kwargs):
        """
        Update a tenant with validation.
        """
        partial = kwargs.pop('partial', False)
        instance = self.get_object()

        # Don't allow changing schema_name or type after creation
        if 'schema_name' in request.data:
            return Response(
                {"detail": "Schema name cannot be changed after tenant creation."},
                status=status.HTTP_400_BAD_REQUEST
            )

        if 'type' in request.data and request.data['type'] != instance.type:
            return Response(
                {"detail": "Tenant type cannot be changed after creation."},
                status=status.HTTP_400_BAD_REQUEST
            )

        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        return Response(serializer.data)

    @action(detail=True, methods=['get'], permission_classes=[permissions.IsAuthenticated])
    def users(self, request, pk=None):
        """
        Get all users for a specific tenant.
        """
        tenant = self.get_object()

        try:
            from django_tenants.utils import schema_context
            from django.contrib.auth import get_user_model
            User = get_user_model()

            with schema_context(tenant.schema_name):
                users = User.objects.all()
                from users.serializers import UserSerializer
                serializer = UserSerializer(users, many=True)
                return Response(serializer.data)

        except Exception as e:
            return Response(
                {"detail": f"Error fetching users: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def create_user(self, request, pk=None):
        """
        Create a user for a specific tenant.
        """
        tenant = self.get_object()

        # Debug logging
        print(f"🔍 Create User Debug:")
        print(f"  User role: {request.user.role}")
        print(f"  User tenant: {request.user.tenant}")
        print(f"  Target tenant: {tenant.name} (type: {tenant.type})")
        print(f"  Target tenant parent: {tenant.parent}")

        # Check permissions based on tenant type and user role
        if request.user.is_superuser or request.user.role == 'superadmin':
            # Superadmin can create users for any tenant
            print(f"  ✅ Superadmin access granted")
        elif request.user.role == 'city_admin' and tenant.type == TenantType.SUBCITY:
            # City admin can create users in subcities under their city
            if tenant.parent != request.user.tenant:
                print(f"  ❌ Hierarchy check failed: subcity_parent={tenant.parent}, user_tenant={request.user.tenant}")
                return Response(
                    {"detail": "You can only create users for subcities under your city."},
                    status=status.HTTP_403_FORBIDDEN
                )
            print(f"  ✅ City admin access granted for subcity user creation")
        elif request.user.role == 'subcity_admin' and tenant.type == TenantType.KEBELE:
            # Subcity admin can create users in kebeles under their subcity
            if tenant.parent != request.user.tenant:
                print(f"  ❌ Hierarchy check failed: kebele_parent={tenant.parent}, user_tenant={request.user.tenant}")
                return Response(
                    {"detail": "You can only create users for kebeles under your subcity."},
                    status=status.HTTP_403_FORBIDDEN
                )
            print(f"  ✅ Subcity admin access granted for kebele user creation")
        else:
            print(f"  ❌ Permission denied: role={request.user.role}, tenant_type={tenant.type}")
            return Response(
                {"detail": f"You don't have permission to create users for this tenant. Your role: {request.user.role}, Tenant type: {tenant.type}"},
                status=status.HTTP_403_FORBIDDEN
            )

        print(f"  ✅ Permission checks passed")

        try:
            from django_tenants.utils import schema_context
            from django.contrib.auth import get_user_model
            from users.serializers import UserCreateSerializer
            User = get_user_model()

            print(f"  🔍 Creating user in schema: {tenant.schema_name}")

            # Create user in the target tenant's schema
            with schema_context(tenant.schema_name):
                # Add tenant to the user data
                user_data = request.data.copy()
                user_data['tenant'] = tenant.id

                print(f"  🔍 User data: {user_data}")

                # Check if user already exists in this tenant schema
                tenant_type_name = tenant.type.title()
                if User.objects.filter(email=user_data.get('email', '')).exists():
                    return Response(
                        {"detail": f"User with this email already exists in this {tenant_type_name.lower()}."},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                if User.objects.filter(username=user_data.get('username', '')).exists():
                    return Response(
                        {"detail": f"User with this username already exists in this {tenant_type_name.lower()}."},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                serializer = UserCreateSerializer(data=user_data)
                if serializer.is_valid():
                    user = serializer.save()
                    print(f"  ✅ User created successfully: {user.username} in schema {tenant.schema_name}")

                    # Automatically assign user to appropriate group based on role
                    self._assign_user_to_default_group(user, tenant)

                    # Return user data
                    from users.serializers import UserSerializer
                    response_serializer = UserSerializer(user)
                    return Response(response_serializer.data, status=status.HTTP_201_CREATED)
                else:
                    print(f"  ❌ Serializer errors: {serializer.errors}")
                    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            print(f"  ❌ Exception creating user: {str(e)}")
            import traceback
            traceback.print_exc()
            return Response(
                {"detail": f"Error creating user: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['patch'], permission_classes=[permissions.IsAuthenticated])
    def update_user(self, request, pk=None):
        """
        Update a user in a specific tenant.
        """
        tenant = self.get_object()
        user_id = request.data.get('user_id')

        if not user_id:
            return Response(
                {"detail": "user_id is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        print(f"🔍 TenantViewSet.update_user called")
        print(f"  Tenant: {tenant.name} (ID: {tenant.id}, Type: {tenant.type})")
        print(f"  User ID to update: {user_id}")
        print(f"  Request user: {request.user.email} (Role: {request.user.role})")

        # Permission checks
        if not (request.user.is_superuser or request.user.role == 'superadmin'):
            if tenant.type == 'kebele' and request.user.role not in ['subcity_admin', 'kebele_admin']:
                print(f"  ❌ Permission denied: role={request.user.role}, tenant_type={tenant.type}")
                return Response(
                    {"detail": f"You don't have permission to update users for this tenant. Your role: {request.user.role}, Tenant type: {tenant.type}"},
                    status=status.HTTP_403_FORBIDDEN
                )
            elif tenant.type == 'subcity' and request.user.role not in ['city_admin', 'subcity_admin']:
                print(f"  ❌ Permission denied: role={request.user.role}, tenant_type={tenant.type}")
                return Response(
                    {"detail": f"You don't have permission to update users for this tenant. Your role: {request.user.role}, Tenant type: {tenant.type}"},
                    status=status.HTTP_403_FORBIDDEN
                )

        print(f"  ✅ Permission checks passed")

        try:
            from django_tenants.utils import schema_context
            from django.contrib.auth import get_user_model
            from users.serializers import UserSerializer
            User = get_user_model()

            print(f"  🔍 Updating user in schema: {tenant.schema_name}")

            # Update user in the tenant's schema
            with schema_context(tenant.schema_name):
                try:
                    user = User.objects.get(id=user_id)
                    print(f"  🔍 Found user: {user.email}")
                except User.DoesNotExist:
                    return Response(
                        {"detail": "User not found in this tenant."},
                        status=status.HTTP_404_NOT_FOUND
                    )

                # Prepare update data (exclude password and sensitive fields)
                update_data = request.data.copy()
                update_data.pop('user_id', None)  # Remove user_id from update data
                update_data.pop('password', None)  # Don't allow password updates here
                update_data.pop('password2', None)

                # Handle primary group assignment separately
                primary_group_id = update_data.pop('primary_group_id', None)

                print(f"  🔍 Update data: {update_data}")
                print(f"  🔍 Primary group ID: {primary_group_id}")

                # Check for duplicate email/username (excluding current user)
                if 'email' in update_data:
                    if User.objects.filter(email=update_data['email']).exclude(id=user_id).exists():
                        return Response(
                            {"detail": "User with this email already exists in this tenant."},
                            status=status.HTTP_400_BAD_REQUEST
                        )

                if 'username' in update_data:
                    if User.objects.filter(username=update_data['username']).exclude(id=user_id).exists():
                        return Response(
                            {"detail": "User with this username already exists in this tenant."},
                            status=status.HTTP_400_BAD_REQUEST
                        )

                # Update user fields
                for field, value in update_data.items():
                    if hasattr(user, field):
                        setattr(user, field, value)

                user.save()
                print(f"  ✅ User updated successfully")

                # Handle primary group assignment
                if primary_group_id is not None:
                    try:
                        from users.models_groups import TenantGroup

                        # Remove user from current primary group
                        current_primary = user.get_primary_group()
                        if current_primary:
                            user.remove_from_group(current_primary)
                            print(f"  🔍 Removed from current primary group: {current_primary.name}")

                        # Add to new primary group if specified
                        if primary_group_id:
                            group = TenantGroup.objects.get(id=primary_group_id, is_active=True)
                            user.add_to_group(group, is_primary=True, reason='Updated via user edit')
                            print(f"  ✅ Added to new primary group: {group.name}")
                        else:
                            print(f"  🔍 Primary group cleared")

                    except TenantGroup.DoesNotExist:
                        print(f"  ⚠️ Warning: Primary group {primary_group_id} not found")
                    except Exception as e:
                        print(f"  ⚠️ Warning: Failed to update primary group: {e}")

                # Return updated user data
                serializer = UserSerializer(user)
                return Response(serializer.data)

        except Exception as e:
            print(f"  ❌ Error updating user: {e}")
            import traceback
            traceback.print_exc()
            return Response(
                {"detail": f"Error updating user: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['patch'], permission_classes=[permissions.IsAuthenticated])
    def change_user_password(self, request, pk=None):
        """
        Change password for a user in a specific tenant.
        """
        tenant = self.get_object()
        user_id = request.data.get('user_id')
        new_password = request.data.get('new_password')
        confirm_password = request.data.get('confirm_password')

        if not user_id:
            return Response(
                {"detail": "user_id is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not new_password:
            return Response(
                {"detail": "new_password is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not confirm_password:
            return Response(
                {"detail": "confirm_password is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        if new_password != confirm_password:
            return Response(
                {"detail": "Passwords do not match"},
                status=status.HTTP_400_BAD_REQUEST
            )

        if len(new_password) < 8:
            return Response(
                {"detail": "Password must be at least 8 characters long"},
                status=status.HTTP_400_BAD_REQUEST
            )

        print(f"🔍 TenantViewSet.change_user_password called")
        print(f"  Tenant: {tenant.name} (ID: {tenant.id}, Type: {tenant.type})")
        print(f"  User ID: {user_id}")
        print(f"  Request user: {request.user.email} (Role: {request.user.role})")

        # Permission checks
        if not (request.user.is_superuser or request.user.role == 'superadmin'):
            if tenant.type == 'kebele' and request.user.role not in ['subcity_admin', 'kebele_admin']:
                print(f"  ❌ Permission denied: role={request.user.role}, tenant_type={tenant.type}")
                return Response(
                    {"detail": f"You don't have permission to change passwords for users in this tenant. Your role: {request.user.role}, Tenant type: {tenant.type}"},
                    status=status.HTTP_403_FORBIDDEN
                )
            elif tenant.type == 'subcity' and request.user.role not in ['city_admin', 'subcity_admin']:
                print(f"  ❌ Permission denied: role={request.user.role}, tenant_type={tenant.type}")
                return Response(
                    {"detail": f"You don't have permission to change passwords for users in this tenant. Your role: {request.user.role}, Tenant type: {tenant.type}"},
                    status=status.HTTP_403_FORBIDDEN
                )

        print(f"  ✅ Permission checks passed")

        try:
            from django_tenants.utils import schema_context
            from django.contrib.auth import get_user_model
            User = get_user_model()

            print(f"  🔍 Changing password for user in schema: {tenant.schema_name}")

            # Change password in the tenant's schema
            with schema_context(tenant.schema_name):
                try:
                    user = User.objects.get(id=user_id)
                    print(f"  🔍 Found user: {user.email}")
                except User.DoesNotExist:
                    return Response(
                        {"detail": "User not found in this tenant."},
                        status=status.HTTP_404_NOT_FOUND
                    )

                # Change the password
                user.set_password(new_password)
                user.save()
                print(f"  ✅ Password changed successfully")

                return Response({
                    "detail": "Password changed successfully",
                    "user": {
                        "id": user.id,
                        "email": user.email,
                        "username": user.username
                    }
                })

        except Exception as e:
            print(f"  ❌ Error changing password: {e}")
            import traceback
            traceback.print_exc()
            return Response(
                {"detail": f"Error changing password: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def hierarchy(self, request):
        # Get all city tenants (top level)
        cities = Tenant.objects.filter(type=TenantType.CITY)
        serializer = TenantTreeSerializer(cities, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['patch'], permission_classes=[permissions.IsAuthenticated])
    def update_theme(self, request, pk=None):
        """
        Update theme colors for a tenant.
        Only public tenant (superadmin) can update themes for all tenants.
        """
        tenant = self.get_object()

        # Check if user has permission to update themes
        if not (request.user.is_superuser or request.user.role == 'superadmin'):
            return Response(
                {"detail": "Only superadmin can update tenant themes."},
                status=status.HTTP_403_FORBIDDEN
            )

        # Validate color format
        primary_color = request.data.get('primary_color')
        secondary_color = request.data.get('secondary_color')

        if primary_color and not self._is_valid_hex_color(primary_color):
            return Response(
                {"detail": "Primary color must be a valid hex color (e.g., #ff8f00)."},
                status=status.HTTP_400_BAD_REQUEST
            )

        if secondary_color and not self._is_valid_hex_color(secondary_color):
            return Response(
                {"detail": "Secondary color must be a valid hex color (e.g., #ef6c00)."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update theme colors
        if primary_color:
            tenant.primary_color = primary_color
        if secondary_color:
            tenant.secondary_color = secondary_color

        tenant.save()

        serializer = self.get_serializer(tenant)
        return Response(serializer.data)

    @action(detail=True, methods=['patch'], permission_classes=[permissions.IsAuthenticated])
    def update_public_access(self, request, pk=None):
        """
        Update public access settings for a tenant.
        Only public tenant (superadmin) can update public access for all tenants.
        """
        tenant = self.get_object()

        # Check if user has permission to update public access
        if not (request.user.is_superuser or request.user.role == 'superadmin'):
            return Response(
                {"detail": "Only superadmin can update tenant public access settings."},
                status=status.HTTP_403_FORBIDDEN
            )

        # Update public access settings
        if 'public_id_application_enabled' in request.data:
            tenant.public_id_application_enabled = request.data['public_id_application_enabled']

        tenant.save()

        serializer = self.get_serializer(tenant)
        return Response(serializer.data)

    def _is_valid_hex_color(self, color):
        """Validate hex color format."""
        import re
        pattern = r'^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$'
        return bool(re.match(pattern, color))

    @action(detail=False, methods=['get'])
    def current_context(self, request):
        """Get current tenant context for auto-filling forms."""
        user = request.user

        if not user.tenant:
            return Response({"detail": "No tenant context available."}, status=status.HTTP_404_NOT_FOUND)

        current_tenant = user.tenant
        context = {
            'current_tenant': {
                'id': current_tenant.id,
                'name': current_tenant.name,
                'type': current_tenant.type,
                'schema_name': current_tenant.schema_name,
                'primary_color': current_tenant.primary_color,
                'secondary_color': current_tenant.secondary_color,
                'public_id_application_enabled': current_tenant.public_id_application_enabled,
                'public_service_portal_enabled': current_tenant.public_service_portal_enabled,
            }
        }

        # Add parent information based on tenant type
        if current_tenant.type == TenantType.KEBELE:
            # For kebele, current tenant is kebele, parent is subcity
            context['kebele'] = {
                'id': current_tenant.id,
                'name': current_tenant.name,
            }
            if current_tenant.parent:
                context['subcity'] = {
                    'id': current_tenant.parent.id,
                    'name': current_tenant.parent.name,
                }
                # Get city (grandparent)
                if current_tenant.parent.parent:
                    context['city'] = {
                        'id': current_tenant.parent.parent.id,
                        'name': current_tenant.parent.parent.name,
                    }
        elif current_tenant.type == TenantType.SUBCITY:
            # For subcity, current tenant is subcity
            context['subcity'] = {
                'id': current_tenant.id,
                'name': current_tenant.name,
            }
            if current_tenant.parent:
                context['city'] = {
                    'id': current_tenant.parent.id,
                    'name': current_tenant.parent.name,
                }
        elif current_tenant.type == TenantType.CITY:
            # For city, current tenant is city
            context['city'] = {
                'id': current_tenant.id,
                'name': current_tenant.name,
            }

        return Response(context)

    @action(detail=False, methods=['get'], pagination_class=None, permission_classes=[permissions.AllowAny])
    def subcities(self, request):
        """Get all subcity tenants with profile data for ID cards."""
        from ..models.subcity import SubCity

        subcities = Tenant.objects.filter(type=TenantType.SUBCITY)
        data = []

        for s in subcities:
            subcity_data = {
                'id': s.id,
                'name': s.name,
                'tenant': s.id,  # Add tenant field for ID card compatibility
            }

            # Get subcity profile data if available
            try:
                subcity_profile = SubCity.objects.get(tenant=s)
                subcity_data.update({
                    'mayor_signature': subcity_profile.mayor_signature.url if subcity_profile.mayor_signature else None,
                    'pattern_image': subcity_profile.pattern_image.url if subcity_profile.pattern_image else None,
                    'logo': subcity_profile.logo.url if subcity_profile.logo else None,
                    'name_am': subcity_profile.name_am,
                })
            except SubCity.DoesNotExist:
                subcity_data.update({
                    'mayor_signature': None,
                    'pattern_image': None,
                    'logo': None,
                    'name_am': None,
                })

            data.append(subcity_data)

        return Response(data)

    @action(detail=False, methods=['get'], pagination_class=None, permission_classes=[permissions.AllowAny])
    def kebeles(self, request):
        """Get all kebele tenants with profile data for ID cards."""
        from ..models.kebele import Kebele

        kebeles = Tenant.objects.filter(type=TenantType.KEBELE).select_related('parent')
        data = []

        for k in kebeles:
            kebele_data = {
                'id': k.id,
                'name': k.name,
                'code': f"GD-KB{k.id}",  # Generate code similar to your API response
                'tenant': k.id,  # Add tenant field for ID card compatibility
            }

            if k.parent:  # parent is the subcity
                kebele_data['sub_city'] = k.parent.id
                kebele_data['subcity_name'] = k.parent.name

            # Get kebele profile data if available
            try:
                kebele_profile = Kebele.objects.get(tenant=k)
                kebele_data.update({
                    'logo': kebele_profile.logo.url if kebele_profile.logo else None,
                    'pattern_image': kebele_profile.pattern_image.url if kebele_profile.pattern_image else None,
                    'name_am': kebele_profile.name_am,
                })
            except Kebele.DoesNotExist:
                kebele_data.update({
                    'logo': None,
                    'pattern_image': None,
                    'name_am': None,
                })

            data.append(kebele_data)

        return Response(data)

    @action(detail=False, methods=['get'], pagination_class=None)
    def amharic_names(self, request):
        """Get tenant names with Amharic data for ID card headers."""
        try:
            # Get tenant IDs from query parameters
            city_tenant_id = request.query_params.get('city_tenant_id')
            subcity_tenant_id = request.query_params.get('subcity_tenant_id')
            kebele_tenant_id = request.query_params.get('kebele_tenant_id')

            result = {}

            # Get city Amharic name and mayor signature
            if city_tenant_id:
                try:
                    city_admin = CityAdministration.objects.get(tenant_id=city_tenant_id)
                    result['city_name_am'] = city_admin.city_name_am or city_admin.city_name
                    result['mayor_signature'] = city_admin.mayor_signature.url if city_admin.mayor_signature else None
                except CityAdministration.DoesNotExist:
                    result['city_name_am'] = None
                    result['mayor_signature'] = None

            # Get subcity Amharic name
            if subcity_tenant_id:
                try:
                    subcity = SubCity.objects.get(tenant_id=subcity_tenant_id)
                    result['subcity_name_am'] = subcity.name_am or subcity.name
                except SubCity.DoesNotExist:
                    result['subcity_name_am'] = None

            # Get kebele Amharic name
            if kebele_tenant_id:
                try:
                    kebele = Kebele.objects.get(tenant_id=kebele_tenant_id)
                    result['kebele_name_am'] = kebele.name_am or kebele.name
                except Kebele.DoesNotExist:
                    result['kebele_name_am'] = None

            return Response(result)

        except Exception as e:
            return Response(
                {'error': f'Failed to fetch Amharic names: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def create_admin(self, request, pk=None):
        """Create an admin user for a specific tenant in the tenant's schema."""
        tenant = self.get_object()

        # Validate required fields
        required_fields = ['email', 'username', 'password', 'password2', 'first_name', 'last_name', 'role']
        for field in required_fields:
            if field not in request.data:
                return Response(
                    {field: ["This field is required."]},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Validate password confirmation
        if request.data['password'] != request.data['password2']:
            return Response(
                {"password": ["Password fields didn't match."]},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Validate role matches tenant type
        tenant_type = tenant.type
        role = request.data['role']

        # Define valid roles for each tenant type
        valid_roles = {
            'city': ['city_admin'],
            'subcity': ['subcity_admin'],
            'kebele': ['kebele_leader', 'clerk']
        }

        if tenant_type not in valid_roles or role not in valid_roles[tenant_type]:
            valid_roles_str = ', '.join(valid_roles.get(tenant_type, []))
            return Response(
                {"role": [f"Role must be one of [{valid_roles_str}] for {tenant_type} tenant."]},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Create user in the tenant's schema
            with schema_context(tenant.schema_name):
                # Check if user already exists in this schema
                if User.objects.filter(email=request.data['email']).exists():
                    return Response(
                        {"email": ["User with this email already exists in this tenant."]},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                if User.objects.filter(username=request.data['username']).exists():
                    return Response(
                        {"username": ["User with this username already exists in this tenant."]},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Create the user
                user = User.objects.create_user(
                    email=request.data['email'],
                    username=request.data['username'],
                    password=request.data['password'],
                    first_name=request.data['first_name'],
                    last_name=request.data['last_name'],
                    role=request.data['role'],
                    tenant=tenant,
                    phone_number=request.data.get('phone_number', ''),
                    is_active=True
                )

                # Automatically assign user to appropriate group based on role
                self._assign_user_to_default_group(user, tenant)

                return Response({
                    "id": user.id,
                    "email": user.email,
                    "username": user.username,
                    "role": user.role,
                    "tenant": tenant.id,
                    "message": f"Admin user created successfully in {tenant.name} tenant and assigned to {user.role} group."
                }, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response(
                {"error": f"Failed to create admin user: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _assign_user_to_default_group(self, user, tenant):
        """Assign user to appropriate default group based on their role"""
        try:
            from users.models_groups import TenantGroup, GroupMembership
            from django_tenants.utils import schema_context, get_public_schema_name

            # Map roles to group names
            role_to_group = {
                'superadmin': 'super_admin',
                'city_admin': 'city_admin',
                'subcity_admin': 'subcity_admin',
                'kebele_leader': 'kebele_leader',
                'clerk': 'clerk'
            }

            group_name = role_to_group.get(user.role)
            if not group_name:
                print(f"  ⚠️ No default group mapping for role: {user.role}")
                return

            # Assign user to group (groups are stored in public schema)
            with schema_context(get_public_schema_name()):
                try:
                    tenant_group = TenantGroup.objects.get(group__name=group_name)

                    # Check if user is already in the group
                    existing_membership = GroupMembership.objects.filter(
                        user_email=user.email,
                        group=tenant_group,
                        is_active=True
                    ).first()

                    if existing_membership:
                        print(f"  ♻️ User {user.email} already in {group_name} group")
                        return

                    # Create group membership
                    membership = GroupMembership.objects.create(
                        user_email=user.email,
                        user_tenant_id=tenant.id,
                        user_tenant_schema=tenant.schema_name,
                        group=tenant_group,
                        assigned_by_email='<EMAIL>',
                        reason=f'Automatic assignment during user creation for role: {user.role}',
                        is_primary=True,
                        is_active=True
                    )

                    print(f"  ✅ Assigned {user.email} to {group_name} group")

                except TenantGroup.DoesNotExist:
                    print(f"  ❌ Group {group_name} not found! Please run setup_general_permissions.py")

        except Exception as e:
            print(f"  ❌ Failed to assign user to group: {e}")
            # Don't fail user creation if group assignment fails


class DomainViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing domains.

    Permissions:
    - Only super admins can manage domains
    """
    queryset = Domain.objects.all()
    serializer_class = DomainSerializer
    permission_classes = [permissions.IsAuthenticated, IsSuperUser]
    filterset_fields = ['tenant']


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def switch_tenant(request):
    """
    Switch to a different tenant and return a new JWT token.
    """
    tenant_id = request.data.get('tenant_id')

    if not tenant_id:
        return Response({"detail": "Tenant ID is required."}, status=status.HTTP_400_BAD_REQUEST)

    try:
        tenant = Tenant.objects.get(id=tenant_id)
    except Tenant.DoesNotExist:
        return Response({"detail": "Tenant not found."}, status=status.HTTP_404_NOT_FOUND)

    user = request.user

    # Check if user has access to this tenant
    if not user.is_superuser:
        if user.tenant:
            # Check if user's tenant is the requested tenant or in its hierarchy
            if user.tenant != tenant:
                # Check if requested tenant is a child of user's tenant
                if tenant.type == 'subcity' and tenant.parent == user.tenant:
                    # User from city can access subcity
                    pass
                elif tenant.type == 'kebele' and tenant.parent and tenant.parent.parent == user.tenant:
                    # User from city can access kebele
                    pass
                elif tenant.type == 'kebele' and tenant.parent == user.tenant:
                    # User from subcity can access kebele
                    pass
                else:
                    # User doesn't have access to this tenant
                    return Response({"detail": "You don't have access to this tenant."}, status=status.HTTP_403_FORBIDDEN)
        else:
            return Response({"detail": "You don't have access to any tenant."}, status=status.HTTP_403_FORBIDDEN)

    # Get a domain for this tenant
    domain = Domain.objects.filter(tenant=tenant).first()
    if not domain:
        return Response({"detail": "No domain found for this tenant."}, status=status.HTTP_404_NOT_FOUND)

    # Set the tenant in the connection
    connection.set_tenant(tenant)

    # Generate new token with tenant information
    refresh = RefreshToken.for_user(user)

    # Add custom claims
    tenant_data = {
        'id': tenant.id,
        'name': tenant.name,
        'type': tenant.type,
        'schema_name': tenant.schema_name,
    }

    # Add parent tenant info if available
    if tenant.parent:
        tenant_data['parent'] = {
            'id': tenant.parent.id,
            'name': tenant.parent.name,
            'type': tenant.parent.type,
        }

    refresh['tenant'] = tenant_data
    refresh['role'] = user.role
    refresh['is_superuser'] = user.is_superuser

    return Response({
        'access': str(refresh.access_token),
        'refresh': str(refresh),
        'tenant': tenant_data
    })
