#!/usr/bin/env python3
"""
Device Configuration Service - Try different device settings to get proper frame size
"""

import logging
import ctypes
from ctypes import cdll, c_int, c_uint, c_void_p, POINTER, byref
import time
from flask import Flask, jsonify

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Constants
FTR_OK = 0
FTR_IMAGE_SIZE = 320 * 480

class ConfigurableDevice:
    def __init__(self):
        self.scan_api = None
        self.device_handle = None
        self.connected = False
    
    def initialize(self):
        """Initialize device"""
        try:
            logger.info("🔧 Loading Futronic SDK...")
            self.scan_api = cdll.LoadLibrary('./ftrScanAPI.dll')
            
            # Setup function signatures
            self.scan_api.ftrScanOpenDevice.restype = c_void_p
            self.scan_api.ftrScanCloseDevice.argtypes = [c_void_p]
            self.scan_api.ftrScanCloseDevice.restype = c_int
            self.scan_api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(ctypes.c_ubyte), POINTER(c_uint)]
            self.scan_api.ftrScanGetFrame.restype = c_int
            self.scan_api.ftrScanGetImageSize.argtypes = [c_void_p, POINTER(c_int), POINTER(c_int)]
            self.scan_api.ftrScanGetImageSize.restype = c_int
            self.scan_api.ftrScanSetOptions.argtypes = [c_void_p, c_int]
            self.scan_api.ftrScanSetOptions.restype = c_int
            
            logger.info("📱 Opening device...")
            self.device_handle = self.scan_api.ftrScanOpenDevice()
            
            if self.device_handle:
                logger.info(f"✅ Device opened! Handle: {self.device_handle}")
                self.connected = True
                return True
            else:
                logger.error("❌ Failed to open device")
                return False
                
        except Exception as e:
            logger.error(f"❌ Initialization error: {e}")
            return False
    
    def test_device_configuration(self):
        """Test different device configurations"""
        results = []
        
        if not self.connected:
            return {'error': 'Device not connected'}
        
        # Test 1: Get actual image size
        logger.info("🔧 Test 1: Getting device image size...")
        try:
            width = c_int()
            height = c_int()
            size_result = self.scan_api.ftrScanGetImageSize(self.device_handle, byref(width), byref(height))
            
            if size_result == FTR_OK:
                actual_size = width.value * height.value
                logger.info(f"✅ Device reports: {width.value}x{height.value} = {actual_size} bytes")
                results.append({
                    'test': 'Image Size',
                    'success': True,
                    'width': width.value,
                    'height': height.value,
                    'total_size': actual_size
                })
            else:
                logger.warning(f"⚠️ Image size query failed: {size_result}")
                results.append({
                    'test': 'Image Size',
                    'success': False,
                    'error': f'Query failed with code {size_result}'
                })
        except Exception as e:
            results.append({
                'test': 'Image Size',
                'success': False,
                'error': str(e)
            })
        
        # Test 2: Try different device options
        logger.info("🔧 Test 2: Testing device options...")
        option_values = [0, 1, 2, 4, 8, 16, 32]
        
        for option in option_values:
            try:
                options_result = self.scan_api.ftrScanSetOptions(self.device_handle, option)
                
                if options_result == FTR_OK:
                    logger.info(f"✅ Option {option} set successfully")
                    
                    # Try capture with this option
                    capture_result = self.test_capture_with_option(option)
                    results.append({
                        'test': f'Option {option}',
                        'success': True,
                        'option_value': option,
                        'capture_result': capture_result
                    })
                else:
                    logger.info(f"⚠️ Option {option} failed: {options_result}")
                    results.append({
                        'test': f'Option {option}',
                        'success': False,
                        'option_value': option,
                        'error': f'Set option failed with code {options_result}'
                    })
                    
            except Exception as e:
                results.append({
                    'test': f'Option {option}',
                    'success': False,
                    'option_value': option,
                    'error': str(e)
                })
        
        # Test 3: Try different buffer sizes
        logger.info("🔧 Test 3: Testing different buffer sizes...")
        buffer_sizes = [320*480, 640*480, 320*240, 256*256, 512*512]
        
        for buf_size in buffer_sizes:
            try:
                capture_result = self.test_capture_with_buffer_size(buf_size)
                results.append({
                    'test': f'Buffer Size {buf_size}',
                    'success': capture_result['frame_size'] > 1000,
                    'buffer_size': buf_size,
                    'capture_result': capture_result
                })
            except Exception as e:
                results.append({
                    'test': f'Buffer Size {buf_size}',
                    'success': False,
                    'buffer_size': buf_size,
                    'error': str(e)
                })
        
        return {
            'device_handle': self.device_handle,
            'total_tests': len(results),
            'successful_tests': sum(1 for r in results if r['success']),
            'results': results
        }
    
    def test_capture_with_option(self, option_value):
        """Test capture with specific device option"""
        try:
            image_buffer = (ctypes.c_ubyte * FTR_IMAGE_SIZE)()
            frame_size = c_uint(FTR_IMAGE_SIZE)
            
            result = self.scan_api.ftrScanGetFrame(self.device_handle, image_buffer, byref(frame_size))
            
            return {
                'result_code': result,
                'frame_size': frame_size.value,
                'success': result == FTR_OK
            }
            
        except Exception as e:
            return {
                'result_code': -1,
                'frame_size': 0,
                'success': False,
                'error': str(e)
            }
    
    def test_capture_with_buffer_size(self, buffer_size):
        """Test capture with different buffer size"""
        try:
            image_buffer = (ctypes.c_ubyte * buffer_size)()
            frame_size = c_uint(buffer_size)
            
            result = self.scan_api.ftrScanGetFrame(self.device_handle, image_buffer, byref(frame_size))
            
            return {
                'result_code': result,
                'frame_size': frame_size.value,
                'buffer_size': buffer_size,
                'success': result == FTR_OK
            }
            
        except Exception as e:
            return {
                'result_code': -1,
                'frame_size': 0,
                'buffer_size': buffer_size,
                'success': False,
                'error': str(e)
            }
    
    def close(self):
        """Close device"""
        if self.device_handle and self.scan_api:
            try:
                self.scan_api.ftrScanCloseDevice(self.device_handle)
                logger.info("🔒 Device closed")
            except Exception as e:
                logger.error(f"Close error: {e}")

# Global device
device = ConfigurableDevice()

@app.route('/api/test/configuration', methods=['GET'])
def test_configuration():
    """Test device configuration"""
    logger.info("🧪 Starting device configuration test...")
    results = device.test_device_configuration()
    return jsonify(results)

@app.route('/', methods=['GET'])
def index():
    """Web interface"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Device Configuration Test</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            button { padding: 15px 30px; margin: 10px; font-size: 16px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
            .result { margin: 20px 0; padding: 15px; border-radius: 5px; background: #f8f9fa; }
            .success { background: #d4edda; color: #155724; }
            .error { background: #f8d7da; color: #721c24; }
            .test-item { margin: 10px 0; padding: 10px; border-left: 3px solid #007bff; background: #f8f9fa; }
            pre { background: #f1f1f1; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
        </style>
    </head>
    <body>
        <h1>🔧 Device Configuration Test</h1>
        <p>This test tries different device settings to find the optimal configuration.</p>
        
        <button onclick="runConfigurationTest()">🧪 Run Configuration Test</button>
        
        <div id="result"></div>
        
        <script>
            async function runConfigurationTest() {
                const resultDiv = document.getElementById('result');
                resultDiv.innerHTML = '<div class="result">🧪 Running configuration tests... This may take a minute.</div>';
                
                try {
                    const response = await fetch('/api/test/configuration');
                    const data = await response.json();
                    
                    if (data.error) {
                        resultDiv.innerHTML = `<div class="result error"><h3>❌ Test Failed</h3><p>${data.error}</p></div>`;
                        return;
                    }
                    
                    let html = `
                        <div class="result success">
                            <h3>🧪 Configuration Test Results</h3>
                            <p><strong>Total Tests:</strong> ${data.total_tests}</p>
                            <p><strong>Successful:</strong> ${data.successful_tests}</p>
                            <p><strong>Device Handle:</strong> ${data.device_handle}</p>
                        </div>
                    `;
                    
                    data.results.forEach(test => {
                        const statusClass = test.success ? 'success' : 'error';
                        const statusIcon = test.success ? '✅' : '❌';
                        
                        html += `
                            <div class="test-item">
                                <h4>${statusIcon} ${test.test}</h4>
                                <pre>${JSON.stringify(test, null, 2)}</pre>
                            </div>
                        `;
                    });
                    
                    resultDiv.innerHTML = html;
                    
                } catch (error) {
                    resultDiv.innerHTML = `<div class="result error"><h3>❌ Network Error</h3><p>${error.message}</p></div>`;
                }
            }
        </script>
    </body>
    </html>
    """

if __name__ == '__main__':
    try:
        logger.info("🚀 Starting Device Configuration Service")
        
        # Initialize device
        if device.initialize():
            logger.info("✅ Device ready for configuration testing")
        else:
            logger.error("❌ Device initialization failed")
            exit(1)
        
        # Start service
        logger.info("🌐 Service running at http://localhost:8001")
        logger.info("🔧 This service tests different device configurations")
        
        app.run(host='0.0.0.0', port=8001, debug=False, threaded=True)
        
    except KeyboardInterrupt:
        logger.info("🛑 Service stopped")
    except Exception as e:
        logger.error(f"❌ Service error: {e}")
    finally:
        device.close()
