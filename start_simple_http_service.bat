@echo off
echo ========================================
echo   SIMPLE HTTP Biometric Service
echo ========================================
echo.
echo 🎯 NO FLASK - Direct HTTP Server
echo.
echo Flask was causing conflicts with the SDK.
echo This version uses Python's built-in HTTP server
echo for maximum compatibility with Futronic SDK.
echo.
echo FEATURES:
echo - No Flask threading conflicts
echo - Direct HTTP request handling
echo - Minimal overhead
echo - SDK-compatible architecture
echo - Same API endpoints as before
echo.

cd local-biometric-service

echo 🚀 Starting SIMPLE HTTP service...
echo 📱 Service URL: http://localhost:8001
echo 🔧 SDK-compatible HTTP server
echo.
echo This should finally work without crashes!
echo.

python simple_http_service.py

echo.
echo Simple HTTP service stopped.
pause
