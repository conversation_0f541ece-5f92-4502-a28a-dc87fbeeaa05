#!/usr/bin/env python3
"""
Test the domain name fix by creating a test tenant.
"""
import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
sys.path.append('/app')
django.setup()

def test_domain_fix():
    """Test that domain names are properly used."""
    print('=== Testing Domain Name Fix ===')
    
    # Test data
    test_data = {
        'city_name': 'Test City Domain',
        'tenant_name': 'Test City Domain',
        'domain_name': 'testcity.com',  # This should be used instead of schema name
        'country': 1,
        'region': 1,
        'contact_email': '<EMAIL>',
        'is_active': True
    }
    
    print(f'Test data: {test_data}')
    
    # Make API call to create city tenant
    try:
        response = requests.post(
            'http://localhost:8000/api/tenants/cities/',
            data=test_data,
            headers={
                'Authorization': 'Bearer your_token_here',  # You'll need to get a real token
                'Content-Type': 'application/json'
            }
        )
        
        print(f'Response status: {response.status_code}')
        print(f'Response data: {response.json() if response.content else "No content"}')
        
        if response.status_code == 201:
            print('✅ City created successfully!')
            response_data = response.json()
            domain_name = response_data.get('domain_name')
            print(f'Domain name returned: {domain_name}')
            
            if domain_name == 'testcity.com':
                print('✅ Domain name fix is working!')
            else:
                print(f'❌ Domain name fix not working. Expected: testcity.com, Got: {domain_name}')
        else:
            print(f'❌ Failed to create city: {response.text}')
            
    except Exception as e:
        print(f'❌ Error testing domain fix: {e}')
    
    # Check database directly
    print('\n=== Checking Database ===')
    from django.db import connection
    cursor = connection.cursor()
    
    # Check latest domain
    cursor.execute("SELECT domain, tenant_id FROM tenants_domain ORDER BY id DESC LIMIT 5;")
    domains = cursor.fetchall()
    print('Latest domains:')
    for domain in domains:
        print(f'  - {domain[0]} (tenant: {domain[1]})')
    
    return True

if __name__ == "__main__":
    test_domain_fix()
