#!/usr/bin/env python3
"""
Simple Status Service - Just reports device connected
"""

from flask import Flask, jsonify
from flask_cors import CORS
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

@app.route('/api/device/status', methods=['GET'])
def device_status():
    logger.info("Status check requested")
    return jsonify({
        'success': True,
        'data': {
            'connected': True,
            'device_connected': True,
            'model': 'Futronic FS88H',
            'status': 'connected'
        }
    })

@app.route('/api/health', methods=['GET'])
def health():
    return jsonify({'status': 'healthy', 'device_connected': True})

if __name__ == '__main__':
    logger.info("🚀 Simple Status Service on port 8002")
    app.run(host='0.0.0.0', port=8002, debug=False)
