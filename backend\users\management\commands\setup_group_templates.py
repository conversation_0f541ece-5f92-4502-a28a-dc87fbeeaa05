from django.core.management.base import BaseCommand
from django.contrib.auth.models import Permission
from users.models_groups import GroupTemplate


class Command(BaseCommand):
    help = 'Set up default group templates for the system'

    def handle(self, *args, **options):
        self.stdout.write('Setting up default group templates...')
        
        # First, let's see what permissions are available
        self.stdout.write('Available permissions:')
        all_permissions = Permission.objects.all()
        for perm in all_permissions:
            self.stdout.write(f'  {perm.codename} - {perm.name} ({perm.content_type.model})')

        # Define group templates with their permissions (using actual permission codenames)
        templates = [
            {
                'name': 'Kebele Clerk',
                'description': 'Basic clerk permissions for kebele operations',
                'group_type': 'operational',
                'level': 10,
                'permissions': [
                    'view_citizen', 'add_citizen', 'change_citizen',
                    'view_idcard', 'add_idcard'
                ]
            },
            {
                'name': '<PERSON>bele Leader',
                'description': 'Leadership permissions for kebele management',
                'group_type': 'administrative',
                'level': 30,
                'permissions': [
                    'view_citizen', 'add_citizen', 'change_citizen', 'delete_citizen',
                    'view_idcard', 'add_idcard', 'change_idcard',
                    'view_user', 'add_user'
                ]
            },
            {
                'name': 'ID Card Processor',
                'description': 'Specialized permissions for ID card processing',
                'group_type': 'functional',
                'level': 20,
                'permissions': [
                    'view_citizen', 'view_idcard', 'add_idcard',
                    'change_idcard'
                ]
            },
            {
                'name': 'Emergency Coordinator',
                'description': 'Emergency response and coordination permissions',
                'group_type': 'functional',
                'level': 50,
                'permissions': [
                    'view_citizen', 'add_citizen', 'change_citizen',
                    'view_idcard', 'add_idcard', 'change_idcard',
                    'view_user'
                ]
            },
            {
                'name': 'Data Entry Specialist',
                'description': 'Focused on data entry and basic citizen management',
                'group_type': 'operational',
                'level': 15,
                'permissions': [
                    'view_citizen', 'add_citizen', 'change_citizen',
                    'view_idcard', 'add_idcard'
                ]
            },
            {
                'name': 'Supervisor',
                'description': 'Supervisory permissions with user management',
                'group_type': 'administrative',
                'level': 40,
                'permissions': [
                    'view_citizen', 'add_citizen', 'change_citizen', 'delete_citizen',
                    'view_idcard', 'add_idcard', 'change_idcard',
                    'view_user', 'add_user', 'change_user'
                ]
            }
        ]
        
        created_count = 0
        updated_count = 0
        
        for template_data in templates:
            template_name = template_data['name']
            permission_codenames = template_data.pop('permissions')
            
            # Get or create template
            template, created = GroupTemplate.objects.get_or_create(
                name=template_name,
                defaults={
                    **template_data,
                    'is_system_template': True,
                    'tenant_types': ['kebele', 'subcity', 'city']  # Applicable to all levels
                }
            )
            
            if created:
                created_count += 1
                self.stdout.write(f'  Created template: {template_name}')
            else:
                updated_count += 1
                # Update existing template
                for key, value in template_data.items():
                    setattr(template, key, value)
                template.save()
                self.stdout.write(f'  Updated template: {template_name}')
            
            # Assign permissions
            permissions = []
            for codename in permission_codenames:
                try:
                    # Get the first permission with this codename (they're duplicated across schemas)
                    permission = Permission.objects.filter(codename=codename).first()
                    if permission:
                        permissions.append(permission)
                    else:
                        self.stdout.write(
                            self.style.WARNING(f'    Permission not found: {codename}')
                        )
                except Exception as e:
                    self.stdout.write(
                        self.style.WARNING(f'    Error with permission {codename}: {e}')
                    )
            
            template.permissions.set(permissions)
            self.stdout.write(f'    Assigned {len(permissions)} permissions')
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully set up group templates: '
                f'{created_count} created, {updated_count} updated'
            )
        )
