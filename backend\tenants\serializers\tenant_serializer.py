from rest_framework import serializers
from ..models.tenant import Tenant, Domain


class DomainSerializer(serializers.ModelSerializer):
    class Meta:
        model = Domain
        fields = ('id', 'domain', 'is_primary')


class TenantSerializer(serializers.ModelSerializer):
    domains = serializers.SerializerMethodField()
    parent_name = serializers.CharField(source='parent.name', read_only=True)
    children_count = serializers.SerializerMethodField()
    users_count = serializers.SerializerMethodField()
    primary_domain = serializers.SerializerMethodField()
    profile_data = serializers.SerializerMethodField()

    class Meta:
        model = Tenant
        fields = ('id', 'name', 'name_am', 'type', 'parent', 'parent_name', 'schema_name', 'domains',
                 'primary_domain', 'children_count', 'users_count', 'created_on',
                 'primary_color', 'secondary_color', 'public_id_application_enabled', 'profile_data')
        read_only_fields = ('schema_name', 'created_on')

    def get_domains(self, obj):
        """Get all domains for this tenant."""
        try:
            from ..models.tenant import Domain
            domains = Domain.objects.filter(tenant=obj)
            return DomainSerializer(domains, many=True).data
        except Exception:
            return []

    def get_children_count(self, obj):
        """Get the number of child tenants."""
        return obj.children.count()

    def get_users_count(self, obj):
        """Get the number of users in this tenant's schema."""
        try:
            from django_tenants.utils import schema_context
            from django.contrib.auth import get_user_model
            User = get_user_model()

            with schema_context(obj.schema_name):
                return User.objects.count()
        except Exception:
            return 0

    def get_primary_domain(self, obj):
        """Get the primary domain for this tenant."""
        try:
            # Try different possible related names
            if hasattr(obj, 'domains'):
                primary_domain = obj.domains.filter(is_primary=True).first()
            elif hasattr(obj, 'domain_set'):
                primary_domain = obj.domain_set.filter(is_primary=True).first()
            else:
                # Import Domain model and query directly
                from ..models.tenant import Domain
                primary_domain = Domain.objects.filter(tenant=obj, is_primary=True).first()

            return primary_domain.domain if primary_domain else None
        except Exception as e:
            print(f"Error getting primary domain: {e}")
            return None

    def get_profile_data(self, obj):
        """Get profile data based on tenant type."""
        try:
            if obj.type == 'city' and hasattr(obj, 'city_profile'):
                from .city_serializer import CityAdministrationSerializer
                return CityAdministrationSerializer(obj.city_profile).data
            elif obj.type == 'subcity' and hasattr(obj, 'subcity_profile'):
                from .subcity_serializer import SubCitySerializer
                return SubCitySerializer(obj.subcity_profile).data
            elif obj.type == 'kebele' and hasattr(obj, 'kebele_profile'):
                from .kebele_serializer import KebeleSerializer
                return KebeleSerializer(obj.kebele_profile).data
            return None
        except Exception as e:
            # Return None if profile doesn't exist or there's an error
            print(f"Error getting profile data: {e}")
            return None

    def validate(self, data):
        tenant_type = data.get('type')
        parent = data.get('parent')

        # Validate parent-child relationship based on tenant type
        if tenant_type == 'city' and parent is not None:
            raise serializers.ValidationError("City cannot have a parent tenant")

        if tenant_type == 'subcity':
            if parent is None:
                raise serializers.ValidationError("Sub-city must have a parent city")
            if parent.type != 'city':
                raise serializers.ValidationError("Sub-city's parent must be a city")

        if tenant_type == 'kebele':
            if parent is None:
                raise serializers.ValidationError("Kebele must have a parent sub-city")
            if parent.type != 'subcity':
                raise serializers.ValidationError("Kebele's parent must be a sub-city")

        return data


class TenantTreeSerializer(serializers.ModelSerializer):
    children = serializers.SerializerMethodField()

    class Meta:
        model = Tenant
        fields = ('id', 'name', 'name_am', 'type', 'schema_name', 'children')

    def get_children(self, obj):
        children = obj.children.all()
        return TenantTreeSerializer(children, many=True).data
