from rest_framework_simplejwt.authentication import J<PERSON>TAuthentication
from django.contrib.auth import get_user_model
from tenants.models import Tenant
from django_tenants.utils import schema_context
import jwt


class TenantAwareJWTAuthentication(JWTAuthentication):
    """
    Custom JWT authentication that populates user.tenant from JWT token and handles schema context.
    """

    def get_user(self, validated_token):
        """
        Get user from the correct schema based on tenant information in the token.
        """
        try:
            # Use the settings from JWT configuration
            from django.conf import settings
            user_id_claim = getattr(settings, 'SIMPLE_JWT', {}).get('USER_ID_CLAIM', 'user_id')
            user_id_field = getattr(settings, 'SIMPLE_JWT', {}).get('USER_ID_FIELD', 'id')

            user_id = validated_token[user_id_claim]
            tenant_id = validated_token.get('tenant_id')
            tenant_schema = validated_token.get('tenant_schema')

            print(f"🔍 TenantAwareJWTAuthentication: Getting user {user_id} from tenant {tenant_id} (schema: {tenant_schema})")

            User = get_user_model()

            # If we have tenant information, try to get user from tenant schema first
            if tenant_id and tenant_schema:
                try:
                    tenant = Tenant.objects.get(id=tenant_id)
                    with schema_context(tenant.schema_name):
                        user = User.objects.get(**{user_id_field: user_id})
                        print(f"✅ Found user in tenant schema: {user.username}")
                        return user
                except (Tenant.DoesNotExist, User.DoesNotExist):
                    print(f"⚠️ User {user_id} not found in tenant schema {tenant_schema}")

            # Fallback: try to get user from public schema
            try:
                user = User.objects.get(**{user_id_field: user_id})
                print(f"✅ Found user in public schema: {user.username}")
                return user
            except User.DoesNotExist:
                print(f"❌ User {user_id} not found in any schema")

        except KeyError:
            print(f"❌ Invalid token: missing user_id claim")

        return None

    def authenticate(self, request):
        """
        Authenticate the request and populate user.tenant and user.role from JWT token.
        """
        result = super().authenticate(request)

        if result is not None:
            user, validated_token = result

            # Extract information from the token
            tenant_id = validated_token.get('tenant_id')
            role = validated_token.get('role')
            is_superuser = validated_token.get('is_superuser', False)

            # Set role from token (this ensures the role is current)
            # The role in the JWT token is the authoritative source for the current session
            if role:
                user.role = role
                print(f"🔍 TenantAwareJWTAuthentication: Setting user role to '{role}' from JWT token")

            # Set is_superuser from token
            user.is_superuser = is_superuser

            # Set tenant information
            if tenant_id:
                try:
                    tenant = Tenant.objects.get(id=tenant_id)
                    # Set tenant attribute on user object
                    user.tenant = tenant
                    print(f"🔍 TenantAwareJWTAuthentication: Setting user tenant to '{tenant.name}'")
                except Tenant.DoesNotExist:
                    # If tenant doesn't exist, continue without setting it
                    print(f"⚠️ Tenant {tenant_id} not found")
                    pass

            return user, validated_token

        return result
