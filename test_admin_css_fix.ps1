# PowerShell script to test Django Admin CSS fix
Write-Host "🧪 Testing Django Admin Interface CSS Fix" -ForegroundColor Cyan
Write-Host "=" * 50

$baseUrl = "http://localhost:8000"
$allTestsPassed = $true

# Test 1: Admin page accessibility
Write-Host "1. Testing admin page accessibility..." -ForegroundColor Yellow
try {
    $adminResponse = Invoke-WebRequest -Uri "$baseUrl/admin/" -UseBasicParsing -TimeoutSec 10
    if ($adminResponse.StatusCode -eq 200) {
        Write-Host "   ✅ Admin page is accessible (Status: 200)" -ForegroundColor Green
        
        # Check if the response contains CSS link tags
        if ($adminResponse.Content -match "admin/css/base.css") {
            Write-Host "   ✅ Admin page contains CSS references" -ForegroundColor Green
        } else {
            Write-Host "   ⚠️  Admin page missing CSS references" -ForegroundColor Yellow
        }
    } else {
        Write-Host "   ❌ Admin page not accessible (Status: $($adminResponse.StatusCode))" -ForegroundColor Red
        $allTestsPassed = $false
    }
} catch {
    Write-Host "   ❌ Failed to access admin page: $($_.Exception.Message)" -ForegroundColor Red
    $allTestsPassed = $false
}

# Test 2: CSS file serving
Write-Host "`n2. Testing CSS file serving..." -ForegroundColor Yellow
$cssFiles = @(
    "/static/admin/css/base.css",
    "/static/admin/css/login.css"
)

$cssSuccess = 0
foreach ($cssFile in $cssFiles) {
    try {
        $cssResponse = Invoke-WebRequest -Uri "$baseUrl$cssFile" -UseBasicParsing -TimeoutSec 10
        $contentType = $cssResponse.Headers["Content-Type"]
        
        if ($cssResponse.StatusCode -eq 200) {
            if ($contentType -match "text/css") {
                Write-Host "   ✅ $cssFile - Status: 200, Content-Type: $contentType" -ForegroundColor Green
                $cssSuccess++
            } else {
                Write-Host "   ⚠️  $cssFile - Status: 200, but wrong Content-Type: $contentType" -ForegroundColor Yellow
            }
        } else {
            Write-Host "   ❌ $cssFile - Status: $($cssResponse.StatusCode)" -ForegroundColor Red
        }
    } catch {
        Write-Host "   ❌ $cssFile - Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test 3: JavaScript file serving
Write-Host "`n3. Testing JavaScript file serving..." -ForegroundColor Yellow
$jsFiles = @(
    "/static/admin/js/core.js"
)

$jsSuccess = 0
foreach ($jsFile in $jsFiles) {
    try {
        $jsResponse = Invoke-WebRequest -Uri "$baseUrl$jsFile" -UseBasicParsing -TimeoutSec 10
        $contentType = $jsResponse.Headers["Content-Type"]
        
        if ($jsResponse.StatusCode -eq 200) {
            if ($contentType -match "javascript" -or $contentType -match "text/plain") {
                Write-Host "   ✅ $jsFile - Status: 200, Content-Type: $contentType" -ForegroundColor Green
                $jsSuccess++
            } else {
                Write-Host "   ⚠️  $jsFile - Status: 200, but unexpected Content-Type: $contentType" -ForegroundColor Yellow
            }
        } else {
            Write-Host "   ❌ $jsFile - Status: $($jsResponse.StatusCode)" -ForegroundColor Red
        }
    } catch {
        Write-Host "   ❌ $jsFile - Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test 4: Check for HTML being served instead of static files (the original problem)
Write-Host "`n4. Testing for HTML content in static files (original issue)..." -ForegroundColor Yellow
try {
    $cssResponse = Invoke-WebRequest -Uri "$baseUrl/static/admin/css/base.css" -UseBasicParsing -TimeoutSec 10
    if ($cssResponse.StatusCode -eq 200) {
        $content = $cssResponse.Content.Substring(0, [Math]::Min(200, $cssResponse.Content.Length))
        if ($content.Trim().StartsWith("<!DOCTYPE html>") -or $content -match "<html") {
            Write-Host "   ❌ CSS file contains HTML content - ISSUE NOT FIXED!" -ForegroundColor Red
            $allTestsPassed = $false
        } else {
            Write-Host "   ✅ CSS file contains actual CSS content - ISSUE FIXED!" -ForegroundColor Green
        }
    } else {
        Write-Host "   ❌ Could not retrieve CSS file (Status: $($cssResponse.StatusCode))" -ForegroundColor Red
        $allTestsPassed = $false
    }
} catch {
    Write-Host "   ❌ Error testing CSS content: $($_.Exception.Message)" -ForegroundColor Red
    $allTestsPassed = $false
}

# Summary
Write-Host "`n$('=' * 50)" -ForegroundColor Cyan
Write-Host "📊 TEST SUMMARY:" -ForegroundColor Cyan
Write-Host "   CSS Files Working: $cssSuccess/$($cssFiles.Count)" -ForegroundColor White
Write-Host "   JS Files Working: $jsSuccess/$($jsFiles.Count)" -ForegroundColor White

if ($cssSuccess -eq $cssFiles.Count -and $jsSuccess -eq $jsFiles.Count -and $allTestsPassed) {
    Write-Host "   🎉 ALL TESTS PASSED - Django Admin CSS Issue is FIXED!" -ForegroundColor Green
    exit 0
} else {
    Write-Host "   Some tests failed - Issue may not be fully resolved" -ForegroundColor Yellow
    exit 1
}
