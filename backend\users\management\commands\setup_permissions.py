from django.core.management.base import BaseCommand
from django.db import transaction
from users.models import Permission, RolePermission, UserRole


class Command(BaseCommand):
    help = 'Setup initial permissions and role-based permissions'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up permissions...'))
        
        with transaction.atomic():
            # Define all permissions
            permissions_data = [
                # Citizen Management
                {
                    'codename': 'view_citizens',
                    'name': 'View Citizens',
                    'description': 'Can view citizen records and details',
                    'category': 'citizens'
                },
                {
                    'codename': 'create_citizens',
                    'name': 'Create Citizens',
                    'description': 'Can register new citizens',
                    'category': 'citizens'
                },
                {
                    'codename': 'edit_citizens',
                    'name': 'Edit Citizens',
                    'description': 'Can modify citizen information',
                    'category': 'citizens'
                },
                {
                    'codename': 'delete_citizens',
                    'name': 'Delete Citizens',
                    'description': 'Can delete citizen records',
                    'category': 'citizens'
                },
                
                # ID Card Management
                {
                    'codename': 'view_id_cards',
                    'name': 'View ID Cards',
                    'description': 'Can view ID card applications and records',
                    'category': 'id_cards'
                },
                {
                    'codename': 'create_id_cards',
                    'name': 'Create ID Cards',
                    'description': 'Can create new ID card applications',
                    'category': 'id_cards'
                },
                {
                    'codename': 'edit_id_cards',
                    'name': 'Edit ID Cards',
                    'description': 'Can modify ID card applications',
                    'category': 'id_cards'
                },
                {
                    'codename': 'approve_id_cards',
                    'name': 'Approve ID Cards',
                    'description': 'Can approve or reject ID card applications',
                    'category': 'id_cards'
                },
                {
                    'codename': 'print_id_cards',
                    'name': 'Print ID Cards',
                    'description': 'Can print approved ID cards',
                    'category': 'id_cards'
                },
                {
                    'codename': 'issue_id_cards',
                    'name': 'Issue ID Cards',
                    'description': 'Can mark ID cards as issued to citizens',
                    'category': 'id_cards'
                },
                
                # User Management
                {
                    'codename': 'view_users',
                    'name': 'View Users',
                    'description': 'Can view user accounts and profiles',
                    'category': 'users'
                },
                {
                    'codename': 'create_users',
                    'name': 'Create Users',
                    'description': 'Can create new user accounts',
                    'category': 'users'
                },
                {
                    'codename': 'edit_users',
                    'name': 'Edit Users',
                    'description': 'Can modify user accounts and profiles',
                    'category': 'users'
                },
                {
                    'codename': 'delete_users',
                    'name': 'Delete Users',
                    'description': 'Can delete user accounts',
                    'category': 'users'
                },
                {
                    'codename': 'manage_permissions',
                    'name': 'Manage Permissions',
                    'description': 'Can grant or revoke permissions to/from users',
                    'category': 'users'
                },
                
                # Tenant Management
                {
                    'codename': 'view_tenants',
                    'name': 'View Tenants',
                    'description': 'Can view tenant information',
                    'category': 'tenants'
                },
                {
                    'codename': 'create_tenants',
                    'name': 'Create Tenants',
                    'description': 'Can create new tenants (cities, subcities, kebeles)',
                    'category': 'tenants'
                },
                {
                    'codename': 'edit_tenants',
                    'name': 'Edit Tenants',
                    'description': 'Can modify tenant information',
                    'category': 'tenants'
                },
                {
                    'codename': 'delete_tenants',
                    'name': 'Delete Tenants',
                    'description': 'Can delete tenants',
                    'category': 'tenants'
                },
                
                # Reports and Analytics
                {
                    'codename': 'view_reports',
                    'name': 'View Reports',
                    'description': 'Can view system reports and analytics',
                    'category': 'reports'
                },
                {
                    'codename': 'export_data',
                    'name': 'Export Data',
                    'description': 'Can export data in various formats',
                    'category': 'reports'
                },
                
                # System Administration
                {
                    'codename': 'view_system_logs',
                    'name': 'View System Logs',
                    'description': 'Can view system logs and audit trails',
                    'category': 'system'
                },
                {
                    'codename': 'manage_system_settings',
                    'name': 'Manage System Settings',
                    'description': 'Can modify system-wide settings',
                    'category': 'system'
                },
            ]
            
            # Create permissions
            created_permissions = {}
            for perm_data in permissions_data:
                permission, created = Permission.objects.get_or_create(
                    codename=perm_data['codename'],
                    defaults={
                        'name': perm_data['name'],
                        'description': perm_data['description'],
                        'category': perm_data['category']
                    }
                )
                created_permissions[perm_data['codename']] = permission
                if created:
                    self.stdout.write(f"Created permission: {permission.name}")
                else:
                    self.stdout.write(f"Permission already exists: {permission.name}")
            
            # Define role-based permissions
            role_permissions = {
                UserRole.SUPERADMIN: [
                    # Superadmin gets all permissions (handled in code, not database)
                ],
                UserRole.CITY_ADMIN: [
                    'view_citizens', 'view_id_cards', 'approve_id_cards', 'print_id_cards', 'issue_id_cards',
                    'view_users', 'create_users', 'edit_users', 'delete_users', 'manage_permissions',
                    'view_tenants', 'create_tenants', 'edit_tenants', 'delete_tenants',
                    'view_reports', 'export_data', 'view_system_logs'
                ],
                UserRole.SUBCITY_ADMIN: [
                    'view_citizens', 'view_id_cards', 'approve_id_cards', 'print_id_cards', 'issue_id_cards',
                    'view_users', 'create_users', 'edit_users', 'delete_users',
                    'view_tenants', 'create_tenants', 'edit_tenants',
                    'view_reports', 'export_data'
                ],
                UserRole.KEBELE_ADMIN: [
                    'view_citizens', 'view_id_cards', 'approve_id_cards',
                    'view_users', 'create_users', 'edit_users',
                    'view_tenants', 'edit_tenants',
                    'view_reports'
                ],
                UserRole.KEBELE_LEADER: [
                    'view_citizens', 'view_id_cards', 'approve_id_cards',
                    'view_users', 'view_reports'
                ],
                UserRole.CLERK: [
                    'view_citizens', 'create_citizens', 'edit_citizens',
                    'view_id_cards', 'create_id_cards', 'edit_id_cards'
                ]
            }
            
            # Create role permissions
            for role, permission_codenames in role_permissions.items():
                for codename in permission_codenames:
                    if codename in created_permissions:
                        role_permission, created = RolePermission.objects.get_or_create(
                            role=role,
                            permission=created_permissions[codename]
                        )
                        if created:
                            self.stdout.write(f"Assigned {codename} to {role}")
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully created {len(created_permissions)} permissions and assigned role-based permissions'
                )
            )
