/**
 * Translation Debugger Component
 * Provides debugging tools for translation management in development
 */

import React, { useState, useEffect } from 'react';
import { useLocalization } from '../contexts/LocalizationContext';

const TranslationDebugger = ({ enabled = process.env.NODE_ENV === 'development' }) => {
  const {
    currentLanguage,
    isLoading,
    error,
    retryCount,
    translationStats,
    getAvailableLanguages,
    retryInitialization,
    clearCache,
    changeLanguage
  } = useLocalization();

  const [isVisible, setIsVisible] = useState(false);
  const [showMissingOnly, setShowMissingOnly] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // Don't render in production unless explicitly enabled
  if (!enabled) {
    return null;
  }

  const availableLanguages = getAvailableLanguages();

  const toggleVisibility = () => {
    setIsVisible(!isVisible);
  };

  const handleLanguageChange = (langCode) => {
    changeLanguage(langCode);
  };

  const handleRetry = () => {
    retryInitialization();
  };

  const handleClearCache = () => {
    clearCache();
    console.log('🧹 Translation cache cleared');
  };

  if (!isVisible) {
    return (
      <div style={{
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        zIndex: 9999
      }}>
        <button
          onClick={toggleVisibility}
          style={{
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '50%',
            width: '50px',
            height: '50px',
            fontSize: '20px',
            cursor: 'pointer',
            boxShadow: '0 2px 10px rgba(0,0,0,0.2)'
          }}
          title="Open Translation Debugger"
        >
          🌍
        </button>
      </div>
    );
  }

  return (
    <div style={{
      position: 'fixed',
      bottom: '20px',
      right: '20px',
      width: '400px',
      maxHeight: '600px',
      backgroundColor: 'white',
      border: '1px solid #ddd',
      borderRadius: '8px',
      boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
      zIndex: 9999,
      fontFamily: 'Arial, sans-serif',
      fontSize: '14px'
    }}>
      {/* Header */}
      <div style={{
        backgroundColor: '#007bff',
        color: 'white',
        padding: '12px 16px',
        borderRadius: '8px 8px 0 0',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <h3 style={{ margin: 0, fontSize: '16px' }}>🌍 Translation Debugger</h3>
        <button
          onClick={toggleVisibility}
          style={{
            background: 'none',
            border: 'none',
            color: 'white',
            fontSize: '18px',
            cursor: 'pointer'
          }}
        >
          ×
        </button>
      </div>

      {/* Content */}
      <div style={{ padding: '16px', maxHeight: '500px', overflowY: 'auto' }}>
        {/* Status */}
        <div style={{ marginBottom: '16px' }}>
          <h4 style={{ margin: '0 0 8px 0', color: '#333' }}>Status</h4>
          <div style={{ fontSize: '12px', color: '#666' }}>
            <div>Language: <strong>{currentLanguage}</strong></div>
            <div>Loading: <strong>{isLoading ? 'Yes' : 'No'}</strong></div>
            {error && (
              <div style={{ color: '#dc3545' }}>
                Error: <strong>{error}</strong>
                {retryCount > 0 && <span> (Retry {retryCount}/3)</span>}
              </div>
            )}
          </div>
        </div>

        {/* Translation Stats */}
        {translationStats && (
          <div style={{ marginBottom: '16px' }}>
            <h4 style={{ margin: '0 0 8px 0', color: '#333' }}>Translation Stats</h4>
            <div style={{ fontSize: '12px', color: '#666' }}>
              <div>Total: <strong>{translationStats.total}</strong></div>
              <div>Translated: <strong>{translationStats.translated}</strong></div>
              <div>Missing: <strong>{translationStats.missing}</strong></div>
              <div>Completion: <strong>{translationStats.percentage}%</strong></div>
            </div>
            <div style={{
              width: '100%',
              height: '8px',
              backgroundColor: '#e9ecef',
              borderRadius: '4px',
              marginTop: '8px',
              overflow: 'hidden'
            }}>
              <div style={{
                width: `${translationStats.percentage}%`,
                height: '100%',
                backgroundColor: translationStats.percentage > 80 ? '#28a745' : 
                                translationStats.percentage > 50 ? '#ffc107' : '#dc3545',
                transition: 'width 0.3s ease'
              }} />
            </div>
          </div>
        )}

        {/* Language Switcher */}
        <div style={{ marginBottom: '16px' }}>
          <h4 style={{ margin: '0 0 8px 0', color: '#333' }}>Quick Language Switch</h4>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
            {availableLanguages.map(lang => (
              <button
                key={lang.code}
                onClick={() => handleLanguageChange(lang.code)}
                style={{
                  padding: '4px 8px',
                  fontSize: '12px',
                  border: '1px solid #ddd',
                  borderRadius: '4px',
                  backgroundColor: lang.code === currentLanguage ? '#007bff' : 'white',
                  color: lang.code === currentLanguage ? 'white' : '#333',
                  cursor: 'pointer'
                }}
                title={lang.name}
              >
                {lang.code.toUpperCase()}
              </button>
            ))}
          </div>
        </div>

        {/* Actions */}
        <div style={{ marginBottom: '16px' }}>
          <h4 style={{ margin: '0 0 8px 0', color: '#333' }}>Actions</h4>
          <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
            <button
              onClick={handleRetry}
              disabled={isLoading}
              style={{
                padding: '6px 12px',
                fontSize: '12px',
                border: '1px solid #28a745',
                borderRadius: '4px',
                backgroundColor: 'white',
                color: '#28a745',
                cursor: isLoading ? 'not-allowed' : 'pointer',
                opacity: isLoading ? 0.6 : 1
              }}
            >
              🔄 Retry Init
            </button>
            <button
              onClick={handleClearCache}
              style={{
                padding: '6px 12px',
                fontSize: '12px',
                border: '1px solid #ffc107',
                borderRadius: '4px',
                backgroundColor: 'white',
                color: '#ffc107',
                cursor: 'pointer'
              }}
            >
              🧹 Clear Cache
            </button>
          </div>
        </div>

        {/* Debug Info */}
        <div style={{ fontSize: '11px', color: '#999', borderTop: '1px solid #eee', paddingTop: '8px' }}>
          <div>Retry Count: {retryCount}</div>
          <div>Cache: {typeof Map !== 'undefined' ? 'Enabled' : 'Disabled'}</div>
          <div>Environment: {process.env.NODE_ENV}</div>
        </div>
      </div>
    </div>
  );
};

export default TranslationDebugger;
