# Generated manually to migrate existing group membership data

from django.db import migrations


def migrate_group_membership_data(apps, schema_editor):
    """
    Migrate existing group membership data to the new schema-safe format.
    This must run before the foreign key fields are removed.
    """
    GroupMembership = apps.get_model('users', 'GroupMembership')
    
    # Update existing records with user email and tenant information
    for membership in GroupMembership.objects.all():
        if hasattr(membership, 'user') and membership.user:
            # Update user_email
            if not membership.user_email:
                membership.user_email = membership.user.email
            
            # Update tenant information
            if membership.user.tenant:
                membership.user_tenant_id = membership.user.tenant.id
                membership.user_tenant_schema = membership.user.tenant.schema_name
            
            # Update assigned_by_email
            if hasattr(membership, 'assigned_by') and membership.assigned_by:
                if not membership.assigned_by_email:
                    membership.assigned_by_email = membership.assigned_by.email
            
            membership.save()


def reverse_migrate_group_membership_data(apps, schema_editor):
    """
    Reverse migration - this is not fully reversible since we're changing
    the fundamental structure, but we can clear the new fields.
    """
    GroupMembership = apps.get_model('users', 'GroupMembership')
    
    # Clear the new fields
    GroupMembership.objects.update(
        user_email='',
        user_tenant_id=None,
        user_tenant_schema='',
        assigned_by_email=''
    )


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0009_update_group_membership_model'),
    ]

    operations = [
        migrations.RunPython(
            migrate_group_membership_data,
            reverse_migrate_group_membership_data,
        ),
    ]
