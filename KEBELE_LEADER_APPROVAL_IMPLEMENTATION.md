# Kebele Leader Approval Workflow Implementation

## Overview
This document outlines the implementation of the kebele leader approval workflow for ID cards in the GoID system. The workflow allows clerks to submit ID cards for approval, kebele leaders to review and approve/reject them, and applies visual patterns to approved ID cards.

## Features Implemented

### 1. Backend Changes

#### Database Schema Updates
- **New IDCard model fields:**
  - `approval_pattern`: <PERSON><PERSON> applied when approved (diagonal_stripes, watermark, border_pattern, corner_seal)
  - `approval_comment`: Comment from kebele leader during approval
  - `submitted_for_approval_at`: Timestamp when submitted for approval
  - `approved_at`: Timestamp when approved

#### New API Endpoints
- **Approval Action Endpoint:** `/api/tenants/{tenant_id}/idcards/{id}/approval_action/`
  - **Actions supported:**
    - `submit_for_approval`: Clerk submits ID card for approval
    - `approve`: Kebele leader approves with pattern selection
    - `reject`: Kebele leader rejects with reason

#### Permission Updates
- Added `kebele_leader` role support for ID card approval
- Updated `CanManageIDCards` permission to include kebele leaders
- Created `CanApproveIDCards` permission class specifically for approval actions

#### Serializers
- **IDCardApprovalSerializer:** Handles approval workflow actions
- **Updated IDCardSerializer:** Includes new approval fields
- **Validation:** Ensures approval pattern is required when approving

### 2. Frontend Changes

#### User Interface Updates
- **ID Card View Page:**
  - "Send for Approval" button for clerks (draft status)
  - "Approve" and "Reject" buttons for kebele leaders (pending status)
  - Approval pattern overlay on approved ID cards
  - Approval information display (approver, date, pattern, comments)

- **ID Card List Page:**
  - "Send for Approval" action button in table rows
  - Updated status chips and statistics

#### Visual Approval Patterns
- **Diagonal Stripes:** Repeating diagonal pattern overlay
- **Watermark:** Radial gradient with "APPROVED" text
- **Border Pattern:** Dashed border around the card
- **Corner Seal:** Checkmark seal in top-right corner

#### Permission Integration
- Role-based button visibility
- Proper permission checks for approval actions

### 3. Workflow Process

#### Step 1: Clerk Submits for Approval
1. Clerk creates ID card (status: `draft`)
2. Clerk clicks "Send for Approval"
3. Status changes to `pending_approval`
4. `submitted_for_approval_at` timestamp is set

#### Step 2: Kebele Leader Reviews
1. Kebele leader sees pending ID cards
2. Reviews citizen documents and information
3. Can approve or reject with comments

#### Step 3: Approval/Rejection
- **If Approved:**
  - Status changes to `approved`
  - `approved_by` and `approved_at` are set
  - `approval_pattern` is applied
  - Visual pattern overlay appears on ID card
  
- **If Rejected:**
  - Status changes to `rejected`
  - `approval_comment` contains rejection reason
  - Clerk can see rejection reason and make corrections

## File Changes Summary

### Backend Files Modified/Created:
1. `backend/idcards/models.py` - Added approval fields to IDCard model
2. `backend/idcards/serializers.py` - Added IDCardApprovalSerializer
3. `backend/idcards/views.py` - Added approval_action endpoint
4. `backend/tenants/views/tenant_idcard_views.py` - Added tenant-specific approval endpoint
5. `backend/common/permissions.py` - Updated permissions for kebele leaders
6. `backend/idcards/migrations/0007_add_approval_fields.py` - Database migration

### Frontend Files Modified:
1. `frontend/src/pages/idcards/IDCardView.jsx` - Added approval UI and pattern overlay
2. `frontend/src/pages/idcards/IDCardList.jsx` - Added approval action buttons
3. `frontend/src/hooks/usePermissions.js` - Added kebele_leader permissions
4. `frontend/src/utils/permissions.js` - Updated approval permission checks

## Usage Instructions

### For Clerks:
1. Create ID card for citizen
2. Click "Send for Approval" button
3. Wait for kebele leader approval
4. If rejected, review comments and make corrections

### For Kebele Leaders:
1. View pending ID cards in the system
2. Review citizen information and documents
3. Click "Approve" to approve with pattern selection
4. Click "Reject" to reject with reason
5. Approved cards will show visual approval pattern

## Testing

A test script `test_approval_workflow.py` has been created to test the approval endpoints. To run tests:

1. Start the Django development server
2. Run: `python test_approval_workflow.py`

## Migration Instructions

1. Apply database migrations:
   ```bash
   cd backend
   python manage.py migrate
   ```

2. Restart the Django server to load new permissions

3. Test the workflow with different user roles

## Security Considerations

- Only clerks can submit ID cards for approval
- Only kebele leaders and admins can approve/reject ID cards
- Proper tenant isolation is maintained
- All actions are logged in the workflow system
- Permission checks are enforced at both API and UI levels

## Future Enhancements

- Email notifications for approval requests
- Bulk approval functionality
- Custom approval pattern designs
- Approval deadline tracking
- Advanced workflow reporting
