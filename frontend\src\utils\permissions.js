/**
 * Utility functions for checking user permissions
 */

/**
 * Check if the user has the specified role
 * @param {Object} user - The user object
 * @param {Array|String} roles - The role(s) to check
 * @returns {Boolean} - Whether the user has the specified role
 */
export const hasRole = (user, roles) => {
  if (!user) return false;

  // If roles is a string, convert it to an array
  const roleArray = Array.isArray(roles) ? roles : [roles];

  // Check if the user is a superuser (has all permissions)
  if (user.is_superuser) return true;

  // Check if the user's role is in the specified roles
  return roleArray.includes(user.role);
};

/**
 * Check if the user can manage citizens
 * @param {Object} user - The user object
 * @returns {Boolean} - Whether the user can manage citizens
 */
export const canManageCitizens = (user) => {
  // Clerks can register citizens
  // All roles can view citizens
  return hasRole(user, ['clerk', 'kebele_admin', 'subcity_admin', 'city_admin', 'superadmin']);
};

/**
 * Check if the user can create citizens
 * @param {Object} user - The user object
 * @returns {Boolean} - Whether the user can create citizens
 */
export const canCreateCitizens = (user) => {
  // Only clerks can register citizens
  return hasRole(user, ['clerk']);
};

/**
 * Check if the user can edit citizens
 * @param {Object} user - The user object
 * @returns {Boolean} - Whether the user can edit citizens
 */
export const canEditCitizens = (user) => {
  // Only clerks and kebele admins can edit citizens
  return hasRole(user, ['clerk', 'kebele_admin']);
};

/**
 * Check if the user can manage ID cards
 * @param {Object} user - The user object
 * @returns {Boolean} - Whether the user can manage ID cards
 */
export const canManageIDCards = (user) => {
  // All roles can view ID cards
  return hasRole(user, ['clerk', 'kebele_admin', 'subcity_admin', 'city_admin', 'superadmin']);
};

/**
 * Check if the user can create ID cards
 * @param {Object} user - The user object
 * @returns {Boolean} - Whether the user can create ID cards
 */
export const canCreateIDCards = (user) => {
  // Only clerks can generate ID cards
  return hasRole(user, ['clerk']);
};

/**
 * Check if the user can approve ID cards
 * @param {Object} user - The user object
 * @returns {Boolean} - Whether the user can approve ID cards
 */
export const canApproveIDCards = (user) => {
  // Kebele leaders, kebele admins and higher can approve ID cards
  return hasRole(user, ['kebele_leader', 'kebele_admin', 'subcity_admin', 'city_admin', 'superadmin']);
};

/**
 * Check if the user can print ID cards
 * @param {Object} user - The user object
 * @returns {Boolean} - Whether the user can print ID cards
 */
export const canPrintIDCards = (user) => {
  // Subcity admins and higher can print ID cards
  return hasRole(user, ['subcity_admin', 'city_admin', 'superadmin']);
};

/**
 * Check if the user can manage users
 * @param {Object} user - The user object
 * @returns {Boolean} - Whether the user can manage users
 */
export const canManageUsers = (user) => {
  // Only admins can manage users
  return hasRole(user, ['kebele_admin', 'subcity_admin', 'city_admin', 'superadmin']);
};

/**
 * Check if the user can create users with the specified role
 * @param {Object} user - The user object
 * @param {String} role - The role to check
 * @returns {Boolean} - Whether the user can create users with the specified role
 */
export const canCreateUserWithRole = (user, role) => {
  if (!user) return false;

  // Superusers and superadmins can create users with any role
  if (user.is_superuser || user.role === 'superadmin') return true;

  // City admins can create subcity admins
  if (user.role === 'city_admin' && role === 'subcity_admin') return true;

  // Subcity admins can create kebele admins
  if (user.role === 'subcity_admin' && role === 'kebele_admin') return true;

  // Kebele admins can create clerks
  if (user.role === 'kebele_admin' && role === 'clerk') return true;

  return false;
};

/**
 * Check if the user can manage tenants
 * @param {Object} user - The user object
 * @returns {Boolean} - Whether the user can manage tenants
 */
export const canManageTenants = (user) => {
  // Only city admins, subcity admins, and superadmins can manage tenants
  return hasRole(user, ['city_admin', 'subcity_admin', 'superadmin']);
};

/**
 * Check if the user can create tenants with the specified type
 * @param {Object} user - The user object
 * @param {String} tenantType - The tenant type to check
 * @returns {Boolean} - Whether the user can create tenants with the specified type
 */
export const canCreateTenantWithType = (user, tenantType) => {
  if (!user) return false;

  // Superusers and superadmins can create tenants of any type
  if (user.is_superuser || user.role === 'superadmin') return true;

  // City admins can create subcity tenants
  if (user.role === 'city_admin' && tenantType === 'subcity') return true;

  // Subcity admins can create kebele tenants
  if (user.role === 'subcity_admin' && tenantType === 'kebele') return true;

  return false;
};

/**
 * Check if the user can view reports
 * @param {Object} user - The user object
 * @returns {Boolean} - Whether the user can view reports
 */
export const canViewReports = (user) => {
  // Only kebele admins and higher can view reports
  return hasRole(user, ['kebele_admin', 'subcity_admin', 'city_admin', 'superadmin']);
};
