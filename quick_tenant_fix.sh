#!/bin/bash

# Quick Tenant Fix Script
# This script applies the immediate fix for the empty tenant response

echo "🚀 Quick Tenant Fix"
echo "=================="

echo "📍 The issue was found in TenantViewSet.get_queryset() method"
echo "Problem: User role check was failing, causing empty results"
echo "Solution: Fixed role attribute access with getattr() and added debug logging"
echo ""

# Step 1: Restart backend to apply the fix
echo "📍 Step 1: Restarting backend to apply the fix..."
docker-compose restart backend
sleep 30

# Step 2: Quick test
echo ""
echo "📍 Step 2: Quick API test..."

# Get token and test
AUTH_RESPONSE=$(curl -s -X POST http://************:8000/api/auth/token/ \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}')

if echo "$AUTH_RESPONSE" | grep -q "access"; then
    TOKEN=$(echo "$AUTH_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin)['access'])" 2>/dev/null)
    
    if [ ! -z "$TOKEN" ]; then
        TENANT_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" http://************:8000/api/tenants/)
        
        echo "API Response:"
        echo "$TENANT_RESPONSE"
        
        if echo "$TENANT_RESPONSE" | grep -q '"name"'; then
            echo ""
            echo "✅ SUCCESS: API is now returning tenant data!"
        else
            echo ""
            echo "❌ Still not working - check debug logs"
        fi
    fi
fi

# Step 3: Show debug logs
echo ""
echo "📍 Step 3: Backend debug logs..."
docker-compose logs --tail=10 backend | grep -E "TenantViewSet|Superuser|tenant" || echo "No debug logs yet - make an API call"

echo ""
echo "✅ Quick fix applied!"
echo ""
echo "🧪 Test now:"
echo "1. Clear browser cache: Ctrl+Shift+R"
echo "2. Open: http://************:3000/tenants"
echo "3. Should see tenant list"
echo ""
echo "💡 If still empty:"
echo "- Check debug logs above"
echo "- Run: python debug_empty_tenant_response.py"
echo "- Verify admin user is superuser in Django admin"
