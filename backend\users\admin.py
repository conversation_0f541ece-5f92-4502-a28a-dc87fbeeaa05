from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.translation import gettext_lazy as _
from django.db import transaction
from django_tenants.utils import schema_context
from .models import User


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    list_display = ('email', 'username', 'role', 'tenant', 'is_active', 'is_staff')
    list_filter = ('role', 'is_active', 'is_staff', 'tenant')
    search_fields = ('email', 'username', 'first_name', 'last_name')
    ordering = ('email',)

    fieldsets = (
        (None, {'fields': ('email', 'password')}),
        (_('Personal info'), {'fields': ('username', 'first_name', 'last_name', 'phone_number', 'profile_picture')}),
        (_('Permissions'), {'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')}),
        (_('Role & Tenant'), {'fields': ('role', 'tenant')}),
        (_('Important dates'), {'fields': ('last_login', 'date_joined')}),
    )

    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'username', 'password1', 'password2', 'role', 'tenant'),
        }),
    )

    def delete_model(self, request, obj):
        """
        Custom delete method to handle related objects in tenant schemas.
        This prevents the "relation citizen_clearance_requests does not exist" error.
        """
        with transaction.atomic():
            # Clean up related clearance requests in all tenant schemas
            self._cleanup_tenant_related_objects(obj)

            # Now delete the user
            super().delete_model(request, obj)

    def delete_queryset(self, request, queryset):
        """
        Custom bulk delete method to handle related objects in tenant schemas.
        """
        with transaction.atomic():
            # Clean up related objects for each user
            for user in queryset:
                self._cleanup_tenant_related_objects(user)

            # Now delete the users
            super().delete_queryset(request, queryset)

    def _cleanup_tenant_related_objects(self, user):
        """
        Clean up related objects in tenant schemas before deleting the user.
        """
        try:
            from tenants.models import Tenant
            from workflows.models import CitizenClearanceRequest

            # Get all kebele tenants where clearance requests might exist
            kebele_tenants = Tenant.objects.filter(type='kebele')

            for tenant in kebele_tenants:
                try:
                    with schema_context(tenant.schema_name):
                        # Delete clearance requests made by this user
                        clearance_requests = CitizenClearanceRequest.objects.filter(
                            requested_by=user
                        )
                        if clearance_requests.exists():
                            count = clearance_requests.count()
                            clearance_requests.delete()
                            print(f"Deleted {count} clearance requests from {tenant.schema_name} for user {user.email}")

                        # Delete clearance requests reviewed by this user
                        reviewed_requests = CitizenClearanceRequest.objects.filter(
                            reviewed_by=user
                        )
                        if reviewed_requests.exists():
                            count = reviewed_requests.count()
                            reviewed_requests.delete()
                            print(f"Deleted {count} reviewed clearance requests from {tenant.schema_name} for user {user.email}")

                        # Delete clearance requests issued by this user
                        issued_requests = CitizenClearanceRequest.objects.filter(
                            issued_by=user
                        )
                        if issued_requests.exists():
                            count = issued_requests.count()
                            issued_requests.delete()
                            print(f"Deleted {count} issued clearance requests from {tenant.schema_name} for user {user.email}")

                except Exception as e:
                    # Log the error but continue with deletion
                    print(f"Warning: Could not clean up clearance requests in {tenant.schema_name}: {e}")

        except Exception as e:
            # Log the error but continue with deletion
            print(f"Warning: Could not clean up tenant-related objects for user {user.email}: {e}")
