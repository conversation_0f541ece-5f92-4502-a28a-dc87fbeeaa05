#!/usr/bin/env python3
"""
Wait for database to be ready before starting Django
"""
import os
import sys
import time
import psycopg2
from urllib.parse import urlparse

def wait_for_db():
    """Wait for database to be ready"""
    database_url = os.environ.get('DATABASE_URL')
    if not database_url:
        print("❌ DATABASE_URL not found in environment")
        sys.exit(1)
    
    # Parse database URL
    parsed = urlparse(database_url)
    db_config = {
        'host': parsed.hostname,
        'port': parsed.port or 5432,
        'user': parsed.username,
        'password': parsed.password,
        'database': parsed.path[1:]  # Remove leading slash
    }
    
    print(f"🔍 Waiting for database at {db_config['host']}:{db_config['port']}")
    print(f"📊 Database: {db_config['database']}")
    print(f"👤 User: {db_config['user']}")
    
    max_retries = 30
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            # Try to connect to database
            conn = psycopg2.connect(**db_config)
            conn.close()
            print("✅ Database is ready!")
            return True
            
        except psycopg2.OperationalError as e:
            retry_count += 1
            print(f"⏳ Database not ready (attempt {retry_count}/{max_retries}): {e}")
            time.sleep(2)
            
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            sys.exit(1)
    
    print(f"❌ Database not ready after {max_retries} attempts")
    sys.exit(1)

if __name__ == "__main__":
    wait_for_db()
