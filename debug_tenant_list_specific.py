#!/usr/bin/env python3
"""
Specific diagnostic for tenant list endpoint that's not fetching.
"""

import requests
import json

def debug_tenant_list_specific():
    """Debug the specific tenant list endpoint issue."""
    
    base_url = "http://10.139.8.141:8000"
    
    print("🔍 Debugging Tenant List Specific Issue")
    print("=" * 45)
    
    # Step 1: Verify authentication works
    print("📍 Step 1: Testing authentication...")
    
    try:
        auth_response = requests.post(
            f"{base_url}/api/auth/token/",
            json={"email": "<EMAIL>", "password": "admin123"},
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"Auth status: {auth_response.status_code}")
        
        if auth_response.status_code == 200:
            auth_data = auth_response.json()
            access_token = auth_data.get('access')
            print("✅ Authentication successful")
            print(f"Token length: {len(access_token) if access_token else 0}")
        else:
            print(f"❌ Authentication failed: {auth_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return False
    
    # Step 2: Test tenant endpoint step by step
    print(f"\n📍 Step 2: Testing tenant endpoint step by step...")
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    # Test without auth first
    print("Testing tenant endpoint WITHOUT auth...")
    try:
        response = requests.get(f"{base_url}/api/tenants/", timeout=10)
        print(f"No auth status: {response.status_code}")
        print(f"No auth response: {response.text[:200]}")
    except Exception as e:
        print(f"No auth error: {e}")
    
    # Test with auth
    print("\nTesting tenant endpoint WITH auth...")
    try:
        response = requests.get(f"{base_url}/api/tenants/", headers=headers, timeout=10)
        print(f"With auth status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        print(f"Response size: {len(response.content)} bytes")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("✅ Tenant endpoint responding with JSON")
                print(f"Response type: {type(data)}")
                
                if isinstance(data, list):
                    print(f"Direct array response with {len(data)} items")
                    if data:
                        print(f"First tenant: {data[0]}")
                    else:
                        print("⚠️  Empty array - no tenants found")
                        
                elif isinstance(data, dict):
                    if 'results' in data:
                        results = data.get('results', [])
                        print(f"Paginated response:")
                        print(f"  Count: {data.get('count', 'unknown')}")
                        print(f"  Results: {len(results)} items")
                        if results:
                            print(f"  First tenant: {results[0]}")
                        else:
                            print("  ⚠️  Empty results - no tenants found")
                    else:
                        print(f"Dict response: {data}")
                        
                return True
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error: {e}")
                print(f"Raw response: {response.text}")
                return False
        else:
            print(f"❌ Tenant endpoint failed with status {response.status_code}")
            print(f"Error response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ ERR_EMPTY_RESPONSE - Connection dropped")
        return False
    except Exception as e:
        print(f"❌ Tenant endpoint error: {e}")
        return False
    
    # Step 3: Compare with Django admin data
    print(f"\n📍 Step 3: Checking what Django admin shows...")
    
    try:
        admin_response = requests.get(f"{base_url}/admin/tenants/tenant/", timeout=10)
        print(f"Django admin status: {admin_response.status_code}")
        
        if admin_response.status_code == 200:
            # Look for tenant data in the HTML
            admin_html = admin_response.text
            if "No tenants" in admin_html or "0 tenants" in admin_html:
                print("⚠️  Django admin shows no tenants")
            elif "tenant" in admin_html.lower():
                print("✅ Django admin shows tenant data exists")
            else:
                print("? Django admin response unclear")
        else:
            print("❌ Cannot access Django admin")
            
    except Exception as e:
        print(f"❌ Django admin check failed: {e}")
    
    # Step 4: Test other similar endpoints
    print(f"\n📍 Step 4: Testing other endpoints for comparison...")
    
    test_endpoints = [
        "/api/tenants/subcities/",
        "/api/tenants/kebeles/", 
        "/api/shared/countries/",
        "/api/shared/regions/"
    ]
    
    for endpoint in test_endpoints:
        try:
            if endpoint.startswith("/api/shared/"):
                # Shared endpoints might not need auth
                response = requests.get(f"{base_url}{endpoint}", timeout=10)
            else:
                response = requests.get(f"{base_url}{endpoint}", headers=headers, timeout=10)
                
            print(f"{endpoint}: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    count = len(data) if isinstance(data, list) else len(data.get('results', []))
                    print(f"  ✅ Success - {count} items")
                except:
                    print(f"  ✅ Success - non-JSON response")
            else:
                print(f"  ❌ Failed - {response.text[:50]}")
                
        except Exception as e:
            print(f"  ❌ Error - {e}")
    
    # Step 5: Test exact frontend request
    print(f"\n📍 Step 5: Simulating exact frontend request...")
    
    frontend_headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Origin": "http://10.139.8.141:3000",
        "Referer": "http://10.139.8.141:3000/tenants"
    }
    
    try:
        response = requests.get(
            f"{base_url}/api/tenants/",
            headers=frontend_headers,
            timeout=10
        )
        
        print(f"Frontend simulation status: {response.status_code}")
        print(f"CORS headers in response:")
        cors_headers = {k: v for k, v in response.headers.items() if 'access-control' in k.lower()}
        print(f"  {cors_headers}")
        
        if response.status_code == 200:
            print("✅ Frontend simulation successful")
            try:
                data = response.json()
                print(f"Data structure: {type(data)}")
                if isinstance(data, list):
                    print(f"Array with {len(data)} items")
                elif isinstance(data, dict) and 'results' in data:
                    print(f"Paginated with {len(data.get('results', []))} items")
            except:
                print("Non-JSON response")
        else:
            print(f"❌ Frontend simulation failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Frontend simulation error: {e}")

if __name__ == "__main__":
    debug_tenant_list_specific()
    
    print("\n" + "=" * 45)
    print("🔧 Specific Solutions for Tenant List:")
    print("")
    print("1. If authentication works but tenant endpoint fails:")
    print("   - Check TenantViewSet permissions")
    print("   - Verify user has superuser status")
    print("   - Check middleware routing")
    print("")
    print("2. If endpoint returns empty data:")
    print("   - Check if tenants exist in database")
    print("   - Verify queryset filtering in TenantViewSet")
    print("   - Check user permissions for viewing tenants")
    print("")
    print("3. If getting 403/401 errors:")
    print("   - User might not have CanManageTenants permission")
    print("   - Check if user.is_superuser is True")
    print("   - Verify JWT token contains correct user info")
    print("")
    print("4. If getting ERR_EMPTY_RESPONSE:")
    print("   - Backend crashing on this specific endpoint")
    print("   - Check backend logs: docker-compose logs backend")
    print("   - Middleware might be interfering")
    print("")
    print("💡 Quick fixes to try:")
    print("- docker-compose exec backend python manage.py shell")
    print("  >>> from tenants.models import Tenant")
    print("  >>> print(Tenant.objects.all())")
    print("- Check user: User.objects.get(email='<EMAIL>').is_superuser")
    print("- Test API directly in Django admin or shell")
