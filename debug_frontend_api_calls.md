# 🔍 Debug Frontend API Calls

Since you can see tenants in Django admin but not in the frontend, the issue is with the API endpoint or frontend requests.

## Step 1: Test API Endpoint Directly

Run this script to test the API:
```bash
python test_tenant_api_directly.py
```

## Step 2: Check Frontend Network Requests

1. **Open Browser Developer Tools** (F12)
2. **Go to Network Tab**
3. **Navigate to** `http://************:3000/tenants`
4. **Look for the API request** to `/api/tenants/`

### What to Check:

#### **Request Details:**
- ✅ **URL**: Should be `http://************:8000/api/tenants/`
- ✅ **Method**: Should be `GET`
- ✅ **Headers**: Should include `Authorization: Bearer <token>`
- ✅ **Status**: Should be `200 OK` (not 404, 500, or ERR_EMPTY_RESPONSE)

#### **Response Details:**
- ✅ **Content-Type**: Should be `application/json`
- ✅ **Body**: Should contain tenant data
- ✅ **Size**: Should be > 0 bytes

## Step 3: Common Issues and Solutions

### Issue 1: ERR_EMPTY_RESPONSE
**Cause**: Backend not responding
**Solution**: 
```bash
docker-compose restart backend
curl -I http://************:8000/admin/
```

### Issue 2: 404 Not Found
**Cause**: URL routing issue
**Check**: 
- Middleware patterns
- URL configuration
- API endpoint registration

### Issue 3: 403 Forbidden
**Cause**: Permission issue
**Check**:
- User authentication
- TenantViewSet permissions
- Superuser status

### Issue 4: 401 Unauthorized
**Cause**: Authentication issue
**Check**:
- JWT token validity
- Token format in headers
- User credentials

### Issue 5: CORS Error
**Cause**: Cross-origin request blocked
**Check**:
- CORS settings in Django
- Frontend making requests to correct URL

## Step 4: Manual API Test

Test the API manually in browser console:

```javascript
// 1. First login to get token
fetch('http://************:8000/api/auth/token/', {
  method: 'POST',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'admin123'
  })
})
.then(r => r.json())
.then(data => {
  console.log('Auth response:', data);
  
  // 2. Use token to get tenants
  if (data.access) {
    return fetch('http://************:8000/api/tenants/', {
      headers: {
        'Authorization': `Bearer ${data.access}`,
        'Content-Type': 'application/json'
      }
    });
  }
})
.then(r => r.json())
.then(data => {
  console.log('Tenants response:', data);
})
.catch(err => {
  console.error('Error:', err);
});
```

## Step 5: Check Frontend Code

Verify the frontend is making correct requests:

### Check TenantList.jsx:
- Line 66: `const response = await axios.get('/api/tenants/');`
- Verify axios is configured with correct base URL
- Check if authentication headers are included

### Check axios configuration:
- Base URL should be `http://************:8000`
- Should include Authorization header
- Should handle authentication properly

## Step 6: Compare Working vs Broken

### Working (Django Admin):
- URL: `http://************:8000/admin/tenants/tenant/`
- Authentication: Django session
- Shows all tenants ✅

### Broken (Frontend API):
- URL: `http://************:8000/api/tenants/`
- Authentication: JWT Bearer token
- Shows ERR_EMPTY_RESPONSE ❌

## Expected Results After Fix:

1. **API Test Script**: Should return 200 OK with tenant data
2. **Browser Network Tab**: Should show successful API request
3. **Frontend**: Should display list of tenants
4. **No Errors**: No ERR_EMPTY_RESPONSE or 404 errors

## Quick Fixes to Try:

```bash
# 1. Restart backend
docker-compose restart backend

# 2. Test API directly
python test_tenant_api_directly.py

# 3. Check backend logs
docker-compose logs backend | grep ERROR

# 4. Test authentication
curl -X POST http://************:8000/api/auth/token/ \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'
```

The key is to identify exactly where the request is failing - authentication, routing, permissions, or response handling.
