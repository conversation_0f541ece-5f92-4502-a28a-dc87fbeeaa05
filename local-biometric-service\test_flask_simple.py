#!/usr/bin/env python3
"""
Simple Flask test to isolate the issue
"""

import logging
from flask import Flask, jsonify
from flask_cors import CORS

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

@app.route('/', methods=['GET'])
def index():
    return "Flask is working!"

@app.route('/api/test', methods=['GET'])
def test():
    return jsonify({'status': 'working', 'message': 'Flask API is responding'})

if __name__ == '__main__':
    try:
        logger.info("🧪 Testing Flask startup...")
        logger.info("🌐 Starting on http://localhost:8001")
        logger.info("✅ Keep this window open!")
        
        app.run(host='0.0.0.0', port=8001, debug=False, threaded=True)
        
    except Exception as e:
        logger.error(f"❌ Flask test failed: {e}")
        input("Press Enter to exit...")
    finally:
        logger.info("Flask test stopped")
