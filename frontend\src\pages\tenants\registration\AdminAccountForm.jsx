import { useState } from 'react';
import {
  Box,
  Typo<PERSON>,
  Grid,
  TextField,
  InputAdornment,
  IconButton,
  FormHelperText,
  Divider,
} from '@mui/material';
import {
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
} from '@mui/icons-material';

const AdminAccountForm = ({ data, onChange, tenantData, tenantType }) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    onChange({ ...data, [name]: value });
  };

  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const handleClickShowConfirmPassword = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  const passwordsMatch = data.password === data.confirmPassword;
  const passwordError = data.password && data.confirmPassword && !passwordsMatch;

  return (
    <Box>
      <Typography variant="h6" gutterBottom fontWeight="bold">
        Administrator Account
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Create an administrator account for this tenant. This user will have full access to manage the tenant.
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Divider sx={{ my: 2 }}>Personal Information</Divider>
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            required
            id="firstName"
            name="firstName"
            label="First Name"
            value={data.firstName || ''}
            onChange={handleChange}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            required
            id="lastName"
            name="lastName"
            label="Last Name"
            value={data.lastName || ''}
            onChange={handleChange}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            required
            id="email"
            name="email"
            label="Email Address"
            type="email"
            value={data.email || ''}
            onChange={handleChange}
            placeholder="<EMAIL>"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            id="phone"
            name="phone"
            label="Phone Number"
            value={data.phone || ''}
            onChange={handleChange}
            placeholder="+251 91 234 5678"
          />
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: 2 }}>Account Information</Divider>
        </Grid>

        <Grid item xs={12}>
          <TextField
            fullWidth
            required
            id="username"
            name="username"
            label="Username"
            value={data.username || ''}
            onChange={handleChange}
            placeholder="admin_username"
            helperText={tenantData?.domain_name
              ? `Username will be used as ${data.username || 'username'}@${tenantData.domain_name} for login`
              : "Username must be unique and will be used for login"}
            InputProps={tenantData?.domain_name ? {
              endAdornment: (
                <InputAdornment position="end">
                  @{tenantData.domain_name}
                </InputAdornment>
              ),
            } : undefined}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            required
            id="password"
            name="password"
            label="Password"
            type={showPassword ? 'text' : 'password'}
            value={data.password || ''}
            onChange={handleChange}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    aria-label="toggle password visibility"
                    onClick={handleClickShowPassword}
                    edge="end"
                  >
                    {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            required
            id="confirmPassword"
            name="confirmPassword"
            label="Confirm Password"
            type={showConfirmPassword ? 'text' : 'password'}
            value={data.confirmPassword || ''}
            onChange={handleChange}
            error={passwordError}
            helperText={passwordError ? "Passwords don't match" : ""}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    aria-label="toggle password visibility"
                    onClick={handleClickShowConfirmPassword}
                    edge="end"
                  >
                    {showConfirmPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </Grid>

        <Grid item xs={12}>
          <FormHelperText>
            Password should be at least 8 characters long and include a mix of letters, numbers, and special characters.
          </FormHelperText>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AdminAccountForm;
