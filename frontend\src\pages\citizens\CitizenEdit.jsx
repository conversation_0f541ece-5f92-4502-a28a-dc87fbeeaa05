import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useLocalization } from '../../contexts/LocalizationContext';
import {
  Box,
  Button,
  Card,
  CardContent,
  Typography,
  TextField,
  Grid,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  Alert,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material';
import { ArrowBack as BackIcon } from '@mui/icons-material';
import { useFormik } from 'formik';
import * as yup from 'yup';
import FixedEthiopianDateAdapter from '../../utils/FixedEthiopianDateAdapter';
import EthiopianCalendarWidget from '../../components/common/EthiopianCalendarWidget';
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import axios from '../../utils/axios';

// Mock data for citizens
const mockCitizens = [
  {
    id: 1,
    first_name: '<PERSON><PERSON>',
    last_name: '<PERSON><PERSON><PERSON>',
    gender: 'male',
    date_of_birth: '1985-05-15',
    phone_number: '+251911234567',
    address: 'Bole, Addis Ababa',
    nationality: 'Ethiopian',
    occupation: 'Teacher',
    emergency_contact_name: 'Almaz Kebede',
    emergency_contact_phone: '+251922345678',
    has_id_card: true,
    created_at: '2023-11-10T12:00:00Z',
  },
  {
    id: 2,
    first_name: 'Sara',
    last_name: 'Mohammed',
    gender: 'female',
    date_of_birth: '1990-08-22',
    phone_number: '+251922345678',
    address: 'Bole, Addis Ababa',
    nationality: 'Ethiopian',
    occupation: 'Doctor',
    emergency_contact_name: 'Ahmed Mohammed',
    emergency_contact_phone: '+251933456789',
    has_id_card: true,
    created_at: '2023-11-11T12:00:00Z',
  },
  {
    id: 3,
    first_name: 'Daniel',
    last_name: 'Tesfaye',
    gender: 'male',
    date_of_birth: '1978-12-10',
    phone_number: '+251933456789',
    address: 'Bole, Addis Ababa',
    nationality: 'Ethiopian',
    occupation: 'Engineer',
    emergency_contact_name: 'Tigist Tesfaye',
    emergency_contact_phone: '+251944567890',
    has_id_card: false,
    created_at: '2023-11-12T12:00:00Z',
  },
];

const validationSchema = yup.object({
  first_name: yup.string().required('First name is required'),
  last_name: yup.string().required('Last name is required'),
  gender: yup.string().required('Gender is required'),
  date_of_birth: yup.date().required('Date of birth is required'),
  phone_number: yup.string().required('Phone number is required'),
  address: yup.string().required('Address is required'),
  nationality: yup.string().required('Nationality is required'),
  occupation: yup.string().nullable(),
  emergency_contact_name: yup.string().nullable(),
  emergency_contact_phone: yup.string().nullable(),
});

const CitizenEdit = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { t } = useLocalization();
  const [citizen, setCitizen] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  useEffect(() => {
    // In a real implementation, this would fetch data from the API
    // For now, we'll just use mock data
    const timer = setTimeout(() => {
      const foundCitizen = mockCitizens.find(c => c.id === parseInt(id));
      if (foundCitizen) {
        setCitizen(foundCitizen);
      } else {
        setError('Citizen not found');
      }
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [id]);

  const formik = useFormik({
    initialValues: {
      first_name: '',
      last_name: '',
      gender: '',
      date_of_birth: null,
      phone_number: '',
      address: '',
      nationality: '',
      occupation: '',
      emergency_contact_name: '',
      emergency_contact_phone: '',
    },
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      try {
        setLoading(true);
        setError('');
        
        // In a real implementation, this would call an API endpoint
        // For now, we'll just simulate a successful request
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        setSuccess(true);
        setTimeout(() => {
          navigate('/citizens');
        }, 1500);
      } catch (err) {
        setError(err.response?.data?.detail || 'Failed to update citizen. Please try again.');
      } finally {
        setLoading(false);
      }
    },
    enableReinitialize: true,
  });

  // Set initial values when citizen data is loaded
  useEffect(() => {
    if (citizen) {
      formik.setValues({
        first_name: citizen.first_name || '',
        last_name: citizen.last_name || '',
        gender: citizen.gender || '',
        date_of_birth: citizen.date_of_birth ? new Date(citizen.date_of_birth) : null,
        phone_number: citizen.phone_number || '',
        address: citizen.address || '',
        nationality: citizen.nationality || '',
        occupation: citizen.occupation || '',
        emergency_contact_name: citizen.emergency_contact_name || '',
        emergency_contact_phone: citizen.emergency_contact_phone || '',
      });
    }
  }, [citizen]);

  const handleDeleteCitizen = async () => {
    try {
      setLoading(true);
      setError('');
      
      // In a real implementation, this would call an API endpoint
      // For now, we'll just simulate a successful request
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setDeleteDialogOpen(false);
      navigate('/citizens');
    } catch (err) {
      setError(err.response?.data?.detail || 'Failed to delete citizen. Please try again.');
      setLoading(false);
    }
  };

  if (loading && !citizen) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error && !citizen) {
    return (
      <Box sx={{ textAlign: 'center', py: 5 }}>
        <Typography variant="h5" color="error" gutterBottom>
          {t('error_loading_citizen', 'Error Loading Citizen')}
        </Typography>
        <Typography variant="body1">{error}</Typography>
        <Button
          variant="contained"
          startIcon={<BackIcon />}
          onClick={() => navigate('/citizens')}
          sx={{ mt: 3 }}
        >
          {t('back_to_citizens', 'Back to Citizens')}
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Button
            variant="outlined"
            startIcon={<BackIcon />}
            onClick={() => navigate('/citizens')}
            sx={{ mr: 2 }}
          >
            {t('back', 'Back')}
          </Button>
          <Typography variant="h4" component="h1">
            {t('edit_citizen', 'Edit Citizen')}
          </Typography>
        </Box>
        <Button
          variant="outlined"
          color="error"
          onClick={() => setDeleteDialogOpen(true)}
          disabled={citizen?.has_id_card}
        >
          {t('delete_citizen', 'Delete Citizen')}
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          Citizen updated successfully!
        </Alert>
      )}

      {citizen?.has_id_card && (
        <Alert severity="info" sx={{ mb: 3 }}>
          This citizen has an ID card and cannot be deleted.
        </Alert>
      )}

      <form onSubmit={formik.handleSubmit}>
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Personal Information
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="first_name"
                  name="first_name"
                  label={t('first_name', 'First Name')}
                  value={formik.values.first_name}
                  onChange={formik.handleChange}
                  error={formik.touched.first_name && Boolean(formik.errors.first_name)}
                  helperText={formik.touched.first_name && formik.errors.first_name}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="last_name"
                  name="last_name"
                  label={t('last_name', 'Last Name')}
                  value={formik.values.last_name}
                  onChange={formik.handleChange}
                  error={formik.touched.last_name && Boolean(formik.errors.last_name)}
                  helperText={formik.touched.last_name && formik.errors.last_name}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth error={formik.touched.gender && Boolean(formik.errors.gender)}>
                  <InputLabel id="gender-label">{t('gender', 'Gender')}</InputLabel>
                  <Select
                    labelId="gender-label"
                    id="gender"
                    name="gender"
                    value={formik.values.gender}
                    label={t('gender', 'Gender')}
                    onChange={formik.handleChange}
                  >
                    <MenuItem value="male">{t('male', 'Male')}</MenuItem>
                    <MenuItem value="female">{t('female', 'Female')}</MenuItem>
                  </Select>
                  {formik.touched.gender && formik.errors.gender && (
                    <FormHelperText>{formik.errors.gender}</FormHelperText>
                  )}
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <EthiopianCalendarWidget
                  label={t('date_of_birth_ethiopian_calendar', 'Date of Birth (Ethiopian Calendar)')}
                  value={formik.values.date_of_birth}
                  onChange={(value) => formik.setFieldValue('date_of_birth', value)}
                  error={formik.touched.date_of_birth && Boolean(formik.errors.date_of_birth)}
                  helperText={formik.touched.date_of_birth && formik.errors.date_of_birth}
                  language="en"
                  showConversion={true}
                  placeholder="Select birth date from Ethiopian calendar"
                  disableFuture={true}
                  minYear={1950}
                  maxYear={2020}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="nationality"
                  name="nationality"
                  label={t('nationality', 'Nationality')}
                  value={formik.values.nationality}
                  onChange={formik.handleChange}
                  error={formik.touched.nationality && Boolean(formik.errors.nationality)}
                  helperText={formik.touched.nationality && formik.errors.nationality}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="occupation"
                  name="occupation"
                  label={t('occupation', 'Occupation')}
                  value={formik.values.occupation}
                  onChange={formik.handleChange}
                  error={formik.touched.occupation && Boolean(formik.errors.occupation)}
                  helperText={formik.touched.occupation && formik.errors.occupation}
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              {t('contact_information', 'Contact Information')}
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="phone_number"
                  name="phone_number"
                  label={t('phone_number', 'Phone Number')}
                  value={formik.values.phone_number}
                  onChange={formik.handleChange}
                  error={formik.touched.phone_number && Boolean(formik.errors.phone_number)}
                  helperText={formik.touched.phone_number && formik.errors.phone_number}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="address"
                  name="address"
                  label={t('address', 'Address')}
                  value={formik.values.address}
                  onChange={formik.handleChange}
                  error={formik.touched.address && Boolean(formik.errors.address)}
                  helperText={formik.touched.address && formik.errors.address}
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              {t('emergency_contact', 'Emergency Contact')}
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="emergency_contact_name"
                  name="emergency_contact_name"
                  label={t('emergency_contact_name', 'Emergency Contact Name')}
                  value={formik.values.emergency_contact_name}
                  onChange={formik.handleChange}
                  error={formik.touched.emergency_contact_name && Boolean(formik.errors.emergency_contact_name)}
                  helperText={formik.touched.emergency_contact_name && formik.errors.emergency_contact_name}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="emergency_contact_phone"
                  name="emergency_contact_phone"
                  label={t('emergency_contact_phone', 'Emergency Contact Phone')}
                  value={formik.values.emergency_contact_phone}
                  onChange={formik.handleChange}
                  error={formik.touched.emergency_contact_phone && Boolean(formik.errors.emergency_contact_phone)}
                  helperText={formik.touched.emergency_contact_phone && formik.errors.emergency_contact_phone}
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
          <Button
            type="submit"
            variant="contained"
            size="large"
            disabled={loading}
            sx={{ minWidth: 150 }}
          >
            {loading ? <CircularProgress size={24} /> : t('update_citizen', 'Update Citizen')}
          </Button>
        </Box>
      </form>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>{t('delete_citizen', 'Delete Citizen')}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {t('are_you_sure_delete_citizen', 'Are you sure you want to delete this citizen? This action cannot be undone.')}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>{t('cancel', 'Cancel')}</Button>
          <Button onClick={handleDeleteCitizen} color="error" variant="contained">
            {loading ? <CircularProgress size={24} /> : t('delete', 'Delete')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CitizenEdit;
