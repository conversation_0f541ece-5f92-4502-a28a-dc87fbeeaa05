#!/usr/bin/env python
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append('/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from django_tenants.utils import schema_context, get_public_schema_name
from tenants.models import Tenant

User = get_user_model()

def find_users():
    print('=== Finding Users in All Schemas ===')
    
    # Check public schema
    with schema_context(get_public_schema_name()):
        print('\n--- Public Schema ---')
        users = User.objects.all()
        print(f'Users in public: {users.count()}')
        for user in users:
            print(f'  - {user.email} (ID: {user.id})')
    
    # Check all tenant schemas
    tenants = Tenant.objects.all()
    for tenant in tenants:
        print(f'\n--- {tenant.schema_name} ({tenant.name}) ---')
        try:
            with schema_context(tenant.schema_name):
                users = User.objects.all()
                print(f'Users in {tenant.schema_name}: {users.count()}')
                for user in users:
                    print(f'  - {user.email} (ID: {user.id}) - Tenant: {user.tenant.name if user.tenant else "None"}')
        except Exception as e:
            print(f'Error accessing {tenant.schema_name}: {e}')

if __name__ == '__main__':
    find_users()
