import axios from '../utils/axios';

/**
 * Get tenant ID from JWT token
 * @returns {number|null} The tenant ID or null if not found
 */
const getTenantId = () => {
  try {
    // First try to get from AuthContext user object (if available)
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    if (user.tenant_id) {
      console.log('🔍 Found tenant_id in user object:', user.tenant_id);
      return user.tenant_id;
    }

    // Fallback: Get the access token from localStorage and decode it
    const accessToken = localStorage.getItem('accessToken');
    if (!accessToken) {
      console.warn('No access token found in localStorage');
      return null;
    }

    // Decode the JWT token to get tenant information
    const base64Url = accessToken.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));

    const tokenData = JSON.parse(jsonPayload);
    console.log('🔍 JWT Token Data:', tokenData);
    console.log('🔍 Found tenant_id in JWT token:', tokenData.tenant_id);

    return tokenData.tenant_id || null;
  } catch (error) {
    console.error('Error getting tenant ID:', error);
    return null;
  }
};

/**
 * Service for citizen-related API operations
 */
const citizenService = {
  /**
   * Create a new citizen with all related family information
   * @param {Object} citizenData - The citizen data including family information
   * @returns {Promise} Promise with created citizen data
   */
  createCitizen: async (citizenData) => {
    try {
      // Get tenant ID
      const tenantId = getTenantId();
      if (!tenantId) {
        throw new Error('Tenant ID not found. Please ensure you are logged in to a tenant.');
      }

      // Transform the form data to match the API structure
      const apiData = transformFormDataToAPI(citizenData);
      console.log('🔍 Creating citizen in tenant:', tenantId, 'with data:', apiData);

      // Log the size of the data being sent
      console.log('📊 API Data summary:', {
        hasBasicInfo: !!(apiData.first_name && apiData.last_name),
        hasDateOfBirth: !!apiData.date_of_birth,
        dateOfBirthValue: apiData.date_of_birth,
        bloodType: apiData.blood_type, // ✅ Added blood_type logging
        disability: apiData.disability, // ✅ Added disability logging
        hasChildren: !!(apiData.children_data && apiData.children_data.length > 0),
        hasParents: !!(apiData.parents_data && apiData.parents_data.length > 0),
        hasEmergencyContact: !!(apiData.emergency_contacts_data && apiData.emergency_contacts_data.length > 0),
        hasSpouse: !!apiData.spouse_data,
        dataKeys: Object.keys(apiData)
      });

      // Log detailed spouse data if present
      if (apiData.spouse_data) {
        console.log('🔍 Detailed spouse data being sent:', JSON.stringify(apiData.spouse_data, null, 2));
      }

      const response = await axios.post(`/api/tenants/${tenantId}/citizens/`, apiData);
      console.log('🔍 Citizen created successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error creating citizen:', error);
      if (error.response) {
        console.error('🚨 Error response status:', error.response.status);
        console.error('🚨 Error response data:', error.response.data);

        // Log specific validation errors
        if (error.response.data) {
          console.error('🔍 Detailed validation errors:');
          Object.keys(error.response.data).forEach(field => {
            console.error(`  - ${field}:`, error.response.data[field]);

            // If it's an object (like spouse_data), log its contents
            if (typeof error.response.data[field] === 'object' && error.response.data[field] !== null) {
              console.error(`    Details for ${field}:`, JSON.stringify(error.response.data[field], null, 2));
            }
          });
        }

        console.error('🚨 Error response headers:', error.response.headers);
      } else if (error.request) {
        console.error('🚨 Error request:', error.request);
      } else {
        console.error('🚨 Error message:', error.message);
      }
      throw error;
    }
  },

  /**
   * Get all citizens
   * @returns {Promise} Promise with citizens data
   */
  getCitizens: async () => {
    try {
      const tenantId = getTenantId();
      if (!tenantId) {
        throw new Error('Tenant ID not found. Please ensure you are logged in to a tenant.');
      }

      const response = await axios.get(`/api/tenants/${tenantId}/citizens/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching citizens:', error);
      throw error;
    }
  },

  /**
   * Get a specific citizen by ID
   * @param {string} citizenId - The citizen ID
   * @returns {Promise} Promise with citizen data
   */
  getCitizen: async (citizenId) => {
    try {
      const tenantId = getTenantId();
      if (!tenantId) {
        throw new Error('Tenant ID not found. Please ensure you are logged in to a tenant.');
      }

      const response = await axios.get(`/api/tenants/${tenantId}/citizens/${citizenId}/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching citizen:', error);
      throw error;
    }
  },

  /**
   * Update a citizen
   * @param {string} citizenId - The citizen ID
   * @param {Object} citizenData - The updated citizen data
   * @returns {Promise} Promise with updated citizen data
   */
  updateCitizen: async (citizenId, citizenData) => {
    try {
      const tenantId = getTenantId();
      if (!tenantId) {
        throw new Error('Tenant ID not found. Please ensure you are logged in to a tenant.');
      }

      const response = await axios.put(`/api/tenants/${tenantId}/citizens/${citizenId}/`, citizenData);
      return response.data;
    } catch (error) {
      console.error('Error updating citizen:', error);
      throw error;
    }
  },

  /**
   * Delete a citizen
   * @param {string} citizenId - The citizen ID
   * @returns {Promise} Promise with deletion result
   */
  deleteCitizen: async (citizenId) => {
    try {
      const tenantId = getTenantId();
      if (!tenantId) {
        throw new Error('Tenant ID not found. Please ensure you are logged in to a tenant.');
      }

      const response = await axios.delete(`/api/tenants/${tenantId}/citizens/${citizenId}/`);
      return response.data;
    } catch (error) {
      console.error('Error deleting citizen:', error);
      throw error;
    }
  },

  /**
   * Search for residents (for family member linking)
   * @param {string} searchTerm - The search term
   * @param {string} gender - Optional gender filter
   * @returns {Promise} Promise with search results
   */
  searchResidents: async (searchTerm, gender = null) => {
    try {
      const tenantId = getTenantId();
      if (!tenantId) {
        throw new Error('Tenant ID not found. Please ensure you are logged in to a tenant.');
      }

      let url = `/api/tenants/${tenantId}/citizens/search/?search=${encodeURIComponent(searchTerm)}`;
      if (gender) {
        url += `&gender=${gender}`;
      }
      const response = await axios.get(url);
      return response.data;
    } catch (error) {
      console.error('Error searching residents:', error);
      throw error;
    }
  },

  /**
   * Get current tenant context for auto-filling forms
   * @returns {Promise} Promise with tenant context data
   */
  getTenantContext: async () => {
    try {
      const response = await axios.get('/api/tenants/current_context/');
      return response.data;
    } catch (error) {
      console.error('Error fetching tenant context:', error);
      throw error;
    }
  }
};

/**
 * Transform form data from the frontend format to the API format
 * @param {Object} formData - The form data from the frontend
 * @returns {Object} The transformed data for the API
 */
function transformFormDataToAPI(formData) {
  // Helper function to format date
  const formatDate = (date) => {
    console.log('🔍 formatDate called with:', {
      date,
      type: typeof date,
      isDate: date instanceof Date,
      isNull: date === null,
      isUndefined: date === undefined
    });

    if (!date) {
      console.log('❌ formatDate: No date provided, returning null');
      return null;
    }
    if (typeof date === 'string') {
      console.log('✅ formatDate: Date is string, returning as-is:', date);
      return date;
    }
    if (date instanceof Date) {
      const formatted = date.toISOString().split('T')[0];
      console.log('✅ formatDate: Date object converted to:', formatted);
      return formatted; // Convert to YYYY-MM-DD format
    }
    console.log('❌ formatDate: Unknown date format, returning null');
    return null;
  };

  // Debug the original form data
  console.log('🔍 Original form data date_of_birth:', {
    value: formData.date_of_birth,
    type: typeof formData.date_of_birth,
    isDate: formData.date_of_birth instanceof Date
  });

  const apiData = {
    // Basic citizen information (ensure required fields are not empty)
    first_name: formData.first_name || '',
    middle_name: formData.middle_name || '',
    last_name: formData.last_name || '',
    first_name_am: formData.first_name_am || '',
    middle_name_am: formData.middle_name_am || '',
    last_name_am: formData.last_name_am || '',
    date_of_birth: formatDate(formData.date_of_birth),
    gender: formData.gender || '',
    blood_type: formData.blood_type || null, // ✅ Added blood_type field
    disability: formData.disability || 'none', // ✅ Added disability field
    religion: formData.religion ? parseInt(formData.religion) : null,
    subcity: formData.subcity ? parseInt(formData.subcity) : null,
    kebele: formData.kebele ? parseInt(formData.kebele) : null,
    ketena: formData.ketena ? parseInt(formData.ketena) : null,
    status: formData.citizen_status ? parseInt(formData.citizen_status) : null,
    house_number: formData.house_number || '',
    phone: formData.phone_number || '', // Map phone_number to phone
    email: formData.email || '',
    nationality: formData.nationality ? parseInt(formData.nationality) : null,
    region: formData.region ? parseInt(formData.region) : null,
    marital_status: formData.marital_status ? parseInt(formData.marital_status) : null,
    employment: formData.employment || '',
    employee_type: formData.employee_type || '',
    organization_name: formData.organization_name || '',
    is_resident: true, // Default to true for new registrations
    is_active: true
  };

  // Add photo if provided
  if (formData.photo) {
    apiData.photo = formData.photo;
  }

  // Add biometric data if provided
  if (formData.left_thumb_fingerprint || formData.right_thumb_fingerprint) {
    apiData.biometric_data = {
      left_thumb_fingerprint: formData.left_thumb_fingerprint || null,
      right_thumb_fingerprint: formData.right_thumb_fingerprint || null
    };
  }

  // Transform children data
  if (formData.has_children && formData.children && formData.children.length > 0) {
    const validChildren = formData.children.filter(child =>
      child.first_name && child.first_name.trim() !== ''
    );

    if (validChildren.length > 0) {
      apiData.children_data = validChildren.map(child => ({
        first_name: child.first_name || '',
        middle_name: child.middle_name || '',
        last_name: child.last_name || '',
        first_name_am: child.first_name_am || '',
        middle_name_am: child.middle_name_am || '',
        last_name_am: child.last_name_am || '',
        date_of_birth: formatDate(child.date_of_birth),
        gender: child.gender || '',
        is_resident: child.is_resident || false,
        linked_citizen: null, // Don't use linked_citizen to avoid cross-schema issues
        nationality: formData.nationality ? parseInt(formData.nationality) : null,
        is_active: true
      }));
    }
  }

  // Transform parents data
  const parentsData = [];

  // Father
  if (formData.has_parents && (formData.father_first_name || formData.is_father_resident)) {
    const fatherData = {
      gender: 'male',
      first_name: formData.father_first_name || '',
      middle_name: formData.father_middle_name || '',
      last_name: formData.father_last_name || '',
      first_name_am: formData.father_first_name_am || '',
      middle_name_am: formData.father_middle_name_am || '',
      last_name_am: formData.father_last_name_am || '',
      phone: formData.father_phone || '',
      email: formData.father_email || '',
      is_resident: formData.is_father_resident || false,
      linked_citizen: null, // Don't use linked_citizen to avoid cross-schema issues
      nationality: formData.nationality ? parseInt(formData.nationality) : null,
      is_active: true
    };

    // Only add if we have at least first_name or it's a resident
    if (fatherData.first_name || formData.is_father_resident) {
      parentsData.push(fatherData);
    }
  }

  // Mother
  if (formData.has_parents && (formData.mother_first_name || formData.is_mother_resident)) {
    const motherData = {
      gender: 'female',
      first_name: formData.mother_first_name || '',
      middle_name: formData.mother_middle_name || '',
      last_name: formData.mother_last_name || '',
      first_name_am: formData.mother_first_name_am || '',
      middle_name_am: formData.mother_middle_name_am || '',
      last_name_am: formData.mother_last_name_am || '',
      phone: formData.mother_phone || '',
      email: formData.mother_email || '',
      is_resident: formData.is_mother_resident || false,
      linked_citizen: null, // Don't use linked_citizen to avoid cross-schema issues
      nationality: formData.nationality ? parseInt(formData.nationality) : null,
      is_active: true
    };

    // Only add if we have at least first_name or it's a resident
    if (motherData.first_name || formData.is_mother_resident) {
      parentsData.push(motherData);
    }
  }

  if (parentsData.length > 0) {
    apiData.parents_data = parentsData;
  }

  // Transform emergency contact data
  if (formData.emergency_contact_first_name || formData.is_emergency_contact_resident) {
    const emergencyContactData = {
      first_name: formData.emergency_contact_first_name || '',
      middle_name: formData.emergency_contact_middle_name || '',
      last_name: formData.emergency_contact_last_name || '',
      first_name_am: formData.emergency_contact_first_name_am || '',
      middle_name_am: formData.emergency_contact_middle_name_am || '',
      last_name_am: formData.emergency_contact_last_name_am || '',
      relationship: formData.emergency_contact_relation || '',
      phone: formData.emergency_contact_phone || '',
      email: formData.emergency_contact_email || '',
      is_resident: formData.is_emergency_contact_resident || false,
      linked_citizen: null, // Don't use linked_citizen to avoid cross-schema issues
      primary_contact: true,
      nationality: formData.nationality ? parseInt(formData.nationality) : null,
      is_active: true
    };

    // Only add if we have at least first_name AND (phone OR email) or it's a resident
    // This matches the backend validation requirement
    const hasValidContact = emergencyContactData.phone || emergencyContactData.email;
    const hasValidName = emergencyContactData.first_name && emergencyContactData.first_name.trim() !== '';

    if ((hasValidName && hasValidContact) || formData.is_emergency_contact_resident) {
      apiData.emergency_contacts_data = [emergencyContactData];
    }
  }

  // Transform spouse data (only if not single)
  console.log('🔍 Checking spouse conditions:', {
    has_spouse: formData.has_spouse,
    spouse_first_name: formData.spouse_first_name,
    is_spouse_resident: formData.is_spouse_resident,
    condition1: formData.has_spouse,
    condition2: formData.spouse_first_name || formData.is_spouse_resident,
    overallCondition: formData.has_spouse && (formData.spouse_first_name || formData.is_spouse_resident)
  });

  if (formData.has_spouse && (formData.spouse_first_name || formData.is_spouse_resident)) {
    const spouseData = {
      first_name: formData.spouse_first_name || '',
      middle_name: formData.spouse_middle_name || '',
      last_name: formData.spouse_last_name || '',
      first_name_am: formData.spouse_first_name_am || '',
      middle_name_am: formData.spouse_middle_name_am || '',
      last_name_am: formData.spouse_last_name_am || '',
      phone: formData.spouse_phone || null, // Use null instead of empty string to avoid unique constraint issues
      email: formData.spouse_email || null, // Use null instead of empty string
      is_resident: formData.is_spouse_resident || false,
      linked_citizen: null, // Don't use linked_citizen to avoid cross-schema issues
      nationality: formData.nationality ? parseInt(formData.nationality) : null,
      primary_contact: false, // Add the missing field
      is_active: true
    };

    // If spouse is resident, store digital_id for reference in email field (if no email provided)
    if (formData.is_spouse_resident && formData.spouse_id && !spouseData.email) {
      spouseData.email = `resident_ref_${formData.spouse_id}@internal.ref`;
    }

    // Only add if we have required fields: first_name AND last_name AND (phone OR email) or it's a resident
    // This matches the backend validation requirement
    const hasValidContact = (spouseData.phone && spouseData.phone.trim() !== '') ||
                           (spouseData.email && spouseData.email.trim() !== '');
    const hasValidName = spouseData.first_name && spouseData.first_name.trim() !== '' &&
                        spouseData.last_name && spouseData.last_name.trim() !== '';

    console.log('🔍 Spouse validation:', {
      hasValidName,
      hasValidContact,
      isResident: formData.is_spouse_resident,
      firstName: spouseData.first_name,
      lastName: spouseData.last_name,
      phone: spouseData.phone,
      email: spouseData.email,
      rawPhoneValue: formData.spouse_phone,
      rawEmailValue: formData.spouse_email,
      // Detailed breakdown
      firstNameValid: spouseData.first_name && spouseData.first_name.trim() !== '',
      lastNameValid: spouseData.last_name && spouseData.last_name.trim() !== '',
      phoneValid: spouseData.phone && spouseData.phone.trim() !== '',
      emailValid: spouseData.email && spouseData.email.trim() !== '',
      // Form data check
      hasSpouseFlag: formData.has_spouse,
      spouseFirstNameFromForm: formData.spouse_first_name,
      isSpouseResidentFromForm: formData.is_spouse_resident
    });

    if ((hasValidName && hasValidContact) || formData.is_spouse_resident) {
      apiData.spouse_data = spouseData;
      console.log('✅ Spouse data will be sent:', JSON.stringify(spouseData, null, 2));
    } else {
      console.log('❌ Spouse data validation failed - not sending spouse data');
      console.log('❌ Validation details:', {
        hasValidName,
        hasValidContact,
        isResident: formData.is_spouse_resident,
        requiredForSending: '(hasValidName && hasValidContact) || isResident'
      });
    }
  } else {
    console.log('❌ Spouse section skipped - conditions not met:', {
      has_spouse: formData.has_spouse,
      spouse_first_name: formData.spouse_first_name,
      is_spouse_resident: formData.is_spouse_resident,
      reason: !formData.has_spouse ? 'has_spouse is false' :
              (!formData.spouse_first_name && !formData.is_spouse_resident) ? 'no spouse name and not resident' : 'unknown'
    });
  }

  return apiData;
}

export default citizenService;
