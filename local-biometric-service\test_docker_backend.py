#!/usr/bin/env python3
"""
Test Docker Backend Configuration
"""

import urllib.request
import json
import subprocess
import time

def check_docker_containers():
    """Check Docker container status"""
    
    print("🐳 Checking Docker Container Status...")
    print("=" * 40)
    
    try:
        # Check if docker-compose is running
        result = subprocess.run(
            ["docker-compose", "ps"], 
            capture_output=True, 
            text=True,
            cwd="C:\\Users\\<USER>\\Desktop\\GoID"
        )
        
        if result.returncode == 0:
            print("✅ Docker Compose Status:")
            print(result.stdout)
        else:
            print("❌ Docker Compose not running or error:")
            print(result.stderr)
            return False
            
    except FileNotFoundError:
        print("❌ Docker or docker-compose not found")
        return False
    except Exception as e:
        print(f"❌ Error checking Docker: {e}")
        return False
    
    return True

def check_docker_logs():
    """Check Docker container logs"""
    
    print(f"\n📋 Checking Docker Logs...")
    print("=" * 30)
    
    containers = ["backend", "web", "db", "frontend"]
    
    for container in containers:
        print(f"\n🔍 {container.upper()} Container Logs:")
        try:
            result = subprocess.run(
                ["docker-compose", "logs", "--tail=10", container],
                capture_output=True,
                text=True,
                cwd="C:\\Users\\<USER>\\Desktop\\GoID"
            )
            
            if result.returncode == 0:
                if result.stdout.strip():
                    print(result.stdout[-500:])  # Last 500 chars
                else:
                    print("   (No recent logs)")
            else:
                print(f"   ❌ Error getting logs: {result.stderr}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

def test_docker_backend_endpoints():
    """Test backend endpoints in Docker"""
    
    print(f"\n🔍 Testing Docker Backend Endpoints...")
    print("=" * 40)
    
    # Docker typically runs on localhost:8000 or different ports
    base_urls = [
        "http://localhost:8000",
        "http://127.0.0.1:8000", 
        "http://localhost:3000",  # Frontend
        "http://localhost:8080",  # Alternative backend port
    ]
    
    for base_url in base_urls:
        print(f"\n📡 Testing base URL: {base_url}")
        
        endpoints = [
            "/",
            "/admin/",
            "/api/",
            "/api/biometrics/",
            "/api/biometrics/check-duplicates/",
            "/health/",
            "/status/",
        ]
        
        for endpoint in endpoints:
            url = base_url + endpoint
            try:
                response = urllib.request.urlopen(url, timeout=5)
                print(f"   ✅ {endpoint}: {response.getcode()}")
                
                # If this is the duplicate detection endpoint, test it
                if "check-duplicates" in endpoint:
                    test_duplicate_endpoint(url)
                    
            except urllib.error.HTTPError as e:
                print(f"   ⚠️ {endpoint}: HTTP {e.code}")
            except urllib.error.URLError as e:
                print(f"   ❌ {endpoint}: Connection failed")
            except Exception as e:
                print(f"   ❌ {endpoint}: {e}")

def test_duplicate_endpoint(url):
    """Test the duplicate detection endpoint"""
    
    print(f"      🧪 Testing duplicate detection...")
    
    # Use the real templates we captured earlier
    test_data = {
        "left_thumb_fingerprint": "eyJ2ZXJzaW9uIjogIjIuMCIsICJ0aHVtYl90eXBlIjogInVua25vd24iLCAibWludXRpYWVfcG9pbnRzIjogW3sieCI6IDEzMSwgInkiOiAxMDYsICJhbmdsZSI6IDMxNiwgInR5cGUiOiAicmlkZ2VfZW5kaW5nIiwgInF1YWxpdHkiOiAwLjkyOTk5OTk5OTk5OTk5OTl9XSwgImltYWdlX2RpbWVuc2lvbnMiOiB7IndpZHRoIjogMTYwLCAiaGVpZ2h0IjogNDgwfSwgInRvdGFsX3BpeGVscyI6IDc2ODAwfQ==",
        "right_thumb_fingerprint": "eyJ2ZXJzaW9uIjogIjIuMCIsICJ0aHVtYl90eXBlIjogInVua25vd24iLCAibWludXRpYWVfcG9pbnRzIjogW3sieCI6IDEwMCwgInkiOiAyMDAsICJhbmdsZSI6IDE4MCwgInR5cGUiOiAiYmlmdXJjYXRpb24iLCAicXVhbGl0eSI6IDAuODV9XSwgImltYWdlX2RpbWVuc2lvbnMiOiB7IndpZHRoIjogMTYwLCAiaGVpZ2h0IjogNDgwfSwgInRvdGFsX3BpeGVscyI6IDc2ODAwfQ=="
    }
    
    try:
        req_data = json.dumps(test_data).encode()
        
        req = urllib.request.Request(
            url,
            data=req_data,
            headers={
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        )
        
        response = urllib.request.urlopen(req, timeout=10)
        result = json.loads(response.read().decode())
        
        print(f"      ✅ Duplicate detection working!")
        print(f"      📊 Response: {result}")
        
        return True
        
    except urllib.error.HTTPError as e:
        error_content = e.read().decode() if hasattr(e, 'read') else str(e)
        print(f"      ⚠️ HTTP {e.code}: {error_content[:100]}...")
    except Exception as e:
        print(f"      ❌ Error: {e}")
    
    return False

def fix_docker_static_files():
    """Fix static files in Docker"""
    
    print(f"\n🔧 Fixing Docker Static Files...")
    print("=" * 35)
    
    commands = [
        ["docker-compose", "exec", "backend", "python", "manage.py", "collectstatic", "--noinput"],
        ["docker-compose", "exec", "backend", "python", "manage.py", "migrate"],
    ]
    
    for cmd in commands:
        try:
            print(f"🔄 Running: {' '.join(cmd[2:])}")
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd="C:\\Users\\<USER>\\Desktop\\GoID"
            )
            
            if result.returncode == 0:
                print(f"   ✅ Success")
                if result.stdout.strip():
                    print(f"   Output: {result.stdout[-200:]}")
            else:
                print(f"   ❌ Error: {result.stderr}")
                
        except Exception as e:
            print(f"   ❌ Command failed: {e}")

def restart_docker_services():
    """Restart Docker services"""
    
    print(f"\n🔄 Restarting Docker Services...")
    print("=" * 35)
    
    try:
        # Restart backend service
        result = subprocess.run(
            ["docker-compose", "restart", "backend"],
            capture_output=True,
            text=True,
            cwd="C:\\Users\\<USER>\\Desktop\\GoID"
        )
        
        if result.returncode == 0:
            print("✅ Backend service restarted")
        else:
            print(f"❌ Restart failed: {result.stderr}")
        
        # Wait for service to start
        print("⏰ Waiting for service to start...")
        time.sleep(10)
        
    except Exception as e:
        print(f"❌ Restart error: {e}")

def main():
    """Main function"""
    
    print("GoID Docker Backend Fixer")
    print("=" * 30)
    
    print("This will diagnose and fix Docker backend issues:")
    print("1. Check container status")
    print("2. Fix static files")
    print("3. Test API endpoints")
    print("4. Restart services if needed")
    print()
    
    # Check Docker status
    if not check_docker_containers():
        print("❌ Docker containers not running properly")
        print("💡 Try: docker-compose up -d")
        return
    
    # Check logs for errors
    check_docker_logs()
    
    # Fix static files
    fix_docker_static_files()
    
    # Restart services
    restart_docker_services()
    
    # Test endpoints
    test_docker_backend_endpoints()
    
    print(f"\n" + "="*50)
    print("DOCKER BACKEND STATUS")
    print("="*50)
    
    print(f"💡 If issues persist:")
    print(f"1. Check docker-compose.yml configuration")
    print(f"2. Verify environment variables")
    print(f"3. Check volume mounts for static files")
    print(f"4. Ensure database is properly connected")
    
    print(f"\n🔧 Manual Docker commands:")
    print(f"   docker-compose logs backend")
    print(f"   docker-compose exec backend python manage.py collectstatic")
    print(f"   docker-compose restart backend")
    print(f"   docker-compose up -d --build")
    
    input("\nPress Enter to exit...")

if __name__ == '__main__':
    main()
