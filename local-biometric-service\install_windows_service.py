#!/usr/bin/env python3
"""
Windows Service Installer for GoID JAR Bridge
"""

import sys
import os
import time
import subprocess
import logging
import json
from pathlib import Path

# Add the service directory to Python path
service_dir = Path(__file__).parent
sys.path.insert(0, str(service_dir))

try:
    import win32serviceutil
    import win32service
    import win32event
    import servicemanager
    WINDOWS_SERVICE_AVAILABLE = True
except ImportError:
    WINDOWS_SERVICE_AVAILABLE = False
    print("⚠️ Windows service modules not available.")
    print("💡 Install with: pip install pywin32")

class GoIDJarBridgeService(win32serviceutil.ServiceFramework):
    """Windows service for GoID JAR Bridge"""

    _svc_name_ = "GoIDJarBridge"
    _svc_display_name_ = "GoID JAR Bridge Service"
    _svc_description_ = "GoID Real Fingerprint Capture Service using Futronic JAR Bridge"
    _svc_deps_ = None  # No dependencies
    _exe_name_ = sys.executable
    _exe_args_ = f'"{__file__}"'
    
    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        self.is_running = True
        
        # Setup logging
        self.setup_logging()
        
        # Service configuration
        self.service_port = 8001
        self.jar_bridge_process = None
        self.restart_count = 0
        self.max_restarts = 10
        
    def setup_logging(self):
        """Setup service logging"""
        log_dir = service_dir / "logs"
        log_dir.mkdir(exist_ok=True)
        
        log_file = log_dir / "jar_bridge_service.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        
    def SvcStop(self):
        """Stop the service"""
        self.logger.info("🛑 GoID JAR Bridge Service stopping...")
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        
        self.is_running = False
        
        # Stop JAR bridge process
        if self.jar_bridge_process:
            try:
                self.jar_bridge_process.terminate()
                self.jar_bridge_process.wait(timeout=10)
                self.logger.info("✅ JAR bridge process stopped")
            except Exception as e:
                self.logger.error(f"❌ Error stopping JAR bridge: {e}")
                try:
                    self.jar_bridge_process.kill()
                except:
                    pass
        
        win32event.SetEvent(self.hWaitStop)
        self.logger.info("✅ GoID JAR Bridge Service stopped")
        
    def SvcDoRun(self):
        """Main service execution"""
        self.logger.info("🚀 GoID JAR Bridge Service starting...")
        
        # Log to Windows Event Log
        servicemanager.LogMsg(
            servicemanager.EVENTLOG_INFORMATION_TYPE,
            servicemanager.PYS_SERVICE_STARTED,
            (self._svc_name_, '')
        )
        
        try:
            self.run_service()
        except Exception as e:
            self.logger.error(f"💥 Service crashed: {e}")
            servicemanager.LogErrorMsg(f"GoID JAR Bridge Service error: {e}")
        
    def run_service(self):
        """Main service loop with auto-restart"""
        self.logger.info("🎯 Starting JAR bridge service loop...")
        
        while self.is_running:
            try:
                # Start JAR bridge
                self.logger.info(f"🔄 Starting JAR bridge (attempt {self.restart_count + 1})")
                
                if self.start_jar_bridge():
                    # Monitor the service
                    self.monitor_service()
                else:
                    self.logger.error("❌ Failed to start JAR bridge")
                    break
                
            except Exception as e:
                self.logger.error(f"❌ JAR bridge error: {e}")
                self.restart_count += 1
                
                if self.restart_count >= self.max_restarts:
                    self.logger.error(f"💥 Max restarts ({self.max_restarts}) reached. Stopping service.")
                    break
                
                # Wait before restart
                self.logger.info(f"⏰ Waiting 30 seconds before restart...")
                if self.wait_for_stop(30):
                    break  # Service stop requested
        
        self.logger.info("🏁 Service loop ended")
        
    def start_jar_bridge(self):
        """Start the JAR bridge as subprocess"""
        try:
            # Path to the JAR bridge script
            jar_bridge_script = service_dir / "working_jar_bridge.py"
            
            if not jar_bridge_script.exists():
                self.logger.error(f"❌ JAR bridge script not found: {jar_bridge_script}")
                return False
            
            # Start the JAR bridge process
            self.logger.info(f"🌐 Starting JAR bridge subprocess...")
            
            self.jar_bridge_process = subprocess.Popen(
                [sys.executable, str(jar_bridge_script)],
                cwd=str(service_dir),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW  # Run without console window
            )
            
            # Wait a moment to see if it starts successfully
            time.sleep(5)
            
            if self.jar_bridge_process.poll() is None:
                self.logger.info(f"✅ JAR bridge process started (PID: {self.jar_bridge_process.pid})")
                return True
            else:
                self.logger.error(f"❌ JAR bridge process failed to start")
                return False
            
        except Exception as e:
            self.logger.error(f"❌ Failed to start JAR bridge: {e}")
            return False
    
    def monitor_service(self):
        """Monitor the JAR bridge service"""
        self.logger.info("👁️ Monitoring JAR bridge service...")
        
        check_interval = 30  # Check every 30 seconds
        
        while self.is_running:
            try:
                # Check if service should stop
                if self.wait_for_stop(check_interval):
                    break
                
                # Check if process is still running
                if self.jar_bridge_process and self.jar_bridge_process.poll() is not None:
                    self.logger.warning("⚠️ JAR bridge process died, restarting...")
                    break
                
                # Health check
                if not self.health_check():
                    self.logger.warning("⚠️ Health check failed, restarting...")
                    break
                
                self.logger.debug("✅ Health check passed")
                
            except Exception as e:
                self.logger.error(f"❌ Monitor error: {e}")
                break
    
    def health_check(self):
        """Perform health check on JAR bridge"""
        try:
            import urllib.request
            
            # Test device status endpoint
            response = urllib.request.urlopen(
                f"http://localhost:{self.service_port}/api/device/status", 
                timeout=10
            )
            
            if response.getcode() == 200:
                return True
            else:
                self.logger.warning(f"⚠️ Health check returned {response.getcode()}")
                return False
                
        except Exception as e:
            self.logger.warning(f"⚠️ Health check failed: {e}")
            return False
    
    def wait_for_stop(self, timeout_seconds):
        """Wait for stop event or timeout"""
        result = win32event.WaitForSingleObject(self.hWaitStop, timeout_seconds * 1000)
        return result == win32event.WAIT_OBJECT_0

def check_requirements():
    """Check if all requirements are met"""
    print("🔍 Checking requirements...")
    
    # Check pywin32
    if not WINDOWS_SERVICE_AVAILABLE:
        print("❌ pywin32 not installed")
        print("💡 Install with: pip install pywin32")
        return False
    
    # Check JAR bridge script
    jar_bridge_script = Path(__file__).parent / "working_jar_bridge.py"
    if not jar_bridge_script.exists():
        print(f"❌ JAR bridge script not found: {jar_bridge_script}")
        return False
    
    # Check admin privileges
    try:
        import ctypes
        if not ctypes.windll.shell32.IsUserAnAdmin():
            print("❌ Administrator privileges required")
            print("💡 Run as Administrator")
            return False
    except:
        print("⚠️ Could not check admin privileges")
    
    print("✅ All requirements met")
    return True

def install_service():
    """Install the Windows service"""
    if not check_requirements():
        return False
    
    try:
        print("📦 Installing GoID JAR Bridge Service...")
        
        # Install service
        win32serviceutil.InstallService(
            GoIDJarBridgeService,
            GoIDJarBridgeService._svc_name_,
            GoIDJarBridgeService._svc_display_name_,
            description=GoIDJarBridgeService._svc_description_
        )
        
        print("✅ Service installed successfully!")
        print(f"   Service Name: {GoIDJarBridgeService._svc_name_}")
        print(f"   Display Name: {GoIDJarBridgeService._svc_display_name_}")
        print("\n💡 Next steps:")
        print("   1. Start service: python install_windows_service.py start")
        print("   2. Check status in Services.msc")
        print("   3. Test: http://localhost:8001/api/device/status")
        
        return True
        
    except Exception as e:
        print(f"❌ Service installation failed: {e}")
        return False

def uninstall_service():
    """Uninstall the Windows service"""
    try:
        print("🗑️ Uninstalling GoID JAR Bridge Service...")
        
        # Stop service first
        try:
            win32serviceutil.StopService(GoIDJarBridgeService._svc_name_)
            time.sleep(2)
        except:
            pass
        
        # Uninstall service
        win32serviceutil.RemoveService(GoIDJarBridgeService._svc_name_)
        
        print("✅ Service uninstalled successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Service uninstallation failed: {e}")
        return False

def start_service():
    """Start the Windows service"""
    try:
        print("▶️ Starting GoID JAR Bridge Service...")
        
        win32serviceutil.StartService(GoIDJarBridgeService._svc_name_)
        
        print("✅ Service started successfully!")
        print("💡 Test with: http://localhost:8001/api/device/status")
        return True
        
    except Exception as e:
        print(f"❌ Service start failed: {e}")
        return False

def stop_service():
    """Stop the Windows service"""
    try:
        print("⏹️ Stopping GoID JAR Bridge Service...")
        
        win32serviceutil.StopService(GoIDJarBridgeService._svc_name_)
        
        print("✅ Service stopped successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Service stop failed: {e}")
        return False

def main():
    """Main entry point"""
    if len(sys.argv) == 1:
        # Running as service
        if WINDOWS_SERVICE_AVAILABLE:
            servicemanager.Initialize()
            servicemanager.PrepareToHostSingle(GoIDJarBridgeService)
            servicemanager.StartServiceCtrlDispatcher()
        else:
            print("❌ Cannot run as service - pywin32 not installed")
            return

    command = sys.argv[1].lower()

    if command == 'check':
        check_requirements()
    elif command == 'install':
        install_service()
    elif command == 'uninstall':
        uninstall_service()
    elif command == 'start':
        start_service()
    elif command == 'stop':
        stop_service()
    elif command == 'restart':
        stop_service()
        time.sleep(2)
        start_service()
    elif command == 'help':
        print("GoID JAR Bridge Windows Service Installer")
        print("=" * 45)
        print("Usage:")
        print("  python install_windows_service.py install    - Install service")
        print("  python install_windows_service.py uninstall  - Uninstall service")
        print("  python install_windows_service.py start      - Start service")
        print("  python install_windows_service.py stop       - Stop service")
        print("  python install_windows_service.py restart    - Restart service")
        print("  python install_windows_service.py check      - Check requirements")
    else:
        print(f"❌ Unknown command: {command}")
        print("💡 Use 'help' for usage information")

if __name__ == '__main__':
    main()
