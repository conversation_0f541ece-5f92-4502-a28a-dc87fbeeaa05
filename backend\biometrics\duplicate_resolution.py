"""
Duplicate Resolution Workflow System

This module provides workflows for handling detected duplicate fingerprints,
including flagging, review processes, and resolution mechanisms.
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from enum import Enum
from django.db import models, transaction
from django.contrib.auth import get_user_model
from django.core.mail import send_mail
from django.conf import settings
from django_tenants.utils import schema_context

logger = logging.getLogger(__name__)
User = get_user_model()


class DuplicateStatus(models.TextChoices):
    """Status options for duplicate cases"""
    PENDING = 'pending', 'Pending Review'
    UNDER_REVIEW = 'under_review', 'Under Review'
    RESOLVED_LEGITIMATE = 'resolved_legitimate', 'Resolved - Legitimate Duplicate'
    RESOLVED_FRAUD = 'resolved_fraud', 'Resolved - Fraud Detected'
    RESOLVED_ERROR = 'resolved_error', 'Resolved - System Error'
    ESCALATED = 'escalated', 'Escalated to Higher Authority'


class DuplicateResolutionAction(models.TextChoices):
    """Available actions for duplicate resolution"""
    APPROVE_BOTH = 'approve_both', 'Approve Both Registrations'
    MERGE_RECORDS = 'merge_records', 'Merge Records'
    BLOCK_NEW = 'block_new', 'Block New Registration'
    BLOCK_EXISTING = 'block_existing', 'Block Existing Registration'
    REQUIRE_ADDITIONAL_VERIFICATION = 'require_verification', 'Require Additional Verification'
    ESCALATE = 'escalate', 'Escalate to Supervisor'


class DuplicateCase(models.Model):
    """Model to track duplicate fingerprint cases"""
    
    # Case identification
    case_id = models.CharField(max_length=50, unique=True, editable=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # Duplicate information
    match_score = models.FloatField()
    confidence_level = models.FloatField()
    detection_method = models.CharField(max_length=50, default='FMatcher')
    
    # Citizens involved
    primary_citizen_id = models.IntegerField()
    primary_citizen_name = models.CharField(max_length=200)
    primary_citizen_digital_id = models.CharField(max_length=50)
    primary_tenant_id = models.IntegerField()
    primary_tenant_name = models.CharField(max_length=100)
    
    secondary_citizen_id = models.IntegerField()
    secondary_citizen_name = models.CharField(max_length=200)
    secondary_citizen_digital_id = models.CharField(max_length=50)
    secondary_tenant_id = models.IntegerField()
    secondary_tenant_name = models.CharField(max_length=100)
    
    # Case status and resolution
    status = models.CharField(max_length=30, choices=DuplicateStatus.choices, default=DuplicateStatus.PENDING)
    priority = models.CharField(max_length=10, choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')], default='medium')
    
    # Assignment and review
    assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_duplicate_cases')
    reviewed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='reviewed_duplicate_cases')
    review_date = models.DateTimeField(null=True, blank=True)
    
    # Resolution
    resolution_action = models.CharField(max_length=30, choices=DuplicateResolutionAction.choices, null=True, blank=True)
    resolution_notes = models.TextField(blank=True)
    resolution_date = models.DateTimeField(null=True, blank=True)
    
    # Additional data
    fingerprint_data = models.JSONField(default=dict)  # Store fingerprint comparison data
    investigation_notes = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'priority']),
            models.Index(fields=['assigned_to', 'status']),
            models.Index(fields=['created_at']),
        ]
    
    def save(self, *args, **kwargs):
        if not self.case_id:
            self.case_id = self._generate_case_id()
        super().save(*args, **kwargs)
    
    def _generate_case_id(self):
        """Generate unique case ID"""
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        return f"DUP-{timestamp}-{self.primary_citizen_id}-{self.secondary_citizen_id}"
    
    def __str__(self):
        return f"Duplicate Case {self.case_id}: {self.primary_citizen_name} vs {self.secondary_citizen_name}"


class DuplicateCaseActivity(models.Model):
    """Track activities and updates on duplicate cases"""
    
    case = models.ForeignKey(DuplicateCase, on_delete=models.CASCADE, related_name='activities')
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    activity_type = models.CharField(max_length=50)
    description = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)
    metadata = models.JSONField(default=dict)
    
    class Meta:
        ordering = ['-timestamp']
    
    def __str__(self):
        return f"{self.activity_type} on {self.case.case_id} by {self.user}"


class DuplicateResolutionWorkflow:
    """
    Workflow manager for handling duplicate fingerprint cases.
    """
    
    def __init__(self):
        self.auto_assignment_enabled = True
        self.notification_enabled = True
    
    def create_duplicate_case(self, duplicate_match: Dict, detection_context: Dict = None) -> DuplicateCase:
        """
        Create a new duplicate case from a detected match.
        
        Args:
            duplicate_match (Dict): Match data from duplicate detection
            detection_context (Dict): Additional context about the detection
            
        Returns:
            DuplicateCase: Created case object
        """
        try:
            with transaction.atomic():
                # Determine priority based on match score
                priority = self._calculate_priority(duplicate_match['match_score'])
                
                # Create the case
                case = DuplicateCase.objects.create(
                    match_score=duplicate_match['match_score'],
                    confidence_level=duplicate_match.get('confidence', 0.0),
                    detection_method=duplicate_match.get('detection_method', 'FMatcher'),
                    
                    # Primary citizen (usually the new registration)
                    primary_citizen_id=detection_context.get('new_citizen_id', 0) if detection_context else 0,
                    primary_citizen_name=detection_context.get('new_citizen_name', 'Unknown') if detection_context else 'Unknown',
                    primary_citizen_digital_id=detection_context.get('new_citizen_digital_id', '') if detection_context else '',
                    primary_tenant_id=detection_context.get('new_tenant_id', 0) if detection_context else 0,
                    primary_tenant_name=detection_context.get('new_tenant_name', 'Unknown') if detection_context else 'Unknown',
                    
                    # Secondary citizen (existing registration)
                    secondary_citizen_id=duplicate_match['citizen_id'],
                    secondary_citizen_name=duplicate_match['citizen_name'],
                    secondary_citizen_digital_id=duplicate_match['citizen_digital_id'],
                    secondary_tenant_id=duplicate_match['tenant_id'],
                    secondary_tenant_name=duplicate_match['tenant_name'],
                    
                    priority=priority,
                    fingerprint_data={
                        'match_details': duplicate_match.get('match_details', []),
                        'matched_thumb': duplicate_match.get('matched_thumb'),
                        'new_thumb': duplicate_match.get('new_thumb'),
                        'detection_timestamp': datetime.now().isoformat()
                    }
                )
                
                # Log case creation
                self._log_activity(case, None, 'case_created', 
                                 f"Duplicate case created with match score {duplicate_match['match_score']:.1f}")
                
                # Auto-assign if enabled
                if self.auto_assignment_enabled:
                    self._auto_assign_case(case)
                
                # Send notifications
                if self.notification_enabled:
                    self._send_case_notifications(case, 'created')
                
                logger.info(f"🚨 Created duplicate case {case.case_id}: {case.primary_citizen_name} vs {case.secondary_citizen_name}")
                
                return case
                
        except Exception as e:
            logger.error(f"Error creating duplicate case: {e}")
            raise
    
    def assign_case(self, case: DuplicateCase, user: User, assigned_by: User = None) -> bool:
        """
        Assign a case to a user for review.
        
        Args:
            case (DuplicateCase): Case to assign
            user (User): User to assign to
            assigned_by (User): User making the assignment
            
        Returns:
            bool: Success status
        """
        try:
            with transaction.atomic():
                case.assigned_to = user
                case.status = DuplicateStatus.UNDER_REVIEW
                case.save()
                
                self._log_activity(case, assigned_by, 'case_assigned', 
                                 f"Case assigned to {user.username}")
                
                if self.notification_enabled:
                    self._send_assignment_notification(case, user)
                
                logger.info(f"📋 Case {case.case_id} assigned to {user.username}")
                return True
                
        except Exception as e:
            logger.error(f"Error assigning case {case.case_id}: {e}")
            return False
    
    def resolve_case(self, case: DuplicateCase, action: str, notes: str, resolved_by: User) -> bool:
        """
        Resolve a duplicate case with the specified action.
        
        Args:
            case (DuplicateCase): Case to resolve
            action (str): Resolution action
            notes (str): Resolution notes
            resolved_by (User): User resolving the case
            
        Returns:
            bool: Success status
        """
        try:
            with transaction.atomic():
                # Update case status
                case.resolution_action = action
                case.resolution_notes = notes
                case.resolution_date = datetime.now()
                case.reviewed_by = resolved_by
                case.review_date = datetime.now()
                
                # Set final status based on action
                if action in [DuplicateResolutionAction.APPROVE_BOTH, DuplicateResolutionAction.MERGE_RECORDS]:
                    case.status = DuplicateStatus.RESOLVED_LEGITIMATE
                elif action in [DuplicateResolutionAction.BLOCK_NEW, DuplicateResolutionAction.BLOCK_EXISTING]:
                    case.status = DuplicateStatus.RESOLVED_FRAUD
                elif action == DuplicateResolutionAction.ESCALATE:
                    case.status = DuplicateStatus.ESCALATED
                else:
                    case.status = DuplicateStatus.RESOLVED_ERROR
                
                case.save()
                
                # Log resolution
                self._log_activity(case, resolved_by, 'case_resolved', 
                                 f"Case resolved with action: {action}")
                
                # Execute the resolution action
                self._execute_resolution_action(case, action)
                
                # Send notifications
                if self.notification_enabled:
                    self._send_resolution_notification(case)
                
                logger.info(f"✅ Case {case.case_id} resolved with action: {action}")
                return True
                
        except Exception as e:
            logger.error(f"Error resolving case {case.case_id}: {e}")
            return False
    
    def escalate_case(self, case: DuplicateCase, escalated_by: User, reason: str) -> bool:
        """
        Escalate a case to higher authority.
        
        Args:
            case (DuplicateCase): Case to escalate
            escalated_by (User): User escalating the case
            reason (str): Reason for escalation
            
        Returns:
            bool: Success status
        """
        try:
            with transaction.atomic():
                case.status = DuplicateStatus.ESCALATED
                case.priority = 'high'  # Escalated cases get high priority
                case.save()
                
                self._log_activity(case, escalated_by, 'case_escalated', 
                                 f"Case escalated: {reason}")
                
                # Notify supervisors
                if self.notification_enabled:
                    self._send_escalation_notification(case, reason)
                
                logger.warning(f"⬆️ Case {case.case_id} escalated by {escalated_by.username}: {reason}")
                return True
                
        except Exception as e:
            logger.error(f"Error escalating case {case.case_id}: {e}")
            return False
    
    def _calculate_priority(self, match_score: float) -> str:
        """Calculate case priority based on match score"""
        if match_score >= 1000:  # Perfect match
            return 'critical'
        elif match_score >= 100:
            return 'high'
        elif match_score >= 60:
            return 'medium'
        else:
            return 'low'
    
    def _auto_assign_case(self, case: DuplicateCase):
        """Auto-assign case to available reviewer"""
        try:
            # Find users with appropriate permissions
            # This is a simplified implementation - in production, you'd have more sophisticated assignment logic
            available_reviewers = User.objects.filter(
                is_staff=True,
                is_active=True
            ).exclude(
                assigned_duplicate_cases__status__in=[DuplicateStatus.UNDER_REVIEW, DuplicateStatus.PENDING]
            )[:1]
            
            if available_reviewers:
                reviewer = available_reviewers[0]
                case.assigned_to = reviewer
                case.status = DuplicateStatus.UNDER_REVIEW
                case.save()
                
                self._log_activity(case, None, 'auto_assigned', 
                                 f"Auto-assigned to {reviewer.username}")
                
        except Exception as e:
            logger.error(f"Error auto-assigning case {case.case_id}: {e}")
    
    def _execute_resolution_action(self, case: DuplicateCase, action: str):
        """Execute the actual resolution action"""
        try:
            if action == DuplicateResolutionAction.BLOCK_NEW:
                # In a real implementation, this would block the new registration
                logger.info(f"🚫 Would block new registration for case {case.case_id}")
                
            elif action == DuplicateResolutionAction.BLOCK_EXISTING:
                # In a real implementation, this would flag/block the existing citizen
                logger.info(f"🚫 Would block existing citizen for case {case.case_id}")
                
            elif action == DuplicateResolutionAction.MERGE_RECORDS:
                # In a real implementation, this would merge the citizen records
                logger.info(f"🔗 Would merge records for case {case.case_id}")
                
            # Add more action implementations as needed
            
        except Exception as e:
            logger.error(f"Error executing resolution action for case {case.case_id}: {e}")
    
    def _log_activity(self, case: DuplicateCase, user: User, activity_type: str, description: str, metadata: Dict = None):
        """Log activity on a case"""
        try:
            DuplicateCaseActivity.objects.create(
                case=case,
                user=user,
                activity_type=activity_type,
                description=description,
                metadata=metadata or {}
            )
        except Exception as e:
            logger.error(f"Error logging activity for case {case.case_id}: {e}")
    
    def _send_case_notifications(self, case: DuplicateCase, event_type: str):
        """Send notifications for case events"""
        # Simplified notification - in production, you'd use proper email templates
        try:
            if hasattr(settings, 'DUPLICATE_CASE_NOTIFICATIONS') and settings.DUPLICATE_CASE_NOTIFICATIONS:
                subject = f"Duplicate Fingerprint Case {event_type.title()}: {case.case_id}"
                message = f"A duplicate fingerprint case has been {event_type}.\n\nCase ID: {case.case_id}\nMatch Score: {case.match_score}\nPriority: {case.priority}"
                
                # Send to administrators
                admin_emails = [user.email for user in User.objects.filter(is_staff=True, email__isnull=False)]
                if admin_emails:
                    send_mail(subject, message, settings.DEFAULT_FROM_EMAIL, admin_emails, fail_silently=True)
                    
        except Exception as e:
            logger.error(f"Error sending notifications for case {case.case_id}: {e}")
    
    def _send_assignment_notification(self, case: DuplicateCase, assigned_user: User):
        """Send notification when case is assigned"""
        # Implementation would send email/notification to assigned user
        pass
    
    def _send_resolution_notification(self, case: DuplicateCase):
        """Send notification when case is resolved"""
        # Implementation would notify relevant parties about resolution
        pass
    
    def _send_escalation_notification(self, case: DuplicateCase, reason: str):
        """Send notification when case is escalated"""
        # Implementation would notify supervisors about escalation
        pass
