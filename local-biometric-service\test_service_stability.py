#!/usr/bin/env python3
"""
Test service stability and error handling
"""

import logging
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_device_initialization():
    """Test device initialization without crashing"""
    try:
        logger.info("🧪 Testing device initialization...")
        
        from futronic_sdk import FutronicSDK
        
        device = FutronicSDK()
        
        if device.initialize():
            status = device.get_device_status()
            logger.info("✅ Device initialized successfully")
            logger.info(f"Real device: {status['real_device']}")
            logger.info(f"Interface: {status['interface']}")
            return device, True
        else:
            logger.warning("⚠️ Device initialization failed")
            return device, False
            
    except Exception as e:
        logger.error(f"❌ Device initialization error: {e}")
        return None, False

def test_safe_capture(device):
    """Test fingerprint capture with safety measures"""
    try:
        logger.info("🧪 Testing safe fingerprint capture...")
        
        if not device:
            logger.error("❌ No device available")
            return False
        
        # Test with very short timeout
        logger.info("📸 Attempting quick capture test...")
        
        try:
            # This should not hang the service
            template = device.capture_fingerprint('left')
            
            if template:
                logger.info(f"✅ Capture successful: Quality {template.quality_score}%")
                return True
            else:
                logger.warning("⚠️ Capture returned None")
                return False
                
        except Exception as capture_error:
            logger.warning(f"⚠️ Capture failed (expected): {capture_error}")
            return False  # This is expected if no finger is placed
            
    except Exception as e:
        logger.error(f"❌ Safe capture test error: {e}")
        return False

def test_service_components():
    """Test individual service components"""
    try:
        logger.info("🧪 Testing service components...")
        
        # Test BiometricDeviceManager
        from biometric_service import BiometricDeviceManager
        
        manager = BiometricDeviceManager()
        
        # Test initialization
        init_success = manager.initialize_device()
        logger.info(f"Manager initialization: {'✅ Success' if init_success else '⚠️ Failed'}")
        
        # Test status
        if manager.device_connected:
            logger.info("✅ Device manager shows device connected")
        else:
            logger.warning("⚠️ Device manager shows device disconnected")
        
        # Test safe capture
        if manager.device_connected:
            logger.info("🧪 Testing manager capture (will timeout quickly)...")
            result = manager.capture_fingerprint('left')
            
            if result['success']:
                logger.info("✅ Manager capture successful")
            else:
                logger.info(f"⚠️ Manager capture failed: {result['error']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Service component test error: {e}")
        return False

def main():
    """Main test function"""
    logger.info("🚀 Service Stability Test")
    logger.info("=" * 30)
    
    # Test 1: Device initialization
    logger.info("\n📋 Test 1: Device Initialization")
    device, init_ok = test_device_initialization()
    
    # Test 2: Safe capture
    logger.info("\n📋 Test 2: Safe Capture Test")
    capture_ok = test_safe_capture(device)
    
    # Test 3: Service components
    logger.info("\n📋 Test 3: Service Components")
    service_ok = test_service_components()
    
    # Summary
    logger.info("\n📊 Test Results:")
    logger.info("=" * 20)
    logger.info(f"Device Init: {'✅ PASS' if init_ok else '❌ FAIL'}")
    logger.info(f"Safe Capture: {'✅ PASS' if capture_ok else '⚠️ EXPECTED FAIL'}")
    logger.info(f"Service Components: {'✅ PASS' if service_ok else '❌ FAIL'}")
    
    if init_ok and service_ok:
        logger.info("\n🎉 Service should be stable!")
        logger.info("💡 The service is ready to start without crashing")
    else:
        logger.info("\n❌ Service may have stability issues")
        logger.info("💡 Check the errors above before starting the service")
    
    # Cleanup
    if device:
        try:
            device.disconnect()
            logger.info("✅ Device disconnected cleanly")
        except:
            pass
    
    return init_ok and service_ok

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    exit(0 if success else 1)
