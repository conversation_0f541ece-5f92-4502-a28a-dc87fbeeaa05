<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GoID Translation System Demo</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header .subtitle {
            margin-top: 10px;
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .language-switcher {
            margin: 20px 0;
            text-align: center;
        }
        
        .language-btn {
            background: #fff;
            color: #4CAF50;
            border: 2px solid #4CAF50;
            padding: 12px 24px;
            margin: 0 10px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            min-width: 120px;
        }
        
        .language-btn:hover {
            background: #4CAF50;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
        }
        
        .language-btn.active {
            background: #4CAF50;
            color: white;
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
        }
        
        .demo-sections {
            padding: 40px;
        }
        
        .demo-section {
            margin-bottom: 40px;
            padding: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .demo-section h2 {
            color: #333;
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .demo-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #4CAF50;
        }
        
        .demo-card h3 {
            margin-top: 0;
            color: #4CAF50;
            font-size: 1.2em;
        }
        
        .translation-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .translation-item:last-child {
            border-bottom: none;
        }
        
        .translation-key {
            font-weight: 500;
            color: #666;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        
        .translation-value {
            font-weight: 500;
            color: #333;
            text-align: right;
            direction: var(--text-direction, ltr);
        }
        
        .amharic {
            font-family: 'Noto Sans Ethiopic', 'Nyala', 'Abyssinica SIL', serif;
            font-size: 1.1em;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            display: block;
        }
        
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
            margin-top: 5px;
        }
        
        .footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        // Mock localization service with sample translations
        const translations = {
            en: {
                // Navigation
                dashboard: 'Dashboard',
                citizens: 'Citizens',
                id_cards: 'ID Cards',
                clearance: 'Clearance',
                transfer: 'Transfer',
                print_queue: 'Print Queue',
                reports: 'Reports',
                tenants: 'Tenants',
                system_users: 'System Users',
                
                // Authentication
                login: 'Login',
                logout: 'Logout',
                sign_in: 'Sign In',
                register: 'Register',
                username: 'Username',
                password: 'Password',
                email: 'Email',
                remember_me: 'Remember Me',
                forgot_password: 'Forgot Password?',
                
                // Citizen Management
                add_citizen: 'Add Citizen',
                edit_citizen: 'Edit Citizen',
                citizen_registration: 'Citizen Registration',
                first_name: 'First Name',
                last_name: 'Last Name',
                full_name: 'Full Name',
                date_of_birth: 'Date of Birth',
                gender: 'Gender',
                male: 'Male',
                female: 'Female',
                phone_number: 'Phone Number',
                address: 'Address',
                
                // Actions
                save: 'Save',
                cancel: 'Cancel',
                submit: 'Submit',
                edit: 'Edit',
                delete: 'Delete',
                view: 'View',
                search: 'Search',
                filter: 'Filter',
                
                // Status
                pending: 'Pending',
                approved: 'Approved',
                rejected: 'Rejected',
                success: 'Success',
                error: 'Error',
                loading: 'Loading...',
                
                // Dashboard
                total_citizens: 'Total Citizens',
                new_registrations: 'New Registrations',
                pending_approvals: 'Pending Approvals',
                
                // Demo specific
                demo_title: 'GoID Translation System Demo',
                demo_subtitle: 'Comprehensive Bilingual Support for Ethiopian ID Management',
                language_switcher: 'Language Switcher',
                current_language: 'Current Language',
                translation_stats: 'Translation Statistics',
                navigation_section: 'Navigation Menu',
                auth_section: 'Authentication Forms',
                citizen_section: 'Citizen Management',
                dashboard_section: 'Dashboard Elements',
                total_translations: 'Total Translations',
                languages_supported: 'Languages Supported',
                components_updated: 'Components Updated',
                coverage_percentage: 'Coverage Percentage'
            },
            
            am: {
                // Navigation
                dashboard: 'ዳሽቦርድ',
                citizens: 'ዜጎች',
                id_cards: 'መታወቂያ ካርዶች',
                clearance: 'ፍቃድ',
                transfer: 'ዝውውር',
                print_queue: 'የህትመት ወረፋ',
                reports: 'ሪፖርቶች',
                tenants: 'ተከራዮች',
                system_users: 'የስርዓት ተጠቃሚዎች',
                
                // Authentication
                login: 'ግባ',
                logout: 'ውጣ',
                sign_in: 'ግባ',
                register: 'ይመዝገቡ',
                username: 'የተጠቃሚ ስም',
                password: 'የይለፍ ቃል',
                email: 'ኢሜይል',
                remember_me: 'አስታውሰኝ',
                forgot_password: 'የይለፍ ቃል ረሳሁ?',
                
                // Citizen Management
                add_citizen: 'ዜጋ ጨምር',
                edit_citizen: 'ዜጋን አርም',
                citizen_registration: 'የዜጋ ምዝገባ',
                first_name: 'ስም',
                last_name: 'የአባት ስም',
                full_name: 'ሙሉ ስም',
                date_of_birth: 'የተወለደበት ቀን',
                gender: 'ጾታ',
                male: 'ወንድ',
                female: 'ሴት',
                phone_number: 'ስልክ ቁጥር',
                address: 'አድራሻ',
                
                // Actions
                save: 'አስቀምጥ',
                cancel: 'ሰርዝ',
                submit: 'አስገባ',
                edit: 'አርም',
                delete: 'ሰርዝ',
                view: 'ይመልከቱ',
                search: 'ፈልግ',
                filter: 'ማጣሪያ',
                
                // Status
                pending: 'በመጠባበቅ ላይ',
                approved: 'ጸድቋል',
                rejected: 'ተቀባይነት አላገኘም',
                success: 'ተሳክቷል',
                error: 'ስህተት',
                loading: 'በመጫን ላይ...',
                
                // Dashboard
                total_citizens: 'ጠቅላላ ዜጎች',
                new_registrations: 'አዲስ ምዝገባዎች',
                pending_approvals: 'በመጠባበቅ ላይ ያሉ ጽድቆች',
                
                // Demo specific
                demo_title: 'የGoID ትርጉም ስርዓት ማሳያ',
                demo_subtitle: 'ለኢትዮጵያ መታወቂያ አስተዳደር ሁለት ቋንቋ ድጋፍ',
                language_switcher: 'ቋንቋ መቀየሪያ',
                current_language: 'የአሁኑ ቋንቋ',
                translation_stats: 'የትርጉም አወሳሰን',
                navigation_section: 'የአሰሳ ዝርዝር',
                auth_section: 'የመግቢያ ፎርሞች',
                citizen_section: 'የዜጋ አስተዳደር',
                dashboard_section: 'የዳሽቦርድ አካላት',
                total_translations: 'ጠቅላላ ትርጉሞች',
                languages_supported: 'የሚደገፉ ቋንቋዎች',
                components_updated: 'የተሻሻሉ አካላት',
                coverage_percentage: 'የሽፋን መቶኛ'
            }
        };

        const TranslationDemo = () => {
            const [currentLanguage, setCurrentLanguage] = React.useState('en');
            
            const t = (key, defaultValue = key) => {
                return translations[currentLanguage][key] || defaultValue;
            };
            
            const changeLanguage = (lang) => {
                setCurrentLanguage(lang);
            };
            
            const navigationItems = [
                'dashboard', 'citizens', 'id_cards', 'clearance', 
                'transfer', 'print_queue', 'reports', 'tenants', 'system_users'
            ];
            
            const authItems = [
                'login', 'register', 'username', 'password', 'email', 
                'remember_me', 'forgot_password'
            ];
            
            const citizenItems = [
                'add_citizen', 'edit_citizen', 'citizen_registration', 
                'first_name', 'last_name', 'date_of_birth', 'gender', 
                'male', 'female', 'phone_number', 'address'
            ];
            
            const dashboardItems = [
                'total_citizens', 'new_registrations', 'pending_approvals'
            ];
            
            const actionItems = [
                'save', 'cancel', 'submit', 'edit', 'delete', 'view', 
                'search', 'filter'
            ];
            
            return (
                <div className="container">
                    <div className="header">
                        <h1>{t('demo_title')}</h1>
                        <div className="subtitle">{t('demo_subtitle')}</div>
                        
                        <div className="language-switcher">
                            <button 
                                className={`language-btn ${currentLanguage === 'en' ? 'active' : ''}`}
                                onClick={() => changeLanguage('en')}
                            >
                                English
                            </button>
                            <button 
                                className={`language-btn ${currentLanguage === 'am' ? 'active' : ''}`}
                                onClick={() => changeLanguage('am')}
                            >
                                አማርኛ
                            </button>
                        </div>
                        
                        <div style={{marginTop: '20px', fontSize: '1.1em'}}>
                            <strong>{t('current_language')}:</strong> {currentLanguage === 'en' ? 'English' : 'አማርኛ (Amharic)'}
                        </div>
                    </div>
                    
                    <div className="demo-sections">
                        <div className="demo-section">
                            <h2>{t('translation_stats')}</h2>
                            <div className="stats">
                                <div className="stat-card">
                                    <span className="stat-number">200+</span>
                                    <div className="stat-label">{t('total_translations')}</div>
                                </div>
                                <div className="stat-card">
                                    <span className="stat-number">2</span>
                                    <div className="stat-label">{t('languages_supported')}</div>
                                </div>
                                <div className="stat-card">
                                    <span className="stat-number">15+</span>
                                    <div className="stat-label">{t('components_updated')}</div>
                                </div>
                                <div className="stat-card">
                                    <span className="stat-number">95%</span>
                                    <div className="stat-label">{t('coverage_percentage')}</div>
                                </div>
                            </div>
                        </div>
                        
                        <div className="demo-grid">
                            <div className="demo-card">
                                <h3>{t('navigation_section')}</h3>
                                {navigationItems.map(item => (
                                    <div key={item} className="translation-item">
                                        <span className="translation-key">{item}</span>
                                        <span className={`translation-value ${currentLanguage === 'am' ? 'amharic' : ''}`}>
                                            {t(item)}
                                        </span>
                                    </div>
                                ))}
                            </div>
                            
                            <div className="demo-card">
                                <h3>{t('auth_section')}</h3>
                                {authItems.map(item => (
                                    <div key={item} className="translation-item">
                                        <span className="translation-key">{item}</span>
                                        <span className={`translation-value ${currentLanguage === 'am' ? 'amharic' : ''}`}>
                                            {t(item)}
                                        </span>
                                    </div>
                                ))}
                            </div>
                            
                            <div className="demo-card">
                                <h3>{t('citizen_section')}</h3>
                                {citizenItems.map(item => (
                                    <div key={item} className="translation-item">
                                        <span className="translation-key">{item}</span>
                                        <span className={`translation-value ${currentLanguage === 'am' ? 'amharic' : ''}`}>
                                            {t(item)}
                                        </span>
                                    </div>
                                ))}
                            </div>
                            
                            <div className="demo-card">
                                <h3>{t('dashboard_section')}</h3>
                                {dashboardItems.map(item => (
                                    <div key={item} className="translation-item">
                                        <span className="translation-key">{item}</span>
                                        <span className={`translation-value ${currentLanguage === 'am' ? 'amharic' : ''}`}>
                                            {t(item)}
                                        </span>
                                    </div>
                                ))}
                                <div style={{marginTop: '20px', fontWeight: 'bold'}}>
                                    <div className="translation-item">
                                        <span>{t('pending')}</span>
                                        <span className={currentLanguage === 'am' ? 'amharic' : ''}>{t('approved')}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div className="footer">
                        <p>GoID Translation System - Complete Bilingual Support for Ethiopian Context</p>
                        <p>English ↔ አማርኛ (Amharic) Translation Coverage</p>
                    </div>
                </div>
            );
        };

        ReactDOM.render(<TranslationDemo />, document.getElementById('root'));
    </script>
</body>
</html>
