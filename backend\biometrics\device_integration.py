"""
Futronic FS88H fingerprint device integration.
This module handles communication with the Futronic FS88H fingerprint scanner.
"""

import logging
import json
import base64
import time
from typing import Dict, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class FingerprintTemplate:
    """Data class for fingerprint template"""
    thumb_type: str  # 'left' or 'right'
    template_data: str  # Base64 encoded template
    quality_score: int  # 0-100
    minutiae_count: int
    capture_time: str
    device_info: Dict


@dataclass
class DeviceInfo:
    """Device information"""
    model: str = "Futronic FS88H"
    serial_number: str = "FS88H001"
    firmware_version: str = "1.2.3"
    sdk_version: str = "4.2.1"
    is_connected: bool = False


# Simulation class removed - only real device support


# Global device instance
_device_instance = None


def get_device():
    """Get the global real device instance - no simulation mode"""
    global _device_instance
    if _device_instance is None:
        # Initialize real Futronic device only
        try:
            from .futronic_real_device import get_real_device
            real_device = get_real_device()
            if real_device.initialized and real_device.connected:
                logger.info("✅ Using real Futronic FS88H device")
                _device_instance = RealDeviceWrapper(real_device)
            else:
                logger.error("❌ Real Futronic device not available")
                logger.error("Please ensure:")
                logger.error("  - Futronic FS88H device is connected via USB")
                logger.error("  - Device drivers are installed")
                logger.error("  - SDK files are in local-biometric-service/fingerPrint folder")
                raise Exception("Real Futronic device initialization failed")
        except Exception as e:
            logger.error(f"❌ Failed to initialize real device: {e}")
            raise Exception(f"Biometric device unavailable: {e}")

    return _device_instance


class RealDeviceWrapper:
    """Wrapper to make real device compatible with existing interface"""

    def __init__(self, real_device):
        self.real_device = real_device
        self.device_connected = real_device.connected
        self.device_initialized = real_device.initialized

    def initialize(self) -> bool:
        """Initialize wrapper"""
        return self.real_device.initialize()

    def capture_fingerprint(self, thumb_type: str) -> Optional[FingerprintTemplate]:
        """Capture using real device"""
        return self.real_device.capture_fingerprint(thumb_type)

    def get_device_status(self) -> dict:
        """Get real device status"""
        status = self.real_device.get_device_status()
        return {
            'connected': status['connected'],
            'initialized': status['initialized'],
            'model': status['model'],
            'interface': status.get('interface', 'futronic_sdk'),
            'real_device': True,
            'timestamp': datetime.now().isoformat()
        }

    def match_templates(self, template1: str, template2: str) -> Tuple[bool, float]:
        """Match two fingerprint templates using real device/processor"""
        try:
            # Use the proper fingerprint processor for matching
            from .fingerprint_processing import FingerprintProcessor

            processor = FingerprintProcessor()
            is_match, confidence = processor._compare_templates(template1, template2)

            logger.info(f"🔍 Template matching result: {is_match} (confidence: {confidence:.3f})")
            return is_match, confidence

        except Exception as e:
            logger.error(f"Template matching failed: {e}")
            # Fallback to exact match for safety
            if template1 == template2:
                return True, 1.0
            return False, 0.0

    def validate_quality(self, template: FingerprintTemplate) -> Tuple[bool, str]:
        """Validate fingerprint template quality"""
        min_quality_score = 70  # Real device minimum quality
        min_minutiae_count = 25  # Real device minimum minutiae
        
        if template.quality_score < min_quality_score:
            return False, f"Quality score {template.quality_score}% is below minimum {min_quality_score}%"
        
        if template.minutiae_count < min_minutiae_count:
            return False, f"Minutiae count {template.minutiae_count} is below minimum {min_minutiae_count}"
        
        return True, "Quality validation passed"

    def process_template(self, raw_data: str, thumb_type: str) -> FingerprintTemplate:
        """Process raw fingerprint data - not typically needed for real device"""
        raise NotImplementedError("Real device processes fingerprints during capture")

    def disconnect(self):
        """Disconnect real device"""
        self.real_device.disconnect()
