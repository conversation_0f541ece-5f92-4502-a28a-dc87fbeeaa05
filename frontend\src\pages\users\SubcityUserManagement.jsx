import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from '../../utils/axios';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Divider,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Tooltip,
  Menu,
  Tabs,
  Tab,
  Autocomplete,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  Business as BusinessIcon,
  PersonAdd as PersonAddIcon,
  People as PeopleIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Home as HomeIcon,
  Lock as LockIcon,
  MoreVert as MoreVertIcon,
  Security as SecurityIcon,
  Assignment as AssignmentIcon,
  VpnKey as VpnKeyIcon,
  Add as AddIcon,
  Refresh as RefreshIcon,
  Visibility as VisibilityIcon,
  Person as PersonIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { usePermissions } from '../../hooks/usePermissions';

const SubcityUserManagement = () => {
  const { user } = useAuth();
  const { hasPermission } = usePermissions();
  const navigate = useNavigate();

  // State management
  const [subcities, setSubcities] = useState([]);
  const [selectedSubcity, setSelectedSubcity] = useState(null);
  const [subcityUsers, setSubcityUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [tabValue, setTabValue] = useState(0);

  // User management dialogs
  const [createUserDialogOpen, setCreateUserDialogOpen] = useState(false);
  const [editUserDialogOpen, setEditUserDialogOpen] = useState(false);
  const [passwordDialogOpen, setPasswordDialogOpen] = useState(false);
  const [creating, setCreating] = useState(false);
  const [editing, setEditing] = useState(false);
  const [changingPassword, setChangingPassword] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [passwordUser, setPasswordUser] = useState(null);
  const [userMenuAnchor, setUserMenuAnchor] = useState(null);
  const [selectedUserForMenu, setSelectedUserForMenu] = useState(null);

  // Group management state
  const [tenantGroups, setTenantGroups] = useState([]);
  const [groupTemplates, setGroupTemplates] = useState([]);
  const [permissions, setPermissions] = useState([]);
  const [groupsWithUsers, setGroupsWithUsers] = useState([]);
  const [selectedUserDetails, setSelectedUserDetails] = useState(null);
  const [userDetailsDialogOpen, setUserDetailsDialogOpen] = useState(false);
  const [groupDetailsDialogOpen, setGroupDetailsDialogOpen] = useState(false);
  const [selectedGroupForManagement, setSelectedGroupForManagement] = useState(null);
  const [groupPermissions, setGroupPermissions] = useState([]);
  const [groupUsers, setGroupUsers] = useState([]);
  const [createGroupDialogOpen, setCreateGroupDialogOpen] = useState(false);
  const [assignGroupDialogOpen, setAssignGroupDialogOpen] = useState(false);
  const [grantPermissionDialogOpen, setGrantPermissionDialogOpen] = useState(false);
  const [editGroupDialogOpen, setEditGroupDialogOpen] = useState(false);
  const [editingGroup, setEditingGroup] = useState(null);
  const [groupLoading, setGroupLoading] = useState(false);
  const [apiStatus, setApiStatus] = useState('unknown');

  // Form state
  const [newUser, setNewUser] = useState({
    username: '',
    email: '',
    first_name: '',
    last_name: '',
    role: 'subcity_admin',
    primary_group_id: '',
    password: '',
    phone_number: ''
  });

  const [editUser, setEditUser] = useState({
    username: '',
    email: '',
    first_name: '',
    last_name: '',
    role: 'subcity_admin',
    primary_group_id: '',
    phone_number: ''
  });

  const [passwordForm, setPasswordForm] = useState({
    new_password: '',
    confirm_password: ''
  });

  // Group management form state
  const [groupForm, setGroupForm] = useState({
    // Group creation
    group_name: '',
    description: '',
    group_type: 'custom',
    level: 10,
    permission_codenames: [],
    template_id: '',
    // Group assignment
    user_email: '',
    user_emails: [], // For multiple user selection
    group_id: '',
    is_primary: false,
    // Permission granting
    permission_codename: '',
    reason: ''
  });

  // Form validation state
  const [formErrors, setFormErrors] = useState({
    group_name: false,
    description: false,
    user_email: false,
    group_id: false,
    permission_codename: false
  });

  useEffect(() => {
    fetchSubcities();
  }, []);

  useEffect(() => {
    if (selectedSubcity) {
      fetchSubcityUsers(selectedSubcity.id);
      fetchTenantGroups(selectedSubcity.id);
    }
  }, [selectedSubcity]);

  useEffect(() => {
    fetchPermissions();
    fetchGroupTemplates();
    testApiConnectivity();
  }, []);

  const fetchSubcities = async () => {
    try {
      setLoading(true);
      setError('');

      // Fetch subcities under the current city
      const response = await axios.get('/api/tenants/', {
        params: {
          type: 'subcity',
          parent: user.tenant?.id
        }
      });

      setSubcities(response.data.results || []);
    } catch (error) {
      console.error('Failed to fetch subcities:', error);
      setError('Failed to load subcities. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchSubcityUsers = async (subcityId) => {
    try {
      const response = await axios.get(`/api/tenants/${subcityId}/users/`);
      console.log('🔍 Fetched subcity users:', response.data);
      setSubcityUsers(response.data || []);
    } catch (error) {
      console.error('Failed to fetch subcity users:', error);
      setSubcityUsers([]);
    }
  };

  const fetchTenantGroups = async (tenantId) => {
    try {
      const response = await axios.get(`/api/auth/group-management/tenant_groups/?tenant_id=${tenantId}`);
      setTenantGroups(response.data.groups || []);
    } catch (error) {
      console.error('Failed to fetch tenant groups:', error);
      // Fallback to mock data for testing
      const mockGroups = [
        {
          id: 1,
          name: 'SubCity Admins',
          description: 'SubCity administration',
          group_type: 'administrative',
          level: 40,
          users_count: 2,
          permissions_count: 12,
          tenant: { name: selectedSubcity?.name }
        },
        {
          id: 2,
          name: 'SubCity Coordinators',
          description: 'SubCity coordination',
          group_type: 'functional',
          level: 30,
          users_count: 1,
          permissions_count: 8,
          tenant: { name: selectedSubcity?.name }
        }
      ];
      setTenantGroups(mockGroups);
    }
  };

  const fetchGroupTemplates = async () => {
    try {
      const response = await axios.get('/api/auth/group-management/group_templates/');
      setGroupTemplates(response.data.templates || []);
    } catch (error) {
      console.error('Failed to fetch group templates:', error);
      // Fallback to mock data for testing
      setGroupTemplates([
        { id: 1, name: 'SubCity Admin', description: 'SubCity administration permissions', group_type: 'administrative', level: 40, permissions_count: 12 },
        { id: 2, name: 'SubCity Coordinator', description: 'SubCity coordination permissions', group_type: 'functional', level: 30, permissions_count: 8 },
        { id: 3, name: 'Service Manager', description: 'Service management', group_type: 'functional', level: 25, permissions_count: 6 },
        { id: 4, name: 'Data Analyst', description: 'Data analysis operations', group_type: 'operational', level: 20, permissions_count: 5 }
      ]);
    }
  };

  const fetchPermissions = async () => {
    try {
      const response = await axios.get('/api/auth/group-management/available_permissions/');
      console.log('Available permissions from group management:', response.data);

      // Deduplicate permissions by codename to avoid duplicate keys
      const rawPermissions = response.data.permissions || [];
      const uniquePermissions = rawPermissions.filter((permission, index, self) =>
        index === self.findIndex(p => p.codename === permission.codename)
      );
      setPermissions(uniquePermissions);
    } catch (error) {
      console.error('Failed to fetch permissions from group management:', error);
      // Fallback to the regular permissions endpoint
      try {
        const fallbackResponse = await axios.get('/api/auth/permissions/');
        console.log('Fallback permissions:', fallbackResponse.data);

        const rawFallbackPermissions = fallbackResponse.data.results || fallbackResponse.data || [];
        const uniqueFallbackPermissions = rawFallbackPermissions.filter((permission, index, self) =>
          index === self.findIndex(p => p.codename === permission.codename)
        );
        setPermissions(uniqueFallbackPermissions);
      } catch (fallbackErr) {
        console.error('Failed to fetch fallback permissions:', fallbackErr);
        setPermissions([]);
      }
    }
  };

  const testApiConnectivity = async () => {
    try {
      await axios.get('/api/auth/group-management/available_permissions/');
      setApiStatus('connected');
    } catch (error) {
      console.error('API connectivity test failed:', error);
      setApiStatus('disconnected');
    }
  };

  const handleCreateUser = async () => {
    if (!selectedSubcity) return;

    try {
      setCreating(true);

      // Create username with domain if not already present
      let username = newUser.username;

      // Get the subcity's domain
      let subcityDomain = selectedSubcity.primary_domain;
      if (!subcityDomain && selectedSubcity.schema_name) {
        subcityDomain = `${selectedSubcity.schema_name}.goid.local`;
      }

      if (subcityDomain && !username.includes('@')) {
        username = `${username}@${subcityDomain}`;
      }

      // Determine role based on selected group
      let userRole = 'subcity_admin'; // Default role for subcity
      if (newUser.primary_group_id) {
        const selectedGroup = tenantGroups.find(g => g.id === parseInt(newUser.primary_group_id));
        if (selectedGroup) {
          // Map group types to roles
          if (selectedGroup.name.toLowerCase().includes('admin')) {
            userRole = 'subcity_admin';
          }
          // Add more role mappings as needed
        }
      }

      const userData = {
        ...newUser,
        username,
        password2: newUser.password,
        tenant: selectedSubcity.id,
        role: userRole
      };

      console.log('🔍 Creating subcity user with data:', userData);
      console.log('🔍 Target subcity:', selectedSubcity);

      await axios.post(`/api/tenants/${selectedSubcity.id}/create_user/`, userData);

      // Refresh users list
      fetchSubcityUsers(selectedSubcity.id);

      // Reset form and close dialog
      setNewUser({
        username: '',
        email: '',
        first_name: '',
        last_name: '',
        role: 'subcity_admin',
        primary_group_id: '',
        password: '',
        phone_number: ''
      });
      setCreateUserDialogOpen(false);
      setSuccess('User created successfully!');

    } catch (error) {
      console.error('Failed to create user:', error);
      console.error('Error response:', error.response?.data);

      if (error.response?.data) {
        const errorData = error.response.data;
        if (typeof errorData === 'object' && !errorData.detail) {
          const errorMessages = Object.entries(errorData)
            .map(([field, messages]) => `${field}: ${Array.isArray(messages) ? messages.join(', ') : messages}`)
            .join('\n');
          setError(`Validation errors:\n${errorMessages}`);
        } else {
          setError(errorData.detail || 'Failed to create user. Please try again.');
        }
      } else {
        setError('Failed to create user. Please try again.');
      }
    } finally {
      setCreating(false);
    }
  };

  const handleInputChange = (field, value) => {
    setNewUser(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleEditInputChange = (field, value) => {
    setEditUser(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleEditUser = (user) => {
    setEditingUser(user);
    setEditUser({
      username: user.username,
      email: user.email,
      first_name: user.first_name,
      last_name: user.last_name,
      role: user.role,
      primary_group_id: user.primary_group_id || '',
      phone_number: user.phone_number || ''
    });
    setEditUserDialogOpen(true);
  };

  const handleUpdateUser = async () => {
    if (!selectedSubcity || !editingUser) return;

    try {
      setEditing(true);

      const updateData = {
        user_id: editingUser.id,
        ...editUser
      };

      await axios.patch(`/api/tenants/${selectedSubcity.id}/update_user/`, updateData);

      // Refresh users list
      fetchSubcityUsers(selectedSubcity.id);

      // Close dialog and reset state
      setEditUserDialogOpen(false);
      setEditingUser(null);
      setSuccess('User updated successfully!');

    } catch (error) {
      console.error('Failed to update user:', error);
      setError('Failed to update user. Please try again.');
    } finally {
      setEditing(false);
    }
  };

  const handlePasswordInputChange = (field, value) => {
    setPasswordForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleChangePassword = (user) => {
    setPasswordUser(user);
    setPasswordForm({
      new_password: '',
      confirm_password: ''
    });
    setPasswordDialogOpen(true);
    setUserMenuAnchor(null);
  };

  const handleUpdatePassword = async () => {
    if (!selectedSubcity || !passwordUser) return;

    if (passwordForm.new_password !== passwordForm.confirm_password) {
      setError('Passwords do not match');
      return;
    }

    if (passwordForm.new_password.length < 8) {
      setError('Password must be at least 8 characters long');
      return;
    }

    try {
      setChangingPassword(true);

      const passwordData = {
        user_id: passwordUser.id,
        new_password: passwordForm.new_password,
        confirm_password: passwordForm.confirm_password
      };

      await axios.patch(`/api/tenants/${selectedSubcity.id}/change_user_password/`, passwordData);

      setPasswordDialogOpen(false);
      setPasswordUser(null);
      setPasswordForm({
        new_password: '',
        confirm_password: ''
      });
      setSuccess('Password changed successfully!');

    } catch (error) {
      console.error('Failed to change password:', error);
      setError('Failed to change password. Please try again.');
    } finally {
      setChangingPassword(false);
    }
  };

  const handleUserMenuClick = (event, user) => {
    setUserMenuAnchor(event.currentTarget);
    setSelectedUserForMenu(user);
  };

  const handleUserMenuClose = () => {
    setUserMenuAnchor(null);
    setSelectedUserForMenu(null);
  };

  // Group Management Functions
  const fetchGroupsWithUsers = async (tenantId) => {
    try {
      setGroupLoading(true);
      const response = await axios.get(`/api/auth/group-management/groups_with_users/?tenant_id=${tenantId}`);
      setGroupsWithUsers(response.data.groups || []);
    } catch (error) {
      console.error('Failed to fetch groups with users:', error);
      setGroupsWithUsers([]);
    } finally {
      setGroupLoading(false);
    }
  };

  const handleCreateGroup = async () => {
    if (!validateGroupForm() || !selectedSubcity) {
      setError('Please fill in required fields');
      return;
    }

    setGroupLoading(true);
    try {
      let groupData;

      if (groupForm.template_id) {
        // Create from template
        groupData = {
          action: 'create_from_template',
          template_id: parseInt(groupForm.template_id),
          group_name: groupForm.group_name,
          description: groupForm.description,
          tenant_id: selectedSubcity.id
        };
      } else {
        // Create custom group
        groupData = {
          action: 'create_group',
          group_name: groupForm.group_name,
          description: groupForm.description,
          group_type: groupForm.group_type,
          level: parseInt(groupForm.level),
          tenant_id: selectedSubcity.id
        };
      }

      console.log('🔍 Creating group with data:', groupData);
      const response = await axios.post('/api/auth/group-management/manage_group/', groupData);

      // Assign permissions if specified and not using template
      if (!groupForm.template_id && groupForm.permission_codenames.length > 0 && response.data.group?.id) {
        try {
          await handleAssignPermissionsToGroup(response.data.group.id, groupForm.permission_codenames);
        } catch (permError) {
          console.warn('Failed to assign additional permissions:', permError);
        }
      }

      setCreateGroupDialogOpen(false);
      resetGroupForm();
      fetchTenantGroups(selectedSubcity.id);
      setError('');
      setSuccess(response.data.message || 'Group created successfully!');
    } catch (error) {
      console.error('Failed to create group:', error);
      let errorMessage = 'Failed to create group';

      if (error.response?.status === 500) {
        errorMessage = 'Server error: Please check if the backend server is running and the database is properly configured.';
      } else if (error.response?.status === 404) {
        errorMessage = 'API endpoint not found. Please check if the group management API is properly configured.';
      } else if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      } else if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      }

      setError(errorMessage);
    } finally {
      setGroupLoading(false);
    }
  };

  const handleAssignPermissionsToGroup = async (groupId, permissionCodenames) => {
    try {
      await axios.post('/api/auth/group-management/manage_group/', {
        action: 'assign_permissions',
        group_id: groupId,
        permission_codenames: permissionCodenames
      });
    } catch (error) {
      console.error('Failed to assign permissions to group:', error);
      throw error;
    }
  };

  const validateGroupForm = () => {
    const errors = {
      group_name: !groupForm.group_name.trim(),
      description: !groupForm.description.trim(),
      user_email: false,
      group_id: false,
      permission_codename: false
    };

    setFormErrors(errors);
    return !Object.values(errors).some(error => error);
  };

  const resetGroupForm = () => {
    setGroupForm({
      group_name: '',
      description: '',
      group_type: 'custom',
      level: 10,
      permission_codenames: [],
      template_id: '',
      user_email: '',
      user_emails: [],
      group_id: '',
      is_primary: false,
      permission_codename: '',
      reason: ''
    });
    setFormErrors({
      group_name: false,
      description: false,
      user_email: false,
      group_id: false,
      permission_codename: false
    });
  };

  const openCreateGroupDialog = () => {
    resetGroupForm();
    setCreateGroupDialogOpen(true);
  };

  const getRoleColor = (role) => {
    switch (role) {
      case 'subcity_admin':
        return 'primary';
      case 'custom':
        return 'secondary';
      default:
        return 'default';
    }
  };

  const getRoleIcon = (role) => {
    switch (role) {
      case 'subcity_admin':
        return <SecurityIcon />;
      default:
        return <PersonIcon />;
    }
  };

  // Check permissions
  if (!hasPermission('create_subcity_users')) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          You don't have permission to manage subcity users.
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" component="h1">
          Subcity User Management
        </Typography>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={fetchSubcities}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>

      {/* Error/Success Messages */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}
      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      {/* Loading State */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      )}

      {/* Subcities Grid */}
      {!loading && (
        <Grid container spacing={3}>
          {subcities.map((subcity) => (
            <Grid item xs={12} md={6} lg={4} key={subcity.id}>
              <Card
                sx={{
                  cursor: 'pointer',
                  border: selectedSubcity?.id === subcity.id ? 2 : 1,
                  borderColor: selectedSubcity?.id === subcity.id ? 'primary.main' : 'divider'
                }}
                onClick={() => setSelectedSubcity(subcity)}
              >
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                      <BusinessIcon />
                    </Avatar>
                    <Box>
                      <Typography variant="h6">{subcity.name}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        {subcity.schema_name}
                      </Typography>
                    </Box>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    Click to manage users
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Selected Subcity Management */}
      {selectedSubcity && (
        <Paper sx={{ mt: 3, p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h5">
              Managing {selectedSubcity.name}
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={() => {
                  fetchSubcityUsers(selectedSubcity.id);
                  fetchTenantGroups(selectedSubcity.id);
                }}
              >
                Refresh
              </Button>
              {tabValue === 0 && (
                <Button
                  variant="contained"
                  startIcon={<PersonAddIcon />}
                  onClick={() => setCreateUserDialogOpen(true)}
                >
                  Add User
                </Button>
              )}
              {tabValue === 1 && (
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={openCreateGroupDialog}
                >
                  Create Group
                </Button>
              )}
            </Box>
          </Box>

          {/* Tabs */}
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
            <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
              <Tab
                label={`Users (${subcityUsers.length})`}
                icon={<PeopleIcon />}
                iconPosition="start"
              />
              <Tab
                label={`Groups (${tenantGroups.length})`}
                icon={<SecurityIcon />}
                iconPosition="start"
              />
            </Tabs>
          </Box>

          {/* Users Tab */}
          {tabValue === 0 && (
            <Box>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>User</TableCell>
                      <TableCell>Email</TableCell>
                      <TableCell>Role</TableCell>
                      <TableCell>Group</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {subcityUsers.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Avatar sx={{ mr: 2, bgcolor: 'secondary.main' }}>
                              {getRoleIcon(user.role)}
                            </Avatar>
                            <Box>
                              <Typography variant="body2" fontWeight="bold">
                                {user.first_name} {user.last_name}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                @{user.username}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>
                          <Chip
                            label={user.role}
                            color={getRoleColor(user.role)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {user.primary_group_name || 'No group'}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <IconButton
                            onClick={(e) => handleUserMenuClick(e, user)}
                            size="small"
                          >
                            <MoreVertIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {subcityUsers.length === 0 && (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Typography variant="body1" color="text.secondary">
                    No users found in this subcity
                  </Typography>
                </Box>
              )}
            </Box>
          )}

          {/* Groups Tab */}
          {tabValue === 1 && (
            <Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6">
                  Group Management
                </Typography>
              </Box>

              {/* Group Management Actions */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} sm={4}>
                  <Card sx={{ height: '100%', border: 1, borderColor: 'divider' }}>
                    <CardContent sx={{ textAlign: 'center', py: 3 }}>
                      <AddIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                      <Typography variant="h6" gutterBottom>
                        Create Group
                      </Typography>
                      <Typography variant="body2" color="textSecondary" paragraph>
                        Create a group for this subcity from templates or custom permissions.
                      </Typography>
                      <Button
                        variant="contained"
                        fullWidth
                        onClick={openCreateGroupDialog}
                      >
                        Create Group
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>

              {/* Groups Table */}
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Group Name</TableCell>
                      <TableCell>Type</TableCell>
                      <TableCell>Level</TableCell>
                      <TableCell>Users</TableCell>
                      <TableCell>Permissions</TableCell>
                      <TableCell align="center">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {tenantGroups.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} align="center">
                          <Typography variant="body2" color="textSecondary">
                            No groups available. Create groups from templates or check API connection.
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ) : (
                      tenantGroups.map((group) => (
                        <TableRow key={group.id} hover>
                          <TableCell>
                            <Box>
                              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                {group.name}
                              </Typography>
                              <Typography variant="caption" color="textSecondary">
                                {group.description}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={group.group_type}
                              size="small"
                              color={
                                group.group_type === 'administrative' ? 'success' :
                                group.group_type === 'functional' ? 'warning' : 'info'
                              }
                            />
                          </TableCell>
                          <TableCell>{group.level}</TableCell>
                          <TableCell>{group.users_count || 0}</TableCell>
                          <TableCell>{group.permissions_count || 0}</TableCell>
                          <TableCell align="center">
                            <Tooltip title="Manage Group">
                              <IconButton size="small">
                                <MoreVertIcon />
                              </IconButton>
                            </Tooltip>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}
        </Paper>
      )}

      {/* User Menu */}
      <Menu
        anchorEl={userMenuAnchor}
        open={Boolean(userMenuAnchor)}
        onClose={handleUserMenuClose}
      >
        <MenuItem onClick={() => {
          handleEditUser(selectedUserForMenu);
          handleUserMenuClose();
        }}>
          <EditIcon sx={{ mr: 1 }} />
          Edit User
        </MenuItem>
        <MenuItem onClick={() => {
          handleChangePassword(selectedUserForMenu);
          handleUserMenuClose();
        }}>
          <LockIcon sx={{ mr: 1 }} />
          Change Password
        </MenuItem>
      </Menu>

      {/* Create User Dialog */}
      <Dialog
        open={createUserDialogOpen}
        onClose={() => setCreateUserDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Add New User to {selectedSubcity?.name}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Username"
                value={newUser.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={newUser.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="First Name"
                value={newUser.first_name}
                onChange={(e) => handleInputChange('first_name', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Last Name"
                value={newUser.last_name}
                onChange={(e) => handleInputChange('last_name', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone Number"
                value={newUser.phone_number}
                onChange={(e) => handleInputChange('phone_number', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Primary Group</InputLabel>
                <Select
                  value={newUser.primary_group_id}
                  onChange={(e) => handleInputChange('primary_group_id', e.target.value)}
                  label="Primary Group"
                >
                  <MenuItem value="">
                    <em>No Group</em>
                  </MenuItem>
                  {tenantGroups.map((group) => (
                    <MenuItem key={group.id} value={group.id}>
                      {group.name} ({group.group_type})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Password"
                type="password"
                value={newUser.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                required
                helperText="Minimum 8 characters"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateUserDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleCreateUser}
            variant="contained"
            disabled={creating}
          >
            {creating ? <CircularProgress size={20} /> : 'Create User'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit User Dialog */}
      <Dialog
        open={editUserDialogOpen}
        onClose={() => setEditUserDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Edit User: {editingUser?.first_name} {editingUser?.last_name}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Username"
                value={editUser.username}
                onChange={(e) => handleEditInputChange('username', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={editUser.email}
                onChange={(e) => handleEditInputChange('email', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="First Name"
                value={editUser.first_name}
                onChange={(e) => handleEditInputChange('first_name', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Last Name"
                value={editUser.last_name}
                onChange={(e) => handleEditInputChange('last_name', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone Number"
                value={editUser.phone_number}
                onChange={(e) => handleEditInputChange('phone_number', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Primary Group</InputLabel>
                <Select
                  value={editUser.primary_group_id}
                  onChange={(e) => handleEditInputChange('primary_group_id', e.target.value)}
                  label="Primary Group"
                >
                  <MenuItem value="">
                    <em>No Group</em>
                  </MenuItem>
                  {tenantGroups.map((group) => (
                    <MenuItem key={group.id} value={group.id}>
                      {group.name} ({group.group_type})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditUserDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleUpdateUser}
            variant="contained"
            disabled={editing}
          >
            {editing ? <CircularProgress size={20} /> : 'Update User'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Change Password Dialog */}
      <Dialog
        open={passwordDialogOpen}
        onClose={() => setPasswordDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Change Password for {passwordUser?.first_name} {passwordUser?.last_name}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="New Password"
                type="password"
                value={passwordForm.new_password}
                onChange={(e) => handlePasswordInputChange('new_password', e.target.value)}
                required
                helperText="Minimum 8 characters"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Confirm Password"
                type="password"
                value={passwordForm.confirm_password}
                onChange={(e) => handlePasswordInputChange('confirm_password', e.target.value)}
                required
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPasswordDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleUpdatePassword}
            variant="contained"
            disabled={changingPassword}
          >
            {changingPassword ? <CircularProgress size={20} /> : 'Change Password'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Create Group Dialog */}
      <Dialog open={createGroupDialogOpen} onClose={() => setCreateGroupDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Create Group for {selectedSubcity?.name}</DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="body2">
              This group will be specific to <strong>{selectedSubcity?.name}</strong> and can only be assigned to users in this subcity.
              You can create a group from a template or create a custom group with specific permissions.
            </Typography>
          </Alert>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            {/* Template Selection */}
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Create from Template (Optional)</InputLabel>
                <Select
                  value={groupForm.template_id}
                  onChange={(e) => {
                    const templateId = e.target.value;
                    setGroupForm({ ...groupForm, template_id: templateId });

                    // Auto-fill form if template is selected
                    if (templateId) {
                      const template = groupTemplates.find(t => t.id === parseInt(templateId));
                      if (template) {
                        setGroupForm(prev => ({
                          ...prev,
                          template_id: templateId,
                          group_name: template.name,
                          description: template.description,
                          group_type: template.group_type,
                          level: template.level
                        }));
                      }
                    }
                  }}
                  label="Create from Template (Optional)"
                >
                  <MenuItem value="">
                    <em>Create Custom Group</em>
                  </MenuItem>
                  {groupTemplates.map((template) => (
                    <MenuItem key={template.id} value={template.id}>
                      <Box>
                        <Typography variant="body2">{template.name}</Typography>
                        <Typography variant="caption" color="textSecondary">
                          {template.description} • {template.permissions_count} permissions
                        </Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <Typography variant="caption" color="textSecondary" sx={{ mt: 0.5, display: 'block' }}>
                Templates provide pre-configured groups with appropriate permissions for common roles.
              </Typography>
            </Grid>

            {/* Group Name */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Group Name"
                value={groupForm.group_name}
                onChange={(e) => setGroupForm({ ...groupForm, group_name: e.target.value })}
                error={formErrors.group_name}
                helperText={formErrors.group_name ? 'Group name is required' : ''}
                required
              />
            </Grid>

            {/* Group Type */}
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Group Type</InputLabel>
                <Select
                  value={groupForm.group_type}
                  onChange={(e) => setGroupForm({ ...groupForm, group_type: e.target.value })}
                  label="Group Type"
                  disabled={!!groupForm.template_id}
                >
                  <MenuItem value="administrative">Administrative</MenuItem>
                  <MenuItem value="functional">Functional</MenuItem>
                  <MenuItem value="operational">Operational</MenuItem>
                  <MenuItem value="custom">Custom</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* Description */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                value={groupForm.description}
                onChange={(e) => setGroupForm({ ...groupForm, description: e.target.value })}
                error={formErrors.description}
                helperText={formErrors.description ? 'Description is required' : 'Describe the purpose and responsibilities of this group'}
                multiline
                rows={2}
                required
              />
            </Grid>

            {/* Level */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Level"
                type="number"
                value={groupForm.level}
                onChange={(e) => setGroupForm({ ...groupForm, level: parseInt(e.target.value) || 10 })}
                helperText="Higher levels indicate more authority (10-100)"
                disabled={!!groupForm.template_id}
                inputProps={{ min: 1, max: 100 }}
              />
            </Grid>

            {/* Custom Permissions (only if not using template) */}
            {!groupForm.template_id && (
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>Initial Permissions (Optional)</InputLabel>
                  <Select
                    multiple
                    value={groupForm.permission_codenames}
                    onChange={(e) => setGroupForm({ ...groupForm, permission_codenames: e.target.value })}
                    label="Initial Permissions (Optional)"
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selected.map((value) => {
                          const permission = permissions.find(p => p.codename === value);
                          return (
                            <Chip
                              key={value}
                              label={permission?.name || value}
                              size="small"
                            />
                          );
                        })}
                      </Box>
                    )}
                  >
                    {permissions.map((permission) => (
                      <MenuItem key={permission.codename} value={permission.codename}>
                        <Box>
                          <Typography variant="body2">{permission.name}</Typography>
                          <Typography variant="caption" color="textSecondary">
                            {permission.codename}
                          </Typography>
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
                <Typography variant="caption" color="textSecondary" sx={{ mt: 0.5, display: 'block' }}>
                  You can add more permissions later through group management.
                </Typography>
              </Grid>
            )}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateGroupDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleCreateGroup}
            variant="contained"
            disabled={groupLoading || !groupForm.group_name}
            startIcon={groupLoading ? <CircularProgress size={20} /> : <SecurityIcon />}
          >
            {groupLoading ? 'Creating...' : (groupForm.template_id ? 'Create from Template' : 'Create Group')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SubcityUserManagement;
