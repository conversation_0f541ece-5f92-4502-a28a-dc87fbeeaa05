import React, { useRef } from 'react';
import { Box, Button, Dialog, DialogActions, DialogContent, DialogTitle, Typography, Alert } from '@mui/material';
import { Print as PrintIcon, Download as DownloadIcon } from '@mui/icons-material';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

/**
 * DirectPrintHandler - Handles direct printing and PDF generation for ID cards
 * without redirecting to Windows print dialog
 */
const DirectPrintHandler = ({
  open,
  onClose,
  children,
  title = "Print ID Card",
  onPrintComplete
}) => {
  const printRef = useRef();

  // One-click direct print function (bypasses dialog completely)
  const handleOneClickPrint = async () => {
    try {
      const element = printRef.current;
      if (!element) {
        console.error('Print element not found');
        return false;
      }

      // Find the actual ID card element
      const idCardElement = element.querySelector('.security-pattern-container') || element;

      if (!idCardElement) {
        console.error('ID card element not found');
        return false;
      }

      // Generate high-resolution canvas
      const canvas = await html2canvas(idCardElement, {
        scale: 3,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: idCardElement.offsetWidth,
        height: idCardElement.offsetHeight,
        scrollX: 0,
        scrollY: 0,
        logging: false,
        removeContainer: true,
        foreignObjectRendering: true,
      });

      // Create print window and immediately trigger print
      const printWindow = window.open('', '_blank', 'width=800,height=600');
      const imageDataUrl = canvas.toDataURL('image/png', 1.0);

      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>ID Card Print</title>
          <style>
            @page { size: 85.6mm 53.98mm; margin: 0; }
            body { margin: 0; padding: 0; display: flex; justify-content: center; align-items: center; min-height: 100vh; }
            .id-card-image { width: 85.6mm; height: 53.98mm; object-fit: contain; }
          </style>
        </head>
        <body>
          <img src="${imageDataUrl}" alt="ID Card" class="id-card-image" />
          <script>
            window.onload = function() {
              setTimeout(function() {
                window.print();
                setTimeout(function() { window.close(); }, 1000);
              }, 500);
            };
          </script>
        </body>
        </html>
      `);

      printWindow.document.close();

      // Call completion callback
      if (onPrintComplete) {
        onPrintComplete();
      }

      return true;
    } catch (error) {
      console.error('Error with one-click print:', error);
      return false;
    }
  };

  // Alternative method: Clone DOM element with all styles
  const handleDOMClonePrint = () => {
    try {
      const element = printRef.current;
      if (!element) {
        alert('Print element not found');
        return;
      }

      // Find the actual ID card element
      const idCardElement = element.querySelector('.security-pattern-container') || element;

      if (!idCardElement) {
        alert('ID card element not found');
        return;
      }

      // Clone the element with all its styles
      const clonedElement = idCardElement.cloneNode(true);

      // Get all computed styles from the original element and its children
      const copyStyles = (source, target) => {
        const sourceStyles = window.getComputedStyle(source);
        const targetStyle = target.style;

        // Copy all computed styles
        for (let i = 0; i < sourceStyles.length; i++) {
          const property = sourceStyles[i];
          targetStyle.setProperty(property, sourceStyles.getPropertyValue(property), sourceStyles.getPropertyPriority(property));
        }

        // Recursively copy styles for children
        for (let i = 0; i < source.children.length; i++) {
          if (target.children[i]) {
            copyStyles(source.children[i], target.children[i]);
          }
        }
      };

      copyStyles(idCardElement, clonedElement);

      // Create new window with the cloned element
      const printWindow = window.open('', '_blank', 'width=800,height=600');

      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>ID Card Print</title>
          <style>
            @page {
              size: 85.6mm 53.98mm;
              margin: 0;
            }

            body {
              margin: 0;
              padding: 20px;
              background: #f5f5f5;
              font-family: Arial, sans-serif;
              display: flex;
              flex-direction: column;
              align-items: center;
              min-height: 100vh;
            }

            .print-controls {
              margin-bottom: 20px;
              text-align: center;
              background: white;
              padding: 15px;
              border-radius: 8px;
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .print-button {
              background: #ff8f00;
              color: white;
              border: none;
              padding: 12px 24px;
              font-size: 16px;
              border-radius: 4px;
              cursor: pointer;
              margin: 0 10px;
            }

            .print-button:hover {
              background: #ef6c00;
            }

            .close-button {
              background: #666;
            }

            .close-button:hover {
              background: #555;
            }

            .id-card-container {
              background: white;
              padding: 20px;
              border-radius: 8px;
              box-shadow: 0 4px 8px rgba(0,0,0,0.15);
              display: flex;
              justify-content: center;
              align-items: center;
            }

            @media print {
              .print-controls, .info-text { display: none !important; }
              body {
                margin: 0 !important;
                padding: 0 !important;
                background: white !important;
                display: flex !important;
                justify-content: center !important;
                align-items: center !important;
                min-height: auto !important;
              }
              .id-card-container {
                background: transparent !important;
                padding: 0 !important;
                border-radius: 0 !important;
                box-shadow: none !important;
              }
            }
          </style>
        </head>
        <body>
          <div class="print-controls">
            <h3 style="margin: 0 0 15px 0; color: #333;">ID Card Print Preview (DOM Clone)</h3>
            <button class="print-button" onclick="window.print()">🖨️ Print ID Card</button>
            <button class="print-button close-button" onclick="window.close()">❌ Close</button>
          </div>

          <div class="id-card-container" id="card-container">
          </div>

          <div class="info-text">
            <p><strong>Print Instructions:</strong></p>
            <p>• Click "Print ID Card" button or press Ctrl+P</p>
            <p>• This method uses direct DOM cloning for better compatibility</p>
          </div>

          <script>
            document.addEventListener('keydown', function(e) {
              if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
              }
              if (e.key === 'Escape') {
                window.close();
              }
            });
          </script>
        </body>
        </html>
      `);

      printWindow.document.close();

      // Append the cloned element to the container
      const container = printWindow.document.getElementById('card-container');
      container.appendChild(clonedElement);

      printWindow.focus();

      if (onPrintComplete) {
        onPrintComplete();
      }

      onClose();

    } catch (error) {
      console.error('Error with DOM clone print:', error);
      alert('Failed to print using DOM clone method. Please try Simple Print instead.');
    }
  };

  // Generate PDF and trigger download
  const handleGeneratePDF = async () => {
    try {
      const element = printRef.current;
      if (!element) return;

      // Find the actual ID card element (with security-pattern-container class)
      const idCardElement = element.querySelector('.security-pattern-container') || element;

      // Configure html2canvas for high quality
      const canvas = await html2canvas(idCardElement, {
        scale: 3, // High resolution
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: idCardElement.offsetWidth,
        height: idCardElement.offsetHeight,
        scrollX: 0,
        scrollY: 0,
        logging: false,
        removeContainer: true,
        foreignObjectRendering: true,
      });

      // Standard ID card dimensions in mm: 85.6 x 53.98 mm
      const imgWidth = 85.6;
      const imgHeight = 53.98;
      
      // Create PDF with ID card dimensions
      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        format: [imgWidth, imgHeight]
      });

      const imgData = canvas.toDataURL('image/png');
      pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);

      // Generate filename with timestamp
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const filename = `ID_Card_${timestamp}.pdf`;

      // Download the PDF
      pdf.save(filename);

      // Call completion callback
      if (onPrintComplete) {
        onPrintComplete();
      }

      onClose();
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Failed to generate PDF. Please try again.');
    }
  };

  // Generate high-resolution image and trigger download
  const handleGenerateImage = async () => {
    try {
      const element = printRef.current;
      if (!element) return;

      // Find the actual ID card element (with security-pattern-container class)
      const idCardElement = element.querySelector('.security-pattern-container') || element;

      const canvas = await html2canvas(idCardElement, {
        scale: 4, // Very high resolution for printing
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: idCardElement.offsetWidth,
        height: idCardElement.offsetHeight,
        scrollX: 0,
        scrollY: 0,
        logging: false,
        removeContainer: true,
        foreignObjectRendering: true,
      });

      // Convert to blob and download
      canvas.toBlob((blob) => {
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        link.download = `ID_Card_${timestamp}.png`;
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        // Call completion callback
        if (onPrintComplete) {
          onPrintComplete();
        }

        onClose();
      }, 'image/png', 1.0);
    } catch (error) {
      console.error('Error generating image:', error);
      alert('Failed to generate image. Please try again.');
    }
  };

  // Direct print using canvas to image approach (bypasses print dialog)
  const handleDirectPrint = async () => {
    try {
      const element = printRef.current;
      if (!element) {
        alert('Print element not found');
        return;
      }

      // Find the actual ID card element (with security-pattern-container class)
      const idCardElement = element.querySelector('.security-pattern-container') || element;

      console.log('🔍 Print Debug Info:', {
        element: element,
        idCardElement: idCardElement,
        offsetWidth: idCardElement.offsetWidth,
        offsetHeight: idCardElement.offsetHeight,
        computedStyle: window.getComputedStyle(idCardElement)
      });

      if (!idCardElement || idCardElement.offsetWidth === 0 || idCardElement.offsetHeight === 0) {
        alert('ID card element not found or has no dimensions. Please try again.');
        return;
      }

      // Wait a moment for any animations or loading to complete
      await new Promise(resolve => setTimeout(resolve, 500));

      // Generate high-resolution canvas of only the ID card
      const canvas = await html2canvas(idCardElement, {
        scale: 2, // Reduced scale to avoid memory issues
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: idCardElement.offsetWidth,
        height: idCardElement.offsetHeight,
        scrollX: 0,
        scrollY: 0,
        logging: true, // Enable logging for debugging
        removeContainer: false, // Keep container for better rendering
        foreignObjectRendering: false, // Disable for better compatibility
        imageTimeout: 15000, // Longer timeout for images
        onclone: (clonedDoc) => {
          // Ensure all styles are copied to the cloned document
          const clonedElement = clonedDoc.querySelector('.security-pattern-container');
          if (clonedElement) {
            console.log('✅ Cloned element found:', clonedElement);
          } else {
            console.log('❌ Cloned element not found');
          }
        }
      });

      console.log('📊 Canvas Info:', {
        width: canvas.width,
        height: canvas.height,
        dataURL: canvas.toDataURL('image/png', 1.0).substring(0, 100) + '...'
      });

      // Create a new window for printing the image
      const printWindow = window.open('', '_blank', 'width=800,height=600');

      // Convert canvas to image data URL
      const imageDataUrl = canvas.toDataURL('image/png', 1.0);

      // Check if canvas is blank (common issue with html2canvas)
      const isBlank = canvas.width === 0 || canvas.height === 0 ||
                     imageDataUrl === 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';

      if (isBlank) {
        console.log('❌ Canvas is blank, falling back to simple print method');
        alert('Canvas rendering failed. Falling back to simple print method.');
        handleSimplePrint();
        return;
      }

      // Write the image to the new window with proper print styles and controls
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>ID Card Print</title>
          <style>
            @page {
              size: 85.6mm 53.98mm;
              margin: 0;
            }

            body {
              margin: 0;
              padding: 20px;
              background: #f5f5f5;
              font-family: Arial, sans-serif;
              display: flex;
              flex-direction: column;
              align-items: center;
              min-height: 100vh;
            }

            .print-controls {
              margin-bottom: 20px;
              text-align: center;
              background: white;
              padding: 15px;
              border-radius: 8px;
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .print-button {
              background: #ff8f00;
              color: white;
              border: none;
              padding: 12px 24px;
              font-size: 16px;
              border-radius: 4px;
              cursor: pointer;
              margin: 0 10px;
              transition: background 0.2s;
            }

            .print-button:hover {
              background: #ef6c00;
            }

            .close-button {
              background: #666;
            }

            .close-button:hover {
              background: #555;
            }

            .id-card-container {
              background: white;
              padding: 20px;
              border-radius: 8px;
              box-shadow: 0 4px 8px rgba(0,0,0,0.15);
              display: flex;
              justify-content: center;
              align-items: center;
            }

            .id-card-image {
              width: 85.6mm;
              height: 53.98mm;
              object-fit: contain;
              display: block;
              border: 1px solid #ddd;
            }

            .info-text {
              margin-top: 20px;
              color: #666;
              font-size: 14px;
              text-align: center;
              max-width: 400px;
              background: white;
              padding: 15px;
              border-radius: 8px;
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            @media print {
              .print-controls, .info-text {
                display: none !important;
              }
              body {
                margin: 0 !important;
                padding: 0 !important;
                background: white !important;
                display: flex !important;
                justify-content: center !important;
                align-items: center !important;
                min-height: auto !important;
              }
              .id-card-container {
                background: transparent !important;
                padding: 0 !important;
                border-radius: 0 !important;
                box-shadow: none !important;
              }
              .id-card-image {
                width: 85.6mm !important;
                height: 53.98mm !important;
                border: none !important;
                page-break-inside: avoid !important;
              }
            }
          </style>
        </head>
        <body>
          <div class="print-controls">
            <h3 style="margin: 0 0 15px 0; color: #333;">ID Card Print Preview</h3>
            <button class="print-button" onclick="window.print()">🖨️ Print ID Card</button>
            <button class="print-button close-button" onclick="window.close()">❌ Close</button>
          </div>

          <div class="id-card-container">
            <img src="${imageDataUrl}" alt="ID Card" class="id-card-image" />
          </div>

          <div class="info-text">
            <p><strong>Print Instructions:</strong></p>
            <p>• Click "Print ID Card" button or press Ctrl+P</p>
            <p>• Select your printer and adjust settings if needed</p>
            <p>• Recommended: Use high-quality paper for best results</p>
            <p>• Press Escape to close this window</p>
          </div>

          <script>
            // Auto-focus for keyboard shortcuts
            window.focus();

            // Handle keyboard shortcuts
            document.addEventListener('keydown', function(e) {
              if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
              }
              if (e.key === 'Escape') {
                window.close();
              }
            });

            // Ensure image is fully loaded
            const img = document.querySelector('.id-card-image');
            img.onload = function() {
              console.log('ID Card image loaded successfully');
            };

            img.onerror = function() {
              alert('Error loading ID card image. Please try again.');
            };
          </script>
        </body>
        </html>
      `);

      printWindow.document.close();

      // Focus the new window
      printWindow.focus();

      // Call completion callback immediately since we're not auto-printing
      if (onPrintComplete) {
        onPrintComplete();
      }

      // Close the original dialog
      onClose();

    } catch (error) {
      console.error('Error printing:', error);
      alert('Failed to print. Please try again.');
    }
  };

  // Simple print method using CSS to hide everything except ID card
  const handleSimplePrint = () => {
    try {
      // Add print-specific styles to hide everything except the ID card
      const printStyles = `
        <style>
          @media print {
            /* Hide everything first */
            * {
              visibility: hidden !important;
              background: transparent !important;
              box-shadow: none !important;
              border: none !important;
            }

            /* Show only the ID card container and its contents */
            .security-pattern-container,
            .security-pattern-container *,
            .security-pattern-container::before,
            .security-pattern-container::after {
              visibility: visible !important;
              display: block !important;
            }

            /* Position the ID card in center of page */
            .security-pattern-container {
              position: fixed !important;
              left: 50% !important;
              top: 50% !important;
              transform: translate(-50%, -50%) !important;
              width: 85.6mm !important;
              height: 53.98mm !important;
              margin: 0 !important;
              padding: 0 !important;
              background: #ffffff !important;
              border: 1px solid #ddd !important;
              border-radius: 6px !important;
              overflow: hidden !important;
              font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif !important;
              box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
              z-index: 9999 !important;
            }

            /* Ensure decorative pattern is visible */
            .security-pattern-container::before {
              content: "" !important;
              position: absolute !important;
              top: 0 !important;
              left: 0 !important;
              right: 0 !important;
              bottom: 0 !important;
              pointer-events: none !important;
              z-index: 5 !important;
              opacity: 0.25 !important;
              background:
                radial-gradient(circle at 15% 25%, rgba(52, 152, 219, 0.6) 0%, rgba(52, 152, 219, 0.2) 30%, transparent 50%),
                radial-gradient(circle at 85% 15%, rgba(41, 128, 185, 0.5) 0%, rgba(41, 128, 185, 0.15) 25%, transparent 40%),
                radial-gradient(circle at 75% 75%, rgba(52, 152, 219, 0.7) 0%, rgba(52, 152, 219, 0.25) 35%, transparent 55%),
                radial-gradient(circle at 25% 85%, rgba(41, 128, 185, 0.5) 0%, rgba(41, 128, 185, 0.18) 28%, transparent 45%),
                radial-gradient(circle at 60% 40%, rgba(30, 139, 195, 0.4) 0%, rgba(30, 139, 195, 0.15) 15%, transparent 20%),
                radial-gradient(circle at 40% 70%, rgba(52, 152, 219, 0.35) 0%, rgba(52, 152, 219, 0.12) 12%, transparent 18%),
                radial-gradient(circle at 80% 60%, rgba(41, 128, 185, 0.45) 0%, rgba(41, 128, 185, 0.18) 18%, transparent 22%),
                conic-gradient(from 0deg at 60% 40%, transparent 0deg, rgba(30, 139, 195, 0.3) 45deg, transparent 90deg, rgba(30, 139, 195, 0.2) 135deg, transparent 180deg, rgba(30, 139, 195, 0.3) 225deg, transparent 270deg, rgba(30, 139, 195, 0.2) 315deg, transparent 360deg),
                conic-gradient(from 45deg at 40% 70%, transparent 0deg, rgba(52, 152, 219, 0.25) 60deg, transparent 120deg, rgba(52, 152, 219, 0.15) 180deg, transparent 240deg, rgba(52, 152, 219, 0.25) 300deg, transparent 360deg),
                conic-gradient(from 90deg at 80% 60%, transparent 0deg, rgba(41, 128, 185, 0.35) 30deg, transparent 90deg, rgba(41, 128, 185, 0.25) 150deg, transparent 210deg, rgba(41, 128, 185, 0.35) 270deg, transparent 330deg),
                radial-gradient(circle at 30% 30%, rgba(52, 152, 219, 0.8) 0%, rgba(52, 152, 219, 0.3) 8%, transparent 12%),
                radial-gradient(circle at 70% 20%, rgba(41, 128, 185, 0.7) 0%, rgba(41, 128, 185, 0.25) 6%, transparent 10%),
                radial-gradient(circle at 20% 60%, rgba(30, 139, 195, 0.75) 0%, rgba(30, 139, 195, 0.28) 7%, transparent 11%),
                radial-gradient(circle at 90% 80%, rgba(52, 152, 219, 0.7) 0%, rgba(52, 152, 219, 0.22) 6%, transparent 10%),
                radial-gradient(circle at 50% 90%, rgba(41, 128, 185, 0.6) 0%, rgba(41, 128, 185, 0.18) 5%, transparent 9%),
                linear-gradient(45deg, transparent 48%, rgba(52, 152, 219, 0.15) 49%, rgba(52, 152, 219, 0.15) 51%, transparent 52%),
                linear-gradient(-45deg, transparent 48%, rgba(41, 128, 185, 0.12) 49%, rgba(41, 128, 185, 0.12) 51%, transparent 52%) !important;
              background-size:
                120px 120px, 80px 80px, 140px 140px, 90px 90px,
                40px 40px, 35px 35px, 45px 45px,
                25px 25px, 20px 20px, 30px 30px,
                15px 15px, 12px 12px, 14px 14px, 13px 13px, 11px 11px,
                60px 60px, 60px 60px !important;
            }

            /* Ensure security patterns are visible */
            .security-pattern-container::after {
              content: "" !important;
              position: absolute !important;
              top: 0 !important;
              left: 0 !important;
              right: 0 !important;
              bottom: 0 !important;
              opacity: 0.08 !important;
              background-image:
                radial-gradient(circle at 25% 25%, #dfcd68 1px, transparent 1px),
                radial-gradient(circle at 75% 75%, #dfcd68 1px, transparent 1px),
                linear-gradient(45deg, transparent 48%, rgba(223, 205, 104, 0.1) 49%, rgba(223, 205, 104, 0.1) 51%, transparent 52%),
                linear-gradient(-45deg, transparent 48%, rgba(223, 205, 104, 0.1) 49%, rgba(223, 205, 104, 0.1) 51%, transparent 52%) !important;
              background-size: 20px 20px, 20px 20px, 40px 40px, 40px 40px !important;
              background-position: 0 0, 10px 10px, 0 0, 0 0 !important;
              pointer-events: none !important;
              z-index: 1 !important;
            }

            /* Hide any side toggle buttons or extra UI elements */
            .MuiToggleButtonGroup-root,
            .MuiToggleButton-root,
            .print-side-toggle,
            .side-toggle,
            .toggle-button,
            .MuiButton-root,
            .MuiIconButton-root,
            .dialog-actions,
            .MuiDialogActions-root,
            .MuiDialogContent-root > *:not(.security-pattern-container),
            .MuiBox-root:not(.security-pattern-container) .MuiBox-root:not(.security-pattern-container *) {
              visibility: hidden !important;
              display: none !important;
            }

            /* Ensure all ID card content is visible */
            .security-pattern-container img,
            .security-pattern-container .MuiTypography-root,
            .security-pattern-container .MuiBox-root {
              visibility: visible !important;
              display: block !important;
            }

            /* Page setup */
            @page {
              size: 85.6mm 53.98mm;
              margin: 0;
            }

            html, body {
              width: 85.6mm !important;
              height: 53.98mm !important;
              margin: 0 !important;
              padding: 0 !important;
              background: white !important;
            }
          }
        </style>
      `;

      // Add the print styles to the document head
      const styleElement = document.createElement('style');
      styleElement.innerHTML = printStyles;
      document.head.appendChild(styleElement);

      // Find the actual ID card element
      const element = printRef.current;
      const idCardElement = element?.querySelector('.security-pattern-container');

      if (!idCardElement) {
        alert('ID card element not found');
        document.head.removeChild(styleElement);
        return;
      }

      console.log('🖨️ Simple Print - ID Card Element Found:', idCardElement);

      // Trigger print
      window.print();

      // Clean up after printing
      setTimeout(() => {
        try {
          document.head.removeChild(styleElement);
        } catch (e) {
          console.log('Style element already removed');
        }

        // Call completion callback
        if (onPrintComplete) {
          onPrintComplete();
        }

        onClose();
      }, 1000);

    } catch (error) {
      console.error('Error with simple print:', error);
      alert('Failed to print. Please try again.');
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          minHeight: '70vh'
        }
      }}
    >
      <DialogTitle>
        <Typography variant="h6" component="div">
          {title}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Choose your preferred printing method
        </Typography>
      </DialogTitle>
      
      <DialogContent>
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body2">
            <strong>Print Options:</strong><br/>
            • <strong>Canvas Print:</strong> High-quality image-based printing<br/>
            • <strong>DOM Clone Print:</strong> Direct element cloning (fallback method)<br/>
            • <strong>Simple Print:</strong> CSS-based browser printing<br/>
            • <strong>Download PDF:</strong> Save as PDF for professional printing<br/>
            • <strong>Download PNG:</strong> Save as high-resolution PNG
          </Typography>
        </Alert>

        {/* Print Preview */}
        <Box 
          ref={printRef}
          sx={{
            display: 'flex',
            justifyContent: 'center',
            mb: 3,
            p: 2,
            bgcolor: '#f5f5f5',
            borderRadius: 1,
            border: '1px solid #ddd'
          }}
        >
          {children}
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3, gap: 1, flexWrap: 'wrap' }}>
        <Button onClick={onClose} variant="outlined">
          Cancel
        </Button>

        <Button
          onClick={handleGenerateImage}
          variant="outlined"
          startIcon={<DownloadIcon />}
          color="secondary"
        >
          Download PNG
        </Button>

        <Button
          onClick={handleGeneratePDF}
          variant="outlined"
          startIcon={<DownloadIcon />}
          color="primary"
        >
          Download PDF
        </Button>

        <Button
          onClick={handleDirectPrint}
          variant="contained"
          startIcon={<PrintIcon />}
          color="primary"
        >
          Canvas Print
        </Button>

        <Button
          onClick={handleDOMClonePrint}
          variant="outlined"
          startIcon={<PrintIcon />}
          color="primary"
        >
          DOM Clone Print
        </Button>

        <Button
          onClick={handleSimplePrint}
          variant="outlined"
          startIcon={<PrintIcon />}
          color="primary"
        >
          Simple Print
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DirectPrintHandler;
