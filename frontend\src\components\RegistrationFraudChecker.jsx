import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Alert,
  AlertTitle,
  Ty<PERSON><PERSON>,
  Chip,
  Button,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Paper,
  Divider
} from '@mui/material';
import {
  Security as SecurityIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  CheckCircle as CheckIcon,
  ExpandMore as ExpandMoreIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import axios from '../utils/axios';

const RegistrationFraudChecker = ({ 
  citizenData, 
  onFraudDetected, 
  onFraudCleared,
  tenantId,
  autoCheck = true,
  showDetails = true 
}) => {
  const [checking, setChecking] = useState(false);
  const [fraudResults, setFraudResults] = useState(null);
  const [lastCheckedData, setLastCheckedData] = useState(null);

  // Auto-check when citizen data changes (debounced)
  useEffect(() => {
    if (!autoCheck || !citizenData) return;

    // Only check if we have enough reliable data and it's different from last check
    const hasEnoughData = citizenData.first_name && citizenData.last_name &&
                         (citizenData.date_of_birth || citizenData.mother_full_name || citizenData.father_full_name);
    const dataChanged = JSON.stringify(citizenData) !== JSON.stringify(lastCheckedData);

    if (hasEnoughData && dataChanged) {
      const timer = setTimeout(() => {
        checkFraud();
      }, 1500); // 1.5 second debounce (slightly longer for more data)

      return () => clearTimeout(timer);
    }
  }, [citizenData, autoCheck]);

  // Notify parent component when fraud status changes
  useEffect(() => {
    if (fraudResults) {
      if (fraudResults.fraud_detected) {
        onFraudDetected?.(fraudResults);
      } else {
        onFraudCleared?.(fraudResults);
      }
    }
  }, [fraudResults, onFraudDetected, onFraudCleared]);

  const checkFraud = async () => {
    if (!citizenData || !tenantId) return;

    try {
      setChecking(true);
      setLastCheckedData({ ...citizenData });

      const response = await axios.post(
        `/api/tenants/${tenantId}/citizens/check-registration-fraud/`,
        citizenData
      );

      setFraudResults(response.data);
    } catch (error) {
      console.error('Fraud check failed:', error);
      // Don't show error to user - fraud detection failure shouldn't block registration
      setFraudResults({
        fraud_check_completed: false,
        can_proceed: true,
        risk_level: 'unknown',
        error: 'Fraud detection temporarily unavailable'
      });
    } finally {
      setChecking(false);
    }
  };

  const getSeverityColor = (riskLevel) => {
    switch (riskLevel) {
      case 'critical':
        return 'error';
      case 'high':
        return 'warning';
      case 'medium':
        return 'info';
      case 'low':
        return 'success';
      default:
        return 'info';
    }
  };

  const getRiskIcon = (riskLevel) => {
    switch (riskLevel) {
      case 'critical':
        return <ErrorIcon />;
      case 'high':
        return <WarningIcon />;
      case 'medium':
        return <WarningIcon />;
      case 'low':
        return <CheckIcon />;
      default:
        return <SecurityIcon />;
    }
  };

  const getRiskTitle = (riskLevel) => {
    switch (riskLevel) {
      case 'critical':
        return 'CRITICAL: Registration Blocked';
      case 'high':
        return 'HIGH RISK: Verification Required';
      case 'medium':
        return 'MEDIUM RISK: Review Recommended';
      case 'low':
        return 'No Fraud Detected';
      default:
        return 'Fraud Check Status';
    }
  };

  // Don't show anything if no data to check
  if (!citizenData || (!citizenData.first_name && !citizenData.last_name)) {
    return null;
  }

  // Show checking indicator
  if (checking) {
    return (
      <Paper sx={{ p: 2, mb: 2, border: '1px solid', borderColor: 'info.main' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <CircularProgress size={24} />
          <Typography variant="body2">
            Checking for duplicate registrations...
          </Typography>
        </Box>
      </Paper>
    );
  }

  // Don't show anything if no results yet
  if (!fraudResults) {
    return null;
  }

  // Show fraud detection results
  return (
    <Paper sx={{ 
      mb: 2, 
      border: '2px solid', 
      borderColor: `${getSeverityColor(fraudResults.risk_level)}.main`,
      backgroundColor: fraudResults.risk_level === 'critical' ? 'error.light' : 'background.paper'
    }}>
      <Alert 
        severity={getSeverityColor(fraudResults.risk_level)}
        icon={getRiskIcon(fraudResults.risk_level)}
        sx={{ 
          '& .MuiAlert-message': { width: '100%' },
          borderRadius: 0
        }}
      >
        <AlertTitle sx={{ fontWeight: 'bold', fontSize: '1.1rem' }}>
          🔍 {getRiskTitle(fraudResults.risk_level)}
        </AlertTitle>

        {/* Main warning messages */}
        {fraudResults.warnings && fraudResults.warnings.map((warning, index) => (
          <Box key={index} sx={{ mb: 2 }}>
            <Typography variant="body1" sx={{ fontWeight: 'bold', mb: 1 }}>
              {warning.message}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              <strong>Action Required:</strong> {warning.action_required}
            </Typography>
          </Box>
        ))}

        {/* Risk level indicator */}
        <Box sx={{ mb: 2 }}>
          <Chip
            label={`Risk Level: ${fraudResults.risk_level?.toUpperCase() || 'UNKNOWN'}`}
            color={getSeverityColor(fraudResults.risk_level)}
            variant="filled"
            size="small"
            sx={{ fontWeight: 'bold' }}
          />
          {!fraudResults.can_proceed && (
            <Chip
              label="REGISTRATION BLOCKED"
              color="error"
              variant="filled"
              size="small"
              sx={{ ml: 1, fontWeight: 'bold' }}
            />
          )}
        </Box>

        {/* Manual check button */}
        <Box sx={{ mb: 2 }}>
          <Button
            variant="outlined"
            size="small"
            startIcon={<RefreshIcon />}
            onClick={checkFraud}
            disabled={checking}
          >
            Re-check for Duplicates
          </Button>
        </Box>

        {/* Detailed match information */}
        {showDetails && fraudResults.matches && fraudResults.matches.length > 0 && (
          <Accordion sx={{ mt: 2 }}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                Found {fraudResults.matches.length} Potential Match(es) in Other Kebeles
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List>
                {fraudResults.matches.map((match, index) => (
                  <ListItem key={index} divider>
                    <ListItemIcon>
                      <PersonIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box>
                          <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                            {match.citizen_name} - {match.kebele_name}
                          </Typography>
                          <Chip
                            label={`${match.confidence} Match`}
                            size="small"
                            color={match.confidence.includes('100') ? 'error' : 'warning'}
                            sx={{ mt: 0.5 }}
                          />
                        </Box>
                      }
                      secondary={
                        <Box sx={{ mt: 1 }}>
                          <Typography variant="body2" color="text.secondary">
                            <strong>Match Type:</strong> {match.match_type.replace('_', ' ').toUpperCase()}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            <strong>Details:</strong> {match.details}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>
        )}

        {/* Recommendations */}
        {fraudResults.recommendations && fraudResults.recommendations.length > 0 && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
              Recommended Actions:
            </Typography>
            <List dense>
              {fraudResults.recommendations.map((recommendation, index) => (
                <ListItem key={index} sx={{ py: 0.5 }}>
                  <ListItemIcon sx={{ minWidth: 32 }}>
                    <SecurityIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText 
                    primary={recommendation}
                    primaryTypographyProps={{ fontSize: '0.9rem' }}
                  />
                </ListItem>
              ))}
            </List>
          </Box>
        )}

        {/* Critical blocking message */}
        {fraudResults.risk_level === 'critical' && (
          <Box sx={{ 
            mt: 2, 
            p: 2, 
            backgroundColor: 'error.dark', 
            borderRadius: 1,
            color: 'error.contrastText'
          }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
              ⛔ REGISTRATION CANNOT PROCEED: This citizen appears to already be registered in another kebele. 
              Please contact the other kebele to verify the citizen's status before attempting to register them here.
            </Typography>
          </Box>
        )}

        {/* Low risk success message */}
        {fraudResults.risk_level === 'low' && (
          <Typography variant="body2" color="success.main" sx={{ fontWeight: 'bold' }}>
            ✅ No duplicate registrations found. Safe to proceed with registration.
          </Typography>
        )}
      </Alert>
    </Paper>
  );
};

export default RegistrationFraudChecker;
