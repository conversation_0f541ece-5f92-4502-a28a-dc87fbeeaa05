"""
Management command for auditing duplicate fingerprints across all tenants.

This command provides comprehensive duplicate detection auditing capabilities
for system administrators and compliance purposes.
"""

import json
import csv
from datetime import datetime
from django.core.management.base import BaseCommand, CommandError
from django.core.cache import cache
from django_tenants.utils import schema_context, get_tenant_model
from biometrics.duplicate_detection_service import EnhancedDuplicateDetectionService
from citizens.models import Biometric


class Command(BaseCommand):
    help = 'Audit duplicate fingerprints across all tenants'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant',
            type=str,
            help='Specific tenant schema to audit (default: all tenants)'
        )
        
        parser.add_argument(
            '--output-format',
            choices=['json', 'csv', 'console'],
            default='console',
            help='Output format for results'
        )
        
        parser.add_argument(
            '--output-file',
            type=str,
            help='Output file path (required for json/csv formats)'
        )
        
        parser.add_argument(
            '--clear-cache',
            action='store_true',
            help='Clear duplicate detection cache before audit'
        )
        
        parser.add_argument(
            '--detailed',
            action='store_true',
            help='Include detailed match information'
        )
        
        parser.add_argument(
            '--threshold',
            type=float,
            default=40.0,
            help='Minimum match score threshold (default: 40.0)'
        )
    
    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🔍 Starting Duplicate Fingerprint Audit'))
        
        # Initialize service
        detection_service = EnhancedDuplicateDetectionService()
        detection_service.match_threshold = options['threshold']
        
        # Clear cache if requested
        if options['clear_cache']:
            detection_service.clear_cache()
            self.stdout.write(self.style.WARNING('🗑️ Cache cleared'))
        
        try:
            # Run audit
            if options['tenant']:
                audit_result = self._audit_specific_tenant(options['tenant'], detection_service)
            else:
                audit_result = self._audit_all_tenants(detection_service)
            
            # Output results
            self._output_results(audit_result, options)
            
            # Summary
            self.stdout.write(self.style.SUCCESS(f'✅ Audit completed'))
            self.stdout.write(f"   Total templates checked: {audit_result['total_templates_checked']}")
            self.stdout.write(f"   Exact duplicates found: {audit_result['exact_duplicates_found']}")
            
            if audit_result['exact_duplicates_found'] > 0:
                self.stdout.write(self.style.ERROR(f"🚨 {audit_result['exact_duplicates_found']} duplicate(s) detected!"))
            else:
                self.stdout.write(self.style.SUCCESS("✅ No duplicates found"))
                
        except Exception as e:
            raise CommandError(f'Audit failed: {str(e)}')
    
    def _audit_all_tenants(self, detection_service):
        """Audit all tenants for duplicates"""
        self.stdout.write('📊 Auditing all tenants...')
        
        return detection_service.bulk_duplicate_check()
    
    def _audit_specific_tenant(self, tenant_schema, detection_service):
        """Audit a specific tenant for duplicates"""
        self.stdout.write(f'📊 Auditing tenant: {tenant_schema}')
        
        try:
            Tenant = get_tenant_model()
            tenant = Tenant.objects.get(schema_name=tenant_schema)
            
            with schema_context(tenant_schema):
                biometric_records = Biometric.objects.select_related('citizen').all()
                
                templates = []
                for record in biometric_records:
                    if record.left_thumb_fingerprint:
                        templates.append({
                            'template_data': record.left_thumb_fingerprint,
                            'template_hash': detection_service._get_template_hash(record.left_thumb_fingerprint),
                            'thumb_type': 'left',
                            'tenant_id': tenant.id,
                            'tenant_name': tenant.name,
                            'citizen_id': record.citizen.id,
                            'citizen_name': f"{record.citizen.first_name} {record.citizen.last_name}",
                            'citizen_digital_id': record.citizen.digital_id,
                            'biometric_id': record.id
                        })
                    
                    if record.right_thumb_fingerprint:
                        templates.append({
                            'template_data': record.right_thumb_fingerprint,
                            'template_hash': detection_service._get_template_hash(record.right_thumb_fingerprint),
                            'thumb_type': 'right',
                            'tenant_id': tenant.id,
                            'tenant_name': tenant.name,
                            'citizen_id': record.citizen.id,
                            'citizen_name': f"{record.citizen.first_name} {record.citizen.last_name}",
                            'citizen_digital_id': record.citizen.digital_id,
                            'biometric_id': record.id
                        })
                
                # Find duplicates within this tenant
                duplicates_found = []
                hash_groups = {}
                
                for template in templates:
                    template_hash = template['template_hash']
                    if template_hash not in hash_groups:
                        hash_groups[template_hash] = []
                    hash_groups[template_hash].append(template)
                
                for template_hash, template_group in hash_groups.items():
                    if len(template_group) > 1:
                        for i, template in enumerate(template_group):
                            for j, other_template in enumerate(template_group[i+1:], i+1):
                                duplicates_found.append({
                                    'type': 'exact_duplicate',
                                    'template1': template,
                                    'template2': other_template,
                                    'match_score': 1000.0,
                                    'confidence': 1.0
                                })
                
                return {
                    'total_templates_checked': len(templates),
                    'exact_duplicates_found': len(duplicates_found),
                    'duplicates': duplicates_found,
                    'check_time': datetime.now().isoformat(),
                    'tenant_specific': True,
                    'tenant_name': tenant.name
                }
                
        except Exception as e:
            raise CommandError(f'Error auditing tenant {tenant_schema}: {str(e)}')
    
    def _output_results(self, audit_result, options):
        """Output audit results in the specified format"""
        output_format = options['output_format']
        output_file = options['output_file']
        detailed = options['detailed']
        
        if output_format == 'console':
            self._output_console(audit_result, detailed)
        elif output_format == 'json':
            if not output_file:
                raise CommandError('Output file required for JSON format')
            self._output_json(audit_result, output_file)
        elif output_format == 'csv':
            if not output_file:
                raise CommandError('Output file required for CSV format')
            self._output_csv(audit_result, output_file, detailed)
    
    def _output_console(self, audit_result, detailed):
        """Output results to console"""
        self.stdout.write('\n📋 AUDIT RESULTS:')
        self.stdout.write(f"   Check time: {audit_result['check_time']}")
        self.stdout.write(f"   Total templates: {audit_result['total_templates_checked']}")
        self.stdout.write(f"   Duplicates found: {audit_result['exact_duplicates_found']}")
        
        if audit_result['exact_duplicates_found'] > 0:
            self.stdout.write('\n🚨 DUPLICATE DETAILS:')
            for i, duplicate in enumerate(audit_result['duplicates'][:10], 1):  # Show first 10
                template1 = duplicate['template1']
                template2 = duplicate['template2']
                
                self.stdout.write(f"\n   Duplicate #{i}:")
                self.stdout.write(f"     Citizen 1: {template1['citizen_name']} ({template1['citizen_digital_id']})")
                self.stdout.write(f"     Tenant 1: {template1['tenant_name']}")
                self.stdout.write(f"     Citizen 2: {template2['citizen_name']} ({template2['citizen_digital_id']})")
                self.stdout.write(f"     Tenant 2: {template2['tenant_name']}")
                self.stdout.write(f"     Match Score: {duplicate['match_score']}")
                
                if detailed:
                    self.stdout.write(f"     Template 1 Hash: {template1['template_hash']}")
                    self.stdout.write(f"     Template 2 Hash: {template2['template_hash']}")
            
            if len(audit_result['duplicates']) > 10:
                self.stdout.write(f"\n   ... and {len(audit_result['duplicates']) - 10} more duplicates")
    
    def _output_json(self, audit_result, output_file):
        """Output results to JSON file"""
        with open(output_file, 'w') as f:
            json.dump(audit_result, f, indent=2)
        
        self.stdout.write(f'📄 Results saved to: {output_file}')
    
    def _output_csv(self, audit_result, output_file, detailed):
        """Output results to CSV file"""
        with open(output_file, 'w', newline='') as f:
            fieldnames = [
                'duplicate_id', 'citizen1_name', 'citizen1_id', 'tenant1_name',
                'citizen2_name', 'citizen2_id', 'tenant2_name', 'match_score',
                'confidence', 'thumb_type1', 'thumb_type2'
            ]
            
            if detailed:
                fieldnames.extend(['template1_hash', 'template2_hash'])
            
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            
            for i, duplicate in enumerate(audit_result['duplicates'], 1):
                template1 = duplicate['template1']
                template2 = duplicate['template2']
                
                row = {
                    'duplicate_id': i,
                    'citizen1_name': template1['citizen_name'],
                    'citizen1_id': template1['citizen_digital_id'],
                    'tenant1_name': template1['tenant_name'],
                    'citizen2_name': template2['citizen_name'],
                    'citizen2_id': template2['citizen_digital_id'],
                    'tenant2_name': template2['tenant_name'],
                    'match_score': duplicate['match_score'],
                    'confidence': duplicate['confidence'],
                    'thumb_type1': template1['thumb_type'],
                    'thumb_type2': template2['thumb_type']
                }
                
                if detailed:
                    row.update({
                        'template1_hash': template1['template_hash'],
                        'template2_hash': template2['template_hash']
                    })
                
                writer.writerow(row)
        
        self.stdout.write(f'📄 Results saved to: {output_file}')
