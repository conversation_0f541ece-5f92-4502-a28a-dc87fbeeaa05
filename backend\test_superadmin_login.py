#!/usr/bin/env python3
"""
Test script to verify superadmin login and permissions
"""
import requests
import json
import jwt
from datetime import datetime

def test_superadmin_login():
    """Test superadmin login and check permissions"""
    
    print("🔍 Testing Superadmin Login and Permissions")
    print("=" * 50)
    
    # Login credentials
    login_data = {
        'email': '<EMAIL>',
        'password': 'admin123'
    }
    
    try:
        # Test login
        print("1. Testing login...")
        response = requests.post('http://localhost:8000/api/auth/token/', json=login_data)
        
        if response.status_code != 200:
            print(f"❌ Login failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
        print("✅ Login successful!")
        
        # Parse response
        data = response.json()
        access_token = data['access']
        
        # Decode JWT token (without verification for inspection)
        print("\n2. Analyzing JWT token...")
        decoded = jwt.decode(access_token, options={"verify_signature": False})
        
        print(f"   User: {decoded.get('email')}")
        print(f"   Role: {decoded.get('role')}")
        print(f"   Is Superuser: {decoded.get('is_superuser')}")
        print(f"   Tenant ID: {decoded.get('tenant_id', 'None')}")
        
        # Check permissions
        permissions = decoded.get('permissions', [])
        print(f"\n3. Permissions analysis:")
        print(f"   Total permissions: {len(permissions)}")
        
        # Check key permissions for tenant management and system settings
        key_permissions = [
            'manage_tenants',
            'full_system_access', 
            'manage_system_settings',
            'view_system_dashboard',
            'manage_all_users'
        ]
        
        print(f"\n4. Key permissions check:")
        all_permissions_present = True
        for perm in key_permissions:
            has_perm = perm in permissions
            status = "✅" if has_perm else "❌"
            print(f"   {status} {perm}")
            if not has_perm:
                all_permissions_present = False
        
        # Test API access with token
        print(f"\n5. Testing API access...")
        headers = {'Authorization': f'Bearer {access_token}'}
        
        # Test tenant list endpoint
        tenant_response = requests.get('http://localhost:8000/api/tenants/', headers=headers)
        if tenant_response.status_code == 200:
            print("   ✅ Tenant API access successful")
            tenants = tenant_response.json()
            print(f"   Found {len(tenants.get('results', []))} tenants")
        else:
            print(f"   ❌ Tenant API access failed: {tenant_response.status_code}")
            all_permissions_present = False
        
        # Summary
        print(f"\n6. Summary:")
        if all_permissions_present:
            print("   ✅ Superadmin has all required permissions!")
            print("   ✅ Should have access to:")
            print("      - Tenant management (/tenants)")
            print("      - System settings (/system/settings)")
            print("      - User management (/users)")
            print("      - All dashboard features")
        else:
            print("   ❌ Some permissions are missing!")
            print("   🔧 Troubleshooting needed")
            
        return all_permissions_present
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False

if __name__ == "__main__":
    success = test_superadmin_login()
    exit(0 if success else 1)
