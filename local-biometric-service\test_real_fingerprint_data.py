#!/usr/bin/env python3
"""
Test Real Fingerprint Data Extraction
"""

import urllib.request
import urllib.parse
import json
import base64

def test_real_fingerprint_extraction():
    """Test if the JAR bridge now extracts real fingerprint data"""
    
    print("=" * 60)
    print("  TESTING REAL FINGERPRINT DATA EXTRACTION")
    print("=" * 60)
    
    print("\nThis test will:")
    print("1. Capture fingerprint using JAR bridge")
    print("2. Analyze the returned template")
    print("3. Verify it contains ACTUAL fingerprint data (not just metadata)")
    print("4. Show the biometric template structure")
    
    input("\nPress Enter to start capture test...")
    
    try:
        # Test capture
        print("\n📸 Capturing fingerprint...")
        print("💡 JAR application will open - please capture your fingerprint")
        
        capture_data = json.dumps({"thumb_type": "left"}).encode()
        
        req = urllib.request.Request(
            "http://localhost:8001/api/capture/fingerprint",
            data=capture_data,
            headers={'Content-Type': 'application/json'}
        )
        
        response = urllib.request.urlopen(req, timeout=40)
        
        if response.getcode() == 200:
            result = json.loads(response.read().decode())
            
            if result.get('success'):
                data = result.get('data', {})
                template_b64 = data.get('template', '')
                
                print(f"✅ Capture successful!")
                print(f"   Template length: {len(template_b64)} characters")
                
                # Decode and analyze the template
                if template_b64:
                    try:
                        template_json = base64.b64decode(template_b64).decode()
                        template_data = json.loads(template_json)
                        
                        print(f"\n🔍 TEMPLATE ANALYSIS:")
                        print(f"📋 Template structure:")
                        
                        # Show key fields
                        print(f"   Version: {template_data.get('version', 'N/A')}")
                        print(f"   Thumb type: {template_data.get('thumb_type', 'N/A')}")
                        print(f"   Image dimensions: {template_data.get('image_dimensions', 'N/A')}")
                        print(f"   Total pixels: {template_data.get('total_pixels', 'N/A')}")
                        print(f"   Data quality: {template_data.get('data_quality', 'N/A')}%")
                        print(f"   Extraction method: {template_data.get('extraction_method', 'N/A')}")
                        
                        # Check for actual biometric data
                        minutiae_points = template_data.get('minutiae_points', [])
                        template_hash = template_data.get('template_hash', '')
                        
                        print(f"\n🎯 BIOMETRIC DATA ANALYSIS:")
                        print(f"   Minutiae points: {len(minutiae_points)}")
                        print(f"   Template hash: {template_hash[:16]}... (truncated)")
                        
                        if len(minutiae_points) > 0:
                            print(f"\n📊 SAMPLE MINUTIAE POINTS:")
                            for i, point in enumerate(minutiae_points[:3]):  # Show first 3
                                print(f"   Point {i+1}: x={point.get('x')}, y={point.get('y')}, angle={point.get('angle')}°, type={point.get('type')}")
                        
                        # Check for additional real data
                        additional_data = data.get('raw_image_data', '')
                        image_hash = data.get('image_hash', '')
                        frame_size = data.get('frame_size_actual', 0)
                        
                        print(f"\n📸 ADDITIONAL REAL DATA:")
                        print(f"   Raw image data: {len(additional_data)} characters")
                        print(f"   Image hash: {image_hash[:16]}... (truncated)")
                        print(f"   Frame size: {frame_size} bytes")
                        
                        # Determine if this is real data
                        real_indicators = 0
                        
                        if len(minutiae_points) >= 15:
                            real_indicators += 1
                            print(f"   ✅ Sufficient minutiae points ({len(minutiae_points)})")
                        
                        if template_hash and len(template_hash) > 20:
                            real_indicators += 1
                            print(f"   ✅ Valid template hash")
                        
                        if frame_size > 1000:
                            real_indicators += 1
                            print(f"   ✅ Realistic frame size ({frame_size} bytes)")
                        
                        if len(additional_data) > 100:
                            real_indicators += 1
                            print(f"   ✅ Contains raw image data")
                        
                        if template_data.get('extraction_method') == 'futronic_jar_real_capture':
                            real_indicators += 1
                            print(f"   ✅ Real capture method confirmed")
                        
                        print(f"\n" + "="*50)
                        print("FINAL ASSESSMENT")
                        print("="*50)
                        
                        if real_indicators >= 4:
                            print(f"🎉 SUCCESS: REAL FINGERPRINT DATA DETECTED!")
                            print(f"✅ Template contains actual biometric information")
                            print(f"✅ {len(minutiae_points)} minutiae points extracted")
                            print(f"✅ {frame_size} bytes of real capture data")
                            print(f"✅ Unique template hash: {template_hash[:16]}...")
                            print(f"✅ Ready for duplicate detection and matching")
                            
                            return True
                        else:
                            print(f"⚠️ PARTIAL: Some real data detected ({real_indicators}/5 indicators)")
                            print(f"💡 Template contains more than just metadata")
                            print(f"💡 May need further improvements for full biometric matching")
                            
                            return False
                        
                    except Exception as decode_error:
                        print(f"❌ Error decoding template: {decode_error}")
                        return False
                else:
                    print(f"❌ No template data received")
                    return False
            else:
                print(f"❌ Capture failed: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP error: {response.getcode()}")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def main():
    print("Real Fingerprint Data Extraction Test")
    print("=" * 40)
    
    print("Prerequisites:")
    print("✅ JAR Bridge service running (localhost:8001)")
    print("✅ Futronic device connected")
    print("✅ Updated JAR bridge with real data extraction")
    
    success = test_real_fingerprint_extraction()
    
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    if success:
        print("🎉 SUCCESS!")
        print("✅ JAR bridge now extracts REAL fingerprint data")
        print("✅ Template contains actual biometric information")
        print("✅ Minutiae points, image data, and hashes included")
        print("✅ Ready for production biometric matching")
        
        print("\nNext steps:")
        print("1. Templates now contain real fingerprint data")
        print("2. Duplicate detection will work with actual biometrics")
        print("3. System provides genuine fraud prevention")
        
    else:
        print("⚠️ NEEDS IMPROVEMENT")
        print("❌ Template may still lack sufficient biometric data")
        print("💡 Consider further enhancements to data extraction")
        
        print("\nTroubleshooting:")
        print("1. Ensure JAR bridge service was restarted")
        print("2. Check that JAR application captures successfully")
        print("3. Verify real data extraction is working")
    
    print("\nTest completed.")
    input("Press Enter to exit...")

if __name__ == '__main__':
    main()
