"""
Ethiopian Calendar Utilities for Django Backend

The Ethiopian calendar has 13 months:
- 12 months of 30 days each
- 1 month (Pagume) of 5 days (6 in leap years)
- Ethiopian year starts on September 11 (or 12 in leap years) of Gregorian calendar
- Ethiopian calendar is approximately 7-8 years behind Gregorian calendar
"""

from datetime import date, datetime, timedelta
from typing import Optional, Dict, Union


# Ethiopian month names
ETHIOPIAN_MONTHS_AM = [
    'መስከረም',  # Meskerem (September)
    'ጥቅምት',    # Tikimt (October)
    'ኅዳር',     # <PERSON><PERSON> (November)
    'ታኅሳስ',    # <PERSON><PERSON><PERSON> (December)
    'ጥር',      # Tir (January)
    'የካቲት',    # <PERSON>katit (February)
    'መጋቢት',    # Megabit (March)
    'ሚያዝያ',    # <PERSON><PERSON> (April)
    'ግንቦት',    # <PERSON><PERSON><PERSON> (May)
    'ሰኔ',      # <PERSON><PERSON> (June)
    'ሐምሌ',     # <PERSON><PERSON> (July)
    'ነሐሴ',     # Nehase (August)
    'ጳጉሜ'      # Pagume (September - 5/6 days)
]

ETHIOPIAN_MONTHS_EN = [
    'Meskerem', 'Tikimt', 'Hidar', '<PERSON><PERSON><PERSON>', 'Tir', '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>gum<PERSON>'
]


def is_ethiopian_leap_year(ethiopian_year: int) -> bool:
    """
    Check if an Ethiopian year is a leap year
    
    Args:
        ethiopian_year: Ethiopian year
        
    Returns:
        True if leap year, False otherwise
    """
    return (ethiopian_year % 4) == 3


def get_days_in_ethiopian_month(month: int, year: int) -> int:
    """
    Get the number of days in an Ethiopian month
    
    Args:
        month: Month (1-13)
        year: Ethiopian year
        
    Returns:
        Number of days in the month
        
    Raises:
        ValueError: If month is invalid
    """
    if 1 <= month <= 12:
        return 30
    elif month == 13:
        return 6 if is_ethiopian_leap_year(year) else 5
    else:
        raise ValueError('Invalid Ethiopian month')


def gregorian_to_ethiopian(gregorian_date: Union[date, datetime]) -> Optional[Dict[str, int]]:
    """
    Convert Gregorian date to Ethiopian date using accurate algorithm

    Args:
        gregorian_date: Gregorian date

    Returns:
        Dictionary with keys 'year', 'month', 'day' or None if invalid
    """
    if not gregorian_date:
        return None

    if isinstance(gregorian_date, datetime):
        gregorian_date = gregorian_date.date()

    # Ethiopian calendar epoch: September 11, 8 AD (Gregorian)
    # But we need to use a more accurate conversion algorithm

    year = gregorian_date.year
    month = gregorian_date.month
    day = gregorian_date.day

    # Convert to Julian Day Number first for accuracy
    if month <= 2:
        year -= 1
        month += 12

    a = year // 100
    b = 2 - a + (a // 4)

    jdn = int(365.25 * (year + 4716)) + int(30.6001 * (month + 1)) + day + b - 1524

    # Convert Julian Day Number to Ethiopian date
    # Ethiopian epoch JDN is 1724221 (September 11, 8 AD Gregorian)
    ethiopian_jdn = jdn - 1724220

    if ethiopian_jdn < 1:
        return None

    # Calculate Ethiopian year
    ethiopian_year = ((ethiopian_jdn - 1) // 365) + 1

    # Adjust for leap years
    leap_adjustments = (ethiopian_year - 1) // 4
    ethiopian_year = ((ethiopian_jdn - 1 - leap_adjustments) // 365) + 1

    # Calculate day of Ethiopian year
    leap_adjustments = (ethiopian_year - 1) // 4
    day_of_year = ethiopian_jdn - ((ethiopian_year - 1) * 365 + leap_adjustments)

    # Calculate Ethiopian month and day
    if day_of_year <= 360:  # First 12 months (30 days each)
        ethiopian_month = ((day_of_year - 1) // 30) + 1
        ethiopian_day = ((day_of_year - 1) % 30) + 1
    else:  # Pagume (13th month)
        ethiopian_month = 13
        ethiopian_day = day_of_year - 360

    return {
        'year': ethiopian_year,
        'month': ethiopian_month,
        'day': ethiopian_day
    }


def ethiopian_to_gregorian(year: int, month: int, day: int) -> Optional[date]:
    """
    Convert Ethiopian date to Gregorian date using accurate algorithm

    Args:
        year: Ethiopian year
        month: Ethiopian month (1-13)
        day: Ethiopian day

    Returns:
        Gregorian date or None if invalid

    Raises:
        ValueError: If Ethiopian date is invalid
    """
    if not year or not month or not day:
        return None

    # Validate Ethiopian date
    if month < 1 or month > 13 or day < 1:
        raise ValueError('Invalid Ethiopian date')

    if month <= 12 and day > 30:
        raise ValueError('Invalid day for Ethiopian month')

    if month == 13 and day > get_days_in_ethiopian_month(13, year):
        raise ValueError('Invalid day for Pagume')

    # Calculate day of Ethiopian year
    if month <= 12:
        day_of_year = (month - 1) * 30 + day
    else:
        day_of_year = 360 + day

    # Calculate leap year adjustments
    leap_adjustments = (year - 1) // 4

    # Calculate Ethiopian Julian Day Number
    ethiopian_jdn = (year - 1) * 365 + leap_adjustments + day_of_year + 1724220

    # Convert Julian Day Number to Gregorian date
    a = ethiopian_jdn + 32044
    b = (4 * a + 3) // 146097
    c = a - (146097 * b) // 4
    d = (4 * c + 3) // 1461
    e = c - (1461 * d) // 4
    m = (5 * e + 2) // 153

    gregorian_day = e - (153 * m + 2) // 5 + 1
    gregorian_month = m + 3 - 12 * (m // 10)
    gregorian_year = 100 * b + d - 4800 + m // 10

    try:
        return date(gregorian_year, gregorian_month, gregorian_day)
    except ValueError:
        return None


def format_ethiopian_date(ethiopian_date: Dict[str, int], format_str: str = 'DD/MM/YYYY', language: str = 'am') -> str:
    """
    Format Ethiopian date as string
    
    Args:
        ethiopian_date: Dictionary with keys 'year', 'month', 'day'
        format_str: Format string ('DD/MM/YYYY', 'DD MMM YYYY', etc.)
        language: 'am' for Amharic, 'en' for English
        
    Returns:
        Formatted date string
    """
    if not ethiopian_date or not all(k in ethiopian_date for k in ['year', 'month', 'day']):
        return ''
    
    year = ethiopian_date['year']
    month = ethiopian_date['month']
    day = ethiopian_date['day']
    
    month_names = ETHIOPIAN_MONTHS_AM if language == 'am' else ETHIOPIAN_MONTHS_EN
    
    padded_day = str(day).zfill(2)
    padded_month = str(month).zfill(2)
    
    if format_str == 'DD/MM/YYYY':
        return f"{padded_day}/{padded_month}/{year}"
    elif format_str == 'DD-MM-YYYY':
        return f"{padded_day}-{padded_month}-{year}"
    elif format_str == 'DD MMM YYYY':
        return f"{padded_day} {month_names[month - 1]} {year}"
    elif format_str == 'MMM DD, YYYY':
        return f"{month_names[month - 1]} {padded_day}, {year}"
    elif format_str == 'YYYY-MM-DD':
        return f"{year}-{padded_month}-{padded_day}"
    else:
        return f"{padded_day}/{padded_month}/{year}"


def parse_ethiopian_date(date_string: str) -> Optional[Dict[str, int]]:
    """
    Parse Ethiopian date string
    
    Args:
        date_string: Date string in format 'DD/MM/YYYY' or 'DD-MM-YYYY'
        
    Returns:
        Dictionary with keys 'year', 'month', 'day' or None if invalid
    """
    if not date_string:
        return None
    
    separators = ['/', '-', '.']
    parts = None
    
    for separator in separators:
        if separator in date_string:
            parts = date_string.split(separator)
            break
    
    if not parts or len(parts) != 3:
        return None
    
    try:
        day = int(parts[0])
        month = int(parts[1])
        year = int(parts[2])
        
        return {'year': year, 'month': month, 'day': day}
    except ValueError:
        return None


def get_current_ethiopian_date() -> Dict[str, int]:
    """
    Get current Ethiopian date
    
    Returns:
        Dictionary with keys 'year', 'month', 'day'
    """
    return gregorian_to_ethiopian(date.today())


def calculate_ethiopian_age(birth_date: Dict[str, int], current_date: Optional[Dict[str, int]] = None) -> Optional[int]:
    """
    Calculate age in Ethiopian calendar
    
    Args:
        birth_date: Ethiopian birth date dictionary
        current_date: Current Ethiopian date (optional)
        
    Returns:
        Age in years or None if invalid
    """
    if not birth_date:
        return None
    
    current = current_date or get_current_ethiopian_date()
    if not current:
        return None
    
    age = current['year'] - birth_date['year']
    
    # Adjust if birthday hasn't occurred this year
    if (current['month'] < birth_date['month'] or 
        (current['month'] == birth_date['month'] and current['day'] < birth_date['day'])):
        age -= 1
    
    return age


def is_valid_ethiopian_date(ethiopian_date: Dict[str, int]) -> bool:
    """
    Check if Ethiopian date is valid
    
    Args:
        ethiopian_date: Dictionary with keys 'year', 'month', 'day'
        
    Returns:
        True if valid, False otherwise
    """
    if not ethiopian_date or not all(k in ethiopian_date for k in ['year', 'month', 'day']):
        return False
    
    year = ethiopian_date['year']
    month = ethiopian_date['month']
    day = ethiopian_date['day']
    
    if year < 1 or month < 1 or month > 13 or day < 1:
        return False
    
    try:
        max_days = get_days_in_ethiopian_month(month, year)
        return day <= max_days
    except ValueError:
        return False


def add_days_to_ethiopian_date(ethiopian_date: Dict[str, int], days: int) -> Optional[Dict[str, int]]:
    """
    Add days to Ethiopian date
    
    Args:
        ethiopian_date: Ethiopian date dictionary
        days: Number of days to add
        
    Returns:
        New Ethiopian date dictionary or None if invalid
    """
    if not ethiopian_date or days == 0:
        return ethiopian_date
    
    # Convert to Gregorian, add days, convert back
    gregorian_date = ethiopian_to_gregorian(
        ethiopian_date['year'], 
        ethiopian_date['month'], 
        ethiopian_date['day']
    )
    
    if not gregorian_date:
        return None
    
    new_gregorian_date = gregorian_date + timedelta(days=days)
    return gregorian_to_ethiopian(new_gregorian_date)


def add_months_to_ethiopian_date(ethiopian_date: Dict[str, int], months: int) -> Optional[Dict[str, int]]:
    """
    Add months to Ethiopian date
    
    Args:
        ethiopian_date: Ethiopian date dictionary
        months: Number of months to add
        
    Returns:
        New Ethiopian date dictionary or None if invalid
    """
    if not ethiopian_date or months == 0:
        return ethiopian_date
    
    year = ethiopian_date['year']
    month = ethiopian_date['month']
    day = ethiopian_date['day']
    
    month += months
    
    # Handle year overflow
    while month > 13:
        month -= 13
        year += 1
    
    while month < 1:
        month += 13
        year -= 1
    
    # Adjust day if it exceeds the month's days
    max_days = get_days_in_ethiopian_month(month, year)
    if day > max_days:
        day = max_days
    
    return {'year': year, 'month': month, 'day': day}


def add_years_to_ethiopian_date(ethiopian_date: Dict[str, int], years: int) -> Optional[Dict[str, int]]:
    """
    Add years to Ethiopian date
    
    Args:
        ethiopian_date: Ethiopian date dictionary
        years: Number of years to add
        
    Returns:
        New Ethiopian date dictionary or None if invalid
    """
    if not ethiopian_date or years == 0:
        return ethiopian_date
    
    year = ethiopian_date['year'] + years
    month = ethiopian_date['month']
    day = ethiopian_date['day']
    
    # Adjust day if it's Pagume 6th and new year is not leap
    if month == 13 and day == 6 and not is_ethiopian_leap_year(year):
        day = 5
    
    return {'year': year, 'month': month, 'day': day}
