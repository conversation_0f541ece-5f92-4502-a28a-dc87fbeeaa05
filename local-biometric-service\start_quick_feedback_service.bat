@echo off
echo ⚡ Quick Feedback Biometric Service
echo ===================================
echo.
echo 🔧 FEATURES:
echo    - Immediate feedback when no finger detected (3 seconds)
echo    - Clear error messages for users
echo    - Quality validation
echo    - No long timeouts or hanging
echo.
echo 📱 Service URL: http://localhost:8001
echo 🌐 Health Check: http://localhost:8001/api/health
echo.
echo ⚠️  INSTRUCTIONS:
echo    - This service detects finger placement quickly
echo    - If no finger is detected, you get immediate feedback
echo    - Place finger BEFORE clicking capture for best results
echo    - Service will tell you exactly what to do if capture fails
echo.
echo 🔧 TROUBLESHOOTING:
echo    - "No finger detected" = Place finger on scanner and retry
echo    - "Poor quality" = Clean finger and place firmly
echo    - "Device not connected" = Check USB connection
echo.

REM Check if DLL files exist
if not exist "ftrScanAPI.dll" (
    echo ❌ ERROR: ftrScanAPI.dll not found!
    echo    Please copy Futronic SDK DLL files to this directory
    pause
    exit /b 1
)

echo 🚀 Starting Quick Feedback Service...
echo.
echo ⚠️  Keep this window open while using GoID system
echo ❌ Close this window to stop the biometric service
echo.

python quick_feedback_biometric_service.py

echo.
echo Service stopped.
pause
