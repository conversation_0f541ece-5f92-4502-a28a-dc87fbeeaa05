@echo off
echo ========================================
echo   GoID Stable Biometric Service
echo ========================================
echo.
echo This is a simplified, more stable version
echo that uses direct frame capture without
echo finger detection to avoid SDK crashes.
echo.
echo 🔍 Pre-flight checks...
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)
echo ✅ Python found

REM Check if we're in the right directory
if not exist "local-biometric-service" (
    echo ❌ local-biometric-service directory not found
    echo Please run this script from the GoID root directory
    pause
    exit /b 1
)
echo ✅ Service directory found

REM Check for DLL files
cd local-biometric-service
if exist "ftrScanAPI.dll" (
    echo ✅ ftrScanAPI.dll found
) else (
    echo ⚠️ ftrScanAPI.dll not found in local-biometric-service directory
    echo Please copy Futronic SDK DLL files to this directory
)

echo.
echo 🚀 Starting STABLE service on port 8001...
echo 📱 Service URL: http://localhost:8001
echo 🌐 Health Check: http://localhost:8001/api/health
echo.
echo ⚠️  IMPORTANT - STABLE MODE:
echo    - Place your finger on the scanner BEFORE clicking capture
echo    - This version skips finger detection to avoid crashes
echo    - Keep this window open while using GoID
echo    - The service will be accessible from Docker containers
echo.
echo ❌ Close this window to stop the service
echo ⌨️  Press Ctrl+C to stop the service gracefully
echo.

python stable_fingerprint_service.py

echo.
echo Stable service stopped.
echo.
echo If you still experience issues:
echo 1. Verify the Futronic device is properly connected
echo 2. Check that DLL files are in the correct location
echo 3. Ensure no other software is using the device
echo 4. Try using the web interface at http://localhost:8001
echo.
pause
