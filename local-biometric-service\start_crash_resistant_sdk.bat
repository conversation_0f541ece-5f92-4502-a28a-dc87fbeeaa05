@echo off
echo ========================================
echo   Crash-Resistant Futronic SDK Service
echo   Process Isolation - Never Crashes
echo ========================================
echo.

echo Checking Python installation...
python --version
if errorlevel 1 (
    echo ERROR: Python not found. Please install Python 3.7+
    pause
    exit /b 1
)

echo.
echo Checking required files...
if not exist "ftrScanAPI.dll" (
    echo ERROR: ftrScanAPI.dll not found
    echo Please ensure Futronic SDK files are in the current directory
    pause
    exit /b 1
)

if not exist "sdk_worker.py" (
    echo ERROR: sdk_worker.py not found
    echo Please ensure all service files are present
    pause
    exit /b 1
)

echo SUCCESS: All required files found

echo.
echo Installing required Python packages...
pip install flask flask-cors

echo.
echo Starting Crash-Resistant Futronic SDK Service...
echo Service will be available at: http://localhost:8001
echo.
echo This service uses process isolation to prevent crashes
echo Press Ctrl+C to stop the service
echo.

python crash_resistant_sdk_service.py

echo.
echo Service stopped.
pause
