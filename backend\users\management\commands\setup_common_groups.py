from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from users.models_groups import TenantGroup, GroupTemplate
from tenants.models import TenantType


class Command(BaseCommand):
    help = 'Setup common groups for all tenants with proper permissions and tenant type restrictions'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up common groups...'))
        
        # Define common groups with their permissions and allowed tenant types
        common_groups = {
            'clerk': {
                'description': 'Kebele clerk with basic citizen and ID card management permissions',
                'group_type': 'operational',
                'level': 10,
                'allowed_tenant_types': ['kebele'],
                'permissions': [
                    'view_citizen',
                    'add_citizen',
                    'change_citizen',
                    'view_idcard',
                    'add_idcard',
                    'change_idcard'
                ]
            },
            'kebele_leader': {
                'description': 'Kebele leader with approval and reporting permissions',
                'group_type': 'administrative',
                'level': 30,
                'allowed_tenant_types': ['kebele'],
                'permissions': [
                    'view_citizen',
                    'view_idcard'
                ]
            },
            'subcity_admin': {
                'description': 'Subcity administrator with user management and oversight permissions',
                'group_type': 'administrative',
                'level': 50,
                'allowed_tenant_types': ['subcity'],
                'permissions': [
                    'view_citizen',
                    'view_idcard',
                    'add_user',
                    'change_user',
                    'view_user'
                ]
            },
            'city_admin': {
                'description': 'City administrator with full administrative permissions',
                'group_type': 'administrative',
                'level': 70,
                'allowed_tenant_types': ['city'],
                'permissions': [
                    'view_citizen',
                    'view_idcard',
                    'add_user',
                    'change_user',
                    'view_user',
                    'view_tenant'
                ]
            }
        }

        # Create or update common groups
        for group_name, group_config in common_groups.items():
            self.stdout.write(f'Setting up group: {group_name}')
            
            # Create or get Django Group
            django_group, created = Group.objects.get_or_create(name=group_name)
            if created:
                self.stdout.write(f'  ✅ Created Django Group: {group_name}')
            else:
                self.stdout.write(f'  ℹ️  Django Group already exists: {group_name}')
            
            # Create or update TenantGroup
            tenant_group, created = TenantGroup.objects.get_or_create(
                group=django_group,
                defaults={
                    'tenant': None,  # Global group
                    'description': group_config['description'],
                    'group_type': group_config['group_type'],
                    'level': group_config['level'],
                    'is_system_group': True,
                    'is_active': True,
                    'allowed_tenant_types': group_config['allowed_tenant_types']
                }
            )

            if created:
                self.stdout.write(f'  ✅ Created TenantGroup: {group_name}')
            else:
                # Update existing group
                tenant_group.description = group_config['description']
                tenant_group.group_type = group_config['group_type']
                tenant_group.level = group_config['level']
                tenant_group.is_system_group = True
                tenant_group.allowed_tenant_types = group_config['allowed_tenant_types']
                tenant_group.save()
                self.stdout.write(f'  ✅ Updated TenantGroup: {group_name}')
            
            # Get and assign permissions
            permission_codenames = group_config['permissions']
            permissions = Permission.objects.filter(codename__in=permission_codenames)
            
            found_permissions = list(permissions.values_list('codename', flat=True))
            missing_permissions = set(permission_codenames) - set(found_permissions)
            
            if missing_permissions:
                self.stdout.write(
                    self.style.WARNING(f'  ⚠️  Missing permissions for {group_name}: {", ".join(missing_permissions)}')
                )
            
            # Assign found permissions
            django_group.permissions.set(permissions)
            self.stdout.write(f'  ✅ Assigned {permissions.count()} permissions to {group_name}')

            # Log allowed tenant types
            tenant_types_str = ', '.join(group_config['allowed_tenant_types'])
            self.stdout.write(f'  ✅ Allowed for tenant types: {tenant_types_str}')

        self.stdout.write(self.style.SUCCESS('\n🎉 Common groups setup completed!'))
        
        # Display summary
        self.stdout.write('\n📋 Summary of Common Groups:')
        for group_name, group_config in common_groups.items():
            tenant_group = TenantGroup.objects.get(group__name=group_name, tenant=None)
            permission_count = tenant_group.group.permissions.count()
            allowed_types = ', '.join(group_config['allowed_tenant_types'])
            
            self.stdout.write(f'  • {group_name}:')
            self.stdout.write(f'    - Type: {group_config["group_type"]} (Level {group_config["level"]})')
            self.stdout.write(f'    - Permissions: {permission_count}')
            self.stdout.write(f'    - Allowed for: {allowed_types}')
            self.stdout.write('')

        self.stdout.write(self.style.SUCCESS('✅ All common groups are ready for use across all tenants!'))
        self.stdout.write('💡 Tenants can now assign these common groups to their users.')
        self.stdout.write('💡 Tenants can also create custom groups for specific needs.')
