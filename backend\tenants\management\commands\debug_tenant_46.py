from django.core.management.base import BaseCommand
from django_tenants.utils import schema_context
from tenants.models import Tenant
from tenants.models.citizen import Citizen
from django.db import connection


class Command(BaseCommand):
    help = 'Debug tenant 46 and check if it exists properly'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🔍 Debugging tenant 46...'))
        
        # List all tenants
        self.stdout.write(f'\n📋 All tenants in the system:')
        tenants = Tenant.objects.all()
        for tenant in tenants:
            self.stdout.write(f'  - ID {tenant.id}: {tenant.name} ({tenant.schema_name}) - Type: {type(tenant)}')
        
        # Check if tenant 46 exists
        try:
            tenant = Tenant.objects.get(id=46)
            self.stdout.write(f'\n✅ Found tenant 46: {tenant.name} ({tenant.schema_name})')
            self.stdout.write(f'   - Type: {type(tenant)}')
            self.stdout.write(f'   - Has name attribute: {hasattr(tenant, "name")}')
            self.stdout.write(f'   - Has schema_name attribute: {hasattr(tenant, "schema_name")}')
            
            # Check if schema exists
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT 1 FROM information_schema.schemata 
                        WHERE schema_name = %s
                    )
                """, [tenant.schema_name])
                schema_exists = cursor.fetchone()[0]
                self.stdout.write(f'   - Schema exists in database: {schema_exists}')
            
            # Try to use schema context
            try:
                with schema_context(tenant.schema_name):
                    self.stdout.write(f'   - Schema context works: ✅')
                    
                    # Check citizens table
                    with connection.cursor() as cursor:
                        cursor.execute(f'SELECT COUNT(*) FROM "{tenant.schema_name}".citizens_citizen')
                        citizen_count = cursor.fetchone()[0]
                        self.stdout.write(f'   - Citizens in schema: {citizen_count}')
                        
                        # List some citizens
                        if citizen_count > 0:
                            cursor.execute(f'SELECT id, first_name, last_name FROM "{tenant.schema_name}".citizens_citizen LIMIT 5')
                            citizens = cursor.fetchall()
                            self.stdout.write(f'   - Sample citizens:')
                            for c in citizens:
                                self.stdout.write(f'     * ID {c[0]}: {c[1]} {c[2]}')
                    
            except Exception as e:
                self.stdout.write(f'   - Schema context error: {str(e)}')
                
        except Tenant.DoesNotExist:
            self.stdout.write(f'\n❌ Tenant 46 does not exist!')
        except Exception as e:
            self.stdout.write(f'\n❌ Error getting tenant 46: {str(e)}')
            import traceback
            self.stdout.write(traceback.format_exc())
        
        self.stdout.write(self.style.SUCCESS('\n🎉 Debug completed!'))
