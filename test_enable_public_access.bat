@echo off
echo 🔧 Testing Public Access Enable for Tenant 5 (Gondar)...

echo.
echo 📋 Current status of tenant 5:
curl -X GET "http://localhost:8000/api/tenants/5/" ^
     -H "Content-Type: application/json"

echo.
echo.
echo 🔄 Attempting to enable public access for tenant 5...
echo Note: This might require authentication. You may need to add Authorization header.

REM Attempt to enable public access (this may require auth)
curl -X PATCH "http://localhost:8000/api/tenants/5/" ^
     -H "Content-Type: application/json" ^
     -d "{\"public_id_application_enabled\": true}"

echo.
echo.
echo 📋 Checking status after update:
curl -X GET "http://localhost:8000/api/tenants/5/" ^
     -H "Content-Type: application/json"

echo.
echo.
echo 🔍 Now check the browser console at http://localhost:3000/services
echo Look for debug messages starting with "🔍 PublicServicePortal:"

pause
