from django.core.management.base import BaseCommand
from django_tenants.utils import schema_context
from tenants.models import Tenant
from citizens.models import Citizen
from idcards.models import IDCard, IDCardTemplate
from django.db import connection
from datetime import date, timedelta


class Command(BaseCommand):
    help = 'Debug ID card citizen relationship for tenant 46'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🔍 Debugging ID card citizen relationship...'))
        
        tenant_id = 46
        citizen_id = 1
        
        try:
            # Get tenant 46
            tenant = Tenant.objects.get(id=tenant_id)
            self.stdout.write(f'📋 Tenant: {tenant.name} ({tenant.schema_name})')
            
            with schema_context(tenant.schema_name):
                self.stdout.write(f'🔍 Inside schema context: {tenant.schema_name}')
                
                # Check current schema
                with connection.cursor() as cursor:
                    cursor.execute("SELECT current_schema()")
                    current_schema = cursor.fetchone()[0]
                    self.stdout.write(f'🔍 Current PostgreSQL schema: {current_schema}')
                
                # Find citizen ID 1
                try:
                    citizen = Citizen.objects.get(id=citizen_id)
                    self.stdout.write(f'✅ Found citizen: {citizen.get_full_name()}')
                    self.stdout.write(f'   - Database ID: {citizen.id}')
                    self.stdout.write(f'   - Digital ID: {citizen.digital_id}')
                    self.stdout.write(f'   - First Name: {citizen.first_name}')
                    self.stdout.write(f'   - Last Name: {citizen.last_name}')
                    self.stdout.write(f'   - Model: {citizen._meta.db_table}')
                    
                    # Check if this matches the expected citizen
                    if citizen.digital_id == 'GOZO152076432185':
                        self.stdout.write(f'✅ This matches the expected citizen from frontend!')
                    else:
                        self.stdout.write(f'❌ This does NOT match expected digital ID: GOZO152076432185')
                        self.stdout.write(f'   Expected: GOZO152076432185')
                        self.stdout.write(f'   Found: {citizen.digital_id}')
                    
                except Citizen.DoesNotExist:
                    self.stdout.write(f'❌ Citizen ID {citizen_id} not found in schema {tenant.schema_name}')
                    
                    # List all citizens to see what's available
                    all_citizens = Citizen.objects.all()
                    self.stdout.write(f'📋 All citizens in {tenant.schema_name}:')
                    for c in all_citizens:
                        self.stdout.write(f'   - ID {c.id}: {c.get_full_name()} (Digital: {c.digital_id})')
                        if c.digital_id == 'GOZO152076432185':
                            self.stdout.write(f'     ⭐ This is the citizen the frontend expects!')
                    return
                
                # Check existing ID cards
                existing_cards = IDCard.objects.all()
                self.stdout.write(f'📋 Existing ID cards in {tenant.schema_name}: {existing_cards.count()}')
                for card in existing_cards:
                    self.stdout.write(f'   - Card: {card.card_number}')
                    if card.citizen:
                        self.stdout.write(f'     Citizen: {card.citizen.get_full_name()} (ID: {card.citizen.id})')
                    else:
                        self.stdout.write(f'     Citizen: NULL/MISSING')
                
                # Try to create a test ID card
                try:
                    template = IDCardTemplate.objects.first()
                    if not template:
                        self.stdout.write(f'❌ No templates found')
                        return
                    
                    self.stdout.write(f'🔧 Creating test ID card...')
                    self.stdout.write(f'   - Citizen: {citizen.get_full_name()} (ID: {citizen.id})')
                    self.stdout.write(f'   - Template: {template.name}')
                    
                    test_card = IDCard.objects.create(
                        citizen=citizen,
                        template=template,
                        issue_date=date.today(),
                        expiry_date=date.today() + timedelta(days=365*5),
                        status='draft'
                    )
                    
                    self.stdout.write(f'✅ Test ID card created: {test_card.card_number}')
                    
                    # Verify the citizen relationship
                    if test_card.citizen:
                        self.stdout.write(f'✅ Citizen relationship intact: {test_card.citizen.get_full_name()}')
                        self.stdout.write(f'   - Citizen ID: {test_card.citizen.id}')
                        self.stdout.write(f'   - Digital ID: {test_card.citizen.digital_id}')
                    else:
                        self.stdout.write(f'❌ Citizen relationship is NULL!')
                    
                except Exception as e:
                    self.stdout.write(f'❌ Error creating test ID card: {str(e)}')
                    import traceback
                    self.stdout.write(traceback.format_exc())
                
        except Tenant.DoesNotExist:
            self.stdout.write(f'❌ Tenant {tenant_id} not found')
        except Exception as e:
            self.stdout.write(f'❌ Error: {str(e)}')
            import traceback
            self.stdout.write(traceback.format_exc())
        
        self.stdout.write(self.style.SUCCESS('\n🎉 Debug completed!'))
