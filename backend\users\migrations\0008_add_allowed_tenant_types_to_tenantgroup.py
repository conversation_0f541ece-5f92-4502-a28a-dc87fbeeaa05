# Generated by Django 4.2.7 on 2025-06-16 09:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0007_tenantgroup_grouptemplate_groupmembership'),
    ]

    operations = [
        migrations.AddField(
            model_name='tenantgroup',
            name='allowed_tenant_types',
            field=models.JSONField(blank=True, default=list, help_text='List of tenant types that can use this group (empty = all types allowed)'),
        ),
    ]
