@echo off
echo 🧪 Testing with Python Launcher (32-bit)
echo ========================================
echo.

REM Try to use Python launcher to force 32-bit
echo 🔍 Checking available Python versions...
py -0

echo.
echo 🧪 Testing with 32-bit Python launcher...
py -3-32 -c "import platform, struct; print(f'Python: {platform.architecture()[0]} - {\"64-bit\" if struct.calcsize(\"P\")*8==64 else \"32-bit\"}')"

if %errorlevel% neq 0 (
    echo ❌ 32-bit Python not available via launcher
    echo Please install 32-bit Python manually
    pause
    exit /b 1
)

echo.
echo 📦 Installing dependencies for 32-bit Python...
py -3-32 -m pip install Flask Flask-CORS requests

echo.
echo 🔧 Testing SDK compatibility...
py -3-32 check_dll_architecture.py

echo.
echo 🧪 Testing Futronic SDK...
py -3-32 test_futronic_sdk.py

pause
