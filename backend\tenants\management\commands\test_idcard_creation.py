from django.core.management.base import BaseCommand
from django_tenants.utils import schema_context
from tenants.models import Tenant
from idcards.models import ID<PERSON>ardTemplate, IDCard
from tenants.models.citizen import Citizen
from datetime import date, timedelta


class Command(BaseCommand):
    help = 'Test ID card creation to verify the fix'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant-id',
            type=int,
            default=42,
            help='Tenant ID to test with (default: 42)',
        )
        parser.add_argument(
            '--create-test-card',
            action='store_true',
            help='Actually create a test ID card',
        )

    def handle(self, *args, **options):
        tenant_id = options.get('tenant_id', 42)
        create_test_card = options.get('create_test_card', False)

        self.stdout.write(self.style.SUCCESS('🧪 Testing ID card creation...'))

        # First, let's find where citizen with ID 13 actually exists
        self.stdout.write(f'\n🔍 Searching for citizen with ID 13 across all tenants...')
        tenants = Tenant.objects.all()
        citizen_found_in = []

        for tenant in tenants:
            try:
                with schema_context(tenant.schema_name):
                    citizen_13 = Citizen.objects.filter(id=13).first()
                    if citizen_13:
                        citizen_found_in.append((tenant, citizen_13))
                        self.stdout.write(f'  ✅ Found citizen ID 13 in {tenant.name} ({tenant.schema_name}): {citizen_13.get_full_name()} - Digital ID: {citizen_13.digital_id}')
            except Exception as e:
                self.stdout.write(f'  ⚠️  Error checking {tenant.schema_name}: {str(e)}')

        if not citizen_found_in:
            self.stdout.write(f'  ❌ Citizen with ID 13 not found in any tenant schema!')

        # Test with specified tenant ID
        try:
            tenant = Tenant.objects.get(id=tenant_id)
            self.stdout.write(f'\n📋 Testing with tenant: {tenant.name} ({tenant.schema_name})')

            with schema_context(tenant.schema_name):
                # Check templates
                templates = IDCardTemplate.objects.all()
                self.stdout.write(f'  📄 Found {templates.count()} templates')

                if templates.count() == 0:
                    self.stdout.write(self.style.ERROR('  ❌ No templates found!'))
                    return

                # List templates
                for template in templates:
                    self.stdout.write(f'    - {template.name} (Default: {template.is_default})')

                # Check citizens
                citizens = Citizen.objects.all()
                self.stdout.write(f'  👥 Found {citizens.count()} citizens in {tenant.schema_name}')

                if citizens.count() == 0:
                    self.stdout.write(self.style.WARNING('  ⚠️  No citizens found to test with'))
                    return

                # List all citizens with their IDs
                self.stdout.write(f'  📋 Citizens in {tenant.schema_name}:')
                for citizen in citizens:
                    self.stdout.write(f'    - ID {citizen.id}: {citizen.get_full_name()} (Digital: {citizen.digital_id})')

                # Test creating an ID card with the first citizen and default template
                citizen = citizens.first()
                default_template = templates.filter(is_default=True).first()

                if not default_template:
                    default_template = templates.first()

                self.stdout.write(f'  🆔 Testing ID card creation for citizen: {citizen.first_name} {citizen.last_name} (ID: {citizen.id})')
                self.stdout.write(f'  📄 Using template: {default_template.name}')

                # Check if citizen already has an ID card
                existing_cards = IDCard.objects.filter(citizen=citizen)
                if existing_cards.exists():
                    self.stdout.write(f'  ✅ Citizen already has {existing_cards.count()} ID card(s)')
                    for card in existing_cards:
                        self.stdout.write(f'    - Card: {card.card_number} (Status: {card.status})')
                else:
                    self.stdout.write(f'  📝 Citizen has no existing ID cards')

                # Create a test ID card if requested
                if create_test_card:
                    try:
                        self.stdout.write(f'  🔧 Creating test ID card...')
                        test_card = IDCard.objects.create(
                            citizen=citizen,
                            template=default_template,
                            issue_date=date.today(),
                            expiry_date=date.today() + timedelta(days=365*5),  # 5 years
                            status='draft'
                        )
                        self.stdout.write(f'  ✅ Test ID card created successfully!')
                        self.stdout.write(f'    - Card Number: {test_card.card_number}')
                        self.stdout.write(f'    - Status: {test_card.status}')
                        self.stdout.write(f'    - Issue Date: {test_card.issue_date}')
                        self.stdout.write(f'    - Expiry Date: {test_card.expiry_date}')
                    except Exception as create_error:
                        self.stdout.write(self.style.ERROR(f'  ❌ Failed to create test ID card: {str(create_error)}'))
                        import traceback
                        self.stdout.write(traceback.format_exc())
                        return

                self.stdout.write(self.style.SUCCESS('  ✅ ID card system is working correctly!'))

        except Tenant.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'❌ Tenant with ID {tenant_id} not found'))
            # List available tenants
            tenants = Tenant.objects.all()
            self.stdout.write('Available tenants:')
            for t in tenants:
                self.stdout.write(f'  - ID {t.id}: {t.name} ({t.schema_name})')
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Error during test: {str(e)}'))
            import traceback
            self.stdout.write(traceback.format_exc())

        self.stdout.write(self.style.SUCCESS('\n🎉 Test completed!'))
