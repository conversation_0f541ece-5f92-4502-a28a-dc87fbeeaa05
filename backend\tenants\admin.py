from django.contrib import admin
from .models import Tenant, Domain
from .models.city import CityAdministration, Region, Country
from .models.subcity import SubCity
from .models.kebele import <PERSON><PERSON><PERSON>, <PERSON>tena
from .models.citizen import (
    Religion, CitizenStatus, MaritalStatus, DocumentType,
    EmploymentType, Document, Parent, Child,
    EmergencyContact, Spouse, Citizen, Biometric, Photo,
    Relationship, CurrentStatus
)


@admin.register(Tenant)
class TenantAdmin(admin.ModelAdmin):
    list_display = ('name', 'type', 'parent', 'schema_name', 'created_on')
    list_filter = ('type',)
    search_fields = ('name', 'schema_name')


@admin.register(Domain)
class DomainAdmin(admin.ModelAdmin):
    list_display = ('domain', 'tenant', 'is_primary')
    list_filter = ('is_primary',)
    search_fields = ('domain',)


@admin.register(Country)
class CountryAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'capital_city')
    search_fields = ('name', 'code')


@admin.register(Region)
class RegionAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'country')
    list_filter = ('country',)
    search_fields = ('name', 'code')


@admin.register(CityAdministration)
class CityAdministrationAdmin(admin.ModelAdmin):
    list_display = ('city_name', 'city_code', 'region', 'country', 'is_active')
    list_filter = ('is_active', 'country', 'region')
    search_fields = ('city_name', 'city_code')


@admin.register(SubCity)
class SubCityAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'city', 'is_active')
    list_filter = ('is_active', 'city')
    search_fields = ('name', 'code')


@admin.register(Kebele)
class KebeleAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'sub_city', 'is_active')
    list_filter = ('is_active', 'sub_city')
    search_fields = ('name', 'code')


@admin.register(Ketena)
class KetenaAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'kebele', 'is_active')
    list_filter = ('is_active', 'kebele')
    search_fields = ('name', 'code')


@admin.register(Religion)
class ReligionAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name',)


@admin.register(CitizenStatus)
class CitizenStatusAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name',)


@admin.register(MaritalStatus)
class MaritalStatusAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name',)


@admin.register(DocumentType)
class DocumentTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'description', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name', 'description')


@admin.register(EmploymentType)
class EmploymentTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'description', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name', 'description')


@admin.register(Document)
class DocumentAdmin(admin.ModelAdmin):
    list_display = ('document_type', 'citizen', 'issue_date', 'expiry_date', 'is_active')
    list_filter = ('document_type', 'is_active')
    search_fields = ('citizen__first_name', 'citizen__last_name', 'document_type__name')
    date_hierarchy = 'issue_date'


@admin.register(Parent)
class ParentAdmin(admin.ModelAdmin):
    list_display = ('first_name', 'middle_name', 'last_name', 'nationality', 'is_active')
    list_filter = ('nationality', 'is_active')
    search_fields = ('first_name', 'middle_name', 'last_name')


@admin.register(Child)
class ChildAdmin(admin.ModelAdmin):
    list_display = ('first_name', 'last_name', 'citizen', 'date_of_birth', 'is_active')
    list_filter = ('nationality', 'is_active')
    search_fields = ('first_name', 'last_name', 'citizen__first_name', 'citizen__last_name')
    date_hierarchy = 'date_of_birth'


@admin.register(EmergencyContact)
class EmergencyContactAdmin(admin.ModelAdmin):
    list_display = ('first_name', 'last_name', 'citizen', 'relationship', 'phone', 'primary_contact', 'is_active')
    list_filter = ('primary_contact', 'is_active', 'nationality')
    search_fields = ('first_name', 'last_name', 'phone', 'email', 'citizen__first_name', 'citizen__last_name')


@admin.register(Spouse)
class SpouseAdmin(admin.ModelAdmin):
    list_display = ('first_name', 'last_name', 'citizen', 'phone', 'primary_contact', 'is_active')
    list_filter = ('primary_contact', 'is_active', 'nationality')
    search_fields = ('first_name', 'last_name', 'phone', 'email', 'citizen__first_name', 'citizen__last_name')


@admin.register(Citizen)
class CitizenAdmin(admin.ModelAdmin):
    list_display = ('first_name', 'last_name', 'digital_id', 'is_active', 'is_resident')
    list_filter = ('is_active', 'is_resident', 'status', 'current_status', 'marital_status', 'religion')
    search_fields = ('first_name', 'last_name', 'digital_id', 'phone', 'email')
    fieldsets = (
        ('Basic Information', {
            'fields': ('digital_id', 'first_name', 'middle_name', 'last_name', 'first_name_am', 'middle_name_am', 'last_name_am')
        }),
        ('Location', {
            'fields': ('subcity', 'kebele', 'ketena', 'house_number', 'region', 'nationality')
        }),
        ('Contact', {
            'fields': ('phone', 'email')
        }),
        ('ID Information', {
            'fields': ('id_issue_date', 'id_expiry_date', 'status')
        }),
        ('Employment', {
            'fields': ('employment', 'employee_type', 'organization_name')
        }),
        ('Family', {
            'fields': ('marital_status', 'mother', 'father')
        }),
        ('Status', {
            'fields': ('is_resident', 'current_status', 'is_active')
        }),
    )

@admin.register(Biometric)
class BiometricAdmin(admin.ModelAdmin):
    list_display = ('citizen', 'created_at', 'updated_at')
    search_fields = ('citizen__first_name', 'citizen__last_name', 'citizen__digital_id')

@admin.register(Photo)
class PhotoAdmin(admin.ModelAdmin):
    list_display = ('citizen', 'upload_date')
    search_fields = ('citizen__first_name', 'citizen__last_name', 'citizen__digital_id')

@admin.register(Relationship)
class RelationshipAdmin(admin.ModelAdmin):
    list_display = ('name', 'description')
    search_fields = ('name', 'description')

@admin.register(CurrentStatus)
class CurrentStatusAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)
