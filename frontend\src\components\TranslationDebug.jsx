import React, { useEffect, useState } from 'react';
import { useLocalization } from '../contexts/LocalizationContext';
import localizationService from '../services/localizationService';

const TranslationDebug = () => {
  const { t, currentLanguage, translationVersion, changeLanguage } = useLocalization();
  const [debugInfo, setDebugInfo] = useState({});

  useEffect(() => {
    const updateDebugInfo = () => {
      const info = {
        currentLanguage,
        translationVersion,
        serviceLanguage: localizationService.getCurrentLanguage(),
        availableLanguages: localizationService.getAvailableLanguages(),
        sampleTranslations: {
          dashboard: t('dashboard', 'DEFAULT_DASHBOARD'),
          citizens: t('citizens', 'DEFAULT_CITIZENS'), 
          login: t('login', 'DEFAULT_LOGIN'),
          password: t('password', 'DEFAULT_PASSWORD')
        },
        rawServiceTranslations: {
          dashboard: localizationService.t('dashboard', 'RAW_DEFAULT'),
          citizens: localizationService.t('citizens', 'RAW_DEFAULT')
        }
      };
      setDebugInfo(info);
    };

    updateDebugInfo();
  }, [currentLanguage, translationVersion, t]);

  const handleLanguageChange = async (lang) => {
    console.log(`🔄 Changing language to: ${lang}`);
    await changeLanguage(lang);
    console.log(`✅ Language changed to: ${localizationService.getCurrentLanguage()}`);
  };

  return (
    <div style={{ 
      position: 'fixed', 
      top: 10, 
      right: 10, 
      background: 'white', 
      border: '2px solid #ccc', 
      padding: '15px', 
      zIndex: 9999,
      maxWidth: '400px',
      fontSize: '12px',
      boxShadow: '0 4px 8px rgba(0,0,0,0.2)'
    }}>
      <h3>🔍 Translation Debug Panel</h3>
      
      <div style={{ marginBottom: '10px' }}>
        <strong>Language Controls:</strong><br/>
        <button onClick={() => handleLanguageChange('en')} style={{ margin: '2px', padding: '5px' }}>
          English
        </button>
        <button onClick={() => handleLanguageChange('am')} style={{ margin: '2px', padding: '5px' }}>
          አማርኛ
        </button>
      </div>

      <div style={{ marginBottom: '10px' }}>
        <strong>Current State:</strong><br/>
        Language: {debugInfo.currentLanguage}<br/>
        Version: {debugInfo.translationVersion}<br/>
        Service Lang: {debugInfo.serviceLanguage}<br/>
      </div>

      <div style={{ marginBottom: '10px' }}>
        <strong>Context Translations (t()):</strong><br/>
        {debugInfo.sampleTranslations && Object.entries(debugInfo.sampleTranslations).map(([key, value]) => (
          <div key={key}>{key}: {value}</div>
        ))}
      </div>

      <div style={{ marginBottom: '10px' }}>
        <strong>Service Translations (raw):</strong><br/>
        {debugInfo.rawServiceTranslations && Object.entries(debugInfo.rawServiceTranslations).map(([key, value]) => (
          <div key={key}>{key}: {value}</div>
        ))}
      </div>

      <div>
        <strong>Test Text:</strong><br/>
        <div style={{ fontFamily: debugInfo.currentLanguage === 'am' ? 'Noto Sans Ethiopic, serif' : 'inherit' }}>
          Dashboard: {t('dashboard', 'Dashboard')}<br/>
          Citizens: {t('citizens', 'Citizens')}<br/>
          Login: {t('login', 'Login')}
        </div>
      </div>
    </div>
  );
};

export default TranslationDebug;
