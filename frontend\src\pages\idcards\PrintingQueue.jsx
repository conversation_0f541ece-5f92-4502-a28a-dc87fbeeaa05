import React, { useState, useEffect } from 'react';
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Grid,
  Chip,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Print as PrintIcon,
  Visibility as ViewIcon,
  CheckCircle as CompleteIcon,
  LocalPrintshop as PrintQueueIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import axios from '../../utils/axios';
import { useAuth } from '../../contexts/AuthContext';

const PrintingQueue = () => {
  const [idCards, setIdCards] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [printDialogOpen, setPrintDialogOpen] = useState(false);
  const [selectedCard, setSelectedCard] = useState(null);
  const [printing, setPrinting] = useState(false);
  const navigate = useNavigate();
  const { user, getTenantId } = useAuth();

  useEffect(() => {
    fetchPrintingQueue();
  }, []);

  const fetchPrintingQueue = async () => {
    try {
      setLoading(true);
      setError('');

      // Get tenant ID from multiple sources for reliability
      const tenantId = getReliableTenantId();
      console.log('🖨️ Fetching printing queue for tenant:', tenantId);
      console.log('🔍 Current user:', user);
      console.log('🔍 User role:', user?.role);

      if (!tenantId) {
        throw new Error('No tenant ID available. Please log in again.');
      }

      // Fetch approved ID cards ready for printing from all child kebeles
      const response = await axios.get(`/api/tenants/${tenantId}/idcards/printing_queue/`);

      console.log('✅ Printing queue response:', response.data);
      setIdCards(response.data.results || response.data || []);
    } catch (error) {
      console.error('❌ Error fetching printing queue:', error);
      console.error('❌ Error status:', error.response?.status);
      console.error('❌ Error data:', error.response?.data);

      let errorMessage = 'Failed to fetch printing queue';
      if (error.response?.status === 401) {
        errorMessage = 'Authentication failed. Please log in again.';
      } else if (error.response?.status === 403) {
        errorMessage = 'Access denied. Only subcity admins can access the printing queue.';
      } else if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleViewCard = (card) => {
    // For printing queue (subcity context), use cross-tenant navigation to the kebele where the card is stored
    const subcityTenantId = getReliableTenantId();
    const kebeleTenantId = card.kebele_tenant?.id;

    if (kebeleTenantId) {
      // Navigate to the kebele tenant context where the ID card is actually stored
      navigate(`/tenants/${kebeleTenantId}/idcards/${card.id}`);
    } else if (subcityTenantId) {
      // Fallback to subcity context
      navigate(`/tenants/${subcityTenantId}/idcards/${card.id}`);
    } else {
      navigate(`/idcards/${card.id}`);
    }
  };

  const handlePrintCard = (card) => {
    // Navigate to ID card detail page and auto-open print preview modal
    const kebeleTenantId = card.kebele_tenant?.id;
    if (kebeleTenantId) {
      // Cross-tenant navigation for subcity admin viewing kebele ID card
      navigate(`/tenants/${kebeleTenantId}/idcards/${card.id}?print=true`);
    } else {
      navigate(`/idcards/${card.id}?print=true`);
    }
  };

  const refreshToken = async () => {
    try {
      const refreshToken = localStorage.getItem('refreshToken');
      if (!refreshToken) {
        console.error('No refresh token available');
        return false;
      }

      const response = await axios.post('/api/auth/token/refresh/', {
        refresh: refreshToken
      });

      if (response.data.access) {
        localStorage.setItem('accessToken', response.data.access);
        console.log('✅ Token refreshed successfully');
        return true;
      }
    } catch (error) {
      console.error('❌ Failed to refresh token:', error);
      return false;
    }
  };

  // Helper function to get tenant ID from multiple sources
  const getReliableTenantId = () => {
    // Try AuthContext first
    let tenantId = getTenantId();

    if (!tenantId) {
      // Try JWT token
      try {
        const accessToken = localStorage.getItem('accessToken');
        if (accessToken) {
          const base64Url = accessToken.split('.')[1];
          const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
          const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
          }).join(''));
          const tokenData = JSON.parse(jsonPayload);
          tenantId = tokenData.tenant_id;
        }
      } catch (e) {
        console.warn('Could not decode token:', e);
      }
    }

    if (!tenantId) {
      // Try localStorage user object
      try {
        const storedUser = JSON.parse(localStorage.getItem('user') || '{}');
        tenantId = storedUser.tenant_id;
      } catch (e) {
        console.warn('Could not parse stored user data');
      }
    }

    return tenantId;
  };



  const handleConfirmPrint = async () => {
    try {
      setPrinting(true);
      const tenantId = getReliableTenantId();

      console.log('🖨️ Printing ID card:', selectedCard.id);
      console.log('🔍 Tenant ID:', tenantId);
      console.log('🔍 User:', user);

      if (!tenantId) {
        throw new Error('No tenant ID available. Please log in again.');
      }

      // Check if user is authenticated
      const accessToken = localStorage.getItem('accessToken');
      console.log('🔍 Access token exists:', !!accessToken);

      if (accessToken) {
        try {
          // Decode token to check expiration
          const base64Url = accessToken.split('.')[1];
          const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
          const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
          }).join(''));
          const tokenData = JSON.parse(jsonPayload);
          const currentTime = Math.floor(Date.now() / 1000);
          console.log('🔍 Token expires at:', new Date(tokenData.exp * 1000));
          console.log('🔍 Current time:', new Date(currentTime * 1000));
          console.log('🔍 Token expired:', tokenData.exp < currentTime);
        } catch (e) {
          console.warn('Could not decode token:', e);
        }
      }

      // Call backend to mark as printed and generate PDF
      // Use the kebele tenant ID where the ID card is actually stored
      const printTenantId = selectedCard.kebele_tenant?.id || tenantId;
      console.log('🔍 Using tenant ID for printing:', printTenantId);
      console.log('🔍 Kebele tenant info:', selectedCard.kebele_tenant);

      try {
        await axios.post(`/api/tenants/${printTenantId}/idcards/${selectedCard.id}/print/`);
      } catch (printError) {
        // If authentication failed, try refreshing token and retry
        if (printError.response?.status === 401) {
          console.log('🔄 Authentication failed, trying to refresh token...');
          const refreshed = await refreshToken();
          if (refreshed) {
            console.log('🔄 Retrying print request with new token...');
            await axios.post(`/api/tenants/${printTenantId}/idcards/${selectedCard.id}/print/`);
          } else {
            throw printError;
          }
        } else {
          throw printError;
        }
      }

      // Refresh the queue
      await fetchPrintingQueue();

      setPrintDialogOpen(false);
      setSelectedCard(null);
    } catch (error) {
      console.error('❌ Error printing ID card:', error);
      console.error('❌ Error response:', error.response);

      // Show more detailed error message
      let errorMessage = 'Failed to print ID card';
      if (error.response?.status === 401) {
        errorMessage = 'Authentication failed. Please log in again.';
      } else if (error.response?.status === 403) {
        errorMessage = 'You do not have permission to print ID cards. Only subcity admins can print.';
      } else if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      } else if (error.message) {
        errorMessage = error.message;
      }

      setError(errorMessage);
    } finally {
      setPrinting(false);
    }
  };

  const getStatusChip = (status) => {
    switch (status) {
      case 'approved':
        return <Chip label="Ready for Printing" color="success" size="small" />;
      case 'printed':
        return <Chip label="Printed" color="info" size="small" />;
      default:
        return <Chip label={status} size="small" />;
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <PrintQueueIcon sx={{ mr: 2, fontSize: 32, color: 'primary.main' }} />
          <Typography variant="h4" component="h1">
            ID Card Printing Queue
          </Typography>
        </Box>

      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {idCards.length === 0 ? (
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 5 }}>
            <PrintQueueIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No ID Cards Ready for Printing
            </Typography>
            <Typography variant="body2" color="text.secondary">
              ID cards will appear here after they receive both kebele and subcity approval.
            </Typography>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              {idCards.length} ID Card{idCards.length !== 1 ? 's' : ''} Ready for Printing
            </Typography>

            <TableContainer component={Paper} sx={{ mt: 2 }}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Citizen Name</TableCell>
                    <TableCell>Digital ID</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Approved Date</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {idCards.map((card) => (
                    <TableRow key={card.id}>
                      <TableCell>
                        <Typography variant="body2" fontWeight="bold">
                          {card.citizen_name ||
                           [card.citizen_first_name, card.citizen_middle_name, card.citizen_last_name]
                             .filter(Boolean)
                             .join(' ') ||
                           'Unknown Citizen'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace">
                          {card.citizen_digital_id || 'N/A'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        {getStatusChip(card.status)}
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {card.subcity_approved_at
                            ? new Date(card.subcity_approved_at).toLocaleDateString()
                            : card.approved_at
                              ? new Date(card.approved_at).toLocaleDateString()
                              : 'N/A'
                          }
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Tooltip title="View Details">
                          <IconButton
                            size="small"
                            onClick={() => handleViewCard(card)}
                            sx={{ mr: 1 }}
                          >
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Print Preview">
                          <IconButton
                            size="small"
                            color="primary"
                            onClick={() => handlePrintCard(card)}
                          >
                            <PrintIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      )}

      {/* Print Confirmation Dialog */}
      <Dialog open={printDialogOpen} onClose={() => setPrintDialogOpen(false)}>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <PrintIcon sx={{ mr: 1 }} />
            Print ID Card
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" gutterBottom>
            Are you sure you want to print the ID card for:
          </Typography>
          <Typography variant="h6" color="primary" gutterBottom>
            {selectedCard?.citizen_name ||
             [selectedCard?.citizen_first_name, selectedCard?.citizen_middle_name, selectedCard?.citizen_last_name]
               .filter(Boolean)
               .join(' ') ||
             'Unknown Citizen'}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            This will mark the ID card as printed and generate the final PDF.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPrintDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirmPrint}
            variant="contained"
            color="primary"
            disabled={printing}
            startIcon={printing ? <CircularProgress size={20} /> : <PrintIcon />}
          >
            {printing ? 'Printing...' : 'Print'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default PrintingQueue;
