# Generated by Django 4.2.7 on 2025-06-07 15:33

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tenants', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='tenant',
            name='primary_color',
            field=models.CharField(default='#ff8f00', help_text='Primary color in hex format (e.g., #ff8f00)', max_length=7),
        ),
        migrations.AddField(
            model_name='tenant',
            name='public_id_application_enabled',
            field=models.BooleanField(default=False, help_text='Allow public access to ID card application services'),
        ),
        migrations.AddField(
            model_name='tenant',
            name='public_service_portal_enabled',
            field=models.BooleanField(default=False, help_text='Enable public service portal for citizens'),
        ),
        migrations.AddField(
            model_name='tenant',
            name='secondary_color',
            field=models.CharField(default='#ef6c00', help_text='Secondary color in hex format (e.g., #ef6c00)', max_length=7),
        ),
    ]
