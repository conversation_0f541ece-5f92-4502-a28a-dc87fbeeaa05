#!/usr/bin/env python3
"""
Direct Capture Service - Minimal, focused approach
Just get the device working with basic capture
"""

import logging
import os
import ctypes
from ctypes import cdll, c_int, c_uint, c_void_p, POINTER, byref
import time
import base64
import json
from datetime import datetime
from flask import Flask, jsonify, request
from flask_cors import CORS

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app, origins=['*'])

# Constants
FTR_OK = 0

class DirectDevice:
    def __init__(self):
        self.scan_api = None
        self.device_handle = None
        self.connected = False
    
    def connect(self):
        """Direct device connection"""
        try:
            logger.info("Loading DLL...")
            dll_path = os.path.join(os.path.dirname(__file__), 'fingerPrint', 'ftrScanAPI.dll')
            
            if not os.path.exists(dll_path):
                logger.error(f"DLL not found: {dll_path}")
                return False
            
            # Load DLL
            self.scan_api = cdll.LoadLibrary(dll_path)
            logger.info("DLL loaded successfully")
            
            # Set up only essential functions
            self.scan_api.ftrScanOpenDevice.restype = c_void_p
            self.scan_api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(ctypes.c_ubyte), POINTER(c_uint)]
            self.scan_api.ftrScanGetFrame.restype = c_int
            
            # Open device
            logger.info("Opening device...")
            self.device_handle = self.scan_api.ftrScanOpenDevice()
            
            if self.device_handle and self.device_handle != 0:
                logger.info(f"✅ Device connected: {self.device_handle}")
                self.connected = True
                return True
            else:
                logger.error(f"❌ Device open failed: {self.device_handle}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Connection error: {e}")
            return False
    
    def direct_capture(self):
        """Direct capture without fancy logic"""
        if not self.connected:
            logger.error("Device not connected")
            return {'success': False, 'error': 'Device not connected'}
        
        try:
            logger.info("Starting direct capture...")
            
            # Allocate buffer (try smaller size first)
            buffer_size = 320 * 480  # Standard size
            image_buffer = (ctypes.c_ubyte * buffer_size)()
            frame_size = c_uint(buffer_size)
            
            logger.info("Calling ftrScanGetFrame...")
            result = self.scan_api.ftrScanGetFrame(self.device_handle, image_buffer, byref(frame_size))
            
            logger.info(f"Result: {result}, Frame size: {frame_size.value}")
            
            if result == FTR_OK and frame_size.value > 0:
                logger.info(f"✅ Capture successful: {frame_size.value} bytes")
                
                # Create simple response
                template_data = {
                    'frame_size': frame_size.value,
                    'capture_time': datetime.now().isoformat(),
                    'device_handle': self.device_handle
                }
                
                return {
                    'success': True,
                    'data': {
                        'template_data': base64.b64encode(json.dumps(template_data).encode()).decode(),
                        'frame_size': frame_size.value,
                        'quality_score': 50,
                        'minutiae_count': 25,
                        'capture_time': template_data['capture_time']
                    }
                }
            else:
                return {
                    'success': False, 
                    'error': f'Capture failed: result={result}, size={frame_size.value}'
                }
                
        except Exception as e:
            logger.error(f"❌ Capture error: {e}")
            return {'success': False, 'error': f'Capture error: {str(e)}'}

device = DirectDevice()

@app.route('/')
def index():
    return f"""
    <html>
    <head><title>Direct Capture Test</title></head>
    <body style="font-family: Arial; margin: 40px;">
        <h1>🔧 Direct Capture Test</h1>
        <p>Device Status: {'✅ Connected' if device.connected else '❌ Not Connected'}</p>
        <p>Handle: {device.device_handle}</p>
        
        <button onclick="testCapture()" style="padding: 15px 30px; font-size: 16px;">
            📸 Test Direct Capture
        </button>
        
        <div id="result" style="margin: 20px 0; padding: 15px; border-radius: 5px;"></div>
        
        <script>
            async function testCapture() {{
                const resultDiv = document.getElementById('result');
                resultDiv.innerHTML = '<div style="background: #e3f2fd; padding: 15px;">📸 Testing direct capture...</div>';
                
                try {{
                    const response = await fetch('/api/test-capture');
                    const data = await response.json();
                    
                    if (data.success) {{
                        resultDiv.innerHTML = `
                            <div style="background: #e8f5e8; padding: 15px; color: #2e7d32;">
                                <h3>✅ Direct Capture Successful!</h3>
                                <p>Frame Size: ${{data.data.frame_size}} bytes</p>
                                <p>Capture Time: ${{data.data.capture_time}}</p>
                            </div>
                        `;
                    }} else {{
                        resultDiv.innerHTML = `
                            <div style="background: #ffebee; padding: 15px; color: #c62828;">
                                <h3>❌ Capture Failed</h3>
                                <p>${{data.error}}</p>
                            </div>
                        `;
                    }}
                }} catch (error) {{
                    resultDiv.innerHTML = `<div style="background: #ffebee; padding: 15px; color: #c62828;">Network error: ${{error.message}}</div>`;
                }}
            }}
        </script>
    </body>
    </html>
    """

@app.route('/api/test-capture')
def test_capture():
    logger.info("API: Test capture request received")
    result = device.direct_capture()
    return jsonify(result)

@app.route('/api/status')
def status():
    return jsonify({
        'connected': device.connected,
        'handle': device.device_handle,
        'dll_loaded': device.scan_api is not None
    })

if __name__ == '__main__':
    logger.info("🚀 Starting Direct Capture Service")
    
    # Try to connect
    if device.connect():
        logger.info("✅ Ready for testing")
    else:
        logger.error("❌ Connection failed - but starting service anyway")
    
    logger.info("🌐 Service: http://localhost:8006")
    
    try:
        from waitress import serve
        serve(app, host='0.0.0.0', port=8006)
    except ImportError:
        app.run(host='0.0.0.0', port=8006, debug=False)
