#!/usr/bin/env python3
"""
Script to create basic .po template files for new languages
"""

import os
from pathlib import Path

# Language configurations
LANGUAGES = {
    'om': {
        'name': '<PERSON><PERSON><PERSON> (Oromo)',
        'team': '<PERSON><PERSON> <<EMAIL>>',
        'basic_translations': {
            'Dashboard': '<PERSON><PERSON><PERSON>',
            'Citizens': '<PERSON><PERSON>i',
            'ID Cards': 'Ka<PERSON>ii <PERSON>yu<PERSON>',
            'Login': 'Seeni',
            'Logout': 'Ba\'i',
            'Username': 'Maqaa Fayyadamaa',
            'Password': 'Jecha Icciitii',
            'Save': 'Olkaa\'i',
            'Cancel': 'Dhiisi',
            'Delete': 'Haqi',
            'Edit': '<PERSON><PERSON><PERSON>',
            'Add': 'Dabali',
            'Search': 'Barbaadi',
            'Loading': 'Fe\'aa jira...',
            'Success': 'Milkaa\'e',
            'Error': '<PERSON>og<PERSON>a',
            'Yes': '<PERSON><PERSON><PERSON><PERSON>',
            'No': '<PERSON><PERSON><PERSON>',
        }
    },
    'ti': {
        'name': 'ትግርኛ (<PERSON><PERSON><PERSON><PERSON>)',
        'team': 'T<PERSON>rinya <<EMAIL>>',
        'basic_translations': {
            'Dashboard': 'ዳሽቦርድ',
            'Citizens': 'ዜጋታት',
            'ID Cards': 'መንነት ካርድ',
            'Login': 'እቶት',
            'Logout': 'ወጻኢ',
            'Username': 'ሽም ተጠቃሚ',
            'Password': 'መሕለፊ ቃል',
            'Save': 'ዓቅብ',
            'Cancel': 'ሰርዝ',
            'Delete': 'ደምስስ',
            'Edit': 'ኣርም',
            'Add': 'ወስኽ',
            'Search': 'ድለ',
            'Loading': 'ይጽዕን ኣሎ...',
            'Success': 'ዓወት',
            'Error': 'ጌጋ',
            'Yes': 'እወ',
            'No': 'ኣይፋልን',
        }
    },
    'so': {
        'name': 'Soomaali (Somali)',
        'team': 'Somali <<EMAIL>>',
        'basic_translations': {
            'Dashboard': 'Shabakadda',
            'Citizens': 'Muwaadiniinta',
            'ID Cards': 'Kaararka Aqoonsiga',
            'Login': 'Gal',
            'Logout': 'Ka bax',
            'Username': 'Magaca isticmaalaha',
            'Password': 'Furaha sirta ah',
            'Save': 'Kaydi',
            'Cancel': 'Jooji',
            'Delete': 'Tirtir',
            'Edit': 'Wax ka beddel',
            'Add': 'Ku dar',
            'Search': 'Raadi',
            'Loading': 'Waa la rarayo...',
            'Success': 'Guul',
            'Error': 'Qalad',
            'Yes': 'Haa',
            'No': 'Maya',
        }
    },
    'aa': {
        'name': 'Qafar (Afar)',
        'team': 'Afar <<EMAIL>>',
        'basic_translations': {
            'Dashboard': 'Gabatee',
            'Citizens': 'Qafar',
            'ID Cards': 'Eenyummaa Kaardii',
            'Login': 'Gal',
            'Logout': 'Bah',
            'Username': 'Maqaa',
            'Password': 'Sirrii',
            'Save': 'Kaydi',
            'Cancel': 'Jooji',
            'Delete': 'Haqi',
            'Edit': 'Beddeli',
            'Add': 'Dabali',
            'Search': 'Barbaadi',
            'Loading': 'Raarayaa...',
            'Success': 'Milkaa',
            'Error': 'Qalad',
            'Yes': 'Wayyo',
            'No': 'Maleey',
        }
    }
}

def create_po_file(language_code, language_info):
    """Create a .po file for a specific language"""
    
    po_content = f'''# {language_info['name']} translation for GoID
# Copyright (C) 2025 GoID Project
# This file is distributed under the same license as the GoID package.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: GoID 1.0\\n"
"Report-Msgid-Bugs-To: \\n"
"POT-Creation-Date: 2025-06-26 16:15+0000\\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\\n"
"Language-Team: {language_info['team']}\\n"
"Language: {language_code}\\n"
"MIME-Version: 1.0\\n"
"Content-Type: text/plain; charset=UTF-8\\n"
"Content-Transfer-Encoding: 8bit\\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\\n"

# Basic UI Elements
'''
    
    # Add basic translations
    for english, translation in language_info['basic_translations'].items():
        po_content += f'''msgid "{english}"
msgstr "{translation}"

'''
    
    # Add placeholder entries for common terms that need translation
    common_terms = [
        'First Name', 'Last Name', 'Email', 'Phone Number', 'Address',
        'Date of Birth', 'Gender', 'Male', 'Female', 'Nationality',
        'Ethiopian', 'Status', 'Active', 'Inactive', 'Pending',
        'Approved', 'Rejected', 'Submit', 'Reset', 'Clear',
        'Print', 'Export', 'Import', 'Settings', 'Help',
        'About', 'Contact', 'Home', 'Back', 'Next', 'Previous',
        'Close', 'Open', 'View', 'Details', 'Summary', 'Total',
        'Date', 'Time', 'Name', 'Description', 'Type', 'Category'
    ]
    
    po_content += '# Common terms (need translation)\n'
    for term in common_terms:
        po_content += f'''msgid "{term}"
msgstr ""

'''
    
    return po_content

def main():
    """Create .po files for all configured languages"""
    base_path = Path('/app/locale')
    
    for lang_code, lang_info in LANGUAGES.items():
        # Create directory structure
        lang_dir = base_path / lang_code / 'LC_MESSAGES'
        lang_dir.mkdir(parents=True, exist_ok=True)
        
        # Create .po file
        po_file = lang_dir / 'django.po'
        po_content = create_po_file(lang_code, lang_info)
        
        with open(po_file, 'w', encoding='utf-8') as f:
            f.write(po_content)
        
        print(f"✅ Created {lang_code} translation file: {po_file}")
        print(f"   Language: {lang_info['name']}")
        print(f"   Basic translations: {len(lang_info['basic_translations'])}")
        print()

if __name__ == '__main__':
    print("🌍 GoID Multi-Language Template Generator")
    print("=" * 50)
    main()
    print("🎉 Language templates created successfully!")
    print("\n📝 Note: These are basic templates. Professional translators should")
    print("   review and complete the translations for production use.")
