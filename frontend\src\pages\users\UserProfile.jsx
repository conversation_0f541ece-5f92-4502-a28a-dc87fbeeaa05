import { useState, useEffect } from 'react';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Typography,
  TextField,
  Grid,
  Avatar,
  Divider,
  Alert,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { useAuth } from '../../contexts/AuthContext';
import axios from '../../utils/axios';

const profileValidationSchema = yup.object({
  first_name: yup.string().required('First name is required'),
  last_name: yup.string().required('Last name is required'),
  email: yup.string().email('Enter a valid email').required('Email is required'),
  phone_number: yup.string().nullable(),
});

const passwordValidationSchema = yup.object({
  current_password: yup.string().required('Current password is required'),
  new_password: yup.string()
    .min(8, 'Password should be of minimum 8 characters length')
    .required('New password is required'),
  confirm_password: yup.string()
    .oneOf([yup.ref('new_password'), null], 'Passwords must match')
    .required('Confirm password is required'),
});

const UserProfile = () => {
  const { user, updateProfile } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [passwordDialogOpen, setPasswordDialogOpen] = useState(false);

  const profileFormik = useFormik({
    initialValues: {
      first_name: user?.first_name || '',
      last_name: user?.last_name || '',
      email: user?.email || '',
      phone_number: user?.phone_number || '',
    },
    validationSchema: profileValidationSchema,
    onSubmit: async (values) => {
      try {
        setLoading(true);
        setError('');
        
        // In a real implementation, this would call an API endpoint
        // For now, we'll just simulate a successful request
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Update the user profile
        updateProfile(values);
        
        setSuccess(true);
        setTimeout(() => {
          setSuccess(false);
        }, 3000);
      } catch (err) {
        setError(err.response?.data?.detail || 'Failed to update profile. Please try again.');
      } finally {
        setLoading(false);
      }
    },
    enableReinitialize: true,
  });

  const passwordFormik = useFormik({
    initialValues: {
      current_password: '',
      new_password: '',
      confirm_password: '',
    },
    validationSchema: passwordValidationSchema,
    onSubmit: async (values) => {
      try {
        setLoading(true);
        setError('');
        
        // In a real implementation, this would call an API endpoint
        // For now, we'll just simulate a successful request
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Close the dialog and reset the form
        setPasswordDialogOpen(false);
        passwordFormik.resetForm();
        
        setSuccess(true);
        setTimeout(() => {
          setSuccess(false);
        }, 3000);
      } catch (err) {
        setError(err.response?.data?.detail || 'Failed to change password. Please try again.');
      } finally {
        setLoading(false);
      }
    },
  });

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        My Profile
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          Profile updated successfully!
        </Alert>
      )}

      <Grid container spacing={3}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar
                sx={{
                  width: 100,
                  height: 100,
                  mx: 'auto',
                  mb: 2,
                  bgcolor: 'primary.main',
                  fontSize: '2rem',
                }}
              >
                {user?.first_name ? user.first_name.charAt(0) : user?.username?.charAt(0)}
              </Avatar>
              <Typography variant="h5" gutterBottom>
                {user?.first_name && user?.last_name
                  ? `${user.first_name} ${user.last_name}`
                  : user?.username}
              </Typography>
              <Typography variant="body1" color="text.secondary" gutterBottom>
                {user?.email}
              </Typography>
              <Divider sx={{ my: 2 }} />
              <Typography variant="body2" gutterBottom>
                <strong>Role:</strong> {user?.role}
              </Typography>
              {user?.tenant_name && (
                <Typography variant="body2" gutterBottom>
                  <strong>Tenant:</strong> {user.tenant_name}
                </Typography>
              )}
              <Typography variant="body2" gutterBottom>
                <strong>Username:</strong> {user?.username}
              </Typography>
              <Button
                variant="outlined"
                color="secondary"
                onClick={() => setPasswordDialogOpen(true)}
                sx={{ mt: 2 }}
              >
                Change Password
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Edit Profile
              </Typography>
              <form onSubmit={profileFormik.handleSubmit}>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      id="first_name"
                      name="first_name"
                      label="First Name"
                      value={profileFormik.values.first_name}
                      onChange={profileFormik.handleChange}
                      error={profileFormik.touched.first_name && Boolean(profileFormik.errors.first_name)}
                      helperText={profileFormik.touched.first_name && profileFormik.errors.first_name}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      id="last_name"
                      name="last_name"
                      label="Last Name"
                      value={profileFormik.values.last_name}
                      onChange={profileFormik.handleChange}
                      error={profileFormik.touched.last_name && Boolean(profileFormik.errors.last_name)}
                      helperText={profileFormik.touched.last_name && profileFormik.errors.last_name}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      id="email"
                      name="email"
                      label="Email Address"
                      value={profileFormik.values.email}
                      onChange={profileFormik.handleChange}
                      error={profileFormik.touched.email && Boolean(profileFormik.errors.email)}
                      helperText={profileFormik.touched.email && profileFormik.errors.email}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      id="phone_number"
                      name="phone_number"
                      label="Phone Number"
                      value={profileFormik.values.phone_number}
                      onChange={profileFormik.handleChange}
                      error={profileFormik.touched.phone_number && Boolean(profileFormik.errors.phone_number)}
                      helperText={profileFormik.touched.phone_number && profileFormik.errors.phone_number}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                      <Button
                        type="submit"
                        variant="contained"
                        disabled={loading}
                      >
                        {loading ? <CircularProgress size={24} /> : 'Update Profile'}
                      </Button>
                    </Box>
                  </Grid>
                </Grid>
              </form>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Change Password Dialog */}
      <Dialog open={passwordDialogOpen} onClose={() => setPasswordDialogOpen(false)}>
        <form onSubmit={passwordFormik.handleSubmit}>
          <DialogTitle>Change Password</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Enter your current password and a new password to change your password.
            </DialogContentText>
            <TextField
              autoFocus
              margin="dense"
              id="current_password"
              name="current_password"
              label="Current Password"
              type="password"
              fullWidth
              value={passwordFormik.values.current_password}
              onChange={passwordFormik.handleChange}
              error={passwordFormik.touched.current_password && Boolean(passwordFormik.errors.current_password)}
              helperText={passwordFormik.touched.current_password && passwordFormik.errors.current_password}
            />
            <TextField
              margin="dense"
              id="new_password"
              name="new_password"
              label="New Password"
              type="password"
              fullWidth
              value={passwordFormik.values.new_password}
              onChange={passwordFormik.handleChange}
              error={passwordFormik.touched.new_password && Boolean(passwordFormik.errors.new_password)}
              helperText={passwordFormik.touched.new_password && passwordFormik.errors.new_password}
            />
            <TextField
              margin="dense"
              id="confirm_password"
              name="confirm_password"
              label="Confirm New Password"
              type="password"
              fullWidth
              value={passwordFormik.values.confirm_password}
              onChange={passwordFormik.handleChange}
              error={passwordFormik.touched.confirm_password && Boolean(passwordFormik.errors.confirm_password)}
              helperText={passwordFormik.touched.confirm_password && passwordFormik.errors.confirm_password}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setPasswordDialogOpen(false)}>Cancel</Button>
            <Button type="submit" variant="contained" disabled={loading}>
              {loading ? <CircularProgress size={24} /> : 'Change Password'}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </Box>
  );
};

export default UserProfile;
