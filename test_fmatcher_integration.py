#!/usr/bin/env python3
"""
Test FMatcher integration without Django dependency
"""

import requests
import json
import base64

def test_fmatcher_service():
    """Test the FMatcher service directly"""
    print("🧪 Testing FMatcher service...")
    
    # Test template data (base64 encoded)
    test_template1 = base64.b64encode(b"test_template_1").decode()
    test_template2 = base64.b64encode(b"test_template_2").decode()
    
    try:
        response = requests.post(
            "http://localhost:8002/api/duplicate/check",
            json={
                'template': test_template1,
                'stored_templates': [test_template2],
                'threshold': 40
            },
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ FMatcher service response:", result)
            return True
        else:
            print(f"❌ FMatcher service error: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"❌ Failed to connect to FMatcher service: {e}")
        return False

def test_django_endpoint():
    """Test Django duplicate check endpoint"""
    print("\n🧪 Testing Django duplicate check endpoint...")
    
    try:
        response = requests.post(
            "http://localhost:8000/api/biometrics/check-duplicates/",
            json={
                'left_thumb_fingerprint': '{"ansi_iso_template":"dGVzdDE="}',
                'right_thumb_fingerprint': '{"ansi_iso_template":"dGVzdDI="}'
            },
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"Django response status: {response.status_code}")
        print(f"Django response: {response.text}")
        
        if response.status_code == 401:
            print("⚠️ Authentication required - this is expected")
            return True
        elif response.status_code == 200:
            print("✅ Django endpoint responded successfully")
            return True
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Failed to connect to Django: {e}")
        return False

def test_with_real_template():
    """Test with a more realistic template structure"""
    print("\n🧪 Testing with realistic template structure...")
    
    # Create template data that matches what your system generates
    template_data = {
        'thumb_type': 'left',
        'capture_method': 'java_bridge',
        'capture_time': '2025-06-26T01:50:06.677825',
        'ansi_iso_template': 'VGhpcyBpcyBhIHRlc3QgQU5TSS9JU08gdGVtcGxhdGU=',  # Base64: "This is a test ANSI/ISO template"
        'fingerprint_image': 'VGhpcyBpcyBhIHRlc3QgaW1hZ2U=',  # Base64: "This is a test image"
        'device_info': {
            'model': 'Futronic FS88H',
            'interface': 'java_bridge',
            'real_device': True,
            'ansi_sdk': True
        }
    }
    
    template_json = json.dumps(template_data)
    
    try:
        response = requests.post(
            "http://localhost:8002/api/duplicate/check",
            json={
                'template': template_data['ansi_iso_template'],
                'stored_templates': [template_data['ansi_iso_template']],  # Same template should match
                'threshold': 40
            },
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Realistic template test response:", result)
            
            # This should detect a duplicate since we're comparing the same template
            if result['success'] and result['data']['has_duplicates']:
                print("✅ Duplicate detection working - identical templates detected as duplicates")
            else:
                print("⚠️ Duplicate detection might not be working - identical templates not detected")
                
            return True
        else:
            print(f"❌ Realistic template test failed: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"❌ Realistic template test error: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Testing FMatcher Integration")
    print("=" * 50)
    
    # Test 1: FMatcher service
    fmatcher_ok = test_fmatcher_service()
    
    # Test 2: Django endpoint
    django_ok = test_django_endpoint()
    
    # Test 3: Realistic template
    realistic_ok = test_with_real_template()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"FMatcher Service: {'✅ OK' if fmatcher_ok else '❌ FAILED'}")
    print(f"Django Endpoint: {'✅ OK' if django_ok else '❌ FAILED'}")
    print(f"Realistic Template: {'✅ OK' if realistic_ok else '❌ FAILED'}")
    
    if fmatcher_ok and django_ok and realistic_ok:
        print("\n🎉 All tests passed! Integration should be working.")
        print("\n🔍 If duplicates still aren't detected, the issue might be:")
        print("   1. No existing fingerprints in database to compare against")
        print("   2. Authentication issues in the frontend")
        print("   3. Template format mismatch")
    else:
        print("\n❌ Some tests failed. Check the errors above.")
