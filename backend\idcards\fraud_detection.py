"""
Fraud Detection Service for ID Card Applications

This module provides comprehensive fraud detection mechanisms to prevent
citizens from obtaining ID cards from multiple kebele tenants.
"""

import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from django.db import connection
from django.utils import timezone
from django_tenants.utils import schema_context
from tenants.models import Tenant
from citizens.models import Citizen, Biometric
from idcards.models import IDCard, IDCardServiceRequest
import difflib
import re

logger = logging.getLogger(__name__)


class FraudDetectionService:
    """
    Comprehensive fraud detection service for ID card applications.
    
    Detection Methods:
    1. Digital ID cross-tenant validation
    2. Personal information matching (name, DOB, phone, email)
    3. Biometric matching (fingerprints, iris scans)
    4. Behavioral pattern analysis
    5. Document verification
    """
    
    def __init__(self):
        self.similarity_threshold = 0.85  # 85% similarity threshold for names
        self.phone_pattern = re.compile(r'^\+?[\d\s\-\(\)]+$')
        
    def check_fraud_indicators(self, citizen_data: Dict, current_tenant_id: int) -> Dict:
        """
        Main fraud detection method that checks all indicators.
        
        Args:
            citizen_data: Dictionary containing citizen information
            current_tenant_id: ID of the tenant where application is being made
            
        Returns:
            Dictionary with fraud detection results
        """
        logger.info(f"🔍 Starting fraud detection for citizen: {citizen_data.get('first_name')} {citizen_data.get('last_name')}")
        
        fraud_indicators = {
            'is_fraud_detected': False,
            'risk_level': 'low',  # low, medium, high, critical
            'indicators': [],
            'matches': [],
            'recommendations': []
        }
        
        try:
            # 1. Check digital ID duplicates
            digital_id_results = self._check_digital_id_duplicates(citizen_data.get('digital_id'))
            if digital_id_results['found']:
                fraud_indicators['indicators'].append('digital_id_duplicate')
                fraud_indicators['matches'].extend(digital_id_results['matches'])
                fraud_indicators['risk_level'] = 'critical'
                fraud_indicators['is_fraud_detected'] = True
            
            # 2. Check personal information matches
            personal_info_results = self._check_personal_information_matches(citizen_data, current_tenant_id)
            if personal_info_results['found']:
                fraud_indicators['indicators'].append('personal_info_match')
                fraud_indicators['matches'].extend(personal_info_results['matches'])
                if fraud_indicators['risk_level'] != 'critical':
                    fraud_indicators['risk_level'] = 'high'
                fraud_indicators['is_fraud_detected'] = True
            
            # 3. Check biometric matches (if biometric data provided)
            if citizen_data.get('biometric_data'):
                biometric_results = self._check_biometric_matches(citizen_data['biometric_data'], current_tenant_id)
                if biometric_results['found']:
                    fraud_indicators['indicators'].append('biometric_match')
                    fraud_indicators['matches'].extend(biometric_results['matches'])
                    fraud_indicators['risk_level'] = 'critical'
                    fraud_indicators['is_fraud_detected'] = True
            
            # 4. Check recent application patterns
            pattern_results = self._check_application_patterns(citizen_data, current_tenant_id)
            if pattern_results['suspicious']:
                fraud_indicators['indicators'].append('suspicious_pattern')
                fraud_indicators['matches'].extend(pattern_results['matches'])
                if fraud_indicators['risk_level'] not in ['critical', 'high']:
                    fraud_indicators['risk_level'] = 'medium'
            
            # 5. Generate recommendations
            fraud_indicators['recommendations'] = self._generate_recommendations(fraud_indicators)
            
            logger.info(f"✅ Fraud detection completed. Risk level: {fraud_indicators['risk_level']}")
            return fraud_indicators
            
        except Exception as e:
            logger.error(f"❌ Error in fraud detection: {str(e)}")
            return {
                'is_fraud_detected': False,
                'risk_level': 'unknown',
                'indicators': ['detection_error'],
                'matches': [],
                'recommendations': ['Manual verification required due to system error'],
                'error': str(e)
            }
    
    def _check_digital_id_duplicates(self, digital_id: str) -> Dict:
        """Check if digital ID already exists in any tenant."""
        if not digital_id:
            return {'found': False, 'matches': []}
        
        logger.info(f"🔍 Checking digital ID duplicates for: {digital_id}")
        matches = []
        
        try:
            # Get all tenants
            tenants = Tenant.objects.filter(type='kebele')
            
            for tenant in tenants:
                try:
                    with schema_context(tenant.schema_name):
                        existing_citizen = Citizen.objects.filter(digital_id=digital_id).first()
                        if existing_citizen:
                            matches.append({
                                'type': 'digital_id_duplicate',
                                'tenant_id': tenant.id,
                                'tenant_name': tenant.name,
                                'citizen_id': existing_citizen.id,
                                'citizen_name': existing_citizen.get_full_name(),
                                'match_confidence': 1.0,
                                'details': f"Exact digital ID match in {tenant.name}"
                            })
                            logger.warning(f"🚨 Digital ID duplicate found in {tenant.name}")
                except Exception as e:
                    logger.error(f"Error checking tenant {tenant.name}: {str(e)}")
                    continue
            
            return {'found': len(matches) > 0, 'matches': matches}
            
        except Exception as e:
            logger.error(f"Error in digital ID duplicate check: {str(e)}")
            return {'found': False, 'matches': []}
    
    def _check_personal_information_matches(self, citizen_data: Dict, current_tenant_id: int) -> Dict:
        """Check for similar personal information across tenants."""
        logger.info(f"🔍 Checking personal information matches")
        matches = []
        
        try:
            # Get all kebele tenants except current one
            tenants = Tenant.objects.filter(type='kebele').exclude(id=current_tenant_id)
            
            for tenant in tenants:
                try:
                    with schema_context(tenant.schema_name):
                        # Search for similar citizens
                        potential_matches = self._find_similar_citizens(citizen_data, tenant)
                        matches.extend(potential_matches)
                except Exception as e:
                    logger.error(f"Error checking tenant {tenant.name}: {str(e)}")
                    continue
            
            return {'found': len(matches) > 0, 'matches': matches}
            
        except Exception as e:
            logger.error(f"Error in personal information check: {str(e)}")
            return {'found': False, 'matches': []}
    
    def _find_similar_citizens(self, citizen_data: Dict, tenant: Tenant) -> List[Dict]:
        """Find citizens with similar personal information in a specific tenant."""
        matches = []

        try:
            # Search criteria - focus on more reliable identifiers
            first_name = citizen_data.get('first_name', '').strip().lower()
            middle_name = citizen_data.get('middle_name', '').strip().lower()
            last_name = citizen_data.get('last_name', '').strip().lower()
            date_of_birth = citizen_data.get('date_of_birth')

            # Parent information (more reliable fraud indicators)
            mother_name = citizen_data.get('mother_full_name', '').strip().lower()
            father_name = citizen_data.get('father_full_name', '').strip().lower()

            # Photo comparison (if available)
            photo_data = citizen_data.get('photo')

            # Get potential matches from this tenant
            citizens = Citizen.objects.filter(is_active=True).prefetch_related('parents')

            for citizen in citizens:
                confidence_score = 0.0
                match_details = []

                # Full name similarity check (higher weight)
                citizen_first = citizen.first_name.strip().lower()
                citizen_middle = (citizen.middle_name or '').strip().lower()
                citizen_last = citizen.last_name.strip().lower()

                first_name_similarity = difflib.SequenceMatcher(None, first_name, citizen_first).ratio()
                middle_name_similarity = difflib.SequenceMatcher(None, middle_name, citizen_middle).ratio() if middle_name and citizen_middle else 0
                last_name_similarity = difflib.SequenceMatcher(None, last_name, citizen_last).ratio()

                # Calculate overall name similarity
                name_weights = [0.3, 0.2, 0.3]  # first, middle, last
                name_similarities = [first_name_similarity, middle_name_similarity, last_name_similarity]
                overall_name_similarity = sum(w * s for w, s in zip(name_weights, name_similarities))

                # Debug logging for name comparison
                logger.info(f"🔍 Comparing names in {tenant.name}:")
                logger.info(f"   New: '{first_name} {middle_name} {last_name}' vs Existing: '{citizen_first} {citizen_middle} {citizen_last}'")
                logger.info(f"   Similarities: First={first_name_similarity:.2f}, Middle={middle_name_similarity:.2f}, Last={last_name_similarity:.2f}")
                logger.info(f"   Overall name similarity: {overall_name_similarity:.2f}")

                if overall_name_similarity >= 0.7:  # 70% name similarity threshold (more sensitive)
                    confidence_score += 0.4
                    match_details.append(f"Name similarity: {overall_name_similarity:.2f}")
                elif overall_name_similarity >= 0.5:  # Partial name match
                    confidence_score += 0.2
                    match_details.append(f"Partial name similarity: {overall_name_similarity:.2f}")

                # Date of birth exact match (critical indicator)
                logger.info(f"   DOB comparison: New='{date_of_birth}' vs Existing='{citizen.date_of_birth}'")
                if date_of_birth and citizen.date_of_birth:
                    # Handle both string and date object comparisons
                    new_dob = str(date_of_birth) if hasattr(date_of_birth, 'strftime') else date_of_birth
                    existing_dob = str(citizen.date_of_birth) if hasattr(citizen.date_of_birth, 'strftime') else citizen.date_of_birth

                    if new_dob == existing_dob:
                        confidence_score += 0.3
                        match_details.append("Exact date of birth match")
                        logger.info(f"   ✅ DOB MATCH FOUND!")

                # Parent name matching (very strong fraud indicator)
                citizen_parents = citizen.parents.all()
                mother_match = False
                father_match = False

                for parent in citizen_parents:
                    parent_full_name = f"{parent.first_name} {parent.middle_name or ''} {parent.last_name}".strip().lower()

                    # Check mother match
                    if mother_name and not mother_match:
                        mother_similarity = difflib.SequenceMatcher(None, mother_name, parent_full_name).ratio()
                        if mother_similarity >= 0.85:  # 85% similarity for parent names
                            mother_match = True
                            confidence_score += 0.2
                            match_details.append(f"Mother name match: {mother_similarity:.2f}")

                    # Check father match
                    if father_name and not father_match:
                        father_similarity = difflib.SequenceMatcher(None, father_name, parent_full_name).ratio()
                        if father_similarity >= 0.85:  # 85% similarity for parent names
                            father_match = True
                            confidence_score += 0.2
                            match_details.append(f"Father name match: {father_similarity:.2f}")

                # Photo comparison (if both photos available)
                if photo_data and citizen.photo:
                    # Simple photo hash comparison (in production, use proper image comparison)
                    if photo_data == citizen.photo:
                        confidence_score += 0.3
                        match_details.append("Exact photo match")
                    else:
                        # Could implement more sophisticated image comparison here
                        pass

                # Debug final confidence score
                logger.info(f"   Final confidence score: {confidence_score:.2f} (threshold: 0.3)")
                logger.info(f"   Match details: {match_details}")

                # If confidence score is high enough, consider it a match
                # Lower threshold since we're using more reliable indicators
                if confidence_score >= 0.3:  # 30% confidence threshold - more sensitive detection
                    matches.append({
                        'type': 'personal_info_match',
                        'tenant_id': tenant.id,
                        'tenant_name': tenant.name,
                        'citizen_id': citizen.id,
                        'citizen_name': citizen.get_full_name(),
                        'match_confidence': confidence_score,
                        'details': '; '.join(match_details)
                    })
                    logger.warning(f"🚨 Personal info match found in {tenant.name}: {citizen.get_full_name()} (confidence: {confidence_score:.2f})")
                else:
                    logger.info(f"   ❌ No match - confidence {confidence_score:.2f} below threshold")

        except Exception as e:
            logger.error(f"Error finding similar citizens in {tenant.name}: {str(e)}")

        return matches
    
    def _check_biometric_matches(self, biometric_data: Dict, current_tenant_id: int) -> Dict:
        """Check for biometric matches across tenants."""
        logger.info(f"🔍 Checking biometric matches")
        matches = []
        
        try:
            # Get all kebele tenants except current one
            tenants = Tenant.objects.filter(type='kebele').exclude(id=current_tenant_id)
            
            for tenant in tenants:
                try:
                    with schema_context(tenant.schema_name):
                        # Search for similar biometric data
                        biometric_matches = self._find_similar_biometrics(biometric_data, tenant)
                        matches.extend(biometric_matches)
                except Exception as e:
                    logger.error(f"Error checking biometrics in tenant {tenant.name}: {str(e)}")
                    continue
            
            return {'found': len(matches) > 0, 'matches': matches}
            
        except Exception as e:
            logger.error(f"Error in biometric check: {str(e)}")
            return {'found': False, 'matches': []}
    
    def _find_similar_biometrics(self, biometric_data: Dict, tenant: Tenant) -> List[Dict]:
        """Find similar biometric data in a specific tenant."""
        matches = []
        
        try:
            # Note: This is a simplified biometric matching
            # In production, you would use specialized biometric matching algorithms
            
            biometrics = Biometric.objects.all()
            
            for biometric in biometrics:
                match_score = 0.0
                match_details = []
                
                # Simple hash comparison (in production, use proper biometric matching)
                if (biometric_data.get('left_thumb_fingerprint') and 
                    biometric.left_thumb_fingerprint == biometric_data['left_thumb_fingerprint']):
                    match_score += 0.5
                    match_details.append("Left thumb fingerprint match")
                
                if (biometric_data.get('right_thumb_fingerprint') and 
                    biometric.right_thumb_fingerprint == biometric_data['right_thumb_fingerprint']):
                    match_score += 0.5
                    match_details.append("Right thumb fingerprint match")
                
                if match_score >= 0.5:  # 50% biometric match threshold
                    matches.append({
                        'type': 'biometric_match',
                        'tenant_id': tenant.id,
                        'tenant_name': tenant.name,
                        'citizen_id': biometric.citizen.id,
                        'citizen_name': biometric.citizen.get_full_name(),
                        'match_confidence': match_score,
                        'details': '; '.join(match_details)
                    })
                    logger.warning(f"🚨 Biometric match found in {tenant.name}: {biometric.citizen.get_full_name()}")
            
        except Exception as e:
            logger.error(f"Error finding similar biometrics in {tenant.name}: {str(e)}")
        
        return matches
    
    def _check_application_patterns(self, citizen_data: Dict, current_tenant_id: int) -> Dict:
        """Check for suspicious application patterns."""
        logger.info(f"🔍 Checking application patterns")
        matches = []
        suspicious = False
        
        try:
            # Check for recent applications with similar data across tenants
            recent_date = timezone.now() - timedelta(days=30)  # Last 30 days
            
            tenants = Tenant.objects.filter(type='kebele').exclude(id=current_tenant_id)
            
            for tenant in tenants:
                try:
                    with schema_context(tenant.schema_name):
                        # Check for recent service requests with similar personal info
                        recent_requests = IDCardServiceRequest.objects.filter(
                            requested_at__gte=recent_date
                        )
                        
                        for request in recent_requests:
                            citizen = request.citizen
                            similarity_score = self._calculate_similarity_score(citizen_data, {
                                'first_name': citizen.first_name,
                                'last_name': citizen.last_name,
                                'date_of_birth': citizen.date_of_birth,
                                'phone': citizen.phone,
                                'email': citizen.email
                            })
                            
                            if similarity_score >= 0.7:  # 70% similarity threshold
                                suspicious = True
                                matches.append({
                                    'type': 'suspicious_pattern',
                                    'tenant_id': tenant.id,
                                    'tenant_name': tenant.name,
                                    'citizen_id': citizen.id,
                                    'citizen_name': citizen.get_full_name(),
                                    'match_confidence': similarity_score,
                                    'details': f"Recent application in {tenant.name} with {similarity_score:.2f} similarity"
                                })
                                
                except Exception as e:
                    logger.error(f"Error checking patterns in tenant {tenant.name}: {str(e)}")
                    continue
            
            return {'suspicious': suspicious, 'matches': matches}
            
        except Exception as e:
            logger.error(f"Error in pattern check: {str(e)}")
            return {'suspicious': False, 'matches': []}
    
    def _calculate_similarity_score(self, data1: Dict, data2: Dict) -> float:
        """Calculate overall similarity score between two citizen data sets using reliable identifiers."""
        score = 0.0
        total_weight = 0.0

        # Full name similarity (weight: 0.4)
        if data1.get('first_name') and data2.get('first_name'):
            first_sim = difflib.SequenceMatcher(None,
                data1['first_name'].lower(),
                data2['first_name'].lower()
            ).ratio()

            middle_sim = 0
            if data1.get('middle_name') and data2.get('middle_name'):
                middle_sim = difflib.SequenceMatcher(None,
                    data1['middle_name'].lower(),
                    data2['middle_name'].lower()
                ).ratio()

            last_sim = 0
            if data1.get('last_name') and data2.get('last_name'):
                last_sim = difflib.SequenceMatcher(None,
                    data1['last_name'].lower(),
                    data2['last_name'].lower()
                ).ratio()

            # Calculate weighted name similarity
            name_score = (first_sim * 0.4 + middle_sim * 0.2 + last_sim * 0.4)
            score += name_score * 0.4
            total_weight += 0.4

        # Date of birth (weight: 0.3)
        if data1.get('date_of_birth') and data2.get('date_of_birth'):
            if data1['date_of_birth'] == data2['date_of_birth']:
                score += 0.3
            total_weight += 0.3

        # Mother's name (weight: 0.15)
        if data1.get('mother_full_name') and data2.get('mother_full_name'):
            mother_sim = difflib.SequenceMatcher(None,
                data1['mother_full_name'].lower(),
                data2['mother_full_name'].lower()
            ).ratio()
            if mother_sim >= 0.85:  # High threshold for parent names
                score += 0.15
            total_weight += 0.15

        # Father's name (weight: 0.15)
        if data1.get('father_full_name') and data2.get('father_full_name'):
            father_sim = difflib.SequenceMatcher(None,
                data1['father_full_name'].lower(),
                data2['father_full_name'].lower()
            ).ratio()
            if father_sim >= 0.85:  # High threshold for parent names
                score += 0.15
            total_weight += 0.15

        # Photo comparison (weight: 0.1)
        if data1.get('photo') and data2.get('photo'):
            if data1['photo'] == data2['photo']:  # Exact match for now
                score += 0.1
            total_weight += 0.1

        return score / total_weight if total_weight > 0 else 0.0
    
    def _normalize_phone(self, phone: str) -> str:
        """Normalize phone number for comparison."""
        if not phone:
            return ""
        # Remove all non-digit characters
        return re.sub(r'\D', '', phone)
    
    def _generate_recommendations(self, fraud_indicators: Dict) -> List[str]:
        """Generate recommendations based on fraud indicators."""
        recommendations = []
        
        if fraud_indicators['risk_level'] == 'critical':
            recommendations.append("🚨 CRITICAL: Block application immediately")
            recommendations.append("Require in-person verification with biometric confirmation")
            recommendations.append("Contact other kebeles for verification")
            
        elif fraud_indicators['risk_level'] == 'high':
            recommendations.append("⚠️ HIGH RISK: Require additional verification")
            recommendations.append("Request additional documentation")
            recommendations.append("Conduct manual review before approval")
            
        elif fraud_indicators['risk_level'] == 'medium':
            recommendations.append("⚠️ MEDIUM RISK: Enhanced verification recommended")
            recommendations.append("Verify contact information")
            recommendations.append("Check with applicant about previous applications")
        
        if 'digital_id_duplicate' in fraud_indicators['indicators']:
            recommendations.append("Digital ID already exists - investigate immediately")
            
        if 'biometric_match' in fraud_indicators['indicators']:
            recommendations.append("Biometric match found - requires biometric expert review")
            
        if 'personal_info_match' in fraud_indicators['indicators']:
            recommendations.append("Personal information matches existing records - verify identity")
        
        return recommendations


# Singleton instance
fraud_detection_service = FraudDetectionService()
