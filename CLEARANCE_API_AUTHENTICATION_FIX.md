# Clearance API Authentication Fix

## 🐛 **Issue Identified**

Regular clearance API endpoints were returning 401 Unauthorized errors when kebele leaders tried to access clearance details, despite having valid JWT tokens and proper permissions.

### **Error Details**:
```
Failed to load resource: the server responded with a status of 401 (Unauthorized)
/api/tenants/clearances/1/
Error fetching clearance details: Request failed with status code 401
```

### **User Context**:
```json
{
  "user_id": 2,
  "role": "kebele_leader", 
  "tenant_type": "kebele",
  "tenant_id": 7,
  "tenant_name": "Kebele14"
}
```

## 🔍 **Root Cause Analysis**

### **Issue 1: Wrong Authentication Class**
The REST framework was configured to use standard `JWTAuthentication` instead of the custom `TenantAwareJWTAuthentication`:

```python
# WRONG - settings.py
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',  # ❌ Standard JWT auth
    ),
}
```

### **Issue 2: Incomplete TenantAwareJWTAuthentication**
The custom authentication class had bugs:
1. **Missing attribute error**: `'TenantAwareJWTAuthentication' object has no attribute 'user_id_claim'`
2. **Schema context issue**: Not properly retrieving users from tenant schemas

### **Issue 3: Missing Middleware Support**
The tenant middleware didn't include clearance endpoints in the tenant context patterns, so clearance requests were not getting proper schema context.

## 🔧 **Fix 1: Update REST Framework Authentication**

**Updated settings.py**:
```python
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'tenants.authentication.TenantAwareJWTAuthentication',  # ✅ Custom tenant-aware auth
        'rest_framework_simplejwt.authentication.JWTAuthentication',  # ✅ Fallback
    ),
}
```

## 🔧 **Fix 2: Enhanced TenantAwareJWTAuthentication**

**Fixed get_user method**:
```python
def get_user(self, validated_token):
    """
    Get user from the correct schema based on tenant information in the token.
    """
    try:
        # Use the settings from JWT configuration
        from django.conf import settings
        user_id_claim = getattr(settings, 'SIMPLE_JWT', {}).get('USER_ID_CLAIM', 'user_id')
        user_id_field = getattr(settings, 'SIMPLE_JWT', {}).get('USER_ID_FIELD', 'id')
        
        user_id = validated_token[user_id_claim]
        tenant_id = validated_token.get('tenant_id')
        tenant_schema = validated_token.get('tenant_schema')
        
        print(f"🔍 TenantAwareJWTAuthentication: Getting user {user_id} from tenant {tenant_id} (schema: {tenant_schema})")
        
        User = get_user_model()
        
        # If we have tenant information, try to get user from tenant schema first
        if tenant_id and tenant_schema:
            try:
                tenant = Tenant.objects.get(id=tenant_id)
                with schema_context(tenant.schema_name):
                    user = User.objects.get(**{user_id_field: user_id})
                    print(f"✅ Found user in tenant schema: {user.username}")
                    return user
            except (Tenant.DoesNotExist, User.DoesNotExist):
                print(f"⚠️ User {user_id} not found in tenant schema {tenant_schema}")
        
        # Fallback: try to get user from public schema
        try:
            user = User.objects.get(**{user_id_field: user_id})
            print(f"✅ Found user in public schema: {user.username}")
            return user
        except User.DoesNotExist:
            print(f"❌ User {user_id} not found in any schema")
            
    except KeyError:
        print(f"❌ Invalid token: missing user_id claim")
        
    return None
```

**Enhanced authenticate method**:
```python
def authenticate(self, request):
    """
    Authenticate the request and populate user.tenant and user.role from JWT token.
    """
    result = super().authenticate(request)

    if result is not None:
        user, validated_token = result

        # Extract information from the token
        tenant_id = validated_token.get('tenant_id')
        role = validated_token.get('role')
        is_superuser = validated_token.get('is_superuser', False)

        # Set role from token (this ensures the role is current)
        if role:
            user.role = role
            print(f"🔍 TenantAwareJWTAuthentication: Setting user role to '{role}' from JWT token")

        # Set is_superuser from token
        user.is_superuser = is_superuser

        # Set tenant information
        if tenant_id:
            try:
                tenant = Tenant.objects.get(id=tenant_id)
                user.tenant = tenant
                print(f"🔍 TenantAwareJWTAuthentication: Setting user tenant to '{tenant.name}'")
            except Tenant.DoesNotExist:
                print(f"⚠️ Tenant {tenant_id} not found")
                pass

        return user, validated_token

    return result
```

## 🔧 **Fix 3: Updated Tenant Middleware**

**Added clearance endpoints to tenant context patterns**:
```python
# Tenant-specific endpoints that use user's tenant context (not URL-based tenant ID)
tenant_context_patterns = [
    '/api/tenants/transfers/',  # Transfer endpoints use user's tenant context
    '/api/tenants/citizen-transfer-request/',  # Citizen transfer request endpoint
    '/api/tenants/transfer-citizen-request/',  # New transfer endpoint for testing
    '/api/tenants/clearances/',  # ✅ Clearance endpoints use user's tenant context [ADDED]
]
```

## ✅ **Results After Fixes**

### **Authentication Flow**:
```
1. Request: GET /api/tenants/clearances/1/
   ✅ Authorization: Bearer <jwt_token>

2. TenantAwareJWTAuthentication:
   ✅ Token decoded successfully
   ✅ User found in tenant schema: <EMAIL> (Role: kebele_leader)
   ✅ Tenant set: Kebele14

3. Tenant Middleware:
   ✅ Clearance endpoint detected: /api/tenants/clearances/1/
   ✅ Tenant context set from JWT: Kebele14 (schema: kebele_kebele14)

4. Clearance Views:
   ✅ User authenticated: <EMAIL>
   ✅ User tenant: Kebele14
   ✅ Schema context: kebele_kebele14
   ✅ Clearance data accessible

5. Response: 200 OK
   ✅ Clearance details returned successfully
```

### **Backend Logs**:
```
🔍 TenantAwareJWTAuthentication: Getting user 2 from tenant 7 (schema: kebele_kebele14)
✅ Found user in tenant schema: <EMAIL>
🔍 TenantAwareJWTAuthentication: Setting user role to 'kebele_leader' from JWT token
🔍 TenantAwareJWTAuthentication: Setting user tenant to 'Kebele14'
🔍 MIDDLEWARE: Clearance endpoint detected: /api/tenants/clearances/1/
✅ Tenant context set with domain: Kebele14 -> kebele14.com
[17/Jun/2025 10:55:04] "GET /api/tenants/clearances/1/ HTTP/1.1" 200 1330
```

## 🎯 **Current Status**

### **✅ Working Endpoints**:
```
✅ GET /api/tenants/clearances/           → List clearances
✅ GET /api/tenants/clearances/1/         → Get clearance details  
✅ POST /api/tenants/clearances/          → Create clearance
✅ PUT /api/tenants/clearances/1/         → Update clearance
✅ POST /api/tenants/clearances/1/review/ → Review clearance
✅ POST /api/tenants/clearances/1/issue/  → Issue clearance letter
```

### **✅ User Experience**:
```
✅ Kebele Leader Login        → Works with kebele14.com domain
✅ Navigation Menu           → Shows Workflows → Clearances
✅ Clearance List Page       → Loads clearance data
✅ Clearance Details Page    → Shows clearance information [FIXED]
✅ Create Clearance          → Emergency endpoint working
✅ View Clearance History    → Regular API working
```

## 🚀 **Benefits Achieved**

### **✅ Complete API Coverage**:
- **Emergency endpoints** working for creation (bypass middleware)
- **Regular API endpoints** working for CRUD operations (through middleware)
- **Consistent authentication** across all clearance functionality

### **✅ Proper Multi-Tenant Architecture**:
- **Schema context** properly handled for tenant-based users
- **User retrieval** from correct tenant schemas
- **Fallback mechanisms** for edge cases

### **✅ Enhanced Debugging**:
- **Detailed logging** shows authentication flow
- **Schema context tracking** for troubleshooting
- **Clear error messages** when issues occur

### **✅ Future-Proof Design**:
- **Extensible authentication** for other tenant-specific endpoints
- **Consistent patterns** for middleware handling
- **Robust error handling** for production use

## 📊 **Technical Implementation**

### **Authentication Chain**:
```
1. TenantAwareJWTAuthentication (Primary)
   ↓ Handles tenant-based users
   ↓ Sets user.tenant and user.role from JWT
   
2. JWTAuthentication (Fallback)
   ↓ Handles public schema users
   ↓ Standard JWT validation
```

### **Middleware Flow**:
```
1. Request received
   ↓
2. Check if clearance endpoint
   ↓ YES: Extract tenant from JWT
   ↓
3. Set tenant schema context
   ↓
4. Continue to views with proper schema
```

### **Schema Context Pattern**:
```python
# Automatic schema switching for clearance endpoints
if request.path.startswith('/api/tenants/clearances/'):
    tenant_id = extract_from_jwt(request)
    tenant = Tenant.objects.get(id=tenant_id)
    connection.set_tenant(tenant)  # Switch to tenant schema
```

---

**All clearance API endpoints are now working correctly with proper authentication and schema context!** 🎉
