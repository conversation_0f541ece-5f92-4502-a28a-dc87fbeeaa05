# Generated by Django 4.2.7 on 2025-06-07 12:06

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('idcards', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='idcard',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_id_cards', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='idcard',
            name='issued_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='issued_id_cards', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='idcard',
            name='kebele_approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='kebele_approved_id_cards', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='idcard',
            name='printed_by',
            field=models.ForeignKey(blank=True, help_text='User who printed the ID card', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='printed_id_cards', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='idcard',
            name='subcity_approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='subcity_approved_id_cards', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='idcard',
            name='template',
            field=models.ForeignKey(blank=True, help_text='ID Card template to use', null=True, on_delete=django.db.models.deletion.SET_NULL, to='idcards.idcardtemplate'),
        ),
        migrations.AddField(
            model_name='idcardreprint',
            name='old_id_card',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reprint_requests', to='idcards.idcard'),
        ),
        migrations.AddField(
            model_name='idcardreplacement',
            name='old_id_card',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='replacement_requests', to='idcards.idcard'),
        ),
        migrations.AddField(
            model_name='idcardrenewal',
            name='old_id_card',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='renewal_requests', to='idcards.idcard'),
        ),
    ]
