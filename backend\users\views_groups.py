from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q, Count, Prefetch
from django.contrib.auth.models import Group, Permission
from django.contrib.auth import get_user_model
from .models_groups import TenantGroup, GroupMembership, GroupTemplate
from .serializers_groups import (
    TenantGroupSerializer, GroupMembershipSerializer, GroupTemplateSerializer,
    GroupManagementSerializer, PermissionSerializer
)
from .permissions_tenant_roles import (
    get_manageable_tenants_for_user, get_users_in_manageable_tenants
)
from tenants.models import Tenant

User = get_user_model()


class GroupManagementViewSet(viewsets.ViewSet):
    """
    ViewSet for managing groups and group memberships.
    """
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def tenant_groups(self, request):
        """
        Get all groups for manageable tenants.
        """
        tenant_id = request.query_params.get('tenant_id')
        
        # Get manageable tenants
        manageable_tenants = get_manageable_tenants_for_user(request.user)
        manageable_tenant_ids = [t.id for t in manageable_tenants]
        
        # Base queryset - include tenant-specific groups and allowed global groups
        base_filter = Q(tenant_id__in=manageable_tenant_ids) | Q(tenant__isnull=True)

        # If filtering by specific tenant, add tenant type restrictions for global groups
        if tenant_id:
            try:
                tenant = Tenant.objects.get(id=tenant_id)
                # Include tenant-specific groups and global groups allowed for this tenant type
                base_filter = (
                    Q(tenant_id=tenant_id) |
                    (Q(tenant__isnull=True) & (
                        Q(allowed_tenant_types=[]) |  # No restrictions
                        Q(allowed_tenant_types__contains=[tenant.type])  # Allowed for this tenant type
                    ))
                )
            except Tenant.DoesNotExist:
                pass

        queryset = TenantGroup.objects.filter(
            base_filter,
            is_active=True
        ).select_related('tenant', 'created_by').prefetch_related('group__permissions')
        
        # Serialize groups
        groups_data = []
        for group in queryset:
            group_data = {
                'id': group.id,
                'name': group.name,
                'description': group.description,
                'group_type': group.group_type,
                'level': group.level,
                'is_system_group': group.is_system_group,
                'tenant': {
                    'id': group.tenant.id if group.tenant else None,
                    'name': group.tenant.name if group.tenant else None,
                    'type': group.tenant.type if group.tenant else None
                } if group.tenant else None,
                'users_count': group.users_count,
                'permissions_count': group.permissions_count,
                'created_at': group.created_at,
                'created_by': group.created_by.email if group.created_by else None
            }
            groups_data.append(group_data)
        
        # Sort by level (highest first) then by name
        groups_data.sort(key=lambda x: (-x['level'], x['name']))
        
        return Response({
            'groups': groups_data,
            'total_groups': len(groups_data),
            'tenant_filter': tenant_id
        })
    
    @action(detail=False, methods=['get'])
    def groups_with_users(self, request):
        """
        Get all groups with their users.
        """
        tenant_id = request.query_params.get('tenant_id')
        
        # Get manageable tenants
        manageable_tenants = get_manageable_tenants_for_user(request.user)
        manageable_tenant_ids = [t.id for t in manageable_tenants]
        
        # Base queryset - include tenant-specific groups and allowed global groups
        base_filter = Q(tenant_id__in=manageable_tenant_ids) | Q(tenant__isnull=True)

        # If filtering by specific tenant, add tenant type restrictions for global groups
        if tenant_id:
            try:
                tenant = Tenant.objects.get(id=tenant_id)
                # Include tenant-specific groups and global groups allowed for this tenant type
                base_filter = (
                    Q(tenant_id=tenant_id) |
                    (Q(tenant__isnull=True) & (
                        Q(allowed_tenant_types=[]) |  # No restrictions
                        Q(allowed_tenant_types__contains=[tenant.type])  # Allowed for this tenant type
                    ))
                )
            except Tenant.DoesNotExist:
                pass

        queryset = TenantGroup.objects.filter(
            base_filter,
            is_active=True
        ).select_related('tenant').prefetch_related(
            'group__user_set',
            'group__permissions'
        )
        
        groups_data = []
        for group in queryset:
            # Get users in this group using GroupMembership model (handles cross-schema lookup)
            memberships = GroupMembership.objects.filter(group=group, is_active=True)

            users_list = []
            for membership in memberships:
                try:
                    user = membership.user  # This uses our custom property that handles schema context
                    if user and user.is_active:
                        # Filter by manageable tenants
                        if user.tenant and user.tenant.id not in manageable_tenant_ids:
                            continue

                        # Filter by specific tenant if requested
                        if tenant_id and user.tenant and str(user.tenant.id) != str(tenant_id):
                            continue

                        users_list.append({
                            'id': user.id,
                            'email': user.email,
                            'first_name': user.first_name,
                            'last_name': user.last_name,
                            'full_name': f"{user.first_name} {user.last_name}".strip(),
                            'username': user.username,
                            'tenant': {
                                'id': user.tenant.id if user.tenant else None,
                                'name': user.tenant.name if user.tenant else None,
                                'type': user.tenant.type if user.tenant else None
                            } if user.tenant else None,
                            'date_joined': user.date_joined,
                            'last_login': user.last_login,
                            'is_primary': membership.is_primary
                        })
                except Exception as e:
                    # Log the error but continue processing other users
                    print(f"Error getting user for membership {membership.user_email}: {e}")
                    continue
            
            # Get permissions
            permissions_list = []
            for perm in group.group.permissions.all():
                permissions_list.append({
                    'codename': perm.codename,
                    'name': perm.name,
                    'content_type': perm.content_type.model if perm.content_type else None
                })
            
            group_data = {
                'id': group.id,
                'name': group.name,
                'description': group.description,
                'group_type': group.group_type,
                'level': group.level,
                'is_system_group': group.is_system_group,
                'tenant': {
                    'id': group.tenant.id if group.tenant else None,
                    'name': group.tenant.name if group.tenant else None,
                    'type': group.tenant.type if group.tenant else None
                } if group.tenant else None,
                'users': users_list,
                'users_count': len(users_list),
                'permissions': permissions_list,
                'permissions_count': len(permissions_list),
                'created_at': group.created_at
            }
            groups_data.append(group_data)
        
        # Sort by level (highest first) then by name
        groups_data.sort(key=lambda x: (-x['level'], x['name']))
        
        return Response({
            'groups': groups_data,
            'total_groups': len(groups_data),
            'total_users': sum(group['users_count'] for group in groups_data),
            'tenant_filter': tenant_id
        })
    
    @action(detail=False, methods=['get'])
    def user_groups_details(self, request):
        """
        Get detailed user information with all groups and permissions.
        """
        user_id = request.query_params.get('user_id')
        user_email = request.query_params.get('user_email')
        
        if not user_id and not user_email:
            return Response(
                {'error': 'user_id or user_email parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            if user_id:
                user = User.objects.get(id=user_id, is_active=True)
            else:
                user = User.objects.get(email=user_email, is_active=True)
            
            # Check if current user can view this user's details
            manageable_users = get_users_in_manageable_tenants(request.user)
            if user not in manageable_users and not request.user.is_superuser:
                return Response(
                    {'error': 'You do not have permission to view this user\'s details'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # Get user's groups
            user_groups = user.get_user_groups()
            primary_group = user.get_primary_group()
            
            groups_info = []
            for group in user_groups:
                # Get membership details
                membership = GroupMembership.objects.filter(
                    user=user, group=group, is_active=True
                ).first()
                
                # Get group permissions
                permissions_list = []
                for perm in group.group.permissions.all():
                    permissions_list.append({
                        'codename': perm.codename,
                        'name': perm.name,
                        'content_type': perm.content_type.model if perm.content_type else None
                    })
                
                group_info = {
                    'id': group.id,
                    'name': group.name,
                    'description': group.description,
                    'group_type': group.group_type,
                    'level': group.level,
                    'is_primary': membership.is_primary if membership else False,
                    'is_system_group': group.is_system_group,
                    'assigned_at': membership.assigned_at if membership else None,
                    'assigned_by': membership.assigned_by.email if membership and membership.assigned_by else None,
                    'reason': membership.reason if membership else '',
                    'scope': group.tenant.name if group.tenant else 'Global',
                    'permissions': permissions_list,
                    'permissions_count': len(permissions_list)
                }
                groups_info.append(group_info)
            
            # Get effective permissions
            effective_permissions = user.get_effective_permissions()
            effective_permissions_list = []
            permissions_by_category = {}
            
            for perm in effective_permissions:
                perm_data = {
                    'codename': perm.codename,
                    'name': perm.name,
                    'content_type': perm.content_type.model if perm.content_type else None
                }
                effective_permissions_list.append(perm_data)
                
                # Group by content type (category)
                category = perm.content_type.model if perm.content_type else 'Other'
                if category not in permissions_by_category:
                    permissions_by_category[category] = []
                permissions_by_category[category].append(perm_data)
            
            return Response({
                'user': {
                    'id': user.id,
                    'email': user.email,
                    'username': user.username,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'full_name': f"{user.first_name} {user.last_name}".strip(),
                    'is_active': user.is_active,
                    'is_staff': user.is_staff,
                    'is_superuser': user.is_superuser,
                    'date_joined': user.date_joined,
                    'last_login': user.last_login,
                    'tenant': {
                        'id': user.tenant.id if user.tenant else None,
                        'name': user.tenant.name if user.tenant else None,
                        'type': user.tenant.type if user.tenant else None,
                        'parent': user.tenant.parent.name if user.tenant and user.tenant.parent else None
                    } if user.tenant else None
                },
                'groups': {
                    'assigned_groups': groups_info,
                    'total_groups': len(groups_info),
                    'primary_group': {
                        'id': primary_group.id if primary_group else None,
                        'name': primary_group.name if primary_group else None,
                        'level': primary_group.level if primary_group else None
                    } if primary_group else None
                },
                'permissions': {
                    'effective_permissions': effective_permissions_list,
                    'total_permissions': len(effective_permissions_list),
                    'permissions_by_category': permissions_by_category
                }
            })
            
        except User.DoesNotExist:
            return Response(
                {'error': 'User not found'},
                status=status.HTTP_404_NOT_FOUND
            )
    
    @action(detail=False, methods=['post'])
    def manage_group(self, request):
        """
        Manage groups (create, assign users, assign permissions, etc.)
        """
        serializer = GroupManagementSerializer(data=request.data, context={'request': request})
        
        if serializer.is_valid():
            try:
                result = serializer.save()
                return Response(result, status=status.HTTP_200_OK)
            except Exception as e:
                return Response(
                    {'error': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['get'])
    def available_permissions(self, request):
        """
        Get all available permissions for group assignment.
        """
        permissions = Permission.objects.all().select_related('content_type')
        
        permissions_data = []
        permissions_by_category = {}
        
        for perm in permissions:
            category = perm.content_type.model if perm.content_type else 'Other'
            perm_data = {
                'id': perm.id,
                'codename': perm.codename,
                'name': perm.name,
                'content_type': category,
                'description': f"Can {perm.name.lower()}"
            }
            
            permissions_data.append(perm_data)
            
            if category not in permissions_by_category:
                permissions_by_category[category] = []
            permissions_by_category[category].append(perm_data)
        
        return Response({
            'permissions': permissions_data,
            'permissions_by_category': permissions_by_category,
            'total_permissions': len(permissions_data)
        })
    
    @action(detail=False, methods=['get'])
    def group_templates(self, request):
        """
        Get available group templates.
        """
        templates = GroupTemplate.objects.filter(is_system_template=True)
        serializer = GroupTemplateSerializer(templates, many=True)
        
        return Response({
            'templates': serializer.data,
            'total_templates': len(serializer.data)
        })
