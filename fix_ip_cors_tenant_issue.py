#!/usr/bin/env python3
"""
Fix the specific IP-related CORS issue preventing tenant list from loading.
"""

import requests
import json

def test_ip_cors_issue():
    """Test the specific IP-related CORS issue."""
    
    base_url = "http://************:8000"
    frontend_url = "http://************:3000"
    
    print("🔍 Testing IP-Related CORS Issue")
    print("=" * 40)
    
    # Step 1: Test authentication with CORS headers
    print("📍 Step 1: Testing authentication with CORS headers...")
    
    try:
        auth_response = requests.post(
            f"{base_url}/api/auth/token/",
            json={"email": "<EMAIL>", "password": "admin123"},
            headers={
                "Content-Type": "application/json",
                "Origin": frontend_url,
                "Referer": f"{frontend_url}/login"
            },
            timeout=10
        )
        
        print(f"Auth status: {auth_response.status_code}")
        print(f"Auth CORS headers: {[h for h in auth_response.headers.keys() if 'access-control' in h.lower()]}")
        
        if auth_response.status_code == 200:
            auth_data = auth_response.json()
            access_token = auth_data.get('access')
            print("✅ Authentication with CORS successful")
        else:
            print(f"❌ Authentication failed: {auth_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return False
    
    # Step 2: Test CORS preflight for tenant endpoint
    print(f"\n📍 Step 2: Testing CORS preflight for tenant endpoint...")
    
    try:
        preflight_response = requests.options(
            f"{base_url}/api/tenants/",
            headers={
                "Origin": frontend_url,
                "Access-Control-Request-Method": "GET",
                "Access-Control-Request-Headers": "authorization,content-type"
            },
            timeout=10
        )
        
        print(f"Preflight status: {preflight_response.status_code}")
        print(f"Preflight headers:")
        for header, value in preflight_response.headers.items():
            if 'access-control' in header.lower():
                print(f"  {header}: {value}")
        
        if preflight_response.status_code != 200:
            print("❌ CORS preflight failed - this is likely the issue!")
            return False
        else:
            print("✅ CORS preflight successful")
            
    except Exception as e:
        print(f"❌ CORS preflight error: {e}")
        return False
    
    # Step 3: Test actual tenant request with full CORS headers
    print(f"\n📍 Step 3: Testing tenant request with full CORS headers...")
    
    try:
        tenant_response = requests.get(
            f"{base_url}/api/tenants/",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json",
                "Origin": frontend_url,
                "Referer": f"{frontend_url}/tenants"
            },
            timeout=10
        )
        
        print(f"Tenant request status: {tenant_response.status_code}")
        print(f"Response CORS headers:")
        for header, value in tenant_response.headers.items():
            if 'access-control' in header.lower():
                print(f"  {header}: {value}")
        
        if tenant_response.status_code == 200:
            try:
                data = tenant_response.json()
                print("✅ Tenant request successful!")
                
                if isinstance(data, list):
                    print(f"Found {len(data)} tenants")
                elif isinstance(data, dict) and 'results' in data:
                    print(f"Found {len(data.get('results', []))} tenants (paginated)")
                    
                return True
                
            except json.JSONDecodeError:
                print(f"❌ Invalid JSON response: {tenant_response.text[:200]}")
                return False
        else:
            print(f"❌ Tenant request failed: {tenant_response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ ERR_EMPTY_RESPONSE - Connection dropped during tenant request")
        return False
    except Exception as e:
        print(f"❌ Tenant request error: {e}")
        return False
    
    # Step 4: Compare with localhost behavior
    print(f"\n📍 Step 4: Comparing with localhost behavior...")
    
    localhost_origins = ["http://localhost:3000", "http://127.0.0.1:3000"]
    
    for origin in localhost_origins:
        try:
            # Test auth with localhost origin
            auth_resp = requests.post(
                f"{base_url}/api/auth/token/",
                json={"email": "<EMAIL>", "password": "admin123"},
                headers={"Content-Type": "application/json", "Origin": origin},
                timeout=5
            )
            
            if auth_resp.status_code == 200:
                token = auth_resp.json().get('access')
                
                # Test tenant endpoint with localhost origin
                tenant_resp = requests.get(
                    f"{base_url}/api/tenants/",
                    headers={"Authorization": f"Bearer {token}", "Origin": origin},
                    timeout=5
                )
                
                print(f"Origin {origin}: Auth={auth_resp.status_code}, Tenant={tenant_resp.status_code}")
            else:
                print(f"Origin {origin}: Auth failed ({auth_resp.status_code})")
                
        except Exception as e:
            print(f"Origin {origin}: Error - {e}")

def fix_cors_configuration():
    """Provide specific CORS configuration fixes."""
    
    print(f"\n📍 Step 5: CORS Configuration Analysis...")
    
    print("Current CORS settings should include:")
    print("✅ CORS_ALLOW_ALL_ORIGINS = True")
    print("✅ CORS_ALLOW_CREDENTIALS = True") 
    print("✅ http://************:3000 in CORS_ALLOWED_ORIGINS")
    print("")
    
    print("Required CORS headers for tenant endpoint:")
    print("- Access-Control-Allow-Origin: http://************:3000")
    print("- Access-Control-Allow-Methods: GET, POST, OPTIONS")
    print("- Access-Control-Allow-Headers: authorization, content-type")
    print("- Access-Control-Allow-Credentials: true")
    print("")
    
    print("Frontend should send:")
    print("- Origin: http://************:3000")
    print("- Authorization: Bearer <token>")
    print("- Content-Type: application/json")

if __name__ == "__main__":
    print("🚀 Fixing IP-Related CORS Tenant Issue")
    print("=" * 45)
    
    success = test_ip_cors_issue()
    fix_cors_configuration()
    
    print("\n" + "=" * 45)
    
    if success:
        print("✅ SUCCESS: CORS is working correctly!")
        print("")
        print("The issue might be:")
        print("1. Frontend caching old failed requests")
        print("2. Browser CORS cache needs clearing")
        print("3. Frontend not sending correct headers")
    else:
        print("❌ FAILED: CORS issue detected")
        print("")
        print("🔧 Immediate fixes needed:")
        print("1. Check Django CORS settings")
        print("2. Restart backend: docker-compose restart backend")
        print("3. Clear browser cache completely")
        print("4. Check frontend axios configuration")
    
    print("")
    print("💡 Quick browser fixes:")
    print("1. Hard refresh: Ctrl+Shift+R")
    print("2. Clear all browser data for the site")
    print("3. Try incognito/private mode")
    print("4. Check browser console for CORS errors")
    print("")
    print("🔍 Frontend debugging:")
    print("1. Open browser console (F12)")
    print("2. Go to Network tab")
    print("3. Navigate to tenant list page")
    print("4. Look for:")
    print("   - OPTIONS request to /api/tenants/ (preflight)")
    print("   - GET request to /api/tenants/ (actual request)")
    print("   - Check if both return 200 OK")
    print("   - Verify CORS headers are present")
