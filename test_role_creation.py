#!/usr/bin/env python3
"""
Test script to debug role creation issues
"""
import os
import sys
import django

# Add the backend directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from users.models import User, Role, Permission, RolePermission
from tenants.models import Tenant
from users.serializers_roles import TenantRoleManagementSerializer

def test_role_creation():
    print("🧪 Testing Role Creation...")
    
    # Check if we have required data
    print("\n1. Checking basic data...")
    
    # Check users
    users = User.objects.all()
    print(f"   Users count: {users.count()}")
    if users.exists():
        print(f"   Sample user: {users.first().email}")
    
    # Check tenants
    tenants = Tenant.objects.all()
    print(f"   Tenants count: {tenants.count()}")
    if tenants.exists():
        print(f"   Sample tenant: {tenants.first().name} ({tenants.first().type})")
    
    # Check permissions
    permissions = Permission.objects.all()
    print(f"   Permissions count: {permissions.count()}")
    if permissions.exists():
        print(f"   Sample permission: {permissions.first().codename}")
    
    # Check existing roles
    roles = Role.objects.all()
    print(f"   Existing roles count: {roles.count()}")
    
    print("\n2. Testing role creation...")
    
    # Get a test tenant (preferably kebele)
    test_tenant = tenants.filter(type='kebele').first()
    if not test_tenant:
        test_tenant = tenants.first()
    
    if not test_tenant:
        print("   ❌ No tenants found. Cannot test role creation.")
        return
    
    print(f"   Using tenant: {test_tenant.name} ({test_tenant.type})")
    
    # Get a test user
    test_user = users.first()
    if not test_user:
        print("   ❌ No users found. Cannot test role creation.")
        return
    
    print(f"   Using user: {test_user.email}")
    
    # Test data for role creation
    test_data = {
        'tenant_id': test_tenant.id,
        'action': 'create_role',
        'role_data': {
            'codename': 'test_role',
            'name': 'Test Role',
            'description': 'A test role for debugging',
            'role_type': 'custom',
            'level': 25,
            'permission_codenames': ['view_citizens', 'create_citizens'] if permissions.filter(codename__in=['view_citizens', 'create_citizens']).exists() else []
        }
    }
    
    print(f"   Test data: {test_data}")
    
    # Create mock request context
    class MockRequest:
        def __init__(self, user):
            self.user = user
    
    mock_request = MockRequest(test_user)
    context = {'request': mock_request}
    
    try:
        print("\n3. Testing serializer validation...")
        serializer = TenantRoleManagementSerializer(data=test_data, context=context)
        
        if serializer.is_valid():
            print("   ✅ Serializer validation passed")
            print(f"   Validated data: {serializer.validated_data}")
            
            print("\n4. Testing role creation...")
            result = serializer.save()
            print(f"   ✅ Role created successfully: {result}")
            
            # Check if role was actually created
            created_role = Role.objects.filter(codename__contains='test_role').first()
            if created_role:
                print(f"   ✅ Role found in database: {created_role.codename}")
                print(f"   Role details: {created_role.name}, Level: {created_role.level}, Tenant: {created_role.scope_tenant}")
            else:
                print("   ❌ Role not found in database")
                
        else:
            print(f"   ❌ Serializer validation failed: {serializer.errors}")
            
    except Exception as e:
        print(f"   ❌ Error during role creation: {e}")
        import traceback
        traceback.print_exc()

def test_direct_role_creation():
    print("\n🔧 Testing Direct Role Creation...")
    
    # Get test tenant
    test_tenant = Tenant.objects.first()
    if not test_tenant:
        print("   ❌ No tenants found")
        return
    
    # Get test user
    test_user = User.objects.first()
    if not test_user:
        print("   ❌ No users found")
        return
    
    try:
        # Create role directly
        role = Role.objects.create(
            codename='direct_test_role',
            name='Direct Test Role',
            description='Direct creation test',
            role_type='custom',
            level=30,
            scope_tenant=test_tenant,
            is_global=False,
            created_by=test_user
        )
        
        print(f"   ✅ Direct role creation successful: {role.codename}")
        
        # Test permission assignment
        permissions = Permission.objects.filter(codename__in=['view_citizens', 'create_citizens'])
        if permissions.exists():
            for permission in permissions:
                RolePermission.objects.create(
                    role_obj=role,
                    permission=permission
                )
            print(f"   ✅ Assigned {permissions.count()} permissions to role")
        
        return role
        
    except Exception as e:
        print(f"   ❌ Direct role creation failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 Role Creation Debug Test")
    print("=" * 50)
    
    test_role_creation()
    test_direct_role_creation()
    
    print("\n" + "=" * 50)
    print("✅ Test completed!")
