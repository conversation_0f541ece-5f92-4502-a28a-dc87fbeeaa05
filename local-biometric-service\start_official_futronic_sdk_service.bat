@echo off
echo ========================================
echo   Official Futronic SDK Service
echo   Professional Biometric Integration
echo ========================================
echo.

echo Checking Python installation...
python --version
if errorlevel 1 (
    echo ERROR: Python not found. Please install Python 3.7+
    pause
    exit /b 1
)

echo.
echo Checking required DLL files...
if not exist "ftrScanAPI.dll" (
    echo ERROR: ftrScanAPI.dll not found
    echo Please ensure Futronic SDK files are in the current directory
    pause
    exit /b 1
)

if exist "ftrMathAPI.dll" (
    echo ✓ ftrMathAPI.dll found - Template matching enabled
) else (
    echo ⚠ ftrMathAPI.dll not found - Template matching disabled
)

echo.
echo Installing required Python packages...
pip install flask flask-cors

echo.
echo Starting Official Futronic SDK Service...
echo Service will be available at: http://localhost:8001
echo.
echo Press Ctrl+C to stop the service
echo.

python official_futronic_sdk_service.py

echo.
echo Service stopped.
pause
