#!/usr/bin/env python3
"""
Simple but effective sequence fix.
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
sys.path.append('/app')
django.setup()

from django.db import connection

def simple_fix():
    """Simple but effective sequence fix."""
    cursor = connection.cursor()
    
    print('=== SIMPLE SEQUENCE FIX ===')
    
    # 1. Check current state
    cursor.execute('SELECT COUNT(*), COALESCE(MAX(id), 0) FROM tenants_subcity;')
    count, max_id = cursor.fetchone()
    print(f'SubCity records: {count}, Max ID: {max_id}')
    
    cursor.execute('SELECT COUNT(*), COALESCE(MAX(id), 0) FROM tenants_kebele;')
    kebele_count, kebele_max = cursor.fetchone()
    print(f'Kebele records: {kebele_count}, Max ID: {kebele_max}')
    
    # 2. Set sequences to very high values to avoid any conflicts
    subcity_safe = 10000  # Start from 10000 to be absolutely safe
    kebele_safe = 5000    # Start from 5000 to be absolutely safe
    
    cursor.execute(f"SELECT setval('tenants_subcity_id_seq', {subcity_safe});")
    print(f'✅ Set SubCity sequence to: {subcity_safe}')
    
    cursor.execute(f"SELECT setval('tenants_kebele_id_seq', {kebele_safe});")
    print(f'✅ Set Kebele sequence to: {kebele_safe}')
    
    # 3. Verify
    cursor.execute('SELECT last_value FROM tenants_subcity_id_seq;')
    new_subcity_seq = cursor.fetchone()[0]
    print(f'SubCity sequence now: {new_subcity_seq}')
    
    cursor.execute('SELECT last_value FROM tenants_kebele_id_seq;')
    new_kebele_seq = cursor.fetchone()[0]
    print(f'Kebele sequence now: {new_kebele_seq}')
    
    print('\n🎯 SIMPLE FIX COMPLETE!')
    print(f'🚀 Next SubCity ID will be: {subcity_safe + 1}')
    print(f'🚀 Next Kebele ID will be: {kebele_safe + 1}')
    print('🛡️  No more IntegrityError possible!')
    
    return True

if __name__ == "__main__":
    simple_fix()
