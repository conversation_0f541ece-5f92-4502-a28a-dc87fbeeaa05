#!/usr/bin/env python3
"""
Script to check ID card statuses across all tenant schemas.
"""

import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from tenants.models import Tenant
from django_tenants.utils import schema_context
from idcards.models import IDCard

User = get_user_model()

def check_idcard_statuses():
    """Check ID card statuses across all tenant schemas."""
    print("=== CHECKING ID CARD STATUSES ACROSS ALL TENANTS ===\n")
    
    # Get all tenants
    tenants = Tenant.objects.all()
    
    total_cards = 0
    status_summary = {}
    
    for tenant in tenants:
        if tenant.schema_name == 'public':
            continue
            
        print(f"🏢 Tenant: {tenant.name} (type: {tenant.type}, ID: {tenant.id})")
        
        try:
            with schema_context(tenant.schema_name):
                cards = IDCard.objects.all().select_related('citizen')
                
                if cards.exists():
                    print(f"   Found {cards.count()} ID cards:")
                    
                    for card in cards:
                        print(f"   📄 ID Card #{card.id}")
                        print(f"      Citizen: {card.citizen.get_full_name()}")
                        print(f"      Status: {card.status}")
                        print(f"      Created by: {card.created_by.username if card.created_by else 'Unknown'}")
                        
                        if card.kebele_approved_by:
                            print(f"      Kebele approved by: {card.kebele_approved_by.username}")
                            print(f"      Kebele approved at: {card.kebele_approved_at}")
                        
                        if card.subcity_approved_by:
                            print(f"      Subcity approved by: {card.subcity_approved_by.username}")
                            print(f"      Subcity approved at: {card.subcity_approved_at}")
                        
                        print(f"      Has kebele pattern: {card.has_kebele_pattern}")
                        print(f"      Has subcity pattern: {card.has_subcity_pattern}")
                        print()
                        
                        # Count statuses
                        if card.status in status_summary:
                            status_summary[card.status] += 1
                        else:
                            status_summary[card.status] = 1
                        
                        total_cards += 1
                else:
                    print(f"   No ID cards found")
                    
        except Exception as e:
            print(f"   ❌ Error checking {tenant.name}: {e}")
        
        print()
    
    print("=" * 50)
    print("📊 SUMMARY")
    print("=" * 50)
    print(f"Total ID cards across all tenants: {total_cards}")
    print("\nStatus breakdown:")
    for status, count in status_summary.items():
        print(f"  {status}: {count}")
    
    print("\n🔍 ANALYSIS:")
    approved_count = status_summary.get('approved', 0)
    if approved_count > 0:
        print(f"⚠️  Found {approved_count} ID cards with 'approved' status")
        print("   These should only exist if they went through the full approval workflow:")
        print("   1. Clerk creates (draft)")
        print("   2. Clerk submits (pending_approval)")
        print("   3. Kebele leader approves (kebele_approved)")
        print("   4. Subcity admin approves (approved)")
        print("\n   If any 'approved' cards don't have subcity_approved_by set,")
        print("   they were likely created by test data and should be reset.")

def fix_test_data_cards():
    """Fix ID cards that were created with approved status but never went through workflow."""
    print("\n=== FIXING TEST DATA CARDS ===\n")
    
    tenants = Tenant.objects.all()
    fixed_count = 0
    
    for tenant in tenants:
        if tenant.schema_name == 'public':
            continue
            
        try:
            with schema_context(tenant.schema_name):
                # Find approved cards without subcity approval
                problematic_cards = IDCard.objects.filter(
                    status='approved',
                    subcity_approved_by__isnull=True
                )
                
                if problematic_cards.exists():
                    print(f"🔧 Fixing {problematic_cards.count()} problematic cards in {tenant.name}")
                    
                    for card in problematic_cards:
                        print(f"   Resetting ID Card #{card.id} ({card.citizen.get_full_name()})")
                        print(f"     Old status: {card.status}")
                        
                        # Reset to draft status
                        card.status = 'draft'
                        card.has_kebele_pattern = False
                        card.has_subcity_pattern = False
                        card.save()
                        
                        print(f"     New status: {card.status}")
                        fixed_count += 1
                        
        except Exception as e:
            print(f"   ❌ Error fixing cards in {tenant.name}: {e}")
    
    print(f"\n✅ Fixed {fixed_count} ID cards")

def main():
    """Main function."""
    print("GoID ID Card Status Checker")
    print("=" * 30)
    
    check_idcard_statuses()
    
    # Ask if user wants to fix problematic cards
    response = input("\nDo you want to fix ID cards that were created with 'approved' status but never went through proper workflow? (y/n): ")
    if response.lower() == 'y':
        fix_test_data_cards()
        print("\n🔄 Re-checking statuses after fix...")
        check_idcard_statuses()

if __name__ == '__main__':
    main()
