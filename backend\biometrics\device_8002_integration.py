"""
Integration with Biometric Device on Port 8002

This module provides integration with your biometric device running on port 8002
and handles direct ANSI/ISO template storage in the database.
"""

import requests
import json
import base64
import logging
from datetime import datetime
from typing import Dict, Optional, Tuple
from django.conf import settings

logger = logging.getLogger(__name__)


class Device8002Integration:
    """
    Integration with biometric device service on port 8002
    """
    
    def __init__(self, device_url: str = "http://localhost:8002"):
        self.device_url = device_url
        self.timeout = 30  # 30 seconds timeout for device operations
        self.last_error = None
    
    def test_device_connection(self) -> bool:
        """Test if the device service is available"""
        try:
            response = requests.get(f"{self.device_url}/health", timeout=5)
            if response.status_code == 200:
                logger.info("✅ Device service on port 8002 is available")
                return True
            else:
                logger.warning(f"⚠️ Device service returned status {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            logger.error("❌ Device service on port 8002 is not available")
            self.last_error = "Device service not available"
            return False
        except Exception as e:
            logger.error(f"❌ Device connection test failed: {e}")
            self.last_error = str(e)
            return False
    
    def capture_fingerprint(self, thumb_type: str = "left") -> Optional[Dict]:
        """
        Capture fingerprint from device on port 8002
        
        Args:
            thumb_type (str): 'left' or 'right'
            
        Returns:
            Dict: Fingerprint data with ANSI/ISO template
        """
        try:
            logger.info(f"🖐️ Capturing {thumb_type} thumb from device on port 8002")
            
            # Call your device service
            response = requests.post(
                f"{self.device_url}/api/capture",
                json={"thumb_type": thumb_type},
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                device_data = response.json()
                
                if device_data.get('success'):
                    # Extract the fingerprint data
                    fingerprint_data = device_data.get('data', {})
                    
                    # Convert to our standard format
                    template_data = self._convert_device_data_to_template(
                        fingerprint_data, thumb_type
                    )
                    
                    logger.info(f"✅ Fingerprint captured successfully: Quality {template_data.get('quality_score', 0)}%")
                    return template_data
                else:
                    error_msg = device_data.get('error', 'Unknown device error')
                    logger.error(f"❌ Device capture failed: {error_msg}")
                    self.last_error = error_msg
                    return None
            else:
                logger.error(f"❌ Device HTTP error: {response.status_code}")
                self.last_error = f"HTTP {response.status_code}: {response.text}"
                return None
                
        except requests.exceptions.Timeout:
            logger.error("❌ Device capture timeout")
            self.last_error = "Device capture timeout"
            return None
        except Exception as e:
            logger.error(f"❌ Capture error: {e}")
            self.last_error = str(e)
            return None
    
    def _convert_device_data_to_template(self, device_data: Dict, thumb_type: str) -> Dict:
        """
        Convert device response to our standard template format
        
        Args:
            device_data (Dict): Raw data from device
            thumb_type (str): 'left' or 'right'
            
        Returns:
            Dict: Standardized template data
        """
        # Extract ANSI/ISO template from device data
        ansi_template = None
        
        # Try different possible field names from your device
        possible_ansi_fields = [
            'ansi_template',
            'ansi_iso_template', 
            'iso_template',
            'template_data',
            'fingerprint_template'
        ]
        
        for field in possible_ansi_fields:
            if field in device_data:
                ansi_template = device_data[field]
                break
        
        # If no direct ANSI template, try to extract from nested data
        if not ansi_template and 'template' in device_data:
            template_info = device_data['template']
            for field in possible_ansi_fields:
                if field in template_info:
                    ansi_template = template_info[field]
                    break
        
        # Create our standard template format
        template_data = {
            'version': '2.0',
            'ansi_iso_template': ansi_template or '',  # Direct ANSI/ISO template
            'device_info': {
                'model': device_data.get('device_model', 'Unknown'),
                'serial': device_data.get('device_serial', 'Unknown'),
                'firmware': device_data.get('firmware_version', 'Unknown'),
                'source_port': 8002
            },
            'thumb_type': thumb_type,
            'capture_time': datetime.now().isoformat(),
            'quality_metrics': {
                'clarity': device_data.get('quality_score', 0),
                'completeness': device_data.get('completeness', 0),
                'uniqueness': device_data.get('uniqueness', 0)
            },
            'minutiae_points': device_data.get('minutiae_points', []),
            'raw_device_data': device_data  # Keep original data for debugging
        }
        
        return template_data
    
    def get_device_status(self) -> Dict:
        """Get device status and information"""
        try:
            response = requests.get(f"{self.device_url}/api/status", timeout=5)
            
            if response.status_code == 200:
                return response.json()
            else:
                return {
                    'success': False,
                    'error': f'HTTP {response.status_code}',
                    'available': False
                }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'available': False
            }
    
    def validate_ansi_template(self, ansi_template: str) -> bool:
        """
        Validate ANSI/ISO template format
        
        Args:
            ansi_template (str): Base64 encoded ANSI template
            
        Returns:
            bool: True if valid
        """
        try:
            if not ansi_template:
                return False
            
            # Decode base64
            template_bytes = base64.b64decode(ansi_template)
            
            # Basic ANSI template validation
            if len(template_bytes) < 20:  # Minimum ANSI template size
                return False
            
            # Check for ANSI header (simplified)
            # Real ANSI templates start with specific byte patterns
            # You can enhance this based on your specific ANSI format
            
            return True
            
        except Exception as e:
            logger.warning(f"ANSI template validation failed: {e}")
            return False


class BiometricDataManager:
    """
    Manager for storing and retrieving biometric data with direct ANSI/ISO support
    """
    
    def __init__(self):
        self.device = Device8002Integration()
    
    def capture_and_store_fingerprint(self, citizen_id: int, thumb_type: str) -> Dict:
        """
        Capture fingerprint from device and store in database
        
        Args:
            citizen_id (int): Citizen ID
            thumb_type (str): 'left' or 'right'
            
        Returns:
            Dict: Operation result
        """
        try:
            from citizens.models import Citizen, Biometric
            
            # Get citizen
            try:
                citizen = Citizen.objects.get(id=citizen_id)
            except Citizen.DoesNotExist:
                return {
                    'success': False,
                    'error': f'Citizen with ID {citizen_id} not found'
                }
            
            # Capture fingerprint from device
            template_data = self.device.capture_fingerprint(thumb_type)
            
            if not template_data:
                return {
                    'success': False,
                    'error': f'Failed to capture fingerprint: {self.device.last_error}'
                }
            
            # Validate ANSI template
            ansi_template = template_data.get('ansi_iso_template')
            if not self.device.validate_ansi_template(ansi_template):
                logger.warning("⚠️ ANSI template validation failed, but continuing...")
            
            # Encode template data as base64 JSON (our storage format)
            encoded_template = base64.b64encode(
                json.dumps(template_data).encode()
            ).decode()
            
            # Get or create biometric record
            biometric, created = Biometric.objects.get_or_create(
                citizen=citizen,
                defaults={}
            )
            
            # Store the fingerprint
            if thumb_type == 'left':
                biometric.left_thumb_fingerprint = encoded_template
            else:
                biometric.right_thumb_fingerprint = encoded_template
            
            biometric.save()
            
            logger.info(f"✅ Fingerprint stored for citizen {citizen_id} ({thumb_type} thumb)")
            
            return {
                'success': True,
                'message': f'Fingerprint captured and stored successfully',
                'template_size': len(encoded_template),
                'ansi_template_size': len(ansi_template) if ansi_template else 0,
                'quality_score': template_data.get('quality_metrics', {}).get('clarity', 0)
            }
            
        except Exception as e:
            logger.error(f"❌ Capture and store failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_stored_ansi_template(self, citizen_id: int, thumb_type: str) -> Optional[str]:
        """
        Get stored ANSI/ISO template for a citizen
        
        Args:
            citizen_id (int): Citizen ID
            thumb_type (str): 'left' or 'right'
            
        Returns:
            str: Base64 encoded ANSI template or None
        """
        try:
            from citizens.models import Citizen, Biometric
            
            citizen = Citizen.objects.get(id=citizen_id)
            biometric = Biometric.objects.get(citizen=citizen)
            
            # Get the stored template
            if thumb_type == 'left':
                stored_template = biometric.left_thumb_fingerprint
            else:
                stored_template = biometric.right_thumb_fingerprint
            
            if not stored_template:
                return None
            
            # Decode the stored template
            template_data = json.loads(base64.b64decode(stored_template).decode())
            
            # Return the ANSI template
            return template_data.get('ansi_iso_template')
            
        except Exception as e:
            logger.error(f"❌ Failed to get ANSI template: {e}")
            return None
    
    def compare_ansi_templates(self, template1: str, template2: str) -> float:
        """
        Compare two ANSI templates using FMatcher
        
        Args:
            template1 (str): Base64 encoded ANSI template
            template2 (str): Base64 encoded ANSI template
            
        Returns:
            float: Similarity score (0-1000+)
        """
        try:
            from .duplicate_detection_service import EnhancedDuplicateDetectionService
            
            service = EnhancedDuplicateDetectionService()
            
            # Decode templates
            template1_bytes = base64.b64decode(template1)
            template2_bytes = base64.b64decode(template2)
            
            # Use FMatcher for comparison
            score = service._compare_with_fmatcher(template1_bytes, template2_bytes)
            
            return score if score is not None else 0.0
            
        except Exception as e:
            logger.error(f"❌ Template comparison failed: {e}")
            return 0.0


# Global instances
device_8002 = Device8002Integration()
biometric_manager = BiometricDataManager()
