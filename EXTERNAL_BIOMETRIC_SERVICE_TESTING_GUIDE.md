# External Biometric Service Testing Guide

## Overview
This guide helps you test the external biometric service integration with the GoID system running in Docker containers.

## Prerequisites
✅ Futronic FS88H device connected via USB  
✅ Futronic SDK DLL files available on the testing PC  
✅ Python 3.8+ installed  
✅ Docker and Docker Compose installed  

## Step 1: Setup External Biometric Service

### 1.1 Install Python Dependencies
```bash
cd local-biometric-service
pip install -r requirements.txt
```

### 1.2 Copy DLL Files (if needed)
Copy the following files to the `local-biometric-service` directory:
- `ftrScanAPI.dll`
- `ftrMathAPI.dll`
- Any other Futronic SDK DLL files

### 1.3 Test Device Connection
```bash
python test_futronic_sdk.py
```
Expected output: Device detection and initialization success

## Step 2: Start External Biometric Service

### Option A: Using Batch Script
```bash
start_external_biometric_service.bat
```

### Option B: Manual Start
```bash
cd local-biometric-service
python working_fingerprint_service.py
```

### Expected Output
```
🚀 Starting Working Fingerprint Service
✅ Device ready for fingerprint capture
🌐 Service running at http://localhost:8001
👆 Ready to capture real fingerprints!
```

## Step 3: Verify Service Endpoints

### 3.1 Health Check
Open browser: `http://localhost:8001/api/health`

Expected response:
```json
{
  "status": "healthy",
  "service": "Working Fingerprint Service",
  "timestamp": "2024-...",
  "device_connected": true
}
```

### 3.2 Device Status
Open browser: `http://localhost:8001/api/device/status`

Expected response:
```json
{
  "success": true,
  "data": {
    "connected": true,
    "initialized": true,
    "handle": 123456,
    "model": "Futronic FS88H",
    "interface": "working_sdk"
  }
}
```

### 3.3 Web Interface Test
Open browser: `http://localhost:8001`
- Click "Capture Left Thumb" or "Capture Right Thumb"
- Follow device prompts to place finger
- Verify successful capture with quality score

## Step 4: Start Docker Services

### 4.1 Production Deployment
```bash
docker-compose -f docker-compose.production.yml up -d
```

### 4.2 Development Deployment
```bash
docker-compose up -d
```

## Step 5: Test Integration

### 5.1 Access GoID Frontend
Open browser: `http://localhost:3000`

### 5.2 Navigate to Citizen Registration
1. Login with appropriate credentials
2. Go to Citizens → Register New Citizen
3. Fill out required information until Biometric Capture step

### 5.3 Test Biometric Capture
1. On the Biometric Capture step, verify device status shows "Connected"
2. Click "Capture Left Thumb"
3. Follow device prompts to place finger on scanner
4. Verify successful capture and green checkmark
5. Click "Capture Right Thumb"
6. Repeat capture process
7. Verify both thumbs show as captured
8. Verify "Next" button becomes enabled

## Step 6: Troubleshooting

### 6.1 External Service Not Detected
**Symptoms:** Frontend shows "Device Status: Disconnected"

**Solutions:**
1. Verify external service is running on port 8001
2. Check Windows Firewall allows Python connections
3. Test service endpoints manually (Step 3)
4. Restart external service

### 6.2 Device Connection Issues
**Symptoms:** Service starts but device_connected: false

**Solutions:**
1. Check USB connection
2. Verify device drivers installed
3. Check Device Manager for Futronic device
4. Restart device and service

### 6.3 Capture Failures
**Symptoms:** Capture attempts fail or timeout

**Solutions:**
1. Clean fingerprint scanner surface
2. Ensure proper finger placement
3. Try different finger pressure
4. Check service logs for errors

### 6.4 Docker Container Access Issues
**Symptoms:** Frontend can't reach external service

**Solutions:**
1. Verify `extra_hosts` configuration in docker-compose
2. Test from container: `docker exec -it <container> curl http://host.docker.internal:8001/api/health`
3. Check network connectivity

## Step 7: Validation Checklist

- [ ] External biometric service starts successfully
- [ ] Device status shows connected and initialized
- [ ] Health check endpoint responds correctly
- [ ] Web interface can capture fingerprints
- [ ] Docker services start without errors
- [ ] Frontend detects external service automatically
- [ ] Left thumb capture works in citizen registration
- [ ] Right thumb capture works in citizen registration
- [ ] Both captures show quality scores
- [ ] Next button enables after both captures
- [ ] Citizen registration completes successfully

## Expected Console Logs

### Frontend Console (Success)
```
✅ Using external biometric service at http://localhost:8001
✅ Device initialized via external service
✅ left thumb captured via external service
✅ right thumb captured via external service
```

### Frontend Console (Fallback)
```
External biometric service not available at http://localhost:8001
✅ Using server biometric service
✅ Device initialized via server service
```

## Production Notes

1. **Firewall Configuration:** Ensure Windows Firewall allows Python to accept connections on port 8001
2. **Service Startup:** Consider setting up the external service to start automatically with Windows
3. **Multiple Workstations:** Each workstation with a fingerprint device should run its own external service
4. **Network Access:** External service only needs to be accessible from the local machine running the browser

## Support

If you encounter issues:
1. Check service logs in the external service console window
2. Verify device connection in Device Manager
3. Test endpoints manually using browser or curl
4. Check Docker container logs: `docker-compose logs frontend backend`
