# Data migration to fix Ethiopian dates with corrected conversion algorithm

from django.db import migrations
from django.apps import apps


def fix_ethiopian_dates(apps, schema_editor):
    """Fix Ethiopian dates with corrected conversion algorithm"""
    from common.ethiopian_calendar import gregorian_to_ethiopian, format_ethiopian_date
    
    Citizen = apps.get_model('citizens', 'Citizen')
    Child = apps.get_model('citizens', 'Child')
    
    # Fix citizen birth dates
    for citizen in Citizen.objects.filter(date_of_birth__isnull=False):
        try:
            ethiopian_date = gregorian_to_ethiopian(citizen.date_of_birth)
            if ethiopian_date:
                new_ethiopian_date_str = format_ethiopian_date(ethiopian_date)
                if citizen.date_of_birth_ethiopian != new_ethiopian_date_str:
                    citizen.date_of_birth_ethiopian = new_ethiopian_date_str
                    citizen.save(update_fields=['date_of_birth_ethiopian'])
                    print(f"Fixed citizen {citizen.id}: {citizen.date_of_birth} -> {citizen.date_of_birth_ethiopian}")
        except Exception as e:
            print(f"Error fixing citizen {citizen.id}: {e}")
    
    # Fix child birth dates
    for child in Child.objects.filter(date_of_birth__isnull=False):
        try:
            ethiopian_date = gregorian_to_ethiopian(child.date_of_birth)
            if ethiopian_date:
                new_ethiopian_date_str = format_ethiopian_date(ethiopian_date)
                if child.date_of_birth_ethiopian != new_ethiopian_date_str:
                    child.date_of_birth_ethiopian = new_ethiopian_date_str
                    child.save(update_fields=['date_of_birth_ethiopian'])
                    print(f"Fixed child {child.id}: {child.date_of_birth} -> {child.date_of_birth_ethiopian}")
        except Exception as e:
            print(f"Error fixing child {child.id}: {e}")


def reverse_fix(apps, schema_editor):
    """Reverse the fix by clearing Ethiopian dates"""
    Citizen = apps.get_model('citizens', 'Citizen')
    Child = apps.get_model('citizens', 'Child')
    
    Citizen.objects.update(date_of_birth_ethiopian=None)
    Child.objects.update(date_of_birth_ethiopian=None)


class Migration(migrations.Migration):

    dependencies = [
        ('citizens', '0004_merge_20250613_1757'),
    ]

    operations = [
        migrations.RunPython(fix_ethiopian_dates, reverse_fix),
    ]
