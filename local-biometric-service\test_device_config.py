#!/usr/bin/env python3
"""
Test device configuration and capture settings
"""

import ctypes
from ctypes import cdll, c_int, c_uint, c_void_p, POINTER, byref
import logging
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Constants
FTR_OK = 0
FTR_IMAGE_WIDTH = 320
FTR_IMAGE_HEIGHT = 480
FTR_IMAGE_SIZE = FTR_IMAGE_WIDTH * FTR_IMAGE_HEIGHT

def test_device_configuration():
    """Test device configuration and settings"""
    try:
        logger.info("🔧 Testing Futronic device configuration...")
        
        # Load scan API
        scan_api = cdll.LoadLibrary('./ftrScanAPI.dll')
        
        # Setup function signatures
        scan_api.ftrScanOpenDevice.restype = c_void_p
        scan_api.ftrScanCloseDevice.argtypes = [c_void_p]
        scan_api.ftrScanCloseDevice.restype = c_int
        scan_api.ftrScanGetImageSize.argtypes = [c_void_p, POIN<PERSON><PERSON>(c_int), POINTER(c_int)]
        scan_api.ftrScanGetImageSize.restype = c_int
        scan_api.ftrScanSetOptions.argtypes = [c_void_p, c_int]
        scan_api.ftrScanSetOptions.restype = c_int
        scan_api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(ctypes.c_ubyte), POINTER(c_uint)]
        scan_api.ftrScanGetFrame.restype = c_int
        
        # Step 1: Open device
        logger.info("📱 Opening device...")
        device_handle = scan_api.ftrScanOpenDevice()
        
        if not device_handle:
            logger.error("❌ Failed to open device")
            return False
        
        logger.info(f"✅ Device opened! Handle: {device_handle}")
        
        # Step 2: Get actual image size
        logger.info("📏 Getting device image size...")
        width = c_int()
        height = c_int()
        result = scan_api.ftrScanGetImageSize(device_handle, byref(width), byref(height))
        
        if result == FTR_OK:
            actual_width = width.value
            actual_height = height.value
            actual_size = actual_width * actual_height
            logger.info(f"✅ Device image size: {actual_width} x {actual_height} = {actual_size} bytes")
        else:
            logger.warning(f"⚠️ Could not get image size (error: {result})")
            actual_width = FTR_IMAGE_WIDTH
            actual_height = FTR_IMAGE_HEIGHT
            actual_size = FTR_IMAGE_SIZE
        
        # Step 3: Try different device options
        logger.info("🔧 Testing device options...")
        
        option_values = [0, 1, 2, 4, 8]  # Common option values
        for option in option_values:
            result = scan_api.ftrScanSetOptions(device_handle, option)
            if result == FTR_OK:
                logger.info(f"✅ Option {option} set successfully")
            else:
                logger.debug(f"⚠️ Option {option} failed (error: {result})")
        
        # Step 4: Test frame capture with correct size
        logger.info("📸 Testing frame capture with device-specific size...")
        
        # Use the actual device size
        image_buffer = (ctypes.c_ubyte * actual_size)()
        frame_size = c_uint(actual_size)
        
        logger.info("🧪 Attempting frame capture...")
        result = scan_api.ftrScanGetFrame(device_handle, image_buffer, byref(frame_size))
        
        logger.info(f"📊 Frame capture results:")
        logger.info(f"  Result code: {result}")
        logger.info(f"  Expected size: {actual_size}")
        logger.info(f"  Actual size: {frame_size.value}")
        
        if result == FTR_OK:
            # Analyze the captured data
            if frame_size.value > 0:
                sample_size = min(1000, frame_size.value)
                pixel_sum = sum(image_buffer[i] for i in range(sample_size))
                avg_pixel = pixel_sum / sample_size
                
                non_zero_pixels = sum(1 for i in range(sample_size) if image_buffer[i] > 5)
                data_percentage = (non_zero_pixels / sample_size) * 100
                
                logger.info(f"  Average pixel value: {avg_pixel:.2f}")
                logger.info(f"  Data percentage: {data_percentage:.1f}%")
                logger.info(f"  Has image data: {'Yes' if data_percentage > 5 else 'No'}")
                
                if data_percentage > 5:
                    logger.info("✅ Frame contains actual image data!")
                else:
                    logger.warning("⚠️ Frame appears to be empty")
            else:
                logger.warning("⚠️ No frame data captured")
        else:
            logger.error(f"❌ Frame capture failed with error: {result}")
        
        # Step 5: Test with finger placement prompt
        logger.info("\n👆 Now place your finger on the scanner and press Enter...")
        input("Press Enter when finger is placed...")
        
        logger.info("📸 Capturing with finger placed...")
        
        # Reset buffer
        image_buffer = (ctypes.c_ubyte * actual_size)()
        frame_size = c_uint(actual_size)
        
        result = scan_api.ftrScanGetFrame(device_handle, image_buffer, byref(frame_size))
        
        logger.info(f"📊 Frame capture with finger:")
        logger.info(f"  Result code: {result}")
        logger.info(f"  Frame size: {frame_size.value}")
        
        if result == FTR_OK and frame_size.value > 0:
            sample_size = min(1000, frame_size.value)
            pixel_sum = sum(image_buffer[i] for i in range(sample_size))
            avg_pixel = pixel_sum / sample_size
            
            non_zero_pixels = sum(1 for i in range(sample_size) if image_buffer[i] > 5)
            data_percentage = (non_zero_pixels / sample_size) * 100
            
            logger.info(f"  Average pixel value: {avg_pixel:.2f}")
            logger.info(f"  Data percentage: {data_percentage:.1f}%")
            
            if data_percentage > 20:
                logger.info("🎉 SUCCESS! Fingerprint data captured!")
            elif data_percentage > 5:
                logger.info("✅ Some image data captured - may need better finger placement")
            else:
                logger.warning("⚠️ Still no significant image data")
        
        # Step 6: Close device
        logger.info("🔒 Closing device...")
        close_result = scan_api.ftrScanCloseDevice(device_handle)
        
        if close_result == FTR_OK:
            logger.info("✅ Device closed successfully")
        else:
            logger.warning(f"⚠️ Device close returned: {close_result}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Device configuration test failed: {e}")
        return False

def main():
    """Main test function"""
    logger.info("🚀 Futronic Device Configuration Test")
    logger.info("=" * 40)
    
    success = test_device_configuration()
    
    logger.info("\n📊 Test Complete")
    logger.info("=" * 20)
    
    if success:
        logger.info("✅ Device configuration test completed")
        logger.info("💡 Check the results above to see if fingerprint capture is working")
    else:
        logger.info("❌ Device configuration test failed")
    
    return success

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
