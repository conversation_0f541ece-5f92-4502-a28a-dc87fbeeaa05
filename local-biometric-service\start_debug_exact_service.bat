@echo off
echo ========================================
echo   DEBUG EXACT Service
echo   EXACT Same Approach as Successful Debug
echo ========================================
echo.

echo 🔍 STRATEGY: Use the EXACT same approach that worked in debug
echo.
echo ✅ WHAT WORKED IN DEBUG:
echo    - ftrScanGetFrame returned 1 (SUCCESS)
echo    - Got 4782 bytes of real fingerprint data
echo    - No crashes with direct calls
echo    - Simple standalone script approach
echo.
echo 🎯 THIS SERVICE:
echo    - Uses EXACT same SDK loading sequence
echo    - Uses EXACT same function signatures
echo    - Uses EXACT same buffer approach (1KB)
echo    - Uses EXACT same calling sequence
echo    - NO Flask complexity - just basic HTTP server
echo.

echo Checking Python installation...
python --version
if errorlevel 1 (
    echo ERROR: Python not found. Please install Python 3.7+
    pause
    exit /b 1
)

echo.
echo Checking required DLL files...
if not exist "ftrScanAPI.dll" (
    echo ERROR: ftrScanAPI.dll not found
    echo Please ensure Futronic SDK files are in the current directory
    pause
    exit /b 1
)

echo SUCCESS: ftrScanAPI.dll found

echo.
echo ========================================
echo   DEBUG EXACT APPROACH
echo ========================================
echo.
echo This service replicates the EXACT debug script that worked:
echo.
echo 1. Load SDK exactly like debug
echo 2. Configure signatures exactly like debug  
echo 3. Initialize device exactly like debug
echo 4. Call ftrScanGetFrame exactly like debug
echo 5. Process results exactly like debug
echo.
echo NO Flask, NO threading, NO complexity
echo Just the EXACT working approach!
echo.

echo Starting DEBUG EXACT Service...
echo Service will be available at: http://localhost:8001
echo.
echo This should work because it's IDENTICAL to the successful debug!
echo.
echo Press Ctrl+C to stop the service
echo.

python debug_exact_service.py

echo.
echo Service stopped.
pause
