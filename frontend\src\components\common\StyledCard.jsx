import { useState } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON>er, 
  Card<PERSON>ontent, 
  CardActions, 
  Typography, 
  Box, 
  IconButton, 
  Divider,
  useTheme,
  alpha
} from '@mui/material';
import { MoreVert as MoreIcon } from '@mui/icons-material';

/**
 * A styled card component with hover effects and consistent styling
 * 
 * @param {Object} props
 * @param {React.ReactNode} props.title - Card title
 * @param {React.ReactNode} props.subtitle - Card subtitle
 * @param {React.ReactNode} props.icon - Icon to display in the header
 * @param {React.ReactNode} props.action - Action component to display in the header
 * @param {React.ReactNode} props.children - Card content
 * @param {React.ReactNode} props.footer - Card footer content
 * @param {string} props.color - Primary color for the card (primary, secondary, success, error, warning, info)
 * @param {boolean} props.elevation - Whether to show elevation on hover
 * @param {Object} props.sx - Additional styles to apply to the card
 */
const StyledCard = ({ 
  title, 
  subtitle, 
  icon, 
  action, 
  children, 
  footer,
  color = 'primary',
  elevation = true,
  sx = {},
  ...rest
}) => {
  const theme = useTheme();
  const [hovered, setHovered] = useState(false);
  
  // Get the color from theme
  const getColor = () => {
    if (!color) return null;
    return theme.palette[color]?.main || theme.palette.primary.main;
  };
  
  const cardColor = getColor();
  
  return (
    <Card
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        borderRadius: 2,
        overflow: 'hidden',
        transition: 'all 0.3s ease',
        border: `1px solid ${theme.palette.mode === 'light' 
          ? 'rgba(0, 0, 0, 0.05)' 
          : 'rgba(255, 255, 255, 0.05)'
        }`,
        boxShadow: elevation 
          ? hovered 
            ? theme.shadows[8] 
            : theme.shadows[2]
          : 'none',
        transform: elevation && hovered ? 'translateY(-4px)' : 'none',
        '&:hover': {
          borderColor: elevation ? alpha(cardColor, 0.2) : 'inherit',
        },
        ...sx
      }}
      {...rest}
    >
      {(title || subtitle || icon) && (
        <>
          <CardHeader
            avatar={icon && (
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 40,
                  height: 40,
                  borderRadius: '50%',
                  backgroundColor: alpha(cardColor, 0.1),
                  color: cardColor,
                }}
              >
                {icon}
              </Box>
            )}
            title={
              <Typography variant="h6" component="h2" fontWeight={600}>
                {title}
              </Typography>
            }
            subheader={subtitle && (
              <Typography variant="body2" color="text.secondary">
                {subtitle}
              </Typography>
            )}
            action={action || (
              <IconButton aria-label="settings" size="small">
                <MoreIcon />
              </IconButton>
            )}
            sx={{
              '& .MuiCardHeader-action': {
                margin: 0,
              },
            }}
          />
          <Divider />
        </>
      )}
      
      <CardContent sx={{ flexGrow: 1, pt: (title || subtitle || icon) ? 2 : 3 }}>
        {children}
      </CardContent>
      
      {footer && (
        <>
          <Divider />
          <CardActions sx={{ p: 2 }}>
            {footer}
          </CardActions>
        </>
      )}
    </Card>
  );
};

export default StyledCard;
