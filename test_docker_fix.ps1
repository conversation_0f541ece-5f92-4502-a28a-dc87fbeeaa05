Write-Host "🚀 Testing Docker Fix for Citizen Clearance Issue" -ForegroundColor Green

Write-Host "📋 Step 1: Stopping existing containers..." -ForegroundColor Yellow
docker-compose down -v

Write-Host "📋 Step 2: Rebuilding and starting containers..." -ForegroundColor Yellow
docker-compose up -d --build

Write-Host "📋 Step 3: Waiting for containers to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

Write-Host "📋 Step 4: Checking container status..." -ForegroundColor Yellow
docker-compose ps

Write-Host "📋 Step 5: Checking backend logs..." -ForegroundColor Yellow
docker-compose logs backend | Select-Object -Last 20

Write-Host "📋 Step 6: Testing if backend is responding..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000" -TimeoutSec 10
    Write-Host "✅ Backend is responding! Status: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "❌ Backend is not responding: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "📋 Step 7: Testing citizen clearance table..." -ForegroundColor Yellow
docker exec goid-backend-1 python manage.py shell -c "
from workflows.models import CitizenClearanceRequest
from django_tenants.utils import schema_context
from tenants.models import Tenant

try:
    # Test model import
    print('✅ CitizenClearanceRequest model imported successfully')
    
    # Test in tenant schemas
    tenants = Tenant.objects.filter(schema_name__in=['kebele_bole_01'])
    for tenant in tenants:
        with schema_context(tenant.schema_name):
            count = CitizenClearanceRequest.objects.count()
            print(f'✅ citizen_clearance_requests table exists in {tenant.schema_name} with {count} records')
            
    print('🎉 All tests passed!')
except Exception as e:
    print(f'❌ Error: {e}')
"

Write-Host "🏁 Test completed!" -ForegroundColor Green
