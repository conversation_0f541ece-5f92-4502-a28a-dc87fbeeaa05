<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#FFD700"/>
      <stop offset="50%" stop-color="#FFB700"/>
      <stop offset="100%" stop-color="#FF8F00"/>
    </linearGradient>
  </defs>
  <!-- Outer circle -->
  <circle cx="50" cy="50" r="48" fill="url(#goldGradient)" stroke="#00528C" stroke-width="2"/>
  <!-- Inner circle -->
  <circle cx="50" cy="50" r="35" fill="none" stroke="#00528C" stroke-width="2"/>
  <!-- Center star -->
  <polygon points="50,20 55,28 65,28 57,35 60,45 50,40 40,45 43,35 35,28 45,28" fill="#00528C"/>
  <!-- Ethiopian cross -->
  <rect x="47" y="60" width="6" height="20" fill="#00528C"/>
  <rect x="40" y="67" width="20" height="6" fill="#00528C"/>
  <!-- Text -->
  <text x="50" y="90" font-family="Arial, sans-serif" font-size="8" fill="#00528C" text-anchor="middle" font-weight="bold">GONDAR CITY</text>
</svg>
