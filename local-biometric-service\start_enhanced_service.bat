@echo off
echo ========================================
echo    ENHANCED DETECTION BIOMETRIC SERVICE
echo ========================================
echo.
echo 🔍 ENHANCED DETECTION FEATURES:
echo   ✅ Multiple detection methods
echo   ✅ Finger presence function (if available)
echo   ✅ Threaded frame capture (crash-safe)
echo   ✅ Simulation fallback (guaranteed working)
echo   ✅ 3-second timeout for no finger
echo.
echo 🎯 DETECTION METHODS (in order of preference):
echo   1. finger_presence - Uses SDK finger presence function
echo   2. threaded_frame - Safe frame capture in separate thread
echo   3. simulation - Time-based fallback (always works)
echo.
echo 📱 SERVICE URL: http://localhost:8001
echo.
echo 🧪 TESTING:
echo   - This service will try different methods to detect finger
echo   - Should be able to distinguish between finger/no finger
echo   - If real detection fails, falls back to simulation
echo   - Never crashes regardless of detection method
echo.
echo 💡 GOAL: Find a detection method that works with your device
echo    while maintaining stability for production use.
echo.

echo 🚀 Starting enhanced detection biometric service...
echo.
echo 🔍 Testing multiple detection methods...
echo 📋 Keep this window open while testing
echo.

python enhanced_detection_service.py

echo.
echo Service stopped.
pause
