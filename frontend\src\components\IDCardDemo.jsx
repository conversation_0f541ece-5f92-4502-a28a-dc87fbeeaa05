import React, { useState } from 'react';
import {
  Box,
  Typo<PERSON>,
  Card,
  Card<PERSON>ontent,
  But<PERSON>,
  ToggleButton,
  ToggleButtonGroup,
  Container,
  Grid,
  Paper
} from '@mui/material';
import {
  CreditCard as FrontIcon,
  CreditCardOff as BackIcon
} from '@mui/icons-material';
import IDCardTemplate from './idcards/IDCardTemplate';

const IDCardDemo = () => {
  const [side, setSide] = useState('front');

  // Sample ID card data matching the Ethiopian Digital ID Card format
  const sampleIdCard = {
    card_number: '9572431481361091', // Using digital ID as card number
    citizen_digital_id: '9572431481361091',
    uuid: 'sample-uuid-123',
    status: 'approved',
    issue_date: '2024-01-15',
    expiry_date: '2034-01-15',
    has_kebele_pattern: true,
    has_subcity_pattern: true,
    citizen: {
      first_name: '<PERSON><PERSON><PERSON><PERSON>',
      middle_name: '<PERSON><PERSON><PERSON>',
      last_name: '<PERSON><PERSON><PERSON>',
      first_name_am: 'ተዎድሮስ',
      middle_name_am: 'አበበው',
      last_name_am: 'ቸኮል',
      gender: 'male',
      date_of_birth: '1988-05-01',
      date_of_birth_ethiopian: '23/08/1980',
      digital_id: '9572431481361091',
      nationality: 'Ethiopian',
      marital_status: 'Single',
      religion: 'Orthodox',
      address: 'Kebele 01, Subcity Zone',
      emergency_contact_phone: '+251-911-123456',
      photo: null // Using placeholder for demo
    },
    citizen_photo: null, // Using placeholder for demo
    tenant_hierarchy: {
      current_tenant: 'ገብርኤል ቀበሌ',
      parent_tenant: 'ዞብል ክ/ከተማ',
      city_tenant: 'ጎንደር ከተማ'
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ textAlign: 'center', mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>
          🇪🇹 Enhanced Ethiopian Digital ID Card
        </Typography>
        <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
          Amhara Regional State - Gondar City Administration - Standard Size (3.375" x 2.125")
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ fontSize: '14px', lineHeight: 1.4 }}>
          በአማራ ብሔራዊ ክልላዊ መንግሥት በጎንደር ከተማ አስተዳደር በዞብል ክ/ከተማ<br/>
          የገብርኤል ቀበሌ አስተዳደር ጽ/ቤት የነዋሪዎች መታወቂያ ካርድ<br/>
          <em style={{ fontSize: '12px', color: '#666' }}>Token-based city info, 2x logo, database fields, 3x QR code</em>
        </Typography>
      </Box>

      <Grid container spacing={4}>
        {/* ID Card Display */}
        <Grid item xs={12} md={8}>
          <Card elevation={3}>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h5" component="h2" sx={{ fontWeight: 'bold' }}>
                  ID Card Preview
                </Typography>
                
                {/* Side Toggle */}
                <ToggleButtonGroup
                  value={side}
                  exclusive
                  onChange={(event, newSide) => {
                    if (newSide !== null) {
                      setSide(newSide);
                    }
                  }}
                  aria-label="card side"
                >
                  <ToggleButton value="front" aria-label="front side">
                    <FrontIcon sx={{ mr: 1 }} />
                    Front
                  </ToggleButton>
                  <ToggleButton value="back" aria-label="back side">
                    <BackIcon sx={{ mr: 1 }} />
                    Back
                  </ToggleButton>
                </ToggleButtonGroup>
              </Box>

              {/* ID Card Display */}
              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
                <Paper 
                  elevation={4}
                  sx={{ 
                    p: 3, 
                    backgroundColor: '#f8f9fa',
                    borderRadius: 2
                  }}
                >
                  <IDCardTemplate 
                    idCard={sampleIdCard} 
                    side={side} 
                    preview={true}
                  />
                </Paper>
              </Box>

              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="body2" color="text.secondary">
                  Click the toggle buttons above to switch between front and back sides
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Features List */}
        <Grid item xs={12} md={4}>
          <Card elevation={2}>
            <CardContent>
              <Typography variant="h6" component="h3" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                ✨ Enhanced Features
              </Typography>
              
              <Box component="ul" sx={{ pl: 2, '& li': { mb: 1 } }}>
                <li>
                  <Typography variant="body2">
                    <strong>🏛️ Gondar Logo:</strong> Official city logo from public/images folder
                  </Typography>
                </li>
                <li>
                  <Typography variant="body2">
                    <strong>📏 Exact Standard Dimensions:</strong> 3.375" x 2.125" (exact measurements)
                  </Typography>
                </li>
                <li>
                  <Typography variant="body2">
                    <strong>🔤 Bilingual Text:</strong> Amharic and English labels
                  </Typography>
                </li>
                <li>
                  <Typography variant="body2">
                    <strong>📸 Large Photo:</strong> 1.5x sized borderless photo with dimensions
                  </Typography>
                </li>
                <li>
                  <Typography variant="body2">
                    <strong>📊 Borderless Barcode:</strong> Natural barcode pattern without borders
                  </Typography>
                </li>
                <li>
                  <Typography variant="body2">
                    <strong>✍️ Footer Signature:</strong> Large signature positioned above footer
                  </Typography>
                </li>
                <li>
                  <Typography variant="body2">
                    <strong>🎨 Professional Layout:</strong> Clean design with single-line footer
                  </Typography>
                </li>
                <li>
                  <Typography variant="body2">
                    <strong>🔒 Security Elements:</strong> Transparent patterns and watermarks
                  </Typography>
                </li>
                <li>
                  <Typography variant="body2">
                    <strong>📊 Database Integration:</strong> Uses actual citizen data with fallbacks
                  </Typography>
                </li>
                <li>
                  <Typography variant="body2">
                    <strong>📱 Responsive Design:</strong> Scales for different screens
                  </Typography>
                </li>
              </Box>

              <Box sx={{ mt: 3, p: 2, backgroundColor: 'success.light', borderRadius: 1 }}>
                <Typography variant="body2" sx={{ fontWeight: 'bold', color: 'success.dark' }}>
                  🎯 Production Ready
                </Typography>
                <Typography variant="caption" color="success.dark">
                  This template is optimized for printing and digital display, 
                  meeting international ID card standards.
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Technical Details */}
      <Card elevation={1} sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" component="h3" gutterBottom sx={{ fontWeight: 'bold' }}>
            🔧 Technical Implementation
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 'bold' }}>
                Design Elements:
              </Typography>
              <Box component="ul" sx={{ pl: 2, '& li': { mb: 0.5 } }}>
                <li><Typography variant="body2">Compact 2-line Amhara Regional State header</Typography></li>
                <li><Typography variant="body2">Large Gondar logo (2x size) from public/images folder</Typography></li>
                <li><Typography variant="body2">Single-line footer with dates (ISSUE/EXPIRY | የተሰጠበት/የሚያበቃበት: dates)</Typography></li>
                <li><Typography variant="body2">Centered photo and content layout</Typography></li>
                <li><Typography variant="body2">Bilingual field labels (Amharic/English)</Typography></li>
                <li><Typography variant="body2">Clean design without vertical bars</Typography></li>
              </Box>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 'bold' }}>
                Data Fields:
              </Typography>
              <Box component="ul" sx={{ pl: 2, '& li': { mb: 0.5 } }}>
                <li><Typography variant="body2">Full name (Amharic & English from database)</Typography></li>
                <li><Typography variant="body2">Date of birth (Ethiopian & Gregorian from database)</Typography></li>
                <li><Typography variant="body2">Gender and nationality (from database)</Typography></li>
                <li><Typography variant="body2">Digital ID number (from database with fallbacks)</Typography></li>
                <li><Typography variant="body2">Large citizen photo (1.5x size with updated dimensions)</Typography></li>
                <li><Typography variant="body2">Borderless barcode pattern (matches photo width)</Typography></li>
                <li><Typography variant="body2">Signature positioned above footer (right side)</Typography></li>
                <li><Typography variant="body2">Back side: Actual citizen.phone, emergency_contacts array</Typography></li>
                <li><Typography variant="body2">Back side: citizen.blood_type, tenantHierarchy data</Typography></li>
                <li><Typography variant="body2">Back side: citizen.employment with proper fallbacks</Typography></li>
                <li><Typography variant="body2">Large QR code (3x size) with database verification data</Typography></li>
                <li><Typography variant="body2">Issue/Expiry dates (from database with defaults)</Typography></li>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Container>
  );
};

export default IDCardDemo;
