import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import axios from '../../utils/axios';
import {
  Box,
  Button,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  CircularProgress,
  Alert,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Paper,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  ArrowBack as BackIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Business as BusinessIcon,
  LocationCity as CityIcon,
  Apartment as SubcityIcon,
  Home as KebeleIcon,
  Domain as DomainIcon,
  People as PeopleIcon,
  AccountTree as HierarchyIcon,
  CalendarToday as CalendarIcon,
  Schema as SchemaIcon,
} from '@mui/icons-material';

const TenantDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [tenant, setTenant] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchTenantDetails();
  }, [id]);

  const fetchTenantDetails = async () => {
    try {
      setLoading(true);
      setError('');
      const response = await axios.get(`/api/tenants/${id}/`);
      setTenant(response.data);
    } catch (error) {
      console.error('Failed to fetch tenant details:', error);
      setError('Failed to load tenant details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'city':
        return <CityIcon />;
      case 'subcity':
        return <SubcityIcon />;
      case 'kebele':
        return <KebeleIcon />;
      default:
        return <BusinessIcon />;
    }
  };

  const getTypeChip = (type) => {
    const colors = {
      city: 'primary',
      subcity: 'secondary',
      kebele: 'success',
    };

    return (
      <Chip
        icon={getTypeIcon(type)}
        label={type.charAt(0).toUpperCase() + type.slice(1)}
        color={colors[type] || 'default'}
        variant="outlined"
      />
    );
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
        <Button
          variant="outlined"
          startIcon={<BackIcon />}
          onClick={() => navigate('/tenants')}
        >
          Back to Tenants
        </Button>
      </Box>
    );
  }

  if (!tenant) {
    return (
      <Box>
        <Alert severity="warning" sx={{ mb: 3 }}>
          Tenant not found.
        </Alert>
        <Button
          variant="outlined"
          startIcon={<BackIcon />}
          onClick={() => navigate('/tenants')}
        >
          Back to Tenants
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<BackIcon />}
            onClick={() => navigate('/tenants')}
          >
            Back
          </Button>
          <Typography variant="h4" component="h1">
            {tenant.name}
          </Typography>
          {getTypeChip(tenant.type)}
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title="Edit Tenant">
            <IconButton
              color="primary"
              onClick={() => navigate(`/tenants/${tenant.id}/edit`)}
            >
              <EditIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Delete Tenant">
            <IconButton
              color="error"
              onClick={() => {
                // This could trigger a delete dialog
                console.log('Delete tenant:', tenant.id);
              }}
            >
              <DeleteIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Basic Information */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <BusinessIcon />
                Basic Information
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <List>
                <ListItem>
                  <ListItemIcon>
                    <BusinessIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Name"
                    secondary={tenant.name}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    {getTypeIcon(tenant.type)}
                  </ListItemIcon>
                  <ListItemText
                    primary="Type"
                    secondary={tenant.type.charAt(0).toUpperCase() + tenant.type.slice(1)}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <SchemaIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Schema Name"
                    secondary={tenant.schema_name}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <CalendarIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Created On"
                    secondary={new Date(tenant.created_on).toLocaleString()}
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Statistics */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <PeopleIcon />
                Statistics
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <List>
                <ListItem>
                  <ListItemIcon>
                    <PeopleIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Users"
                    secondary={`${tenant.users_count || 0} users`}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <HierarchyIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Child Tenants"
                    secondary={`${tenant.children_count || 0} children`}
                  />
                </ListItem>
                {tenant.parent_name && (
                  <ListItem>
                    <ListItemIcon>
                      <HierarchyIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Parent Tenant"
                      secondary={tenant.parent_name}
                    />
                  </ListItem>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Domains */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <DomainIcon />
                Domains
              </Typography>
              <Divider sx={{ mb: 2 }} />
              {tenant.domains && tenant.domains.length > 0 ? (
                <Grid container spacing={2}>
                  {tenant.domains.map((domain) => (
                    <Grid item xs={12} sm={6} md={4} key={domain.id}>
                      <Paper
                        elevation={1}
                        sx={{
                          p: 2,
                          border: domain.is_primary ? '2px solid' : '1px solid',
                          borderColor: domain.is_primary ? 'primary.main' : 'divider',
                        }}
                      >
                        <Typography variant="subtitle1" fontWeight="bold">
                          {domain.domain}
                        </Typography>
                        <Chip
                          label={domain.is_primary ? 'Primary' : 'Secondary'}
                          color={domain.is_primary ? 'primary' : 'default'}
                          size="small"
                          sx={{ mt: 1 }}
                        />
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              ) : (
                <Typography color="text.secondary">
                  No domains configured for this tenant.
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default TenantDetails;
