#!/usr/bin/env python3
"""
Test duplicate detection with actual database data.
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from biometrics.fingerprint_processing import FingerprintProcessor
from tenants.models.citizen import Citizen, Biometric
from django_tenants.utils import schema_context, get_tenant_model
import base64
import json

def test_duplicate_detection_with_data():
    """Test duplicate detection with actual database data"""
    
    # Real template from database
    template_b64 = "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"
    
    print("🔍 Testing Duplicate Detection with Database Data")
    print("=" * 60)
    
    processor = FingerprintProcessor()
    
    # First, check what's in the database
    print("\n📊 DATABASE STATUS:")
    print("-" * 40)
    
    Tenant = get_tenant_model()
    tenants = Tenant.objects.filter(type='kebele')
    print(f"Found {tenants.count()} kebele tenants")
    
    total_citizens = 0
    total_biometrics = 0
    
    for tenant in tenants:
        try:
            with schema_context(tenant.schema_name):
                citizen_count = Citizen.objects.count()
                biometric_count = Biometric.objects.count()
                total_citizens += citizen_count
                total_biometrics += biometric_count
                print(f"  {tenant.name}: {citizen_count} citizens, {biometric_count} biometrics")
        except Exception as e:
            print(f"  {tenant.name}: Error accessing - {e}")
    
    print(f"Total: {total_citizens} citizens, {total_biometrics} biometrics")
    
    if total_biometrics == 0:
        print("\n⚠️  No biometric data found in database!")
        print("Creating test data for duplicate detection...")
        
        # Create test data in the first kebele tenant
        if tenants.exists():
            test_tenant = tenants.first()
            with schema_context(test_tenant.schema_name):
                # Get or create a test citizen
                test_citizen, created = Citizen.objects.get_or_create(
                    digital_id="TEST-DUPLICATE-001",
                    defaults={
                        'first_name': "Test",
                        'last_name': "Citizen",
                        'date_of_birth': "1990-01-01",
                        'gender': "M",
                        'phone': "0911234567"
                    }
                )

                # Get or create biometric data with our test template
                test_biometric, bio_created = Biometric.objects.get_or_create(
                    citizen=test_citizen,
                    defaults={
                        'left_thumb_fingerprint': template_b64,
                        'right_thumb_fingerprint': template_b64
                    }
                )

                if created:
                    print(f"✅ Created test citizen: {test_citizen.get_full_name()}")
                else:
                    print(f"✅ Using existing test citizen: {test_citizen.get_full_name()}")

                if bio_created:
                    print(f"✅ Created test biometric: ID {test_biometric.id}")
                else:
                    print(f"✅ Using existing test biometric: ID {test_biometric.id}")
                    # Update with our test template
                    test_biometric.left_thumb_fingerprint = template_b64
                    test_biometric.right_thumb_fingerprint = template_b64
                    test_biometric.save()
                    print(f"✅ Updated biometric with test template")
    
    # Now test duplicate detection
    print("\n🔍 TESTING DUPLICATE DETECTION:")
    print("-" * 40)
    
    # Test 1: Exact duplicate
    print("\n🔍 TEST 1: Exact Duplicate Detection")
    templates = {
        'left_thumb_fingerprint': template_b64,
        'right_thumb_fingerprint': template_b64
    }
    
    result = processor.check_duplicates(templates)
    print(f"Result: {result}")
    
    # Test 2: Similar fingerprint
    print("\n🔍 TEST 2: Similar Fingerprint Detection")
    
    # Create slightly modified template
    decoded = json.loads(base64.b64decode(template_b64))
    modified_template = decoded.copy()
    modified_template['minutiae_points'] = decoded['minutiae_points'].copy()
    
    # Modify first 3 points slightly
    for i in range(3):
        point = modified_template['minutiae_points'][i].copy()
        point['x'] += 2
        point['y'] += 1
        point['angle'] += 5
        modified_template['minutiae_points'][i] = point
    
    modified_template_b64 = base64.b64encode(json.dumps(modified_template).encode()).decode()
    
    templates_similar = {
        'left_thumb_fingerprint': template_b64,
        'right_thumb_fingerprint': modified_template_b64
    }
    
    result2 = processor.check_duplicates(templates_similar)
    print(f"Result: {result2}")
    
    # Summary
    print("\n✅ SUMMARY:")
    exact_duplicate_detected = result.get('has_duplicates', False)
    similar_duplicate_detected = result2.get('has_duplicates', False)
    
    print(f"Exact duplicate detected: {'PASS' if exact_duplicate_detected else 'FAIL'}")
    print(f"Similar fingerprint detected: {'PASS' if similar_duplicate_detected else 'FAIL'}")
    
    if exact_duplicate_detected or similar_duplicate_detected:
        print("\n🎉 DUPLICATE DETECTION IS WORKING!")
        print("The system successfully identifies matching fingerprints.")
        
        if exact_duplicate_detected:
            print(f"Found {result['total_matches']} exact matches")
            for match in result['matches']:
                print(f"  - {match['citizen_name']} in {match['tenant_name']} (Score: {match['match_score']:.3f})")
        
        if similar_duplicate_detected:
            print(f"Found {result2['total_matches']} similar matches")
            for match in result2['matches']:
                print(f"  - {match['citizen_name']} in {match['tenant_name']} (Score: {match['match_score']:.3f})")
    else:
        print("\n❌ Duplicate detection may not be working as expected.")
        print("Check the matching algorithm and database data.")

if __name__ == "__main__":
    test_duplicate_detection_with_data()
