#!/usr/bin/env python3
"""
Build script for GoID Biometric Service
Creates standalone executable package for deployment
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def install_requirements():
    """Install required packages for building"""
    packages = [
        'pyinstaller',
        'pystray',
        'Pillow',
        'flask',
        'flask-cors'
    ]
    
    print("Installing build requirements...")
    for package in packages:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ {package} installed")
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install {package}")
            return False
    return True

def create_spec_file():
    """Create PyInstaller spec file"""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['tray_service.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('improved_biometric_service.py', '.'),
        ('ftrScanAPI.dll', '.'),
        ('ftrMathAPI.dll', '.'),
        ('*.bat', '.'),
    ],
    hiddenimports=[
        'pystray._win32',
        'PIL._tkinter_finder',
        'flask',
        'flask_cors',
        'ctypes',
        'multiprocessing'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='GoID_Biometric_Service',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # No console window
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
)
'''
    
    with open('goid_biometric.spec', 'w') as f:
        f.write(spec_content.strip())
    
    print("✅ PyInstaller spec file created")

def create_icon():
    """Create application icon"""
    try:
        from PIL import Image, ImageDraw
        
        # Create a simple icon
        image = Image.new('RGB', (256, 256), color='white')
        draw = ImageDraw.Draw(image)
        
        # Draw fingerprint-like pattern
        center = 128
        for i in range(5):
            radius = 30 + i * 20
            draw.ellipse([center-radius, center-radius, center+radius, center+radius], 
                        outline=(0, 100, 200), width=8)
        
        # Save as ICO
        image.save('icon.ico', format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])
        print("✅ Application icon created")
        return True
    except Exception as e:
        print(f"⚠️ Could not create icon: {e}")
        return False

def build_executable():
    """Build the executable using PyInstaller"""
    try:
        print("Building executable with PyInstaller...")
        
        # Run PyInstaller
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--onefile",
            "--windowed",
            "--name", "GoID_Biometric_Service",
            "goid_biometric.spec"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Executable built successfully")
            return True
        else:
            print(f"❌ Build failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Build error: {e}")
        return False

def create_installer():
    """Create installation package"""
    try:
        # Create deployment folder
        deploy_dir = Path("deployment")
        deploy_dir.mkdir(exist_ok=True)
        
        # Copy executable
        exe_path = Path("dist/GoID_Biometric_Service.exe")
        if exe_path.exists():
            shutil.copy2(exe_path, deploy_dir / "GoID_Biometric_Service.exe")
            print("✅ Executable copied to deployment folder")
        
        # Copy DLL files
        dll_files = ["ftrScanAPI.dll", "ftrMathAPI.dll"]
        for dll in dll_files:
            if Path(dll).exists():
                shutil.copy2(dll, deploy_dir / dll)
                print(f"✅ {dll} copied")
        
        # Create installation script
        install_script = '''@echo off
echo ========================================
echo   GoID Biometric Service Installer
echo ========================================
echo.
echo Installing GoID Biometric Service...
echo.

REM Create installation directory
set INSTALL_DIR=%PROGRAMFILES%\\GoID\\BiometricService
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM Copy files
copy "GoID_Biometric_Service.exe" "%INSTALL_DIR%\\"
copy "ftrScanAPI.dll" "%INSTALL_DIR%\\"
copy "ftrMathAPI.dll" "%INSTALL_DIR%\\"

REM Create desktop shortcut
set DESKTOP=%USERPROFILE%\\Desktop
echo [InternetShortcut] > "%DESKTOP%\\GoID Biometric Service.url"
echo URL=file:///%INSTALL_DIR%\\GoID_Biometric_Service.exe >> "%DESKTOP%\\GoID Biometric Service.url"
echo IconFile=%INSTALL_DIR%\\GoID_Biometric_Service.exe >> "%DESKTOP%\\GoID Biometric Service.url"
echo IconIndex=0 >> "%DESKTOP%\\GoID Biometric Service.url"

REM Create start menu entry
set STARTMENU=%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs
if not exist "%STARTMENU%\\GoID" mkdir "%STARTMENU%\\GoID"
echo [InternetShortcut] > "%STARTMENU%\\GoID\\GoID Biometric Service.url"
echo URL=file:///%INSTALL_DIR%\\GoID_Biometric_Service.exe >> "%STARTMENU%\\GoID\\GoID Biometric Service.url"
echo IconFile=%INSTALL_DIR%\\GoID_Biometric_Service.exe >> "%STARTMENU%\\GoID\\GoID Biometric Service.url"
echo IconIndex=0 >> "%STARTMENU%\\GoID\\GoID Biometric Service.url"

REM Add to startup (optional)
echo Would you like to start the service automatically with Windows? (Y/N)
set /p STARTUP_CHOICE=
if /i "%STARTUP_CHOICE%"=="Y" (
    reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run" /v "GoID_Biometric" /t REG_SZ /d "%INSTALL_DIR%\\GoID_Biometric_Service.exe" /f
    echo ✅ Added to Windows startup
)

echo.
echo ========================================
echo   Installation Complete!
echo ========================================
echo.
echo The GoID Biometric Service has been installed to:
echo %INSTALL_DIR%
echo.
echo You can start it from:
echo - Desktop shortcut
echo - Start Menu ^> GoID ^> GoID Biometric Service
echo - Or run directly: %INSTALL_DIR%\\GoID_Biometric_Service.exe
echo.
echo The service will appear in your system tray when running.
echo.
pause
'''
        
        with open(deploy_dir / "install.bat", 'w') as f:
            f.write(install_script)
        
        # Create uninstaller
        uninstall_script = '''@echo off
echo ========================================
echo   GoID Biometric Service Uninstaller
echo ========================================
echo.
echo Removing GoID Biometric Service...
echo.

set INSTALL_DIR=%PROGRAMFILES%\\GoID\\BiometricService

REM Stop service if running
taskkill /f /im "GoID_Biometric_Service.exe" 2>nul

REM Remove from startup
reg delete "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run" /v "GoID_Biometric" /f 2>nul

REM Remove shortcuts
del "%USERPROFILE%\\Desktop\\GoID Biometric Service.url" 2>nul
rmdir /s /q "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\GoID" 2>nul

REM Remove installation directory
rmdir /s /q "%INSTALL_DIR%" 2>nul

echo.
echo ✅ GoID Biometric Service has been removed.
echo.
pause
'''
        
        with open(deploy_dir / "uninstall.bat", 'w') as f:
            f.write(uninstall_script)
        
        # Create README
        readme_content = '''# GoID Biometric Service - Deployment Package

## Installation

1. Run `install.bat` as Administrator
2. Follow the installation prompts
3. The service will be installed and can be started from the system tray

## Features

- System tray application
- Auto-restart on crash
- Web interface at http://localhost:8001
- Professional biometric capture
- Multi-capture resistant architecture

## Usage

- Right-click the tray icon for options
- Left-click to show status
- Access web interface from tray menu
- Service automatically monitors and restarts if needed

## Uninstallation

Run `uninstall.bat` as Administrator to completely remove the service.

## Support

For technical support, check the logs folder in the installation directory.
'''
        
        with open(deploy_dir / "README.txt", 'w') as f:
            f.write(readme_content)
        
        print("✅ Installation package created in 'deployment' folder")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create installer: {e}")
        return False

def main():
    """Main build process"""
    print("GoID Biometric Service - Build Script")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("improved_biometric_service.py").exists():
        print("❌ Please run this script from the local-biometric-service directory")
        sys.exit(1)
    
    # Install requirements
    if not install_requirements():
        print("❌ Failed to install requirements")
        sys.exit(1)
    
    # Create icon
    create_icon()
    
    # Create spec file
    create_spec_file()
    
    # Build executable
    if not build_executable():
        print("❌ Build failed")
        sys.exit(1)
    
    # Create installer
    if not create_installer():
        print("❌ Failed to create installer")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("✅ BUILD COMPLETE!")
    print("=" * 50)
    print("\nDeployment package created in 'deployment' folder:")
    print("- GoID_Biometric_Service.exe (standalone executable)")
    print("- install.bat (installation script)")
    print("- uninstall.bat (removal script)")
    print("- README.txt (documentation)")
    print("\nTo deploy to kebele workstations:")
    print("1. Copy the 'deployment' folder to target machine")
    print("2. Run 'install.bat' as Administrator")
    print("3. Service will start automatically in system tray")

if __name__ == '__main__':
    main()
