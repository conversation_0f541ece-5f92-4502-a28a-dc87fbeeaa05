/**
 * Frontend Translation Validator Component
 * Validates translations in the frontend and provides real-time feedback
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useLocalization } from '../contexts/LocalizationContext';

const TranslationValidator = ({ enabled = process.env.NODE_ENV === 'development', showResults = false }) => {
  const { 
    currentLanguage, 
    translationStats, 
    getAvailableLanguages,
    t 
  } = useLocalization();

  const [validationResults, setValidationResults] = useState({});
  const [isValidating, setIsValidating] = useState(false);
  const [showResultsState, setShowResultsState] = useState(showResults);

  // Required translation keys for validation
  const requiredKeys = [
    'dashboard', 'login', 'logout', 'username', 'password',
    'save', 'cancel', 'delete', 'edit', 'add', 'search',
    'loading', 'success', 'error', 'yes', 'no'
  ];

  // Common translation keys to check
  const commonKeys = [
    'welcome', 'home', 'settings', 'profile', 'help',
    'about', 'contact', 'submit', 'reset', 'confirm',
    'back', 'next', 'previous', 'close', 'open'
  ];

  const validateTranslations = useCallback(() => {
    setIsValidating(true);
    
    const results = {
      timestamp: new Date().toISOString(),
      language: currentLanguage,
      summary: {
        total: 0,
        missing: 0,
        empty: 0,
        valid: 0
      },
      issues: [],
      coverage: {}
    };

    // Test all required keys
    const allKeysToTest = [...requiredKeys, ...commonKeys];
    
    allKeysToTest.forEach(key => {
      results.summary.total++;
      
      const translation = t(key, null);
      const issue = {
        key,
        type: 'required',
        severity: requiredKeys.includes(key) ? 'error' : 'warning'
      };

      if (translation === null || translation === key) {
        // Translation is missing
        issue.status = 'missing';
        issue.message = `Translation missing for key: ${key}`;
        results.summary.missing++;
        results.issues.push(issue);
      } else if (!translation.trim()) {
        // Translation is empty
        issue.status = 'empty';
        issue.message = `Translation is empty for key: ${key}`;
        results.summary.empty++;
        results.issues.push(issue);
      } else {
        // Translation exists and is not empty
        issue.status = 'valid';
        issue.translation = translation;
        results.summary.valid++;
        
        // Additional validation checks
        const validationIssues = validateTranslationContent(key, translation);
        if (validationIssues.length > 0) {
          results.issues.push(...validationIssues);
        }
      }
    });

    // Calculate coverage
    results.coverage = {
      required: {
        total: requiredKeys.length,
        valid: requiredKeys.filter(key => {
          const translation = t(key, null);
          return translation && translation !== key && translation.trim();
        }).length
      },
      common: {
        total: commonKeys.length,
        valid: commonKeys.filter(key => {
          const translation = t(key, null);
          return translation && translation !== key && translation.trim();
        }).length
      }
    };

    results.coverage.required.percentage = Math.round(
      (results.coverage.required.valid / results.coverage.required.total) * 100
    );
    results.coverage.common.percentage = Math.round(
      (results.coverage.common.valid / results.coverage.common.total) * 100
    );

    setValidationResults(results);
    setIsValidating(false);
  }, [currentLanguage, t]);

  const validateTranslationContent = (key, translation) => {
    const issues = [];

    // Length validation
    if (translation.length > 500) {
      issues.push({
        key,
        type: 'content',
        status: 'warning',
        severity: 'warning',
        message: `Translation too long (${translation.length} chars, max 500)`
      });
    }

    // HTML content check
    if (/<[^>]+>/.test(translation)) {
      issues.push({
        key,
        type: 'content',
        status: 'warning',
        severity: 'warning',
        message: 'Translation contains HTML tags'
      });
    }

    // Placeholder validation
    const placeholders = translation.match(/\{[^}]+\}/g);
    if (placeholders) {
      placeholders.forEach(placeholder => {
        if (!/^\{[a-zA-Z_][a-zA-Z0-9_]*\}$/.test(placeholder)) {
          issues.push({
            key,
            type: 'content',
            status: 'error',
            severity: 'error',
            message: `Invalid placeholder format: ${placeholder}`
          });
        }
      });
    }

    return issues;
  };

  // Auto-validate when language changes
  useEffect(() => {
    if (enabled) {
      const timer = setTimeout(validateTranslations, 1000);
      return () => clearTimeout(timer);
    }
  }, [currentLanguage, enabled, validateTranslations]);

  const exportResults = () => {
    const dataStr = JSON.stringify(validationResults, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `translation-validation-${currentLanguage}-${Date.now()}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  if (!enabled) {
    return null;
  }

  const { summary, issues, coverage } = validationResults;

  return (
    <div style={{
      position: 'fixed',
      top: '20px',
      left: '20px',
      width: '350px',
      maxHeight: '500px',
      backgroundColor: 'white',
      border: '1px solid #ddd',
      borderRadius: '8px',
      boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
      zIndex: 9998,
      fontFamily: 'Arial, sans-serif',
      fontSize: '12px',
      display: showResultsState ? 'block' : 'none'
    }}>
      {/* Header */}
      <div style={{
        backgroundColor: '#17a2b8',
        color: 'white',
        padding: '10px 12px',
        borderRadius: '8px 8px 0 0',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <h4 style={{ margin: 0, fontSize: '14px' }}>🔍 Translation Validator</h4>
        <button
          onClick={() => setShowResultsState(false)}
          style={{
            background: 'none',
            border: 'none',
            color: 'white',
            fontSize: '16px',
            cursor: 'pointer'
          }}
        >
          ×
        </button>
      </div>

      {/* Content */}
      <div style={{ padding: '12px', maxHeight: '400px', overflowY: 'auto' }}>
        {/* Summary */}
        {summary && (
          <div style={{ marginBottom: '12px' }}>
            <h5 style={{ margin: '0 0 6px 0', color: '#333' }}>
              Summary ({currentLanguage.toUpperCase()})
            </h5>
            <div style={{ fontSize: '11px', color: '#666' }}>
              <div>Total Keys: <strong>{summary.total}</strong></div>
              <div>Valid: <strong style={{ color: '#28a745' }}>{summary.valid}</strong></div>
              <div>Missing: <strong style={{ color: '#dc3545' }}>{summary.missing}</strong></div>
              <div>Empty: <strong style={{ color: '#ffc107' }}>{summary.empty}</strong></div>
            </div>
          </div>
        )}

        {/* Coverage */}
        {coverage && (
          <div style={{ marginBottom: '12px' }}>
            <h5 style={{ margin: '0 0 6px 0', color: '#333' }}>Coverage</h5>
            <div style={{ fontSize: '11px' }}>
              <div style={{ marginBottom: '4px' }}>
                <div>Required Keys: {coverage.required.percentage}%</div>
                <div style={{
                  width: '100%',
                  height: '4px',
                  backgroundColor: '#e9ecef',
                  borderRadius: '2px',
                  overflow: 'hidden'
                }}>
                  <div style={{
                    width: `${coverage.required.percentage}%`,
                    height: '100%',
                    backgroundColor: coverage.required.percentage > 80 ? '#28a745' : 
                                   coverage.required.percentage > 50 ? '#ffc107' : '#dc3545'
                  }} />
                </div>
              </div>
              <div>
                <div>Common Keys: {coverage.common.percentage}%</div>
                <div style={{
                  width: '100%',
                  height: '4px',
                  backgroundColor: '#e9ecef',
                  borderRadius: '2px',
                  overflow: 'hidden'
                }}>
                  <div style={{
                    width: `${coverage.common.percentage}%`,
                    height: '100%',
                    backgroundColor: coverage.common.percentage > 80 ? '#28a745' : 
                                   coverage.common.percentage > 50 ? '#ffc107' : '#dc3545'
                  }} />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Issues */}
        {issues && issues.length > 0 && (
          <div style={{ marginBottom: '12px' }}>
            <h5 style={{ margin: '0 0 6px 0', color: '#333' }}>
              Issues ({issues.length})
            </h5>
            <div style={{ maxHeight: '150px', overflowY: 'auto' }}>
              {issues.slice(0, 10).map((issue, index) => (
                <div
                  key={index}
                  style={{
                    padding: '4px 6px',
                    marginBottom: '2px',
                    backgroundColor: issue.severity === 'error' ? '#f8d7da' : '#fff3cd',
                    borderLeft: `3px solid ${issue.severity === 'error' ? '#dc3545' : '#ffc107'}`,
                    fontSize: '10px'
                  }}
                >
                  <div style={{ fontWeight: 'bold' }}>{issue.key}</div>
                  <div>{issue.message}</div>
                </div>
              ))}
              {issues.length > 10 && (
                <div style={{ textAlign: 'center', color: '#666', fontSize: '10px' }}>
                  ... and {issues.length - 10} more issues
                </div>
              )}
            </div>
          </div>
        )}

        {/* Actions */}
        <div style={{ display: 'flex', gap: '6px', flexWrap: 'wrap' }}>
          <button
            onClick={validateTranslations}
            disabled={isValidating}
            style={{
              padding: '4px 8px',
              fontSize: '11px',
              border: '1px solid #17a2b8',
              borderRadius: '3px',
              backgroundColor: 'white',
              color: '#17a2b8',
              cursor: isValidating ? 'not-allowed' : 'pointer',
              opacity: isValidating ? 0.6 : 1
            }}
          >
            {isValidating ? '🔄 Validating...' : '🔍 Validate'}
          </button>
          {validationResults.summary && (
            <button
              onClick={exportResults}
              style={{
                padding: '4px 8px',
                fontSize: '11px',
                border: '1px solid #28a745',
                borderRadius: '3px',
                backgroundColor: 'white',
                color: '#28a745',
                cursor: 'pointer'
              }}
            >
              📄 Export
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

// Floating toggle button
export const TranslationValidatorToggle = ({ enabled = process.env.NODE_ENV === 'development' }) => {
  const [showValidator, setShowValidator] = useState(false);

  if (!enabled) {
    return null;
  }

  return (
    <>
      <div style={{
        position: 'fixed',
        top: '20px',
        left: '20px',
        zIndex: 9999
      }}>
        <button
          onClick={() => setShowValidator(!showValidator)}
          style={{
            backgroundColor: '#17a2b8',
            color: 'white',
            border: 'none',
            borderRadius: '50%',
            width: '40px',
            height: '40px',
            fontSize: '16px',
            cursor: 'pointer',
            boxShadow: '0 2px 10px rgba(0,0,0,0.2)'
          }}
          title="Toggle Translation Validator"
        >
          🔍
        </button>
      </div>
      {showValidator && <TranslationValidator enabled={enabled} showResults={showValidator} />}
    </>
  );
};

export default TranslationValidator;
