# Dual Calendar System - Ethiopian & Gregorian

## Overview

This system implements a dual calendar approach where:
1. **Users see and interact with Ethiopian calendar dates**
2. **System stores both Ethiopian and Gregorian dates**
3. **All operations work with both calendar systems**

## User Experience Flow

```
User Selects Ethiopian Date → System Converts → Stores Both Calendars
     (UI Display)              (Automatic)        (Database)
```

### Example User Flow:
1. User sees date picker showing Ethiopian calendar (e.g., "15/05/2016" Ethiopian)
2. User selects an Ethiopian date
3. System automatically converts to Gregorian (e.g., "2024-01-23" Gregorian)
4. System stores both dates:
   - `date_gregorian`: "2024-01-23" (for system operations)
   - `date_ethiopian`: "15/05/2016" (for user display)
   - `date_ethiopian_components`: {year: 2016, month: 5, day: 15} (for calculations)

## Implementation Components

### 1. FixedEthiopianDateAdapter (`frontend/src/utils/FixedEthiopianDateAdapter.js`)

**Purpose**: MUI date picker adapter that handles Ethiopian calendar display and conversion.

**Key Features**:
- Extends `AdapterDateFns` for MUI compatibility
- Shows Ethiopian dates to users
- Converts between Ethiopian and Gregorian calendars
- Provides dual calendar information

**Key Methods**:
```javascript
// Get both calendar representations
getDualCalendarInfo(gregorianDate) → {
  gregorian: Date,
  ethiopian: {year, month, day},
  formatted: {
    ethiopian: "15/05/2016",
    gregorian: "01/23/2024"
  }
}

// Create Gregorian date from Ethiopian components
createFromEthiopian(year, month, day) → Date

// Get Ethiopian components from Gregorian date
getEthiopianComponents(gregorianDate) → {year, month, day}
```

### 2. DualCalendarDatePicker (`frontend/src/components/common/DualCalendarDatePicker.jsx`)

**Purpose**: Enhanced date picker component that shows both calendar systems.

**Features**:
- Displays Ethiopian calendar to user
- Shows both Ethiopian and Gregorian representations
- Provides clear visual feedback about dual calendar system
- Includes tooltips and helper text

**Usage**:
```jsx
<DualCalendarDatePicker
  label="Birth Date (Ethiopian Calendar)"
  value={birthDate}
  onChange={(newDate) => {
    setBirthDate(newDate);
    // newDate is a Gregorian Date object
    // Component automatically shows Ethiopian equivalent
  }}
  language="en" // or "am" for Amharic
  showBothCalendars={true}
/>
```

### 3. Ethiopian Calendar Utilities (`frontend/src/utils/ethiopianCalendar.js`)

**Purpose**: Core conversion and calculation functions.

**Key Functions**:
- `gregorianToEthiopian(date)` - Convert Gregorian to Ethiopian
- `ethiopianToGregorian(year, month, day)` - Convert Ethiopian to Gregorian
- `formatEthiopianDate(ethiopianDate, format, language)` - Format Ethiopian dates
- `parseEthiopianDate(dateString)` - Parse Ethiopian date strings

## Data Storage Strategy

### Database Schema Approach

Store both calendar representations for maximum flexibility:

```sql
-- Example table structure
CREATE TABLE id_cards (
    id SERIAL PRIMARY KEY,
    citizen_id INTEGER,
    
    -- Gregorian dates (for system operations, sorting, calculations)
    issue_date DATE,
    expiry_date DATE,
    
    -- Ethiopian dates (for user display and reference)
    issue_date_ethiopian VARCHAR(10), -- "15/05/2016"
    expiry_date_ethiopian VARCHAR(10), -- "15/05/2026"
    
    -- Ethiopian components (for Ethiopian calendar calculations)
    issue_date_ethiopian_components JSONB, -- {"year": 2016, "month": 5, "day": 15}
    expiry_date_ethiopian_components JSONB,
    
    created_at TIMESTAMP DEFAULT NOW()
);
```

### Frontend Data Handling

```javascript
// When user selects a date
const handleDateChange = (gregorianDate) => {
  const adapter = new FixedEthiopianDateAdapter({ locale: 'en' });
  const dualInfo = adapter.getDualCalendarInfo(gregorianDate);
  
  // Prepare data for backend
  const dataToSave = {
    // Gregorian for system operations
    issue_date: gregorianDate.toISOString().split('T')[0],
    
    // Ethiopian for user display
    issue_date_ethiopian: dualInfo.formatted.ethiopian,
    
    // Ethiopian components for calculations
    issue_date_ethiopian_components: dualInfo.ethiopian
  };
  
  // Send to backend
  await saveData(dataToSave);
};
```

## Benefits of Dual Calendar System

### 1. User Experience
- ✅ Users work with familiar Ethiopian calendar
- ✅ Clear visual feedback showing both calendars
- ✅ No confusion about date conversions
- ✅ Culturally appropriate interface

### 2. System Reliability
- ✅ Gregorian dates for accurate system operations
- ✅ Ethiopian dates for user display
- ✅ Both calendars always in sync
- ✅ No data loss during conversions

### 3. Developer Experience
- ✅ Clear separation of concerns
- ✅ Easy to debug with dual calendar info
- ✅ Flexible data access patterns
- ✅ Future-proof architecture

## Usage Examples

### Basic Date Picker
```jsx
import DualCalendarDatePicker from '../components/common/DualCalendarDatePicker';

function MyForm() {
  const [birthDate, setBirthDate] = useState(null);
  
  return (
    <DualCalendarDatePicker
      label="Birth Date"
      value={birthDate}
      onChange={setBirthDate}
      showBothCalendars={true}
    />
  );
}
```

### Advanced Usage with Validation
```jsx
function AdvancedForm() {
  const [issueDate, setIssueDate] = useState(null);
  const [adapter] = useState(() => new FixedEthiopianDateAdapter());
  
  const handleDateChange = (newDate) => {
    setIssueDate(newDate);
    
    if (newDate) {
      const dualInfo = adapter.getDualCalendarInfo(newDate);
      console.log('Selected:', dualInfo);
      
      // Validate Ethiopian date
      if (dualInfo.ethiopian.year < 2010) {
        setError('Date must be after 2010 Ethiopian calendar');
      }
    }
  };
  
  return (
    <DualCalendarDatePicker
      label="Issue Date"
      value={issueDate}
      onChange={handleDateChange}
      error={!!error}
      helperText={error}
    />
  );
}
```

## Migration Strategy

### For Existing Data
1. Add Ethiopian date columns to existing tables
2. Run migration to populate Ethiopian dates from existing Gregorian dates
3. Update frontend components to use dual calendar system
4. Gradually migrate forms to use new date picker

### Migration Script Example
```javascript
// Backend migration
const adapter = new EthiopianDateAdapter();

for (const record of existingRecords) {
  const ethiopianDate = adapter.getEthiopianComponents(record.gregorian_date);
  const ethiopianFormatted = adapter.formatEthiopianDate(ethiopianDate);
  
  await updateRecord(record.id, {
    date_ethiopian: ethiopianFormatted,
    date_ethiopian_components: ethiopianDate
  });
}
```

## Testing

### Unit Tests
- Test date conversions (Ethiopian ↔ Gregorian)
- Test date formatting in both languages
- Test edge cases (leap years, month boundaries)

### Integration Tests
- Test complete user flow (select → convert → store)
- Test form validation with Ethiopian dates
- Test data persistence and retrieval

### User Acceptance Tests
- Verify users can select Ethiopian dates intuitively
- Confirm both calendars display correctly
- Validate cultural appropriateness

## Troubleshooting

### Common Issues
1. **Date Conversion Errors**: Check Ethiopian calendar utilities
2. **Display Issues**: Verify FixedEthiopianDateAdapter configuration
3. **Storage Problems**: Ensure backend handles both date formats

### Debug Tools
- Enable development logging in FixedEthiopianDateAdapter
- Use DualCalendarExample component for testing
- Check browser console for conversion logs

## Future Enhancements

1. **Localization**: Full Amharic language support
2. **Holidays**: Ethiopian holiday integration
3. **Date Ranges**: Ethiopian calendar date range pickers
4. **Reports**: Ethiopian calendar-based reporting
5. **Mobile**: Touch-optimized Ethiopian calendar widgets
