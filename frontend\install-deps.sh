#!/bin/sh

# Install dependencies from package.json
echo "Installing dependencies from package.json..."
npm install

# Ensure dayjs and required plugins are installed
echo "Installing dayjs and required plugins..."
npm install dayjs@latest

# Install specific dayjs plugins
echo "Installing specific dayjs plugins..."
npm install dayjs/plugin/weekOfYear
npm install dayjs/plugin/customParseFormat
npm install dayjs/plugin/localizedFormat
npm install dayjs/plugin/isBetween

# Create plugins directory if it doesn't exist
mkdir -p node_modules/dayjs/plugin

# Print versions
echo "Installed packages:"
npm list dayjs
npm list @mui/x-date-pickers

# Check if plugins directory exists
echo "Checking dayjs plugins directory..."
if [ -d "node_modules/dayjs/plugin" ]; then
  echo "Plugins directory exists"
  ls -la node_modules/dayjs/plugin
else
  echo "Plugins directory does not exist"
fi

# Exit with success
exit 0
