import React from 'react';
import { Box, Typography, Paper, Accordion, AccordionSummary, AccordionDetails } from '@mui/material';
import { ExpandMore as ExpandMoreIcon } from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { usePermissions } from '../../hooks/usePermissions';

const NavigationDebug = () => {
  const { user } = useAuth();
  const { 
    hasPermission, 
    getDynamicNavigationItems, 
    userPermissions, 
    userGroups,
    customGroups 
  } = usePermissions();

  // Test some basic permissions
  const testPermissions = [
    'view_dashboard',
    'view_citizens',
    'view_idcards',
    'manage_users',
    'view_reports'
  ];

  const navigationItems = getDynamicNavigationItems();

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        🔍 Navigation Debug Information
      </Typography>

      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="h6">User Information</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Paper sx={{ p: 2, mb: 2 }}>
            <Typography variant="subtitle1" gutterBottom>
              <strong>Basic User Data:</strong>
            </Typography>
            <Typography variant="body2">Username: {user?.username || 'Not available'}</Typography>
            <Typography variant="body2">Role: {user?.role || 'Not available'}</Typography>
            <Typography variant="body2">Is Superuser: {user?.is_superuser ? 'Yes' : 'No'}</Typography>
            <Typography variant="body2">Tenant: {user?.tenant_name || 'Not available'}</Typography>
            <Typography variant="body2">Tenant Type: {user?.tenant_type || 'Not available'}</Typography>
          </Paper>

          <Paper sx={{ p: 2, mb: 2 }}>
            <Typography variant="subtitle1" gutterBottom>
              <strong>Permissions Array:</strong>
            </Typography>
            <Typography variant="body2">
              Count: {userPermissions.length}
            </Typography>
            <Box sx={{ maxHeight: 200, overflow: 'auto', mt: 1 }}>
              {userPermissions.length > 0 ? (
                userPermissions.map((perm, index) => (
                  <Typography key={index} variant="body2" sx={{ fontFamily: 'monospace' }}>
                    • {perm}
                  </Typography>
                ))
              ) : (
                <Typography variant="body2" color="error">
                  No permissions found in user object
                </Typography>
              )}
            </Box>
          </Paper>

          <Paper sx={{ p: 2 }}>
            <Typography variant="subtitle1" gutterBottom>
              <strong>Groups Array:</strong>
            </Typography>
            <Typography variant="body2">
              Count: {userGroups.length}
            </Typography>
            <Typography variant="body2">
              Custom Groups: {customGroups.length}
            </Typography>
            <Box sx={{ mt: 1 }}>
              {userGroups.length > 0 ? (
                userGroups.map((group, index) => (
                  <Typography key={index} variant="body2" sx={{ fontFamily: 'monospace' }}>
                    • {group}
                  </Typography>
                ))
              ) : (
                <Typography variant="body2" color="error">
                  No groups found in user object
                </Typography>
              )}
            </Box>
          </Paper>
        </AccordionDetails>
      </Accordion>

      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="h6">Permission Tests</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Paper sx={{ p: 2 }}>
            <Typography variant="subtitle1" gutterBottom>
              <strong>Testing Basic Permissions:</strong>
            </Typography>
            {testPermissions.map((permission) => {
              const hasAccess = hasPermission(permission);
              return (
                <Typography 
                  key={permission} 
                  variant="body2" 
                  sx={{ 
                    fontFamily: 'monospace',
                    color: hasAccess ? 'success.main' : 'error.main'
                  }}
                >
                  {hasAccess ? '✅' : '❌'} {permission}
                </Typography>
              );
            })}
          </Paper>
        </AccordionDetails>
      </Accordion>

      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="h6">Generated Navigation Items</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Paper sx={{ p: 2 }}>
            <Typography variant="subtitle1" gutterBottom>
              <strong>Dynamic Navigation Items:</strong>
            </Typography>
            <Typography variant="body2" gutterBottom>
              Count: {navigationItems.length}
            </Typography>
            {navigationItems.length > 0 ? (
              navigationItems.map((item, index) => (
                <Box key={index} sx={{ mb: 2, p: 1, border: '1px solid #ddd', borderRadius: 1 }}>
                  <Typography variant="body2">
                    <strong>{item.label}</strong> → {item.path}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Permission: {item.permission} | Icon: {item.icon}
                  </Typography>
                  {item.children && item.children.length > 0 && (
                    <Box sx={{ ml: 2, mt: 1 }}>
                      <Typography variant="caption" color="text.secondary">
                        Children:
                      </Typography>
                      {item.children.map((child, childIndex) => (
                        <Typography key={childIndex} variant="caption" display="block" sx={{ ml: 1 }}>
                          • {child.label} → {child.path}
                        </Typography>
                      ))}
                    </Box>
                  )}
                </Box>
              ))
            ) : (
              <Typography variant="body2" color="error">
                No navigation items generated! This is why the menu is empty.
              </Typography>
            )}
          </Paper>
        </AccordionDetails>
      </Accordion>

      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="h6">Raw User Object</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Paper sx={{ p: 2 }}>
            <Typography variant="subtitle1" gutterBottom>
              <strong>Complete User Object:</strong>
            </Typography>
            <Box sx={{ 
              maxHeight: 400, 
              overflow: 'auto', 
              bgcolor: 'grey.100', 
              p: 2, 
              borderRadius: 1,
              fontFamily: 'monospace',
              fontSize: '0.8rem'
            }}>
              <pre>{JSON.stringify(user, null, 2)}</pre>
            </Box>
          </Paper>
        </AccordionDetails>
      </Accordion>
    </Box>
  );
};

export default NavigationDebug;
