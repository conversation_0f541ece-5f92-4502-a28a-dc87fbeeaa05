from django.core.management.base import BaseCommand
from tenants.models import Tenant
from django_tenants.models import Domain


class Command(BaseCommand):
    help = 'Debug tenant resolution for tenants 42 and 46'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🔍 Debugging tenant resolution...'))
        
        for tenant_id in [42, 46]:
            self.stdout.write(f'\n📋 === TENANT {tenant_id} ===')
            
            try:
                tenant = Tenant.objects.get(id=tenant_id)
                self.stdout.write(f'✅ Tenant exists: {tenant.name} ({tenant.schema_name})')
                self.stdout.write(f'   - Type: {tenant.type}')
                self.stdout.write(f'   - Parent: {tenant.parent}')
                self.stdout.write(f'   - Created: {tenant.created_on}')
                
                # Check domains
                domains = Domain.objects.filter(tenant=tenant)
                self.stdout.write(f'   - Domains: {domains.count()}')
                
                if domains.count() == 0:
                    self.stdout.write(f'   ❌ NO DOMAINS CONFIGURED!')
                    self.stdout.write(f'   ⚠️  This will cause FakeTenant creation')
                else:
                    for domain in domains:
                        self.stdout.write(f'     * {domain.domain} (primary: {domain.is_primary})')
                
                # Check if there's a primary domain
                primary_domains = domains.filter(is_primary=True)
                if primary_domains.count() == 0:
                    self.stdout.write(f'   ⚠️  No primary domain set')
                elif primary_domains.count() > 1:
                    self.stdout.write(f'   ⚠️  Multiple primary domains: {primary_domains.count()}')
                else:
                    self.stdout.write(f'   ✅ Primary domain: {primary_domains.first().domain}')
                
            except Tenant.DoesNotExist:
                self.stdout.write(f'❌ Tenant {tenant_id} does not exist!')
            except Exception as e:
                self.stdout.write(f'❌ Error: {str(e)}')
        
        # Check all domains
        self.stdout.write(f'\n📋 === ALL DOMAINS ===')
        all_domains = Domain.objects.all()
        for domain in all_domains:
            self.stdout.write(f'   - {domain.domain} → Tenant {domain.tenant.id} ({domain.tenant.name}) [Primary: {domain.is_primary}]')
        
        self.stdout.write(self.style.SUCCESS('\n🎉 Debug completed!'))
