#!/usr/bin/env python3
"""
Ultimate fix for persistent IntegrityError issues.
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
sys.path.append('/app')
django.setup()

from django.db import connection

def ultimate_fix():
    """Ultimate fix for persistent IntegrityError."""
    cursor = connection.cursor()
    
    print('=== ULTIMATE SUBCITY DIAGNOSTIC ===')
    
    # 1. Check ALL schemas for tenants_subcity tables
    cursor.execute("""
        SELECT schemaname, tablename, tableowner
        FROM pg_tables 
        WHERE tablename = 'tenants_subcity'
        ORDER BY schemaname;
    """)
    all_schemas = cursor.fetchall()
    print(f'All schemas with tenants_subcity: {len(all_schemas)}')
    for schema in all_schemas:
        print(f'  Schema: {schema[0]}, Owner: {schema[2]}')
    
    # 2. Check records in ALL schemas
    total_records = 0
    max_id_found = 0
    
    for schema_info in all_schemas:
        schema_name = schema_info[0]
        try:
            cursor.execute(f'SELECT id, name FROM {schema_name}.tenants_subcity ORDER BY id;')
            records = cursor.fetchall()
            print(f'\nSchema "{schema_name}" records: {len(records)}')
            for record in records:
                print(f'  ID: {record[0]}, Name: {record[1]}')
                max_id_found = max(max_id_found, record[0])
                total_records += 1
        except Exception as e:
            print(f'  Error accessing {schema_name}: {e}')
    
    print(f'\nTotal SubCity records across all schemas: {total_records}')
    print(f'Highest ID found: {max_id_found}')
    
    # 3. Check for any constraints or triggers
    cursor.execute("""
        SELECT conname, contype, pg_get_constraintdef(oid) as definition
        FROM pg_constraint 
        WHERE conrelid = 'tenants_subcity'::regclass;
    """)
    constraints = cursor.fetchall()
    print(f'\nConstraints on tenants_subcity: {len(constraints)}')
    for constraint in constraints:
        print(f'  {constraint[0]} ({constraint[1]}): {constraint[2]}')
    
    # 4. Check sequence ownership and current value
    cursor.execute("""
        SELECT sequence_name, last_value, increment_by, is_called
        FROM information_schema.sequences 
        WHERE sequence_name = 'tenants_subcity_id_seq';
    """)
    seq_info = cursor.fetchall()
    print(f'\nSequence info: {seq_info}')
    
    # 5. AGGRESSIVE FIX: Set sequence to a very high value
    safe_value = max(max_id_found + 100, 1000)  # Use either max+100 or 1000, whichever is higher
    cursor.execute(f"SELECT setval('tenants_subcity_id_seq', {safe_value});")
    print(f'✅ Set sequence to VERY SAFE value: {safe_value}')
    
    # 6. Also fix Kebele sequence aggressively
    cursor.execute('SELECT COALESCE(MAX(id), 0) FROM tenants_kebele;')
    max_kebele = cursor.fetchone()[0]
    safe_kebele = max(max_kebele + 100, 500)
    cursor.execute(f"SELECT setval('tenants_kebele_id_seq', {safe_kebele});")
    print(f'✅ Set Kebele sequence to VERY SAFE value: {safe_kebele}')
    
    # 7. Verify final state
    cursor.execute('SELECT last_value, is_called FROM tenants_subcity_id_seq;')
    final_seq = cursor.fetchone()
    print(f'\nFinal SubCity sequence: last_value={final_seq[0]}, is_called={final_seq[1]}')
    
    cursor.execute('SELECT last_value, is_called FROM tenants_kebele_id_seq;')
    final_kebele_seq = cursor.fetchone()
    print(f'Final Kebele sequence: last_value={final_kebele_seq[0]}, is_called={final_kebele_seq[1]}')
    
    print('\n🚀 ULTIMATE FIX COMPLETE!')
    print(f'🛡️  SubCity IDs will start from: {safe_value + 1}')
    print(f'🛡️  Kebele IDs will start from: {safe_kebele + 1}')
    print('🎯 This should eliminate ALL IntegrityError issues!')
    
    return True

if __name__ == "__main__":
    ultimate_fix()
