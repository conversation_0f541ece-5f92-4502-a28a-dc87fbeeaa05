import React, { useState, useEffect } from 'react';
import {
  TextField,
  Box,
  Typography,
  Chip,
  Tooltip,
  InputAdornment,
  IconButton
} from '@mui/material';
import {
  CalendarToday as CalendarIcon,
  Info as InfoIcon,
  SwapHoriz as SwapIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import FixedEthiopianDateAdapter from '../../utils/FixedEthiopianDateAdapter';
import {
  gregorianToEthiopian,
  ethiopianToGregorian,
  formatEthiopianDate,
  parseEthiopianDate,
  isValidEthiopianDate
} from '../../utils/ethiopianCalendar';

/**
 * Dual Calendar Date Picker Component
 * 
 * This component provides a date picker that:
 * 1. Shows Ethiopian calendar dates to the user
 * 2. Allows user to select Ethiopian dates
 * 3. Automatically converts and stores both Ethiopian and Gregorian dates
 * 4. Shows both calendar representations for clarity
 */
const DualCalendarDatePicker = ({
  label = "Select Date",
  value,
  onChange,
  error,
  helperText,
  required = false,
  disabled = false,
  fullWidth = true,
  language = 'en', // 'am' for Amharic, 'en' for English
  showBothCalendars = true,
  ...otherProps
}) => {
  const [adapter] = useState(() => new FixedEthiopianDateAdapter({ locale: language }));
  const [dualInfo, setDualInfo] = useState(null);

  // Update dual calendar info when value changes
  useEffect(() => {
    if (value && value instanceof Date && !isNaN(value.getTime())) {
      const info = adapter.getDualCalendarInfo(value);
      setDualInfo(info);
    } else {
      setDualInfo(null);
    }
  }, [value, adapter]);

  const handleDateChange = (newDate) => {
    if (onChange) {
      onChange(newDate);
    }
  };

  const getHelperContent = () => {
    if (error && helperText) {
      return helperText;
    }

    if (showBothCalendars && dualInfo) {
      return (
        <Box sx={{ mt: 1 }}>
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', alignItems: 'center' }}>
            <Chip
              size="small"
              icon={<CalendarIcon />}
              label={`Ethiopian: ${dualInfo.formatted.ethiopian}`}
              color="primary"
              variant="outlined"
            />
            <SwapIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
            <Chip
              size="small"
              icon={<CalendarIcon />}
              label={`Gregorian: ${dualInfo.formatted.gregorian}`}
              color="secondary"
              variant="outlined"
            />
          </Box>
          <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
            User selects Ethiopian date, system stores both calendars
          </Typography>
        </Box>
      );
    }

    return helperText;
  };

  return (
    <LocalizationProvider dateAdapter={FixedEthiopianDateAdapter} adapterLocale={language}>
      <Box>
        <DatePicker
          label={label}
          value={value}
          onChange={handleDateChange}
          disabled={disabled}
          slotProps={{
            textField: {
              fullWidth,
              required,
              error,
              helperText: getHelperContent(),
              InputProps: {
                endAdornment: (
                  <InputAdornment position="end">
                    <Tooltip title="Ethiopian Calendar - User selects Ethiopian date, system converts to Gregorian">
                      <IconButton size="small">
                        <InfoIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </InputAdornment>
                )
              }
            }
          }}
          {...otherProps}
        />

        {/* Additional info for developers/debugging */}
        {process.env.NODE_ENV === 'development' && dualInfo && (
          <Box sx={{ mt: 1, p: 1, bgcolor: 'grey.50', borderRadius: 1 }}>
            <Typography variant="caption" color="text.secondary">
              <strong>Debug Info:</strong><br />
              Ethiopian: {dualInfo.ethiopian.year}/{dualInfo.ethiopian.month}/{dualInfo.ethiopian.day}<br />
              Gregorian: {dualInfo.gregorian.toISOString()}<br />
              Formatted Ethiopian: {dualInfo.formatted.ethiopianLong}<br />
              Formatted Gregorian: {dualInfo.formatted.gregorianLong}
            </Typography>
          </Box>
        )}
      </Box>
    </LocalizationProvider>
  );
};

export default DualCalendarDatePicker;
