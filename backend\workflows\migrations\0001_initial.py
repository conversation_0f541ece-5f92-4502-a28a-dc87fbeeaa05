# Generated by Django 4.2.7 on 2025-06-07 12:06

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('tenants', '0001_initial'),
        ('idcards', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='WorkflowLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('submit', 'Submit'), ('approve', 'Approve'), ('reject', 'Reject'), ('print', 'Print'), ('issue', 'Issue'), ('revoke', 'Revoke')], max_length=20)),
                ('from_status', models.CharField(choices=[('draft', 'Draft'), ('pending_approval', 'Pending Kebele Approval'), ('kebele_approved', 'Kebele Approved - Pending Subcity Approval'), ('approved', 'Fully Approved - Ready for Printing'), ('rejected', 'Rejected'), ('printed', 'Printed'), ('issued', 'Issued'), ('expired', 'Expired'), ('revoked', 'Revoked')], max_length=20)),
                ('to_status', models.CharField(choices=[('draft', 'Draft'), ('pending_approval', 'Pending Kebele Approval'), ('kebele_approved', 'Kebele Approved - Pending Subcity Approval'), ('approved', 'Fully Approved - Ready for Printing'), ('rejected', 'Rejected'), ('printed', 'Printed'), ('issued', 'Issued'), ('expired', 'Expired'), ('revoked', 'Revoked')], max_length=20)),
                ('comment', models.TextField(blank=True, null=True)),
                ('performed_at', models.DateTimeField(auto_now_add=True)),
                ('id_card', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='workflow_logs', to='idcards.idcard')),
                ('performed_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-performed_at'],
            },
        ),
        migrations.CreateModel(
            name='CitizenTransferRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transfer_id', models.CharField(editable=False, max_length=20, unique=True)),
                ('citizen_id', models.IntegerField(blank=True, help_text='ID of citizen in source kebele (null for destination schema records)', null=True)),
                ('citizen_name', models.CharField(help_text='Full name of citizen', max_length=255)),
                ('citizen_digital_id', models.CharField(blank=True, max_length=50, null=True)),
                ('transfer_reason', models.CharField(choices=[('relocation', 'Relocation/Moving'), ('marriage', 'Marriage'), ('work', 'Work/Employment'), ('education', 'Education'), ('family', 'Family Reasons'), ('other', 'Other')], default='relocation', max_length=20)),
                ('reason_description', models.TextField(blank=True, help_text='Detailed reason for transfer', null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending Review'), ('accepted', 'Accepted'), ('rejected', 'Rejected'), ('completed', 'Transfer Completed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('reviewed_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('application_letter', models.FileField(blank=True, help_text='Application letter from citizen requesting transfer', null=True, upload_to='transfer_documents/application_letters/')),
                ('current_kebele_id', models.FileField(blank=True, help_text='Current kebele ID card of the citizen', null=True, upload_to='transfer_documents/kebele_ids/')),
                ('documents_uploaded_at', models.DateTimeField(blank=True, help_text='When documents were uploaded', null=True)),
                ('review_notes', models.TextField(blank=True, help_text='Notes from destination kebele leader', null=True)),
                ('new_citizen_id', models.IntegerField(blank=True, help_text='ID of citizen in destination kebele after transfer', null=True)),
                ('completed_by', models.ForeignKey(blank=True, help_text='Kebele A leader who completed the transfer', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='transfer_requests_completed', to=settings.AUTH_USER_MODEL)),
                ('destination_kebele', models.ForeignKey(help_text='Kebele where citizen wants to transfer', on_delete=django.db.models.deletion.CASCADE, related_name='incoming_transfers', to='tenants.tenant')),
                ('requested_by', models.ForeignKey(help_text='Kebele A leader who initiated the request', on_delete=django.db.models.deletion.CASCADE, related_name='transfer_requests_made', to=settings.AUTH_USER_MODEL)),
                ('reviewed_by', models.ForeignKey(blank=True, help_text='Kebele B leader who reviewed the request', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='transfer_requests_reviewed', to=settings.AUTH_USER_MODEL)),
                ('source_kebele', models.ForeignKey(help_text='Kebele where citizen currently resides', on_delete=django.db.models.deletion.CASCADE, related_name='outgoing_transfers', to='tenants.tenant')),
            ],
            options={
                'db_table': 'citizen_transfer_requests',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CitizenTransferHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('original_citizen_data', models.JSONField(help_text='Complete citizen data from source kebele')),
                ('transfer_date', models.DateTimeField(auto_now_add=True)),
                ('source_kebele_name', models.CharField(max_length=255)),
                ('destination_kebele_name', models.CharField(max_length=255)),
                ('transfer_year', models.IntegerField()),
                ('transfer_month', models.IntegerField()),
                ('transfer_request', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='history', to='workflows.citizentransferrequest')),
            ],
            options={
                'db_table': 'citizen_transfer_history',
                'ordering': ['-transfer_date'],
            },
        ),
        migrations.CreateModel(
            name='CitizenClearanceRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('clearance_id', models.CharField(editable=False, max_length=20, unique=True)),
                ('citizen_id', models.IntegerField(help_text='ID of citizen in kebele')),
                ('citizen_name', models.CharField(help_text='Full name of citizen', max_length=255)),
                ('citizen_digital_id', models.CharField(blank=True, max_length=50, null=True)),
                ('destination_location', models.CharField(help_text='Where the citizen is moving to (city, region, country)', max_length=255)),
                ('clearance_reason', models.CharField(choices=[('relocation_outside_city', 'Relocation Outside City'), ('marriage_outside_city', 'Marriage Outside City'), ('work_outside_city', 'Work/Employment Outside City'), ('education_outside_city', 'Education Outside City'), ('family_outside_city', 'Family Reasons Outside City'), ('emigration', 'Emigration/Moving Abroad'), ('other', 'Other')], default='relocation_outside_city', max_length=30)),
                ('reason_description', models.TextField(help_text='Detailed reason for requesting clearance')),
                ('status', models.CharField(choices=[('pending', 'Pending Review'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('issued', 'Clearance Letter Issued'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('reviewed_at', models.DateTimeField(blank=True, null=True)),
                ('issued_at', models.DateTimeField(blank=True, null=True)),
                ('application_letter', models.FileField(blank=True, help_text='Application letter from citizen requesting clearance', null=True, upload_to='clearance_documents/application_letters/')),
                ('current_kebele_id', models.FileField(blank=True, help_text='Current kebele ID card of the citizen', null=True, upload_to='clearance_documents/kebele_ids/')),
                ('supporting_documents', models.FileField(blank=True, help_text='Additional supporting documents (job offer, marriage certificate, etc.)', null=True, upload_to='clearance_documents/supporting/')),
                ('documents_uploaded_at', models.DateTimeField(blank=True, help_text='When documents were uploaded', null=True)),
                ('review_notes', models.TextField(blank=True, help_text='Notes from subcity admin', null=True)),
                ('clearance_letter_path', models.CharField(blank=True, help_text='Path to generated clearance letter PDF', max_length=500, null=True)),
                ('issued_by', models.ForeignKey(blank=True, help_text='User who issued the clearance letter', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='clearance_requests_issued', to=settings.AUTH_USER_MODEL)),
                ('requested_by', models.ForeignKey(help_text='Kebele leader who initiated the request', on_delete=django.db.models.deletion.CASCADE, related_name='clearance_requests_made', to=settings.AUTH_USER_MODEL)),
                ('reviewed_by', models.ForeignKey(blank=True, help_text='Subcity admin who reviewed the request', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='clearance_requests_reviewed', to=settings.AUTH_USER_MODEL)),
                ('source_kebele', models.ForeignKey(help_text='Kebele where citizen currently resides', on_delete=django.db.models.deletion.CASCADE, related_name='clearance_requests', to='tenants.tenant')),
            ],
            options={
                'db_table': 'citizen_clearance_requests',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='citizentransferrequest',
            index=models.Index(fields=['status'], name='citizen_tra_status_65438c_idx'),
        ),
        migrations.AddIndex(
            model_name='citizentransferrequest',
            index=models.Index(fields=['source_kebele', 'status'], name='citizen_tra_source__93d763_idx'),
        ),
        migrations.AddIndex(
            model_name='citizentransferrequest',
            index=models.Index(fields=['destination_kebele', 'status'], name='citizen_tra_destina_5797c3_idx'),
        ),
        migrations.AddIndex(
            model_name='citizentransferrequest',
            index=models.Index(fields=['created_at'], name='citizen_tra_created_50067a_idx'),
        ),
        migrations.AddIndex(
            model_name='citizentransferhistory',
            index=models.Index(fields=['transfer_year', 'transfer_month'], name='citizen_tra_transfe_6d7f7f_idx'),
        ),
        migrations.AddIndex(
            model_name='citizentransferhistory',
            index=models.Index(fields=['source_kebele_name'], name='citizen_tra_source__a7fa98_idx'),
        ),
        migrations.AddIndex(
            model_name='citizentransferhistory',
            index=models.Index(fields=['destination_kebele_name'], name='citizen_tra_destina_eaa082_idx'),
        ),
        migrations.AddIndex(
            model_name='citizenclearancerequest',
            index=models.Index(fields=['status'], name='citizen_cle_status_c2f404_idx'),
        ),
        migrations.AddIndex(
            model_name='citizenclearancerequest',
            index=models.Index(fields=['source_kebele', 'status'], name='citizen_cle_source__0c120b_idx'),
        ),
        migrations.AddIndex(
            model_name='citizenclearancerequest',
            index=models.Index(fields=['created_at'], name='citizen_cle_created_1448ca_idx'),
        ),
        migrations.AddIndex(
            model_name='citizenclearancerequest',
            index=models.Index(fields=['citizen_id'], name='citizen_cle_citizen_bdb68a_idx'),
        ),
    ]
