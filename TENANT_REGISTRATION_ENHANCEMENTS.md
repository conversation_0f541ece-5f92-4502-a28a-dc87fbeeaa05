# Tenant Registration Enhancements

## Overview
Enhanced tenant registration system to support Amharic names for all tenant types and mayor signature upload for city tenants.

## Features Added

### 1. Amharic Name Support
Added Amharic name fields for all tenant types to support bilingual registration:

#### City Tenants
- `city_name_am` - City name in Amharic script
- `mayor_name_am` - Mayor name in Amharic script  
- `deputy_mayor_am` - Deputy mayor name in Amharic script

#### SubCity Tenants
- `name_am` - SubCity name in Amharic script

#### Kebele Tenants
- `name_am` - Kebele name in Amharic script

#### Ketena
- `name_am` - Ketena name in Amharic script

### 2. Mayor Signature Upload
Added mayor signature upload functionality for city tenants:
- `mayor_signature` - Image field for mayor's signature
- Supports common image formats (PNG, JPG, etc.)
- Recommended size: 300x100px
- Stored in `signatures/` directory

## Files Modified

### Backend Models
1. **`backend/tenants/models/city.py`**
   - Added `city_name_am`, `mayor_name_am`, `mayor_signature`, `deputy_mayor_am` fields

2. **`backend/tenants/models/subcity.py`**
   - Added `name_am` field

3. **`backend/tenants/models/kebele.py`**
   - Added `name_am` field to both Kebele and Ketena models

### Backend Serializers
1. **`backend/tenants/serializers/city_serializer.py`**
   - Updated CityAdministrationSerializer and CityRegistrationSerializer
   - Added new Amharic fields and mayor signature to field lists

2. **`backend/tenants/serializers/subcity_serializer.py`**
   - Updated SubCitySerializer and SubCityRegistrationSerializer
   - Added `name_am` field and updated create method

3. **`backend/tenants/serializers/kebele_serializer.py`**
   - Updated KebeleSerializer, KebeleRegistrationSerializer, KetenaSerializer, and KetenaCreateSerializer
   - Added `name_am` field and updated create methods

### Frontend Forms
1. **`frontend/src/pages/tenants/registration/CityRegistrationForm.jsx`**
   - Added Amharic city name field
   - Added Amharic mayor and deputy mayor name fields
   - Added mayor signature upload with file handling
   - Enhanced form layout and user experience

2. **`frontend/src/pages/tenants/registration/SubcityRegistrationForm.jsx`**
   - Added Amharic subcity name field
   - Updated form layout

3. **`frontend/src/pages/tenants/registration/KebeleRegistrationForm.jsx`**
   - Added Amharic kebele name field
   - Updated form layout

4. **`frontend/src/pages/tenants/TenantRegistration.jsx`**
   - Updated form submission logic for all tenant types
   - Added handling for Amharic names in API calls
   - Added mayor signature file upload to FormData

### Database Migration
1. **`backend/tenants/migrations/0002_add_amharic_names_and_mayor_signature.py`**
   - Migration to add all new fields to existing database tables
   - Handles both Amharic name fields and mayor signature field

## Usage Examples

### City Registration with Amharic Names
```javascript
const cityData = {
  city_name: "Gondar",
  city_name_am: "ጎንደር",
  mayor_name: "John Doe",
  mayor_name_am: "ጆን ዶ",
  deputy_mayor: "Jane Smith", 
  deputy_mayor_am: "ጄን ስሚዝ",
  mayor_signature: signatureFile, // File object
  // ... other fields
};
```

### SubCity Registration with Amharic Names
```javascript
const subcityData = {
  name: "Zoble",
  name_am: "ዞብል",
  // ... other fields
};
```

### Kebele Registration with Amharic Names
```javascript
const kebeleData = {
  name: "Kebele 14",
  name_am: "ቀበሌ 14",
  // ... other fields
};
```

## API Changes

### City Registration Endpoint
**POST** `/api/tenants/cities/`

New fields added to request:
- `city_name_am` (optional)
- `mayor_name_am` (optional) 
- `mayor_signature` (file, optional)
- `deputy_mayor_am` (optional)

### SubCity Registration Endpoint
**POST** `/api/tenants/subcities/`

New fields added to request:
- `name_am` (optional)

### Kebele Registration Endpoint
**POST** `/api/tenants/kebeles/`

New fields added to request:
- `name_am` (optional)

## File Upload Handling

### Mayor Signature Upload
- Accepts image files (PNG, JPG, JPEG, GIF)
- Files stored in `media/signatures/` directory
- Recommended dimensions: 300x100px
- File size limit: Configurable via Django settings

### Frontend File Handling
```javascript
const handleMayorSignatureChange = (e) => {
  const file = e.target.files[0];
  if (file) {
    setMayorSignatureFile(file);
    onChange({ ...data, mayor_signature: file });
  }
};
```

## Validation

### Required Fields
- English names remain required for all tenant types
- Amharic names are optional but recommended
- Mayor signature is optional for city registration

### Field Constraints
- All name fields: max 100 characters
- Amharic fields support Unicode characters
- Image files validated for format and size

## Benefits

1. **Bilingual Support**: Full support for Amharic alongside English
2. **Cultural Relevance**: Better serves Ethiopian administrative needs
3. **Official Documentation**: Mayor signatures for official city documents
4. **User Experience**: Intuitive form layout with clear field labels
5. **Data Integrity**: Proper validation and storage of multilingual data

## Future Enhancements

1. **Additional Languages**: Support for other Ethiopian languages
2. **Digital Signatures**: Integration with digital signature services
3. **Signature Verification**: Automated signature validation
4. **Bulk Import**: CSV/Excel import with Amharic name support
5. **Reporting**: Bilingual reports and documents

## Migration Instructions

1. **Apply Migration**:
   ```bash
   python manage.py migrate tenants
   ```

2. **Update Frontend Dependencies**:
   ```bash
   npm install  # If any new dependencies were added
   ```

3. **Test Registration**:
   - Test city registration with Amharic names and signature upload
   - Test subcity and kebele registration with Amharic names
   - Verify file uploads work correctly

4. **Verify Database**:
   - Check that new fields are created in database tables
   - Verify file storage directory is created and writable

This enhancement provides comprehensive bilingual support for Ethiopian administrative structures while maintaining backward compatibility with existing data.
