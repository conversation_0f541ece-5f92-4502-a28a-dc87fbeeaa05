import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Typography,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  ToggleButton,
  ToggleButtonGroup,
  Divider
} from '@mui/material';
import {
  ArrowBack as BackIcon,
  Print as PrintIcon,
  Visibility as PreviewIcon,
  CreditCard as FrontIcon,
  CreditCardOff as BackSideIcon
} from '@mui/icons-material';
import axios from 'axios';
import { useAuth } from '../../contexts/AuthContext';
import { usePermissions } from '../../hooks/usePermissions';
import IDCardTemplate from '../../components/idcards/IDCardTemplate';

const IDCardPrintPreview = () => {
  const { id, tenantId } = useParams();
  const navigate = useNavigate();
  const { user, getTenantId } = useAuth();
  const { hasPermission } = usePermissions();

  const [idCard, setIdCard] = useState(null);
  const [loading, setLoading] = useState(true);
  const [printing, setPrinting] = useState(false);
  const [error, setError] = useState('');
  const [printDialogOpen, setPrintDialogOpen] = useState(false);
  const [cardSide, setCardSide] = useState('front'); // 'front' or 'back'

  // Get reliable tenant ID
  const getReliableTenantId = () => {
    let effectiveTenantId = tenantId || getTenantId();

    if (!effectiveTenantId) {
      try {
        const accessToken = localStorage.getItem('accessToken');
        if (accessToken) {
          const base64Url = accessToken.split('.')[1];
          const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
          const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
          }).join(''));
          const tokenData = JSON.parse(jsonPayload);
          effectiveTenantId = tokenData.tenant_id;
        }
      } catch (e) {
        console.warn('Could not decode token:', e);
      }
    }

    return effectiveTenantId;
  };

  const fetchIDCard = async () => {
    try {
      setLoading(true);
      setError('');

      const effectiveTenantId = getReliableTenantId();
      console.log('🔍 Fetching ID card for print preview:', { id, tenantId, effectiveTenantId, user });

      // Check authentication
      const accessToken = localStorage.getItem('accessToken');
      if (!accessToken) {
        throw new Error('No access token found. Please log in again.');
      }

      // Debug token and user info
      console.log('🔍 Authentication Debug:', {
        hasToken: !!accessToken,
        tokenLength: accessToken?.length,
        userRole: user?.role,
        userTenant: user?.tenant_id,
        hasPermission: hasPermission('print_idcards')
      });

      let response;
      if (tenantId) {
        // Cross-tenant context - subcity admin accessing kebele ID card
        // Use the subcity tenant ID (effectiveTenantId) to call cross-tenant endpoint
        console.log('🔄 Using cross-tenant endpoint from subcity context');
        try {
          response = await axios.get(`/api/tenants/${effectiveTenantId}/idcards/${id}/cross_tenant_detail/`);
        } catch (crossTenantError) {
          console.warn('Cross-tenant endpoint failed:', {
            status: crossTenantError.response?.status,
            statusText: crossTenantError.response?.statusText,
            data: crossTenantError.response?.data,
            url: crossTenantError.config?.url
          });
          // If cross-tenant fails, try using the kebele tenant ID directly
          console.log('🔄 Trying direct kebele tenant access');
          response = await axios.get(`/api/tenants/${tenantId}/idcards/${id}/`);
        }
      } else {
        // Regular tenant context
        console.log('🔄 Using regular tenant endpoint');
        response = await axios.get(`/api/tenants/${effectiveTenantId}/idcards/${id}/`);
      }

      const idCardData = response.data;

      // Fetch citizen details if needed
      if (idCardData.citizen_id && !idCardData.citizen_details) {
        try {
          let citizenData;
          if (tenantId && idCardData.kebele_tenant) {
            const citizenResponse = await axios.get(`/api/tenants/${idCardData.kebele_tenant.id}/citizens/${idCardData.citizen_id}/`);
            citizenData = citizenResponse.data;
          } else {
            const citizenResponse = await axios.get(`/api/tenants/${effectiveTenantId}/citizens/${idCardData.citizen_id}/`);
            citizenData = citizenResponse.data;
          }

          idCardData.citizen = citizenData;
        } catch (citizenError) {
          console.warn('Could not fetch citizen details:', citizenError);
        }
      } else if (idCardData.citizen_details) {
        idCardData.citizen = idCardData.citizen_details;
      }

      setIdCard(idCardData);
    } catch (error) {
      console.error('Error fetching ID card:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        url: error.config?.url
      });

      let errorMessage = 'Failed to load ID card for preview';
      if (error.response?.status === 401) {
        errorMessage = 'Authentication failed. Please log in again.';
      } else if (error.response?.status === 403) {
        errorMessage = 'You do not have permission to view this ID card.';
      } else if (error.response?.status === 404) {
        errorMessage = 'ID card not found.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleConfirmPrint = async () => {
    try {
      setPrinting(true);

      const effectiveTenantId = getReliableTenantId();
      const printTenantId = idCard.kebele_tenant?.id || effectiveTenantId;

      console.log('🖨️ Printing ID card:', { id, printTenantId });

      await axios.post(`/api/tenants/${printTenantId}/idcards/${id}/print/`);

      setPrintDialogOpen(false);

      // Close popup window after successful print
      window.close();

    } catch (error) {
      console.error('Error printing ID card:', error);
      setError('Failed to print ID card');
    } finally {
      setPrinting(false);
    }
  };

  const handleBack = () => {
    // Close popup window
    window.close();
  };

  useEffect(() => {
    fetchIDCard();
  }, [id, tenantId]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={3}>
        <Alert severity="error">{error}</Alert>
        <Button startIcon={<BackIcon />} onClick={handleBack} sx={{ mt: 2 }}>
          Back
        </Button>
      </Box>
    );
  }

  if (!idCard) {
    return (
      <Box p={3}>
        <Alert severity="warning">ID card not found</Alert>
        <Button startIcon={<BackIcon />} onClick={handleBack} sx={{ mt: 2 }}>
          Back
        </Button>
      </Box>
    );
  }

  // Check permissions
  if (!hasPermission('print_idcards')) {
    return (
      <Box p={3}>
        <Alert severity="error">You don't have permission to print ID cards</Alert>
        <Button startIcon={<BackIcon />} onClick={handleBack} sx={{ mt: 2 }}>
          Back
        </Button>
      </Box>
    );
  }

  return (
    <Box p={3}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          <PreviewIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          Print Preview
        </Typography>

        <Box>
          <Button
            variant="outlined"
            startIcon={<BackIcon />}
            onClick={handleBack}
            sx={{ mr: 2 }}
          >
            Close
          </Button>

          <Button
            variant="contained"
            color="primary"
            startIcon={<PrintIcon />}
            onClick={() => setPrintDialogOpen(true)}
            disabled={idCard.status !== 'approved'}
          >
            Print ID Card
          </Button>
        </Box>
      </Box>

      {/* ID Card Info */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          ID Card Information
        </Typography>
        <Typography variant="body1">
          <strong>Citizen:</strong> {idCard.citizen?.get_full_name || idCard.citizen?.first_name + ' ' + idCard.citizen?.last_name}
        </Typography>
        <Typography variant="body1">
          <strong>Card Number:</strong> {idCard.card_number}
        </Typography>
        <Typography variant="body1">
          <strong>Status:</strong> {idCard.status}
        </Typography>
        {idCard.kebele_tenant && (
          <Typography variant="body1">
            <strong>Kebele:</strong> {idCard.kebele_tenant.name}
          </Typography>
        )}
      </Paper>

      {/* Card Side Toggle */}
      <Box display="flex" justifyContent="center" mb={3}>
        <ToggleButtonGroup
          value={cardSide}
          exclusive
          onChange={(event, newSide) => {
            if (newSide !== null) {
              setCardSide(newSide);
            }
          }}
          aria-label="card side"
        >
          <ToggleButton value="front" aria-label="front side">
            <FrontIcon sx={{ mr: 1 }} />
            Front Side
          </ToggleButton>
          <ToggleButton value="back" aria-label="back side">
            <BackSideIcon sx={{ mr: 1 }} />
            Back Side
          </ToggleButton>
        </ToggleButtonGroup>
      </Box>

      {/* ID Card Preview */}
      <Box display="flex" justifyContent="center" mb={3}>
        <Paper
          elevation={3}
          sx={{
            p: 2,
            backgroundColor: '#f5f5f5',
            maxWidth: '600px',
            width: '100%'
          }}
        >
          <IDCardTemplate
            idCard={idCard}
            side={cardSide}
            preview={true}
          />
        </Paper>
      </Box>

      {/* Print Confirmation Dialog */}
      <Dialog open={printDialogOpen} onClose={() => setPrintDialogOpen(false)}>
        <DialogTitle>Confirm Print</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to print this ID card for{' '}
            <strong>{idCard.citizen?.get_full_name || idCard.citizen?.first_name + ' ' + idCard.citizen?.last_name}</strong>?
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            This action will mark the ID card as printed and it will be removed from the printing queue.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPrintDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirmPrint}
            variant="contained"
            disabled={printing}
            startIcon={printing ? <CircularProgress size={20} /> : <PrintIcon />}
          >
            {printing ? 'Printing...' : 'Confirm Print'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default IDCardPrintPreview;
