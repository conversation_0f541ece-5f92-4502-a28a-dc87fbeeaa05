@echo off
echo ========================================
echo   COMPLETE JAR BRIDGE SETUP
echo   One-Click Setup for GoID Integration
echo ========================================
echo.

echo STEP 1: Installing Python requirements...
echo.
pip install requests flask flask-cors
echo.

echo STEP 2: Testing setup...
echo.
python simple_test.py
echo.

echo STEP 3: Starting JAR Bridge Service...
echo.
echo The JAR Bridge service will start in a new window.
echo Keep that window open for the service to work.
echo.

start "JAR Bridge Service" cmd /k "python working_jar_bridge.py"

echo.
echo ✅ Setup completed!
echo.
echo Service Status:
echo   - JAR Bridge Service: Starting in new window
echo   - Service URL: http://localhost:8001
echo   - Ready for GoID integration
echo.
echo Next Steps:
echo   1. Keep the JAR Bridge service window open
echo   2. Start your GoID system: docker-compose up
echo   3. Open GoID frontend: http://localhost:3000
echo   4. Test fingerprint capture in citizen registration
echo.

pause
