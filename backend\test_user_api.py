#!/usr/bin/env python
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append('/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from django_tenants.utils import schema_context
from users.serializers import UserSerializer
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()

def test_user_api():
    print('=== Testing User API Endpoints ===')
    
    # Test with clerk user in kebele15
    with schema_context('kebele_kebele15'):
        try:
            user = User.objects.get(email='<EMAIL>')
            print(f'\nTesting with: {user.email} (Role: {user.role})')
            
            # Test serializer directly
            print('\n--- Direct Serializer Test ---')
            serializer = UserSerializer(user)
            data = serializer.data
            
            print(f'User ID: {data.get("id")}')
            print(f'Email: {data.get("email")}')
            print(f'Role: {data.get("role")}')
            print(f'Groups: {len(data.get("groups", []))}')
            print(f'Permissions: {len(data.get("permissions", []))}')
            
            if data.get('permissions'):
                print('\nFirst 10 permissions:')
                for perm in data.get('permissions', [])[:10]:
                    print(f'  - {perm["codename"]}: {perm["name"]}')
            else:
                print('❌ No permissions found in serializer!')
            
            # Test API endpoint
            print('\n--- API Endpoint Test ---')
            client = APIClient()
            
            # Get JWT token
            refresh = RefreshToken.for_user(user)
            access_token = str(refresh.access_token)
            
            # Set authorization header
            client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
            
            # Test the user detail endpoint
            response = client.get(f'/api/users/{user.id}/')
            print(f'API Response Status: {response.status_code}')
            
            if response.status_code == 200:
                api_data = response.json()
                print(f'API User ID: {api_data.get("id")}')
                print(f'API Email: {api_data.get("email")}')
                print(f'API Role: {api_data.get("role")}')
                print(f'API Groups: {len(api_data.get("groups", []))}')
                print(f'API Permissions: {len(api_data.get("permissions", []))}')
                
                if api_data.get('permissions'):
                    print('\nAPI First 5 permissions:')
                    for perm in api_data.get('permissions', [])[:5]:
                        print(f'  - {perm["codename"]}: {perm["name"]}')
                else:
                    print('❌ No permissions found in API response!')
            else:
                print(f'❌ API Error: {response.status_code}')
                print(f'Response: {response.content}')
            
        except User.DoesNotExist:
            print('<EMAIL> not found')
        except Exception as e:
            print(f'Error: {e}')
            import traceback
            traceback.print_exc()

    # Test with kebele leader user
    with schema_context('kebele_kebele15'):
        try:
            user = User.objects.get(email='<EMAIL>')
            print(f'\n\nTesting with: {user.email} (Role: {user.role})')
            
            # Test serializer directly
            serializer = UserSerializer(user)
            data = serializer.data
            
            print(f'Groups: {len(data.get("groups", []))}')
            print(f'Permissions: {len(data.get("permissions", []))}')
            
            if data.get('permissions'):
                print('\nFirst 5 permissions:')
                for perm in data.get('permissions', [])[:5]:
                    print(f'  - {perm["codename"]}: {perm["name"]}')
            
        except User.DoesNotExist:
            print('<EMAIL> not found')
        except Exception as e:
            print(f'Error: {e}')

if __name__ == '__main__':
    test_user_api()
