from rest_framework import serializers
from django.db import transaction
from ..models.subcity import SubCity
from ..models.city import CityAdministration
from ..models.tenant import Tenant, TenantType

class SubCitySerializer(serializers.ModelSerializer):
    city_name = serializers.CharField(source='city.city_name', read_only=True)

    class Meta:
        model = SubCity
        fields = (
            'id', 'name', 'name_am', 'code', 'city', 'city_name', 'is_active',
            'tenant', 'logo', 'mayor_signature', 'pattern_image', 'created_at', 'updated_at'
        )
        read_only_fields = ('code', 'created_at', 'updated_at')

class SubCityRegistrationSerializer(serializers.ModelSerializer):
    """Serializer for registering a new subcity with its tenant."""
    tenant_name = serializers.CharField(write_only=True)
    city_id = serializers.IntegerField(write_only=True)
    tenant_id = serializers.IntegerField(read_only=True)
    domain_name = serializers.CharField(write_only=True, required=False)  # Accept but ignore
    code = serializers.CharField(write_only=True, required=False)  # Accept but ignore

    class Meta:
        model = SubCity
        fields = (
            'name', 'name_am', 'tenant_name', 'city_id', 'is_active', 'tenant_id',
            'domain_name', 'code', 'logo', 'mayor_signature', 'pattern_image'
        )

    def validate_name(self, value):
        """Validate that the subcity name is unique."""
        if SubCity.objects.filter(name=value).exists():
            raise serializers.ValidationError(f"A subcity with the name '{value}' already exists.")
        return value

    def validate_city_id(self, value):
        """Validate that the city_id refers to a valid city tenant."""
        try:
            # Check if it's a tenant ID for a city
            city_tenant = Tenant.objects.get(id=value, type=TenantType.CITY)
            # Note: We don't require CityAdministration profile to exist
            # Some city tenants might not have profiles (like seeded data)
        except Tenant.DoesNotExist:
            raise serializers.ValidationError("City tenant does not exist")
        return value

    @transaction.atomic
    def create(self, validated_data):
        from django.core.management import call_command
        from django_tenants.utils import schema_context
        from users.models import User

        tenant_name = validated_data.pop('tenant_name')
        city_tenant_id = validated_data.pop('city_id')
        # Extract domain_name before removing it
        user_domain_name = validated_data.pop('domain_name', None)
        # Remove fields that are not part of the SubCity model
        validated_data.pop('code', None)  # Remove if present

        # Get city tenant and city administration (if exists)
        city_tenant = Tenant.objects.get(id=city_tenant_id, type=TenantType.CITY)
        city_admin = CityAdministration.objects.filter(tenant=city_tenant).first()

        # Generate schema name
        base_schema_name = f"subcity_{tenant_name.lower().replace(' ', '_')}"
        schema_name = base_schema_name

        # Ensure schema name is unique
        count = 1
        while Tenant.objects.filter(schema_name=schema_name).exists():
            schema_name = f"{base_schema_name}_{count}"
            count += 1

        # Create tenant
        tenant = Tenant.objects.create(
            name=tenant_name,
            type=TenantType.SUBCITY,
            parent=city_tenant,
            schema_name=schema_name
        )

        # Create domain for the tenant using user-provided domain name
        from ..models.tenant import Domain
        # Use the user-provided domain_name if available, otherwise fall back to schema-based name
        if user_domain_name:
            domain_name = user_domain_name
        else:
            domain_name = f"{schema_name}.goid.local"
        Domain.objects.create(domain=domain_name, tenant=tenant, is_primary=True)

        # Run migrations for the new tenant schema
        with schema_context(schema_name):
            call_command('migrate', verbosity=0)

        # Create subcity profile
        # Only pass fields that exist in the SubCity model
        subcity_data = {
            'tenant': tenant,
            'name': validated_data.get('name'),
            'name_am': validated_data.get('name_am'),
            'city': city_admin,
            'is_active': validated_data.get('is_active', True),
            'logo': validated_data.get('logo'),
            'mayor_signature': validated_data.get('mayor_signature'),
            'pattern_image': validated_data.get('pattern_image')
        }
        subcity = SubCity.objects.create(**subcity_data)

        return subcity

    def to_representation(self, instance):
        """Add tenant_id and domain_name to the serialized output."""
        data = super().to_representation(instance)
        if instance.tenant:
            data['tenant_id'] = instance.tenant.id
            # Get domain name from tenant's domains
            domain = instance.tenant.domains.filter(is_primary=True).first()
            data['domain_name'] = domain.domain if domain else None
        # Remove fields that were only for input
        data.pop('code', None)
        return data
