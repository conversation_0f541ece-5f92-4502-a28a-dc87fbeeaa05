@echo off
echo ========================================
echo    OFFICIAL CAPTURE BRIDGE
echo ========================================
echo.
echo BRILLIANT SOLUTION: USE OFFICIAL SOFTWARE
echo.
echo HOW IT WORKS:
echo   1. Uses official Futronic software for capture
echo   2. Official software captures real fingerprints perfectly
echo   3. Bridge imports captured data into GoID system
echo   4. Best of both worlds - official capture + GoID integration
echo.
echo ADVANTAGES:
echo   - REAL fingerprint capture (no simulation)
echo   - Uses working official software
echo   - Highest quality fingerprint data
echo   - No Python/SDK compatibility issues
echo   - Perfect for production use
echo.
echo PROCESS:
echo   1. Click capture in web interface
echo   2. Official software opens automatically
echo   3. Use official software to capture fingerprint
echo   4. Save fingerprint image
echo   5. Bridge imports real data to GoID database
echo.
echo REQUIREMENTS:
echo   - Official Futronic software (WorkedEx.exe or ftrScanApiEx.exe)
echo   - Place official software in service directory
echo   - Or install official Futronic SDK
echo.

python official_capture_bridge.py

echo.
echo Official capture bridge stopped.
pause
