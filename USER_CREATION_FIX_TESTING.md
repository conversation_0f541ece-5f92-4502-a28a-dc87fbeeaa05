# User Creation Role Assignment Fix - Testing Guide 🧪

## 🎯 **Issue Fixed**

**Problem**: When creating a user with "kebele_leader" group permissions, the system was automatically assigning the "clerk" role instead of respecting the group-based permissions.

**Root Cause**: The frontend user creation form was hardcoding the role to 'clerk' regardless of the selected group.

## 🔧 **Fix Applied**

### **Frontend Changes (KebeleUserManagement.jsx)**

#### **1. User Creation Logic Updated**
```javascript
// BEFORE: Hardcoded role
const userData = {
  ...newUser,
  username,
  password2: newUser.password,
  tenant: selectedKebele.id,
  role: 'clerk' // ❌ Always clerk
};

// AFTER: Dynamic role based on group
// Determine role based on selected group
let userRole = 'clerk'; // Default role
if (newUser.primary_group_id) {
  const selectedGroup = tenantGroups.find(g => g.id === parseInt(newUser.primary_group_id));
  if (selectedGroup) {
    // Map group types to roles
    if (selectedGroup.name.toLowerCase().includes('leader') || 
        selectedGroup.group_type === 'administrative' || 
        selectedGroup.level >= 30) {
      userRole = 'kebele_leader';
    } else if (selectedGroup.name.toLowerCase().includes('admin')) {
      userRole = 'kebele_admin';
    }
    // Otherwise keep default 'clerk'
  }
}

const userData = {
  ...newUser,
  username,
  password2: newUser.password,
  tenant: selectedKebele.id,
  role: userRole // ✅ Use determined role
};
```

#### **2. Role Mapping Logic**
```javascript
// Group → Role Mapping Rules:
// 1. Group name contains "leader" → kebele_leader
// 2. Group type is "administrative" → kebele_leader  
// 3. Group level >= 30 → kebele_leader
// 4. Group name contains "admin" → kebele_admin
// 5. Default → clerk
```

#### **3. User Update Logic Also Fixed**
```javascript
// Same logic applied to user updates
// When editing a user and changing their group, role is automatically updated
```

## 🧪 **Testing Steps**

### **Test 1: Create User with Kebele Leader Group**

#### **Step 1: Access User Management**
```
1. Login as subcity admin (e.g., subcity user)
2. Navigate to: /users/kebele-management
3. Select a kebele from the dropdown
4. Click "Create User" button
```

#### **Step 2: Fill User Form**
```
Username: testleader
Email: <EMAIL>
First Name: Test
Last Name: Leader
Password: password123
Primary Group: Select "Kebele Leaders" (or similar administrative group)
```

#### **Step 3: Submit and Verify**
```
1. Click "Create User"
2. Check the created user in the users list
3. Expected Result: User should have role = 'kebele_leader'
4. Expected Result: User should be in the selected group
```

### **Test 2: Create User with Clerk Group**

#### **Step 1: Create Clerk User**
```
Username: testclerk
Email: <EMAIL>
First Name: Test
Last Name: Clerk
Password: password123
Primary Group: Select "Kebele Clerks" (or similar operational group)
```

#### **Step 2: Verify**
```
Expected Result: User should have role = 'clerk'
Expected Result: User should be in the clerk group
```

### **Test 3: Edit User Group and Verify Role Update**

#### **Step 1: Edit Existing User**
```
1. Select a user with 'clerk' role
2. Click "Edit" button
3. Change Primary Group to "Kebele Leaders"
4. Save changes
```

#### **Step 2: Verify**
```
Expected Result: User role should automatically change to 'kebele_leader'
Expected Result: User should be moved to the new group
```

## 🔍 **Verification Methods**

### **Method 1: Frontend User List**
```
1. Check the user list in the kebele management page
2. Look at the "Role" column
3. Verify it shows the correct role based on the group
```

### **Method 2: Backend Database Check**
```python
# Run in Django shell
from users.models import User
from tenants.models import Tenant

kebele = Tenant.objects.get(name='Kebele15')  # or your test kebele
user = User.objects.get(email='<EMAIL>', tenant=kebele)

print(f"User: {user.email}")
print(f"Role: {user.role}")
print(f"Primary Group: {user.get_primary_group()}")
print(f"All Groups: {[g.name for g in user.get_groups()]}")
```

### **Method 3: Login Test**
```
1. Login as the newly created user
2. Check the navigation menu
3. Kebele leaders should see Transfer/Clearance menus
4. Clerks should see limited menus
```

## 📊 **Expected Results**

### **✅ Kebele Leader User**
```
Role: 'kebele_leader'
Group: Administrative group (level >= 30)
Navigation: Full kebele leader menus (Transfers, Clearances, etc.)
Permissions: Can approve transfers, create clearances, etc.
```

### **✅ Clerk User**
```
Role: 'clerk'
Group: Operational group (level < 30)
Navigation: Basic clerk menus
Permissions: Basic citizen management, limited ID card operations
```

### **✅ Admin User**
```
Role: 'kebele_admin'
Group: Group with "admin" in name
Navigation: Administrative menus
Permissions: User management, advanced operations
```

## 🚨 **Troubleshooting**

### **If Role Still Shows 'clerk'**
```
1. Check browser console for JavaScript errors
2. Verify the group selection is working
3. Check if tenantGroups array is populated
4. Verify the group mapping logic conditions
```

### **If Group Assignment Fails**
```
1. Check backend logs for group assignment errors
2. Verify the group exists in the public schema
3. Check if user has proper tenant assignment
4. Verify group permissions in database
```

### **If Navigation Doesn't Update**
```
1. Clear browser cache and refresh
2. Check if user context is updated after creation
3. Verify JWT token contains correct role
4. Check navigation permission logic
```

## 🎉 **Success Criteria**

### **✅ All Tests Pass When:**
```
1. Users created with "Kebele Leaders" group get 'kebele_leader' role
2. Users created with "Kebele Clerks" group get 'clerk' role
3. Users created with admin groups get 'kebele_admin' role
4. Role updates automatically when group is changed
5. Navigation menus reflect the correct permissions
6. Transfer approval buttons work for kebele leaders
7. No JavaScript errors in browser console
8. Backend logs show successful group assignments
```

---

**The fix ensures that user roles are automatically determined based on their group assignments, providing a seamless integration between the group-based permission system and the legacy role system.** 🚀

**Key Benefits:**
- ✅ **Automatic role assignment** based on group selection
- ✅ **Consistent permissions** between groups and roles  
- ✅ **Backward compatibility** with existing role-based checks
- ✅ **Dynamic updates** when groups are changed
- ✅ **Proper navigation** based on actual permissions
