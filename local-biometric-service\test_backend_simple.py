#!/usr/bin/env python3
"""
Simple Backend Test
"""

import urllib.request
import json

def test_backend_endpoints():
    """Test various backend endpoints"""
    
    print("🔍 Testing GoID Backend Endpoints...")
    print("=" * 40)
    
    # Test basic endpoints
    endpoints = [
        ("Root", "http://localhost:8000/"),
        ("Admin", "http://localhost:8000/admin/"),
        ("API Root", "http://localhost:8000/api/"),
        ("Biometrics", "http://localhost:8000/api/biometrics/"),
        ("Citizens", "http://localhost:8000/api/citizens/"),
        ("Health", "http://localhost:8000/health/"),
        ("Status", "http://localhost:8000/status/"),
    ]
    
    for name, url in endpoints:
        try:
            response = urllib.request.urlopen(url, timeout=5)
            content = response.read().decode()[:200]  # First 200 chars
            print(f"✅ {name}: {response.getcode()} - {content[:50]}...")
        except urllib.error.HTTPError as e:
            print(f"⚠️ {name}: HTTP {e.code}")
        except urllib.error.URLError as e:
            print(f"❌ {name}: Connection failed")
        except Exception as e:
            print(f"❌ {name}: {e}")

def test_duplicate_detection_simple():
    """Test duplicate detection with minimal data"""
    
    print(f"\n🔍 Testing Duplicate Detection API...")
    print("=" * 40)
    
    # Simple test data
    test_data = {
        "left_thumb_fingerprint": "test_template_left",
        "right_thumb_fingerprint": "test_template_right"
    }
    
    # Try different endpoints
    endpoints = [
        "http://localhost:8000/api/biometrics/check-duplicates/",
        "http://localhost:8000/api/biometric/check-duplicates/",
        "http://localhost:8000/api/duplicates/",
        "http://localhost:8000/biometrics/check-duplicates/",
    ]
    
    for endpoint in endpoints:
        print(f"\n📡 Testing: {endpoint}")
        
        try:
            req_data = json.dumps(test_data).encode()
            
            req = urllib.request.Request(
                endpoint,
                data=req_data,
                headers={
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            )
            
            response = urllib.request.urlopen(req, timeout=10)
            result = response.read().decode()
            
            print(f"   ✅ Success: {response.getcode()}")
            print(f"   Response: {result[:100]}...")
            
            return True
            
        except urllib.error.HTTPError as e:
            error_content = e.read().decode() if hasattr(e, 'read') else str(e)
            print(f"   ⚠️ HTTP {e.code}: {error_content[:100]}...")
        except urllib.error.URLError as e:
            print(f"   ❌ Connection error: {e}")
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    return False

def check_django_urls():
    """Check Django URL configuration"""
    
    print(f"\n🔍 Checking Django URL Configuration...")
    print("=" * 40)
    
    try:
        # Try to get URL patterns info
        response = urllib.request.urlopen("http://localhost:8000/", timeout=5)
        
        if response.getcode() == 200:
            print("✅ Django server is responding")
            
            # Check if we can access admin
            try:
                admin_response = urllib.request.urlopen("http://localhost:8000/admin/", timeout=5)
                print(f"✅ Admin interface accessible: {admin_response.getcode()}")
            except:
                print("⚠️ Admin interface may have issues")
            
            # Check API root
            try:
                api_response = urllib.request.urlopen("http://localhost:8000/api/", timeout=5)
                print(f"✅ API root accessible: {api_response.getcode()}")
            except urllib.error.HTTPError as e:
                print(f"⚠️ API root: HTTP {e.code}")
            except:
                print("❌ API root not accessible")
        
    except Exception as e:
        print(f"❌ Django server not responding: {e}")

def main():
    """Main function"""
    
    print("GoID Backend Simple Test")
    print("=" * 25)
    
    # Test basic connectivity
    test_backend_endpoints()
    
    # Check Django configuration
    check_django_urls()
    
    # Test duplicate detection
    if test_duplicate_detection_simple():
        print(f"\n🎉 SUCCESS!")
        print(f"✅ Duplicate detection API is working")
    else:
        print(f"\n❌ DUPLICATE DETECTION ISSUES")
        print(f"💡 Possible solutions:")
        print(f"1. Check Django URL configuration")
        print(f"2. Ensure biometrics app is installed")
        print(f"3. Run migrations: python manage.py migrate")
        print(f"4. Check API endpoint names")
    
    print(f"\n📋 Backend Status Summary:")
    print(f"✅ Django server is running on localhost:8000")
    print(f"⚠️ Static files need configuration (admin CSS/JS missing)")
    print(f"⚠️ Authentication may be required for API endpoints")
    
    print(f"\n💡 Next steps:")
    print(f"1. Fix static files: python manage.py collectstatic")
    print(f"2. Create superuser: python manage.py createsuperuser")
    print(f"3. Check API URL configuration")
    print(f"4. Test with authentication")
    
    input("\nPress Enter to exit...")

if __name__ == '__main__':
    main()
