#!/usr/bin/env python
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append('/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from tenants.models import Tenant
from django_tenants.utils import schema_context
from users.serializers import UserSerializer

User = get_user_model()

print('=== Testing UserSerializer with new group model ===')

# First, let's see which users actually have group memberships
from users.models_groups import GroupMembership
print('Users with group memberships:')
for membership in GroupMembership.objects.all():
    print(f'  {membership.user_email} -> {membership.group.name} (Primary: {membership.is_primary})')

print('\n=== Testing <EMAIL> (should have clerk group) ===')
# <NAME_EMAIL> who should now have a group
tenant = Tenant.objects.get(name='<PERSON><PERSON><PERSON><PERSON>')
with schema_context(tenant.schema_name):
    try:
        user = User.objects.get(email='<EMAIL>')
        print(f'User: {user.email}')

        groups = user.get_user_groups()
        print(f'Groups found: {len(groups)}')
        for group in groups:
            print(f'  - {group.name} (ID: {group.id})')

        # Test primary group
        primary = user.get_primary_group()
        print(f'Primary group: {primary.name if primary else None}')

        # Test UserSerializer
        serializer = UserSerializer(user)
        data = serializer.data
        print(f'Serializer groups: {len(data.get("groups", []))}')
        for group in data.get('groups', []):
            print(f'  - {group["name"]} (Primary: {group["is_primary"]})')

    except User.DoesNotExist:
        print('User <EMAIL> not found')
    except Exception as e:
        print(f'Error: {e}')

print('\n=== Testing <EMAIL> (should have multiple groups) ===')
# <NAME_EMAIL> who should have multiple groups
try:
    user = User.objects.get(email='<EMAIL>')
    print(f'User: {user.email}')

    groups = user.get_user_groups()
    print(f'Groups found: {len(groups)}')
    for group in groups:
        print(f'  - {group.name} (ID: {group.id})')

    # Test primary group
    primary = user.get_primary_group()
    print(f'Primary group: {primary.name if primary else None}')

    # Test UserSerializer
    serializer = UserSerializer(user)
    data = serializer.data
    print(f'Serializer groups: {len(data.get("groups", []))}')
    for group in data.get('groups', []):
        print(f'  - {group["name"]} (Primary: {group["is_primary"]})')

except User.DoesNotExist:
    print('User <EMAIL> not found')
except Exception as e:
    print(f'Error: {e}')
