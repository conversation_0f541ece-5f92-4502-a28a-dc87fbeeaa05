#!/bin/bash

# Complete Tenant List Fix Script
# This script comprehensively fixes tenant list issues

echo "🔧 Complete Tenant List Fix"
echo "=========================="

# Step 1: Diagnose current state
echo "📍 Step 1: Diagnosing current state..."
python diagnose_tenant_list_issue.py

echo ""
echo "📍 Step 2: Restarting services in correct order..."

# Stop all services
docker-compose down

# Start database first
echo "Starting database..."
docker-compose up -d db
sleep 15

# Start backend
echo "Starting backend..."
docker-compose up -d backend
sleep 30

# Check if backend is responding
echo "Testing backend response..."
BACKEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://************:8000/admin/ || echo "000")

if [ "$BACKEND_STATUS" = "000" ]; then
    echo "❌ Backend not responding, checking logs..."
    docker-compose logs --tail=20 backend
    echo ""
    echo "Trying to restart backend..."
    docker-compose restart backend
    sleep 30
fi

# Step 3: Run migrations
echo ""
echo "📍 Step 3: Running database migrations..."
docker-compose exec backend python manage.py migrate_schemas --shared
docker-compose exec backend python manage.py migrate_schemas

# Step 4: Create superuser
echo ""
echo "📍 Step 4: Ensuring superuser exists..."
docker-compose exec -T backend python manage.py shell << 'EOF'
from django.contrib.auth import get_user_model
User = get_user_model()

try:
    user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'username': 'admin',
            'first_name': 'Super',
            'last_name': 'Admin',
            'is_superuser': True,
            'is_staff': True,
            'is_active': True
        }
    )
    user.set_password('admin123')
    user.is_superuser = True
    user.is_staff = True
    user.is_active = True
    user.save()
    
    if created:
        print(f"✅ Superuser created: {user.email}")
    else:
        print(f"✅ Superuser updated: {user.email}")
        
except Exception as e:
    print(f"❌ Error with superuser: {e}")
EOF

# Step 5: Create test tenants if database is empty
echo ""
echo "📍 Step 5: Creating test tenants..."
python create_test_tenants.py

# Step 6: Start frontend
echo ""
echo "📍 Step 6: Starting frontend..."
docker-compose up -d frontend
sleep 20

# Step 7: Final comprehensive test
echo ""
echo "📍 Step 7: Final comprehensive test..."
python diagnose_tenant_list_issue.py

# Step 8: Test specific endpoints
echo ""
echo "📍 Step 8: Testing specific endpoints..."

# Get auth token
echo "Getting authentication token..."
AUTH_RESPONSE=$(curl -s -X POST http://************:8000/api/auth/token/ \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}')

if echo "$AUTH_RESPONSE" | grep -q "access"; then
    echo "✅ Authentication successful"
    
    # Extract token
    TOKEN=$(echo "$AUTH_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin)['access'])" 2>/dev/null)
    
    if [ ! -z "$TOKEN" ]; then
        echo "Testing tenant endpoints with token..."
        
        # Test tenants endpoint
        echo "Testing /api/tenants/..."
        TENANTS_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" http://************:8000/api/tenants/)
        echo "Response: $TENANTS_RESPONSE"
        
        if echo "$TENANTS_RESPONSE" | grep -q -E '\[|\{'; then
            echo "✅ Tenants endpoint returning data"
        else
            echo "❌ Tenants endpoint not returning proper data"
        fi
        
        # Test subcities endpoint
        echo ""
        echo "Testing /api/tenants/subcities/..."
        SUBCITIES_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" http://************:8000/api/tenants/subcities/)
        echo "Response: $SUBCITIES_RESPONSE"
        
        # Test kebeles endpoint
        echo ""
        echo "Testing /api/tenants/kebeles/..."
        KEBELES_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" http://************:8000/api/tenants/kebeles/)
        echo "Response: $KEBELES_RESPONSE"
        
    else
        echo "❌ Could not extract token from response"
    fi
else
    echo "❌ Authentication failed"
    echo "Response: $AUTH_RESPONSE"
fi

# Step 9: Show final status
echo ""
echo "📍 Step 9: Final status check..."
echo "Container status:"
docker-compose ps

echo ""
echo "✅ Complete tenant list fix completed!"
echo ""
echo "🧪 Manual testing steps:"
echo "1. Open: http://************:3000/"
echo "2. Login with: <EMAIL> / admin123"
echo "3. Navigate to: Tenants section"
echo "4. Check: Browser console for errors"
echo ""
echo "🔍 Expected results:"
echo "- ✅ Login should work without 500 errors"
echo "- ✅ Tenants page should load without 404 errors"
echo "- ✅ Should see list of tenants (at least test tenants)"
echo "- ✅ No ERR_EMPTY_RESPONSE errors"
echo ""
echo "💡 If still not working:"
echo "1. Check browser console for specific errors"
echo "2. Check network tab for failed requests"
echo "3. Run: docker-compose logs backend | grep ERROR"
echo "4. Try: docker-compose restart"
echo ""
echo "🚨 If completely broken:"
echo "docker-compose down -v && docker-compose up -d"
echo "(Warning: This will delete all data)"
