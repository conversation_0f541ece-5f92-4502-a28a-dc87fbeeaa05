#!/bin/bash

# Fix Tenant URL Routing Script
# This script fixes the URL routing issue causing API root instead of tenant list

echo "🔧 Fixing Tenant URL Routing Issue"
echo "=================================="

echo "📍 Issue Found:"
echo "- Frontend calls: /api/tenants/"
echo "- Backend was routing to: /api/tenants/tenants/ (wrong)"
echo "- Result: API root response instead of tenant list"
echo ""
echo "📍 Fix Applied:"
echo "- Changed router.register(r'tenants', TenantViewSet)"
echo "- To: router.register(r'', TenantViewSet, basename='tenant')"
echo "- Now /api/tenants/ correctly routes to TenantViewSet"
echo ""

# Step 1: Restart backend to apply URL routing changes
echo "📍 Step 1: Restarting backend to apply URL routing changes..."
docker-compose restart backend
sleep 30

# Step 2: Test the fixed URL routing
echo ""
echo "📍 Step 2: Testing fixed URL routing..."

echo "Getting authentication token..."
AUTH_RESPONSE=$(curl -s -X POST http://************:8000/api/auth/token/ \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}')

if echo "$AUTH_RESPONSE" | grep -q "access"; then
    echo "✅ Authentication successful"
    
    # Extract token
    TOKEN=$(echo "$AUTH_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin)['access'])" 2>/dev/null)
    
    if [ ! -z "$TOKEN" ]; then
        echo ""
        echo "Testing /api/tenants/ endpoint..."
        TENANT_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
          -H "Content-Type: application/json" \
          http://************:8000/api/tenants/)
        
        echo "API Response:"
        echo "$TENANT_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$TENANT_RESPONSE"
        
        # Check if we got actual tenant data (not API root)
        if echo "$TENANT_RESPONSE" | grep -q '"name".*"type"'; then
            echo ""
            echo "✅ SUCCESS: API now returns actual tenant data!"
            
            # Count tenants
            TENANT_COUNT=$(echo "$TENANT_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, list):
        print(len(data))
    elif isinstance(data, dict) and 'results' in data:
        print(len(data.get('results', [])))
    else:
        print('0')
except:
    print('0')
" 2>/dev/null)
            
            echo "Found $TENANT_COUNT tenants in response"
            
        elif echo "$TENANT_RESPONSE" | grep -q '"tenants":.*"subcities"'; then
            echo ""
            echo "❌ STILL GETTING API ROOT: Response contains endpoint links, not tenant data"
            echo "The URL routing fix may not have taken effect yet"
            
        else
            echo ""
            echo "❌ UNEXPECTED RESPONSE: Not tenant data or API root"
        fi
        
        # Test the old problematic URL for comparison
        echo ""
        echo "Testing old problematic URL /api/tenants/tenants/..."
        OLD_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
          http://************:8000/api/tenants/tenants/ 2>/dev/null)
        
        if echo "$OLD_RESPONSE" | grep -q '"name".*"type"'; then
            echo "⚠️  Old URL still works - this might indicate routing conflict"
        else
            echo "✅ Old URL no longer works - routing fix successful"
        fi
        
    else
        echo "❌ Could not extract token"
    fi
else
    echo "❌ Authentication failed"
    echo "Response: $AUTH_RESPONSE"
fi

# Step 3: Check backend logs for any routing errors
echo ""
echo "📍 Step 3: Checking backend logs for routing info..."
docker-compose logs --tail=20 backend | grep -E "TenantViewSet|routing|URL|tenant" || echo "No relevant logs found"

# Step 4: Test URL resolution
echo ""
echo "📍 Step 4: Testing URL resolution in Django..."
docker-compose exec -T backend python manage.py shell << 'EOF'
from django.urls import resolve, reverse

print("=== URL RESOLUTION TEST ===")

# Test the main tenant URL
try:
    match = resolve('/api/tenants/')
    print(f"✅ /api/tenants/ resolves to: {match.func}")
    print(f"   View name: {match.view_name}")
    print(f"   URL name: {match.url_name}")
except Exception as e:
    print(f"❌ /api/tenants/ resolution failed: {e}")

# Test the old problematic URL
try:
    match = resolve('/api/tenants/tenants/')
    print(f"⚠️  /api/tenants/tenants/ still resolves to: {match.func}")
except Exception as e:
    print(f"✅ /api/tenants/tenants/ no longer resolves (good): {e}")

# Test reverse URL
try:
    url = reverse('tenant-list')
    print(f"✅ Reverse URL for tenant-list: {url}")
except Exception as e:
    print(f"❌ Reverse URL failed: {e}")
EOF

echo ""
echo "✅ Tenant URL routing fix completed!"
echo ""
echo "🧪 Manual testing steps:"
echo "1. Clear browser cache: Ctrl+Shift+R"
echo "2. Open: http://************:3000/tenants"
echo "3. Should now see actual tenant list (not empty)"
echo ""
echo "🔍 Expected results:"
echo "- ✅ API returns tenant objects with 'name', 'type', etc."
echo "- ✅ No more API root response with endpoint links"
echo "- ✅ Frontend displays tenant list"
echo "- ✅ Debug logs show 'Superuser access granted'"
echo ""
echo "💡 If still not working:"
echo "1. Check if backend restart completed successfully"
echo "2. Verify URL resolution test above shows correct routing"
echo "3. Clear browser cache completely"
echo "4. Check for any Django URL conflicts"
echo ""
echo "🔧 Debug commands:"
echo "- Test API: curl -H \"Authorization: Bearer TOKEN\" http://************:8000/api/tenants/"
echo "- Check routing: docker-compose exec backend python manage.py show_urls | grep tenant"
echo "- View logs: docker-compose logs backend | grep -E 'TenantViewSet|URL'"
