<!DOCTYPE html>
<html>
<head>
    <title>Debug User Data</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { border: 1px solid #ccc; margin: 10px 0; padding: 15px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Debug User Data & Permissions</h1>
    
    <div class="debug-section">
        <h2>Instructions</h2>
        <p>1. Open browser developer tools (F12)</p>
        <p>2. Go to Console tab</p>
        <p>3. Login to the application</p>
        <p>4. After login, paste this code in the console:</p>
        <pre>
// Check if user data is available in localStorage
console.log('=== USER DATA DEBUG ===');
const accessToken = localStorage.getItem('accessToken');
console.log('Access Token:', accessToken ? 'Present' : 'Missing');

// Try to decode JWT token to see user data
if (accessToken) {
    try {
        const payload = JSON.parse(atob(accessToken.split('.')[1]));
        console.log('JWT Payload:', payload);
    } catch (e) {
        console.log('JWT decode error:', e);
    }
}

// Check if AuthContext has user data
console.log('=== REACT CONTEXT DEBUG ===');
// This will only work if you're on the React app page
if (window.React) {
    console.log('React is available');
} else {
    console.log('React not available - make sure you\'re on the app page');
}

// Check network requests
console.log('=== NETWORK DEBUG ===');
console.log('Check Network tab for:');
console.log('1. POST /api/auth/login/ - should return access/refresh tokens');
console.log('2. GET /api/users/{id}/ - should return user with permissions array');
console.log('3. Look for any 401/403 errors');
        </pre>
    </div>

    <div class="debug-section">
        <h2>Expected User Object Structure</h2>
        <pre>
{
  "id": 1,
  "email": "<EMAIL>",
  "role": "clerk",
  "permissions": [
    {
      "codename": "view_citizen",
      "name": "Can view Citizen",
      "content_type": "citizens.citizen"
    },
    {
      "codename": "add_citizen", 
      "name": "Can add Citizen",
      "content_type": "citizens.citizen"
    }
    // ... more permissions
  ],
  "groups": [
    {
      "id": 1,
      "name": "clerk",
      "is_primary": true
    }
  ]
}
        </pre>
    </div>

    <div class="debug-section">
        <h2>Permission Mapping Test</h2>
        <p>Paste this in console after login to test permission mapping:</p>
        <pre>
// Test permission mapping
const PERMISSION_MAPPING = {
    'view_dashboard': ['view_citizen', 'view_idcard', 'view_user'],
    'view_citizens': ['view_citizen'],
    'view_idcards': ['view_idcard'],
    'manage_users': ['add_user', 'change_user', 'view_user']
};

function hasPermission(user, permission) {
    if (!user || !user.permissions) {
        console.log('No user or permissions found');
        return false;
    }
    
    const requiredPermissions = PERMISSION_MAPPING[permission] || [permission];
    const userPermissionCodes = user.permissions.map(p => p.codename);
    
    console.log(`Testing permission: ${permission}`);
    console.log(`Required: ${requiredPermissions}`);
    console.log(`User has: ${userPermissionCodes}`);
    
    const result = requiredPermissions.some(reqPerm => userPermissionCodes.includes(reqPerm));
    console.log(`Result: ${result}`);
    return result;
}

// Get user from React context (this might not work depending on how it's structured)
// You'll need to check the actual user object from your app
const testUser = {
    permissions: [
        { codename: 'view_citizen', name: 'Can view Citizen' },
        { codename: 'add_citizen', name: 'Can add Citizen' },
        { codename: 'view_idcard', name: 'Can view ID Card' }
    ]
};

console.log('=== PERMISSION TESTS ===');
hasPermission(testUser, 'view_dashboard');
hasPermission(testUser, 'view_citizens');
hasPermission(testUser, 'view_idcards');
hasPermission(testUser, 'manage_users');
        </pre>
    </div>

    <div class="debug-section warning">
        <h2>Common Issues</h2>
        <ul>
            <li><strong>No permissions in user object</strong> - Check if API is returning permissions</li>
            <li><strong>Empty permissions array</strong> - User might not be assigned to any groups</li>
            <li><strong>Network errors</strong> - Check if user fetch API call is failing</li>
            <li><strong>Permission mapping mismatch</strong> - Frontend mapping might not match backend permissions</li>
            <li><strong>Authentication context not updating</strong> - User object might not include permissions</li>
        </ul>
    </div>
</body>
</html>
