#!/usr/bin/env python3
"""
Force create user tables in all tenant schemas.
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
sys.path.append('/app')
django.setup()

from django.db import connection
from django_tenants.utils import schema_context
from django.core.management import call_command

def force_create_user_tables():
    """Force create user tables in all tenant schemas."""
    cursor = connection.cursor()
    
    print('=== Force Creating User Tables ===')
    
    # Get all tenant schemas
    cursor.execute("SELECT schema_name FROM tenants_tenant WHERE schema_name != 'public';")
    schemas = cursor.fetchall()
    
    print(f'Found {len(schemas)} tenant schemas')
    
    for schema in schemas:
        schema_name = schema[0]
        print(f'\n--- Processing schema: {schema_name} ---')
        
        try:
            with schema_context(schema_name):
                # First, try to fake the initial migration for users app
                print(f'Faking initial migration for users app in {schema_name}')
                call_command('migrate', 'users', '0001', '--fake', verbosity=1)
                
                # Then run all migrations
                print(f'Running all migrations in {schema_name}')
                call_command('migrate', verbosity=1)
                
        except Exception as e:
            print(f'Error with fake migration, trying direct approach: {e}')
            
            try:
                with schema_context(schema_name):
                    # Try to run migrations without faking
                    call_command('migrate', 'users', verbosity=1)
                    call_command('migrate', 'auth', verbosity=1)
                    
            except Exception as e2:
                print(f'Error with direct migration: {e2}')
                
                # Last resort: manually create the tables
                print(f'Manually creating auth_user table in {schema_name}')
                try:
                    cursor.execute(f"""
                        CREATE TABLE IF NOT EXISTS {schema_name}.auth_user (
                            id SERIAL PRIMARY KEY,
                            password VARCHAR(128) NOT NULL,
                            last_login TIMESTAMP WITH TIME ZONE,
                            is_superuser BOOLEAN NOT NULL,
                            username VARCHAR(150) NOT NULL UNIQUE,
                            first_name VARCHAR(150) NOT NULL,
                            last_name VARCHAR(150) NOT NULL,
                            email VARCHAR(254) NOT NULL UNIQUE,
                            is_staff BOOLEAN NOT NULL,
                            is_active BOOLEAN NOT NULL,
                            date_joined TIMESTAMP WITH TIME ZONE NOT NULL,
                            role VARCHAR(20) NOT NULL DEFAULT 'clerk',
                            tenant_id BIGINT,
                            phone_number VARCHAR(15),
                            profile_picture VARCHAR(100)
                        );
                    """)
                    
                    cursor.execute(f"""
                        CREATE TABLE IF NOT EXISTS {schema_name}.auth_group (
                            id SERIAL PRIMARY KEY,
                            name VARCHAR(150) NOT NULL UNIQUE
                        );
                    """)
                    
                    cursor.execute(f"""
                        CREATE TABLE IF NOT EXISTS {schema_name}.auth_permission (
                            id SERIAL PRIMARY KEY,
                            name VARCHAR(255) NOT NULL,
                            content_type_id INTEGER NOT NULL,
                            codename VARCHAR(100) NOT NULL
                        );
                    """)
                    
                    cursor.execute(f"""
                        CREATE TABLE IF NOT EXISTS {schema_name}.auth_user_groups (
                            id SERIAL PRIMARY KEY,
                            user_id INTEGER NOT NULL,
                            group_id INTEGER NOT NULL,
                            UNIQUE(user_id, group_id)
                        );
                    """)
                    
                    cursor.execute(f"""
                        CREATE TABLE IF NOT EXISTS {schema_name}.auth_user_user_permissions (
                            id SERIAL PRIMARY KEY,
                            user_id INTEGER NOT NULL,
                            permission_id INTEGER NOT NULL,
                            UNIQUE(user_id, permission_id)
                        );
                    """)
                    
                    print(f'✅ Manually created auth tables in {schema_name}')
                    
                except Exception as e3:
                    print(f'❌ Failed to manually create tables: {e3}')
    
    print('\n=== Verification ===')
    for schema in schemas:
        schema_name = schema[0]
        cursor.execute(f"SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = '{schema_name}' AND table_name = 'auth_user');")
        has_user_table = cursor.fetchone()[0]
        print(f'{schema_name}: auth_user table = {has_user_table}')
    
    return True

if __name__ == "__main__":
    force_create_user_tables()
