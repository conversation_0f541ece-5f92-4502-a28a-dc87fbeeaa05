#!/usr/bin/env python3
"""
Test the improved fingerprint matching system in Django environment.
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from biometrics.fingerprint_processing import FingerprintProcessor
import base64
import json

def test_fingerprint_matching():
    """Test the improved fingerprint matching system"""
    
    # Real template from database
    template_b64 = "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"
    
    print("🧪 Testing Improved Fingerprint Matching System")
    print("=" * 60)
    
    processor = FingerprintProcessor()
    
    # Test 1: Exact Match
    print("\n🔍 TEST 1: Exact Match (Same Template)")
    print("-" * 40)
    is_match, confidence = processor._compare_templates(template_b64, template_b64)
    print(f"Result: Match={is_match}, Confidence={confidence:.3f}")
    print(f"Expected: Match=True, Confidence=1.0")
    
    # Test 2: Create slightly modified template
    decoded = json.loads(base64.b64decode(template_b64))
    modified_template = decoded.copy()
    modified_template['minutiae_points'] = decoded['minutiae_points'].copy()
    
    # Modify first 3 points slightly (within tolerance)
    for i in range(3):
        point = modified_template['minutiae_points'][i].copy()
        point['x'] += 2  # Small coordinate change
        point['y'] += 1  # Small coordinate change  
        point['angle'] += 5  # Small angle change
        modified_template['minutiae_points'][i] = point
    
    modified_template_b64 = base64.b64encode(json.dumps(modified_template).encode()).decode()
    
    print("\n🔍 TEST 2: Similar Fingerprint (Small Changes)")
    print("-" * 40)
    is_match2, confidence2 = processor._compare_templates(template_b64, modified_template_b64)
    print(f"Result: Match={is_match2}, Confidence={confidence2:.3f}")
    print(f"Expected: Match=True (if confidence > 0.85)")
    
    # Test 3: Create completely different template
    different_template = decoded.copy()
    different_template['minutiae_points'] = [
        {"x": 100, "y": 100, "angle": 45, "type": "ridge_ending", "quality": 0.9},
        {"x": 120, "y": 150, "angle": 90, "type": "bifurcation", "quality": 0.8},
        {"x": 80, "y": 200, "angle": 135, "type": "ridge_ending", "quality": 0.85},
    ] * 10  # Repeat to get 30 points
    
    different_template_b64 = base64.b64encode(json.dumps(different_template).encode()).decode()
    
    print("\n🔍 TEST 3: Different Fingerprint")
    print("-" * 40)
    is_match3, confidence3 = processor._compare_templates(template_b64, different_template_b64)
    print(f"Result: Match={is_match3}, Confidence={confidence3:.3f}")
    print(f"Expected: Match=False (confidence < 0.85)")
    
    # Summary
    print("\n✅ SUMMARY:")
    exact_pass = is_match and confidence == 1.0
    similar_pass = is_match2
    different_pass = not is_match3
    
    print(f"Exact match: {'PASS' if exact_pass else 'FAIL'}")
    print(f"Similar fingerprint: {'PASS' if similar_pass else 'FAIL'}")
    print(f"Different fingerprint: {'PASS' if different_pass else 'FAIL'}")
    
    if exact_pass and similar_pass and different_pass:
        print("\n🎉 ALL TESTS PASSED! Fingerprint matching is working correctly.")
    else:
        print("\n❌ Some tests failed. Check the matching algorithm.")
    
    return exact_pass, similar_pass, different_pass

if __name__ == "__main__":
    test_fingerprint_matching()
