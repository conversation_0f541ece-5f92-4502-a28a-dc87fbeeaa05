#!/usr/bin/env python3
"""
Inspect DLL functions to find correct function names
"""

import ctypes
from ctypes import cdll, windll
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def inspect_dll_exports(dll_path):
    """Try to inspect DLL exports (limited capability in Python)"""
    try:
        logger.info(f"🔍 Inspecting {dll_path}...")
        
        # Load the DLL
        dll = cdll.LoadLibrary(dll_path)
        logger.info(f"✅ {dll_path} loaded successfully")
        
        # Try common Futronic function names
        common_functions = [
            # Scan API functions
            'ftrScanOpenDevice',
            'ftrScanCloseDevice', 
            'ftrScanGetFrame',
            'ftrScanIsFingerPresent',
            'ftrScanGetImageSize',
            'ftrScanSetOptions',
            
            # Math API functions  
            'ftrCreateTemplate',
            'ftrVerifyTemplate',
            'ftrMatchTemplate',
            'ftrCreateTemplateFromBuffer',
            'ftrEnrollTemplate',
            
            # Alternative naming conventions
            'FtrScanOpenDevice',
            'FtrScanCloseDevice',
            'FtrCreateTemplate',
            'FtrVerifyTemplate',
            
            # Possible C-style exports
            '_ftrScanOpenDevice',
            '_ftrScanCloseDevice', 
            '_ftrCreateTemplate',
            '_ftrVerifyTemplate',
            
            # With ordinals or decorations
            'ftrScanOpenDevice@0',
            'ftrScanCloseDevice@4',
            'ftrCreateTemplate@12',
        ]
        
        found_functions = []
        
        for func_name in common_functions:
            try:
                func = getattr(dll, func_name)
                found_functions.append(func_name)
                logger.info(f"  ✅ Found: {func_name}")
            except AttributeError:
                pass
        
        if not found_functions:
            logger.warning(f"  ❌ No common functions found in {dll_path}")
        
        return found_functions
        
    except Exception as e:
        logger.error(f"❌ Failed to inspect {dll_path}: {e}")
        return []

def test_basic_functions():
    """Test basic function calls"""
    try:
        logger.info("🧪 Testing basic SDK functions...")
        
        # Load scan API
        scan_api = cdll.LoadLibrary('./ftrScanAPI.dll')
        
        # Try different function name variations for opening device
        open_device_names = [
            'ftrScanOpenDevice',
            'FtrScanOpenDevice', 
            '_ftrScanOpenDevice',
            'ftrScanOpenDevice@0'
        ]
        
        for func_name in open_device_names:
            try:
                open_func = getattr(scan_api, func_name)
                logger.info(f"✅ Found open device function: {func_name}")
                
                # Try to call it (this might fail, but we'll see the signature)
                try:
                    result = open_func()
                    logger.info(f"  Function call result: {result}")
                except Exception as e:
                    logger.info(f"  Function call failed (expected): {e}")
                
                break
            except AttributeError:
                continue
        
        # Load math API
        math_api = cdll.LoadLibrary('./ftrMathAPI.dll')
        
        # Try different function name variations for template creation
        template_names = [
            'ftrCreateTemplate',
            'FtrCreateTemplate',
            '_ftrCreateTemplate', 
            'ftrCreateTemplate@12'
        ]
        
        for func_name in template_names:
            try:
                template_func = getattr(math_api, func_name)
                logger.info(f"✅ Found template function: {func_name}")
                break
            except AttributeError:
                continue
        
    except Exception as e:
        logger.error(f"❌ Basic function test failed: {e}")

def main():
    """Main inspection function"""
    logger.info("🔍 Futronic SDK Function Inspector")
    logger.info("=" * 40)
    
    # Inspect both DLLs
    dlls_to_inspect = [
        './ftrScanAPI.dll',
        './ftrMathAPI.dll'
    ]
    
    all_functions = {}
    
    for dll in dlls_to_inspect:
        functions = inspect_dll_exports(dll)
        all_functions[dll] = functions
    
    # Test basic functions
    logger.info("\n🧪 Testing Basic Functions:")
    test_basic_functions()
    
    # Summary
    logger.info("\n📊 Summary:")
    logger.info("=" * 20)
    
    for dll, functions in all_functions.items():
        logger.info(f"{dll}: {len(functions)} functions found")
        for func in functions:
            logger.info(f"  - {func}")
    
    if not any(all_functions.values()):
        logger.warning("❌ No functions found - this might be a DLL compatibility issue")
        logger.info("💡 Suggestions:")
        logger.info("1. Check if you have the correct Futronic SDK version")
        logger.info("2. Try using a DLL dependency walker tool")
        logger.info("3. Contact Futronic for proper SDK documentation")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
