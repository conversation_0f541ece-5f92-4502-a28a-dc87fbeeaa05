@echo off
echo ========================================
echo   SIMPLE HYBRID Service
echo   Fixed Python 3.13 Compatibility Issue
echo ========================================
echo.

echo QUICK FIX: Removed problematic file watcher library
echo.
echo ISSUE FIXED:
echo    - Python 3.13 has compatibility issues with watchdog library
echo    - TypeError: 'handle' must be a _ThreadHandle
echo    - Replaced with simple file polling approach
echo.
echo SIMPLE HYBRID SOLUTION:
echo    - Official Futronic software for capture (WORKING)
echo    - File polling instead of file watcher
echo    - Automatic processing of saved fingerprints
echo    - Direct GoID database integration
echo    - No external dependencies that cause issues
echo.

echo Checking Python installation...
python --version
if errorlevel 1 (
    echo ERROR: Python not found. Please install Python 3.7+
    pause
    exit /b 1
)

echo.
echo Installing basic requirements...
pip install requests

echo.
echo Checking for official Futronic executable...
if exist "ftrScanApiEx.exe" (
    echo SUCCESS: ftrScanApiEx.exe found
) else if exist "ftrScanAPI.exe" (
    echo SUCCESS: ftrScanAPI.exe found
) else (
    echo WARNING: Official executable not found
    echo Please ensure ftrScanApiEx.exe is in current directory
    echo.
)

echo.
echo ========================================
echo   SIMPLE HYBRID WORKFLOW
echo ========================================
echo.
echo HOW IT WORKS:
echo   1. Service starts with file polling (every 2 seconds)
echo   2. Click "Start Left Thumb" → Official software opens
echo   3. Capture fingerprint in official software
echo   4. Save to suggested filename in captured_fingerprints folder
echo   5. Polling detects new file automatically
echo   6. Automatic processing and GoID database storage
echo.
echo ADVANTAGES:
echo   - No problematic external libraries
echo   - Compatible with Python 3.13
echo   - Uses working official software
echo   - Automatic file detection and processing
echo   - Direct GoID integration
echo   - Simple and reliable
echo.
echo EXPECTED WORKFLOW:
echo   1. Start service → Opens web interface
echo   2. Click capture button → Official software opens
echo   3. Capture and save fingerprint
echo   4. System detects and processes automatically
echo   5. Data sent to GoID database
echo   6. Check results to confirm success
echo.

echo Starting SIMPLE HYBRID Service...
echo Service will be available at: http://localhost:8001
echo.
echo This version fixes the Python 3.13 compatibility issue
echo and provides a working fingerprint capture solution!
echo.
echo Press Ctrl+C to stop the service
echo.

python simple_hybrid_service.py

echo.
echo Service stopped.
pause
