#!/usr/bin/env python3
"""
Silent launcher for GoID Biometric Tray Service
This .pyw file runs without showing a console window
"""

import sys
import os
from pathlib import Path

# Add current directory to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Change to service directory
os.chdir(current_dir)

# Import and run the tray service
try:
    from tray_service import main
    main()
except Exception as e:
    # Log error to file since we can't show console
    with open('startup_error.log', 'w') as f:
        f.write(f"Startup error: {e}\n")
        import traceback
        f.write(traceback.format_exc())
