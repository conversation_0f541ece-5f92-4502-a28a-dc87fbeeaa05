#!/usr/bin/env python3
"""
Test script to verify API endpoints are working
"""
import requests
import json

BASE_URL = "http://localhost:8000"

def test_endpoint(endpoint, method="GET", data=None, headers=None):
    """Test an API endpoint"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method == "GET":
            response = requests.get(url, headers=headers)
        elif method == "POST":
            response = requests.post(url, json=data, headers=headers)
        
        print(f"\n{'='*60}")
        print(f"Testing: {method} {endpoint}")
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code < 500:
            try:
                response_data = response.json()
                print(f"Response Data: {json.dumps(response_data, indent=2)[:500]}...")
            except:
                print(f"Response Text: {response.text[:500]}...")
        else:
            print(f"Server Error: {response.text[:200]}...")
            
        return response.status_code, response
        
    except requests.exceptions.ConnectionError:
        print(f"\n❌ Connection Error: Cannot connect to {url}")
        print("Make sure the Django server is running on localhost:8000")
        return None, None
    except Exception as e:
        print(f"\n❌ Error testing {endpoint}: {e}")
        return None, None

def main():
    print("🚀 Testing API Endpoints...")
    
    # Test basic connectivity
    print("\n1. Testing basic connectivity...")
    status, _ = test_endpoint("/admin/")
    
    if status is None:
        print("\n❌ Cannot connect to backend server!")
        print("Please start the Django server with: python manage.py runserver")
        return
    
    # Test auth endpoints (should return 401 without token)
    print("\n2. Testing auth endpoints...")
    test_endpoint("/api/auth/permissions/")
    test_endpoint("/api/auth/roles/")
    test_endpoint("/api/auth/tenant-role-management/manageable_tenants/")
    
    # Test with invalid token (should return 401)
    print("\n3. Testing with invalid token...")
    headers = {"Authorization": "Bearer invalid_token"}
    test_endpoint("/api/auth/permissions/", headers=headers)
    
    print("\n✅ API endpoint tests completed!")
    print("\nNote: 401 Unauthorized responses are expected without valid authentication.")
    print("The important thing is that the endpoints are reachable (not 404 Not Found).")

if __name__ == "__main__":
    main()
