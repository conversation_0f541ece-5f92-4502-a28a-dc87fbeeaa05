# Futronic FS88H Setup Script for Windows
# Run this script as Administrator

Write-Host "🔧 Futronic FS88H Device Setup for Windows" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green

# Check if running as Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "❌ This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ Running as Administrator" -ForegroundColor Green

# Function to check if device is connected
function Test-FutronicDevice {
    Write-Host "`n🔍 Checking for Futronic devices..." -ForegroundColor Cyan
    
    $devices = Get-WmiObject -Class Win32_USBHub | Where-Object { $_.Name -like "*Futronic*" -or $_.DeviceID -like "*VID_1491*" }
    
    if ($devices) {
        Write-Host "✅ Futronic device(s) detected:" -ForegroundColor Green
        foreach ($device in $devices) {
            Write-Host "  - $($device.Name)" -ForegroundColor White
            Write-Host "    Device ID: $($device.DeviceID)" -ForegroundColor Gray
        }
        return $true
    } else {
        Write-Host "⚠️ No Futronic devices detected" -ForegroundColor Yellow
        Write-Host "Please ensure your FS88H device is connected via USB" -ForegroundColor Yellow
        return $false
    }
}

# Function to check Docker Desktop
function Test-DockerDesktop {
    Write-Host "`n🐳 Checking Docker Desktop..." -ForegroundColor Cyan
    
    try {
        $dockerVersion = docker --version
        Write-Host "✅ Docker is installed: $dockerVersion" -ForegroundColor Green
        
        # Check if Docker is running
        $dockerInfo = docker info 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Docker is running" -ForegroundColor Green
            return $true
        } else {
            Write-Host "⚠️ Docker is installed but not running" -ForegroundColor Yellow
            Write-Host "Please start Docker Desktop" -ForegroundColor Yellow
            return $false
        }
    } catch {
        Write-Host "❌ Docker is not installed or not in PATH" -ForegroundColor Red
        Write-Host "Please install Docker Desktop from https://www.docker.com/products/docker-desktop" -ForegroundColor Yellow
        return $false
    }
}

# Function to check for Futronic SDK
function Test-FutronicSDK {
    Write-Host "`n📚 Checking for Futronic SDK..." -ForegroundColor Cyan
    
    $sdkPaths = @(
        "C:\Program Files\Futronic\SDK",
        "C:\Program Files (x86)\Futronic\SDK",
        "C:\Futronic\SDK"
    )
    
    foreach ($path in $sdkPaths) {
        if (Test-Path $path) {
            Write-Host "✅ Futronic SDK found at: $path" -ForegroundColor Green
            
            # Check for DLL files
            $dllFiles = Get-ChildItem -Path $path -Recurse -Filter "*.dll" | Select-Object -First 5
            if ($dllFiles) {
                Write-Host "  SDK DLL files found:" -ForegroundColor White
                foreach ($dll in $dllFiles) {
                    Write-Host "    - $($dll.Name)" -ForegroundColor Gray
                }
            }
            return $true
        }
    }
    
    Write-Host "⚠️ Futronic SDK not found in standard locations" -ForegroundColor Yellow
    Write-Host "Please download and install the Futronic SDK from:" -ForegroundColor Yellow
    Write-Host "https://www.futronic-tech.com/downloads.html" -ForegroundColor Cyan
    return $false
}

# Function to setup Docker USB access
function Set-DockerUSBAccess {
    Write-Host "`n🔌 Configuring Docker USB access..." -ForegroundColor Cyan
    
    # For Windows, we need to use Docker Desktop's USB passthrough
    Write-Host "For Windows Docker Desktop, USB passthrough requires:" -ForegroundColor Yellow
    Write-Host "1. Docker Desktop with WSL2 backend" -ForegroundColor White
    Write-Host "2. USB device sharing enabled in Docker Desktop settings" -ForegroundColor White
    Write-Host "3. Device connected and recognized by Windows" -ForegroundColor White
    
    Write-Host "`nTo enable USB sharing in Docker Desktop:" -ForegroundColor Cyan
    Write-Host "1. Open Docker Desktop" -ForegroundColor White
    Write-Host "2. Go to Settings > Resources > WSL Integration" -ForegroundColor White
    Write-Host "3. Enable integration with your WSL2 distro" -ForegroundColor White
    Write-Host "4. Restart Docker Desktop" -ForegroundColor White
}

# Function to test the setup
function Test-Setup {
    Write-Host "`n🧪 Testing the setup..." -ForegroundColor Cyan
    
    try {
        # Test if containers are running
        $containers = docker ps --format "table {{.Names}}\t{{.Status}}" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Docker containers status:" -ForegroundColor Green
            Write-Host $containers -ForegroundColor White
        } else {
            Write-Host "⚠️ No Docker containers running" -ForegroundColor Yellow
            Write-Host "Run 'docker-compose up -d' to start the application" -ForegroundColor Yellow
        }
        
        # Test device detection command
        Write-Host "`nTesting device detection..." -ForegroundColor Cyan
        $deviceTest = docker-compose exec backend python manage.py detect_futronic_device 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Device detection command executed successfully" -ForegroundColor Green
        } else {
            Write-Host "⚠️ Device detection command failed" -ForegroundColor Yellow
            Write-Host "This is normal if containers are not running" -ForegroundColor Gray
        }
        
    } catch {
        Write-Host "⚠️ Could not test setup - containers may not be running" -ForegroundColor Yellow
    }
}

# Main execution
Write-Host "`n🚀 Starting Futronic FS88H setup..." -ForegroundColor Green

# Step 1: Check for device
$deviceOK = Test-FutronicDevice

# Step 2: Check Docker
$dockerOK = Test-DockerDesktop

# Step 3: Check SDK
$sdkOK = Test-FutronicSDK

# Step 4: Configure Docker USB
Set-DockerUSBAccess

# Step 5: Test setup
Test-Setup

# Summary
Write-Host "`n📋 Setup Summary:" -ForegroundColor Green
Write-Host "=================" -ForegroundColor Green
Write-Host "Device Connected: $(if($deviceOK){'✅ Yes'}else{'❌ No'})" -ForegroundColor $(if($deviceOK){'Green'}else{'Red'})
Write-Host "Docker Ready: $(if($dockerOK){'✅ Yes'}else{'❌ No'})" -ForegroundColor $(if($dockerOK){'Green'}else{'Red'})
Write-Host "SDK Installed: $(if($sdkOK){'✅ Yes'}else{'❌ No'})" -ForegroundColor $(if($sdkOK){'Green'}else{'Red'})

if ($deviceOK -and $dockerOK) {
    Write-Host "`n🎉 Setup looks good! Next steps:" -ForegroundColor Green
    Write-Host "1. Rebuild Docker containers: docker-compose build --no-cache" -ForegroundColor White
    Write-Host "2. Start the application: docker-compose up -d" -ForegroundColor White
    Write-Host "3. Test device integration: docker-compose exec backend python manage.py detect_futronic_device" -ForegroundColor White
    Write-Host "4. Access the demo: http://localhost:3000/demo/biometric" -ForegroundColor White
} else {
    Write-Host "`n⚠️ Setup incomplete. Please address the issues above." -ForegroundColor Yellow
}

Write-Host "`nPress any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
