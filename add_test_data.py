#!/usr/bin/env python3
"""
Simple script to add test data to the database via Docker
"""
import subprocess
import sys

def run_docker_command(command):
    """Run a command inside the Docker container"""
    full_command = f"docker exec -it goid-backend-1 {command}"
    print(f"Running: {full_command}")
    
    try:
        result = subprocess.run(full_command, shell=True, capture_output=True, text=True)
        print(f"Output: {result.stdout}")
        if result.stderr:
            print(f"Error: {result.stderr}")
        return result.returncode == 0
    except Exception as e:
        print(f"Exception: {e}")
        return False

def main():
    print("🚀 Adding test data to GoID database...")
    
    # First, let's check what tenants exist
    print("\n1. Checking existing tenants...")
    run_docker_command('python manage.py shell -c "from tenants.models import Tenant; [print(f\\"Tenant: {t.name} (ID: {t.id}, schema: {t.schema_name})\\") for t in Tenant.objects.all()]"')
    
    # Check existing users
    print("\n2. Checking existing users...")
    run_docker_command('python manage.py shell -c "from users.models import User; [print(f\\"User: {u.username} (tenant_id: {u.tenant_id})\\") for u in User.objects.all()]"')
    
    # Try to run our populate command
    print("\n3. Running populate test data command...")
    success = run_docker_command('python manage.py populate_test_data')
    
    if success:
        print("✅ Test data added successfully!")
    else:
        print("❌ Failed to add test data")
        
        # Try alternative approach - create data manually
        print("\n4. Trying alternative approach...")
        
        # Create a simple citizen in the first available tenant
        run_docker_command('''python manage.py shell -c "
from tenants.models import Tenant
from django_tenants.utils import schema_context
from citizens.models import Citizen
from idcards.models import IDCard, IDCardStatus
from datetime import datetime, timedelta

tenant = Tenant.objects.filter(tenant_type='kebele').first()
if tenant:
    print(f'Using tenant: {tenant.name}')
    with schema_context(tenant.schema_name):
        citizen, created = Citizen.objects.get_or_create(
            first_name='Test',
            middle_name='User',
            last_name='Demo',
            defaults={
                'gender': 'M',
                'date_of_birth': datetime(1990, 1, 1).date(),
                'phone_number': '+251911000000',
                'email': '<EMAIL>'
            }
        )
        if created:
            print(f'Created citizen: {citizen.first_name} {citizen.last_name}')
        
        id_card, created = IDCard.objects.get_or_create(
            citizen=citizen,
            defaults={
                'card_number': 'TEST001234567890',
                'status': IDCardStatus.APPROVED,
                'issue_date': datetime.now().date(),
                'expiry_date': (datetime.now() + timedelta(days=365*5)).date(),
            }
        )
        if created:
            print(f'Created ID card: {id_card.card_number}')
        
        print(f'Total citizens: {Citizen.objects.count()}')
        print(f'Total ID cards: {IDCard.objects.count()}')
else:
    print('No kebele tenant found')
"''')

if __name__ == "__main__":
    main()
