#!/usr/bin/env python
"""
System initialization script for GoID.
This script ensures the system is properly set up with permissions, groups, and superadmin user.
Run this script after migrations to initialize the system.
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.core.management import call_command
from django.db import transaction
import subprocess

def run_migrations():
    """Run database migrations"""
    print('🔄 Running database migrations...')
    try:
        call_command('migrate', verbosity=1)
        print('✅ Migrations completed successfully')
        return True
    except Exception as e:
        print(f'❌ Migration failed: {e}')
        return False

def setup_permissions_and_groups():
    """Setup permissions, groups, and superadmin user"""
    print('🔧 Setting up permissions and groups...')
    try:
        # Import and run the setup script
        from setup_general_permissions import main as setup_main
        setup_main()
        print('✅ Permissions and groups setup completed')
        return True
    except Exception as e:
        print(f'❌ Permissions setup failed: {e}')
        return False

def assign_existing_admin_groups():
    """Assign existing admin users to their appropriate groups"""
    print('👥 Assigning existing admin users to groups...')
    try:
        # Run the assignment command without dry-run to actually assign users
        call_command('assign_admin_groups', verbosity=1)
        print('✅ Admin group assignments completed')
        return True
    except Exception as e:
        print(f'❌ Admin group assignment failed: {e}')
        # Don't fail the whole setup if this fails
        print('⚠️ Continuing with setup - you can run assign_admin_groups manually later')
        return True

def verify_system():
    """Verify system is properly configured"""
    print('🔍 Verifying system configuration...')
    
    try:
        from django.contrib.auth import get_user_model
        from users.models_groups import TenantGroup, GroupMembership
        from django_tenants.utils import schema_context, get_public_schema_name
        
        User = get_user_model()
        
        # Check superadmin user exists
        superadmin = User.objects.filter(email='<EMAIL>', is_superuser=True).first()
        if not superadmin:
            print('❌ Superadmin user not found')
            return False
        
        # Check super_admin group exists
        with schema_context(get_public_schema_name()):
            super_admin_group = TenantGroup.objects.filter(group__name='super_admin').first()
            if not super_admin_group:
                print('❌ Super admin group not found')
                return False
            
            # Check superadmin is in group
            membership = GroupMembership.objects.filter(
                user_email=superadmin.email,
                group=super_admin_group,
                is_active=True
            ).first()
            
            if not membership:
                print('❌ Superadmin not assigned to super_admin group')
                return False
        
        print('✅ System verification passed')
        print(f'   - Superadmin user: {superadmin.email}')
        print(f'   - Super admin group: {super_admin_group.name}')
        print(f'   - Group membership: Active')
        
        return True
        
    except Exception as e:
        print(f'❌ System verification failed: {e}')
        return False

def main():
    """Main initialization function"""
    print('🚀 Initializing GoID System...')
    print('=' * 50)
    
    success = True
    
    # Step 1: Run migrations
    if not run_migrations():
        success = False
    
    # Step 2: Setup permissions and groups
    if success and not setup_permissions_and_groups():
        success = False
    
    # Step 3: Verify system
    if success and not verify_system():
        success = False
    
    print('=' * 50)
    if success:
        print('🎉 GoID System initialization completed successfully!')
        print('')
        print('📋 System Ready:')
        print('   - Database: ✅ Migrated')
        print('   - Permissions: ✅ Configured')
        print('   - Groups: ✅ Created')
        print('   - Superadmin: ✅ Ready')
        print('')
        print('🌐 Login Credentials:')
        print('   Email: <EMAIL>')
        print('   Password: admin123')
        print('')
        print('🔗 Access URLs:')
        print('   Frontend: http://localhost:3000')
        print('   Backend API: http://localhost:8000/api/')
        print('   Admin Panel: http://localhost:8000/admin/')
        
    else:
        print('❌ GoID System initialization failed!')
        print('Please check the errors above and try again.')
        sys.exit(1)

if __name__ == '__main__':
    main()
