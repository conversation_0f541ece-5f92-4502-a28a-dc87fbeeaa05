<!DOCTYPE html>
<html>
<head>
    <title>Test Permission Logic</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { border: 1px solid #ccc; margin: 10px 0; padding: 15px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; }
        .permission { margin: 5px 0; }
        .has-permission { color: green; }
        .no-permission { color: red; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Test Permission Logic</h1>
    
    <div class="test-section">
        <h2>Test 1: Clerk User with Backend Permissions</h2>
        <div id="clerk-test"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Kebele Leader with Backend Permissions</h2>
        <div id="kebele-leader-test"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: User with No Permissions</h2>
        <div id="no-permissions-test"></div>
    </div>

    <script>
        // Permission mapping (same as frontend)
        const PERMISSION_MAPPING = {
            'view_dashboard': ['view_citizen', 'view_idcard', 'view_user'],
            'view_citizens': ['view_citizen'],
            'manage_citizens': ['add_citizen', 'change_citizen', 'delete_citizen'],
            'register_citizens': ['add_citizen'],
            'view_idcards': ['view_idcard'],
            'manage_idcards': ['add_idcard', 'change_idcard', 'delete_idcard'],
            'create_idcards': ['add_idcard'],
            'approve_idcards': ['delete_citizen', 'delete_idcard'],
            'print_idcards': ['change_idcard'],
            'view_tenants': ['view_tenant'],
            'manage_users': ['add_user', 'change_user', 'view_user'],
            'view_reports': ['delete_citizen', 'delete_idcard', 'view_tenant'],
        };

        // Mock users with backend permissions (based on our API test results)
        const clerkUser = {
            id: 3,
            email: '<EMAIL>',
            role: 'clerk',
            permissions: [
                { codename: 'add_citizen', name: 'Can add Citizen' },
                { codename: 'change_citizen', name: 'Can change Citizen' },
                { codename: 'view_citizen', name: 'Can view Citizen' },
                { codename: 'add_idcard', name: 'Can add ID Card' },
                { codename: 'change_idcard', name: 'Can change ID Card' },
                { codename: 'view_idcard', name: 'Can view ID Card' },
                // Note: No delete permissions, no user management
            ]
        };

        const kebeleLeaderUser = {
            id: 2,
            email: '<EMAIL>',
            role: 'kebele_leader',
            permissions: [
                { codename: 'add_citizen', name: 'Can add Citizen' },
                { codename: 'change_citizen', name: 'Can change Citizen' },
                { codename: 'delete_citizen', name: 'Can delete Citizen' },
                { codename: 'view_citizen', name: 'Can view Citizen' },
                { codename: 'add_idcard', name: 'Can add ID Card' },
                { codename: 'change_idcard', name: 'Can change ID Card' },
                { codename: 'delete_idcard', name: 'Can delete ID Card' },
                { codename: 'view_idcard', name: 'Can view ID Card' },
                // Note: No user management permissions
            ]
        };

        const noPermissionsUser = {
            id: 4,
            email: '<EMAIL>',
            role: 'clerk',
            permissions: [] // No permissions
        };

        function hasPermission(user, permission) {
            console.log(`Testing permission: ${permission} for user: ${user.email}`);
            
            if (!user) {
                console.log('No user found');
                return false;
            }

            // Super admin has all permissions
            if (user.is_superuser || user.role === 'superadmin') {
                console.log('User is superadmin');
                return true;
            }

            // Use actual permissions from backend if available
            if (user.permissions && Array.isArray(user.permissions)) {
                console.log(`User has ${user.permissions.length} permissions`);
                const requiredPermissions = PERMISSION_MAPPING[permission] || [permission];
                console.log(`Required permissions:`, requiredPermissions);
                
                // Check if user has any of the required backend permissions
                const userPermissionCodes = user.permissions.map(p => p.codename);
                console.log(`User permission codes:`, userPermissionCodes);
                
                const result = requiredPermissions.some(reqPerm => userPermissionCodes.includes(reqPerm));
                console.log(`Result: ${result}`);
                return result;
            }

            console.log('No permissions array found');
            return false;
        }

        function testUserPermissions(user, containerId) {
            const container = document.getElementById(containerId);
            const testPermissions = [
                'view_dashboard',
                'view_citizens',
                'register_citizens',
                'manage_citizens',
                'view_idcards',
                'create_idcards',
                'approve_idcards',
                'manage_users',
                'view_reports',
                'view_tenants'
            ];

            let html = `<h3>${user.email} (${user.role})</h3>`;
            html += `<p>Backend permissions: ${user.permissions.length}</p>`;
            
            html += '<h4>Navigation Permission Tests:</h4>';
            
            testPermissions.forEach(permission => {
                const hasAccess = hasPermission(user, permission);
                const className = hasAccess ? 'has-permission' : 'no-permission';
                const status = hasAccess ? '✅ SHOW' : '❌ HIDE';
                
                html += `<div class="permission ${className}">
                    <strong>${permission}:</strong> ${status}
                </div>`;
            });

            // Expected navigation items
            html += '<h4>Expected Navigation Items:</h4>';
            
            if (hasPermission(user, 'view_dashboard')) {
                html += '<div class="success">✅ Dashboard should show</div>';
            } else {
                html += '<div class="error">❌ Dashboard should show but permission missing</div>';
            }
            
            if (hasPermission(user, 'view_citizens')) {
                html += '<div class="success">✅ Citizens should show</div>';
            } else {
                html += '<div class="error">❌ Citizens should show but permission missing</div>';
            }
            
            if (hasPermission(user, 'view_idcards')) {
                html += '<div class="success">✅ ID Cards should show</div>';
            } else {
                html += '<div class="error">❌ ID Cards should show but permission missing</div>';
            }

            container.innerHTML = html;
        }

        // Test all users
        console.log('=== Testing Clerk User ===');
        testUserPermissions(clerkUser, 'clerk-test');
        
        console.log('=== Testing Kebele Leader User ===');
        testUserPermissions(kebeleLeaderUser, 'kebele-leader-test');
        
        console.log('=== Testing No Permissions User ===');
        testUserPermissions(noPermissionsUser, 'no-permissions-test');
    </script>
</body>
</html>
