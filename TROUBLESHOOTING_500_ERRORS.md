# Troubleshooting 500 Internal Server Errors

## Overview

When you see "Failed to load resource: the server responded with a status of 500 (Internal Server Error)", it means the backend Django server is encountering an error while processing the request.

## 🔍 Common Causes and Solutions

### 1. **Backend Server Not Running**

**Symptoms:**
- 500 errors on all API calls
- API Status shows "Disconnected"
- Network errors in browser console

**Solution:**
```bash
cd backend
python manage.py runserver
```

**Verification:**
- Visit `http://localhost:8000/admin/` in browser
- Should see Django admin login page (not 500 error)

### 2. **Database Migration Issues**

**Symptoms:**
- 500 errors when creating/updating data
- Database-related errors in Django logs
- Missing table errors

**Solution:**
```bash
cd backend
python manage.py makemigrations
python manage.py migrate
python manage.py makemigrations users
python manage.py migrate users
```

### 3. **Missing Permissions Setup**

**Symptoms:**
- 500 errors when accessing permissions
- Role creation fails
- Permission-related database errors

**Solution:**
```bash
cd backend
python manage.py setup_permissions
python manage.py setup_dynamic_roles
```

### 4. **Missing Dependencies**

**Symptoms:**
- Import errors in Django logs
- Module not found errors
- 500 errors on server startup

**Solution:**
```bash
cd backend
pip install -r requirements.txt
# or if using virtual environment
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### 5. **Database Connection Issues**

**Symptoms:**
- Database connection errors
- 500 errors on all database operations
- "Database is locked" errors (SQLite)

**Solution:**
```bash
# Check database settings in settings.py
# For SQLite, ensure db.sqlite3 file has proper permissions
cd backend
python manage.py dbshell
# Should connect without errors
```

### 6. **URL Configuration Issues**

**Symptoms:**
- 500 errors on specific endpoints
- URL pattern errors in Django logs
- Import errors in urls.py

**Solution:**
```bash
# Check that urls.py files are properly configured
# Verify imports in backend/users/urls.py
# Check main backend/goid/urls.py includes users URLs
```

## 🛠️ Debugging Steps

### Step 1: Check Django Server Logs

```bash
cd backend
python manage.py runserver
# Look for error messages in the console output
```

### Step 2: Test Basic Connectivity

```bash
# Test if server is running
curl http://localhost:8000/admin/
# Should return HTML, not 500 error
```

### Step 3: Check Database

```bash
cd backend
python manage.py shell
```

```python
# Test database connectivity
from users.models import User, Permission, Role
print("Users count:", User.objects.count())
print("Permissions count:", Permission.objects.count())
print("Roles count:", Role.objects.count())
```

### Step 4: Test API Endpoints

```bash
# Test permissions endpoint (should return 401, not 500)
curl http://localhost:8000/api/auth/permissions/

# Test with authentication
curl -X POST http://localhost:8000/api/auth/token/ \
  -H "Content-Type: application/json" \
  -d '{"username": "your_username", "password": "your_password"}'
```

### Step 5: Check Django Settings

Verify these settings in `backend/goid/settings.py`:

```python
# Database configuration
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# Installed apps should include
INSTALLED_APPS = [
    # ...
    'users',
    'tenants',
    'rest_framework',
    'rest_framework_simplejwt',
    # ...
]

# URL configuration
ROOT_URLCONF = 'goid.urls'
```

## 🚨 Emergency Fixes

### Quick Reset (Development Only)

```bash
cd backend

# Backup current database
cp db.sqlite3 db.sqlite3.backup

# Reset database
rm db.sqlite3
python manage.py migrate
python manage.py setup_permissions
python manage.py setup_dynamic_roles

# Create superuser
python manage.py createsuperuser

# Create test data
python manage.py shell
```

```python
from users.models import User, UserRole
from tenants.models import Tenant

# Create test subcity
subcity = Tenant.objects.create(
    name='Test Subcity',
    type='subcity',
    code='TEST_SC'
)

# Create test kebele
kebele = Tenant.objects.create(
    name='Test Kebele',
    type='kebele',
    code='TEST_KB',
    parent=subcity
)

# Create test subcity admin
admin = User.objects.create_user(
    username='test_admin',
    email='<EMAIL>',
    password='password123',
    role=UserRole.SUBCITY_ADMIN,
    tenant=subcity,
    first_name='Test',
    last_name='Admin'
)

print(f"Created admin: {admin.email}")
print(f"Created subcity: {subcity.name}")
print(f"Created kebele: {kebele.name}")
```

## 📋 Verification Checklist

After applying fixes, verify:

- [ ] Django server starts without errors
- [ ] `/admin/` page loads successfully
- [ ] Database migrations are applied
- [ ] Permissions are set up
- [ ] API endpoints return 401 (not 500) without authentication
- [ ] Frontend API Status shows "Connected"
- [ ] Role management functions work without 500 errors

## 🔧 Frontend Improvements

The frontend now includes:

### Enhanced Error Handling
- Detailed error messages for different error types
- Troubleshooting tips in error alerts
- API connectivity status indicator

### Better User Experience
- API status indicator in header
- Retry button for disconnected state
- Informative error messages with next steps

### Mock Data Fallback
- System works with mock data when APIs are unavailable
- Allows testing UI functionality independently
- Graceful degradation for development

## 📞 Getting Help

If issues persist:

1. **Check Django Logs**: Look for specific error messages
2. **Verify Environment**: Ensure Python version and dependencies
3. **Test Step by Step**: Follow debugging steps systematically
4. **Check Documentation**: Review Django and DRF documentation
5. **Reset if Needed**: Use emergency reset for development environments

The enhanced error handling and status indicators will help identify and resolve issues more quickly!
