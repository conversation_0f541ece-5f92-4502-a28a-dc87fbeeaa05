#!/usr/bin/env python3
"""
Quick fix for the "relation citizen_clearance_requests does not exist" error
when deleting users from Django admin.

This script cleans up foreign key relationships in tenant schemas.
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django_tenants.utils import schema_context
from tenants.models import Tenant
from users.models import User

def fix_user_deletion_for_kebele_14():
    """
    Fix user deletion issues specifically for kebele 14 users.
    """
    print("🔧 Fixing user deletion issues for kebele 14...")

    # Find kebele 14 tenant
    try:
        kebele_14 = Tenant.objects.get(schema_name='kebele_kebele14')
        print(f"✅ Found kebele 14: {kebele_14.name}")
    except Tenant.DoesNotExist:
        print("❌ Kebele 14 tenant not found")
        print("Available tenants:")
        for t in Tenant.objects.all():
            print(f"  {t.schema_name} - {t.name} ({t.type})")
        return
    
    # Check for users that should only be in kebele 14
    kebele_14_users = User.objects.filter(tenant=kebele_14)
    print(f"🔍 Found {kebele_14_users.count()} users assigned to kebele 14:")
    
    for user in kebele_14_users:
        print(f"   - {user.email} (ID: {user.id}, Role: {user.role})")
    
    print()
    
    # Clean up relationships in all tenant schemas for these users
    all_tenants = Tenant.objects.filter(type='kebele')
    
    for user in kebele_14_users:
        print(f"🧹 Cleaning up relationships for user: {user.email}")
        
        for tenant in all_tenants:
            try:
                with schema_context(tenant.schema_name):
                    from workflows.models import CitizenClearanceRequest, WorkflowLog
                    
                    # Clean up clearance requests
                    requested = CitizenClearanceRequest.objects.filter(requested_by=user)
                    reviewed = CitizenClearanceRequest.objects.filter(reviewed_by=user)
                    issued = CitizenClearanceRequest.objects.filter(issued_by=user)
                    
                    if requested.exists():
                        count = requested.count()
                        requested.delete()
                        print(f"   ✅ Deleted {count} requested clearances in {tenant.schema_name}")
                    
                    if reviewed.exists():
                        count = reviewed.count()
                        reviewed.update(reviewed_by=None)
                        print(f"   ✅ Cleared reviewer from {count} clearances in {tenant.schema_name}")
                    
                    if issued.exists():
                        count = issued.count()
                        issued.update(issued_by=None)
                        print(f"   ✅ Cleared issuer from {count} clearances in {tenant.schema_name}")
                    
                    # Clean up workflow logs
                    logs = WorkflowLog.objects.filter(performed_by=user)
                    if logs.exists():
                        count = logs.count()
                        logs.update(performed_by=None)
                        print(f"   ✅ Cleared performer from {count} workflow logs in {tenant.schema_name}")
                        
            except Exception as e:
                print(f"   ⚠️  Warning: Could not clean {tenant.schema_name}: {e}")
        
        print(f"✅ Cleanup completed for {user.email}")
        print()
    
    print("🎉 All user relationships cleaned up!")
    print("✅ You can now safely delete users from the Django admin interface")

def clean_specific_user(user_email):
    """
    Clean up relationships for a specific user.
    """
    try:
        user = User.objects.get(email=user_email)
        print(f"🔍 Found user: {user.email} (ID: {user.id})")
        
        all_tenants = Tenant.objects.filter(type='kebele')
        
        for tenant in all_tenants:
            try:
                with schema_context(tenant.schema_name):
                    from workflows.models import CitizenClearanceRequest, WorkflowLog
                    
                    # Clean up all relationships
                    CitizenClearanceRequest.objects.filter(requested_by=user).delete()
                    CitizenClearanceRequest.objects.filter(reviewed_by=user).update(reviewed_by=None)
                    CitizenClearanceRequest.objects.filter(issued_by=user).update(issued_by=None)
                    WorkflowLog.objects.filter(performed_by=user).update(performed_by=None)
                    
            except Exception as e:
                print(f"   ⚠️  Warning: Could not clean {tenant.schema_name}: {e}")
        
        print(f"✅ Cleanup completed for {user.email}")
        print("✅ You can now safely delete this user from the Django admin interface")
        
    except User.DoesNotExist:
        print(f"❌ User not found: {user_email}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # Clean specific user
        user_email = sys.argv[1]
        clean_specific_user(user_email)
    else:
        # Clean all kebele 14 users
        fix_user_deletion_for_kebele_14()
