@echo off
echo 🔧 GoID Biometric Service - Futronic SDK Setup
echo ==============================================
echo.

echo 📋 This script will set up the Futronic SDK for FS88H device
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ This script must be run as Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo ✅ Running as Administrator
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo ✅ Python found
echo.

REM Install Python dependencies
echo 📦 Installing Python dependencies...
pip install Flask Flask-CORS requests

echo.
echo 🔍 Checking for Futronic SDK DLL files...
echo.

REM Check for SDK DLL files in current directory
if exist "ftrScanAPI.dll" (
    echo ✅ ftrScanAPI.dll found in current directory
) else (
    echo ❌ ftrScanAPI.dll not found in current directory
)

if exist "ftrMathAPI.dll" (
    echo ✅ ftrMathAPI.dll found in current directory
) else (
    echo ❌ ftrMathAPI.dll not found in current directory
)

REM Check system directories
echo.
echo 🔍 Checking system directories...

if exist "C:\Windows\System32\ftrScanAPI.dll" (
    echo ✅ ftrScanAPI.dll found in System32
) else (
    echo ❌ ftrScanAPI.dll not found in System32
)

if exist "C:\Windows\SysWOW64\ftrScanAPI.dll" (
    echo ✅ ftrScanAPI.dll found in SysWOW64
) else (
    echo ❌ ftrScanAPI.dll not found in SysWOW64
)

echo.
echo 📋 SDK DLL Setup Instructions:
echo ==============================
echo.
echo If DLL files are not found, please:
echo.
echo 1. Copy the following files to this directory:
echo    - ftrScanAPI.dll
echo    - ftrMathAPI.dll
echo    - Any other Futronic SDK DLL files you have
echo.
echo 2. OR install the Futronic SDK to system directories
echo.
echo 3. OR copy DLL files to Windows\System32 directory
echo.

REM Create a test script
echo 🧪 Creating device test script...
echo import sys > test_futronic_sdk.py
echo import logging >> test_futronic_sdk.py
echo logging.basicConfig(level=logging.INFO) >> test_futronic_sdk.py
echo. >> test_futronic_sdk.py
echo try: >> test_futronic_sdk.py
echo     from futronic_sdk import FutronicSDK >> test_futronic_sdk.py
echo     device = FutronicSDK() >> test_futronic_sdk.py
echo     if device.initialize(): >> test_futronic_sdk.py
echo         status = device.get_device_status() >> test_futronic_sdk.py
echo         print(f"Device Status: {status}") >> test_futronic_sdk.py
echo         if status['real_device']: >> test_futronic_sdk.py
echo             print("✅ Real device detected and working!") >> test_futronic_sdk.py
echo             print("🧪 Testing fingerprint capture...") >> test_futronic_sdk.py
echo             try: >> test_futronic_sdk.py
echo                 template = device.capture_fingerprint('left') >> test_futronic_sdk.py
echo                 print(f"✅ Capture successful! Quality: {template.quality_score}%%") >> test_futronic_sdk.py
echo             except Exception as e: >> test_futronic_sdk.py
echo                 print(f"❌ Capture failed: {e}") >> test_futronic_sdk.py
echo         else: >> test_futronic_sdk.py
echo             print("⚠️ Running in simulation mode") >> test_futronic_sdk.py
echo     else: >> test_futronic_sdk.py
echo         print("❌ Device initialization failed") >> test_futronic_sdk.py
echo except Exception as e: >> test_futronic_sdk.py
echo     print(f"❌ Error: {e}") >> test_futronic_sdk.py

echo.
echo ✅ Setup complete!
echo.
echo 📋 Next steps:
echo 1. Ensure Futronic SDK DLL files are available (see instructions above)
echo 2. Connect your Futronic FS88H device
echo 3. Test device: python test_futronic_sdk.py
echo 4. Start service: python biometric_service.py
echo.
echo 🔗 If you need the Futronic SDK DLLs:
echo - Contact Futronic for official SDK
echo - Check your device installation directory
echo - Look for existing Futronic software installations
echo.
pause
