@echo off
echo ========================================
echo   REAL CAPTURE ONLY
echo   Focus: Get ACTUAL Fingerprints
echo ========================================
echo.

echo MISSION: Capture REAL fingerprints from hardware
echo.
echo NO SYNTHETIC DATA - ONLY REAL BIOMETRIC CAPTURE
echo.
echo METHODS TO TRY:
echo   1. Different DLL Signatures - Try every possible function signature
echo   2. Raw Memory Access - Direct device memory reading
echo   3. USB-Level Access - Low-level USB communication
echo   4. Windows API - Windows Biometric Framework
echo   5. Official Software - Integration with Futronic tools
echo.
echo GOAL: Find ANY method that captures actual fingerprint ridges
echo.

echo Checking Python installation...
python --version
if errorlevel 1 (
    echo ERROR: Python not found. Please install Python 3.7+
    pause
    exit /b 1
)

echo.
echo Checking required DLL files...
if not exist "ftrScanAPI.dll" (
    echo ERROR: ftrScanAPI.dll not found
    echo Please ensure Futronic SDK files are in the current directory
    pause
    exit /b 1
)

echo SUCCESS: ftrScanAPI.dll found

echo.
echo ========================================
echo   REAL CAPTURE STRATEGY
echo ========================================
echo.
echo COMPREHENSIVE APPROACH:
echo.
echo METHOD 1 - DLL SIGNATURE TESTING:
echo   - Try 10+ different function signatures
echo   - Test c_ulong, c_uint, c_int parameter types
echo   - Try direct parameters vs pointers
echo   - Test different handle types
echo.
echo METHOD 2 - RAW MEMORY ACCESS:
echo   - Direct memory reading from device handle
echo   - Windows API ReadProcessMemory
echo   - Bypass DLL function calls entirely
echo.
echo METHOD 3 - USB-LEVEL ACCESS:
echo   - Direct USB device communication
echo   - Enumerate USB devices
echo   - Low-level hardware protocol
echo.
echo METHOD 4 - WINDOWS API:
echo   - Windows Biometric Framework (WBF)
echo   - System-level biometric services
echo   - Native Windows fingerprint support
echo.
echo METHOD 5 - OFFICIAL SOFTWARE:
echo   - Execute official Futronic tools
echo   - Capture output files
echo   - Process real captured images
echo.

echo Starting REAL CAPTURE ONLY Service...
echo Service will be available at: http://localhost:8001
echo.
echo This service will try EVERY possible method
echo to capture REAL fingerprints from your hardware!
echo.
echo Press Ctrl+C to stop the service
echo.

python real_capture_only.py

echo.
echo Service stopped.
pause
