#!/usr/bin/env python3
"""
Fix Docker Static Files Configuration
"""

import subprocess
import os

def check_docker_status():
    """Check Docker container status"""
    
    print("🐳 Checking Docker Status...")
    print("=" * 30)
    
    try:
        # Check container status
        result = subprocess.run(
            ["docker-compose", "-f", "docker-compose.production.yaml", "ps"],
            capture_output=True,
            text=True,
            cwd="C:\\Users\\<USER>\\Desktop\\GoID"
        )
        
        print("📊 Container Status:")
        print(result.stdout)
        
        # Check if backend is running
        if "backend" in result.stdout and "Up" in result.stdout:
            print("✅ Backend container is running")
            return True
        else:
            print("❌ Backend container not running properly")
            return False
            
    except Exception as e:
        print(f"❌ Error checking Docker: {e}")
        return False

def fix_static_files():
    """Fix static files in Docker container"""
    
    print(f"\n🔧 Fixing Static Files...")
    print("=" * 25)
    
    commands = [
        # Collect static files
        ["docker-compose", "-f", "docker-compose.production.yaml", "exec", "-T", "backend", "python", "manage.py", "collectstatic", "--noinput"],

        # Run migrations
        ["docker-compose", "-f", "docker-compose.production.yaml", "exec", "-T", "backend", "python", "manage.py", "migrate"],

        # Check Django settings
        ["docker-compose", "-f", "docker-compose.production.yaml", "exec", "-T", "backend", "python", "manage.py", "shell", "-c",
         "from django.conf import settings; print('STATIC_URL:', settings.STATIC_URL); print('STATIC_ROOT:', settings.STATIC_ROOT)"],
    ]
    
    for i, cmd in enumerate(commands, 1):
        try:
            print(f"\n🔄 Step {i}: {' '.join(cmd[3:])}")
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd="C:\\Users\\<USER>\\Desktop\\GoID",
                timeout=60
            )
            
            if result.returncode == 0:
                print("   ✅ Success")
                if result.stdout.strip():
                    print(f"   Output: {result.stdout[-300:]}")
            else:
                print(f"   ❌ Error: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print("   ⏰ Command timed out")
        except Exception as e:
            print(f"   ❌ Command failed: {e}")

def check_django_urls():
    """Check Django URL configuration"""
    
    print(f"\n🔍 Checking Django URLs...")
    print("=" * 25)
    
    try:
        # Check URL patterns
        result = subprocess.run(
            ["docker-compose", "-f", "docker-compose.production.yaml", "exec", "-T", "backend", "python", "manage.py", "shell", "-c",
             "from django.urls import get_resolver; print('URL patterns:', [str(p.pattern) for p in get_resolver().url_patterns])"],
            capture_output=True,
            text=True,
            cwd="C:\\Users\\<USER>\\Desktop\\GoID",
            timeout=30
        )
        
        if result.returncode == 0:
            print("✅ URL Configuration:")
            print(result.stdout)
        else:
            print(f"❌ Error checking URLs: {result.stderr}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def restart_services():
    """Restart Docker services"""
    
    print(f"\n🔄 Restarting Services...")
    print("=" * 25)
    
    try:
        # Restart backend
        result = subprocess.run(
            ["docker-compose", "-f", "docker-compose.production.yaml", "restart", "backend"],
            capture_output=True,
            text=True,
            cwd="C:\\Users\\<USER>\\Desktop\\GoID"
        )
        
        if result.returncode == 0:
            print("✅ Backend restarted")
        else:
            print(f"❌ Restart failed: {result.stderr}")
        
        # Wait for startup
        import time
        print("⏰ Waiting for service to start...")
        time.sleep(15)
        
    except Exception as e:
        print(f"❌ Restart error: {e}")

def test_endpoints():
    """Test key endpoints"""
    
    print(f"\n🧪 Testing Endpoints...")
    print("=" * 20)
    
    import urllib.request
    
    endpoints = [
        ("Admin", "http://localhost:8000/admin/"),
        ("Static CSS", "http://localhost:8000/static/admin/css/base.css"),
        ("API Root", "http://localhost:8000/api/"),
        ("Biometrics", "http://localhost:8000/api/biometrics/"),
    ]
    
    for name, url in endpoints:
        try:
            response = urllib.request.urlopen(url, timeout=10)
            content_type = response.headers.get('Content-Type', 'unknown')
            print(f"✅ {name}: {response.getcode()} ({content_type})")
        except urllib.error.HTTPError as e:
            print(f"⚠️ {name}: HTTP {e.code}")
        except urllib.error.URLError as e:
            print(f"❌ {name}: Connection failed")
        except Exception as e:
            print(f"❌ {name}: {e}")

def create_root_url_fix():
    """Create a simple fix for root URL"""
    
    print(f"\n🔧 Creating Root URL Fix...")
    print("=" * 30)
    
    # Create a simple script to add root URL
    fix_script = '''
from django.conf import settings
from django.urls import include, path
from django.contrib import admin
from django.http import HttpResponse

def home_view(request):
    return HttpResponse("GoID Backend is running!")

# This should be added to your main urls.py
urlpatterns = [
    path('', home_view, name='home'),
    path('admin/', admin.site.urls),
    path('api/', include('your_api_app.urls')),  # Replace with actual app name
]
'''
    
    print("💡 Add this to your main urls.py file:")
    print(fix_script)

def main():
    """Main function"""
    
    print("GoID Docker Static Files Fixer")
    print("=" * 35)
    
    print("This will fix the Docker backend static files and URL issues.")
    print()
    
    # Check Docker status
    if not check_docker_status():
        print("❌ Docker containers not running properly")
        print("💡 Try: docker-compose up -d")
        return
    
    # Fix static files
    fix_static_files()
    
    # Check URL configuration
    check_django_urls()
    
    # Restart services
    restart_services()
    
    # Test endpoints
    test_endpoints()
    
    # Show root URL fix
    create_root_url_fix()
    
    print(f"\n" + "="*50)
    print("DOCKER BACKEND FIX SUMMARY")
    print("="*50)
    
    print(f"🔧 Issues Fixed:")
    print(f"   ✅ Static files collected")
    print(f"   ✅ Database migrations run")
    print(f"   ✅ Services restarted")
    
    print(f"\n💡 Manual fixes needed:")
    print(f"1. Add root URL pattern to main urls.py")
    print(f"2. Ensure static files are properly served in production")
    print(f"3. Check docker-compose.yml for static volume mounts")
    
    print(f"\n🧪 Test these URLs:")
    print(f"   http://localhost:8000/admin/")
    print(f"   http://localhost:8000/api/")
    print(f"   http://localhost:8000/api/biometrics/")
    
    print(f"\n🔧 If issues persist:")
    print(f"   docker-compose down")
    print(f"   docker-compose up -d --build")
    
    input("\nPress Enter to exit...")

if __name__ == '__main__':
    main()
