#!/usr/bin/env python3
"""
Test script to analyze what GonderFingerPrint.jar actually outputs
and fix the fingerprint data extraction
"""

import subprocess
import json
import time
import os
import base64
from datetime import datetime

def test_jar_output():
    """Test what the JAR actually outputs when run"""
    
    jar_path = os.path.abspath('./local-biometric-service/fingerPrint/GonderFingerPrint.jar')
    dll_path = os.path.abspath('./local-biometric-service/fingerPrint')
    
    print("🧪 TESTING JAR OUTPUT ANALYSIS")
    print("=" * 50)
    print(f"JAR Path: {jar_path}")
    print(f"DLL Path: {dll_path}")
    
    if not os.path.exists(jar_path):
        print("❌ JAR file not found!")
        return
    
    try:
        # Find Java
        java_exe = 'java'
        
        print(f"🚀 Testing JAR execution...")
        print("💡 This will run the JAR for 10 seconds to capture output")
        
        # Launch the JAR application
        cmd = [java_exe, '-jar', jar_path]
        
        print(f"Command: {' '.join(cmd)}")
        print(f"Working directory: {dll_path}")
        
        # Start the JAR application
        process = subprocess.Popen(
            cmd,
            cwd=dll_path,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print("⏰ JAR started - waiting 10 seconds for output...")
        
        # Wait for 10 seconds
        time.sleep(10)
        
        # Check if still running
        if process.poll() is None:
            print("🔄 JAR still running - terminating...")
            process.terminate()
            try:
                stdout, stderr = process.communicate(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
                stdout, stderr = process.communicate()
        else:
            stdout, stderr = process.communicate()
        
        print("\n📋 JAR OUTPUT ANALYSIS:")
        print("=" * 40)
        
        if stdout:
            print(f"📤 STDOUT ({len(stdout)} chars):")
            print("-" * 30)
            print(stdout)
            print("-" * 30)
            
            # Analyze the output
            analyze_jar_output(stdout)
        else:
            print("❌ No STDOUT output")
        
        if stderr:
            print(f"📤 STDERR ({len(stderr)} chars):")
            print("-" * 30)
            print(stderr)
            print("-" * 30)
        else:
            print("✅ No STDERR output")
        
        print(f"\n📊 Process info:")
        print(f"Return code: {process.returncode}")
        
    except Exception as e:
        print(f"❌ JAR test failed: {e}")

def analyze_jar_output(output):
    """Analyze the JAR output to understand its format"""
    
    print("\n🔍 DETAILED OUTPUT ANALYSIS:")
    print("=" * 40)
    
    lines = output.split('\n')
    print(f"Total lines: {len(lines)}")
    
    # Look for patterns
    patterns_found = {
        'buffered_image': [],
        'total_size': [],
        'success_indicators': [],
        'error_indicators': [],
        'fingerprint_data': [],
        'numeric_data': []
    }
    
    for i, line in enumerate(lines):
        line = line.strip()
        if not line:
            continue
            
        print(f"Line {i+1:2d}: {line}")
        
        # Check for patterns
        if 'BufferedImage' in line:
            patterns_found['buffered_image'].append((i+1, line))
        
        if 'Total' in line and ':' in line:
            patterns_found['total_size'].append((i+1, line))
        
        if any(word in line.lower() for word in ['success', 'complete', 'done', 'finished']):
            patterns_found['success_indicators'].append((i+1, line))
        
        if any(word in line.lower() for word in ['error', 'fail', 'exception', 'null']):
            patterns_found['error_indicators'].append((i+1, line))
        
        if any(word in line.lower() for word in ['fingerprint', 'template', 'minutiae', 'capture']):
            patterns_found['fingerprint_data'].append((i+1, line))
        
        # Look for numeric data that might be fingerprint data
        if any(char.isdigit() for char in line) and len(line) > 10:
            patterns_found['numeric_data'].append((i+1, line))
    
    print("\n📊 PATTERN ANALYSIS:")
    print("=" * 30)
    
    for pattern_name, matches in patterns_found.items():
        if matches:
            print(f"\n🔍 {pattern_name.upper()} ({len(matches)} matches):")
            for line_num, content in matches:
                print(f"  Line {line_num}: {content[:80]}...")
        else:
            print(f"\n❌ {pattern_name.upper()}: No matches found")
    
    # Generate recommendations
    print("\n💡 RECOMMENDATIONS:")
    print("=" * 30)
    
    if not patterns_found['buffered_image']:
        print("❌ No BufferedImage data found - JAR may not be capturing images")
    
    if not patterns_found['total_size']:
        print("❌ No total size data found - JAR may not be reporting data size")
    
    if not patterns_found['fingerprint_data']:
        print("❌ No fingerprint-specific data found - JAR may need different parameters")
    
    if patterns_found['error_indicators']:
        print("⚠️ Error indicators found - JAR may be encountering issues")
    
    if not any(patterns_found.values()):
        print("❌ CRITICAL: No meaningful output detected!")
        print("💡 The JAR may need:")
        print("   - Command line arguments")
        print("   - Interactive input")
        print("   - GUI interaction")
        print("   - Different execution method")

def create_fixed_jar_bridge():
    """Create a fixed version of the JAR bridge based on analysis"""
    
    print("\n🔧 CREATING FIXED JAR BRIDGE:")
    print("=" * 40)
    
    # This would create an improved version based on findings
    print("💡 Based on the analysis, the JAR bridge needs:")
    print("1. Better output parsing")
    print("2. Interactive capture workflow")
    print("3. Real data extraction methods")
    print("4. Proper error handling")

if __name__ == "__main__":
    test_jar_output()
    create_fixed_jar_bridge()
