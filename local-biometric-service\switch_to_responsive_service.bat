@echo off
echo ========================================
echo    SWITCHING TO RESPONSIVE SERVICE
echo ========================================
echo.
echo 🔄 FIXING THE ATTEMPT TRACKING ISSUE
echo.
echo 🎯 CURRENT PROBLEM:
echo   - You're still running the old service
echo   - Response shows "Method: smart_behavioral" 
echo   - Response shows "Attempts: 12"
echo   - This means global attempt tracking is still active
echo.
echo ✅ SOLUTION:
echo   - Stop current service
echo   - Start new responsive_detection_service.py
echo   - No more global attempt tracking
echo   - Each test will be independent
echo.

echo 🛑 Stopping any running biometric services...
taskkill /f /im python.exe /fi "WINDOWTITLE eq *biometric*" 2>nul
taskkill /f /im python.exe /fi "WINDOWTITLE eq *service*" 2>nul

echo.
echo ⏳ Waiting for services to stop...
timeout /t 3 /nobreak >nul

echo.
echo 🚀 Starting NEW responsive detection service...
echo.
echo 🎯 NEW SERVICE FEATURES:
echo   - Method: responsive_independent
echo   - No attempt tracking
echo   - Fresh analysis every time
echo   - Can alternate success/failure properly
echo.
echo 📱 NEW SERVICE URL: http://localhost:8001
echo.
echo 🧪 TESTING:
echo   - Quick click = "No finger detected"
echo   - Patient wait (3s) = "Finger detected"
echo   - Each test independent of previous tests
echo.

python responsive_detection_service.py

echo.
echo Service stopped.
pause
