import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CardContent,
  Typography,
  TextField,
  Grid,
  Divider,
  CircularProgress,
  Alert,
  Snackbar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Switch,
  FormControlLabel,
  Chip,
  Avatar,
  Tab,
  Tabs,
} from '@mui/material';
import {
  ArrowBack as BackIcon,
  Save as SaveIcon,
  CloudUpload as CloudUploadIcon,
  Business as BusinessIcon,
  LocationCity as LocationCityIcon,
  Home as HomeIcon,
  Person as PersonIcon,
  ContactMail as ContactIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import axios from '../../utils/axios';
import { useSharedData } from '../../contexts/SharedDataContext';

// Custom TabPanel component
function CustomTabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tenant-tabpanel-${index}`}
      aria-labelledby={`tenant-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

// Accessibility props for tabs
function a11yProps(index) {
  return {
    id: `tenant-tab-${index}`,
    'aria-controls': `tenant-tabpanel-${index}`,
  };
}

const EnhancedTenantEditForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { countries, regions, loading: sharedDataLoading } = useSharedData();
  
  const [tenant, setTenant] = useState(null);
  const [tenantProfile, setTenantProfile] = useState(null);
  const [formData, setFormData] = useState({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [tabValue, setTabValue] = useState(0);
  const [logoFile, setLogoFile] = useState(null);
  const [signatureFile, setSignatureFile] = useState(null);
  const [patternFile, setPatternFile] = useState(null);

  useEffect(() => {
    fetchTenantDetails();
  }, [id]);

  const fetchTenantDetails = async () => {
    try {
      setLoading(true);
      setError('');

      // Fetch tenant info with profile data
      const tenantResponse = await axios.get(`/api/tenants/${id}/`);
      const tenantData = tenantResponse.data;
      setTenant(tenantData);

      // Get profile data from the tenant response
      const profileData = tenantData.profile_data || {};
      setTenantProfile(profileData);

      // Validate region value against available regions
      console.log('🔍 Setting form data...');
      console.log('🔍 Available regions:', regions.map(r => ({ id: r.id, name: r.name })));
      console.log('🔍 Profile data region:', profileData.region);

      const validRegionIds = regions.map(r => r.id);
      const regionValue = profileData.region;
      const isValidRegion = !regionValue || validRegionIds.includes(regionValue);

      console.log('🔍 Region validation:', {
        regionValue,
        validRegionIds,
        isValidRegion
      });

      setFormData({
        // Basic tenant fields
        name: tenantData.name || '',

        // Profile-specific fields
        ...profileData,

        // Ensure boolean fields are properly set
        is_active: profileData.is_active !== undefined ? profileData.is_active : true,
        is_resident: profileData.is_resident !== undefined ? profileData.is_resident : false,

        // Reset region if it's not valid
        region: isValidRegion ? regionValue : '',
      });

    } catch (error) {
      console.error('Failed to fetch tenant details:', error);
      setError('Failed to load tenant details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    // If country changes, reset region to avoid out-of-range values
    if (name === 'country') {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value,
        region: '' // Reset region when country changes
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }));
    }
  };

  const handleSwitchChange = (name) => (event) => {
    setFormData(prev => ({
      ...prev,
      [name]: event.target.checked
    }));
  };

  const handleLogoChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setLogoFile(file);
    }
  };

  const handleSignatureChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setSignatureFile(file);
    }
  };

  const handlePatternChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setPatternFile(file);
    }
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.name?.trim() && !formData.city_name?.trim()) {
      setSnackbar({
        open: true,
        message: 'Name is required.',
        severity: 'error'
      });
      return;
    }

    try {
      setSaving(true);
      
      // Prepare form data for submission
      const submitData = new FormData();

      // Add all form fields except file fields (we'll handle those separately)
      const fileFields = ['logo', 'mayor_signature', 'pattern_image'];
      Object.keys(formData).forEach(key => {
        if (formData[key] !== null && formData[key] !== undefined && formData[key] !== '' && !fileFields.includes(key)) {
          submitData.append(key, formData[key]);
        }
      });

      // Only add files if new files are selected
      if (logoFile) {
        submitData.append('logo', logoFile);
      }
      if (signatureFile) {
        submitData.append('mayor_signature', signatureFile);
      }
      if (patternFile) {
        submitData.append('pattern_image', patternFile);
      }

      // Update based on tenant type
      let updateUrl;
      if (tenant.type === 'city' && tenantProfile?.id) {
        updateUrl = `/api/tenants/cities/${tenantProfile.id}/`;
      } else if (tenant.type === 'subcity' && tenantProfile?.id) {
        updateUrl = `/api/tenants/subcities/${tenantProfile.id}/`;
      } else if (tenant.type === 'kebele' && tenantProfile?.id) {
        updateUrl = `/api/tenants/kebeles/${tenantProfile.id}/`;
      } else {
        // Fallback to basic tenant update if no profile exists
        updateUrl = `/api/tenants/${id}/`;
      }

      console.log('🔍 Submitting form data:', submitData);
      console.log('🔍 Form data entries:');
      for (let [key, value] of submitData.entries()) {
        console.log(`  ${key}:`, value);
      }

      console.log('🔍 Original formData:', formData);
      console.log('🔍 Tenant type:', tenant?.type);
      console.log('🔍 Update URL:', updateUrl);
      console.log('🔍 Tenant profile ID:', tenantProfile?.id);

      await axios.patch(updateUrl, submitData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      setSnackbar({
        open: true,
        message: 'Tenant updated successfully!',
        severity: 'success'
      });

      // Navigate back to details page after a short delay
      setTimeout(() => {
        navigate(`/tenants/${id}/details`);
      }, 1500);

    } catch (error) {
      console.error('Failed to update tenant:', error);
      console.error('Error response:', error.response?.data);

      let errorMessage = 'Failed to update tenant. Please try again.';

      if (error.response?.data) {
        if (error.response.data.detail) {
          errorMessage = error.response.data.detail;
        } else if (typeof error.response.data === 'object') {
          // Handle validation errors
          const errors = [];
          Object.keys(error.response.data).forEach(field => {
            const fieldErrors = error.response.data[field];
            if (Array.isArray(fieldErrors)) {
              errors.push(`${field}: ${fieldErrors.join(', ')}`);
            } else {
              errors.push(`${field}: ${fieldErrors}`);
            }
          });
          if (errors.length > 0) {
            errorMessage = errors.join('; ');
          }
        }
      }

      setSnackbar({
        open: true,
        message: errorMessage,
        severity: 'error'
      });
    } finally {
      setSaving(false);
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'city': return <LocationCityIcon />;
      case 'subcity': return <BusinessIcon />;
      case 'kebele': return <HomeIcon />;
      default: return <BusinessIcon />;
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'city': return 'primary';
      case 'subcity': return 'secondary';
      case 'kebele': return 'success';
      default: return 'default';
    }
  };

  if (loading || sharedDataLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
        <Button
          variant="outlined"
          startIcon={<BackIcon />}
          onClick={() => navigate(`/tenants/${id}/details`)}
        >
          Back to Details
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<BackIcon />}
            onClick={() => navigate(`/tenants/${id}/details`)}
          >
            Back
          </Button>
          <Typography variant="h4" component="h1">
            Edit {tenant?.type?.charAt(0).toUpperCase() + tenant?.type?.slice(1)}: {tenant?.name}
          </Typography>
          <Chip
            icon={getTypeIcon(tenant?.type)}
            label={tenant?.type?.charAt(0).toUpperCase() + tenant?.type?.slice(1)}
            color={getTypeColor(tenant?.type)}
            variant="outlined"
          />
        </Box>
      </Box>

      {/* Edit Form */}
      <Card>
        <CardContent>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs value={tabValue} onChange={handleTabChange} aria-label="tenant edit tabs">
              <Tab icon={<InfoIcon />} label="Basic Information" {...a11yProps(0)} />
              {tenant?.type === 'city' && <Tab icon={<PersonIcon />} label="Leadership" {...a11yProps(1)} />}
              {tenant?.type === 'city' && <Tab icon={<ContactIcon />} label="Contact & Details" {...a11yProps(2)} />}
              {(tenant?.type === 'kebele' || tenant?.type === 'subcity') && <Tab icon={<CloudUploadIcon />} label="ID Card Customization" {...a11yProps(1)} />}
            </Tabs>
          </Box>

          <Box component="form" onSubmit={handleSubmit}>
            {/* Basic Information Tab */}
            <CustomTabPanel value={tabValue} index={0}>
              <Typography variant="h6" gutterBottom>
                Basic Information
              </Typography>
              <Divider sx={{ mb: 3 }} />

              <Grid container spacing={3}>
                {/* Name fields based on tenant type */}
                {tenant?.type === 'city' ? (
                  <>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        required
                        label="City Name (English)"
                        name="city_name"
                        value={formData.city_name || ''}
                        onChange={handleInputChange}
                        disabled={saving}
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="City Name (Amharic)"
                        name="city_name_am"
                        value={formData.city_name_am || ''}
                        onChange={handleInputChange}
                        disabled={saving}
                        helperText="City name in Amharic script"
                      />
                    </Grid>
                  </>
                ) : (
                  <>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        required
                        label={`${tenant?.type?.charAt(0).toUpperCase() + tenant?.type?.slice(1)} Name (English)`}
                        name="name"
                        value={formData.name || ''}
                        onChange={handleInputChange}
                        disabled={saving}
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label={`${tenant?.type?.charAt(0).toUpperCase() + tenant?.type?.slice(1)} Name (Amharic)`}
                        name="name_am"
                        value={formData.name_am || ''}
                        onChange={handleInputChange}
                        disabled={saving}
                        helperText="Name in Amharic script"
                      />
                    </Grid>
                  </>
                )}



                {/* Logo upload for all tenant types */}
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    {tenant?.type === 'city' ? 'City Logo' :
                     tenant?.type === 'subcity' ? 'Subcity Logo' : 'Kebele Logo'}
                  </Typography>
                  <Box
                    sx={{
                      border: '2px dashed #ddd',
                      borderRadius: 2,
                      p: 2,
                      textAlign: 'center',
                      backgroundColor: '#fafafa',
                      '&:hover': { borderColor: '#bbb' }
                    }}
                  >
                    {logoFile ? (
                      <Box>
                        <img
                          src={URL.createObjectURL(logoFile)}
                          alt="Logo Preview"
                          style={{
                            maxWidth: '120px',
                            maxHeight: '120px',
                            objectFit: 'contain',
                            marginBottom: '8px'
                          }}
                        />
                        <Typography variant="body2" sx={{ mb: 1 }}>
                          Selected: {logoFile.name}
                        </Typography>
                        <Button
                          variant="outlined"
                          color="error"
                          size="small"
                          onClick={() => setLogoFile(null)}
                        >
                          Remove
                        </Button>
                      </Box>
                    ) : (
                      <Box>
                        <CloudUploadIcon sx={{ fontSize: 40, color: '#bbb', mb: 1 }} />
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          Upload Logo
                        </Typography>
                        <Typography variant="caption" color="text.secondary" display="block" gutterBottom>
                          Recommended: 200x200px, PNG/JPG, Max 5MB
                        </Typography>
                        <input
                          accept="image/jpeg,image/jpg,image/png"
                          style={{ display: 'none' }}
                          id="logo-upload-enhanced"
                          type="file"
                          onChange={handleLogoChange}
                        />
                        <label htmlFor="logo-upload-enhanced">
                          <Button
                            variant="contained"
                            component="span"
                            startIcon={<CloudUploadIcon />}
                            size="small"
                          >
                            Choose File
                          </Button>
                        </label>
                        {formData.logo && (
                          <Typography variant="body2" sx={{ mt: 1, color: 'text.secondary' }}>
                            Current: {formData.logo.split('/').pop()}
                          </Typography>
                        )}
                      </Box>
                    )}
                  </Box>
                  <FormHelperText>
                    Official logo for {tenant?.type} administration
                  </FormHelperText>
                </Grid>

                {/* Status switches */}
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.is_active || false}
                        onChange={handleSwitchChange('is_active')}
                        disabled={saving}
                      />
                    }
                    label="Active Status"
                  />
                  <FormHelperText>
                    Enable or disable this {tenant?.type}
                  </FormHelperText>
                </Grid>

                {tenant?.type === 'city' && (
                  <Grid item xs={12} md={6}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={formData.is_resident || false}
                          onChange={handleSwitchChange('is_resident')}
                          disabled={saving}
                        />
                      }
                      label="Resident City"
                    />
                    <FormHelperText>
                      Mark as resident city
                    </FormHelperText>
                  </Grid>
                )}
              </Grid>
            </CustomTabPanel>

            {/* Leadership Tab (City only) */}
            {tenant?.type === 'city' && (
              <CustomTabPanel value={tabValue} index={1}>
                <Typography variant="h6" gutterBottom>
                  Leadership Information
                </Typography>
                <Divider sx={{ mb: 3 }} />

                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Mayor Name (English)"
                      name="mayor_name"
                      value={formData.mayor_name || ''}
                      onChange={handleInputChange}
                      disabled={saving}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Mayor Name (Amharic)"
                      name="mayor_name_am"
                      value={formData.mayor_name_am || ''}
                      onChange={handleInputChange}
                      disabled={saving}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Box>
                      <input
                        accept="image/*"
                        style={{ display: 'none' }}
                        id="signature-upload"
                        type="file"
                        onChange={handleSignatureChange}
                      />
                      <label htmlFor="signature-upload">
                        <Button
                          variant="outlined"
                          component="span"
                          startIcon={<CloudUploadIcon />}
                          sx={{ mb: 1 }}
                        >
                          Upload Mayor Signature
                        </Button>
                      </label>
                      {signatureFile && (
                        <Typography variant="body2" sx={{ mt: 1 }}>
                          Selected: {signatureFile.name}
                        </Typography>
                      )}
                      {formData.mayor_signature && !signatureFile && (
                        <Typography variant="body2" sx={{ mt: 1, color: 'text.secondary' }}>
                          Current signature: {formData.mayor_signature.split('/').pop()}
                        </Typography>
                      )}
                    </Box>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Deputy Mayor (English)"
                      name="deputy_mayor"
                      value={formData.deputy_mayor || ''}
                      onChange={handleInputChange}
                      disabled={saving}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Deputy Mayor (Amharic)"
                      name="deputy_mayor_am"
                      value={formData.deputy_mayor_am || ''}
                      onChange={handleInputChange}
                      disabled={saving}
                    />
                  </Grid>
                </Grid>
              </CustomTabPanel>
            )}

            {/* Contact & Details Tab (City only) */}
            {tenant?.type === 'city' && (
              <CustomTabPanel value={tabValue} index={2}>
                <Typography variant="h6" gutterBottom>
                  Contact & Additional Details
                </Typography>
                <Divider sx={{ mb: 3 }} />

                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      type="email"
                      label="Contact Email"
                      name="contact_email"
                      value={formData.contact_email || ''}
                      onChange={handleInputChange}
                      disabled={saving}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Contact Phone"
                      name="contact_phone"
                      value={formData.contact_phone || ''}
                      onChange={handleInputChange}
                      disabled={saving}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Country</InputLabel>
                      <Select
                        name="country"
                        value={formData.country || ''}
                        onChange={handleInputChange}
                        disabled={saving}
                      >
                        <MenuItem value="">
                          <em>Select a country</em>
                        </MenuItem>
                        {countries.map((country) => (
                          <MenuItem key={country.id} value={country.id}>
                            {country.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Region</InputLabel>
                      <Select
                        name="region"
                        value={formData.region || ''}
                        onChange={handleInputChange}
                        disabled={saving}
                      >
                        <MenuItem value="">
                          <em>Select a region</em>
                        </MenuItem>
                        {regions
                          .filter(region => !formData.country || region.country === formData.country)
                          .map((region) => (
                            <MenuItem key={region.id} value={region.id}>
                              {region.name}
                            </MenuItem>
                          ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      multiline
                      rows={3}
                      label="City Introduction"
                      name="city_intro"
                      value={formData.city_intro || ''}
                      onChange={handleInputChange}
                      disabled={saving}
                      helperText="Brief description of the city"
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Motto/Slogan"
                      name="motto_slogan"
                      value={formData.motto_slogan || ''}
                      onChange={handleInputChange}
                      disabled={saving}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Website"
                      name="website"
                      value={formData.website || ''}
                      onChange={handleInputChange}
                      disabled={saving}
                      placeholder="https://example.com"
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Google Maps URL"
                      name="google_maps_url"
                      value={formData.google_maps_url || ''}
                      onChange={handleInputChange}
                      disabled={saving}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      type="number"
                      label="Area (sq km)"
                      name="area_sq_km"
                      value={formData.area_sq_km || ''}
                      onChange={handleInputChange}
                      disabled={saving}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      type="number"
                      label="Elevation (meters)"
                      name="elevation_meters"
                      value={formData.elevation_meters || ''}
                      onChange={handleInputChange}
                      disabled={saving}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      multiline
                      rows={2}
                      label="Headquarter Address"
                      name="headquarter_address"
                      value={formData.headquarter_address || ''}
                      onChange={handleInputChange}
                      disabled={saving}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Postal Code"
                      name="postal_code"
                      value={formData.postal_code || ''}
                      onChange={handleInputChange}
                      disabled={saving}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      type="date"
                      label="Established Date"
                      name="established_date"
                      value={formData.established_date || ''}
                      onChange={handleInputChange}
                      disabled={saving}
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>
                </Grid>
              </CustomTabPanel>
            )}

            {/* ID Card Customization Tab (Kebele and Subcity) */}
            {(tenant?.type === 'kebele' || tenant?.type === 'subcity') && (
              <CustomTabPanel value={tabValue} index={1}>
                <Typography variant="h6" gutterBottom>
                  ID Card Customization
                </Typography>
                <Divider sx={{ mb: 3 }} />

                <Grid container spacing={3}>
                  {/* Mayor/Leader Signature Upload */}
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle1" gutterBottom>
                      {tenant?.type === 'kebele' ? 'Leader' : 'Admin'} Signature
                    </Typography>
                    <Box
                      sx={{
                        border: '2px dashed #ddd',
                        borderRadius: 2,
                        p: 3,
                        textAlign: 'center',
                        backgroundColor: '#fafafa',
                        '&:hover': { borderColor: '#bbb' }
                      }}
                    >
                      {signatureFile ? (
                        <Box>
                          <img
                            src={URL.createObjectURL(signatureFile)}
                            alt="Signature Preview"
                            style={{
                              maxWidth: '200px',
                              maxHeight: '80px',
                              objectFit: 'contain',
                              marginBottom: '8px'
                            }}
                          />
                          <Typography variant="body2" sx={{ mb: 1 }}>
                            Selected: {signatureFile.name}
                          </Typography>
                          <Button
                            variant="outlined"
                            color="error"
                            size="small"
                            onClick={() => setSignatureFile(null)}
                          >
                            Remove
                          </Button>
                        </Box>
                      ) : (
                        <Box>
                          <CloudUploadIcon sx={{ fontSize: 48, color: '#bbb', mb: 1 }} />
                          <Typography variant="body2" color="text.secondary" gutterBottom>
                            Upload {tenant?.type === 'kebele' ? 'Leader' : 'Admin'} Signature
                          </Typography>
                          <Typography variant="caption" color="text.secondary" display="block" gutterBottom>
                            Recommended: 200x80px, PNG/JPG, Max 5MB
                          </Typography>
                          <input
                            accept="image/jpeg,image/jpg,image/png"
                            style={{ display: 'none' }}
                            id="signature-upload-enhanced"
                            type="file"
                            onChange={handleSignatureChange}
                          />
                          <label htmlFor="signature-upload-enhanced">
                            <Button
                              variant="contained"
                              component="span"
                              startIcon={<CloudUploadIcon />}
                              size="small"
                            >
                              Choose File
                            </Button>
                          </label>
                          {formData.mayor_signature && (
                            <Typography variant="body2" sx={{ mt: 1, color: 'text.secondary' }}>
                              Current: {formData.mayor_signature.split('/').pop()}
                            </Typography>
                          )}
                        </Box>
                      )}
                    </Box>
                    <FormHelperText>
                      Signature image that will appear on approved ID cards
                    </FormHelperText>
                  </Grid>

                  {/* Security Pattern Upload */}
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle1" gutterBottom>
                      Security Pattern Image
                    </Typography>
                    <Box
                      sx={{
                        border: '2px dashed #ddd',
                        borderRadius: 2,
                        p: 3,
                        textAlign: 'center',
                        backgroundColor: '#fafafa',
                        '&:hover': { borderColor: '#bbb' }
                      }}
                    >
                      {patternFile ? (
                        <Box>
                          <img
                            src={URL.createObjectURL(patternFile)}
                            alt="Pattern Preview"
                            style={{
                              maxWidth: '200px',
                              maxHeight: '120px',
                              objectFit: 'contain',
                              marginBottom: '8px'
                            }}
                          />
                          <Typography variant="body2" sx={{ mb: 1 }}>
                            Selected: {patternFile.name}
                          </Typography>
                          <Button
                            variant="outlined"
                            color="error"
                            size="small"
                            onClick={() => setPatternFile(null)}
                          >
                            Remove
                          </Button>
                        </Box>
                      ) : (
                        <Box>
                          <CloudUploadIcon sx={{ fontSize: 48, color: '#bbb', mb: 1 }} />
                          <Typography variant="body2" color="text.secondary" gutterBottom>
                            Upload Security Pattern
                          </Typography>
                          <Typography variant="caption" color="text.secondary" display="block" gutterBottom>
                            Recommended: 500x300px, PNG with transparency, Max 5MB
                          </Typography>
                          <input
                            accept="image/jpeg,image/jpg,image/png"
                            style={{ display: 'none' }}
                            id="pattern-upload-enhanced"
                            type="file"
                            onChange={handlePatternChange}
                          />
                          <label htmlFor="pattern-upload-enhanced">
                            <Button
                              variant="contained"
                              component="span"
                              startIcon={<CloudUploadIcon />}
                              size="small"
                            >
                              Choose File
                            </Button>
                          </label>
                          {formData.pattern_image && (
                            <Typography variant="body2" sx={{ mt: 1, color: 'text.secondary' }}>
                              Current: {formData.pattern_image.split('/').pop()}
                            </Typography>
                          )}
                        </Box>
                      )}
                    </Box>
                    <FormHelperText>
                      Custom security pattern for ID card background
                    </FormHelperText>
                  </Grid>

                  {/* Preview Section */}
                  <Grid item xs={12}>
                    <Typography variant="subtitle1" gutterBottom>
                      Current Images
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                      {formData.mayor_signature && (
                        <Box sx={{ textAlign: 'center' }}>
                          <img
                            src={formData.mayor_signature}
                            alt="Current Signature"
                            style={{
                              maxWidth: '150px',
                              maxHeight: '60px',
                              objectFit: 'contain',
                              border: '1px solid #ddd',
                              borderRadius: '4px',
                              padding: '4px'
                            }}
                          />
                          <Typography variant="caption" display="block">
                            Current Signature
                          </Typography>
                        </Box>
                      )}
                      {formData.pattern_image && (
                        <Box sx={{ textAlign: 'center' }}>
                          <img
                            src={formData.pattern_image}
                            alt="Current Pattern"
                            style={{
                              maxWidth: '150px',
                              maxHeight: '90px',
                              objectFit: 'contain',
                              border: '1px solid #ddd',
                              borderRadius: '4px',
                              padding: '4px'
                            }}
                          />
                          <Typography variant="caption" display="block">
                            Current Pattern
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  </Grid>
                </Grid>
              </CustomTabPanel>
            )}

            {/* Action Buttons */}
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 4, pt: 2, borderTop: 1, borderColor: 'divider' }}>
              <Button
                variant="outlined"
                onClick={() => navigate(`/tenants/${id}/details`)}
                disabled={saving}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
                disabled={saving}
                size="large"
              >
                {saving ? 'Saving...' : 'Save Changes'}
              </Button>
            </Box>
          </Box>
        </CardContent>
      </Card>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default EnhancedTenantEditForm;
