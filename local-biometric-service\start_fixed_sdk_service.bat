@echo off
echo ========================================
echo   FIXED Futronic SDK Service
echo   Root Cause Analysis and Fixes Applied
echo ========================================
echo.

echo Checking Python installation...
python --version
if errorlevel 1 (
    echo ERROR: Python not found. Please install Python 3.7+
    pause
    exit /b 1
)

echo.
echo Checking required DLL files...
if not exist "ftrScanAPI.dll" (
    echo ERROR: ftrScanAPI.dll not found
    echo Please ensure Futronic SDK files are in the current directory
    pause
    exit /b 1
)

echo SUCCESS: ftrScanAPI.dll found

if exist "ftrMathAPI.dll" (
    echo SUCCESS: ftrMathAPI.dll found - Running debug analysis...
    echo.
    echo === Math API Debug Analysis ===
    python debug_math_api.py
    echo.
    echo === Debug Analysis Complete ===
    echo.
) else (
    echo WARNING: ftrMathAPI.dll not found - Template matching will be disabled
)

echo.
echo Installing required Python packages...
pip install flask flask-cors

echo.
echo Starting FIXED Futronic SDK Service...
echo.
echo FIXES APPLIED:
echo - Math API function discovery and proper configuration
echo - Thread-based timeout protection for SDK calls
echo - Crash-resistant finger detection
echo - Robust frame capture with retry logic
echo - Comprehensive error handling and logging
echo.
echo Service will be available at: http://localhost:8001
echo Press Ctrl+C to stop the service
echo.

python windows_compatible_sdk_service.py

echo.
echo Service stopped.
pause
