#!/usr/bin/env python3
"""
Minimal biometric service that only opens device and serves basic endpoints
"""

import logging
from flask import Flask, jsonify, request
from datetime import datetime
import ctypes
from ctypes import cdll, c_void_p

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

class MinimalDevice:
    def __init__(self):
        self.scan_api = None
        self.device_handle = None
        self.connected = False
    
    def initialize(self):
        """Minimal device initialization"""
        try:
            logger.info("🔧 Loading Futronic SDK...")
            self.scan_api = cdll.LoadLibrary('./ftrScanAPI.dll')
            
            # Setup minimal function signature
            self.scan_api.ftrScanOpenDevice.restype = c_void_p
            
            logger.info("📱 Opening device...")
            self.device_handle = self.scan_api.ftrScanOpenDevice()
            
            if self.device_handle:
                logger.info(f"✅ Device opened! Handle: {self.device_handle}")
                self.connected = True
                return True
            else:
                logger.error("❌ Failed to open device")
                return False
                
        except Exception as e:
            logger.error(f"❌ Initialization error: {e}")
            return False
    
    def get_status(self):
        """Get device status"""
        return {
            'connected': self.connected,
            'handle': self.device_handle,
            'model': 'Futronic FS88H',
            'interface': 'minimal_sdk'
        }

# Global device
device = MinimalDevice()

@app.route('/api/device/status', methods=['GET'])
def get_device_status():
    """Get device status"""
    return jsonify({
        'success': True,
        'data': device.get_status()
    })

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check"""
    return jsonify({
        'status': 'healthy',
        'service': 'Minimal Biometric Service',
        'timestamp': datetime.now().isoformat(),
        'device_connected': device.connected
    })

@app.route('/', methods=['GET'])
def index():
    """Simple status page"""
    status = device.get_status()
    
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Minimal Biometric Service</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; }}
            .status {{ padding: 20px; margin: 20px 0; border-radius: 5px; }}
            .connected {{ background: #d4edda; color: #155724; }}
            .disconnected {{ background: #f8d7da; color: #721c24; }}
        </style>
    </head>
    <body>
        <h1>🔒 Minimal Biometric Service</h1>
        
        <div class="status {'connected' if status['connected'] else 'disconnected'}">
            <h3>Device Status</h3>
            <p><strong>Connected:</strong> {'Yes' if status['connected'] else 'No'}</p>
            <p><strong>Handle:</strong> {status.get('handle', 'None')}</p>
            <p><strong>Model:</strong> {status.get('model', 'Unknown')}</p>
            <p><strong>Interface:</strong> {status.get('interface', 'Unknown')}</p>
        </div>
        
        <h3>API Endpoints:</h3>
        <ul>
            <li><a href="/api/device/status">/api/device/status</a></li>
            <li><a href="/api/health">/api/health</a></li>
        </ul>
        
        <p><em>This is a minimal service to test basic device connectivity.</em></p>
    </body>
    </html>
    """
    return html

if __name__ == '__main__':
    try:
        logger.info("🚀 Starting Minimal Biometric Service")
        
        # Initialize device
        logger.info("🔧 Initializing device...")
        if device.initialize():
            logger.info("✅ Device initialization successful")
        else:
            logger.warning("⚠️ Device initialization failed")
        
        # Start service
        logger.info("🌐 Starting Flask service on port 8001...")
        logger.info("📱 Open http://localhost:8001 to view status")
        
        app.run(
            host='0.0.0.0',
            port=8001,
            debug=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        logger.info("🛑 Service stopped by user")
    except Exception as e:
        logger.error(f"❌ Service error: {e}")
        input("Press Enter to exit...")
