import React from 'react';
import { useLocalization } from '../contexts/LocalizationContext';
import { Box, Typography, Button, Alert } from '@mui/material';

const QuickTranslationTest = () => {
  const { t, currentLanguage } = useLocalization();

  // Test the problematic keys
  const testKeys = [
    'generate_printable_book',
    'submit_for_approval',
    'kebele_approve',
    'fully_approved',
    'all_sub_cities',
    'photo',
    'name',
    'id_number',
    'population_by_ketena',
    'age_group_distribution',
    'search_citizens',
    'clear_filters',
    'pending_kebele_approval',
    'kebele_approved'
  ];

  console.log('🔍 Translation Test - Current Language:', currentLanguage);
  
  return (
    <Box sx={{ p: 2, border: '1px solid #ccc', borderRadius: 1, m: 2 }}>
      <Typography variant="h6" gutterBottom>
        🧪 Quick Translation Test ({currentLanguage})
      </Typography>
      
      <Alert severity="info" sx={{ mb: 2 }}>
        Testing translation keys that were reported as not working
      </Alert>

      {testKeys.map(key => {
        const translation = t(key, `FALLBACK_${key}`);
        const isWorking = translation !== key && translation !== `FALLBACK_${key}`;
        
        console.log(`🔍 Testing key "${key}": "${translation}" (working: ${isWorking})`);
        
        return (
          <Box key={key} sx={{ mb: 1, p: 1, bgcolor: isWorking ? 'success.light' : 'error.light', borderRadius: 1 }}>
            <Typography variant="body2">
              <strong>{key}:</strong> {translation} 
              {isWorking ? ' ✅' : ' ❌'}
            </Typography>
          </Box>
        );
      })}

      <Box sx={{ mt: 2 }}>
        <Typography variant="subtitle2" gutterBottom>Sample UI Elements:</Typography>
        <Button variant="outlined" size="small" sx={{ mr: 1 }}>
          {t('generate_printable_book', 'Generate Book')}
        </Button>
        <Button variant="outlined" size="small" sx={{ mr: 1 }}>
          {t('submit_for_approval', 'Submit')}
        </Button>
        <Button variant="outlined" size="small">
          {t('kebele_approve', 'Approve')}
        </Button>
      </Box>
    </Box>
  );
};

export default QuickTranslationTest;
