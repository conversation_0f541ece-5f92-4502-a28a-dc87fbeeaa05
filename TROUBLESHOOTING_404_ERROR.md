# 🔧 Troubleshooting 404 Error for Approval Workflow

## Problem
Getting a 404 error when trying to access the approval action endpoint:
```
Failed to load resource: the server responded with a status of 404 (Not Found)
Error submitting for approval: AxiosError
```

## Expected URL
The approval endpoint should be accessible at:
```
POST /api/tenants/{tenant_id}/idcards/{id}/approval_action/
```

## Step-by-Step Solution

### 1. 🔄 Restart Django Server
**This is the most common fix!** After adding new URL patterns, Django needs to be restarted.

```bash
# Stop the current Django server (Ctrl+C in the terminal)
# Then restart it:
cd backend
python manage.py runserver
```

### 2. 📊 Apply Database Migration
Apply the new migration for approval fields:

```bash
cd backend
python manage.py migrate
```

### 3. 🔍 Verify URL Pattern
Check that the URL pattern is correctly registered in `backend/tenants/urls.py`:

```python
# Should be on lines 81-83:
path('<int:tenant_id>/idcards/<int:pk>/approval_action/', TenantSpecificIDCardViewSet.as_view({
    'post': 'approval_action'
}), name='tenant-idcards-approval-action'),
```

### 4. 🧪 Test the Endpoint
Use the provided test script to verify the endpoint:

```bash
python test_approval_endpoint.py
```

### 5. 🔐 Check Authentication
Make sure you're logged in and have a valid JWT token. The endpoint requires authentication.

### 6. 📋 Verify Data Exists
Ensure the tenant and ID card exist in the database:
- Tenant ID should be valid
- ID card ID should exist in the tenant's schema
- User should have appropriate permissions

## Common Issues and Solutions

### Issue 1: Server Not Restarted
**Symptom:** 404 error immediately after adding new URLs
**Solution:** Restart Django development server

### Issue 2: Migration Not Applied
**Symptom:** Database errors or missing fields
**Solution:** Run `python manage.py migrate`

### Issue 3: Wrong URL Format
**Symptom:** 404 error with correct server restart
**Solution:** Verify URL format matches exactly:
```
/api/tenants/1/idcards/1/approval_action/
```

### Issue 4: Authentication Issues
**Symptom:** 401 or 403 errors
**Solution:** Check JWT token and user permissions

### Issue 5: Permission Denied
**Symptom:** 403 Forbidden error
**Solution:** Verify user has correct role (clerk, kebele_leader, etc.)

## Testing Commands

### Test URL Availability
```bash
# Test if URL is registered (should return 401/403, not 404)
curl -X POST http://localhost:8000/api/tenants/1/idcards/1/approval_action/
```

### Test with Authentication
```bash
# Replace YOUR_JWT_TOKEN with actual token
curl -X POST \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"action": "submit_for_approval", "comment": "Test"}' \
  http://localhost:8000/api/tenants/1/idcards/1/approval_action/
```

## Expected Response Codes

- **200**: Success - Action completed
- **400**: Bad Request - Invalid data or action
- **401**: Unauthorized - Not logged in
- **403**: Forbidden - No permission for this action
- **404**: Not Found - URL pattern not registered (needs server restart)

## Quick Checklist

- [ ] Django server restarted after URL changes
- [ ] Database migration applied (`python manage.py migrate`)
- [ ] User is authenticated with valid JWT token
- [ ] User has appropriate role (clerk for submit, kebele_leader for approve)
- [ ] Tenant ID and ID card ID exist in database
- [ ] URL format is exactly: `/api/tenants/{tenant_id}/idcards/{id}/approval_action/`

## If Still Not Working

1. Check Django server logs for detailed error messages
2. Verify the URL pattern in browser developer tools
3. Test with a simple GET request first to see if URL is registered
4. Check if there are any syntax errors in the views or URLs files
5. Ensure all imports are correct in the views files

## Contact Information

If the issue persists after following these steps, please provide:
1. Exact error message from browser console
2. Django server logs
3. URL being called (visible in browser network tab)
4. User role and authentication status
