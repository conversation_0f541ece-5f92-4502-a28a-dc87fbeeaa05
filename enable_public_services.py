#!/usr/bin/env python3
"""
Script to enable public services for the GoID system.
This script will:
1. Enable public_services_enabled system-wide
2. Enable public_id_application_enabled for all tenants
"""

import os
import sys
import django

# Add the backend directory to Python path
backend_path = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_path)

# Change to backend directory
os.chdir(backend_path)

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from tenants.models.system_settings import SystemSettings
from tenants.models.tenant import Tenant

def enable_public_services():
    """Enable public services system-wide and for all tenants"""
    
    print("🔧 Enabling Public Services for GoID System...")
    
    try:
        # 1. Enable system-wide public services
        print("\n1️⃣ Enabling system-wide public services...")
        
        system_settings, created = SystemSettings.objects.get_or_create(
            id=1,  # Assuming single system settings record
            defaults={
                'public_services_enabled': True,
                'primary_color': '#1976d2',
                'secondary_color': '#ef6c00'
            }
        )
        
        if not system_settings.public_services_enabled:
            system_settings.public_services_enabled = True
            system_settings.save()
            print("   ✅ System-wide public services enabled")
        else:
            print("   ℹ️ System-wide public services already enabled")
        
        # 2. Enable public ID application for all tenants
        print("\n2️⃣ Enabling public ID application for all tenants...")
        
        tenants = Tenant.objects.all()
        enabled_count = 0
        
        for tenant in tenants:
            if not tenant.public_id_application_enabled:
                tenant.public_id_application_enabled = True
                tenant.save()
                enabled_count += 1
                print(f"   ✅ Enabled public services for: {tenant.name}")
            else:
                print(f"   ℹ️ Public services already enabled for: {tenant.name}")
        
        print(f"\n📊 Summary:")
        print(f"   • System-wide public services: {'✅ Enabled' if system_settings.public_services_enabled else '❌ Disabled'}")
        print(f"   • Total tenants: {tenants.count()}")
        print(f"   • Tenants with public services enabled: {Tenant.objects.filter(public_id_application_enabled=True).count()}")
        print(f"   • Newly enabled tenants: {enabled_count}")
        
        print(f"\n🎉 Public Services Portal should now show service lists!")
        print(f"📝 You can now access the public portal and see available services from all enabled tenants.")
        
        return True
        
    except Exception as e:
        print(f"❌ Error enabling public services: {e}")
        import traceback
        traceback.print_exc()
        return False

def disable_public_services():
    """Disable public services system-wide and for all tenants"""
    
    print("🔧 Disabling Public Services for GoID System...")
    
    try:
        # 1. Disable system-wide public services
        print("\n1️⃣ Disabling system-wide public services...")
        
        try:
            system_settings = SystemSettings.objects.get(id=1)
            if system_settings.public_services_enabled:
                system_settings.public_services_enabled = False
                system_settings.save()
                print("   ✅ System-wide public services disabled")
            else:
                print("   ℹ️ System-wide public services already disabled")
        except SystemSettings.DoesNotExist:
            print("   ℹ️ No system settings found")
        
        # 2. Disable public ID application for all tenants
        print("\n2️⃣ Disabling public ID application for all tenants...")
        
        tenants = Tenant.objects.all()
        disabled_count = 0
        
        for tenant in tenants:
            if tenant.public_id_application_enabled:
                tenant.public_id_application_enabled = False
                tenant.save()
                disabled_count += 1
                print(f"   ✅ Disabled public services for: {tenant.name}")
            else:
                print(f"   ℹ️ Public services already disabled for: {tenant.name}")
        
        print(f"\n📊 Summary:")
        print(f"   • Total tenants: {tenants.count()}")
        print(f"   • Newly disabled tenants: {disabled_count}")
        
        print(f"\n🔒 Public Services Portal is now disabled!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error disabling public services: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_status():
    """Check current status of public services"""
    
    print("📊 Checking Public Services Status...")
    
    try:
        # Check system settings
        try:
            system_settings = SystemSettings.objects.get(id=1)
            system_enabled = system_settings.public_services_enabled
        except SystemSettings.DoesNotExist:
            system_enabled = False
        
        # Check tenant settings
        tenants = Tenant.objects.all()
        enabled_tenants = Tenant.objects.filter(public_id_application_enabled=True)
        
        print(f"\n📋 Current Status:")
        print(f"   • System-wide public services: {'✅ Enabled' if system_enabled else '❌ Disabled'}")
        print(f"   • Total tenants: {tenants.count()}")
        print(f"   • Tenants with public services enabled: {enabled_tenants.count()}")
        
        if enabled_tenants.exists():
            print(f"\n📝 Enabled tenants:")
            for tenant in enabled_tenants:
                print(f"   • {tenant.name} (ID: {tenant.id})")
        
        overall_status = system_enabled and enabled_tenants.exists()
        print(f"\n🎯 Overall Status: {'✅ Public Services Available' if overall_status else '❌ Public Services Unavailable'}")
        
        if not overall_status:
            if not system_enabled:
                print("   ⚠️ System-wide public services are disabled")
            if not enabled_tenants.exists():
                print("   ⚠️ No tenants have public services enabled")
        
        return overall_status
        
    except Exception as e:
        print(f"❌ Error checking status: {e}")
        return False

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='Manage Public Services for GoID System')
    parser.add_argument('action', choices=['enable', 'disable', 'status'], 
                       help='Action to perform: enable, disable, or check status')
    
    args = parser.parse_args()
    
    if args.action == 'enable':
        enable_public_services()
    elif args.action == 'disable':
        disable_public_services()
    elif args.action == 'status':
        check_status()
