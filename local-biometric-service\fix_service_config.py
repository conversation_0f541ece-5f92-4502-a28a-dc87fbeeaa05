#!/usr/bin/env python3
"""
Fix Service Configuration Issues
"""

import sys
import subprocess
import time
from pathlib import Path

def fix_service_configuration():
    """Fix the NSSM service configuration"""
    
    print("🔧 Fixing Service Configuration...")
    print("=" * 40)
    
    # Paths
    service_dir = Path(__file__).parent
    nssm_exe = service_dir / "nssm" / "nssm.exe"
    python_exe = sys.executable
    jar_bridge_script = service_dir / "working_jar_bridge.py"
    service_name = "GoIDJarBridge"
    
    if not nssm_exe.exists():
        print("❌ NSSM not found")
        return False
    
    print(f"📁 Service directory: {service_dir}")
    print(f"🐍 Python executable: {python_exe}")
    print(f"📜 JAR bridge script: {jar_bridge_script}")
    
    try:
        # Stop the service first
        print("\n⏹️ Stopping service...")
        subprocess.run([str(nssm_exe), "stop", service_name], capture_output=True)
        time.sleep(3)
        
        # Update service configuration
        print("⚙️ Updating service configuration...")
        
        # Set application path (Python executable)
        result = subprocess.run([str(nssm_exe), "set", service_name, "Application", python_exe], 
                              capture_output=True, text=True)
        print(f"   Application: {'✅' if result.returncode == 0 else '❌'}")
        
        # Set parameters (script path)
        result = subprocess.run([str(nssm_exe), "set", service_name, "AppParameters", str(jar_bridge_script)], 
                              capture_output=True, text=True)
        print(f"   Parameters: {'✅' if result.returncode == 0 else '❌'}")
        
        # Set working directory
        result = subprocess.run([str(nssm_exe), "set", service_name, "AppDirectory", str(service_dir)], 
                              capture_output=True, text=True)
        print(f"   Working Directory: {'✅' if result.returncode == 0 else '❌'}")
        
        # Set startup type to automatic
        result = subprocess.run([str(nssm_exe), "set", service_name, "Start", "SERVICE_AUTO_START"], 
                              capture_output=True, text=True)
        print(f"   Startup Type: {'✅' if result.returncode == 0 else '❌'}")
        
        # Set log files
        log_dir = service_dir / "logs"
        log_dir.mkdir(exist_ok=True)
        
        result = subprocess.run([str(nssm_exe), "set", service_name, "AppStdout", str(log_dir / "service_output.log")], 
                              capture_output=True, text=True)
        print(f"   Output Log: {'✅' if result.returncode == 0 else '❌'}")
        
        result = subprocess.run([str(nssm_exe), "set", service_name, "AppStderr", str(log_dir / "service_error.log")], 
                              capture_output=True, text=True)
        print(f"   Error Log: {'✅' if result.returncode == 0 else '❌'}")
        
        # Set restart behavior
        result = subprocess.run([str(nssm_exe), "set", service_name, "AppRestartDelay", "30000"], 
                              capture_output=True, text=True)
        print(f"   Restart Delay: {'✅' if result.returncode == 0 else '❌'}")
        
        # Set exit actions
        result = subprocess.run([str(nssm_exe), "set", service_name, "AppExit", "Default", "Restart"], 
                              capture_output=True, text=True)
        print(f"   Exit Action: {'✅' if result.returncode == 0 else '❌'}")
        
        print("✅ Service configuration updated!")
        return True
        
    except Exception as e:
        print(f"❌ Error updating service configuration: {e}")
        return False

def start_and_test_service():
    """Start the service and test it"""
    
    print("\n▶️ Starting service...")
    
    service_name = "GoIDJarBridge"
    
    try:
        # Start service
        result = subprocess.run(["sc", "start", service_name], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Service start command sent")
        else:
            print(f"⚠️ Service start command failed: {result.stderr}")
        
        # Wait for service to start
        print("⏰ Waiting 15 seconds for service to start...")
        time.sleep(15)
        
        # Check service status
        print("📊 Checking service status...")
        result = subprocess.run(["sc", "query", service_name], capture_output=True, text=True)
        print(result.stdout)
        
        # Test connectivity
        print("🧪 Testing service connectivity...")
        
        import urllib.request
        
        for attempt in range(5):
            try:
                response = urllib.request.urlopen("http://localhost:8001/api/device/status", timeout=10)
                
                if response.getcode() == 200:
                    print("✅ Service is responding correctly!")
                    print("🌐 API endpoint: http://localhost:8001/api/device/status")
                    return True
                else:
                    print(f"⚠️ Service responded with status: {response.getcode()}")
                    
            except Exception as e:
                print(f"❌ Attempt {attempt + 1}: {e}")
                if attempt < 4:
                    print("⏰ Waiting 5 seconds before retry...")
                    time.sleep(5)
        
        print("❌ Service is not responding after multiple attempts")
        return False
        
    except Exception as e:
        print(f"❌ Error testing service: {e}")
        return False

def check_service_logs():
    """Check service logs for errors"""
    
    print("\n📄 Checking service logs...")
    
    log_dir = Path("logs")
    
    if not log_dir.exists():
        print("❌ Logs directory not found")
        return
    
    # Check output log
    output_log = log_dir / "service_output.log"
    if output_log.exists() and output_log.stat().st_size > 0:
        print(f"\n📋 Service Output Log (last 10 lines):")
        try:
            with open(output_log, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                for line in lines[-10:]:
                    print(f"   {line.strip()}")
        except Exception as e:
            print(f"❌ Error reading output log: {e}")
    else:
        print("📋 No output log found")
    
    # Check error log
    error_log = log_dir / "service_error.log"
    if error_log.exists() and error_log.stat().st_size > 0:
        print(f"\n🚨 Service Error Log (last 10 lines):")
        try:
            with open(error_log, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                for line in lines[-10:]:
                    print(f"   {line.strip()}")
        except Exception as e:
            print(f"❌ Error reading error log: {e}")
    else:
        print("📋 No error log found (this is good)")

def main():
    """Main function"""
    
    print("GoID JAR Bridge Service Configuration Fixer")
    print("=" * 45)
    
    print("This will fix the service configuration that's causing the PAUSED state.")
    print("We know the JAR bridge works manually, so this is a service config issue.")
    print()
    
    # Fix service configuration
    if fix_service_configuration():
        # Start and test service
        if start_and_test_service():
            print("\n🎉 SUCCESS!")
            print("✅ Service is now running correctly")
            print("✅ API is responding")
            print("✅ Ready for biometric capture")
        else:
            print("\n⚠️ Service configuration updated but still not responding")
            check_service_logs()
            
            print("\n💡 Additional troubleshooting:")
            print("1. Check Windows Event Viewer for service errors")
            print("2. Try manual startup again: python working_jar_bridge.py")
            print("3. Check if port 8001 is blocked by firewall")
    else:
        print("\n❌ Failed to update service configuration")
        print("💡 Try reinstalling the service:")
        print("   python simple_service_installer.py uninstall")
        print("   python simple_service_installer.py install")
    
    input("\nPress Enter to exit...")

if __name__ == '__main__':
    main()
