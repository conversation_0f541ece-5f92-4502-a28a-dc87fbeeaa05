@echo off
echo 🚀 GoID Fixed Deployment
echo ========================
echo.

REM Check if Docker is running
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running. Please start Docker Desktop.
    pause
    exit /b 1
)

echo ✅ Docker is running
echo.

REM Ensure environment file exists
if not exist .env (
    echo 📋 Creating environment file...
    copy .env.production .env
    echo ✅ Environment file created
)

REM Stop and clean existing deployment
echo 🛑 Stopping existing containers...
docker-compose -f docker-compose.production.yml down -v

REM Clean up old containers and images
echo 🧹 Cleaning up...
docker system prune -f

REM Build and start database first
echo 🗄️ Starting database...
docker-compose -f docker-compose.production.yml up -d db redis

REM Wait for database to be ready
echo ⏳ Waiting for database to be ready...
timeout /t 30 /nobreak >nul

REM Check database status
echo 🔍 Checking database status...
docker-compose -f docker-compose.production.yml exec db pg_isready -U goid_user -d goid_db

REM Start all services
echo 🚀 Starting all services...
docker-compose -f docker-compose.production.yml up -d --build

REM Wait for services to start
echo ⏳ Waiting for services to start...
timeout /t 60 /nobreak >nul

echo ✅ Backend startup.sh handles all initialization:
echo   - Database migrations
echo   - Sample tenants creation
echo   - Sample users creation
echo   - Sample citizens creation
echo   - System configuration

REM Show final status
echo 🔍 Final Status:
docker-compose -f docker-compose.production.yml ps

echo.
echo ✅ Deployment Complete!
echo ======================
echo.
echo 🌐 Frontend: http://localhost:3000
echo 🔧 Backend API: http://localhost:8000
echo 🗄️ pgAdmin: http://localhost:5050
echo.
echo 👤 Default Users Created:
echo   - Super Admin: <EMAIL> / admin123
echo   - City Admin: <EMAIL> / password123
echo   - Subcity Admin: <EMAIL> / password123
echo   - Kebele Admin: <EMAIL> / password123
echo   - Clerk: <EMAIL> / password123
echo.
echo 🏢 Sample Tenants Created:
echo   - City: Addis Ababa (addis.goid.local)
echo   - Subcity: Bole (bole.goid.local)
echo   - Kebele: Bole 01 (bole01.goid.local)
echo.
echo 👥 Sample Citizens: 5 citizens with ID cards created
echo.
echo 📋 If there are issues, run: debug_database.bat
echo.
pause
