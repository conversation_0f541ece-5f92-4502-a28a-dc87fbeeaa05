from django.core.management.base import BaseCommand
from tenants.models import Tenant
from django_tenants.models import Domain


class Command(BaseCommand):
    help = 'Fix tenant domain configuration for tenants 42 and 46'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🔧 Fixing tenant domain configuration...'))
        
        tenant_ids = [42, 46]
        
        for tenant_id in tenant_ids:
            self.stdout.write(f'\n📋 Checking tenant {tenant_id}...')
            
            try:
                tenant = Tenant.objects.get(id=tenant_id)
                self.stdout.write(f'✅ Found tenant: {tenant.name} ({tenant.schema_name})')
                
                # Check existing domains
                domains = Domain.objects.filter(tenant=tenant)
                self.stdout.write(f'   - Existing domains: {domains.count()}')
                
                for domain in domains:
                    self.stdout.write(f'     * {domain.domain} (primary: {domain.is_primary})')
                
                # Create domain if none exists
                if domains.count() == 0:
                    domain_name = f'{tenant.schema_name}.localhost'
                    self.stdout.write(f'   - Creating domain: {domain_name}')
                    
                    domain = Domain.objects.create(
                        domain=domain_name,
                        tenant=tenant,
                        is_primary=True
                    )
                    self.stdout.write(f'   ✅ Created domain: {domain.domain}')
                
                # Ensure there's a primary domain
                primary_domains = domains.filter(is_primary=True)
                if primary_domains.count() == 0:
                    first_domain = domains.first()
                    if first_domain:
                        first_domain.is_primary = True
                        first_domain.save()
                        self.stdout.write(f'   ✅ Set {first_domain.domain} as primary')
                
                # Verify tenant can be resolved
                try:
                    resolved_tenant = Tenant.objects.get(id=tenant_id)
                    self.stdout.write(f'   ✅ Tenant resolution works: {resolved_tenant.name}')
                except Exception as e:
                    self.stdout.write(f'   ❌ Tenant resolution failed: {str(e)}')
                
            except Tenant.DoesNotExist:
                self.stdout.write(f'❌ Tenant {tenant_id} does not exist!')
            except Exception as e:
                self.stdout.write(f'❌ Error with tenant {tenant_id}: {str(e)}')
        
        self.stdout.write(self.style.SUCCESS('\n🎉 Domain configuration completed!'))
