from django.contrib import admin
from .models import WorkflowLog


@admin.register(WorkflowLog)
class WorkflowLogAdmin(admin.ModelAdmin):
    list_display = ('id_card', 'action', 'from_status', 'to_status', 'performed_by', 'performed_at')
    list_filter = ('action', 'from_status', 'to_status', 'performed_at')
    search_fields = ('id_card__card_number', 'comment', 'performed_by__username')
    readonly_fields = ('performed_at',)
    fieldsets = (
        ('Log Information', {
            'fields': ('id_card', 'action', 'from_status', 'to_status', 'comment')
        }),
        ('System Information', {
            'fields': ('performed_by', 'performed_at')
        }),
    )
