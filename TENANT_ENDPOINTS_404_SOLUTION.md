# 🔧 Tenant Endpoints 404 Error Solution

## Problem
Multiple tenant-related endpoints are returning 404 errors:
```
Failed to load resource: the server responded with a status of 404 (Not Found)
************:8000/api/tenants/
************:8000/api/tenants/subcities/
************:8000/api/tenants/kebeles/
```

## Root Cause Analysis
The issue is caused by **middleware confusion** between:
1. **Tenant management endpoints** (should use public schema)
2. **Tenant-specific data endpoints** (should use tenant schema)

The middleware was incorrectly treating all `/api/tenants/` URLs as tenant-specific patterns and trying to extract tenant IDs from them.

## ✅ **Complete Solution Applied**

### **Updated Middleware Logic** (`backend/tenants/middleware.py`)

#### **Before (Problematic):**
```python
# All /api/tenants/ URLs were treated as tenant-specific
if request.path.startswith('/api/tenants/'):
    # Try to extract tenant ID from URL...
```

#### **After (Fixed):**
```python
# Distinguish between tenant management and tenant-specific endpoints
tenant_management_patterns = [
    '/api/tenants/$',              # Tenant list
    '/api/tenants/\\d+/$',         # Tenant detail (CRUD)
    '/api/tenants/subcities/',     # Subcities list
    '/api/tenants/kebeles/',       # Kebeles list
    '/api/tenants/ketenas/',       # Ketenas list
    '/api/tenants/cities/',        # Cities list
    '/api/tenants/domains/',       # Domains list
    '/api/tenants/registration/',  # Tenant registration
    '/api/tenants/switch/',        # Tenant switching
]

# Check tenant management patterns
import re
for pattern in tenant_management_patterns:
    if re.match(pattern, request.path):
        # Use public schema for tenant management
        connection.set_schema_to_public()
        return None

# Only then check for tenant-specific patterns like /api/tenants/42/citizens/
```

## 🎯 **Endpoint Classification**

### **Tenant Management Endpoints** (Public Schema)
These manage tenant entities themselves:
- ✅ `/api/tenants/` - List all tenants
- ✅ `/api/tenants/{id}/` - CRUD operations on tenant
- ✅ `/api/tenants/subcities/` - List all subcities
- ✅ `/api/tenants/kebeles/` - List all kebeles
- ✅ `/api/tenants/cities/` - List all cities
- ✅ `/api/tenants/domains/` - List all domains
- ✅ `/api/tenants/registration/` - Tenant registration

### **Tenant-Specific Data Endpoints** (Tenant Schema)
These access data within a specific tenant:
- ✅ `/api/tenants/{id}/citizens/` - Citizens in tenant
- ✅ `/api/tenants/{id}/idcards/` - ID cards in tenant
- ✅ `/api/tenants/{id}/citizens/stats/` - Citizen stats for tenant

## 🚀 **Quick Fix Instructions**

### **Option 1: Use the Fix Script**
```bash
chmod +x fix_tenant_endpoints_404.sh
./fix_tenant_endpoints_404.sh
```

### **Option 2: Manual Steps**
```bash
# 1. Restart backend to apply middleware changes
docker-compose restart backend

# 2. Wait for restart
sleep 30

# 3. Test the endpoints
python test_all_tenant_endpoints.py

# 4. If needed, run migrations
docker-compose exec backend python manage.py migrate_schemas --shared
docker-compose exec backend python manage.py migrate_schemas
```

## 🧪 **Testing the Fix**

### **1. Test Tenant Management Endpoints**
```bash
# Test tenant list (should return 200 with auth)
curl -H "Authorization: Bearer TOKEN" http://************:8000/api/tenants/

# Test subcities (should return 200 with auth)
curl -H "Authorization: Bearer TOKEN" http://************:8000/api/tenants/subcities/

# Test kebeles (should return 200 with auth)
curl -H "Authorization: Bearer TOKEN" http://************:8000/api/tenants/kebeles/
```

### **2. Test Frontend Integration**
1. **Open frontend**: `http://************:3000/`
2. **Navigate to Tenants**: Should load without 404 errors
3. **Check citizen registration**: Subcity/kebele dropdowns should populate
4. **Check browser console**: No more 404 errors

### **3. Test Shared Endpoints** (for comparison)
```bash
# These should still work
curl http://************:8000/api/shared/countries/
curl http://************:8000/api/shared/regions/
```

## 🔍 **Verification Steps**

1. **No 404 errors** for tenant management endpoints
2. **Tenant list loads** in frontend
3. **Subcity/kebele dropdowns** populate in forms
4. **Tenant-specific endpoints** still work with proper tenant context
5. **Shared endpoints** continue to work normally

## 🆘 **Troubleshooting**

### **Issue: Still getting 404 on tenant endpoints**
**Solutions:**
1. **Restart backend**: `docker-compose restart backend`
2. **Check Django logs**: `docker-compose logs backend`
3. **Verify middleware**: Look for regex pattern matching in logs
4. **Test patterns**: Use the test script to verify each endpoint

### **Issue: Authentication errors (401/403)**
**Solutions:**
1. **Get valid token**: Login with `<EMAIL>` / `admin123`
2. **Check permissions**: Ensure user has appropriate role
3. **Verify headers**: Include `Authorization: Bearer TOKEN`
4. **Check user exists**: Verify superuser is created

### **Issue: Tenant-specific endpoints broken**
**Solutions:**
1. **Check tenant ID extraction**: Verify regex still works for `/api/tenants/42/citizens/`
2. **Test with valid tenant**: Ensure tenant exists in database
3. **Check schema switching**: Verify tenant schema is set correctly
4. **Test permissions**: Ensure user has access to specific tenant

### **Issue: Dropdowns still empty**
**Solutions:**
1. **Check data exists**: Verify subcities/kebeles exist in database
2. **Test API directly**: Use curl to test endpoints
3. **Check frontend cache**: Clear browser cache
4. **Verify authentication**: Ensure frontend sends proper auth headers

## 📋 **Expected Results After Fix**

- ✅ **Tenant list** returns 200 OK (not 404)
- ✅ **Subcities endpoint** returns 200 OK (not 404)
- ✅ **Kebeles endpoint** returns 200 OK (not 404)
- ✅ **Frontend tenant section** loads without errors
- ✅ **Citizen registration dropdowns** populate correctly
- ✅ **Tenant-specific endpoints** still work for data access
- ✅ **Clean browser console** without 404 errors

## 🔧 **Manual Debugging**

If issues persist, debug manually:

```bash
# Enter backend container
docker-compose exec backend bash

# Test Django shell
python manage.py shell

# In shell, test URL patterns:
from django.urls import resolve
try:
    match = resolve('/api/tenants/')
    print(f"Tenant list resolves to: {match.func}")
except:
    print("Tenant list URL does not resolve")

try:
    match = resolve('/api/tenants/subcities/')
    print(f"Subcities resolves to: {match.func}")
except:
    print("Subcities URL does not resolve")

# Test middleware patterns:
import re
patterns = ['/api/tenants/$', '/api/tenants/subcities/']
test_urls = ['/api/tenants/', '/api/tenants/subcities/']

for url in test_urls:
    for pattern in patterns:
        if re.match(pattern, url):
            print(f"URL {url} matches pattern {pattern}")
```

## 📞 **Support**

If tenant endpoints are still not working after following this guide:
1. Provide output of `python test_all_tenant_endpoints.py`
2. Share Django logs: `docker-compose logs backend`
3. Include browser console errors and network tab information
4. Confirm which specific endpoints are still failing

The solution addresses the core middleware logic that was incorrectly categorizing tenant management endpoints as tenant-specific data endpoints, causing them to fail with 404 errors.
