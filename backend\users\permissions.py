from rest_framework import permissions
from django.db import connection


class IsSuperUserOrAdmin(permissions.BasePermission):
    """
    Allows access to superusers or users with admin roles.
    """
    def has_permission(self, request, view):
        return bool(
            request.user and
            (request.user.is_superuser or
             request.user.role in ['superadmin', 'city_admin', 'subcity_admin', 'kebele_admin'])
        )


class IsSelfOrAdmin(permissions.BasePermission):
    """
    Allows users to edit their own profile or admins to edit any profile.
    """
    def has_object_permission(self, request, view, obj):
        # Allow if the user is editing their own profile
        if obj.id == request.user.id:
            return True

        # Allow if the user is a superuser or has an admin role
        return bool(
            request.user.is_superuser or
            request.user.role in ['superadmin', 'city_admin', 'subcity_admin', 'kebele_admin']
        )


class TenantPermission(permissions.BasePermission):
    """
    Permission class that checks if the user has access to the current tenant.
    """
    def has_permission(self, request, view):
        user = request.user

        # Superusers have access to all tenants
        if user.is_superuser:
            return True

        # Check if user has a tenant
        if not user.tenant:
            return False

        # Check if user's tenant matches the current tenant
        if hasattr(connection, 'tenant') and connection.tenant:
            current_tenant = connection.tenant
            user_tenant = user.tenant

            # Direct match
            if user_tenant == current_tenant:
                return True

            # Check hierarchy based on user's role
            if user.role == 'city_admin':
                # City admin can access subcities and kebeles under their city
                if current_tenant.type == 'subcity' and current_tenant.parent == user_tenant:
                    return True
                if current_tenant.type == 'kebele' and current_tenant.parent and current_tenant.parent.parent == user_tenant:
                    return True

            if user.role == 'subcity_admin':
                # Subcity admin can access kebeles under their subcity
                if current_tenant.type == 'kebele' and current_tenant.parent == user_tenant:
                    return True

        return False
