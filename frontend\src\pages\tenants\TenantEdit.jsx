import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Typography,
  TextField,
  Grid,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  Alert,
  CircularProgress,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material';
import {
  ArrowBack as BackIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import { useFormik } from 'formik';
import * as yup from 'yup';
import axios from '../../utils/axios';

// Mock data for tenants
const mockTenants = [
  {
    id: 1,
    name: 'Addis Ababa',
    type: 'city',
    parent: null,
    parent_name: null,
    schema_name: 'city_addis_ababa',
    domains: [
      {
        id: 1,
        domain: 'addis.goid.local',
        is_primary: true
      }
    ],
    created_on: '2023-11-15T12:00:00Z'
  },
  {
    id: 2,
    name: '<PERSON><PERSON>',
    type: 'subcity',
    parent: 1,
    parent_name: 'Addis Ababa',
    schema_name: 'subcity_bole',
    domains: [
      {
        id: 2,
        domain: 'bole.goid.local',
        is_primary: true
      }
    ],
    created_on: '2023-11-15T12:00:00Z'
  },
  {
    id: 3,
    name: 'Bole 01',
    type: 'kebele',
    parent: 2,
    parent_name: 'Bole',
    schema_name: 'kebele_bole_01',
    domains: [
      {
        id: 3,
        domain: 'bole01.goid.local',
        is_primary: true
      }
    ],
    created_on: '2023-11-15T12:00:00Z'
  }
];

// Mock data for parent tenants
const mockParentTenants = [
  {
    id: 1,
    name: 'Addis Ababa',
    type: 'city',
  },
  {
    id: 2,
    name: 'Bole',
    type: 'subcity',
    parent: 1,
  },
];

const validationSchema = yup.object({
  name: yup.string().required('Name is required'),
});

const domainValidationSchema = yup.object({
  domain: yup.string().required('Domain is required'),
  is_primary: yup.boolean(),
});

const TenantEdit = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [tenant, setTenant] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [parentTenants, setParentTenants] = useState([]);
  const [domainDialogOpen, setDomainDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedDomain, setSelectedDomain] = useState(null);

  useEffect(() => {
    // In a real implementation, this would fetch data from the API
    // For now, we'll just use mock data
    const timer = setTimeout(() => {
      const foundTenant = mockTenants.find(t => t.id === parseInt(id));
      if (foundTenant) {
        setTenant(foundTenant);
        setParentTenants(mockParentTenants);
      } else {
        setError('Tenant not found');
      }
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [id]);

  const formik = useFormik({
    initialValues: {
      name: '',
    },
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      try {
        setLoading(true);
        setError('');
        
        // In a real implementation, this would call an API endpoint
        // For now, we'll just simulate a successful request
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        setSuccess(true);
        setTimeout(() => {
          navigate('/tenants');
        }, 1500);
      } catch (err) {
        setError(err.response?.data?.detail || 'Failed to update tenant. Please try again.');
      } finally {
        setLoading(false);
      }
    },
    enableReinitialize: true,
  });

  const domainFormik = useFormik({
    initialValues: {
      domain: '',
      is_primary: false,
    },
    validationSchema: domainValidationSchema,
    onSubmit: async (values) => {
      try {
        // In a real implementation, this would call an API endpoint
        // For now, we'll just simulate a successful request
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Add the new domain to the tenant
        const newDomain = {
          id: Math.floor(Math.random() * 1000),
          domain: values.domain,
          is_primary: values.is_primary,
        };
        
        setTenant({
          ...tenant,
          domains: [...tenant.domains, newDomain],
        });
        
        // Close the dialog and reset the form
        setDomainDialogOpen(false);
        domainFormik.resetForm();
      } catch (err) {
        setError(err.response?.data?.detail || 'Failed to add domain. Please try again.');
      }
    },
  });

  // Set initial values when tenant data is loaded
  useEffect(() => {
    if (tenant) {
      formik.setValues({
        name: tenant.name,
      });
    }
  }, [tenant]);

  const handleDeleteDomain = (domain) => {
    setSelectedDomain(domain);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteDomain = async () => {
    try {
      // In a real implementation, this would call an API endpoint
      // For now, we'll just simulate a successful request
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Remove the domain from the tenant
      setTenant({
        ...tenant,
        domains: tenant.domains.filter(d => d.id !== selectedDomain.id),
      });
      
      // Close the dialog
      setDeleteDialogOpen(false);
      setSelectedDomain(null);
    } catch (err) {
      setError(err.response?.data?.detail || 'Failed to delete domain. Please try again.');
    }
  };

  if (loading && !tenant) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error && !tenant) {
    return (
      <Box sx={{ textAlign: 'center', py: 5 }}>
        <Typography variant="h5" color="error" gutterBottom>
          Error Loading Tenant
        </Typography>
        <Typography variant="body1">{error}</Typography>
        <Button
          variant="contained"
          startIcon={<BackIcon />}
          onClick={() => navigate('/tenants')}
          sx={{ mt: 3 }}
        >
          Back to Tenants
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Button
            variant="outlined"
            startIcon={<BackIcon />}
            onClick={() => navigate('/tenants')}
            sx={{ mr: 2 }}
          >
            Back
          </Button>
          <Typography variant="h4" component="h1">
            Edit Tenant
          </Typography>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          Tenant updated successfully!
        </Alert>
      )}

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <form onSubmit={formik.handleSubmit}>
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Tenant Information
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      id="name"
                      name="name"
                      label="Tenant Name"
                      value={formik.values.name}
                      onChange={formik.handleChange}
                      error={formik.touched.name && Boolean(formik.errors.name)}
                      helperText={formik.touched.name && formik.errors.name}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      id="type"
                      name="type"
                      label="Tenant Type"
                      value={tenant?.type || ''}
                      InputProps={{
                        readOnly: true,
                      }}
                    />
                  </Grid>
                  {tenant?.parent && (
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        id="parent"
                        name="parent"
                        label="Parent Tenant"
                        value={tenant?.parent_name || ''}
                        InputProps={{
                          readOnly: true,
                        }}
                      />
                    </Grid>
                  )}
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      id="schema_name"
                      name="schema_name"
                      label="Schema Name"
                      value={tenant?.schema_name || ''}
                      InputProps={{
                        readOnly: true,
                      }}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>

            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
              <Button
                type="submit"
                variant="contained"
                size="large"
                disabled={loading}
                sx={{ minWidth: 150 }}
              >
                {loading ? <CircularProgress size={24} /> : 'Update Tenant'}
              </Button>
            </Box>
          </form>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Domains
                </Typography>
                <Button
                  variant="outlined"
                  startIcon={<AddIcon />}
                  onClick={() => setDomainDialogOpen(true)}
                >
                  Add Domain
                </Button>
              </Box>
              <Divider sx={{ mb: 2 }} />
              <List>
                {tenant?.domains.map((domain) => (
                  <ListItem key={domain.id}>
                    <ListItemText
                      primary={domain.domain}
                      secondary={domain.is_primary ? 'Primary Domain' : 'Secondary Domain'}
                    />
                    <ListItemSecondaryAction>
                      <IconButton
                        edge="end"
                        aria-label="delete"
                        onClick={() => handleDeleteDomain(domain)}
                        disabled={domain.is_primary && tenant.domains.length > 1}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
                {tenant?.domains.length === 0 && (
                  <ListItem>
                    <ListItemText primary="No domains found" />
                  </ListItem>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Add Domain Dialog */}
      <Dialog open={domainDialogOpen} onClose={() => setDomainDialogOpen(false)}>
        <form onSubmit={domainFormik.handleSubmit}>
          <DialogTitle>Add Domain</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Add a new domain for this tenant. The domain will be used to access the tenant's data.
            </DialogContentText>
            <TextField
              autoFocus
              margin="dense"
              id="domain"
              name="domain"
              label="Domain"
              type="text"
              fullWidth
              value={domainFormik.values.domain}
              onChange={domainFormik.handleChange}
              error={domainFormik.touched.domain && Boolean(domainFormik.errors.domain)}
              helperText={domainFormik.touched.domain && domainFormik.errors.domain}
            />
            <FormControl fullWidth margin="dense">
              <InputLabel id="is-primary-label">Primary Domain</InputLabel>
              <Select
                labelId="is-primary-label"
                id="is_primary"
                name="is_primary"
                value={domainFormik.values.is_primary}
                label="Primary Domain"
                onChange={domainFormik.handleChange}
              >
                <MenuItem value={true}>Yes</MenuItem>
                <MenuItem value={false}>No</MenuItem>
              </Select>
              <FormHelperText>
                If set as primary, this domain will be used as the default domain for this tenant.
              </FormHelperText>
            </FormControl>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDomainDialogOpen(false)}>Cancel</Button>
            <Button type="submit" variant="contained">Add</Button>
          </DialogActions>
        </form>
      </Dialog>

      {/* Delete Domain Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Delete Domain</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the domain "{selectedDomain?.domain}"?
            This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmDeleteDomain} color="error" variant="contained">Delete</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TenantEdit;
