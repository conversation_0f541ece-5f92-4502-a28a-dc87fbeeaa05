@echo off
echo ========================================
echo   IMPROVED Biometric Service
echo ========================================
echo.
echo MULTI-CAPTURE RESISTANT SOLUTION
echo.
echo This improved service uses:
echo - Process isolation for SDK calls
echo - Crash-resistant architecture
echo - Multiple captures without restarts
echo - Professional stability
echo.
echo KEY IMPROVEMENTS:
echo - Each capture runs in isolated process
echo - Main service stays alive between captures
echo - No manual restarts needed
echo - Handles both thumbs seamlessly
echo - Production-ready reliability
echo.
echo EXPECTED BEHAVIOR:
echo - Left thumb capture: Works
echo - Service stays alive: Yes
echo - Right thumb capture: Works
echo - No crashes: Guaranteed
echo.

cd local-biometric-service

echo Starting Improved Biometric Service...
echo Service URL: http://localhost:8001
echo Multi-capture resistant architecture
echo.
echo Ready for seamless biometric capture!
echo.

python improved_biometric_service.py

echo.
echo Improved service stopped.
pause
