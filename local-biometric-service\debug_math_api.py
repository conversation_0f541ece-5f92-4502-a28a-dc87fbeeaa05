#!/usr/bin/env python3
"""
Debug Math API - Find actual function names in ftrMathAPI.dll
"""

import ctypes
from ctypes import cdll
import sys
import os

def debug_math_api():
    """Debug the Math API to find actual function names"""
    try:
        # Check if DLL exists
        dll_path = os.path.abspath('./ftrMathAPI.dll')
        if not os.path.exists(dll_path):
            print(f"ERROR: ftrMathAPI.dll not found at: {dll_path}")
            return
        
        print(f"Loading ftrMathAPI.dll from: {dll_path}")
        
        # Load the DLL
        math_api = cdll.LoadLibrary(dll_path)
        print("SUCCESS: ftrMathAPI.dll loaded successfully")
        
        # Try to find common function names
        possible_functions = [
            # Template extraction functions
            'ftrExtractTemplate',
            'FtrExtractTemplate', 
            'ftrMathExtractTemplate',
            'ExtractTemplate',
            'ftrExtract',
            'Extract',
            'CreateTemplate',
            'ftrCreateTemplate',
            
            # Template matching functions
            'ftrMatchTemplates',
            'FtrMatchTemplates',
            'ftrMathMatchTemplates',
            'MatchTemplates',
            'ftrMatch',
            'Match',
            'CompareTemplates',
            'ftrCompareTemplates',
            
            # Other possible functions
            'ftrMathInit',
            'ftrInit',
            'ftrMathClose',
            'ftrClose',
            'ftrGetVersion',
            'ftrMathGetVersion'
        ]
        
        found_functions = []
        
        for func_name in possible_functions:
            try:
                func = getattr(math_api, func_name)
                found_functions.append(func_name)
                print(f"FOUND: {func_name}")
            except AttributeError:
                print(f"NOT FOUND: {func_name}")
        
        print(f"\nSUMMARY: Found {len(found_functions)} functions in ftrMathAPI.dll:")
        for func in found_functions:
            print(f"  - {func}")
        
        if not found_functions:
            print("\nWARNING: No expected functions found in ftrMathAPI.dll")
            print("This DLL might be:")
            print("1. A different version with different function names")
            print("2. Corrupted or incomplete")
            print("3. Not the correct Math API DLL")
        
        return found_functions
        
    except Exception as e:
        print(f"ERROR: Failed to debug Math API: {e}")
        return []

if __name__ == '__main__':
    print("=== Futronic Math API Debug Tool ===")
    print()
    
    found_functions = debug_math_api()
    
    print()
    print("=== Debug Complete ===")
    
    if found_functions:
        print(f"Found {len(found_functions)} functions - Math API should work")
    else:
        print("No functions found - Math API will be disabled")
