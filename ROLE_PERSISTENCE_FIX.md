# Role Persistence Fix

## Problem Description

Users were experiencing a critical authentication issue where their role would persist across different tenant logins. For example:

1. User logs into a subcity tenant as `subcity_admin`
2. User logs out and logs into a kebele tenant
3. User's role in the kebele tenant incorrectly shows as `subcity_admin` instead of `clerk`

This happened because the JWT token generation was always using the user's single role from their database record (`user.role`), regardless of which tenant they were logging into.

## Root Cause

The issue was in the JWT token generation process in `backend/users/serializers.py`:

```python
# BEFORE (problematic code)
token['role'] = user.role  # Always used the same role from database
```

This meant that:
- Users have a single role stored in their database record
- JWT tokens always contained this single role
- The role didn't change based on tenant context
- Users accessing different tenants would have inappropriate roles

## Solution

### 1. Enhanced JWT Token Generation

Modified `CustomTokenObtainPairSerializer.get_token()` to determine the appropriate role based on tenant context:

```python
# AFTER (fixed code)
user_role = cls._determine_role_for_tenant(user, tenant)
token['role'] = user_role  # Use tenant-specific role
```

### 2. Tenant-Specific Role Determination

Added `_determine_role_for_tenant()` method that:

- **Preserves superuser/superadmin roles** regardless of tenant
- **Maintains hierarchy permissions** (city_admin can access subcities/kebeles)
- **Assigns appropriate default roles** for cross-tenant access
- **Defaults to 'clerk'** for kebele tenant access when user doesn't have specific kebele roles

### 3. Authentication Backend Enhancement

Updated `DomainBasedAuthBackend` to store tenant-specific role information:

```python
user._tenant_role = user.role  # Store role from tenant schema
```

### 4. Debug Logging

Added comprehensive debug logging to track:
- User authentication process
- Role determination logic
- JWT token generation
- Tenant context

## Role Assignment Logic

### For City Tenants
- Only `city_admin` role is valid
- Other users default to `clerk`

### For Subcity Tenants  
- `subcity_admin` role for subcity admins
- `city_admin` role maintained for city admins accessing subcities
- Other users default to `clerk`

### For Kebele Tenants
- `clerk` role as default for most users
- `kebele_leader` and `kebele_admin` roles preserved for users with those specific roles
- `subcity_admin` and `city_admin` roles maintained for hierarchy access

## Hierarchy Access Rules

1. **City Admin** can access:
   - Their city tenant (as `city_admin`)
   - Subcities under their city (as `city_admin`)
   - Kebeles under their city (as `city_admin`)

2. **Subcity Admin** can access:
   - Their subcity tenant (as `subcity_admin`)
   - Kebeles under their subcity (as `subcity_admin`)

3. **Kebele Users** can access:
   - Their kebele tenant (with their assigned role: `clerk`, `kebele_leader`, or `kebele_admin`)

## Files Modified

1. **`backend/users/serializers.py`**
   - Enhanced `get_token()` method
   - Added `_determine_role_for_tenant()` method
   - Added debug logging

2. **`backend/users/backends.py`**
   - Enhanced `DomainBasedAuthBackend`
   - Added tenant-specific role storage
   - Added debug logging

3. **`backend/tenants/authentication.py`**
   - Enhanced debug logging for JWT authentication

## Testing

Created `backend/test_role_fix.py` to verify the fix:

```bash
# Run the test
cd backend
python test_role_fix.py
```

The test verifies that:
1. User can login to subcity tenant with `subcity_admin` role
2. Same user can login to kebele tenant with `clerk` role
3. Roles don't persist across tenant logins

## Expected Behavior After Fix

✅ **Correct Behavior:**
- User logs into subcity → Gets `subcity_admin` role
- User logs into kebele → Gets `clerk` role (or appropriate kebele role)
- Roles are determined by tenant context, not previous logins

❌ **Previous Buggy Behavior:**
- User logs into subcity → Gets `subcity_admin` role
- User logs into kebele → Still gets `subcity_admin` role (WRONG!)

## Security Benefits

1. **Principle of Least Privilege**: Users only get the minimum role needed for each tenant
2. **Prevents Privilege Escalation**: Users can't retain higher privileges in inappropriate tenants
3. **Proper Access Control**: Each tenant login gets the correct role for that specific context
4. **Audit Trail**: Debug logs track role assignments for security monitoring

## Monitoring

The fix includes extensive debug logging. Monitor Django logs for:
- `🔍 Domain-based Authentication:` - Shows authentication process
- `🔍 JWT Token Generation:` - Shows role determination
- `🔍 TenantAwareJWTAuthentication:` - Shows JWT role application

This ensures the role persistence issue is permanently resolved and provides visibility into the authentication process.
