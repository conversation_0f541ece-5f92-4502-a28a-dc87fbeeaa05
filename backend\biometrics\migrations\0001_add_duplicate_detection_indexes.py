# Generated migration for duplicate detection performance optimization

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('biometrics', '__latest__'),  # Replace with actual latest migration
    ]

    operations = [
        # Add indexes for better duplicate detection performance
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_biometric_left_thumb_hash ON citizens_biometric USING hash (left_thumb_fingerprint);",
            reverse_sql="DROP INDEX IF EXISTS idx_biometric_left_thumb_hash;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_biometric_right_thumb_hash ON citizens_biometric USING hash (right_thumb_fingerprint);",
            reverse_sql="DROP INDEX IF EXISTS idx_biometric_right_thumb_hash;"
        ),
        
        # Add partial indexes for non-null fingerprints
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_biometric_left_thumb_partial ON citizens_biometric (left_thumb_fingerprint) WHERE left_thumb_fingerprint IS NOT NULL AND left_thumb_fingerprint != '';",
            reverse_sql="DROP INDEX IF EXISTS idx_biometric_left_thumb_partial;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_biometric_right_thumb_partial ON citizens_biometric (right_thumb_fingerprint) WHERE right_thumb_fingerprint IS NOT NULL AND right_thumb_fingerprint != '';",
            reverse_sql="DROP INDEX IF EXISTS idx_biometric_right_thumb_partial;"
        ),
        
        # Add composite index for citizen lookup
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_biometric_citizen_composite ON citizens_biometric (citizen_id, created_at);",
            reverse_sql="DROP INDEX IF EXISTS idx_biometric_citizen_composite;"
        ),
        
        # Add index for duplicate case management
        migrations.CreateModel(
            name='DuplicateCase',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('case_id', models.CharField(max_length=50, unique=True, editable=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('match_score', models.FloatField()),
                ('confidence_level', models.FloatField()),
                ('detection_method', models.CharField(max_length=50, default='FMatcher')),
                ('primary_citizen_id', models.IntegerField()),
                ('primary_citizen_name', models.CharField(max_length=200)),
                ('primary_citizen_digital_id', models.CharField(max_length=50)),
                ('primary_tenant_id', models.IntegerField()),
                ('primary_tenant_name', models.CharField(max_length=100)),
                ('secondary_citizen_id', models.IntegerField()),
                ('secondary_citizen_name', models.CharField(max_length=200)),
                ('secondary_citizen_digital_id', models.CharField(max_length=50)),
                ('secondary_tenant_id', models.IntegerField()),
                ('secondary_tenant_name', models.CharField(max_length=100)),
                ('status', models.CharField(max_length=30, choices=[
                    ('pending', 'Pending Review'),
                    ('under_review', 'Under Review'),
                    ('resolved_legitimate', 'Resolved - Legitimate Duplicate'),
                    ('resolved_fraud', 'Resolved - Fraud Detected'),
                    ('resolved_error', 'Resolved - System Error'),
                    ('escalated', 'Escalated to Higher Authority')
                ], default='pending')),
                ('priority', models.CharField(max_length=10, choices=[
                    ('low', 'Low'),
                    ('medium', 'Medium'),
                    ('high', 'High'),
                    ('critical', 'Critical')
                ], default='medium')),
                ('assigned_to', models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_duplicate_cases')),
                ('reviewed_by', models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, blank=True, related_name='reviewed_duplicate_cases')),
                ('review_date', models.DateTimeField(null=True, blank=True)),
                ('resolution_action', models.CharField(max_length=30, choices=[
                    ('approve_both', 'Approve Both Registrations'),
                    ('merge_records', 'Merge Records'),
                    ('block_new', 'Block New Registration'),
                    ('block_existing', 'Block Existing Registration'),
                    ('require_verification', 'Require Additional Verification'),
                    ('escalate', 'Escalate to Supervisor')
                ], null=True, blank=True)),
                ('resolution_notes', models.TextField(blank=True)),
                ('resolution_date', models.DateTimeField(null=True, blank=True)),
                ('fingerprint_data', models.JSONField(default=dict)),
                ('investigation_notes', models.TextField(blank=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        
        migrations.CreateModel(
            name='DuplicateCaseActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('case', models.ForeignKey('biometrics.DuplicateCase', on_delete=models.CASCADE, related_name='activities')),
                ('user', models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True)),
                ('activity_type', models.CharField(max_length=50)),
                ('description', models.TextField()),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('metadata', models.JSONField(default=dict)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        
        # Add indexes for duplicate case management
        migrations.AddIndex(
            model_name='duplicatecase',
            index=models.Index(fields=['status', 'priority'], name='idx_duplicate_case_status_priority'),
        ),
        migrations.AddIndex(
            model_name='duplicatecase',
            index=models.Index(fields=['assigned_to', 'status'], name='idx_duplicate_case_assigned_status'),
        ),
        migrations.AddIndex(
            model_name='duplicatecase',
            index=models.Index(fields=['created_at'], name='idx_duplicate_case_created'),
        ),
        migrations.AddIndex(
            model_name='duplicatecase',
            index=models.Index(fields=['primary_citizen_id'], name='idx_duplicate_case_primary_citizen'),
        ),
        migrations.AddIndex(
            model_name='duplicatecase',
            index=models.Index(fields=['secondary_citizen_id'], name='idx_duplicate_case_secondary_citizen'),
        ),
        
        # Add performance optimization for citizen queries
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_citizen_digital_id_hash ON citizens_citizen USING hash (digital_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_citizen_digital_id_hash;"
        ),
        
        # Add statistics table for performance monitoring
        migrations.RunSQL(
            """
            CREATE TABLE IF NOT EXISTS biometric_performance_stats (
                id SERIAL PRIMARY KEY,
                date DATE NOT NULL,
                total_checks INTEGER DEFAULT 0,
                duplicates_found INTEGER DEFAULT 0,
                cache_hits INTEGER DEFAULT 0,
                avg_processing_time FLOAT DEFAULT 0.0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(date)
            );
            """,
            reverse_sql="DROP TABLE IF EXISTS biometric_performance_stats;"
        ),
        
        # Add index for performance stats
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_biometric_stats_date ON biometric_performance_stats (date DESC);",
            reverse_sql="DROP INDEX IF EXISTS idx_biometric_stats_date;"
        ),
    ]
