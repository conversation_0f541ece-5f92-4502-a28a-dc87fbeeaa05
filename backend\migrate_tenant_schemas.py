#!/usr/bin/env python3
"""
Run migrations on all existing tenant schemas to create user tables.
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
sys.path.append('/app')
django.setup()

from django.db import connection
from django_tenants.utils import schema_context
from django.core.management import call_command

def migrate_all_tenant_schemas():
    """Run migrations on all existing tenant schemas."""
    cursor = connection.cursor()

    print('=== Migrating All Tenant Schemas ===')

    # Get all tenant schemas
    cursor.execute("SELECT schema_name FROM tenants_tenant WHERE schema_name != 'public';")
    schemas = cursor.fetchall()

    print(f'Found {len(schemas)} tenant schemas to migrate')

    for schema in schemas:
        schema_name = schema[0]
        print(f'\n--- Migrating schema: {schema_name} ---')

        try:
            with schema_context(schema_name):
                # Run migrations for this tenant schema
                call_command('migrate', verbosity=1)
                print(f'✅ Successfully migrated {schema_name}')
        except Exception as e:
            print(f'❌ Error migrating {schema_name}: {e}')

    print('\n=== Migration Complete ===')

    # Verify important tables were created
    print('\n=== Verification ===')
    for schema in schemas:
        schema_name = schema[0]

        # Check for user tables
        cursor.execute(f"SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = '{schema_name}' AND table_name = 'auth_user');")
        has_user_table = cursor.fetchone()[0]

        # Check for citizen tables
        cursor.execute(f"SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = '{schema_name}' AND table_name = 'tenants_citizen');")
        has_citizen_table = cursor.fetchone()[0]

        # Check for ID card tables
        cursor.execute(f"SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = '{schema_name}' AND table_name = 'idcards_idcard');")
        has_idcard_table = cursor.fetchone()[0]

        # Check for ID card template tables
        cursor.execute(f"SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = '{schema_name}' AND table_name = 'idcards_idcardtemplate');")
        has_template_table = cursor.fetchone()[0]

        print(f'{schema_name}:')
        print(f'  - auth_user: {has_user_table}')
        print(f'  - tenants_citizen: {has_citizen_table}')
        print(f'  - idcards_idcard: {has_idcard_table}')
        print(f'  - idcards_idcardtemplate: {has_template_table}')

    return True

if __name__ == "__main__":
    migrate_all_tenant_schemas()
