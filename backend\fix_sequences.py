#!/usr/bin/env python3
"""
Script to fix database sequence issues for tenant registration.
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
sys.path.append('/app')
django.setup()

from django.db import connection

def fix_sequences():
    """Fix database sequences for tenant models."""
    cursor = connection.cursor()

    try:
        # Fix SubCity sequence
        cursor.execute("""
            SELECT setval(
                pg_get_serial_sequence('tenants_subcity', 'id'),
                COALESCE((SELECT MAX(id) FROM tenants_subcity), 0) + 1
            );
        """)
        print("✅ SubCity sequence fixed")

        # Fix Kebele sequence
        cursor.execute("""
            SELECT setval(
                pg_get_serial_sequence('tenants_kebele', 'id'),
                COALESCE((SELECT MAX(id) FROM tenants_kebele), 0) + 1
            );
        """)
        print("✅ Kebele sequence fixed")

        # Fix Tenant sequence
        cursor.execute("""
            SELECT setval(
                pg_get_serial_sequence('tenants_tenant', 'id'),
                COALESCE((SELECT MAX(id) FROM tenants_tenant), 0) + 1
            );
        """)
        print("✅ Tenant sequence fixed")

        # Fix Domain sequence
        cursor.execute("""
            SELECT setval(
                pg_get_serial_sequence('tenants_domain', 'id'),
                COALESCE((SELECT MAX(id) FROM tenants_domain), 0) + 1
            );
        """)
        print("✅ Domain sequence fixed")

        print("\n🎉 All sequences have been fixed!")

    except Exception as e:
        print(f"❌ Error fixing sequences: {e}")
        return False

    return True

if __name__ == "__main__":
    fix_sequences()
