from django.core.management.base import BaseCommand
from django_tenants.utils import schema_context
from tenants.models import Tenant
from idcards.models import IDCardTemplate


class Command(BaseCommand):
    help = 'Initialize default ID card templates for all tenants'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force recreation of templates even if they already exist',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🌱 Initializing ID card templates for all tenants...'))

        force_recreate = options.get('force', False)
        if force_recreate:
            self.stdout.write(self.style.WARNING('⚠️  Force mode enabled - will recreate existing templates'))

        # Get all tenants
        tenants = Tenant.objects.all()
        self.stdout.write(f'📋 Found {tenants.count()} tenants')

        success_count = 0
        error_count = 0

        for tenant in tenants:
            self.stdout.write(f'\n🏢 Processing tenant: {tenant.name} ({tenant.schema_name})')

            try:
                with schema_context(tenant.schema_name):
                    # Check if templates already exist
                    existing_count = IDCardTemplate.objects.count()
                    if existing_count > 0 and not force_recreate:
                        self.stdout.write(f"  ✅ Templates already exist ({existing_count} templates), skipping...")
                        continue

                    if force_recreate and existing_count > 0:
                        self.stdout.write(f"  🗑️  Deleting {existing_count} existing templates...")
                        IDCardTemplate.objects.all().delete()

                    # Create default template
                    default_template = IDCardTemplate.objects.create(
                        name="Default Template",
                        description="Standard ID card template with official government styling",
                        background_color="#FFFFFF",
                        text_color="#000000",
                        accent_color="#1976D2",
                        logo_position="top_left",
                        photo_position="left",
                        header_text="Federal Democratic Republic of Ethiopia",
                        subtitle_text="National ID Card",
                        footer_text="",
                        is_active=True,
                        is_default=True
                    )
                    self.stdout.write(f"  ✅ Created: {default_template.name}")

                    # Create modern template
                    modern_template = IDCardTemplate.objects.create(
                        name="Modern Template",
                        description="Modern ID card template with contemporary design",
                        background_color="#F5F5F5",
                        text_color="#212121",
                        accent_color="#2196F3",
                        logo_position="top_center",
                        photo_position="right",
                        header_text="Federal Democratic Republic of Ethiopia",
                        subtitle_text="Digital ID Card",
                        footer_text="Valid for official identification",
                        is_active=True,
                        is_default=False
                    )
                    self.stdout.write(f"  ✅ Created: {modern_template.name}")

                    total_created = IDCardTemplate.objects.count()
                    self.stdout.write(
                        self.style.SUCCESS(f"  🎉 Successfully created {total_created} templates for {tenant.name}")
                    )
                    success_count += 1

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"  ❌ Error processing tenant {tenant.name}: {str(e)}")
                )
                error_count += 1
                continue

        # Summary
        self.stdout.write(f'\n📊 Summary:')
        self.stdout.write(f'  ✅ Successfully processed: {success_count} tenants')
        if error_count > 0:
            self.stdout.write(f'  ❌ Errors: {error_count} tenants')

        self.stdout.write(
            self.style.SUCCESS('\n🎉 Template initialization completed!')
        )
