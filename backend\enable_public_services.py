#!/usr/bin/env python3
"""
Simple script to enable public services using Django shell.
Run with: python manage.py shell < enable_public_services.py
"""

from tenants.models.system_settings import SystemSettings
from tenants.models.tenant import Tenant

print("🔧 Enabling Public Services for GoID System...")

try:
    # 1. Enable system-wide public services
    print("\n1️⃣ Enabling system-wide public services...")
    
    system_settings, created = SystemSettings.objects.get_or_create(
        id=1,  # Assuming single system settings record
        defaults={
            'public_services_enabled': True,
            'primary_color': '#1976d2',
            'secondary_color': '#ef6c00'
        }
    )
    
    if not system_settings.public_services_enabled:
        system_settings.public_services_enabled = True
        system_settings.save()
        print("   ✅ System-wide public services enabled")
    else:
        print("   ℹ️ System-wide public services already enabled")
    
    # 2. Enable public ID application for all tenants
    print("\n2️⃣ Enabling public ID application for all tenants...")
    
    tenants = Tenant.objects.all()
    enabled_count = 0
    
    for tenant in tenants:
        if not tenant.public_id_application_enabled:
            tenant.public_id_application_enabled = True
            tenant.save()
            enabled_count += 1
            print(f"   ✅ Enabled public services for: {tenant.name}")
        else:
            print(f"   ℹ️ Public services already enabled for: {tenant.name}")
    
    print(f"\n📊 Summary:")
    print(f"   • System-wide public services: {'✅ Enabled' if system_settings.public_services_enabled else '❌ Disabled'}")
    print(f"   • Total tenants: {tenants.count()}")
    print(f"   • Tenants with public services enabled: {Tenant.objects.filter(public_id_application_enabled=True).count()}")
    print(f"   • Newly enabled tenants: {enabled_count}")
    
    print(f"\n🎉 Public Services Portal should now show service lists!")
    print(f"📝 You can now access the public portal and see available services from all enabled tenants.")
    
except Exception as e:
    print(f"❌ Error enabling public services: {e}")
    import traceback
    traceback.print_exc()
