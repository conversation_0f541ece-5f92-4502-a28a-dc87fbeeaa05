import React from 'react';
import { useLocalization } from '../../contexts/LocalizationContext';
import LanguageSwitcher from '../../components/common/LanguageSwitcher';
import TranslationTestSuite from '../../components/TranslationTestSuite';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  Chip,
  Divider,
  Alert
} from '@mui/material';

const TranslationDemo = () => {
  const { t, currentLanguage, isAmharic, getAvailableLanguages } = useLocalization();

  const demoSections = [
    {
      title: 'Dashboard Statistics',
      keys: [
        'total_citizens', 'new_registrations', 'pending_approvals', 'flagged_cases',
        'approved_cards', 'expiring_soon', 'expired_ids', 'migration_activity'
      ]
    },
    {
      title: 'Common Actions',
      keys: [
        'save', 'cancel', 'submit', 'edit', 'delete', 'view', 'search',
        'approve', 'reject', 'print', 'back', 'next', 'retry'
      ]
    },
    {
      title: 'Form Labels',
      keys: [
        'name', 'gender', 'male', 'female', 'phone', 'location',
        'date_of_birth', 'id_number', 'photo'
      ]
    },
    {
      title: 'Status & States',
      keys: [
        'active', 'inactive', 'pending', 'approved', 'rejected', 'draft',
        'completed', 'in_progress', 'printed', 'issued'
      ]
    }
  ];

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h4" component="h1">
              🌐 GoID Translation Demo
            </Typography>
            <LanguageSwitcher size="lg" showLabel={true} />
          </Box>
          
          <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
            Demonstration of the GoID translation system with support for multiple Ethiopian languages.
            Current language: <strong>{currentLanguage}</strong>
          </Typography>

          <Alert severity="info" sx={{ mb: 2 }}>
            Switch between languages using the language switcher above to see translations in action.
            The system supports English, Amharic (አማርኛ), Oromo (Afaan Oromoo), Tigrinya (ትግርኛ), Somali (Soomaali), and Afar (Qafar).
          </Alert>

          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {getAvailableLanguages().map(lang => (
              <Chip
                key={lang.code}
                label={`${lang.nativeName} (${lang.code})`}
                color={lang.code === currentLanguage ? 'primary' : 'default'}
                variant={lang.code === currentLanguage ? 'filled' : 'outlined'}
              />
            ))}
          </Box>
        </CardContent>
      </Card>

      {/* Demo Sections */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {demoSections.map((section, index) => (
          <Grid item xs={12} md={6} key={index}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom color="primary">
                  {section.title}
                </Typography>
                <Divider sx={{ mb: 2 }} />
                
                <Grid container spacing={1}>
                  {section.keys.map(key => (
                    <Grid item xs={12} sm={6} key={key}>
                      <Box sx={{ 
                        p: 1, 
                        border: '1px solid', 
                        borderColor: 'divider', 
                        borderRadius: 1,
                        backgroundColor: 'background.paper'
                      }}>
                        <Typography variant="caption" color="text.secondary" display="block">
                          {key}
                        </Typography>
                        <Typography 
                          variant="body2" 
                          sx={{ 
                            fontWeight: 'medium',
                            direction: isAmharic ? 'rtl' : 'ltr',
                            textAlign: isAmharic ? 'right' : 'left'
                          }}
                        >
                          {t(key, `[${key}]`)}
                        </Typography>
                      </Box>
                    </Grid>
                  ))}
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Sample UI Components */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom color="primary">
            Sample UI Components with Translations
          </Typography>
          <Divider sx={{ mb: 2 }} />
          
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <Button variant="contained" fullWidth>
                {t('save', 'Save')}
              </Button>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Button variant="outlined" fullWidth>
                {t('cancel', 'Cancel')}
              </Button>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Button variant="contained" color="success" fullWidth>
                {t('approve', 'Approve')}
              </Button>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Button variant="contained" color="error" fullWidth>
                {t('reject', 'Reject')}
              </Button>
            </Grid>
          </Grid>

          <Box sx={{ mt: 3 }}>
            <Typography variant="subtitle2" gutterBottom>
              Status Chips:
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Chip label={t('pending', 'Pending')} color="warning" />
              <Chip label={t('approved', 'Approved')} color="success" />
              <Chip label={t('rejected', 'Rejected')} color="error" />
              <Chip label={t('draft', 'Draft')} color="default" />
              <Chip label={t('completed', 'Completed')} color="info" />
            </Box>
          </Box>
        </CardContent>
      </Card>

      {/* Translation Test Suite */}
      <TranslationTestSuite />

      {/* Footer */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="body2" color="text.secondary" align="center">
            GoID Translation System - Supporting Ethiopian multilingual requirements
          </Typography>
          <Typography variant="caption" color="text.secondary" align="center" display="block">
            Built with React, Material-UI, and comprehensive i18n support
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default TranslationDemo;
