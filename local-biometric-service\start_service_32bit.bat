@echo off
echo 🚀 Starting GoID Biometric Service (32-bit Python)
echo ================================================
echo.

REM Try to find 32-bit Python installation
set PYTHON32=""

REM Common 32-bit Python installation paths
if exist "C:\Python311-32\python.exe" set PYTHON32="C:\Python311-32\python.exe"
if exist "C:\Python310-32\python.exe" set PYTHON32="C:\Python310-32\python.exe"
if exist "C:\Python39-32\python.exe" set PYTHON32="C:\Python39-32\python.exe"
if exist "C:\Program Files (x86)\Python311\python.exe" set PYTHON32="C:\Program Files (x86)\Python311\python.exe"
if exist "C:\Program Files (x86)\Python310\python.exe" set PYTHON32="C:\Program Files (x86)\Python310\python.exe"

REM Check if we found 32-bit Python
if %PYTHON32%=="" (
    echo ❌ 32-bit Python not found!
    echo.
    echo Please install 32-bit Python from:
    echo https://www.python.org/downloads/windows/
    echo.
    echo Install to: C:\Python311-32\
    echo.
    pause
    exit /b 1
)

echo ✅ Found 32-bit Python: %PYTHON32%
echo.

REM Check Python architecture
%PYTHON32% -c "import platform, struct; print(f'Python: {platform.architecture()[0]} - {\"64-bit\" if struct.calcsize(\"P\")*8==64 else \"32-bit\"}')"

REM Install dependencies if needed
echo 📦 Installing dependencies...
%PYTHON32% -m pip install Flask Flask-CORS requests

echo.
echo 🔧 Testing SDK compatibility...
%PYTHON32% check_dll_architecture.py

echo.
echo 🧪 Testing Futronic SDK...
%PYTHON32% test_futronic_sdk.py

echo.
echo 🌐 Starting biometric service...
%PYTHON32% biometric_service.py

pause
