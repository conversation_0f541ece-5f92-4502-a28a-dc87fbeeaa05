"""
Windows Service Installer for Futronic ANSI/ISO SDK Service
Uses NSSM (Non-Sucking Service Manager) to install the service
"""

import os
import sys
import subprocess
import shutil
import requests
import zipfile
import tempfile

def download_nssm():
    """Download NSSM if not present"""
    nssm_path = "nssm.exe"
    
    if os.path.exists(nssm_path):
        print("✅ NSSM already exists")
        return nssm_path
    
    print("📥 Downloading NSSM...")
    try:
        # Download NSSM
        nssm_url = "https://nssm.cc/release/nssm-2.24.zip"
        response = requests.get(nssm_url, stream=True)
        response.raise_for_status()
        
        # Save to temp file
        with tempfile.NamedTemporaryFile(delete=False, suffix='.zip') as temp_file:
            for chunk in response.iter_content(chunk_size=8192):
                temp_file.write(chunk)
            temp_zip_path = temp_file.name
        
        # Extract NSSM
        with zipfile.ZipFile(temp_zip_path, 'r') as zip_ref:
            # Find the correct NSSM executable based on architecture
            arch = "win64" if sys.maxsize > 2**32 else "win32"
            nssm_exe_path = f"nssm-2.24/{arch}/nssm.exe"
            
            # Extract just the NSSM executable
            with zip_ref.open(nssm_exe_path) as source, open(nssm_path, 'wb') as target:
                shutil.copyfileobj(source, target)
        
        # Clean up
        os.unlink(temp_zip_path)
        
        print("✅ NSSM downloaded successfully")
        return nssm_path
        
    except Exception as e:
        print(f"❌ Failed to download NSSM: {e}")
        return None

def install_ansi_service():
    """Install Futronic ANSI/ISO SDK service"""
    
    service_name = "FutronicAnsiSDKService"
    service_display_name = "Futronic ANSI/ISO SDK Service"
    service_description = "Professional fingerprint template extraction and matching using Futronic ANSI/ISO SDK"
    
    # Get current directory and Python executable
    current_dir = os.path.abspath(os.path.dirname(__file__))
    python_exe = sys.executable
    service_script = os.path.join(current_dir, "futronic_ansi_service.py")
    
    print(f"🔧 Installing {service_display_name}...")
    print(f"   Service Name: {service_name}")
    print(f"   Python: {python_exe}")
    print(f"   Script: {service_script}")
    
    # Download NSSM
    nssm_path = download_nssm()
    if not nssm_path:
        print("❌ Cannot proceed without NSSM")
        return False
    
    try:
        # Stop service if it exists
        print("🛑 Stopping existing service (if any)...")
        subprocess.run([nssm_path, "stop", service_name], 
                      capture_output=True, text=True, check=False)
        
        # Remove service if it exists
        print("🗑️ Removing existing service (if any)...")
        subprocess.run([nssm_path, "remove", service_name, "confirm"], 
                      capture_output=True, text=True, check=False)
        
        # Install new service
        print("📦 Installing new service...")
        result = subprocess.run([
            nssm_path, "install", service_name, python_exe, service_script
        ], capture_output=True, text=True, check=True)
        
        # Configure service
        print("⚙️ Configuring service...")
        
        # Set display name
        subprocess.run([nssm_path, "set", service_name, "DisplayName", service_display_name], 
                      capture_output=True, text=True, check=True)
        
        # Set description
        subprocess.run([nssm_path, "set", service_name, "Description", service_description], 
                      capture_output=True, text=True, check=True)
        
        # Set startup type to automatic
        subprocess.run([nssm_path, "set", service_name, "Start", "SERVICE_AUTO_START"], 
                      capture_output=True, text=True, check=True)
        
        # Set working directory
        subprocess.run([nssm_path, "set", service_name, "AppDirectory", current_dir], 
                      capture_output=True, text=True, check=True)
        
        # Configure restart policy
        subprocess.run([nssm_path, "set", service_name, "AppRestartDelay", "5000"], 
                      capture_output=True, text=True, check=True)
        
        # Set log files
        log_dir = os.path.join(current_dir, "logs")
        os.makedirs(log_dir, exist_ok=True)
        
        stdout_log = os.path.join(log_dir, "ansi_service_stdout.log")
        stderr_log = os.path.join(log_dir, "ansi_service_stderr.log")
        
        subprocess.run([nssm_path, "set", service_name, "AppStdout", stdout_log], 
                      capture_output=True, text=True, check=True)
        subprocess.run([nssm_path, "set", service_name, "AppStderr", stderr_log], 
                      capture_output=True, text=True, check=True)
        
        # Start the service
        print("🚀 Starting service...")
        result = subprocess.run([nssm_path, "start", service_name], 
                               capture_output=True, text=True, check=True)
        
        print("✅ Service installed and started successfully!")
        print(f"   Service Name: {service_name}")
        print(f"   Display Name: {service_display_name}")
        print(f"   Status: Running")
        print(f"   Port: 8002")
        print(f"   Logs: {log_dir}")
        
        # Test service
        print("\n🧪 Testing service...")
        import time
        time.sleep(3)  # Wait for service to start
        
        try:
            import requests
            response = requests.get("http://localhost:8002/health", timeout=5)
            if response.status_code == 200:
                health_data = response.json()
                print("✅ Service is responding correctly")
                print(f"   SDK Initialized: {health_data.get('sdk_initialized')}")
                print(f"   Standards: {health_data.get('standards')}")
            else:
                print(f"⚠️ Service responded with status: {response.status_code}")
        except Exception as e:
            print(f"⚠️ Service test failed: {e}")
            print("   The service may still be starting up")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Service installation failed: {e}")
        print(f"   Command: {' '.join(e.cmd)}")
        print(f"   Output: {e.stdout}")
        print(f"   Error: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def uninstall_ansi_service():
    """Uninstall Futronic ANSI/ISO SDK service"""
    
    service_name = "FutronicAnsiSDKService"
    
    print(f"🗑️ Uninstalling {service_name}...")
    
    # Download NSSM
    nssm_path = download_nssm()
    if not nssm_path:
        print("❌ Cannot proceed without NSSM")
        return False
    
    try:
        # Stop service
        print("🛑 Stopping service...")
        subprocess.run([nssm_path, "stop", service_name], 
                      capture_output=True, text=True, check=False)
        
        # Remove service
        print("🗑️ Removing service...")
        result = subprocess.run([nssm_path, "remove", service_name, "confirm"], 
                               capture_output=True, text=True, check=True)
        
        print("✅ Service uninstalled successfully!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Service uninstallation failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    """Main function"""
    print("🔧 Futronic ANSI/ISO SDK Service Installer")
    print("=" * 50)
    
    if len(sys.argv) > 1 and sys.argv[1] == "uninstall":
        success = uninstall_ansi_service()
    else:
        print("📋 This will install the Futronic ANSI/ISO SDK Service as a Windows service")
        print("📋 The service will start automatically with Windows")
        print("📋 Service will run on port 8002")
        
        confirm = input("\nDo you want to proceed? (y/N): ").strip().lower()
        if confirm in ['y', 'yes']:
            success = install_ansi_service()
        else:
            print("❌ Installation cancelled")
            return
    
    if success:
        print("\n🎉 Operation completed successfully!")
    else:
        print("\n❌ Operation failed!")

if __name__ == "__main__":
    # Check if running as administrator
    try:
        import ctypes
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        if not is_admin:
            print("❌ This script requires administrator privileges")
            print("   Please run as administrator")
            input("Press Enter to exit...")
            sys.exit(1)
    except:
        pass
    
    main()
