Write-Host "🔍 Checking for Futronic FS88H Device..." -ForegroundColor Green

# Check Device Manager for Futronic devices
Write-Host "`nChecking Device Manager..." -ForegroundColor Cyan
try {
    $devices = Get-PnpDevice | Where-Object {$_.FriendlyName -like "*Futronic*"}
    if ($devices) {
        Write-Host "✅ Futronic device(s) found:" -ForegroundColor Green
        foreach ($device in $devices) {
            Write-Host "  - $($device.FriendlyName) - Status: $($device.Status)" -ForegroundColor White
        }
    } else {
        Write-Host "⚠️ No Futronic devices found in Device Manager" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Error checking Device Manager: $($_.Exception.Message)" -ForegroundColor Red
}

# Check USB devices
Write-Host "`nChecking USB devices..." -ForegroundColor Cyan
try {
    $usbDevices = Get-WmiObject -Class Win32_USBHub | Where-Object {$_.Name -like "*Futronic*"}
    if ($usbDevices) {
        Write-Host "✅ USB Futronic device(s) found:" -ForegroundColor Green
        foreach ($device in $usbDevices) {
            Write-Host "  - $($device.Name)" -ForegroundColor White
        }
    } else {
        Write-Host "⚠️ No Futronic USB devices found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Error checking USB devices: $($_.Exception.Message)" -ForegroundColor Red
}

# Check if device is connected (look for any USB device with Futronic vendor ID)
Write-Host "`nChecking for Futronic vendor ID (1491)..." -ForegroundColor Cyan
try {
    $allUSB = Get-WmiObject -Class Win32_USBControllerDevice
    $futronicFound = $false
    foreach ($usb in $allUSB) {
        if ($usb.Dependent -like "*VID_1491*") {
            Write-Host "✅ Futronic device detected with vendor ID 1491" -ForegroundColor Green
            $futronicFound = $true
            break
        }
    }
    if (-not $futronicFound) {
        Write-Host "⚠️ No devices with Futronic vendor ID found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Error checking vendor IDs: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n📋 Instructions:" -ForegroundColor Green
Write-Host "1. Make sure your Futronic FS88H device is connected via USB" -ForegroundColor White
Write-Host "2. If not detected, try a different USB port" -ForegroundColor White
Write-Host "3. Download and install Futronic SDK from: https://www.futronic-tech.com/downloads.html" -ForegroundColor White
Write-Host "4. After SDK installation, Windows should automatically install drivers" -ForegroundColor White

Write-Host "`nPress any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
