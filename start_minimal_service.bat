@echo off
echo ========================================
echo   MINIMAL STABLE Biometric Service
echo ========================================
echo.
echo 🎯 FOCUS: Don't crash after template encoding
echo.
echo The crash happens right after:
echo "✅ Template encoded successfully, length: 344"
echo.
echo This version adds:
echo - Longer stability delay (2 seconds)
echo - Immediate buffer cleanup
echo - Step-by-step logging
echo - Minimal response creation
echo.

cd local-biometric-service

echo 🚀 Starting MINIMAL STABLE service...
echo 📱 Service URL: http://localhost:8001
echo.
echo NO SERVER SIMULATION FALLBACK
echo - Real device capture only
echo - Clear error messages if device fails
echo - Focus on fixing the actual crash
echo.

python minimal_stable_service.py

echo.
echo Minimal stable service stopped.
pause
