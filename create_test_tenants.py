#!/usr/bin/env python3
"""
Script to create test tenants if database is empty.
"""

import subprocess
import sys

def run_docker_command(command):
    """Run a docker-compose command and return output."""
    try:
        result = subprocess.run(
            f"docker-compose {command}",
            shell=True,
            capture_output=True,
            text=True,
            timeout=60
        )
        return result.stdout, result.stderr, result.returncode
    except Exception as e:
        return "", str(e), 1

def create_test_tenants():
    """Create test tenants in the database."""
    
    print("🏗️  Creating Test Tenants")
    print("=" * 30)
    
    # Django shell script to create test tenants
    django_script = '''
from tenants.models import Tenant, Domain
from shared.models import Country, Region

# Get or create Ethiopia and Addis Ababa region
try:
    country, _ = Country.objects.get_or_create(
        name="Ethiopia",
        defaults={"code": "ET", "is_active": True}
    )
    
    region, _ = Region.objects.get_or_create(
        name="Addis Ababa",
        defaults={"country": country, "is_active": True}
    )
    
    print(f"✅ Country: {country.name}")
    print(f"✅ Region: {region.name}")
    
except Exception as e:
    print(f"❌ Error creating country/region: {e}")
    # Continue anyway

# Create test tenants
tenants_to_create = [
    {
        "name": "Addis Ababa City Administration",
        "type": "city",
        "schema_name": "city_addis_ababa",
        "domain": "addisababa.goid.local"
    },
    {
        "name": "Bole Sub City",
        "type": "subcity", 
        "schema_name": "subcity_bole",
        "domain": "bole.goid.local",
        "parent_name": "Addis Ababa City Administration"
    },
    {
        "name": "Bole 01 Kebele",
        "type": "kebele",
        "schema_name": "kebele_bole01", 
        "domain": "bole01.goid.local",
        "parent_name": "Bole Sub City"
    }
]

created_tenants = []

for tenant_data in tenants_to_create:
    try:
        # Check if tenant already exists
        existing = Tenant.objects.filter(name=tenant_data["name"]).first()
        if existing:
            print(f"✅ Tenant already exists: {existing.name}")
            created_tenants.append(existing)
            continue
        
        # Get parent if specified
        parent = None
        if tenant_data.get("parent_name"):
            parent = Tenant.objects.filter(name=tenant_data["parent_name"]).first()
            if not parent:
                print(f"⚠️  Parent not found: {tenant_data['parent_name']}")
        
        # Create tenant
        tenant = Tenant.objects.create(
            name=tenant_data["name"],
            type=tenant_data["type"],
            schema_name=tenant_data["schema_name"],
            parent=parent,
            is_active=True
        )
        
        # Create domain
        domain = Domain.objects.create(
            domain=tenant_data["domain"],
            tenant=tenant,
            is_primary=True
        )
        
        print(f"✅ Created tenant: {tenant.name} ({tenant.type})")
        print(f"   Schema: {tenant.schema_name}")
        print(f"   Domain: {domain.domain}")
        
        created_tenants.append(tenant)
        
    except Exception as e:
        print(f"❌ Error creating tenant {tenant_data['name']}: {e}")

print(f"\\n📊 Summary:")
print(f"Total tenants in database: {Tenant.objects.count()}")
print(f"Total domains in database: {Domain.objects.count()}")

# List all tenants
print(f"\\n📋 All tenants:")
for tenant in Tenant.objects.all():
    domain = tenant.domains.filter(is_primary=True).first()
    domain_name = domain.domain if domain else "No domain"
    parent_name = tenant.parent.name if tenant.parent else "No parent"
    print(f"- {tenant.name} ({tenant.type})")
    print(f"  Schema: {tenant.schema_name}")
    print(f"  Domain: {domain_name}")
    print(f"  Parent: {parent_name}")
    print()
'''
    
    print("Creating test tenants in database...")
    stdout, stderr, code = run_docker_command(f'exec -T backend python manage.py shell -c "{django_script}"')
    
    print("Creation result:")
    print(stdout)
    
    if stderr:
        print("Errors:")
        print(stderr)
    
    if code != 0:
        print(f"❌ Command failed with code: {code}")
        return False
    
    print("✅ Test tenant creation completed!")
    return True

def run_migrations():
    """Run database migrations."""
    print("\n🔄 Running database migrations...")
    
    # Run shared migrations
    print("Running shared migrations...")
    stdout, stderr, code = run_docker_command("exec backend python manage.py migrate_schemas --shared")
    print(stdout)
    if stderr:
        print("Errors:")
        print(stderr)
    
    # Run tenant migrations
    print("Running tenant migrations...")
    stdout, stderr, code = run_docker_command("exec backend python manage.py migrate_schemas")
    print(stdout)
    if stderr:
        print("Errors:")
        print(stderr)
    
    return code == 0

if __name__ == "__main__":
    print("🚀 Setting up test tenants for GoID system")
    print("=" * 45)
    
    # First run migrations
    if not run_migrations():
        print("❌ Migration failed, but continuing...")
    
    # Create test tenants
    if create_test_tenants():
        print("\n✅ Test tenants created successfully!")
        print("\n🧪 You can now test with:")
        print("- City: Addis Ababa City Administration")
        print("- Subcity: Bole Sub City") 
        print("- Kebele: Bole 01 Kebele")
        print("\n🌐 Domains:")
        print("- addisababa.goid.local")
        print("- bole.goid.local")
        print("- bole01.goid.local")
    else:
        print("\n❌ Failed to create test tenants")
        print("💡 Try running migrations first:")
        print("docker-compose exec backend python manage.py migrate_schemas --shared")
        print("docker-compose exec backend python manage.py migrate_schemas")
    
    print("\n💡 Next steps:")
    print("1. Run: python diagnose_tenant_list_issue.py")
    print("2. Test frontend: http://10.139.8.141:3000/tenants")
    print("3. Login with: <EMAIL> / admin123")
