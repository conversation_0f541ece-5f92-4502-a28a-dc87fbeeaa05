#!/bin/bash

# Test Edit Kebele User Functionality Script
# This script tests the complete edit kebele user feature

echo "🧪 Testing Edit Kebele User Functionality"
echo "========================================="

echo "📍 Features Implemented:"
echo "1. ✅ Backend update_user action in TenantViewSet"
echo "2. ✅ URL pattern for /api/tenants/{id}/update_user/"
echo "3. ✅ Middleware support for update_user endpoint"
echo "4. ✅ Frontend edit user dialog and functionality"
echo "5. ✅ Permission checks for subcity_admin and superadmin"
echo ""

# Step 1: Restart backend to apply all changes
echo "📍 Step 1: Restarting backend to apply all changes..."
docker-compose restart backend
sleep 30

# Step 2: Restart frontend to apply UI changes
echo ""
echo "📍 Step 2: Restarting frontend to apply UI changes..."
docker-compose restart frontend
sleep 20

# Step 3: Test the backend API endpoints
echo ""
echo "📍 Step 3: Testing backend API endpoints..."

echo "Getting authentication token..."
AUTH_RESPONSE=$(curl -s -X POST http://************:8000/api/auth/token/ \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}')

if echo "$AUTH_RESPONSE" | grep -q "access"; then
    echo "✅ Authentication successful"
    
    # Extract token
    TOKEN=$(echo "$AUTH_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin)['access'])" 2>/dev/null)
    
    if [ ! -z "$TOKEN" ]; then
        echo ""
        echo "Testing edit kebele user endpoints..."
        
        # Get a kebele to test with
        echo "1. Getting kebele tenants..."
        KEBELE_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
          "http://************:8000/api/tenants/?type=kebele")
        
        KEBELE_ID=$(echo "$KEBELE_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, list) and len(data) > 0:
        print(data[0]['id'])
    elif isinstance(data, dict) and 'results' in data and len(data['results']) > 0:
        print(data['results'][0]['id'])
    else:
        print('none')
except:
    print('none')
" 2>/dev/null)
        
        if [ "$KEBELE_ID" != "none" ]; then
            echo "   Using kebele ID: $KEBELE_ID"
            
            # Get users in this kebele
            echo "2. Getting users in kebele $KEBELE_ID..."
            USERS_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
              http://************:8000/api/tenants/$KEBELE_ID/users/)
            
            USER_ID=$(echo "$USERS_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, list) and len(data) > 0:
        print(data[0]['id'])
    else:
        print('none')
except:
    print('none')
" 2>/dev/null)
            
            if [ "$USER_ID" != "none" ]; then
                echo "   Found user ID to test with: $USER_ID"
                
                # Test update_user endpoint
                echo "3. Testing update_user endpoint..."
                UPDATE_RESPONSE=$(curl -s -X PATCH \
                  -H "Authorization: Bearer $TOKEN" \
                  -H "Content-Type: application/json" \
                  -d "{\"user_id\":$USER_ID,\"first_name\":\"Updated\",\"last_name\":\"TestUser\"}" \
                  http://************:8000/api/tenants/$KEBELE_ID/update_user/ \
                  -w "HTTP_STATUS:%{http_code}")
                
                HTTP_STATUS=$(echo "$UPDATE_RESPONSE" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
                RESPONSE_BODY=$(echo "$UPDATE_RESPONSE" | sed 's/HTTP_STATUS:[0-9]*$//')
                
                echo "   Update user status: $HTTP_STATUS"
                
                if [ "$HTTP_STATUS" = "200" ]; then
                    echo "   ✅ Update user endpoint working!"
                    echo "   Updated user data:"
                    echo "$RESPONSE_BODY" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE_BODY"
                elif [ "$HTTP_STATUS" = "403" ]; then
                    echo "   ❌ Permission denied - check user role and permissions"
                elif [ "$HTTP_STATUS" = "404" ]; then
                    echo "   ❌ Endpoint not found - check URL routing"
                elif [ "$HTTP_STATUS" = "400" ]; then
                    echo "   ⚠️  Bad request - check request data format"
                    echo "   Response: $RESPONSE_BODY"
                else
                    echo "   ❌ Unexpected status: $HTTP_STATUS"
                    echo "   Response: $RESPONSE_BODY"
                fi
                
            else
                echo "   ⚠️  No users found in kebele to test with"
                echo "   Create a user first to test edit functionality"
            fi
            
        else
            echo "   ⚠️  No kebele tenants found to test with"
        fi
        
    else
        echo "❌ Could not extract token"
    fi
else
    echo "❌ Authentication failed"
    echo "Response: $AUTH_RESPONSE"
fi

# Step 4: Test URL resolution
echo ""
echo "📍 Step 4: Testing URL resolution..."

docker-compose exec -T backend python manage.py shell << 'EOF'
from django.urls import resolve

print("=== URL RESOLUTION TEST ===")

test_urls = [
    "/api/tenants/1/users/",
    "/api/tenants/1/create_user/",
    "/api/tenants/1/update_user/",
    "/api/tenants/1/create_admin/",
]

for url in test_urls:
    try:
        match = resolve(url)
        print(f"✅ {url} -> {match.func.__name__}")
    except Exception as e:
        print(f"❌ {url} -> {e}")
EOF

# Step 5: Check backend logs for any errors
echo ""
echo "📍 Step 5: Checking backend logs for errors..."
docker-compose logs --tail=20 backend | grep -E "ERROR|Exception|Traceback|update_user" || echo "No relevant logs found"

echo ""
echo "✅ Edit kebele user functionality test completed!"
echo ""
echo "🧪 Manual testing steps:"
echo "1. Open: http://************:3000/users/kebele-management"
echo "2. Select a kebele from the left panel"
echo "3. In the users list, click the Edit icon (pencil) for any user"
echo "4. Edit User dialog should open with current user data"
echo "5. Modify fields like first name, last name, email, role, phone"
echo "6. Click 'Update User' button"
echo "7. User should be updated and list refreshed"
echo ""
echo "🔍 Expected results:"
echo "- ✅ Edit button opens dialog with current user data"
echo "- ✅ Username field is disabled (cannot be changed)"
echo "- ✅ Other fields are editable"
echo "- ✅ Update button saves changes successfully"
echo "- ✅ Users list refreshes with updated data"
echo "- ✅ No 404 or 403 errors"
echo ""
echo "💡 Features of edit functionality:"
echo "- 🔒 Username cannot be changed (security)"
echo "- ✏️  Email, names, role, phone can be updated"
echo "- 🔐 Password updates not allowed (separate feature)"
echo "- ✅ Validation for duplicate emails/usernames"
echo "- 🎯 Permission checks for subcity_admin and superadmin"
echo "- 🔄 Real-time list refresh after updates"
echo ""
echo "🚨 Troubleshooting:"
echo "- If 404 error: Check URL routing and middleware"
echo "- If 403 error: Check user permissions and role"
echo "- If validation errors: Check for duplicate emails"
echo "- If UI not updating: Clear browser cache"
echo ""
echo "🔧 Debug commands:"
echo "- Test API: curl -X PATCH -H \"Authorization: Bearer TOKEN\" -H \"Content-Type: application/json\" -d '{\"user_id\":1,\"first_name\":\"Test\"}' http://************:8000/api/tenants/KEBELE_ID/update_user/"
echo "- Check logs: docker-compose logs backend | grep update_user"
echo "- Check permissions: Check user role in JWT token"
