#!/bin/bash

# Test Tenant ViewSet Fix Script
# This script tests the fix for empty tenant API response

echo "🔧 Testing Tenant ViewSet Fix"
echo "============================="

# Step 1: Restart backend to apply the ViewSet changes
echo "📍 Step 1: Restarting backend to apply ViewSet changes..."
docker-compose restart backend
sleep 30

# Step 2: Test the debug output and API response
echo ""
echo "📍 Step 2: Testing API with debug output..."

echo "Getting authentication token..."
AUTH_RESPONSE=$(curl -s -X POST http://************:8000/api/auth/token/ \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}')

if echo "$AUTH_RESPONSE" | grep -q "access"; then
    echo "✅ Authentication successful"
    
    # Extract token
    TOKEN=$(echo "$AUTH_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin)['access'])" 2>/dev/null)
    
    if [ ! -z "$TOKEN" ]; then
        echo "Testing tenant endpoint..."
        
        # Make the API call
        TENANT_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
          -H "Content-Type: application/json" \
          http://************:8000/api/tenants/)
        
        echo "API Response:"
        echo "$TENANT_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$TENANT_RESPONSE"
        
        # Check if we got data
        if echo "$TENANT_RESPONSE" | grep -q -E '\[|\{'; then
            TENANT_COUNT=$(echo "$TENANT_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, list):
        print(len(data))
    elif isinstance(data, dict) and 'results' in data:
        print(len(data.get('results', [])))
    else:
        print('0')
except:
    print('0')
" 2>/dev/null)
            
            echo "Found $TENANT_COUNT tenants in API response"
            
            if [ "$TENANT_COUNT" -gt "0" ]; then
                echo "✅ SUCCESS: API is now returning tenant data!"
            else
                echo "❌ STILL EMPTY: API returning empty data"
            fi
        else
            echo "❌ INVALID RESPONSE: API not returning valid JSON"
        fi
        
        # Check backend logs for debug output
        echo ""
        echo "Backend debug logs:"
        docker-compose logs --tail=20 backend | grep -E "TenantViewSet Debug|Superuser access|tenant" || echo "No debug logs found"
        
    else
        echo "❌ Could not extract token"
    fi
else
    echo "❌ Authentication failed"
    echo "Response: $AUTH_RESPONSE"
fi

# Step 3: Test database directly to compare
echo ""
echo "📍 Step 3: Checking database directly for comparison..."

docker-compose exec -T backend python manage.py shell << 'EOF'
from tenants.models import Tenant
from django.contrib.auth import get_user_model

print("=== DATABASE VERIFICATION ===")

# Check tenants in database
all_tenants = Tenant.objects.all()
print(f"Total tenants in database: {all_tenants.count()}")

if all_tenants.exists():
    print("\nTenants in database:")
    for tenant in all_tenants:
        print(f"- ID: {tenant.id}, Name: {tenant.name}, Type: {tenant.type}, Active: {tenant.is_active}")

# Check admin user
User = get_user_model()
try:
    admin_user = User.objects.get(email='<EMAIL>')
    print(f"\nAdmin user check:")
    print(f"- Email: {admin_user.email}")
    print(f"- Is superuser: {admin_user.is_superuser}")
    print(f"- Is staff: {admin_user.is_staff}")
    print(f"- Is active: {admin_user.is_active}")
    print(f"- Role: {getattr(admin_user, 'role', 'No role attribute')}")
    print(f"- Tenant: {getattr(admin_user, 'tenant', 'No tenant assigned')}")
    
    if not admin_user.is_superuser:
        print("❌ ISSUE: Admin user is not marked as superuser!")
        print("Fixing admin user...")
        admin_user.is_superuser = True
        admin_user.is_staff = True
        admin_user.is_active = True
        admin_user.save()
        print("✅ Admin user fixed")
        
except User.DoesNotExist:
    print("❌ Admin user not found!")

print("\n=== VIEWSET TEST ===")

# Test the viewset directly
try:
    from tenants.views.tenant_views import TenantViewSet
    from django.test import RequestFactory
    
    factory = RequestFactory()
    request = factory.get('/api/tenants/')
    request.user = admin_user
    
    viewset = TenantViewSet()
    viewset.request = request
    
    # Test queryset
    queryset = viewset.get_queryset()
    print(f"ViewSet queryset count: {queryset.count()}")
    
    if queryset.count() > 0:
        print("✅ ViewSet now returns tenants!")
        for tenant in queryset[:3]:
            print(f"  - {tenant.name} ({tenant.type})")
    else:
        print("❌ ViewSet still returns no tenants")
        
except Exception as e:
    print(f"❌ ViewSet test failed: {e}")
    import traceback
    traceback.print_exc()
EOF

# Step 4: Test frontend after fix
echo ""
echo "📍 Step 4: Testing frontend after fix..."

echo "Restarting frontend to clear any cache..."
docker-compose restart frontend
sleep 20

echo ""
echo "✅ Tenant ViewSet fix test completed!"
echo ""
echo "🧪 Manual testing steps:"
echo "1. Clear browser cache completely (CRITICAL)"
echo "2. Open: http://************:3000/"
echo "3. Login with: <EMAIL> / admin123"
echo "4. Navigate to: Tenants section"
echo "5. Check: Should now see list of tenants"
echo ""
echo "🔍 Expected results:"
echo "- ✅ API returns tenant data (not empty)"
echo "- ✅ Backend logs show 'Superuser access granted'"
echo "- ✅ Frontend displays tenant list"
echo "- ✅ No more empty tenant list"
echo ""
echo "💡 If still not working:"
echo "1. Check if admin user is marked as superuser"
echo "2. Look for debug logs in backend"
echo "3. Clear browser cache completely"
echo "4. Check if user.role attribute exists"
echo ""
echo "🔧 Debug commands:"
echo "- Check logs: docker-compose logs backend | grep -E 'TenantViewSet|Superuser'"
echo "- Test API: curl -H \"Authorization: Bearer TOKEN\" http://************:8000/api/tenants/"
echo "- Check user: Django admin → Users → <EMAIL>"
