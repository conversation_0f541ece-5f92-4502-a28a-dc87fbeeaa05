#!/usr/bin/env python3
"""
Simple Test Biometric Service - Works without real hardware
"""

import logging
import time
import base64
import json
from datetime import datetime
from flask import Flask, jsonify, request
from flask_cors import CORS

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Enable CORS for all routes
CORS(app, origins=['*'])

class SimulatedDevice:
    def __init__(self):
        self.connected = True
        self.initialized = True
        self.model = "Futronic FS88H (Simulated)"
    
    def get_status(self):
        return {
            'connected': self.connected,
            'initialized': self.initialized,
            'model': self.model,
            'real_device': False,
            'simulation_mode': True
        }
    
    def capture_fingerprint(self, thumb_type):
        """Simulate fingerprint capture"""
        import random
        
        # Simulate capture delay
        time.sleep(1)
        
        # Generate mock template data
        template_data = {
            'version': '1.0',
            'thumb_type': thumb_type,
            'capture_time': datetime.now().isoformat(),
            'frame_size': random.randint(50000, 150000),
            'quality_score': random.randint(80, 98),
            'minutiae_count': random.randint(35, 60),
            'device_info': {
                'model': self.model,
                'real_device': False,
                'simulation_mode': True
            }
        }
        
        # Encode template
        template_encoded = base64.b64encode(json.dumps(template_data).encode()).decode()
        
        return {
            'success': True,
            'data': {
                'thumb_type': thumb_type,
                'template_data': template_encoded,
                'quality_score': template_data['quality_score'],
                'minutiae_count': template_data['minutiae_count'],
                'frame_size': template_data['frame_size'],
                'capture_time': template_data['capture_time'],
                'device_info': template_data['device_info']
            }
        }

# Global device instance
device = SimulatedDevice()

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'Simple Test Biometric Service',
        'timestamp': datetime.now().isoformat(),
        'device_connected': device.connected,
        'simulation_mode': True
    })

@app.route('/api/device/status', methods=['GET'])
def device_status():
    """Get device status"""
    return jsonify({
        'success': True,
        'data': device.get_status()
    })

@app.route('/api/device/reconnect', methods=['POST'])
def device_reconnect():
    """Reconnect device"""
    device.connected = True
    device.initialized = True
    return jsonify({
        'success': True,
        'message': 'Device reconnected successfully',
        'data': device.get_status()
    })

@app.route('/api/capture/fingerprint', methods=['GET', 'POST'])
def capture_fingerprint():
    """Capture fingerprint"""
    try:
        if request.method == 'GET':
            thumb_type = request.args.get('thumb_type', 'left')
        else:
            data = request.get_json() or {}
            thumb_type = data.get('thumb_type', 'left')

        if thumb_type not in ['left', 'right']:
            return jsonify({
                'success': False,
                'error': 'Invalid thumb_type. Must be "left" or "right"'
            }), 400

        logger.info(f"🔍 Capturing {thumb_type} thumb fingerprint...")

        if not device.connected:
            return jsonify({
                'success': False,
                'error': 'Device not connected'
            }), 503

        result = device.capture_fingerprint(thumb_type)
        logger.info(f"✅ {thumb_type} thumb capture successful")
        return jsonify(result)

    except Exception as e:
        logger.error(f"❌ Capture error: {e}")
        return jsonify({
            'success': False,
            'error': f'Service error: {str(e)}'
        }), 500

@app.route('/', methods=['GET'])
def index():
    """Web interface"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Simple Test Biometric Service</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
            button { padding: 15px 30px; margin: 10px; font-size: 16px; border: none; border-radius: 5px; cursor: pointer; }
            .btn-primary { background: #007bff; color: white; }
            .btn-success { background: #28a745; color: white; }
            .result { margin: 20px 0; padding: 15px; border-radius: 5px; }
            .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
            .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
            .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🔒 Simple Test Biometric Service</h1>
            <p>This is a simulated biometric service for testing GoID without real hardware.</p>
            
            <div class="info result">
                <strong>🎭 Simulation Mode:</strong> No real device required - perfect for development and testing!
            </div>
            
            <h3>Device Status</h3>
            <button class="btn-primary" onclick="checkStatus()">📱 Check Device Status</button>
            <button class="btn-primary" onclick="checkHealth()">💚 Health Check</button>
            
            <h3>Fingerprint Capture</h3>
            <button class="btn-success" onclick="captureFingerprint('left')">👈 Capture Left Thumb</button>
            <button class="btn-success" onclick="captureFingerprint('right')">👉 Capture Right Thumb</button>
            
            <div id="result"></div>
        </div>
        
        <script>
            async function checkHealth() {
                const resultDiv = document.getElementById('result');
                try {
                    const response = await fetch('/api/health');
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>💚 Health Check</h3>
                            <p><strong>Status:</strong> ${data.status}</p>
                            <p><strong>Service:</strong> ${data.service}</p>
                            <p><strong>Device Connected:</strong> ${data.device_connected}</p>
                            <p><strong>Timestamp:</strong> ${data.timestamp}</p>
                        </div>
                    `;
                } catch (error) {
                    resultDiv.innerHTML = `<div class="result error">Health check failed: ${error}</div>`;
                }
            }
            
            async function checkStatus() {
                const resultDiv = document.getElementById('result');
                try {
                    const response = await fetch('/api/device/status');
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>📱 Device Status</h3>
                            <p><strong>Connected:</strong> ${data.data.connected}</p>
                            <p><strong>Model:</strong> ${data.data.model}</p>
                            <p><strong>Simulation Mode:</strong> ${data.data.simulation_mode}</p>
                        </div>
                    `;
                } catch (error) {
                    resultDiv.innerHTML = `<div class="result error">Status check failed: ${error}</div>`;
                }
            }
            
            async function captureFingerprint(thumbType) {
                const resultDiv = document.getElementById('result');
                resultDiv.innerHTML = '<div class="result info">📸 Capturing fingerprint... Please wait...</div>';
                
                try {
                    const response = await fetch(`/api/capture/fingerprint?thumb_type=${thumbType}`);
                    const data = await response.json();
                    
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="result success">
                                <h3>✅ Fingerprint Captured Successfully!</h3>
                                <p><strong>Thumb:</strong> ${data.data.thumb_type}</p>
                                <p><strong>Quality:</strong> ${data.data.quality_score}%</p>
                                <p><strong>Minutiae:</strong> ${data.data.minutiae_count}</p>
                                <p><strong>Frame Size:</strong> ${data.data.frame_size} bytes</p>
                                <p><strong>Device Model:</strong> ${data.data.device_info.model}</p>
                                <p><strong>Simulation Mode:</strong> ${data.data.device_info.simulation_mode}</p>
                                <p><strong>Template Length:</strong> ${data.data.template_data.length} characters</p>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="result error">
                                <h3>❌ Capture Failed</h3>
                                <p>${data.error}</p>
                            </div>
                        `;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<div class="result error">Capture failed: ${error}</div>`;
                }
            }
        </script>
    </body>
    </html>
    """

if __name__ == '__main__':
    logger.info("🚀 Starting Simple Test Biometric Service")
    logger.info("=" * 50)
    logger.info("📱 Service URL: http://localhost:8001")
    logger.info("💚 Health Check: http://localhost:8001/api/health")
    logger.info("📱 Device Status: http://localhost:8001/api/device/status")
    logger.info("🔍 Capture API: http://localhost:8001/api/capture/fingerprint")
    logger.info("🌐 Web Interface: http://localhost:8001")
    logger.info("🎭 Running in simulation mode (no real hardware required)")
    logger.info("=" * 50)
    
    app.run(host='0.0.0.0', port=8001, debug=True)
