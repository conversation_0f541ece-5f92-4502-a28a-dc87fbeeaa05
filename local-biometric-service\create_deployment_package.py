#!/usr/bin/env python3
"""
Create GoID Biometric Service Deployment Package
"""

import os
import shutil
import zipfile
from pathlib import Path
import json

def create_deployment_package():
    """Create a complete deployment package"""
    
    print("📦 Creating GoID Biometric Service Deployment Package...")
    print("=" * 60)
    
    # Source directory (current directory)
    source_dir = Path(__file__).parent
    
    # Create deployment directory
    deploy_dir = source_dir / "GoID_Biometric_Service_Deploy"
    
    if deploy_dir.exists():
        print(f"🗑️ Removing existing deployment directory...")
        shutil.rmtree(deploy_dir)
    
    deploy_dir.mkdir()
    
    print(f"📁 Creating deployment package in: {deploy_dir}")
    
    # Files to include in deployment
    files_to_copy = [
        "working_jar_bridge.py",
        "simple_service_installer.py", 
        "debug_service.py",
        "install_service_alternative.bat",
        "manage_service.bat",
        "INSTALL_SERVICE_GUIDE.md"
    ]
    
    # Directories to copy
    dirs_to_copy = [
        "fingerPrint",  # Contains GonderFingerPrint.jar
        "nssm"         # Contains NSSM executable
    ]
    
    # Copy files
    print(f"\n📄 Copying files...")
    for file_name in files_to_copy:
        source_file = source_dir / file_name
        if source_file.exists():
            dest_file = deploy_dir / file_name
            shutil.copy2(source_file, dest_file)
            print(f"   ✅ {file_name}")
        else:
            print(f"   ⚠️ {file_name} (not found)")
    
    # Copy directories
    print(f"\n📁 Copying directories...")
    for dir_name in dirs_to_copy:
        source_dir_path = source_dir / dir_name
        if source_dir_path.exists():
            dest_dir_path = deploy_dir / dir_name
            shutil.copytree(source_dir_path, dest_dir_path)
            print(f"   ✅ {dir_name}/")
        else:
            print(f"   ⚠️ {dir_name}/ (not found)")
    
    # Create logs directory
    logs_dir = deploy_dir / "logs"
    logs_dir.mkdir()
    print(f"   ✅ logs/ (created)")
    
    # Create installation scripts
    create_installation_scripts(deploy_dir)
    
    # Create configuration file
    create_config_file(deploy_dir)
    
    # Create README
    create_readme(deploy_dir)
    
    # Create ZIP package
    create_zip_package(deploy_dir)
    
    print(f"\n🎉 Deployment package created successfully!")
    print(f"📁 Location: {deploy_dir}")
    print(f"📦 ZIP file: {deploy_dir}.zip")
    
    return deploy_dir

def create_installation_scripts(deploy_dir):
    """Create installation scripts for deployment"""
    
    print(f"\n📝 Creating installation scripts...")
    
    # Create simple installer batch file
    installer_bat = deploy_dir / "INSTALL.bat"
    installer_content = '''@echo off
echo ========================================
echo   GoID Biometric Service Installer
echo ========================================
echo.
echo This will install the GoID Biometric Service
echo for real fingerprint capture using Futronic devices.
echo.
pause

REM Change to script directory
cd /d "%~dp0"

REM Check admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Running as Administrator
) else (
    echo ❌ Administrator privileges required
    echo 💡 Right-click INSTALL.bat and "Run as Administrator"
    pause
    exit /b 1
)

echo.
echo 📦 Installing service...
python simple_service_installer.py install

echo.
echo ▶️ Starting service...
python simple_service_installer.py start

echo.
echo 📊 Checking service status...
python simple_service_installer.py status

echo.
echo 🧪 Testing service...
python simple_service_installer.py test

echo.
echo ========================================
echo   Installation Complete!
echo ========================================
echo.
echo The GoID Biometric Service is now running.
echo.
echo Service Management:
echo   - Use MANAGE.bat for service management
echo   - Test URL: http://localhost:8001/api/device/status
echo   - Logs are in the logs/ directory
echo.
pause
'''
    
    with open(installer_bat, 'w', encoding='utf-8') as f:
        f.write(installer_content)
    
    print(f"   ✅ INSTALL.bat")
    
    # Create uninstaller
    uninstaller_bat = deploy_dir / "UNINSTALL.bat"
    uninstaller_content = '''@echo off
echo ========================================
echo   GoID Biometric Service Uninstaller
echo ========================================
echo.
echo This will remove the GoID Biometric Service.
echo.
pause

cd /d "%~dp0"

net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Running as Administrator
) else (
    echo ❌ Administrator privileges required
    echo 💡 Right-click UNINSTALL.bat and "Run as Administrator"
    pause
    exit /b 1
)

echo.
echo 🗑️ Uninstalling service...
python simple_service_installer.py uninstall

echo.
echo ✅ Service uninstalled successfully!
pause
'''
    
    with open(uninstaller_bat, 'w', encoding='utf-8') as f:
        f.write(uninstaller_content)
    
    print(f"   ✅ UNINSTALL.bat")
    
    # Create management script
    manage_bat = deploy_dir / "MANAGE.bat"
    manage_content = '''@echo off
echo ========================================
echo   GoID Biometric Service Manager
echo ========================================
echo.

cd /d "%~dp0"

:menu
echo Service Management Options:
echo.
echo 1. Check Service Status
echo 2. Start Service
echo 3. Stop Service
echo 4. Restart Service
echo 5. Test Service
echo 6. View Logs
echo 7. Exit
echo.
set /p choice="Enter your choice (1-7): "

if "%choice%"=="1" goto status
if "%choice%"=="2" goto start
if "%choice%"=="3" goto stop
if "%choice%"=="4" goto restart
if "%choice%"=="5" goto test
if "%choice%"=="6" goto logs
if "%choice%"=="7" goto exit
goto menu

:status
echo.
python simple_service_installer.py status
pause
goto menu

:start
echo.
python simple_service_installer.py start
pause
goto menu

:stop
echo.
python simple_service_installer.py stop
pause
goto menu

:restart
echo.
python simple_service_installer.py stop
timeout /t 3 /nobreak >nul
python simple_service_installer.py start
pause
goto menu

:test
echo.
python simple_service_installer.py test
pause
goto menu

:logs
echo.
if exist "logs" (
    explorer logs
) else (
    echo ❌ Log directory not found
)
pause
goto menu

:exit
exit /b 0
'''
    
    with open(manage_bat, 'w', encoding='utf-8') as f:
        f.write(manage_content)
    
    print(f"   ✅ MANAGE.bat")

def create_config_file(deploy_dir):
    """Create configuration file"""
    
    config = {
        "service": {
            "name": "GoIDJarBridge",
            "display_name": "GoID JAR Bridge Service",
            "description": "GoID Real Fingerprint Capture Service using Futronic JAR Bridge",
            "port": 8001
        },
        "biometric": {
            "device_model": "Futronic FS88H",
            "jar_application": "fingerPrint/GonderFingerPrint.jar",
            "capture_timeout": 30,
            "quality_threshold": 70
        },
        "logging": {
            "level": "INFO",
            "directory": "logs",
            "max_size_mb": 10,
            "backup_count": 5
        }
    }
    
    config_file = deploy_dir / "config.json"
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"   ✅ config.json")

def create_readme(deploy_dir):
    """Create README file"""
    
    readme_content = '''# GoID Biometric Service Deployment Package

## 🎯 Overview

This package contains everything needed to deploy the GoID Biometric Service for real fingerprint capture using Futronic FS88H devices.

## 📦 Package Contents

- `working_jar_bridge.py` - Main biometric service
- `simple_service_installer.py` - Service installer
- `fingerPrint/GonderFingerPrint.jar` - Futronic JAR application
- `nssm/` - Windows service manager
- `INSTALL.bat` - Easy installation
- `UNINSTALL.bat` - Easy removal
- `MANAGE.bat` - Service management
- `config.json` - Configuration file

## 🚀 Installation

### Prerequisites
- Windows 10/11
- Python 3.8+ (64-bit recommended)
- Futronic FS88H fingerprint device
- Administrator privileges

### Quick Installation
1. **Extract** this package to a folder (e.g., `C:\\GoID\\BiometricService\\`)
2. **Right-click** `INSTALL.bat` and select "Run as Administrator"
3. **Follow** the installation prompts
4. **Test** the service at: http://localhost:8001/api/device/status

### Manual Installation
```cmd
# Open Command Prompt as Administrator
cd "C:\\path\\to\\extracted\\package"
python simple_service_installer.py install
python simple_service_installer.py start
```

## 🛠️ Service Management

### Using Management Interface
- Run `MANAGE.bat` for interactive service management

### Command Line
```cmd
python simple_service_installer.py start      # Start service
python simple_service_installer.py stop       # Stop service
python simple_service_installer.py restart    # Restart service
python simple_service_installer.py status     # Check status
python simple_service_installer.py test       # Test API
```

### Windows Services
- Open `services.msc`
- Look for "GoID JAR Bridge Service"
- Right-click for start/stop/properties

## 🧪 Testing

### API Endpoints
- **Device Status:** http://localhost:8001/api/device/status
- **Capture Fingerprint:** POST http://localhost:8001/api/capture/fingerprint

### Manual Testing
```cmd
# Test JAR bridge manually
python working_jar_bridge.py

# Test service health
python simple_service_installer.py test
```

## 📋 Troubleshooting

### Service Won't Start
1. Check logs in `logs/` directory
2. Test manual startup: `python working_jar_bridge.py`
3. Verify Java is installed
4. Check Futronic device connection

### Fingerprint Capture Issues
1. Ensure Futronic device is connected
2. Check device drivers are installed
3. Test JAR application manually
4. Verify device permissions

### Common Solutions
- **Restart service:** Use MANAGE.bat → Restart Service
- **Check logs:** Use MANAGE.bat → View Logs
- **Reinstall:** Run UNINSTALL.bat, then INSTALL.bat
- **Debug:** Run `python debug_service.py`

## 🔧 Configuration

Edit `config.json` to customize:
- Service port (default: 8001)
- Capture timeout (default: 30 seconds)
- Quality threshold (default: 70%)
- Logging settings

## 📞 Support

For issues:
1. Check service logs in `logs/` directory
2. Run debug script: `python debug_service.py`
3. Test manual operation: `python working_jar_bridge.py`
4. Check Windows Event Viewer for service errors

## 🎉 Success Indicators

When properly installed:
- ✅ Service shows "Running" in services.msc
- ✅ http://localhost:8001/api/device/status returns JSON
- ✅ JAR application opens for fingerprint capture
- ✅ Real fingerprints are captured and processed

## 📁 File Structure

```
GoID_Biometric_Service_Deploy/
├── INSTALL.bat                 # Easy installer
├── UNINSTALL.bat              # Easy uninstaller  
├── MANAGE.bat                 # Service manager
├── working_jar_bridge.py      # Main service
├── simple_service_installer.py # Service installer
├── config.json               # Configuration
├── fingerPrint/              # JAR application
│   └── GonderFingerPrint.jar
├── nssm/                     # Service manager
│   └── nssm.exe
└── logs/                     # Service logs
```
'''
    
    readme_file = deploy_dir / "README.md"
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"   ✅ README.md")

def create_zip_package(deploy_dir):
    """Create ZIP package for easy distribution"""
    
    print(f"\n📦 Creating ZIP package...")
    
    zip_file = Path(str(deploy_dir) + ".zip")
    
    with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(deploy_dir):
            for file in files:
                file_path = Path(root) / file
                arc_name = file_path.relative_to(deploy_dir.parent)
                zipf.write(file_path, arc_name)
    
    print(f"   ✅ {zip_file.name}")
    print(f"   📊 Size: {zip_file.stat().st_size / 1024 / 1024:.1f} MB")

def main():
    """Main function"""
    
    print("GoID Biometric Service Deployment Package Creator")
    print("=" * 55)
    
    deploy_dir = create_deployment_package()
    
    print(f"\n🎉 Deployment package ready!")
    print(f"📁 Directory: {deploy_dir}")
    print(f"📦 ZIP file: {deploy_dir}.zip")
    
    print(f"\n💡 Next steps:")
    print(f"1. Test the package on this machine")
    print(f"2. Copy to other kebele workstations")
    print(f"3. Run INSTALL.bat as Administrator on each machine")
    print(f"4. Test fingerprint capture on each machine")
    
    input("\nPress Enter to exit...")

if __name__ == '__main__':
    main()
