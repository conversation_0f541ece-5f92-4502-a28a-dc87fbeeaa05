@echo off
echo ========================================
echo   PERSISTENT Biometric Service
echo ========================================
echo.
echo 🔄 AUTO-RESTART SOLUTION
echo.
echo This script automatically restarts the biometric
echo service whenever it crashes, ensuring it's always
echo available for fingerprint capture.
echo.
echo HOW IT WORKS:
echo - Service starts and captures left thumb successfully
echo - Service crashes after left thumb (known issue)
echo - Script automatically restarts service
echo - Fresh service captures right thumb successfully
echo - Continuous availability for clerks
echo.
echo BENEFITS:
echo - No manual intervention needed
echo - Always available for next capture
echo - Clerks don't need technical knowledge
echo - Production-ready solution
echo.

cd local-biometric-service

:restart_loop
echo.
echo 🚀 Starting Kebele Biometric Service...
echo 📱 Service URL: http://localhost:8001
echo 🔄 Will auto-restart if service crashes
echo.

python kebele_biometric_service.py

echo.
echo ⚠️  Service stopped - Auto-restarting in 2 seconds...
timeout /t 2 /nobreak >nul
echo 🔄 Restarting service...
goto restart_loop
