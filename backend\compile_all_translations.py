#!/usr/bin/env python3
"""
Script to compile all .po files to .mo files for all supported languages
"""

import os
import struct
import re
from pathlib import Path

def compile_po_to_mo(po_file_path, mo_file_path):
    """
    Convert a PO file to MO file format.
    """
    translations = {}
    
    if not os.path.exists(po_file_path):
        print(f"❌ PO file not found: {po_file_path}")
        return 0
    
    with open(po_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Parse PO file using regex
    po_entries = re.findall(r'msgid\s+"([^"]*)"(?:\n[^m]*)*msgstr\s+"([^"]*)"', content, re.MULTILINE)
    
    for msgid, msgstr in po_entries:
        if msgid and msgstr:  # Skip empty translations
            # Unescape quotes and handle multiline
            msgid = msgid.replace('\\"', '"').replace('\\n', '\n')
            msgstr = msgstr.replace('\\"', '"').replace('\\n', '\n')
            translations[msgid] = msgstr
    
    if not translations:
        print(f"⚠️  No translations found in {po_file_path}")
        return 0
    
    # Create MO file
    keys = list(translations.keys())
    values = [translations[k] for k in keys]
    
    # Calculate offsets
    koffsets = []
    voffsets = []
    kencoded = [k.encode('utf-8') for k in keys]
    vencoded = [v.encode('utf-8') for v in values]
    
    # Build the output
    keystart = 7 * 4 + 16 * len(keys)
    valuestart = keystart + sum(len(k) for k in kencoded)
    
    koffsets = []
    voffsets = []
    
    offset = keystart
    for k in kencoded:
        koffsets.append(offset)
        offset += len(k)
    
    offset = valuestart
    for v in vencoded:
        voffsets.append(offset)
        offset += len(v)
    
    # Write MO file
    with open(mo_file_path, 'wb') as f:
        # Header
        f.write(struct.pack('<I', 0x950412de))  # Magic number
        f.write(struct.pack('<I', 0))           # Version
        f.write(struct.pack('<I', len(keys)))   # Number of entries
        f.write(struct.pack('<I', 7*4))         # Offset of key table
        f.write(struct.pack('<I', 7*4 + 8*len(keys)))  # Offset of value table
        f.write(struct.pack('<I', 0))           # Hash table size
        f.write(struct.pack('<I', 0))           # Offset of hash table
        
        # Key descriptors
        for i in range(len(keys)):
            f.write(struct.pack('<I', len(kencoded[i])))
            f.write(struct.pack('<I', koffsets[i]))
        
        # Value descriptors
        for i in range(len(values)):
            f.write(struct.pack('<I', len(vencoded[i])))
            f.write(struct.pack('<I', voffsets[i]))
        
        # Keys
        for k in kencoded:
            f.write(k)
        
        # Values
        for v in vencoded:
            f.write(v)
    
    return len(translations)

def main():
    """Compile all .po files to .mo files"""
    locale_path = Path('/app/locale')
    
    if not locale_path.exists():
        print(f"❌ Locale directory not found: {locale_path}")
        return
    
    total_compiled = 0
    languages_processed = 0
    
    # Find all language directories
    for lang_dir in locale_path.iterdir():
        if lang_dir.is_dir():
            lc_messages_dir = lang_dir / 'LC_MESSAGES'
            po_file = lc_messages_dir / 'django.po'
            mo_file = lc_messages_dir / 'django.mo'
            
            if po_file.exists():
                print(f"🔄 Compiling {lang_dir.name} translations...")
                count = compile_po_to_mo(str(po_file), str(mo_file))
                
                if count > 0:
                    print(f"✅ {lang_dir.name}: {count} translations compiled")
                    print(f"   📁 Output: {mo_file}")
                    print(f"   📊 Size: {mo_file.stat().st_size} bytes")
                    total_compiled += count
                    languages_processed += 1
                else:
                    print(f"⚠️  {lang_dir.name}: No valid translations found")
                print()
            else:
                print(f"⚠️  {lang_dir.name}: No django.po file found")
    
    print("=" * 50)
    print(f"🎉 Compilation complete!")
    print(f"📊 Languages processed: {languages_processed}")
    print(f"📊 Total translations compiled: {total_compiled}")
    
    if languages_processed > 0:
        print("\n🔄 Restart the Django application to load new translations.")

if __name__ == '__main__':
    print("🌍 GoID Translation Compiler")
    print("=" * 50)
    main()
