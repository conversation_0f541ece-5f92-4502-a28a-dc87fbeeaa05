import { useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import { publicAxios } from '../utils/axios';

export const useTenantTheme = () => {
  const { user } = useAuth();
  const { updateTenantColors, tenantColors } = useTheme();

  useEffect(() => {
    // Load global system settings instead of tenant-specific colors
    const loadSystemColors = async () => {
      try {
        const response = await publicAxios.get('/api/tenants/system-settings/');
        const systemSettings = response.data;

        const newColors = {
          primary: systemSettings.primary_color || '#ff8f00',
          secondary: systemSettings.secondary_color || '#ef6c00'
        };

        console.log('🎨 Loading system colors:', newColors);

        // Only update if colors have actually changed
        if (newColors.primary !== tenantColors.primary || newColors.secondary !== tenantColors.secondary) {
          updateTenantColors(newColors);
        }
      } catch (error) {
        console.error('Error loading system colors:', error);
        // Fallback to default colors only if current colors are not set
        if (!tenantColors.primary) {
          const defaultColors = {
            primary: '#ff8f00',
            secondary: '#ef6c00'
          };
          updateTenantColors(defaultColors);
        }
      }
    };

    // Load colors only once on mount - NO POLLING
    loadSystemColors();
  }, []); // Empty dependency array to run only once on mount

  return {
    tenantColors,
    updateTenantColors
  };
};
