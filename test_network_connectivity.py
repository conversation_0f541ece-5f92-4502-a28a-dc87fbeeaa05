#!/usr/bin/env python3
"""
Test network connectivity and CORS configuration for multi-computer access.
"""

import requests
import json

def test_network_connectivity():
    """Test network connectivity from different origins."""
    
    base_url = "http://************:8000"
    frontend_origins = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://************:3000"
    ]
    
    print("🌐 Testing Network Connectivity")
    print("=" * 40)
    
    # Step 1: Test basic backend connectivity
    print("📍 Step 1: Testing basic backend connectivity...")
    
    try:
        response = requests.get(f"{base_url}/admin/", timeout=10)
        print(f"Backend admin: {response.status_code}")
        
        if response.status_code in [200, 302]:
            print("✅ Backend is accessible via network IP")
        else:
            print("❌ Backend not accessible")
            return False
    except Exception as e:
        print(f"❌ Backend connection failed: {e}")
        return False
    
    # Step 2: Test CORS preflight requests
    print(f"\n📍 Step 2: Testing CORS preflight requests...")
    
    for origin in frontend_origins:
        try:
            # CORS preflight request
            response = requests.options(
                f"{base_url}/api/tenants/",
                headers={
                    "Origin": origin,
                    "Access-Control-Request-Method": "GET",
                    "Access-Control-Request-Headers": "authorization,content-type"
                },
                timeout=10
            )
            
            print(f"CORS preflight for {origin}: {response.status_code}")
            
            # Check CORS headers
            cors_headers = {
                "Access-Control-Allow-Origin": response.headers.get("Access-Control-Allow-Origin"),
                "Access-Control-Allow-Methods": response.headers.get("Access-Control-Allow-Methods"),
                "Access-Control-Allow-Headers": response.headers.get("Access-Control-Allow-Headers"),
            }
            
            if response.status_code == 200:
                print(f"  ✅ CORS allowed")
                print(f"  Headers: {cors_headers}")
            else:
                print(f"  ❌ CORS blocked")
                
        except Exception as e:
            print(f"  ❌ CORS test failed: {e}")
    
    # Step 3: Test authentication with CORS
    print(f"\n📍 Step 3: Testing authentication with CORS...")
    
    for origin in frontend_origins:
        try:
            response = requests.post(
                f"{base_url}/api/auth/token/",
                json={"email": "<EMAIL>", "password": "admin123"},
                headers={
                    "Content-Type": "application/json",
                    "Origin": origin
                },
                timeout=10
            )
            
            print(f"Auth from {origin}: {response.status_code}")
            
            if response.status_code == 200:
                auth_data = response.json()
                if auth_data.get('access'):
                    print(f"  ✅ Authentication successful")
                    
                    # Test tenant endpoint with this token
                    token = auth_data['access']
                    tenant_response = requests.get(
                        f"{base_url}/api/tenants/",
                        headers={
                            "Authorization": f"Bearer {token}",
                            "Origin": origin
                        },
                        timeout=10
                    )
                    
                    print(f"  Tenant endpoint: {tenant_response.status_code}")
                    
                    if tenant_response.status_code == 200:
                        print(f"  ✅ Tenant endpoint working")
                        return True
                    else:
                        print(f"  ❌ Tenant endpoint failed: {tenant_response.text[:100]}")
                else:
                    print(f"  ❌ No access token received")
            else:
                print(f"  ❌ Authentication failed: {response.text[:100]}")
                
        except Exception as e:
            print(f"  ❌ Auth test failed: {e}")
    
    return False

def test_from_browser_perspective():
    """Simulate exactly what the browser does."""
    
    base_url = "http://************:8000"
    origin = "http://************:3000"
    
    print(f"\n📍 Step 4: Simulating browser requests...")
    
    # Simulate the exact sequence the frontend does
    session = requests.Session()
    
    # 1. Browser sends preflight for auth
    print("1. CORS preflight for authentication...")
    preflight_auth = session.options(
        f"{base_url}/api/auth/token/",
        headers={
            "Origin": origin,
            "Access-Control-Request-Method": "POST",
            "Access-Control-Request-Headers": "content-type"
        }
    )
    print(f"   Status: {preflight_auth.status_code}")
    
    # 2. Browser sends actual auth request
    print("2. Actual authentication request...")
    auth_response = session.post(
        f"{base_url}/api/auth/token/",
        json={"email": "<EMAIL>", "password": "admin123"},
        headers={
            "Content-Type": "application/json",
            "Origin": origin
        }
    )
    print(f"   Status: {auth_response.status_code}")
    
    if auth_response.status_code == 200:
        token = auth_response.json().get('access')
        
        # 3. Browser sends preflight for tenant request
        print("3. CORS preflight for tenant request...")
        preflight_tenant = session.options(
            f"{base_url}/api/tenants/",
            headers={
                "Origin": origin,
                "Access-Control-Request-Method": "GET",
                "Access-Control-Request-Headers": "authorization"
            }
        )
        print(f"   Status: {preflight_tenant.status_code}")
        
        # 4. Browser sends actual tenant request
        print("4. Actual tenant request...")
        tenant_response = session.get(
            f"{base_url}/api/tenants/",
            headers={
                "Authorization": f"Bearer {token}",
                "Origin": origin
            }
        )
        print(f"   Status: {tenant_response.status_code}")
        
        if tenant_response.status_code == 200:
            print("   ✅ Complete browser simulation successful!")
            return True
        else:
            print(f"   ❌ Tenant request failed: {tenant_response.text[:100]}")
    
    return False

if __name__ == "__main__":
    print("🚀 Testing Network Access for Multi-Computer Setup")
    print("=" * 55)
    
    success = test_network_connectivity()
    browser_success = test_from_browser_perspective()
    
    print("\n" + "=" * 55)
    
    if success and browser_success:
        print("✅ SUCCESS: Network access is working correctly!")
        print("")
        print("🎯 You should now be able to:")
        print("1. Access frontend from other computers: http://************:3000/")
        print("2. Login and see tenant list without ERR_EMPTY_RESPONSE")
        print("3. Use the system from any computer on the network")
    else:
        print("❌ FAILED: Network access issues detected")
        print("")
        print("🔧 Troubleshooting steps:")
        print("1. Check firewall: sudo ufw status")
        print("2. Test port access: telnet ************ 8000")
        print("3. Check Docker binding: docker-compose logs backend")
        print("4. Verify CORS settings in Django")
        print("5. Run: ./fix_network_access_issue.sh")
    
    print("")
    print("💡 Common solutions:")
    print("- Restart Docker services: docker-compose restart")
    print("- Check firewall rules for ports 3000 and 8000")
    print("- Verify ************ is the correct network IP")
    print("- Test from another computer: curl -I http://************:8000/admin/")
