#!/bin/bash

echo "🚀 GoID Production Deployment Setup"
echo "===================================="
echo

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   echo "❌ This script should not be run as root"
   exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "📦 Installing Docker..."
    sudo apt update
    sudo apt install -y docker.io docker-compose
    sudo usermod -aG docker $USER
    echo "✅ Docker installed. Please log out and log back in."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "📦 Installing Docker Compose..."
    sudo apt install -y docker-compose
fi

echo "✅ Docker and Docker Compose are available"

# Create environment file if it doesn't exist
if [ ! -f .env ]; then
    echo "🔧 Creating environment file..."
    cat > .env << EOF
# Database Configuration
DB_PASSWORD=$(openssl rand -base64 32)

# Security
SECRET_KEY=$(openssl rand -base64 64)

# Network
SERVER_IP=$(hostname -I | awk '{print $1}')
ALLOWED_HOSTS=*

# SSL (set to true for production)
SSL_ENABLED=false
EOF
    echo "✅ Environment file created"
else
    echo "✅ Environment file already exists"
fi

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p backend/media backend/static backups nginx/ssl

# Set permissions
echo "🔐 Setting permissions..."
chmod 755 backend/media backend/static backups

# Pull latest images
echo "📥 Pulling Docker images..."
docker-compose -f docker-compose.production.yml pull

# Build and start services
echo "🚀 Starting GoID services..."
docker-compose -f docker-compose.production.yml up -d

# Wait for services to start
echo "⏳ Waiting for services to start..."
sleep 30

# Run database migrations
echo "🗄️ Running database migrations..."
docker-compose -f docker-compose.production.yml exec -T backend python manage.py migrate

# Create superuser
echo "👤 Creating superuser..."
docker-compose -f docker-compose.production.yml exec -T backend python manage.py shell << EOF
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(email='<EMAIL>').exists():
    User.objects.create_superuser('<EMAIL>', 'admin123')
    print('Superuser created: <EMAIL> / admin123')
else:
    print('Superuser already exists')
EOF

# Check service status
echo "🔍 Checking service status..."
docker-compose -f docker-compose.production.yml ps

# Get server IP
SERVER_IP=$(hostname -I | awk '{print $1}')

echo
echo "✅ GoID Production Deployment Complete!"
echo "======================================"
echo
echo "🌐 Frontend URL: http://$SERVER_IP:3000"
echo "🔧 Backend API: http://$SERVER_IP:8000"
echo "👤 Admin Login: <EMAIL> / admin123"
echo
echo "📋 Next Steps:"
echo "1. Install biometric service on each workstation"
echo "2. Configure network access"
echo "3. Test citizen registration with biometric capture"
echo "4. Change default admin password"
echo
echo "📖 See PRODUCTION_DEPLOYMENT.md for detailed instructions"
echo
