@echo off
echo ========================================
echo   Ultra Stable Biometric Service
echo ========================================
echo.
echo Designed for MAXIMUM STABILITY
echo - Thread-safe operations
echo - Memory management
echo - Error isolation
echo - Service survival guarantee
echo.

cd local-biometric-service

echo 🚀 Starting ULTRA STABLE service...
echo 📱 Service URL: http://localhost:8001
echo 🎯 Focus: Never crash, always available
echo.
echo ⚠️  ULTRA STABLE FEATURES:
echo    - Thread locks prevent conflicts
echo    - Memory cleanup after each capture
echo    - Isolated error handling
echo    - Service stays alive no matter what
echo.
echo 📋 FOR PRODUCTION USE:
echo    - Designed for kebele workstations
echo    - Handles multiple captures reliably
echo    - No need to restart service
echo    - Real fingerprint capture only
echo.

python ultra_stable_service.py

echo.
echo Ultra stable service stopped.
pause
