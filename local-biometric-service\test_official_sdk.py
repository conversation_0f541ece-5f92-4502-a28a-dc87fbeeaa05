#!/usr/bin/env python3
"""
Test Script for Official Futronic SDK Service
Validates SDK integration and service functionality
"""

import requests
import json
import time
import sys
from datetime import datetime

class SDKServiceTester:
    def __init__(self, base_url="http://localhost:8001"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
    
    def log_test(self, test_name, success, message="", data=None):
        """Log test result"""
        result = {
            'test': test_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now().isoformat(),
            'data': data
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        
        if data and not success:
            print(f"   Details: {json.dumps(data, indent=2)}")
    
    def test_service_health(self):
        """Test service health endpoint"""
        try:
            response = self.session.get(f"{self.base_url}/api/health", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                self.log_test(
                    "Service Health Check",
                    True,
                    f"Service is healthy - Version: {data.get('version', 'Unknown')}",
                    data
                )
                return True
            else:
                self.log_test(
                    "Service Health Check",
                    False,
                    f"HTTP {response.status_code}",
                    response.text
                )
                return False
                
        except Exception as e:
            self.log_test(
                "Service Health Check",
                False,
                f"Connection failed: {str(e)}"
            )
            return False
    
    def test_device_status(self):
        """Test device status endpoint"""
        try:
            response = self.session.get(f"{self.base_url}/api/device/status", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    device_info = data.get('data', {})
                    connected = device_info.get('connected', False)
                    
                    self.log_test(
                        "Device Status Check",
                        True,
                        f"Device {'connected' if connected else 'not connected'} - Model: {device_info.get('model', 'Unknown')}",
                        device_info
                    )
                    return connected
                else:
                    self.log_test(
                        "Device Status Check",
                        False,
                        "Status check failed",
                        data
                    )
                    return False
            else:
                self.log_test(
                    "Device Status Check",
                    False,
                    f"HTTP {response.status_code}",
                    response.text
                )
                return False
                
        except Exception as e:
            self.log_test(
                "Device Status Check",
                False,
                f"Request failed: {str(e)}"
            )
            return False
    
    def test_device_initialization(self):
        """Test device initialization"""
        try:
            response = self.session.post(f"{self.base_url}/api/device/initialize", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log_test(
                        "Device Initialization",
                        True,
                        "Device initialized successfully",
                        data.get('data')
                    )
                    return True
                else:
                    self.log_test(
                        "Device Initialization",
                        False,
                        data.get('error', 'Unknown error'),
                        data
                    )
                    return False
            else:
                self.log_test(
                    "Device Initialization",
                    False,
                    f"HTTP {response.status_code}",
                    response.text
                )
                return False
                
        except Exception as e:
            self.log_test(
                "Device Initialization",
                False,
                f"Request failed: {str(e)}"
            )
            return False
    
    def test_fingerprint_capture(self, thumb_type="left"):
        """Test fingerprint capture"""
        try:
            print(f"\n🖐️ Testing {thumb_type} thumb capture...")
            print("   Please place your finger on the scanner when prompted")
            
            response = self.session.post(
                f"{self.base_url}/api/capture/fingerprint",
                json={'thumb_type': thumb_type},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    capture_data = data.get('data', {})
                    quality_score = capture_data.get('quality_score', 0)
                    minutiae_count = capture_data.get('minutiae_count', 0)
                    
                    self.log_test(
                        f"{thumb_type.title()} Thumb Capture",
                        True,
                        f"Quality: {quality_score}%, Minutiae: {minutiae_count}",
                        {
                            'quality_score': quality_score,
                            'minutiae_count': minutiae_count,
                            'has_template': bool(capture_data.get('template_data'))
                        }
                    )
                    return capture_data
                else:
                    self.log_test(
                        f"{thumb_type.title()} Thumb Capture",
                        False,
                        data.get('error', 'Unknown error'),
                        data
                    )
                    return None
            else:
                self.log_test(
                    f"{thumb_type.title()} Thumb Capture",
                    False,
                    f"HTTP {response.status_code}",
                    response.text
                )
                return None
                
        except Exception as e:
            self.log_test(
                f"{thumb_type.title()} Thumb Capture",
                False,
                f"Request failed: {str(e)}"
            )
            return None
    
    def test_template_matching(self, template1_data, template2_data):
        """Test template matching (if available)"""
        try:
            if not template1_data or not template2_data:
                self.log_test(
                    "Template Matching",
                    False,
                    "Insufficient template data for matching test"
                )
                return False
            
            template1 = template1_data.get('template_data')
            template2 = template2_data.get('template_data')
            
            if not template1 or not template2:
                self.log_test(
                    "Template Matching",
                    False,
                    "Template data not available"
                )
                return False
            
            response = self.session.post(
                f"{self.base_url}/api/match/templates",
                json={
                    'template1': template1,
                    'template2': template2
                },
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    match_data = data.get('data', {})
                    match_score = match_data.get('match_score', 0)
                    is_match = match_data.get('is_match', False)
                    
                    self.log_test(
                        "Template Matching",
                        True,
                        f"Match Score: {match_score}%, Is Match: {is_match}",
                        match_data
                    )
                    return True
                else:
                    self.log_test(
                        "Template Matching",
                        False,
                        data.get('error', 'Unknown error'),
                        data
                    )
                    return False
            else:
                self.log_test(
                    "Template Matching",
                    False,
                    f"HTTP {response.status_code}",
                    response.text
                )
                return False
                
        except Exception as e:
            self.log_test(
                "Template Matching",
                False,
                f"Request failed: {str(e)}"
            )
            return False
    
    def run_comprehensive_test(self):
        """Run comprehensive test suite"""
        print("🧪 Starting Official Futronic SDK Service Test Suite")
        print("=" * 60)
        
        # Test 1: Service Health
        if not self.test_service_health():
            print("\n❌ Service is not running. Please start the service first.")
            return False
        
        # Test 2: Device Status
        device_connected = self.test_device_status()
        
        # Test 3: Device Initialization (if not connected)
        if not device_connected:
            print("\n🔌 Attempting device initialization...")
            device_connected = self.test_device_initialization()
        
        if not device_connected:
            print("\n⚠️ Device not available. Skipping capture tests.")
            print("   Please ensure your Futronic FS88H device is connected.")
        else:
            # Test 4: Fingerprint Capture
            print("\n📸 Testing fingerprint capture...")
            left_capture = self.test_fingerprint_capture("left")
            
            # Optional: Test right thumb capture
            if left_capture:
                print("\n📸 Testing second capture for matching...")
                right_capture = self.test_fingerprint_capture("right")
                
                # Test 5: Template Matching (if both captures successful)
                if right_capture:
                    print("\n🔍 Testing template matching...")
                    self.test_template_matching(left_capture, right_capture)
        
        # Print summary
        self.print_test_summary()
        
        return True
    
    def print_test_summary(self):
        """Print test summary"""
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests*100):.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result['success']:
                    print(f"   - {result['test']}: {result['message']}")
        
        print("\n✅ Test completed successfully!" if failed_tests == 0 else "\n⚠️ Some tests failed. Check the details above.")

def main():
    """Main test function"""
    print("Official Futronic SDK Service Tester")
    print("====================================")
    
    # Check if service URL is provided
    service_url = "http://localhost:8001"
    if len(sys.argv) > 1:
        service_url = sys.argv[1]
    
    print(f"Testing service at: {service_url}")
    print()
    
    # Create tester and run tests
    tester = SDKServiceTester(service_url)
    tester.run_comprehensive_test()

if __name__ == "__main__":
    main()
