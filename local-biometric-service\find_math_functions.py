#!/usr/bin/env python3
"""
Find Math API functions with more comprehensive search
"""

import ctypes
from ctypes import cdll
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def find_math_functions():
    """Try to find math API functions with various naming patterns"""
    try:
        logger.info("🔍 Searching for Math API functions...")
        
        math_api = cdll.LoadLibrary('./ftrMathAPI.dll')
        
        # Extended list of possible function names
        possible_names = [
            # Standard naming
            'ftrCreateTemplate', 'ftrVerifyTemplate', 'ftrMatchTemplate',
            'ftrEnrollTemplate', 'ftrIdentifyTemplate', 'ftrExtractTemplate',
            
            # Capital case
            'FtrCreateTemplate', 'FtrVerifyTemplate', 'FtrMatchTemplate',
            'FtrEnrollTemplate', 'FtrIdentifyTemplate', 'FtrExtractTemplate',
            
            # With underscores
            '_ftrCreateTemplate', '_ftrVerifyTemplate', '_ftrMatchTemplate',
            '_ftrEnrollTemplate', '_ftrIdentifyTemplate', '_ftrExtractTemplate',
            
            # Alternative naming patterns
            'CreateTemplate', 'VerifyTemplate', 'MatchTemplate',
            'EnrollTemplate', 'IdentifyTemplate', 'ExtractTemplate',
            
            # Futronic specific patterns
            'ftrMathCreateTemplate', 'ftrMathVerifyTemplate', 'ftrMathMatchTemplate',
            'FtrMathCreateTemplate', 'FtrMathVerifyTemplate', 'FtrMathMatchTemplate',
            
            # Template operations
            'ftrTemplateCreate', 'ftrTemplateVerify', 'ftrTemplateMatch',
            'FtrTemplateCreate', 'FtrTemplateVerify', 'FtrTemplateMatch',
            
            # Biometric operations
            'ftrBioCreateTemplate', 'ftrBioVerifyTemplate', 'ftrBioMatchTemplate',
            'FtrBioCreateTemplate', 'FtrBioVerifyTemplate', 'FtrBioMatchTemplate',
            
            # With ordinals (common in Windows DLLs)
            'ftrCreateTemplate@12', 'ftrVerifyTemplate@8', 'ftrMatchTemplate@12',
            'ftrCreateTemplate@16', 'ftrVerifyTemplate@12', 'ftrMatchTemplate@16',
            
            # ISO template functions
            'ftrCreateISOTemplate', 'ftrVerifyISOTemplate', 'ftrMatchISOTemplate',
            'FtrCreateISOTemplate', 'FtrVerifyISOTemplate', 'FtrMatchISOTemplate',
            
            # ANSI template functions  
            'ftrCreateANSITemplate', 'ftrVerifyANSITemplate', 'ftrMatchANSITemplate',
            'FtrCreateANSITemplate', 'FtrVerifyANSITemplate', 'FtrMatchANSITemplate',
        ]
        
        found_functions = []
        
        for func_name in possible_names:
            try:
                func = getattr(math_api, func_name)
                found_functions.append(func_name)
                logger.info(f"  ✅ Found: {func_name}")
            except AttributeError:
                pass
        
        return found_functions
        
    except Exception as e:
        logger.error(f"❌ Failed to search math functions: {e}")
        return []

def test_alternative_approach():
    """Test if we can use scan API only (some SDKs combine functions)"""
    try:
        logger.info("🧪 Testing scan API for template functions...")
        
        scan_api = cdll.LoadLibrary('./ftrScanAPI.dll')
        
        # Some Futronic SDKs put template functions in scan API
        template_in_scan = [
            'ftrScanCreateTemplate', 'ftrScanVerifyTemplate', 'ftrScanMatchTemplate',
            'FtrScanCreateTemplate', 'FtrScanVerifyTemplate', 'FtrScanMatchTemplate',
            'ftrCreateTemplate', 'ftrVerifyTemplate', 'ftrMatchTemplate',
            'CreateTemplate', 'VerifyTemplate', 'MatchTemplate',
        ]
        
        found_in_scan = []
        
        for func_name in template_in_scan:
            try:
                func = getattr(scan_api, func_name)
                found_in_scan.append(func_name)
                logger.info(f"  ✅ Found in scan API: {func_name}")
            except AttributeError:
                pass
        
        return found_in_scan
        
    except Exception as e:
        logger.error(f"❌ Scan API template search failed: {e}")
        return []

def test_livefinger_dll():
    """Test LiveFinger2.dll for template functions"""
    try:
        logger.info("🧪 Testing LiveFinger2.dll...")
        
        live_finger = cdll.LoadLibrary('./LiveFinger2.dll')
        
        # LiveFinger might have the template functions
        live_finger_functions = [
            'CreateTemplate', 'VerifyTemplate', 'MatchTemplate',
            'ExtractTemplate', 'EnrollTemplate', 'IdentifyTemplate',
            'ftrCreateTemplate', 'ftrVerifyTemplate', 'ftrMatchTemplate',
            'LiveFingerCreateTemplate', 'LiveFingerVerifyTemplate', 'LiveFingerMatchTemplate',
            'LF_CreateTemplate', 'LF_VerifyTemplate', 'LF_MatchTemplate',
        ]
        
        found_in_live = []
        
        for func_name in live_finger_functions:
            try:
                func = getattr(live_finger, func_name)
                found_in_live.append(func_name)
                logger.info(f"  ✅ Found in LiveFinger2: {func_name}")
            except AttributeError:
                pass
        
        return found_in_live
        
    except Exception as e:
        logger.error(f"❌ LiveFinger2 search failed: {e}")
        return []

def main():
    """Main function search"""
    logger.info("🔍 Comprehensive Math Function Search")
    logger.info("=" * 40)
    
    # Search in Math API
    logger.info("\n📋 Searching ftrMathAPI.dll:")
    math_functions = find_math_functions()
    
    # Search in Scan API
    logger.info("\n📋 Searching ftrScanAPI.dll for template functions:")
    scan_template_functions = test_alternative_approach()
    
    # Search in LiveFinger2
    logger.info("\n📋 Searching LiveFinger2.dll:")
    live_finger_functions = test_livefinger_dll()
    
    # Summary
    logger.info("\n📊 Function Search Results:")
    logger.info("=" * 30)
    
    logger.info(f"ftrMathAPI.dll: {len(math_functions)} functions")
    for func in math_functions:
        logger.info(f"  - {func}")
    
    logger.info(f"ftrScanAPI.dll templates: {len(scan_template_functions)} functions")
    for func in scan_template_functions:
        logger.info(f"  - {func}")
    
    logger.info(f"LiveFinger2.dll: {len(live_finger_functions)} functions")
    for func in live_finger_functions:
        logger.info(f"  - {func}")
    
    # Recommendations
    total_found = len(math_functions) + len(scan_template_functions) + len(live_finger_functions)
    
    if total_found > 0:
        logger.info(f"\n✅ Found {total_found} template-related functions!")
        logger.info("💡 We can now update the SDK integration with correct function names")
    else:
        logger.warning("\n❌ No template functions found")
        logger.info("💡 Possible solutions:")
        logger.info("1. This might be a newer SDK with different function names")
        logger.info("2. Functions might be in a different DLL")
        logger.info("3. SDK might use a different API approach")
        logger.info("4. Try using only scan functions for basic fingerprint capture")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
