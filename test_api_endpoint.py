#!/usr/bin/env python
import os
import sys
import django
import requests

# Add the backend directory to the Python path
sys.path.append('/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()
user = User.objects.filter(is_superuser=True).first()
token = RefreshToken.for_user(user)

print(f'Testing API endpoint: /api/tenants/8/users/')
print(f'Using token for user: {user.email}')

headers = {'Authorization': f'Bearer {token.access_token}'}
response = requests.get('http://localhost:8000/api/tenants/8/users/', headers=headers)

print(f'Status: {response.status_code}')
if response.status_code == 200:
    data = response.json()
    print(f'Found {len(data)} users')
    for user_data in data:
        print(f'\nUser: {user_data["email"]}')
        print(f'  Primary Group ID: {user_data.get("primary_group_id")}')
        print(f'  Primary Group Name: {user_data.get("primary_group_name")}')
        groups = user_data.get("groups", [])
        print(f'  Groups ({len(groups)}):')
        for group in groups:
            print(f'    - {group["name"]} (ID: {group["id"]}, Primary: {group["is_primary"]})')
else:
    print(f'Error: {response.text}')
