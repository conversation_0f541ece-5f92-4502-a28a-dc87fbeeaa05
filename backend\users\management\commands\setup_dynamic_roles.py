from django.core.management.base import BaseCommand
from django.db import transaction
from users.models import Role, RoleType, UserRole, Permission, RolePermission


class Command(BaseCommand):
    help = 'Setup built-in roles as Role objects and create role hierarchy'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-built-in',
            action='store_true',
            help='Create built-in roles as Role objects',
        )
        parser.add_argument(
            '--setup-hierarchy',
            action='store_true',
            help='Setup role hierarchy relationships',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up dynamic roles...'))
        
        with transaction.atomic():
            if options['create_built_in']:
                self.create_built_in_roles()
            
            if options['setup_hierarchy']:
                self.setup_role_hierarchy()
            
            # If no specific options, do both
            if not options['create_built_in'] and not options['setup_hierarchy']:
                self.create_built_in_roles()
                self.setup_role_hierarchy()
        
        self.stdout.write(self.style.SUCCESS('Dynamic roles setup completed!'))

    def create_built_in_roles(self):
        """Create built-in roles as Role objects"""
        self.stdout.write('Creating built-in roles...')
        
        built_in_roles = [
            {
                'codename': 'superadmin',
                'name': 'Super Admin',
                'description': 'System administrator with full access to all features and data',
                'role_type': RoleType.SYSTEM,
                'level': 100,
                'allowed_tenant_types': ['city', 'subcity', 'kebele']
            },
            {
                'codename': 'city_admin',
                'name': 'City Admin',
                'description': 'Administrator for city-level operations and management',
                'role_type': RoleType.ADMINISTRATIVE,
                'level': 80,
                'allowed_tenant_types': ['city', 'subcity', 'kebele']
            },
            {
                'codename': 'subcity_admin',
                'name': 'Sub City Admin',
                'description': 'Administrator for subcity-level operations and management',
                'role_type': RoleType.ADMINISTRATIVE,
                'level': 60,
                'allowed_tenant_types': ['subcity', 'kebele']
            },
            {
                'codename': 'kebele_admin',
                'name': 'Kebele Admin',
                'description': 'Administrator for kebele-level operations and management',
                'role_type': RoleType.ADMINISTRATIVE,
                'level': 40,
                'allowed_tenant_types': ['kebele']
            },
            {
                'codename': 'kebele_leader',
                'name': 'Kebele Leader',
                'description': 'Kebele leader responsible for approvals and oversight',
                'role_type': RoleType.OPERATIONAL,
                'level': 30,
                'allowed_tenant_types': ['kebele']
            },
            {
                'codename': 'clerk',
                'name': 'Clerk',
                'description': 'Front-line staff for citizen registration and data entry',
                'role_type': RoleType.OPERATIONAL,
                'level': 10,
                'allowed_tenant_types': ['kebele']
            }
        ]
        
        for role_data in built_in_roles:
            role, created = Role.objects.get_or_create(
                codename=role_data['codename'],
                defaults={
                    'name': role_data['name'],
                    'description': role_data['description'],
                    'role_type': role_data['role_type'],
                    'level': role_data['level'],
                    'allowed_tenant_types': role_data['allowed_tenant_types'],
                    'is_built_in': True,
                    'is_active': True
                }
            )
            
            if created:
                self.stdout.write(f"Created built-in role: {role.name}")
            else:
                self.stdout.write(f"Built-in role already exists: {role.name}")

    def setup_role_hierarchy(self):
        """Setup role hierarchy relationships"""
        self.stdout.write('Setting up role hierarchy...')
        
        # Define which roles can manage which other roles
        hierarchy = {
            'superadmin': ['city_admin', 'subcity_admin', 'kebele_admin', 'kebele_leader', 'clerk'],
            'city_admin': ['subcity_admin', 'kebele_admin', 'kebele_leader', 'clerk'],
            'subcity_admin': ['kebele_admin', 'kebele_leader', 'clerk'],
            'kebele_admin': ['kebele_leader', 'clerk'],
            'kebele_leader': ['clerk'],
            'clerk': []
        }
        
        for manager_codename, managed_codenames in hierarchy.items():
            try:
                manager_role = Role.objects.get(codename=manager_codename, is_built_in=True)
                managed_roles = Role.objects.filter(
                    codename__in=managed_codenames,
                    is_built_in=True
                )
                
                manager_role.can_manage_roles.set(managed_roles)
                self.stdout.write(
                    f"Set {manager_role.name} to manage: {', '.join([r.name for r in managed_roles])}"
                )
                
            except Role.DoesNotExist:
                self.stdout.write(
                    self.style.WARNING(f"Role {manager_codename} not found, skipping hierarchy setup")
                )

    def migrate_legacy_permissions(self):
        """Migrate existing RolePermission entries to use Role objects"""
        self.stdout.write('Migrating legacy role permissions...')
        
        # Get all legacy role permissions
        legacy_permissions = RolePermission.objects.filter(
            role__isnull=False,
            role_obj__isnull=True
        )
        
        migrated_count = 0
        for legacy_perm in legacy_permissions:
            try:
                # Find corresponding Role object
                role_obj = Role.objects.get(codename=legacy_perm.role, is_built_in=True)
                
                # Create new RolePermission with Role object
                new_perm, created = RolePermission.objects.get_or_create(
                    role_obj=role_obj,
                    permission=legacy_perm.permission,
                    defaults={
                        'is_active': legacy_perm.is_active
                    }
                )
                
                if created:
                    migrated_count += 1
                    self.stdout.write(
                        f"Migrated permission {legacy_perm.permission.name} for role {role_obj.name}"
                    )
                
            except Role.DoesNotExist:
                self.stdout.write(
                    self.style.WARNING(
                        f"No Role object found for legacy role {legacy_perm.role}, skipping"
                    )
                )
        
        self.stdout.write(f"Migrated {migrated_count} role permissions")

    def create_example_custom_roles(self):
        """Create some example custom roles to demonstrate the system"""
        self.stdout.write('Creating example custom roles...')
        
        example_roles = [
            {
                'codename': 'emergency_coordinator',
                'name': 'Emergency Coordinator',
                'description': 'Special role for emergency situations with elevated permissions',
                'role_type': RoleType.CUSTOM,
                'level': 50,
                'allowed_tenant_types': ['kebele', 'subcity'],
                'can_manage': ['clerk'],
                'permissions': ['print_id_cards', 'approve_id_cards', 'create_id_cards', 'view_citizens']
            },
            {
                'codename': 'field_officer',
                'name': 'Field Officer',
                'description': 'Mobile officer for field operations and citizen registration',
                'role_type': RoleType.OPERATIONAL,
                'level': 20,
                'allowed_tenant_types': ['kebele'],
                'can_manage': [],
                'permissions': ['view_citizens', 'create_citizens', 'edit_citizens', 'view_id_cards']
            },
            {
                'codename': 'senior_clerk',
                'name': 'Senior Clerk',
                'description': 'Experienced clerk with additional approval permissions',
                'role_type': RoleType.OPERATIONAL,
                'level': 15,
                'allowed_tenant_types': ['kebele'],
                'can_manage': [],
                'permissions': ['view_citizens', 'create_citizens', 'edit_citizens', 'view_id_cards', 'create_id_cards', 'approve_id_cards']
            },
            {
                'codename': 'data_analyst',
                'name': 'Data Analyst',
                'description': 'Analyst role with read-only access and reporting permissions',
                'role_type': RoleType.CUSTOM,
                'level': 25,
                'allowed_tenant_types': ['city', 'subcity', 'kebele'],
                'can_manage': [],
                'permissions': ['view_citizens', 'view_id_cards', 'view_reports', 'export_data']
            }
        ]
        
        for role_data in example_roles:
            role, created = Role.objects.get_or_create(
                codename=role_data['codename'],
                defaults={
                    'name': role_data['name'],
                    'description': role_data['description'],
                    'role_type': role_data['role_type'],
                    'level': role_data['level'],
                    'allowed_tenant_types': role_data['allowed_tenant_types'],
                    'is_built_in': False,
                    'is_active': True
                }
            )
            
            if created:
                self.stdout.write(f"Created example role: {role.name}")
                
                # Set manageable roles
                if role_data['can_manage']:
                    manageable_roles = Role.objects.filter(
                        codename__in=role_data['can_manage'],
                        is_active=True
                    )
                    role.can_manage_roles.set(manageable_roles)
                
                # Assign permissions
                if role_data['permissions']:
                    permissions = Permission.objects.filter(
                        codename__in=role_data['permissions'],
                        is_active=True
                    )
                    role_permissions = [
                        RolePermission(role_obj=role, permission=permission)
                        for permission in permissions
                    ]
                    RolePermission.objects.bulk_create(role_permissions, ignore_conflicts=True)
            else:
                self.stdout.write(f"Example role already exists: {role.name}")

    def add_arguments(self, parser):
        super().add_arguments(parser)
        parser.add_argument(
            '--create-examples',
            action='store_true',
            help='Create example custom roles',
        )
        parser.add_argument(
            '--migrate-permissions',
            action='store_true',
            help='Migrate legacy role permissions to use Role objects',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up dynamic roles...'))
        
        with transaction.atomic():
            if options.get('create_built_in', False):
                self.create_built_in_roles()
            
            if options.get('setup_hierarchy', False):
                self.setup_role_hierarchy()
            
            if options.get('create_examples', False):
                self.create_example_custom_roles()
            
            if options.get('migrate_permissions', False):
                self.migrate_legacy_permissions()
            
            # If no specific options, do basic setup
            if not any([
                options.get('create_built_in'),
                options.get('setup_hierarchy'),
                options.get('create_examples'),
                options.get('migrate_permissions')
            ]):
                self.create_built_in_roles()
                self.setup_role_hierarchy()
        
        self.stdout.write(self.style.SUCCESS('Dynamic roles setup completed!'))
