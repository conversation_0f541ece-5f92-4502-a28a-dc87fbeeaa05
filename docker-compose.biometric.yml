# Docker Compose override for external biometric service
# This configuration assumes the biometric service runs on the host machine
# for direct USB device access

version: '3.8'

services:
  frontend:
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:8000
      # The frontend will automatically detect the external biometric service
      # running on the host machine at various addresses:
      # - http://localhost:8001 (direct)
      # - http://host.docker.internal:8001 (Docker Desktop)
      # - http://**********:8001 (Docker bridge IP)
    extra_hosts:
      # Allow container to access host services
      - "host.docker.internal:host-gateway"

  backend:
    environment:
      - DATABASE_URL=************************************/goid
      - DEBUG=1
      # Backend can also fall back to external biometric service if needed
    extra_hosts:
      - "host.docker.internal:host-gateway"

# Instructions:
# 1. Start the external biometric service on the host:
#    > start_external_biometric_service.bat
# 
# 2. Start Docker services with biometric support:
#    > docker-compose -f docker-compose.yml -f docker-compose.biometric.yml up
#
# 3. The frontend will automatically detect and use the external service
#    for fingerprint capture while falling back to server-side processing
#    if the external service is not available.
