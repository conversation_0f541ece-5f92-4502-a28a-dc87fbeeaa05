#!/usr/bin/env python3
"""
Simple test to verify Flask routes are working
"""
from flask import Flask, jsonify
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

@app.route('/', methods=['GET'])
def index():
    return "Hello World! Flask is working."

@app.route('/api/test', methods=['GET'])
def test():
    return jsonify({'message': 'Test endpoint working'})

if __name__ == '__main__':
    print("Starting simple Flask test on port 8002...")
    app.run(host='0.0.0.0', port=8002, debug=True)
