#!/usr/bin/env python
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append('/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from django_tenants.utils import schema_context
from rest_framework.test import APIClient
from rest_framework import status
import json

User = get_user_model()

def test_login_flow():
    print('=== Testing Complete Login Flow ===')
    
    # Test login with clerk user
    client = APIClient()
    
    print('\n--- Testing Clerk Login ---')
    login_data = {
        'username': '<EMAIL>',
        'password': 'password123'
    }
    
    try:
        response = client.post('/api/auth/token/', login_data, format='json')
        print(f'Login response status: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            print(f'Login successful!')
            print(f'Response keys: {list(data.keys())}')
            
            # Check if we have access and refresh tokens
            if 'access' in data and 'refresh' in data:
                print(f'✅ Access token: {len(data["access"])} characters')
                print(f'✅ Refresh token: {len(data["refresh"])} characters')
                
                # Decode the access token to check permissions
                import jwt
                from django.conf import settings
                
                try:
                    decoded = jwt.decode(data['access'], settings.SECRET_KEY, algorithms=['HS256'])
                    print(f'✅ JWT decoded successfully')
                    print(f'JWT keys: {list(decoded.keys())}')
                    
                    if 'permissions' in decoded:
                        permissions = decoded['permissions']
                        print(f'✅ Permissions in JWT: {len(permissions)} permissions')
                        print(f'Sample permissions: {permissions[:5]}')
                        
                        # Check for key permissions
                        key_permissions = ['view_citizen', 'add_citizen', 'view_idcard', 'add_idcard']
                        found_permissions = [p for p in key_permissions if p in permissions]
                        print(f'Key permissions found: {found_permissions}')
                        
                        if len(found_permissions) >= 3:
                            print('✅ JWT contains expected permissions for navigation')
                        else:
                            print('❌ JWT missing expected permissions')
                    else:
                        print('❌ No permissions in JWT')
                        
                    # Check user info
                    print(f'User ID: {decoded.get("user_id")}')
                    print(f'Email: {decoded.get("email")}')
                    print(f'Role: {decoded.get("role")}')
                    print(f'Tenant: {decoded.get("tenant_name")} (ID: {decoded.get("tenant_id")})')
                    
                except Exception as e:
                    print(f'❌ Error decoding JWT: {e}')
            else:
                print('❌ Missing access or refresh token')
                
            # Check user data in response
            if 'email' in data:
                print(f'✅ User email in response: {data["email"]}')
            if 'role' in data:
                print(f'✅ User role in response: {data["role"]}')
                
        else:
            print(f'❌ Login failed with status {response.status_code}')
            print(f'Response: {response.content}')
            
    except Exception as e:
        print(f'❌ Login test error: {e}')
        import traceback
        traceback.print_exc()

    # Test with kebele leader
    print('\n--- Testing Kebele Leader Login ---')
    login_data = {
        'username': '<EMAIL>',
        'password': 'password123'
    }
    
    try:
        response = client.post('/api/auth/token/', login_data, format='json')
        print(f'Login response status: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            
            if 'access' in data:
                import jwt
                from django.conf import settings
                
                try:
                    decoded = jwt.decode(data['access'], settings.SECRET_KEY, algorithms=['HS256'])
                    
                    if 'permissions' in decoded:
                        permissions = decoded['permissions']
                        print(f'✅ Kebele Leader permissions: {len(permissions)} permissions')
                        
                        # Check for leader-specific permissions
                        leader_permissions = ['delete_citizen', 'delete_idcard']
                        found_leader_permissions = [p for p in leader_permissions if p in permissions]
                        print(f'Leader-specific permissions: {found_leader_permissions}')
                        
                        if len(found_leader_permissions) >= 1:
                            print('✅ Kebele leader has expected elevated permissions')
                        else:
                            print('❌ Kebele leader missing elevated permissions')
                            
                except Exception as e:
                    print(f'❌ Error decoding kebele leader JWT: {e}')
                    
    except Exception as e:
        print(f'❌ Kebele leader login error: {e}')

if __name__ == '__main__':
    test_login_flow()
