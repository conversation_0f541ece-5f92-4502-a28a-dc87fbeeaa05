from rest_framework import viewsets, permissions
from ..models.citizen import (
    Religion, CitizenStatus, MaritalStatus,
    EmploymentType, Parent, Child,
    EmergencyContact, Spouse, Citizen, Biometric, Photo,
    Relationship, CurrentStatus
)
from ..serializers import (
    ReligionSerializer, TenantCitizenStatusSerializer, MaritalStatusSerializer,
    EmploymentTypeSerializer, DocumentSerializer,
    ParentSerializer, ChildSerializer, EmergencyContactSerializer,
    SpouseSerializer, TenantCitizenSerializer, CitizenCreateSerializer,
    BiometricSerializer, PhotoSerializer, RelationshipSerializer, CurrentStatusSerializer
)
# Import Document model from citizens app
from citizens.models import Document
from ..permissions import IsSuperUser, CanManageTenants

class ReligionViewSet(viewsets.ModelViewSet):
    """API endpoint for managing religions."""
    queryset = Religion.objects.all()
    serializer_class = ReligionSerializer
    permission_classes = [permissions.IsAuthenticated]
    filterset_fields = ['is_active']
    search_fields = ['name']

class CitizenStatusViewSet(viewsets.ModelViewSet):
    """API endpoint for managing citizen statuses."""
    queryset = CitizenStatus.objects.all()
    serializer_class = TenantCitizenStatusSerializer
    permission_classes = [permissions.IsAuthenticated]
    filterset_fields = ['is_active']
    search_fields = ['name']

class MaritalStatusViewSet(viewsets.ModelViewSet):
    """API endpoint for managing marital statuses."""
    queryset = MaritalStatus.objects.all()
    serializer_class = MaritalStatusSerializer
    permission_classes = [permissions.IsAuthenticated]
    filterset_fields = ['is_active']
    search_fields = ['name']

# DocumentType is now handled by shared schema - no tenant-specific viewset needed

class EmploymentTypeViewSet(viewsets.ModelViewSet):
    """API endpoint for managing employment types."""
    queryset = EmploymentType.objects.all()
    serializer_class = EmploymentTypeSerializer
    permission_classes = [permissions.IsAuthenticated]
    filterset_fields = ['is_active']
    search_fields = ['name', 'description']

class ParentViewSet(viewsets.ModelViewSet):
    """API endpoint for managing parents."""
    queryset = Parent.objects.all()
    serializer_class = ParentSerializer
    permission_classes = [permissions.IsAuthenticated, CanManageTenants]
    filterset_fields = ['is_active', 'nationality']
    search_fields = ['first_name', 'middle_name', 'last_name']

class ChildViewSet(viewsets.ModelViewSet):
    """API endpoint for managing children."""
    queryset = Child.objects.all()
    serializer_class = ChildSerializer
    permission_classes = [permissions.IsAuthenticated, CanManageTenants]
    filterset_fields = ['is_active', 'nationality', 'citizen']
    search_fields = ['first_name', 'middle_name', 'last_name']

class EmergencyContactViewSet(viewsets.ModelViewSet):
    """API endpoint for managing emergency contacts."""
    queryset = EmergencyContact.objects.all()
    serializer_class = EmergencyContactSerializer
    permission_classes = [permissions.IsAuthenticated, CanManageTenants]
    filterset_fields = ['is_active', 'nationality', 'citizen', 'primary_contact']
    search_fields = ['first_name', 'middle_name', 'last_name', 'phone', 'email']

class SpouseViewSet(viewsets.ModelViewSet):
    """API endpoint for managing spouses."""
    queryset = Spouse.objects.all()
    serializer_class = SpouseSerializer
    permission_classes = [permissions.IsAuthenticated, CanManageTenants]
    filterset_fields = ['is_active', 'nationality', 'citizen', 'primary_contact']
    search_fields = ['first_name', 'middle_name', 'last_name', 'phone', 'email']

class DocumentViewSet(viewsets.ModelViewSet):
    """API endpoint for managing documents."""
    queryset = Document.objects.all()
    serializer_class = DocumentSerializer
    permission_classes = [permissions.IsAuthenticated, CanManageTenants]
    filterset_fields = ['is_active', 'document_type', 'citizen']
    search_fields = ['document_type__name', 'citizen__first_name', 'citizen__last_name']

class RelationshipViewSet(viewsets.ModelViewSet):
    """API endpoint for managing relationships."""
    queryset = Relationship.objects.all()
    serializer_class = RelationshipSerializer
    permission_classes = [permissions.IsAuthenticated]
    search_fields = ['name', 'description']

class CurrentStatusViewSet(viewsets.ModelViewSet):
    """API endpoint for managing current statuses."""
    queryset = CurrentStatus.objects.all()
    serializer_class = CurrentStatusSerializer
    permission_classes = [permissions.IsAuthenticated]
    search_fields = ['name']

class BiometricViewSet(viewsets.ModelViewSet):
    """API endpoint for managing biometrics."""
    queryset = Biometric.objects.all()
    serializer_class = BiometricSerializer
    permission_classes = [permissions.IsAuthenticated, CanManageTenants]
    filterset_fields = ['citizen']

class PhotoViewSet(viewsets.ModelViewSet):
    """API endpoint for managing photos."""
    queryset = Photo.objects.all()
    serializer_class = PhotoSerializer
    permission_classes = [permissions.IsAuthenticated, CanManageTenants]
    filterset_fields = ['citizen']

class CitizenViewSet(viewsets.ModelViewSet):
    """API endpoint for managing citizens."""
    queryset = Citizen.objects.all()
    permission_classes = [permissions.IsAuthenticated, CanManageTenants]
    filterset_fields = ['is_active', 'is_resident', 'status', 'current_status', 'marital_status', 'religion', 'subcity', 'kebele', 'ketena']
    search_fields = ['first_name', 'middle_name', 'last_name', 'digital_id', 'phone', 'email']

    def get_queryset(self):
        """Return only active citizens by default."""
        # Check if 'is_active' filter is explicitly provided
        is_active_param = self.request.query_params.get('is_active')

        if is_active_param is not None:
            # If is_active filter is explicitly provided, use it
            if is_active_param.lower() in ['true', '1']:
                return Citizen.objects.filter(is_active=True)
            elif is_active_param.lower() in ['false', '0']:
                return Citizen.objects.filter(is_active=False)
            else:
                return Citizen.objects.all()
        else:
            # By default, only show active citizens
            return Citizen.objects.filter(is_active=True)

    def get_serializer_class(self):
        """Return different serializers for different actions."""
        if self.action == 'create':
            return CitizenCreateSerializer
        return TenantCitizenSerializer
