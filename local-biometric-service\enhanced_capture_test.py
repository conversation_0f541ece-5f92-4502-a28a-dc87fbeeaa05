#!/usr/bin/env python3
"""
Enhanced Real Fingerprint Capture Test
Validates actual fingerprint content (not just zeros)
"""

import os
import sys
import time
import ctypes
import base64
from datetime import datetime

def analyze_fingerprint_data(data_bytes):
    """Analyze captured data to see if it contains real fingerprint"""
    if not data_bytes:
        return False, "No data captured"
    
    # Count non-zero bytes
    non_zero_count = sum(1 for byte in data_bytes if byte != 0)
    total_bytes = len(data_bytes)
    non_zero_percentage = (non_zero_count / total_bytes) * 100
    
    # Calculate basic statistics
    unique_values = len(set(data_bytes))
    
    print(f"Data analysis:")
    print(f"  Total bytes: {total_bytes}")
    print(f"  Non-zero bytes: {non_zero_count}")
    print(f"  Non-zero percentage: {non_zero_percentage:.1f}%")
    print(f"  Unique values: {unique_values}")
    
    # Determine if this looks like real fingerprint data
    if non_zero_count == 0:
        return False, "All zeros - no fingerprint detected"
    elif non_zero_percentage < 5:
        return False, f"Too few non-zero bytes ({non_zero_percentage:.1f}%) - likely empty scan"
    elif unique_values < 10:
        return False, f"Too few unique values ({unique_values}) - not fingerprint pattern"
    else:
        return True, f"Looks like real fingerprint data ({non_zero_percentage:.1f}% non-zero, {unique_values} unique values)"

def enhanced_fingerprint_capture():
    """Enhanced fingerprint capture with data validation"""
    
    try:
        print("=== ENHANCED REAL FINGERPRINT CAPTURE ===")
        print("Loading Futronic SDK...")
        
        # Load SDK
        scan_api = ctypes.cdll.LoadLibrary('./ftrScanAPI.dll')
        print("SDK loaded successfully")
        
        # Setup functions
        scan_api.ftrScanOpenDevice.restype = ctypes.c_void_p
        scan_api.ftrScanCloseDevice.argtypes = [ctypes.c_void_p]
        scan_api.ftrScanCloseDevice.restype = ctypes.c_int
        scan_api.ftrScanGetFrame.argtypes = [ctypes.c_void_p, ctypes.POINTER(ctypes.c_ubyte), ctypes.POINTER(ctypes.c_uint)]
        scan_api.ftrScanGetFrame.restype = ctypes.c_int
        
        # Connect device
        device_handle = scan_api.ftrScanOpenDevice()
        if not device_handle:
            print("ERROR: Failed to connect to device")
            return False
        
        print(f"Device connected! Handle: {device_handle}")
        
        # Use larger buffer for better capture
        img_width = 320
        img_height = 480
        buffer_size = img_width * img_height
        print(f"Using buffer size: {buffer_size} bytes")
        
        print("\n=== FINGERPRINT CAPTURE INSTRUCTIONS ===")
        print("1. CLEAN the scanner surface with a soft cloth")
        print("2. CLEAN your finger (dry, no lotion)")
        print("3. PRESS finger VERY FIRMLY on scanner")
        print("4. COVER the entire scanner area")
        print("5. HOLD COMPLETELY STILL during capture")
        print("6. Use your THUMB for best results")
        
        input("\nPress Enter when scanner is clean and finger is ready...")
        
        # Multiple attempts with validation
        max_attempts = 15
        
        for attempt in range(1, max_attempts + 1):
            print(f"\n--- ATTEMPT {attempt}/{max_attempts} ---")
            print("Place finger VERY FIRMLY on scanner...")
            print("Press down hard and hold still...")
            
            # Wait for finger placement
            time.sleep(2)
            
            # Prepare buffer
            buffer = (ctypes.c_ubyte * buffer_size)()
            buffer_size_var = ctypes.c_uint(buffer_size)
            
            print("CAPTURING...")
            
            try:
                result = scan_api.ftrScanGetFrame(device_handle, buffer, ctypes.byref(buffer_size_var))
                actual_size = buffer_size_var.value
                
                print(f"Capture result: {result}, Data size: {actual_size}")
                
                if result == 0:  # Success
                    # Convert to bytes and analyze
                    fingerprint_data = bytes(buffer[:actual_size])
                    
                    # Analyze the data
                    is_real, analysis = analyze_fingerprint_data(fingerprint_data)
                    
                    if is_real:
                        print(f"SUCCESS! {analysis}")
                        
                        # Save real fingerprint data
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        filename = f"real_fingerprint_{timestamp}.txt"
                        
                        fingerprint_b64 = base64.b64encode(fingerprint_data).decode()
                        
                        with open(filename, 'w') as f:
                            f.write(f"REAL Fingerprint Capture - VALIDATED\n")
                            f.write(f"Timestamp: {datetime.now().isoformat()}\n")
                            f.write(f"Image Size: {img_width}x{img_height}\n")
                            f.write(f"Data Size: {actual_size} bytes\n")
                            f.write(f"Analysis: {analysis}\n")
                            f.write(f"Base64 Data:\n{fingerprint_b64}\n")
                        
                        print(f"REAL fingerprint saved to: {filename}")
                        print("SUCCESS: ACTUAL FINGERPRINT CAPTURED!")
                        
                        scan_api.ftrScanCloseDevice(device_handle)
                        return True
                    else:
                        print(f"EMPTY SCAN: {analysis}")
                        print("Try pressing finger more firmly...")
                
                elif result == 1:
                    print("ERROR: No finger detected")
                    print("Press finger MORE FIRMLY on scanner")
                elif result == 2:
                    print("ERROR: Finger moved during capture")
                    print("Keep finger COMPLETELY STILL")
                else:
                    print(f"ERROR: Unknown error code {result}")
                
                if attempt < max_attempts:
                    print("Adjusting finger position for next attempt...")
                    time.sleep(1)
                    
            except Exception as e:
                print(f"CRASH on attempt {attempt}: {e}")
                break
        
        print(f"\nFAILED: All {max_attempts} attempts failed to capture real fingerprint")
        print("\nTROUBLESHOoting TIPS:")
        print("1. Clean scanner surface thoroughly")
        print("2. Use a different finger (try thumb)")
        print("3. Press MUCH harder on scanner")
        print("4. Make sure finger is dry (no moisture/lotion)")
        print("5. Try in better lighting")
        print("6. Check if scanner needs calibration")
        
        scan_api.ftrScanCloseDevice(device_handle)
        return False
        
    except Exception as e:
        print(f"FATAL ERROR: {e}")
        return False

if __name__ == '__main__':
    try:
        print("ENHANCED REAL FINGERPRINT CAPTURE TEST")
        print("This validates actual fingerprint content (not just zeros)")
        print("Will analyze captured data to confirm real fingerprint")
        print()
        
        success = enhanced_fingerprint_capture()
        
        if success:
            print("\n=== FINAL RESULT: SUCCESS ===")
            print("REAL fingerprint captured and validated!")
            print("Your GoID system can now use actual fingerprint data!")
        else:
            print("\n=== FINAL RESULT: FAILED ===")
            print("Could not capture real fingerprint data")
            print("May need hardware troubleshooting or different approach")
        
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"\nTest error: {e}")
    finally:
        print("\nTest completed")
        input("Press Enter to exit...")
