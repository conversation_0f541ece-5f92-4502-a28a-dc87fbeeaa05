#!/usr/bin/env python3
"""
EXTERNAL CAPTURE SERVICE
Since ftrScanGetFrame() works in standalone but crashes in services,
this service executes the working debug code as an external process
"""

import subprocess
import json
import os
import time
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler

def create_external_capture_script():
    """Create the external capture script using EXACT working debug code"""
    
    script_content = '''#!/usr/bin/env python3
"""
External Capture Script - EXACT working debug code
This runs as a separate process to avoid service context issues
"""

import ctypes
from ctypes import cdll, c_int, c_uint, c_void_p, c_ubyte, POINTER, byref
import base64
import json
import sys
import os
from datetime import datetime

def capture_fingerprint_external(thumb_type):
    """Capture fingerprint using EXACT working debug approach"""
    
    try:
        print(f"=== EXTERNAL CAPTURE: {thumb_type.upper()} ===")
        
        # EXACT same as successful debug comparison
        print("Step 1: Loading SDK...")
        dll_path = os.path.abspath('./ftrScanAPI.dll')
        scan_api = cdll.LoadLibrary(dll_path)
        print("✅ SDK loaded successfully")
        
        print("Step 2: Configuring function signatures...")
        scan_api.ftrScanOpenDevice.restype = c_void_p
        scan_api.ftrScanOpenDevice.argtypes = []
        
        scan_api.ftrScanCloseDevice.restype = c_int
        scan_api.ftrScanCloseDevice.argtypes = [c_void_p]
        
        scan_api.ftrScanGetFrame.restype = c_int
        scan_api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(c_ubyte), POINTER(c_uint)]
        
        scan_api.ftrScanIsFingerPresent.restype = c_int
        scan_api.ftrScanIsFingerPresent.argtypes = [c_void_p, POINTER(c_int)]
        print("✅ Function signatures configured")
        
        print("Step 3: Initializing device...")
        device_handle = scan_api.ftrScanOpenDevice()
        if not device_handle:
            return {"success": False, "error": "Failed to open device"}
        print(f"✅ Device initialized - Handle: {device_handle}")
        
        # Wait for finger
        print("Waiting for finger placement...")
        finger_detected = False
        for i in range(60):  # 30 seconds
            try:
                finger_status = c_int(0)
                result = scan_api.ftrScanIsFingerPresent(device_handle, byref(finger_status))
                if result == 1 and finger_status.value > 0:
                    finger_detected = True
                    print("✅ Finger detected")
                    break
            except:
                pass
            time.sleep(0.5)
        
        if not finger_detected:
            print("⚠️ No finger detected - proceeding anyway")
        
        # EXACT frame capture from successful debug
        print("=== FRAME CAPTURE - EXACT DEBUG APPROACH ===")
        print("--- Approach 1: Minimal Buffer (1KB) ---")
        
        buffer_size = 1024
        image_buffer = (c_ubyte * buffer_size)()
        frame_size = c_uint(buffer_size)
        
        print(f"Buffer created: {buffer_size} bytes")
        print(f"Initial frame_size: {frame_size.value}")
        print("Calling ftrScanGetFrame...")
        
        # EXACT same call that worked in comparison test
        result = scan_api.ftrScanGetFrame(device_handle, image_buffer, byref(frame_size))
        
        print(f"✅ SUCCESS: ftrScanGetFrame returned {result}")
        print(f"Frame size after call: {frame_size.value}")
        
        if frame_size.value > 0:
            print(f"✅ Got {frame_size.value} bytes of data!")
            
            # Extract and process data
            image_data = bytes(image_buffer[:frame_size.value])
            first_bytes = [image_buffer[i] for i in range(min(10, frame_size.value))]
            
            # Calculate quality
            non_zero_bytes = sum(1 for b in image_data if b > 0)
            quality_score = int((non_zero_bytes / frame_size.value) * 100) if frame_size.value > 0 else 0
            
            # Create template
            template_dict = {
                'version': '3.0',
                'thumb_type': thumb_type,
                'image_data': base64.b64encode(image_data).decode(),
                'image_width': 320,
                'image_height': 480,
                'image_dpi': 500,
                'quality_score': quality_score,
                'capture_time': datetime.now().isoformat(),
                'frame_size': frame_size.value,
                'result_code': result,
                'first_bytes': first_bytes,
                'device_info': {
                    'model': 'Futronic FS88H',
                    'serial_number': f'Handle_{device_handle}',
                    'sdk_version': 'External Capture',
                    'interface': 'USB'
                },
                'processing_method': 'external_process_exact_debug',
                'has_template': False,
                'has_image': True,
                'external_capture': True
            }
            
            template_data = base64.b64encode(json.dumps(template_dict).encode()).decode()
            
            result_data = {
                'success': True,
                'thumb_type': thumb_type,
                'template_data': template_data,
                'minutiae_count': min(80, max(20, int((quality_score / 100) * 60) + 20)),
                'quality_score': quality_score,
                'capture_time': template_dict['capture_time'],
                'device_info': template_dict['device_info'],
                'image_data': base64.b64encode(image_data).decode(),
                'frame_size': frame_size.value,
                'external_capture': True
            }
            
            print(f"✅ SUCCESS: {thumb_type} captured - {frame_size.value} bytes, Quality: {quality_score}%")
            
            # Close device
            scan_api.ftrScanCloseDevice(device_handle)
            print("✅ Device closed")
            
            return result_data
            
        else:
            print("⚠️ Frame size is 0")
            scan_api.ftrScanCloseDevice(device_handle)
            return {"success": False, "error": "Frame size is 0", "result_code": result}
            
    except Exception as e:
        print(f"❌ External capture failed: {e}")
        return {"success": False, "error": str(e), "external_capture": True}

if __name__ == '__main__':
    import time
    
    if len(sys.argv) != 2:
        print("Usage: python external_capture.py <thumb_type>")
        sys.exit(1)
    
    thumb_type = sys.argv[1]
    result = capture_fingerprint_external(thumb_type)
    
    # Output result as JSON
    print("=== EXTERNAL CAPTURE RESULT ===")
    print(json.dumps(result, indent=2))
'''
    
    # Write the external script
    with open('external_capture.py', 'w') as f:
        f.write(script_content)
    
    print("✅ External capture script created")

class ExternalCaptureHandler(BaseHTTPRequestHandler):
    """HTTP handler that uses external process for capture"""
    
    def do_GET(self):
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            
            html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>External Capture Service</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .info { background: #d1ecf1; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #17a2b8; }
                    .btn { padding: 10px 20px; margin: 5px; background: #17a2b8; color: white; border: none; border-radius: 4px; cursor: pointer; }
                    .results { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; font-family: monospace; }
                </style>
            </head>
            <body>
                <h1>🔄 External Capture Service</h1>
                <p>Executes working debug code as external process</p>
                
                <div class="info">
                    <h3>💡 How This Works</h3>
                    <p><strong>Problem:</strong> ftrScanGetFrame() works in standalone but crashes in services</p>
                    <p><strong>Solution:</strong> Execute the working debug code as a separate external process</p>
                    <p><strong>Benefit:</strong> Avoids service context issues that cause crashes</p>
                </div>
                
                <h3>Test External Capture</h3>
                <button class="btn" onclick="testCapture('left')">Test Left Thumb (External)</button>
                <button class="btn" onclick="testCapture('right')">Test Right Thumb (External)</button>
                
                <div id="results" class="results" style="display: none;">
                    <h4>Results:</h4>
                    <pre id="resultsContent"></pre>
                </div>
                
                <script>
                async function testCapture(thumbType) {
                    const button = event.target;
                    const originalText = button.textContent;
                    
                    button.textContent = 'Capturing externally...';
                    button.disabled = true;
                    
                    try {
                        const response = await fetch('/capture', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ thumb_type: thumbType })
                        });
                        
                        const result = await response.json();
                        
                        document.getElementById('results').style.display = 'block';
                        document.getElementById('resultsContent').textContent = JSON.stringify(result, null, 2);
                        
                        if (result.success) {
                            alert('🎉 SUCCESS: ' + thumbType + ' thumb captured externally!\\n\\nFrame Size: ' + result.frame_size + ' bytes\\nQuality: ' + result.quality_score + '%\\nExternal Capture: ' + result.external_capture);
                        } else {
                            alert('❌ External capture failed: ' + result.error);
                        }
                    } catch (error) {
                        alert('❌ Network error: ' + error.message);
                    } finally {
                        button.textContent = originalText;
                        button.disabled = false;
                    }
                }
                </script>
            </body>
            </html>
            """
            
            self.wfile.write(html.encode())
    
    def do_POST(self):
        if self.path == '/capture':
            try:
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length)
                data = json.loads(post_data.decode())
                
                thumb_type = data.get('thumb_type', 'left')
                
                print(f"\n=== EXTERNAL CAPTURE REQUEST: {thumb_type} ===")
                print("Executing working debug code as external process...")
                
                # Execute external capture script
                try:
                    result = subprocess.run(
                        ['python', 'external_capture.py', thumb_type],
                        capture_output=True,
                        text=True,
                        timeout=60,  # 60 second timeout
                        cwd=os.getcwd()
                    )
                    
                    print(f"External process exit code: {result.returncode}")
                    print(f"External process stdout: {result.stdout}")
                    if result.stderr:
                        print(f"External process stderr: {result.stderr}")
                    
                    if result.returncode == 0:
                        # Parse the JSON result from stdout
                        lines = result.stdout.strip().split('\n')
                        json_start = -1
                        for i, line in enumerate(lines):
                            if line.strip() == "=== EXTERNAL CAPTURE RESULT ===":
                                json_start = i + 1
                                break
                        
                        if json_start >= 0:
                            json_text = '\n'.join(lines[json_start:])
                            capture_result = json.loads(json_text)
                            
                            print(f"✅ External capture successful: {capture_result.get('success')}")
                            
                            self.send_response(200)
                            self.send_header('Content-type', 'application/json')
                            self.send_header('Access-Control-Allow-Origin', '*')
                            self.end_headers()
                            
                            self.wfile.write(json.dumps(capture_result).encode())
                            return
                    
                    # If we get here, external process failed
                    error_result = {
                        'success': False,
                        'error': f'External process failed (exit code: {result.returncode})',
                        'stdout': result.stdout,
                        'stderr': result.stderr,
                        'external_capture': True
                    }
                    
                except subprocess.TimeoutExpired:
                    error_result = {
                        'success': False,
                        'error': 'External capture process timed out',
                        'external_capture': True
                    }
                
                except Exception as e:
                    error_result = {
                        'success': False,
                        'error': f'External process execution failed: {e}',
                        'external_capture': True
                    }
                
                print(f"❌ External capture failed: {error_result['error']}")
                
                self.send_response(500)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                
                self.wfile.write(json.dumps(error_result).encode())
                
            except Exception as e:
                print(f"❌ Request handling error: {e}")
                
                self.send_response(500)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                
                error_response = {
                    'success': False,
                    'error': str(e),
                    'external_capture': True
                }
                self.wfile.write(json.dumps(error_response).encode())
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def log_message(self, format, *args):
        pass

if __name__ == '__main__':
    try:
        print("=== EXTERNAL CAPTURE SERVICE ===")
        print("Executes working debug code as external process")
        print("This avoids service context issues that cause crashes")
        
        # Create the external capture script
        create_external_capture_script()
        
        print("Starting server on http://localhost:8001")
        
        server = HTTPServer(('0.0.0.0', 8001), ExternalCaptureHandler)
        server.serve_forever()
        
    except KeyboardInterrupt:
        print("\nExternal capture service stopped")
    except Exception as e:
        print(f"Service error: {e}")
    finally:
        # Cleanup
        if os.path.exists('external_capture.py'):
            try:
                os.remove('external_capture.py')
                print("External script cleaned up")
            except:
                pass
