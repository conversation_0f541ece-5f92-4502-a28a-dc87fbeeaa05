#!/usr/bin/env python3
"""
Simple test script to verify the transfer endpoint is working.
"""
import requests
import json

# Test data
url = "http://************:8000/api/tenants/citizen-transfer-request/"
headers = {
    "Content-Type": "application/json",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.invalid_signature_for_testing"
}

data = {
    "citizen_id": 8,
    "citizen_name": "Rosalyn Good",
    "citizen_digital_id": "1CE82EC6",
    "destination_kebele": 46,
    "transfer_reason": "relocation",
    "reason_description": "Test transfer request"
}

print("Testing transfer endpoint...")
print(f"URL: {url}")
print(f"Data: {json.dumps(data, indent=2)}")

try:
    response = requests.post(url, headers=headers, json=data, timeout=10)
    print(f"\nResponse Status: {response.status_code}")
    print(f"Response Headers: {dict(response.headers)}")
    print(f"Response Body: {response.text}")
    
    if response.status_code == 200:
        print("✅ SUCCESS: Transfer request endpoint is working!")
    else:
        print(f"❌ FAILED: Got status {response.status_code}")
        
except requests.exceptions.RequestException as e:
    print(f"❌ REQUEST ERROR: {e}")
except Exception as e:
    print(f"❌ UNEXPECTED ERROR: {e}")
