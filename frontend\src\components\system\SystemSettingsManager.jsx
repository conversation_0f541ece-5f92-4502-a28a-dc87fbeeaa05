import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardHeader,
  CardContent,
  Grid,
  Switch,
  FormControlLabel,
  Button,
  CircularProgress,
  Alert,
  Chip,
  IconButton,
  Tooltip,
  Divider,
} from '@mui/material';
import {
  Palette as PaletteIcon,
  Public as PublicIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  Save as SaveIcon,
} from '@mui/icons-material';
import axios from '../../utils/axios';
import { useAuth } from '../../contexts/AuthContext';

const SystemSettingsManager = () => {
  const { user } = useAuth();
  const [settings, setSettings] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Load system settings
  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/tenants/system-settings/');
      setSettings(response.data);
    } catch (err) {
      console.error('Error fetching system settings:', err);
      setError('Failed to load system settings');
    } finally {
      setLoading(false);
    }
  };

  const updateTheme = async (colorType, color) => {
    try {
      setSaving(true);
      await axios.patch('/api/tenants/system-settings/update_theme/', {
        [colorType]: color
      });
      
      // Update local state
      setSettings(prev => ({
        ...prev,
        [colorType]: color
      }));
      
      setSuccess('Theme updated successfully!');
      setTimeout(() => setSuccess(''), 3000);
    } catch (err) {
      console.error('Error updating theme:', err);
      
      if (err.response?.status === 403) {
        setError('Access denied: Only public tenant superadmins can update system settings');
      } else {
        setError('Failed to update theme');
      }
      setTimeout(() => setError(''), 5000);
    } finally {
      setSaving(false);
    }
  };

  const updatePublicAccess = async (enabled) => {
    try {
      setSaving(true);
      await axios.patch('/api/tenants/system-settings/update_public_access/', {
        public_services_enabled: enabled
      });
      
      // Update local state
      setSettings(prev => ({
        ...prev,
        public_services_enabled: enabled
      }));
      
      setSuccess('Public access settings updated successfully!');
      setTimeout(() => setSuccess(''), 3000);
    } catch (err) {
      console.error('Error updating public access:', err);
      
      if (err.response?.status === 403) {
        setError('Access denied: Only public tenant superadmins can update system settings');
      } else {
        setError('Failed to update public access settings');
      }
      setTimeout(() => setError(''), 5000);
    } finally {
      setSaving(false);
    }
  };

  const handleColorChange = (colorType, color) => {
    updateTheme(colorType, color);
  };

  const handlePublicAccessToggle = (enabled) => {
    updatePublicAccess(enabled);
  };

  // Check if user is public tenant superadmin
  if (!user?.is_superuser && user?.role !== 'superadmin') {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          <Typography variant="h6" gutterBottom>
            Access Denied
          </Typography>
          <Typography>
            Only superadmins can access the System Settings Manager. 
            You need superadmin privileges to manage global system settings.
          </Typography>
        </Alert>
      </Box>
    );
  }

  // Additional check for public tenant
  if (user?.tenant && user.tenant.type !== 'public') {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          <Typography variant="h6" gutterBottom>
            Access Denied
          </Typography>
          <Typography>
            Only superadmins from the public tenant can manage global system settings.
            You are currently in a {user.tenant.type} tenant.
          </Typography>
        </Alert>
      </Box>
    );
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!settings) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Failed to load system settings. Please try refreshing the page.
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <SettingsIcon sx={{ mr: 2, fontSize: 32, color: 'primary.main' }} />
        <Typography variant="h4" component="h1">
          Global System Settings
        </Typography>
        <Tooltip title="Refresh Settings">
          <IconButton onClick={fetchSettings} sx={{ ml: 2 }}>
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </Box>

      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Manage global system-wide settings that apply to all tenants and public services.
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Theme Settings Card */}
        <Grid item xs={12} md={6}>
          <Card 
            sx={{ 
              height: '100%',
              background: `linear-gradient(135deg, ${settings.primary_color}15 0%, ${settings.secondary_color}15 100%)`,
              border: `2px solid ${settings.primary_color}30`
            }}
          >
            <CardHeader
              avatar={<PaletteIcon sx={{ color: settings.primary_color }} />}
              title="Global Theme Colors"
              subheader="System-wide color scheme"
              action={
                saving && (
                  <CircularProgress size={20} />
                )
              }
            />
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>
                    Primary Color
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <input
                      type="color"
                      value={settings.primary_color}
                      onChange={(e) => handleColorChange('primary_color', e.target.value)}
                      style={{
                        width: 60,
                        height: 40,
                        border: 'none',
                        borderRadius: 8,
                        cursor: 'pointer'
                      }}
                    />
                    <Chip 
                      label={settings.primary_color}
                      sx={{ 
                        backgroundColor: settings.primary_color,
                        color: 'white',
                        fontWeight: 'bold'
                      }}
                    />
                  </Box>
                </Grid>
                
                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>
                    Secondary Color
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <input
                      type="color"
                      value={settings.secondary_color}
                      onChange={(e) => handleColorChange('secondary_color', e.target.value)}
                      style={{
                        width: 60,
                        height: 40,
                        border: 'none',
                        borderRadius: 8,
                        cursor: 'pointer'
                      }}
                    />
                    <Chip 
                      label={settings.secondary_color}
                      sx={{ 
                        backgroundColor: settings.secondary_color,
                        color: 'white',
                        fontWeight: 'bold'
                      }}
                    />
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Public Access Settings Card */}
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%' }}>
            <CardHeader
              avatar={<PublicIcon sx={{ color: settings.public_services_enabled ? settings.primary_color : 'text.secondary' }} />}
              title="Public Services Access"
              subheader="Global public access control"
            />
            <CardContent>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.public_services_enabled || false}
                    onChange={(e) => handlePublicAccessToggle(e.target.checked)}
                    sx={{
                      '& .MuiSwitch-switchBase.Mui-checked': {
                        color: settings.primary_color,
                      },
                      '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                        backgroundColor: settings.primary_color,
                      },
                    }}
                  />
                }
                label="Enable Public ID Card Services"
              />
              
              <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                When enabled, citizens can access ID card application services through the public portal.
                When disabled, the "Public Services" button will be hidden from the landing page.
              </Typography>
              
              <Divider sx={{ my: 2 }} />
              
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  Status:
                </Typography>
                <Chip 
                  label={settings.public_services_enabled ? 'Public Services Available' : 'Public Services Disabled'}
                  color={settings.public_services_enabled ? 'success' : 'default'}
                  size="small"
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Box sx={{ mt: 3, p: 2, backgroundColor: 'background.paper', borderRadius: 2 }}>
        <Typography variant="body2" color="text.secondary">
          <strong>Note:</strong> These settings apply globally to the entire system. 
          Theme colors will be used across all tenant interfaces, and public access controls 
          whether citizens can access ID card services system-wide.
        </Typography>
      </Box>
    </Box>
  );
};

export default SystemSettingsManager;
