#!/bin/bash

# Debug Backend Issues Script
# This script helps diagnose backend connectivity and error issues

echo "🔧 Debugging Backend Issues"
echo "=========================="

# Step 1: Check Docker containers status
echo "📍 Step 1: Checking Docker containers status..."
echo "Container Status:"
docker-compose ps

echo ""
echo "📍 Step 2: Checking backend logs..."
echo "Recent Backend Logs:"
docker-compose logs --tail=50 backend

echo ""
echo "📍 Step 3: Checking database connection..."
echo "Database Status:"
docker-compose logs --tail=20 db

echo ""
echo "📍 Step 4: Testing backend connectivity..."
echo "Testing backend health:"

# Test if backend is responding at all
echo "Testing basic connectivity..."
curl -v http://************:8000/ 2>&1 | head -20

echo ""
echo "Testing admin endpoint..."
curl -v http://************:8000/admin/ 2>&1 | head -10

echo ""
echo "📍 Step 5: Testing specific endpoints..."

# Test auth endpoint
echo "Testing auth endpoint..."
curl -X POST http://************:8000/api/auth/token/ \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}' \
  -v 2>&1 | head -20

echo ""
echo "Testing tenants endpoint..."
curl -v http://************:8000/api/tenants/ 2>&1 | head -10

echo ""
echo "📍 Step 6: Checking Django configuration..."
echo "Running Django checks..."
docker-compose exec backend python manage.py check

echo ""
echo "📍 Step 7: Testing database connection..."
echo "Testing database connectivity..."
docker-compose exec backend python manage.py shell -c "
from django.db import connection
try:
    cursor = connection.cursor()
    cursor.execute('SELECT 1')
    print('✅ Database connection successful')
except Exception as e:
    print(f'❌ Database connection failed: {e}')
"

echo ""
echo "📍 Step 8: Checking migrations..."
echo "Checking migration status..."
docker-compose exec backend python manage.py showmigrations

echo ""
echo "📍 Step 9: Restart backend and test..."
echo "Restarting backend service..."
docker-compose restart backend

echo "Waiting for backend to restart..."
sleep 30

echo "Testing after restart..."
curl -I http://************:8000/admin/ 2>&1

echo ""
echo "✅ Backend debugging completed!"
echo ""
echo "🔍 Common Issues and Solutions:"
echo "1. If containers are not running:"
echo "   docker-compose up -d"
echo ""
echo "2. If database connection fails:"
echo "   docker-compose restart db"
echo "   docker-compose exec backend python manage.py migrate"
echo ""
echo "3. If migrations are missing:"
echo "   docker-compose exec backend python manage.py makemigrations"
echo "   docker-compose exec backend python manage.py migrate"
echo ""
echo "4. If still getting 500 errors:"
echo "   docker-compose exec backend python manage.py collectstatic --noinput"
echo "   docker-compose restart backend"
echo ""
echo "5. If ERR_EMPTY_RESPONSE persists:"
echo "   Check if backend is binding to correct IP/port"
echo "   Verify firewall/network settings"
echo "   Check Docker network configuration"
echo ""
echo "💡 Next steps:"
echo "1. Check the logs above for specific error messages"
echo "2. Run: docker-compose logs backend | grep ERROR"
echo "3. If database issues: docker-compose exec backend python manage.py migrate"
echo "4. If still failing: docker-compose down && docker-compose up -d"
