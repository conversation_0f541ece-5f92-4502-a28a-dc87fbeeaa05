import React from 'react';
import {
  <PERSON><PERSON>,
  AlertTitle,
  Box,
  Typography,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Paper
} from '@mui/material';
import {
  Warning as WarningIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  Security as SecurityIcon,
  Person as PersonIcon,
  Fingerprint as FingerprintIcon,
  Pattern as PatternIcon,
  CreditCard as DigitalIdIcon
} from '@mui/icons-material';

const FraudDetectionAlert = ({ fraudResults, onClose }) => {
  if (!fraudResults || fraudResults.risk_level === 'low') {
    return null;
  }

  const getSeverityColor = (riskLevel) => {
    switch (riskLevel) {
      case 'critical':
        return 'error';
      case 'high':
        return 'warning';
      case 'medium':
        return 'info';
      default:
        return 'info';
    }
  };

  const getRiskIcon = (riskLevel) => {
    switch (riskLevel) {
      case 'critical':
        return <ErrorIcon />;
      case 'high':
        return <WarningIcon />;
      case 'medium':
        return <InfoIcon />;
      default:
        return <InfoIcon />;
    }
  };

  const getIndicatorIcon = (indicator) => {
    switch (indicator) {
      case 'digital_id_duplicate':
        return <DigitalIdIcon />;
      case 'personal_info_match':
        return <PersonIcon />;
      case 'biometric_match':
        return <FingerprintIcon />;
      case 'suspicious_pattern':
        return <PatternIcon />;
      default:
        return <SecurityIcon />;
    }
  };

  const getIndicatorLabel = (indicator) => {
    switch (indicator) {
      case 'digital_id_duplicate':
        return 'Digital ID Duplicate';
      case 'personal_info_match':
        return 'Personal Information Match';
      case 'biometric_match':
        return 'Biometric Match';
      case 'suspicious_pattern':
        return 'Suspicious Application Pattern';
      default:
        return indicator.replace('_', ' ').toUpperCase();
    }
  };

  const getRiskLevelText = (riskLevel) => {
    switch (riskLevel) {
      case 'critical':
        return 'CRITICAL FRAUD RISK';
      case 'high':
        return 'HIGH FRAUD RISK';
      case 'medium':
        return 'MEDIUM FRAUD RISK';
      default:
        return 'FRAUD RISK DETECTED';
    }
  };

  return (
    <Paper sx={{ mb: 3, border: '2px solid', borderColor: `${getSeverityColor(fraudResults.risk_level)}.main` }}>
      <Alert 
        severity={getSeverityColor(fraudResults.risk_level)}
        icon={getRiskIcon(fraudResults.risk_level)}
        onClose={onClose}
        sx={{ 
          '& .MuiAlert-message': { width: '100%' },
          borderRadius: 0
        }}
      >
        <AlertTitle sx={{ fontWeight: 'bold', fontSize: '1.2rem' }}>
          🚨 {getRiskLevelText(fraudResults.risk_level)}
        </AlertTitle>
        
        <Typography variant="body1" sx={{ mb: 2 }}>
          Potential fraud indicators have been detected for this application. Please review carefully before proceeding.
        </Typography>

        {/* Risk Level Indicator */}
        <Box sx={{ mb: 2 }}>
          <Chip
            label={`Risk Level: ${fraudResults.risk_level.toUpperCase()}`}
            color={getSeverityColor(fraudResults.risk_level)}
            variant="filled"
            size="small"
            sx={{ fontWeight: 'bold' }}
          />
        </Box>

        {/* Fraud Indicators */}
        {fraudResults.indicators && fraudResults.indicators.length > 0 && (
          <Box sx={{ mb: 2 }}>
            <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
              Detected Indicators:
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {fraudResults.indicators.map((indicator, index) => (
                <Chip
                  key={index}
                  icon={getIndicatorIcon(indicator)}
                  label={getIndicatorLabel(indicator)}
                  size="small"
                  variant="outlined"
                  color={getSeverityColor(fraudResults.risk_level)}
                />
              ))}
            </Box>
          </Box>
        )}

        {/* Recommendations */}
        {fraudResults.recommendations && fraudResults.recommendations.length > 0 && (
          <Box sx={{ mb: 2 }}>
            <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
              Recommended Actions:
            </Typography>
            <List dense>
              {fraudResults.recommendations.map((recommendation, index) => (
                <ListItem key={index} sx={{ py: 0.5 }}>
                  <ListItemIcon sx={{ minWidth: 32 }}>
                    <SecurityIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText 
                    primary={recommendation}
                    primaryTypographyProps={{ fontSize: '0.9rem' }}
                  />
                </ListItem>
              ))}
            </List>
          </Box>
        )}

        {/* Detailed Matches */}
        {fraudResults.matches && fraudResults.matches.length > 0 && (
          <Accordion sx={{ mt: 2 }}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                Detailed Match Information ({fraudResults.matches.length} matches found)
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List>
                {fraudResults.matches.map((match, index) => (
                  <ListItem key={index} divider>
                    <ListItemIcon>
                      {getIndicatorIcon(match.type)}
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box>
                          <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                            {match.citizen_name} - {match.tenant_name}
                          </Typography>
                          <Chip
                            label={`${Math.round(match.match_confidence * 100)}% Match`}
                            size="small"
                            color={match.match_confidence > 0.8 ? 'error' : 'warning'}
                            sx={{ mt: 0.5 }}
                          />
                        </Box>
                      }
                      secondary={
                        <Box sx={{ mt: 1 }}>
                          <Typography variant="body2" color="text.secondary">
                            <strong>Type:</strong> {getIndicatorLabel(match.type)}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            <strong>Details:</strong> {match.details}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            <strong>Citizen ID:</strong> {match.citizen_id}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>
        )}

        {/* Critical Warning */}
        {fraudResults.risk_level === 'critical' && (
          <Box sx={{ 
            mt: 2, 
            p: 2, 
            backgroundColor: 'error.light', 
            borderRadius: 1,
            border: '2px solid',
            borderColor: 'error.main'
          }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', color: 'error.contrastText' }}>
              ⚠️ CRITICAL WARNING: This application has been flagged for potential fraud. 
              Do not proceed without thorough manual verification and supervisor approval.
            </Typography>
          </Box>
        )}
      </Alert>
    </Paper>
  );
};

export default FraudDetectionAlert;
