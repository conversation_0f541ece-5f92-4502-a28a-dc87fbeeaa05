#!/usr/bin/env python3
"""
Debug Service Issues
"""

import sys
import os
import subprocess
from pathlib import Path

def check_service_config():
    """Check service configuration"""
    
    print("🔍 Debugging Service Configuration...")
    print("=" * 50)
    
    # Check NSSM
    nssm_exe = Path(__file__).parent / "nssm" / "nssm.exe"
    if not nssm_exe.exists():
        print("❌ NSSM not found")
        return False
    
    print(f"✅ NSSM found: {nssm_exe}")
    
    # Check service configuration
    service_name = "GoIDJarBridge"
    
    try:
        print(f"\n📋 Service Configuration for '{service_name}':")
        
        # Get service path
        result = subprocess.run([str(nssm_exe), "get", service_name, "Application"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"   Application: {result.stdout.strip()}")
        
        # Get service parameters
        result = subprocess.run([str(nssm_exe), "get", service_name, "AppParameters"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"   Parameters: {result.stdout.strip()}")
        
        # Get working directory
        result = subprocess.run([str(nssm_exe), "get", service_name, "AppDirectory"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"   Working Dir: {result.stdout.strip()}")
        
        # Get log files
        result = subprocess.run([str(nssm_exe), "get", service_name, "AppStdout"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"   Output Log: {result.stdout.strip()}")
        
        result = subprocess.run([str(nssm_exe), "get", service_name, "AppStderr"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"   Error Log: {result.stdout.strip()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking service config: {e}")
        return False

def check_files():
    """Check if required files exist"""
    
    print(f"\n📁 Checking Required Files:")
    
    service_dir = Path(__file__).parent
    python_exe = sys.executable
    jar_bridge_script = service_dir / "working_jar_bridge.py"
    jar_file = service_dir / "fingerPrint" / "GonderFingerPrint.jar"
    
    print(f"   Python: {python_exe} {'✅' if Path(python_exe).exists() else '❌'}")
    print(f"   JAR Bridge: {jar_bridge_script} {'✅' if jar_bridge_script.exists() else '❌'}")
    print(f"   JAR File: {jar_file} {'✅' if jar_file.exists() else '❌'}")
    print(f"   Working Dir: {service_dir}")
    
    return all([
        Path(python_exe).exists(),
        jar_bridge_script.exists(),
        jar_file.exists()
    ])

def check_logs():
    """Check service logs"""
    
    print(f"\n📄 Checking Service Logs:")
    
    log_dir = Path(__file__).parent / "logs"
    
    if not log_dir.exists():
        print("   ❌ Logs directory doesn't exist")
        return False
    
    output_log = log_dir / "service_output.log"
    error_log = log_dir / "service_error.log"
    
    print(f"   Output log: {output_log} {'✅' if output_log.exists() else '❌'}")
    print(f"   Error log: {error_log} {'✅' if error_log.exists() else '❌'}")
    
    # Show recent log content
    if output_log.exists() and output_log.stat().st_size > 0:
        print(f"\n📋 Recent Output Log:")
        try:
            with open(output_log, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                for line in lines[-10:]:  # Last 10 lines
                    print(f"     {line.strip()}")
        except Exception as e:
            print(f"     ❌ Error reading output log: {e}")
    
    if error_log.exists() and error_log.stat().st_size > 0:
        print(f"\n🚨 Recent Error Log:")
        try:
            with open(error_log, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                for line in lines[-10:]:  # Last 10 lines
                    print(f"     {line.strip()}")
        except Exception as e:
            print(f"     ❌ Error reading error log: {e}")
    
    return True

def test_manual_start():
    """Test starting the JAR bridge manually"""
    
    print(f"\n🧪 Testing Manual Start:")
    
    service_dir = Path(__file__).parent
    jar_bridge_script = service_dir / "working_jar_bridge.py"
    
    if not jar_bridge_script.exists():
        print("   ❌ JAR bridge script not found")
        return False
    
    print(f"   Script: {jar_bridge_script}")
    print(f"   Command: python {jar_bridge_script}")
    print(f"   💡 Try running this manually to see if it works")
    
    return True

def fix_service_config():
    """Try to fix common service configuration issues"""
    
    print(f"\n🔧 Attempting to Fix Service Configuration:")
    
    nssm_exe = Path(__file__).parent / "nssm" / "nssm.exe"
    service_name = "GoIDJarBridge"
    service_dir = Path(__file__).parent
    python_exe = sys.executable
    jar_bridge_script = service_dir / "working_jar_bridge.py"
    
    try:
        # Ensure log directory exists
        log_dir = service_dir / "logs"
        log_dir.mkdir(exist_ok=True)
        
        # Update service configuration
        print("   🔄 Updating service configuration...")
        
        # Set application path
        subprocess.run([str(nssm_exe), "set", service_name, "Application", python_exe])
        
        # Set parameters
        subprocess.run([str(nssm_exe), "set", service_name, "AppParameters", str(jar_bridge_script)])
        
        # Set working directory
        subprocess.run([str(nssm_exe), "set", service_name, "AppDirectory", str(service_dir)])
        
        # Set log files
        subprocess.run([str(nssm_exe), "set", service_name, "AppStdout", str(log_dir / "service_output.log")])
        subprocess.run([str(nssm_exe), "set", service_name, "AppStderr", str(log_dir / "service_error.log")])
        
        # Set restart behavior
        subprocess.run([str(nssm_exe), "set", service_name, "AppRestartDelay", "30000"])
        
        print("   ✅ Service configuration updated")
        return True
        
    except Exception as e:
        print(f"   ❌ Error fixing service config: {e}")
        return False

def main():
    """Main debug function"""
    
    print("GoID JAR Bridge Service Debugger")
    print("=" * 40)
    
    # Check service configuration
    check_service_config()
    
    # Check required files
    files_ok = check_files()
    
    # Check logs
    check_logs()
    
    # Test manual start
    test_manual_start()
    
    # Offer to fix configuration
    if not files_ok:
        print(f"\n❌ Some required files are missing!")
        print(f"💡 Make sure all files are in the correct location")
    else:
        print(f"\n🔧 Would you like to try fixing the service configuration?")
        response = input("Enter 'y' to fix, or any other key to skip: ")
        
        if response.lower() == 'y':
            if fix_service_config():
                print(f"\n✅ Configuration fixed! Try starting the service:")
                print(f"   python simple_service_installer.py start")
                print(f"   python simple_service_installer.py status")
            else:
                print(f"\n❌ Could not fix configuration automatically")
    
    print(f"\n💡 Next Steps:")
    print(f"1. Test manually: python working_jar_bridge.py")
    print(f"2. Check logs in: logs/")
    print(f"3. Try restarting: python simple_service_installer.py restart")
    print(f"4. If still failing, try reinstalling the service")

if __name__ == '__main__':
    main()
