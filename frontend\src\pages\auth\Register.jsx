import { useState } from 'react';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  TextField,
  Link,
  Grid,
  Typography,
  Alert,
  CircularProgress,
  InputAdornment,
  IconButton,
  FormControl,
  OutlinedInput,
} from '@mui/material';
import {
  Email as EmailIcon,
  Lock as LockIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
} from '@mui/icons-material';
import axios from '../../utils/axios';
import { useLocalization } from '../../contexts/LocalizationContext';

const Register = () => {
  const navigate = useNavigate();
  const { t } = useLocalization();
  const [formData, setFormData] = useState({
    email: '',
    username: '',
    password: '',
    confirmPassword: '',
    organizationName: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Basic validation
    if (!formData.email || !formData.username || !formData.password || !formData.confirmPassword) {
      setError(t('please_fill_required_fields', 'Please fill in all required fields'));
      return;
    }
    
    if (formData.password !== formData.confirmPassword) {
      setError(t('passwords_must_match', 'Passwords must match'));
      return;
    }
    
    try {
      setLoading(true);
      setError('');
      
      // In a real implementation, this would call an API endpoint
      // For now, we'll just simulate a successful request
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setSuccess(true);
      
      // Redirect to login after a delay
      setTimeout(() => {
        navigate('/login');
      }, 3000);
    } catch (err) {
      setError(err.response?.data?.detail || t('registration_failed_try_again', 'Registration failed. Please try again.'));
    } finally {
      setLoading(false);
    }
  };

  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };

  return (
    <Box component="form" onSubmit={handleSubmit} noValidate sx={{ width: '100%' }}>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {t('registration_successful', 'Registration successful! You will be redirected to the login page.')}
        </Alert>
      )}

      <Typography variant="body2" sx={{ mb: 1 }}>
        {t('email', 'Email Address')} <Box component="span" sx={{ color: 'error.main' }}>*</Box>
      </Typography>
      <TextField
        fullWidth
        id="email"
        name="email"
        autoComplete="email"
        value={formData.email}
        onChange={handleChange}
        placeholder={t('enter_your_email', 'Enter your email')}
        sx={{ mb: 3 }}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <EmailIcon color="action" />
            </InputAdornment>
          ),
        }}
      />

      <Typography variant="body2" sx={{ mb: 1 }}>
        {t('username', 'Username')} <Box component="span" sx={{ color: 'error.main' }}>*</Box>
      </Typography>
      <TextField
        fullWidth
        id="username"
        name="username"
        autoComplete="username"
        value={formData.username}
        onChange={handleChange}
        placeholder={t('choose_username', 'Choose a username')}
        sx={{ mb: 3 }}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <PersonIcon color="action" />
            </InputAdornment>
          ),
        }}
      />

      <Typography variant="body2" sx={{ mb: 1 }}>
        {t('organization_name', 'Organization Name')}
      </Typography>
      <TextField
        fullWidth
        id="organizationName"
        name="organizationName"
        value={formData.organizationName}
        onChange={handleChange}
        placeholder={t('organization_name', 'Enter your organization name')}
        sx={{ mb: 3 }}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <BusinessIcon color="action" />
            </InputAdornment>
          ),
        }}
      />

      <Typography variant="body2" sx={{ mb: 1 }}>
        {t('password', 'Password')} <Box component="span" sx={{ color: 'error.main' }}>*</Box>
      </Typography>
      <FormControl fullWidth variant="outlined" sx={{ mb: 3 }}>
        <OutlinedInput
          id="password"
          name="password"
          type={showPassword ? 'text' : 'password'}
          value={formData.password}
          onChange={handleChange}
          placeholder={t('create_password', 'Create a password')}
          startAdornment={
            <InputAdornment position="start">
              <LockIcon color="action" />
            </InputAdornment>
          }
          endAdornment={
            <InputAdornment position="end">
              <IconButton
                aria-label="toggle password visibility"
                onClick={handleClickShowPassword}
                edge="end"
              >
                {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
              </IconButton>
            </InputAdornment>
          }
        />
      </FormControl>

      <Typography variant="body2" sx={{ mb: 1 }}>
        {t('confirm_password', 'Confirm Password')} <Box component="span" sx={{ color: 'error.main' }}>*</Box>
      </Typography>
      <FormControl fullWidth variant="outlined" sx={{ mb: 3 }}>
        <OutlinedInput
          id="confirmPassword"
          name="confirmPassword"
          type={showPassword ? 'text' : 'password'}
          value={formData.confirmPassword}
          onChange={handleChange}
          placeholder={t('confirm_your_password', 'Confirm your password')}
          startAdornment={
            <InputAdornment position="start">
              <LockIcon color="action" />
            </InputAdornment>
          }
        />
      </FormControl>

      <Button
        type="submit"
        fullWidth
        variant="contained"
        sx={{ 
          mt: 1, 
          mb: 3, 
          py: 1.5,
          backgroundColor: '#3f51b5',
          '&:hover': {
            backgroundColor: '#303f9f',
          }
        }}
        disabled={loading || success}
      >
        {loading ? <CircularProgress size={24} /> : t('register', 'Register')}
      </Button>

      <Box sx={{ textAlign: 'center' }}>
        <Typography variant="body2" color="text.secondary">
          {t('already_have_account', 'Already have an account?')} <Link component={RouterLink} to="/login" sx={{ fontWeight: 'bold' }}>{t('sign_in', 'Sign In')}</Link>
        </Typography>
      </Box>
    </Box>
  );
};

export default Register;
