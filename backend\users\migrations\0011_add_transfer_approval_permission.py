# Generated migration for adding transfer approval permission

from django.db import migrations


def create_transfer_permissions(apps, schema_editor):
    """Create transfer and clearance approval permissions."""
    Permission = apps.get_model('users', 'Permission')
    
    # Create transfer approval permission
    Permission.objects.get_or_create(
        codename='approve_transfer_requests',
        defaults={
            'name': 'Approve Transfer Requests',
            'description': 'Can approve or reject citizen transfer requests from other kebeles',
            'category': 'transfers',
            'is_active': True
        }
    )
    
    # Create clearance approval permission
    Permission.objects.get_or_create(
        codename='approve_clearance_requests',
        defaults={
            'name': 'Approve Clearance Requests',
            'description': 'Can approve or reject citizen clearance requests',
            'category': 'clearances',
            'is_active': True
        }
    )


def remove_transfer_permissions(apps, schema_editor):
    """Remove transfer and clearance approval permissions."""
    Permission = apps.get_model('users', 'Permission')
    
    Permission.objects.filter(codename='approve_transfer_requests').delete()
    Permission.objects.filter(codename='approve_clearance_requests').delete()


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0010_migrate_group_membership_data'),
    ]

    operations = [
        migrations.RunPython(create_transfer_permissions, remove_transfer_permissions),
    ]
