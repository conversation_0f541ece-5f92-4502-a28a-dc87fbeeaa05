# GoID - Digital ID Card System

GoID is a multi-tenant Django-based digital ID card system for managing citizen identification across multiple centers.

## Features

- **Multi-tenant architecture** with hierarchical structure (city -> sub city -> kebele)
- **Unique login/dashboard per tenant**
- **Citizen registration** per kebele
- **Digital ID card generation**, approval workflow, and printing/exporting (PDF)
- **Role-based access control** (clerks, kebele leaders, subcity admins, city admins)
- **Comprehensive reporting and statistics**

## Project Structure

- **Backend**: Django REST Framework API with PostgreSQL database
- **Frontend**: React with Material UI

## Authentication

- JWT-based authentication

## Multi-tenancy

The system uses a schema-based approach for multi-tenancy:

- Each tenant (city, subcity, kebele) has its own schema in the database
- Tenant data is isolated using PostgreSQL schemas
- Parent-child relationships between tenants (city -> subcity -> kebele)
- Common reference data is shared across all tenants

## Getting Started

### Prerequisites

- Docker and Docker Compose

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/goid.git
   cd goid
   ```

2. Start the Docker containers:
   ```
   docker-compose up -d
   ```

3. Initialize the database with sample data:
   ```
   docker-compose exec backend python init_data.py
   ```

   This will create:
   - A superuser (username: admin, password: admin123)
   - Sample tenants (city, subcity, kebele)
   - Sample users for each tenant

4. Access the application:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000/api/
   - API Documentation: http://localhost:8000/swagger/
   - Django Admin: http://localhost:8000/admin/

## Development

### Backend Development

The backend is built with Django and Django REST Framework. The main components are:

- **tenants**: Manages the multi-tenant architecture
- **users**: Custom user model with role-based permissions
- **citizens**: Citizen registration and management
- **idcards**: ID card generation and management
- **workflows**: Approval workflows for ID cards

### Frontend Development

The frontend is built with React and Material UI. The main components are:

- **Authentication**: Login and user management
- **Dashboard**: Overview of system statistics
- **Citizens**: Registration and management of citizens
- **ID Cards**: Generation, approval, and printing of ID cards
- **Administration**: Tenant and user management

## Deployment

For production deployment, make sure to:

1. Update the Django `SECRET_KEY` in the settings
2. Configure proper database credentials
3. Set up proper CORS settings
4. Configure SSL/TLS for secure communication
5. Set up proper backup and monitoring

## License

This project is proprietary and confidential.

## Contact

For any questions or support, please contact [<EMAIL>](mailto:<EMAIL>).
