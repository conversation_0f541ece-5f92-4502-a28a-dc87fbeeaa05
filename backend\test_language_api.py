#!/usr/bin/env python3
import os
import django
import json

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.test import Client
from django.utils import translation

def test_language_api():
    client = Client()
    
    print('Testing language API...')
    
    # Set language to Amharic
    response = client.post('/api/common/language/set/', 
        data=json.dumps({'language': 'am'}), 
        content_type='application/json')
    print(f'Set language response: {response.status_code}')
    if response.status_code == 200:
        print(f'Response data: {response.json()}')
    else:
        print(f'Error response: {response.content.decode()}')
    
    # Get translations after setting language
    response = client.get('/api/common/translations/')
    print(f'Get translations response: {response.status_code}')
    if response.status_code == 200:
        data = response.json()
        print(f'Language: {data["language"]}')
        print(f'Dashboard: {data["translations"]["dashboard"]}')
        print(f'Citizens: {data["translations"]["citizens"]}')
    
    # Test direct translation activation
    print('\nTesting direct translation activation:')
    translation.activate('am')
    response = client.get('/api/common/translations/')
    if response.status_code == 200:
        data = response.json()
        print(f'Direct activation - Language: {data["language"]}')
        print(f'Direct activation - Dashboard: {data["translations"]["dashboard"]}')

if __name__ == "__main__":
    test_language_api()
