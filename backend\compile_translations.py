#!/usr/bin/env python3
"""
Simple script to compile PO files to MO files without requiring gettext tools.
"""

import os
import struct
import re

def compile_po_to_mo(po_file_path, mo_file_path):
    """
    Convert a PO file to MO file format.
    """
    translations = {}
    
    with open(po_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Parse PO file using regex
    po_entries = re.findall(r'msgid\s+"([^"]*)"(?:\n[^m]*)*msgstr\s+"([^"]*)"', content, re.MULTILINE)
    
    for msgid, msgstr in po_entries:
        if msgid and msgstr:  # Skip empty translations
            # Unescape quotes and handle multiline
            msgid = msgid.replace('\\"', '"').replace('\\n', '\n')
            msgstr = msgstr.replace('\\"', '"').replace('\\n', '\n')
            translations[msgid] = msgstr
    
    print(f"Found {len(translations)} translations")
    
    # Create MO file
    keys = list(translations.keys())
    values = [translations[k] for k in keys]
    
    # Calculate offsets
    koffsets = []
    voffsets = []
    kencoded = [k.encode('utf-8') for k in keys]
    vencoded = [v.encode('utf-8') for v in values]
    
    # Build the output
    keystart = 7 * 4 + 16 * len(keys)
    valuestart = keystart + sum(len(k) for k in kencoded)
    
    koffsets = []
    voffsets = []
    
    offset = keystart
    for k in kencoded:
        koffsets.append(offset)
        offset += len(k)
    
    offset = valuestart
    for v in vencoded:
        voffsets.append(offset)
        offset += len(v)
    
    # Write MO file
    with open(mo_file_path, 'wb') as f:
        # Header
        f.write(struct.pack('<I', 0x950412de))  # Magic number
        f.write(struct.pack('<I', 0))           # Version
        f.write(struct.pack('<I', len(keys)))   # Number of entries
        f.write(struct.pack('<I', 7*4))         # Offset of key table
        f.write(struct.pack('<I', 7*4 + 8*len(keys)))  # Offset of value table
        f.write(struct.pack('<I', 0))           # Hash table size
        f.write(struct.pack('<I', 0))           # Offset of hash table
        
        # Key descriptors
        for i in range(len(keys)):
            f.write(struct.pack('<I', len(kencoded[i])))
            f.write(struct.pack('<I', koffsets[i]))
        
        # Value descriptors
        for i in range(len(values)):
            f.write(struct.pack('<I', len(vencoded[i])))
            f.write(struct.pack('<I', voffsets[i]))
        
        # Keys
        for k in kencoded:
            f.write(k)
        
        # Values
        for v in vencoded:
            f.write(v)
    
    print(f"MO file created: {mo_file_path}")
    return len(translations)

if __name__ == "__main__":
    po_file = "/app/locale/am/LC_MESSAGES/django.po"
    mo_file = "/app/locale/am/LC_MESSAGES/django.mo"
    
    if os.path.exists(po_file):
        count = compile_po_to_mo(po_file, mo_file)
        print(f"Successfully compiled {count} translations")
    else:
        print(f"PO file not found: {po_file}")
