import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Grid,
  Alert,
  <PERSON>,
  Divider,
  <PERSON><PERSON>,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  CalendarToday as CalendarIcon,
  Today as TodayIcon,
  Event as EventIcon,
  SwapHoriz as SwapIcon
} from '@mui/icons-material';
import EthiopianDatePickerComponent from '../common/EthiopianDatePickerComponent';
import {
  getCurrentEthiopianDate,
  formatEthiopianDate,
  gregorianToEthiopian
} from '../../utils/ethiopianCalendar';

/**
 * Ethiopian Calendar Demo Component
 *
 * Demonstrates the self-contained Ethiopian calendar widget which provides:
 * - True Ethiopian calendar interface
 * - Ethiopian years (2017 = current)
 * - Ethiopian months (Meskerem, Tikimt, etc.)
 * - Automatic conversion to Gregorian for system storage
 * - No external package dependencies (Docker compatible)
 */
const EtCalendarDemo = () => {
  const [selectedDate, setSelectedDate] = useState(null);
  const [language, setLanguage] = useState('en');
  const [showConversion, setShowConversion] = useState(true);
  
  const currentEthiopian = getCurrentEthiopianDate();
  const today = new Date();
  const todayEthiopian = gregorianToEthiopian(today);

  const handleDateChange = (gregorianDate) => {
    setSelectedDate(gregorianDate);
    
    if (gregorianDate) {
      const ethiopianDate = gregorianToEthiopian(gregorianDate);
      console.log('Et-Calendar Selection:', {
        userInterface: 'Ethiopian Calendar',
        userSees: formatEthiopianDate(ethiopianDate, 'DD MMM YYYY', language),
        systemReceives: gregorianDate.toISOString(),
        ethiopianYear: ethiopianDate.year,
        currentEthiopianYear: currentEthiopian.year,
        isCurrentYear: ethiopianDate.year === currentEthiopian.year
      });
    }
  };

  const setToday = () => {
    setSelectedDate(new Date());
  };

  const getSelectedInfo = () => {
    if (!selectedDate) return null;
    
    const ethiopianDate = gregorianToEthiopian(selectedDate);
    return {
      gregorian: {
        date: selectedDate,
        formatted: selectedDate.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }),
        iso: selectedDate.toISOString().split('T')[0]
      },
      ethiopian: {
        date: ethiopianDate,
        formatted: formatEthiopianDate(ethiopianDate, 'DD MMM YYYY', language),
        formattedShort: formatEthiopianDate(ethiopianDate, 'DD/MM/YYYY', language),
        formattedAmharic: formatEthiopianDate(ethiopianDate, 'DD MMM YYYY', 'am')
      }
    };
  };

  const selectedInfo = getSelectedInfo();

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Ethiopian Calendar Widget Demo
      </Typography>

      <Alert severity="success" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>Self-contained Ethiopian Calendar:</strong> Using custom Ethiopian calendar widget with no external dependencies.
          Users see Ethiopian Year {currentEthiopian.year}, {formatEthiopianDate(currentEthiopian, 'MMM', 'en')} month, etc.
        </Typography>
      </Alert>

      <Grid container spacing={3}>
        {/* Package Information */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                📦 Self-contained Ethiopian Calendar Widget
              </Typography>

              <Typography variant="body2" color="text.secondary" paragraph>
                <strong>Implementation:</strong> EthiopianCalendarWidget<br />
                <strong>Features:</strong> True Ethiopian calendar interface, No external dependencies, Docker compatible<br />
                <strong>Dependencies:</strong> None (self-contained)
              </Typography>

              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', mb: 2 }}>
                <Chip
                  icon={<CalendarIcon />}
                  label={`Today (Ethiopian): ${formatEthiopianDate(todayEthiopian, 'DD MMM YYYY', 'en')}`}
                  color="primary"
                  size="medium"
                />
                <Chip
                  icon={<CalendarIcon />}
                  label={`Today (Gregorian): ${today.toLocaleDateString('en-US')}`}
                  color="secondary"
                  size="medium"
                />
              </Box>

              <Typography variant="body2" color="text.secondary">
                Ethiopian Year: <strong>{todayEthiopian.year}</strong> • 
                Current Month: <strong>{formatEthiopianDate(todayEthiopian, 'MMM', 'en')}</strong> • 
                Day: <strong>{todayEthiopian.day}</strong>
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Controls */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Demo Controls
              </Typography>
              
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'center' }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={language === 'am'}
                      onChange={(e) => setLanguage(e.target.checked ? 'am' : 'en')}
                    />
                  }
                  label={`Language: ${language === 'am' ? 'Amharic' : 'English'}`}
                />
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={showConversion}
                      onChange={(e) => setShowConversion(e.target.checked)}
                    />
                  }
                  label="Show Conversion"
                />

                <Button
                  variant="outlined"
                  startIcon={<TodayIcon />}
                  onClick={setToday}
                  size="small"
                >
                  Set to Today
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Ethiopian Calendar Picker */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <EventIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Ethiopian Calendar Widget
              </Typography>
              
              <EthiopianDatePickerComponent
                label="Select Date using Ethiopian Calendar"
                value={selectedDate}
                onChange={handleDateChange}
                language={language}
                showConversion={showConversion}
                helperText="True Ethiopian calendar interface - Year 2017 = Current"
                placeholder="Click to open Ethiopian calendar"
              />
            </CardContent>
          </Card>
        </Grid>

        {/* Selected Date Information */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Selected Date Information
              </Typography>

              {selectedInfo ? (
                <Box>
                  <Typography variant="subtitle2" color="primary" gutterBottom>
                    Ethiopian Calendar (User Interface):
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 1 }}>
                    {selectedInfo.ethiopian.formatted}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Short: {selectedInfo.ethiopian.formattedShort}<br />
                    Amharic: {selectedInfo.ethiopian.formattedAmharic}
                  </Typography>

                  <Divider sx={{ my: 2 }} />

                  <Typography variant="subtitle2" color="secondary" gutterBottom>
                    Gregorian Calendar (System Storage):
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 1 }}>
                    {selectedInfo.gregorian.formatted}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    ISO: {selectedInfo.gregorian.iso}
                  </Typography>

                  <Divider sx={{ my: 2 }} />

                  <Typography variant="subtitle2" gutterBottom>
                    Package Integration:
                  </Typography>
                  <Box sx={{ p: 1, bgcolor: 'grey.50', borderRadius: 1 }}>
                    <Typography variant="caption" color="text.secondary">
                      <strong>Package:</strong> et-calendar-react<br />
                      <strong>Calendar Type:</strong> Ethiopian (calendarType=true)<br />
                      <strong>User Experience:</strong> Ethiopian Year {selectedInfo.ethiopian.date.year}<br />
                      <strong>System Storage:</strong> {selectedInfo.gregorian.iso}<br />
                      <strong>Language:</strong> {language === 'am' ? 'Amharic' : 'English'}
                    </Typography>
                  </Box>
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  Select a date using the Ethiopian calendar to see the conversion details.
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Package Usage Example */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Package Usage Example
              </Typography>
              
              <Box sx={{ p: 2, bgcolor: 'grey.100', borderRadius: 1, fontFamily: 'monospace' }}>
                <Typography variant="body2" component="pre">
{`import React, { useState } from 'react';
import EtCalendar from 'et-calendar-react';

const MyComponent = () => {
  const [selectedDate, setSelectedDate] = useState(null);

  const handleDateChange = (newDate) => {
    setSelectedDate(newDate);
    // newDate is a Gregorian Date object
    // User saw Ethiopian calendar interface
  };

  return (
    <EtCalendar
      value={selectedDate}
      onChange={handleDateChange}
      calendarType={true} // true for Ethiopian
      lang="${language}" // 'en' or 'am'
      placeholder="Select Ethiopian date"
      fullWidth={true}
    />
  );
};`}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Benefits */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Benefits of Et-Calendar-React Package
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <CalendarIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="subtitle2">True Ethiopian Calendar</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Proper Ethiopian calendar with Year 2017, Meskerem/Tikimt months, 30-day months
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={4}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <SwapIcon color="secondary" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="subtitle2">Automatic Conversion</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Package handles conversion between Ethiopian and Gregorian calendars automatically
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={4}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <EventIcon color="success" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="subtitle2">Easy Integration</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Drop-in replacement for standard date pickers with Ethiopian calendar support
                    </Typography>
                  </Box>
                </Grid>
              </Grid>

              <Alert severity="info" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  <strong>Perfect Solution:</strong> The et-calendar-react package provides exactly what we need - 
                  a true Ethiopian calendar interface where users see Ethiopian Year 2017, Ethiopian months, 
                  and Ethiopian days, while the system receives standard Gregorian Date objects.
                </Typography>
              </Alert>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default EtCalendarDemo;
