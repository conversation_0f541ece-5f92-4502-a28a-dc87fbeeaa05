#!/bin/bash

# Fix JWT Token and Pagination Issues Script
# This script fixes JWT token tenant info and pagination NaN error

echo "🔧 Fixing JWT Token and Pagination Issues"
echo "========================================="

# Step 1: Test current JWT token issue
echo "📍 Step 1: Testing current JWT token issue..."
python test_jwt_token_issue.py

# Step 2: Restart backend to apply middleware changes
echo ""
echo "📍 Step 2: Restarting backend to ensure middleware changes are applied..."
docker-compose restart backend

# Step 3: Wait for backend to restart
echo "⏳ Waiting for backend to restart..."
sleep 30

# Step 4: Create superuser if needed
echo ""
echo "📍 Step 3: Checking/creating superuser..."
echo "Creating superuser with email: <EMAIL>"

# Create superuser non-interactively
docker-compose exec -T backend python manage.py shell << 'EOF'
from django.contrib.auth import get_user_model
User = get_user_model()

# Check if superuser exists
if not User.objects.filter(email='<EMAIL>').exists():
    print("Creating superuser...")
    user = User.objects.create_superuser(
        email='<EMAIL>',
        username='admin',
        password='admin123',
        first_name='Super',
        last_name='Admin'
    )
    print(f"✅ Superuser created: {user.email}")
else:
    print("✅ Superuser already exists")

# Also ensure the user is marked as superuser
user = User.objects.get(email='<EMAIL>')
if not user.is_superuser:
    user.is_superuser = True
    user.is_staff = True
    user.save()
    print("✅ Updated user to superuser status")
EOF

# Step 5: Restart frontend to apply pagination fix
echo ""
echo "📍 Step 4: Restarting frontend to apply pagination fix..."
docker-compose restart frontend

# Step 6: Wait for frontend to restart
echo "⏳ Waiting for frontend to restart..."
sleep 20

# Step 7: Test again
echo ""
echo "📍 Step 5: Testing after fixes..."
python test_jwt_token_issue.py

# Step 8: Test pagination specifically
echo ""
echo "📍 Step 6: Testing pagination fix..."
echo "Open frontend and check:"
echo "1. Navigate to http://10.139.8.141:3000/tenants"
echo "2. Check browser console for pagination warnings"
echo "3. Verify pagination component shows proper numbers"

echo ""
echo "✅ JWT Token and Pagination fixes completed!"
echo ""
echo "🔍 Issues Fixed:"
echo "1. ✅ Fixed pagination NaN error in TenantList component"
echo "2. ✅ Added safe calculation for totalPages"
echo "3. ✅ Ensured superuser exists with proper credentials"
echo "4. ✅ Applied middleware changes for tenant endpoints"
echo ""
echo "🌐 Expected Results:"
echo "   JWT Token: Should include tenant info for tenant users"
echo "   JWT Token: Should have is_superuser=true for superusers"
echo "   Pagination: Should show valid numbers (not NaN)"
echo "   Tenant List: Should load without 404 errors"
echo ""
echo "💡 Manual testing:"
echo "1. <NAME_EMAIL> / admin123"
echo "2. Check browser console for JWT token debug info"
echo "3. Navigate to Tenants section"
echo "4. Verify no pagination warnings in console"
echo "5. Check that pagination shows '1' instead of 'NaN'"
echo ""
echo "💡 If JWT token still missing tenant info:"
echo "- This is normal for superusers (they don't need tenant assignment)"
echo "- For tenant users, ensure they are assigned to a tenant"
echo "- Check that tenant exists and has proper domain configuration"
echo ""
echo "💡 If pagination still shows NaN:"
echo "- Clear browser cache completely"
echo "- Check network tab for API response structure"
echo "- Verify API returns proper count field or array"
