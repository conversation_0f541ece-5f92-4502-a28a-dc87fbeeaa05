import { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Alert,
  CircularProgress,
  Grid,
  Paper,
  Chip,
  IconButton,
  Card,
  CardContent,
  Tooltip,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Description as DocumentIcon,
  Delete as DeleteIcon,
  Download as DownloadIcon,
  Add as AddIcon,
  Close as CloseIcon,
  Visibility as PreviewIcon,
} from '@mui/icons-material';
import EthiopianCalendarWidget from '../common/EthiopianCalendarWidget';
import axios from '../../utils/axios';
import { useAuth } from '../../contexts/AuthContext';
import { useLocalization } from '../../contexts/LocalizationContext';
import DeleteConfirmationDialog from './DeleteConfirmationDialog';

const DocumentUpload = ({ citizenId, tenantId, onDocumentUploaded, initialDocuments = null }) => {
  const { user } = useAuth();
  const { t } = useLocalization();
  const [documents, setDocuments] = useState(initialDocuments || []);
  const [documentTypes, setDocumentTypes] = useState([]);
  const [loading, setLoading] = useState(!initialDocuments);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);

  // Delete confirmation dialog state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState(null);
  const [deleting, setDeleting] = useState(false);

  // Role-based permissions
  const canUpload = user?.role === 'clerk';
  const canDelete = user?.role === 'clerk';
  const canView = true; // All roles can view documents

  // Upload form state
  const [uploadForm, setUploadForm] = useState({
    document_type: '',
    document_file: null,
    issue_date: null,
    expiry_date: null,
  });

  useEffect(() => {
    // If initial documents are provided, use them; otherwise fetch from API
    if (initialDocuments) {
      setDocuments(initialDocuments);
      setLoading(false);
    } else {
      fetchDocuments();
    }
    fetchDocumentTypes();
  }, [citizenId, tenantId, initialDocuments]);

  const fetchDocuments = async () => {
    try {
      setLoading(true);
      console.log('🔍 Fetching documents for citizen:', citizenId, 'in tenant:', tenantId);

      const response = await axios.get(`/api/tenants/${tenantId}/documents/`, {
        params: { citizen: citizenId }
      });

      console.log('🔍 Documents API response:', response.data);
      setDocuments(response.data.results || response.data || []);
    } catch (error) {
      console.error('❌ Error fetching documents:', error);
      console.error('❌ Error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      });
      setError('Failed to load documents');
    } finally {
      setLoading(false);
    }
  };

  const fetchDocumentTypes = async () => {
    try {
      const response = await axios.get('/api/shared/document-types/');
      setDocumentTypes(response.data.results || response.data || []);
    } catch (error) {
      console.error('Error fetching document types:', error);
    }
  };

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        setError('File size must be less than 10MB');
        return;
      }

      setUploadForm(prev => ({ ...prev, document_file: file }));
      setError('');
    }
  };

  const handleUpload = async () => {
    if (!uploadForm.document_type || !uploadForm.document_file) {
      setError('Please select document type and file');
      return;
    }

    try {
      setUploading(true);
      setError('');

      const formData = new FormData();
      formData.append('citizen_id', citizenId);
      formData.append('document_type_id', uploadForm.document_type);
      formData.append('document_file', uploadForm.document_file);

      if (uploadForm.issue_date) {
        formData.append('issue_date', uploadForm.issue_date.toISOString().split('T')[0]);
      }
      if (uploadForm.expiry_date) {
        formData.append('expiry_date', uploadForm.expiry_date.toISOString().split('T')[0]);
      }

      // Debug logging
      console.log('🔍 Upload Debug Info:');
      console.log('- citizenId:', citizenId);
      console.log('- tenantId:', tenantId);
      console.log('- document_type:', uploadForm.document_type);
      console.log('- document_file:', uploadForm.document_file);
      console.log('- issue_date:', uploadForm.issue_date);
      console.log('- expiry_date:', uploadForm.expiry_date);
      console.log('- FormData entries:');
      for (let [key, value] of formData.entries()) {
        console.log(`  ${key}:`, value);
      }

      const response = await axios.post(`/api/tenants/${tenantId}/documents/`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('🔍 Upload Response:', response.data);

      setSuccess('Document uploaded successfully!');
      setUploadDialogOpen(false);
      setUploadForm({
        document_type: '',
        document_file: null,
        issue_date: null,
        expiry_date: null,
      });

      // Refresh documents list
      fetchDocuments();

      // Notify parent component
      if (onDocumentUploaded) {
        onDocumentUploaded();
      }

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(''), 3000);
    } catch (error) {
      console.error('🔍 Upload Error Details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        headers: error.response?.headers,
        config: error.config
      });

      let errorMessage = 'Failed to upload document';
      if (error.response?.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data;
        } else if (error.response.data.detail) {
          errorMessage = error.response.data.detail;
        } else if (error.response.data.non_field_errors) {
          errorMessage = error.response.data.non_field_errors.join(', ');
        } else {
          // Handle field-specific errors
          const fieldErrors = Object.entries(error.response.data)
            .map(([field, errors]) => `${field}: ${Array.isArray(errors) ? errors.join(', ') : errors}`)
            .join('; ');
          errorMessage = fieldErrors || 'Validation error';
        }
      }

      setError(errorMessage);
    } finally {
      setUploading(false);
    }
  };

  const handleDeleteClick = (document) => {
    if (!canDelete) {
      setError('You do not have permission to delete documents');
      return;
    }

    setDocumentToDelete(document);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!documentToDelete) return;

    try {
      setDeleting(true);
      console.log('🔍 Deleting document:', documentToDelete.id, 'from tenant:', tenantId);
      console.log('🔍 Delete URL:', `/api/tenants/${tenantId}/documents/${documentToDelete.id}/`);
      console.log('🔍 Current user:', user);
      console.log('🔍 User tenant ID:', user?.tenant_id);
      console.log('🔍 Tenant ID mismatch?', tenantId !== user?.tenant_id);
      console.log('🔍 Available documents:', documents);
      console.log('🔍 Document to delete:', documentToDelete);
      console.log('🔍 Document citizen ID:', documentToDelete.citizen);

      // CRITICAL FIX: Use the user's actual tenant ID instead of the passed tenantId
      const actualTenantId = user?.tenant_id || tenantId;
      console.log('🔍 Using actual tenant ID:', actualTenantId);

      if (actualTenantId !== tenantId) {
        console.warn('⚠️ Tenant ID mismatch detected! Using user tenant ID instead.');
      }

      const response = await axios.delete(`/api/tenants/${actualTenantId}/documents/${documentToDelete.id}/`);
      console.log('🔍 Delete response:', response);

      setSuccess('Document deleted successfully!');
      setDeleteDialogOpen(false);
      setDocumentToDelete(null);

      // Refresh documents list
      if (initialDocuments) {
        // If using initial documents, notify parent to refresh
        if (onDocumentUploaded) {
          onDocumentUploaded();
        }
      } else {
        // If fetching documents directly, refresh the list
        fetchDocuments();
      }

      setTimeout(() => setSuccess(''), 3000);
    } catch (error) {
      console.error('❌ Error deleting document:', error);
      console.error('❌ Delete error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      });

      let errorMessage = 'Failed to delete document';
      if (error.response?.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data;
        } else if (error.response.data.detail) {
          errorMessage = error.response.data.detail;
        } else if (error.response.data.error) {
          errorMessage = error.response.data.error;
        }
      }

      setError(errorMessage);
    } finally {
      setDeleting(false);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setDocumentToDelete(null);
  };

  const handleDownload = async (documentData) => {
    try {
      const relativeUrl = documentData.document_file.replace('http://10.139.8.141:8000', '');
      console.log('🔍 Downloading document:', relativeUrl);

      // Try direct download first
      const link = document.createElement('a');
      link.href = relativeUrl;
      link.setAttribute('download', `${documentData.document_type_name}.${documentData.document_file.split('.').pop()}`);
      link.setAttribute('target', '_blank');
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

    } catch (error) {
      console.error('Error downloading document:', error);
      setError('Failed to download document');
    }
  };

  const handleView = (documentData) => {
    console.log('🔍 Opening document in new tab:', documentData);
    if (documentData && documentData.document_file) {
      const relativeUrl = documentData.document_file.replace('http://10.139.8.141:8000', '');
      window.open(relativeUrl, '_blank');
    } else {
      console.error('❌ Invalid document data for view:', documentData);
      setError('Cannot view document: Invalid document data');
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Not specified';
    return new Date(dateString).toLocaleDateString();
  };

  const getDocumentTypeName = (typeId) => {
    const type = documentTypes.find(t => t.id === typeId);
    return type ? type.name : 'Unknown';
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 4 }}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Loading documents...</Typography>
      </Box>
    );
  }

  return (
      <Box>
        {/* Success/Error Messages */}
        {success && (
          <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess('')}>
            {success}
          </Alert>
        )}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
            {error}
          </Alert>
        )}

        {/* Upload Button */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h6" component="h3">
            Uploaded Documents ({documents.length})
          </Typography>
          {canUpload && (
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setUploadDialogOpen(true)}
            >
              {t('upload_document', 'Upload Document')}
            </Button>
          )}
        </Box>

        {/* Documents List */}
        {documents.length === 0 ? (
          <Paper sx={{ p: 4, textAlign: 'center' }}>
            <DocumentIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              {t('no_documents_uploaded_yet', 'No documents uploaded yet')}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              {canUpload
                ? t('upload_documents_instruction', 'Upload documents such as birth certificates, passports, or other identification documents.')
                : t('no_documents_uploaded_for_citizen', 'No documents have been uploaded for this citizen yet.')
              }
            </Typography>
            {canUpload && (
              <Button
                variant="contained"
                startIcon={<UploadIcon />}
                onClick={() => setUploadDialogOpen(true)}
              >
                {t('upload_first_document', 'Upload First Document')}
              </Button>
            )}
          </Paper>
        ) : (
          <Grid container spacing={2}>
            {documents.map((document) => (
              <Grid item xs={12} md={6} key={document.id}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                      <DocumentIcon color="primary" />
                      <Typography variant="h6" component="h4">
                        {document.document_type_name}
                      </Typography>
                    </Box>

                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      <strong>Issue Date:</strong> {formatDate(document.issue_date)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      <strong>Expiry Date:</strong> {formatDate(document.expiry_date)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      <strong>Uploaded:</strong> {formatDate(document.created_at)}
                    </Typography>

                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', alignItems: 'center' }}>
                      <Button
                        size="small"
                        variant="contained"
                        startIcon={<PreviewIcon />}
                        onClick={() => handleView(document)}
                      >
                        View
                      </Button>
                      <Button
                        size="small"
                        variant="outlined"
                        startIcon={<DownloadIcon />}
                        onClick={() => handleDownload(document)}
                      >
                        Download
                      </Button>
                      {canDelete && (
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDeleteClick(document)}
                          title="Delete document"
                        >
                          <DeleteIcon />
                        </IconButton>
                      )}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}

        {/* Upload Dialog - Only for clerks */}
        {canUpload && (
          <Dialog
            open={uploadDialogOpen}
            onClose={() => setUploadDialogOpen(false)}
            maxWidth="sm"
            fullWidth
          >
          <DialogTitle>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              {t('upload_document', 'Upload Document')}
              <IconButton onClick={() => setUploadDialogOpen(false)}>
                <CloseIcon />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <FormControl fullWidth required>
                  <InputLabel>Document Type</InputLabel>
                  <Select
                    value={uploadForm.document_type}
                    label="Document Type"
                    onChange={(e) => setUploadForm(prev => ({ ...prev, document_type: e.target.value }))}
                  >
                    {documentTypes.map((type) => (
                      <MenuItem key={type.id} value={type.id}>
                        {type.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <input
                  accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                  style={{ display: 'none' }}
                  id="document-file-upload"
                  type="file"
                  onChange={handleFileChange}
                />
                <label htmlFor="document-file-upload">
                  <Button
                    variant="outlined"
                    component="span"
                    startIcon={<UploadIcon />}
                    fullWidth
                    sx={{ py: 2 }}
                  >
                    {uploadForm.document_file ? uploadForm.document_file.name : 'Choose File'}
                  </Button>
                </label>
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                  Supported formats: PDF, DOC, DOCX, JPG, PNG (Max 10MB)
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <EthiopianCalendarWidget
                  label="Issue Date (Ethiopian Calendar)"
                  value={uploadForm.issue_date}
                  onChange={(date) => setUploadForm(prev => ({ ...prev, issue_date: date }))}
                  language="en"
                  showConversion={true}
                  placeholder="Select issue date"
                  minYear={1950}
                  maxYear={2030}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <EthiopianCalendarWidget
                  label="Expiry Date (Ethiopian Calendar)"
                  value={uploadForm.expiry_date}
                  onChange={(date) => setUploadForm(prev => ({ ...prev, expiry_date: date }))}
                  language="en"
                  showConversion={true}
                  placeholder="Select expiry date"
                  minYear={1950}
                  maxYear={2040}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setUploadDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleUpload}
              variant="contained"
              disabled={uploading || !uploadForm.document_type || !uploadForm.document_file}
              startIcon={uploading ? <CircularProgress size={20} /> : <UploadIcon />}
            >
              {uploading ? 'Uploading...' : 'Upload'}
            </Button>
          </DialogActions>
        </Dialog>
        )}

        {/* Delete Confirmation Dialog */}
        <DeleteConfirmationDialog
          open={deleteDialogOpen}
          onClose={handleDeleteCancel}
          onConfirm={handleDeleteConfirm}
          document={documentToDelete}
          loading={deleting}
        />
      </Box>
  );
};

export default DocumentUpload;
