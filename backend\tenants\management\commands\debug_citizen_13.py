from django.core.management.base import BaseCommand
from django_tenants.utils import schema_context
from tenants.models import Tenant
from tenants.models.citizen import Citizen
from django.db import connection


class Command(BaseCommand):
    help = 'Debug citizen ID 13 in tenant 42 with detailed verification'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🔍 Comprehensive debugging of citizen ID 13 in tenant 42...'))

        try:
            # Get tenant 42
            tenant = Tenant.objects.get(id=42)
            self.stdout.write(f'📋 Tenant: {tenant.name} ({tenant.schema_name})')

            # Check current database connection
            self.stdout.write(f'🔍 Current database connection: {connection.settings_dict["NAME"]}')

            # Check citizen 13 in tenant 42's schema
            with schema_context(tenant.schema_name):
                self.stdout.write(f'🔍 Inside schema context: {tenant.schema_name}')

                # Check current schema in database
                with connection.cursor() as cursor:
                    cursor.execute("SELECT current_schema()")
                    current_schema = cursor.fetchone()[0]
                    self.stdout.write(f'🔍 Current PostgreSQL schema: {current_schema}')

                # Check if the citizens table exists in this schema
                with connection.cursor() as cursor:
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT 1 FROM information_schema.tables
                            WHERE table_schema = %s AND table_name = 'citizens_citizen'
                        )
                    """, [tenant.schema_name])
                    table_exists = cursor.fetchone()[0]
                    self.stdout.write(f'🔍 citizens_citizen table exists in {tenant.schema_name}: {table_exists}')

                # Count total citizens using raw SQL
                with connection.cursor() as cursor:
                    cursor.execute(f'SELECT COUNT(*) FROM "{tenant.schema_name}".citizens_citizen')
                    raw_count = cursor.fetchone()[0]
                    self.stdout.write(f'🔍 Raw SQL citizen count: {raw_count}')

                # Count using Django ORM
                orm_count = Citizen.objects.count()
                self.stdout.write(f'🔍 Django ORM citizen count: {orm_count}')

                # Check for citizen ID 13 using raw SQL
                with connection.cursor() as cursor:
                    cursor.execute(f'SELECT id, first_name, last_name, digital_id FROM "{tenant.schema_name}".citizens_citizen WHERE id = 13')
                    raw_result = cursor.fetchone()
                    if raw_result:
                        self.stdout.write(f'✅ Raw SQL found citizen ID 13: {raw_result}')
                    else:
                        self.stdout.write(f'❌ Raw SQL did NOT find citizen ID 13')

                # Check using Django ORM
                try:
                    citizen = Citizen.objects.get(id=13)
                    self.stdout.write(f'✅ Django ORM found citizen ID 13: {citizen.get_full_name()}')
                    self.stdout.write(f'   - Digital ID: {citizen.digital_id}')
                    self.stdout.write(f'   - Database ID: {citizen.id}')
                    self.stdout.write(f'   - First Name: {citizen.first_name}')
                    self.stdout.write(f'   - Last Name: {citizen.last_name}')

                    # Test the exact validation that the serializer would do
                    self.stdout.write(f'\n🧪 Testing serializer validation logic...')
                    try:
                        # This is what the serializer validate_citizen method does
                        test_citizen = Citizen.objects.get(pk=13)
                        self.stdout.write(f'✅ Serializer validation would succeed: {test_citizen.get_full_name()}')
                    except Citizen.DoesNotExist:
                        self.stdout.write(f'❌ Serializer validation would fail: Citizen.DoesNotExist')

                except Citizen.DoesNotExist:
                    self.stdout.write(f'❌ Django ORM did NOT find citizen ID 13 in {tenant.schema_name}')

                    # List all citizens in this schema using ORM
                    all_citizens = Citizen.objects.all()
                    self.stdout.write(f'📋 All citizens via Django ORM in {tenant.schema_name} ({all_citizens.count()} total):')
                    for c in all_citizens:
                        self.stdout.write(f'   - ID {c.id}: {c.get_full_name()} (Digital: {c.digital_id})')

                    # List all citizens using raw SQL
                    self.stdout.write(f'📋 All citizens via Raw SQL:')
                    with connection.cursor() as cursor:
                        cursor.execute(f'SELECT id, first_name, last_name, digital_id FROM "{tenant.schema_name}".citizens_citizen ORDER BY id')
                        raw_citizens = cursor.fetchall()
                        for c in raw_citizens:
                            self.stdout.write(f'   - ID {c[0]}: {c[1]} {c[2]} (Digital: {c[3]})')

        except Tenant.DoesNotExist:
            self.stdout.write(self.style.ERROR('❌ Tenant 42 not found'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Error: {str(e)}'))
            import traceback
            self.stdout.write(traceback.format_exc())

        self.stdout.write(self.style.SUCCESS('\n🎉 Debug completed!'))
