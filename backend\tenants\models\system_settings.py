from django.db import models


class SystemSettings(models.Model):
    """
    Global system settings that apply to the entire application.
    Only one instance should exist (singleton pattern).
    """
    
    # Theme settings
    primary_color = models.CharField(
        max_length=7, 
        default='#ff8f00',
        help_text='Primary color for the entire system (hex format, e.g., #ff8f00)'
    )
    secondary_color = models.CharField(
        max_length=7, 
        default='#ef6c00',
        help_text='Secondary color for the entire system (hex format, e.g., #ef6c00)'
    )
    
    # Public access settings
    public_services_enabled = models.BooleanField(
        default=False,
        help_text='Enable public access to ID card application services system-wide'
    )
    
    # Metadata
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(
        'users.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text='User who last updated these settings'
    )
    
    class Meta:
        verbose_name = 'System Settings'
        verbose_name_plural = 'System Settings'
    
    def __str__(self):
        return f"System Settings (Primary: {self.primary_color}, Secondary: {self.secondary_color}, Public: {self.public_services_enabled})"
    
    @classmethod
    def get_settings(cls):
        """
        Get the system settings instance (singleton pattern).
        Creates one if it doesn't exist.
        """
        settings, created = cls.objects.get_or_create(
            id=1,
            defaults={
                'primary_color': '#ff8f00',
                'secondary_color': '#ef6c00',
                'public_services_enabled': False,
            }
        )
        return settings
    
    def save(self, *args, **kwargs):
        """
        Ensure only one instance exists (singleton pattern).
        """
        self.pk = 1
        super().save(*args, **kwargs)
    
    def delete(self, *args, **kwargs):
        """
        Prevent deletion of the singleton instance.
        """
        pass
