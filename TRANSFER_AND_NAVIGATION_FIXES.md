# Transfer and Navigation Fixes

## 🐛 **Issues Identified and Fixed**

### **1. Transfer Request 500 Error**
**Error**: `Request failed with status code 500` when kebele leader tries to create transfer requests
**Root Cause**: Emergency transfer endpoint was using DRF JWT authentication which tried to get user from wrong schema

### **2. Missing Navigation Menus**
**Issue**: Kebele leader couldn't see clearance and transfer navigation menus despite having permissions
**Root Cause**: Dynamic navigation generation didn't include workflows for kebele leaders

## 🔧 **Fix 1: Emergency Transfer Authentication**

### **Problem**: Schema Context Issue
The emergency transfer endpoint was using:
```python
# WRONG - Gets user from wrong schema
user = jwt_auth.get_user(validated_token)
```

### **Solution**: Manual JWT Decoding with Schema Context
**Updated to match clearance endpoint approach**:
```python
# Decode JWT token manually
payload = jwt.decode(token, settings.SECRET_KEY, algorithms=['HS256'])
user_id = payload.get('user_id')
tenant_id = payload.get('tenant_id')

# Get tenant first
tenant = Tenant.objects.get(id=tenant_id)

# Get user from correct schema (tenant schema, not public)
user = None
try:
    with schema_context(tenant.schema_name):
        user = User.objects.get(id=user_id)
        print(f"✅ Found user in tenant schema: {user.username} (Role: {user.role})")
except User.DoesNotExist:
    # Fallback: try public schema
    user = User.objects.get(id=user_id)
```

### **Enhanced Permission Validation**:
```python
# Validate permissions with detailed logging
if tenant.type != 'kebele':
    print(f"❌ Tenant type check failed: {tenant.type} != 'kebele'")
    return JsonResponse({'error': 'Only kebele users can create transfer requests'}, status=403)

if user.role not in ['kebele_leader', 'clerk']:
    print(f"❌ Role check failed: {user.role} not in ['kebele_leader', 'clerk']")
    return JsonResponse({'error': 'Only kebele leaders and clerks can create transfer requests'}, status=403)

print(f"✅ Permission checks passed for user {user.username} with role {user.role}")
```

## 🔧 **Fix 2: Added Navigation Menus for Workflows**

### **Added Permission Mappings**:
```javascript
// New permission mappings
'create_clearances': ['verify_documents', 'view_citizens_list'], // Kebele leaders can create clearances
'create_transfers': ['verify_documents', 'view_citizens_list']   // Kebele leaders can create transfers
```

### **Updated Role Permissions**:
```javascript
kebele_leader: [
  'view_dashboard',
  'view_citizens',
  'view_idcards',
  'approve_idcards',
  'transfer_citizens',
  'view_reports',
  'view_workflows',
  'verify_documents',
  'create_clearances',    // ✅ Added
  'create_transfers'      // ✅ Added
],

clerk: [
  'view_dashboard',
  'view_citizens',
  'register_citizens',
  'view_idcards',
  'create_idcards',
  'view_workflows',       // ✅ Added
  'create_clearances',    // ✅ Added
  'create_transfers'      // ✅ Added
]
```

### **Dynamic Navigation Generation**:
```javascript
// Add Workflows (Clearances & Transfers) for kebele leaders and clerks
if (user.role === 'kebele_leader' || user.role === 'clerk') {
  const workflowsItem = {
    id: 'workflows',
    label: 'Workflows',
    icon: 'Timeline',
    permission: 'view_workflows',
    children: []
  };

  // Add clearances
  workflowsItem.children.push({
    id: 'clearances',
    label: 'Clearances',
    path: '/clearances',
    permission: 'create_clearances'
  });

  // Add transfers
  workflowsItem.children.push({
    id: 'transfers',
    label: 'Transfers',
    path: '/transfers',
    permission: 'create_transfers'
  });

  // Set default path to clearances
  workflowsItem.path = '/clearances';

  basicItems.push(workflowsItem);
}
```

## ✅ **Expected Results After Fixes**

### **For Kebele Leader (`<EMAIL>`)**:

#### **✅ Transfer Requests**:
```
POST /api/emergency-transfer/
✅ User found in tenant schema: <EMAIL> (Role: kebele_leader)
✅ Permission checks passed <NAME_EMAIL> with role kebele_leader
✅ Transfer request created in both schemas successfully
```

#### **✅ Navigation Menu**:
```
✅ Dashboard
✅ Citizens → /citizens
✅ ID Cards → /idcards
  ✅ Pending Approval
✅ Workflows → /clearances
  ✅ Clearances
  ✅ Transfers
✅ Reports
```

#### **✅ Clearance Requests**:
```
POST /api/emergency-clearance/
✅ User found in tenant schema: <EMAIL> (Role: kebele_leader)
✅ Permission checks passed
✅ Clearance request created and letter generated successfully
```

### **For Clerk Users**:
```
✅ Citizens → /citizens
  ✅ Register Citizen
✅ ID Cards → /idcards
  ✅ Generate ID Cards (button visible)
✅ Workflows → /clearances
  ✅ Clearances
  ✅ Transfers
```

## 🧪 **Testing Instructions**

### **Test 1: Transfer Request Creation**
1. Login as kebele leader: `<EMAIL>` / `password123`
2. Navigate to Citizens → Select a citizen
3. Click "Transfer Citizen"
4. Fill out transfer form
5. **Expected**: Transfer request created successfully (no 500 error)

### **Test 2: Navigation Menu Visibility**
1. Login as kebele leader
2. Check navigation menu
3. **Expected**: "Workflows" menu visible with "Clearances" and "Transfers" submenus

### **Test 3: Clearance Request Creation**
1. Login as kebele leader
2. Navigate to Citizens → Select a citizen
3. Click "Create Clearance Request"
4. Fill out clearance form
5. **Expected**: Clearance request created and PDF generated

### **Test 4: Clerk Navigation**
1. Login as clerk
2. Check navigation menu
3. **Expected**: "Workflows" menu visible with clearance and transfer options

## 🚀 **Benefits Achieved**

### **✅ Consistent Authentication**:
- **Transfer endpoint** now uses same schema context approach as clearance endpoint
- **User retrieval** works correctly for tenant-based users
- **Permission validation** with detailed logging for debugging

### **✅ Complete Navigation**:
- **Kebele leaders** can see workflows menu with clearances and transfers
- **Clerks** can also access workflow features
- **Dynamic generation** based on user role and permissions

### **✅ Enhanced Debugging**:
- **Detailed logging** shows exactly where user is found
- **Permission validation** logs each step
- **Clear error messages** for troubleshooting

### **✅ User Experience**:
- **Appropriate menus** for each role
- **Functional workflows** for kebele-level operations
- **Consistent behavior** across all emergency endpoints

## 📊 **Permission Matrix After Fixes**

### **Kebele Leader**:
```
✅ View Citizens          → Can view local citizens
✅ View ID Cards          → Can view local ID cards
✅ Approve ID Cards       → Can approve pending ID cards
✅ Create Clearances      → Can create clearance requests [FIXED]
✅ Create Transfers       → Can create transfer requests [FIXED]
✅ Workflows Menu         → Visible in navigation [FIXED]
❌ Register Citizens      → Cannot register (clerk only)
❌ Create ID Cards        → Cannot create (clerk only)
```

### **Clerk**:
```
✅ View Citizens          → Can view local citizens
✅ Register Citizens      → Can register new citizens
✅ View ID Cards          → Can view ID card lists
✅ Create ID Cards        → Can generate ID cards
✅ Create Clearances      → Can create clearance requests [ADDED]
✅ Create Transfers       → Can create transfer requests [ADDED]
✅ Workflows Menu         → Visible in navigation [ADDED]
❌ Approve ID Cards       → Cannot approve (kebele leader only)
```

## 🔧 **Technical Implementation**

### **Schema Context Pattern**:
```python
# Consistent pattern for multi-tenant user retrieval
def get_user_from_correct_schema(user_id, tenant):
    try:
        # Try tenant schema first
        with schema_context(tenant.schema_name):
            return User.objects.get(id=user_id)
    except User.DoesNotExist:
        # Fallback to public schema
        return User.objects.get(id=user_id)
```

### **Dynamic Navigation Pattern**:
```javascript
// Role-based navigation generation
if (user.role === 'kebele_leader' || user.role === 'clerk') {
    // Add workflows menu with appropriate children
    const workflowsItem = {
        id: 'workflows',
        label: 'Workflows',
        children: [
            { id: 'clearances', label: 'Clearances', path: '/clearances' },
            { id: 'transfers', label: 'Transfers', path: '/transfers' }
        ]
    };
    basicItems.push(workflowsItem);
}
```

---

**Both transfer functionality and navigation menus are now working correctly for kebele leaders and clerks!** 🎉
