from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.db import connection
import json

from .models import IDCard, IDCardServiceRequest, IDCardRenewal, IDCardReplacement, IDCardReprint
from citizens.models import Citizen
from .fraud_detection import fraud_detection_service
from tenants.models import Tenant, Kebele
from django_tenants.utils import schema_context


@csrf_exempt
@require_http_methods(["POST"])
def apply_renewal_service(request):
    """Apply for ID card renewal service with fraud detection"""
    try:
        data = json.loads(request.body)

        # Get citizen by digital ID
        digital_id = data.get('digital_id')
        if not digital_id:
            return JsonResponse({'error': 'Digital ID is required'}, status=400)

        citizen = get_object_or_404(Citizen, digital_id=digital_id)

        # Get the current ID card
        try:
            id_card = IDCard.objects.get(citizen=citizen)
        except IDCard.DoesNotExist:
            return JsonResponse({'error': 'No ID card found for this citizen'}, status=404)

        # Check if ID card is expired or expiring soon
        if not (id_card.status == 'expired' or id_card.expiry_date <= timezone.now().date()):
            return JsonResponse({'error': 'ID card is not eligible for renewal'}, status=400)

        # Fraud detection check
        current_tenant_id = getattr(connection.tenant, 'id', None)
        if current_tenant_id:
            citizen_data = {
                'digital_id': citizen.digital_id,
                'first_name': citizen.first_name,
                'last_name': citizen.last_name,
                'date_of_birth': citizen.date_of_birth,
                'phone': citizen.phone,
                'email': citizen.email
            }

            fraud_results = fraud_detection_service.check_fraud_indicators(citizen_data, current_tenant_id)

            # If critical fraud detected, block the application
            if fraud_results['risk_level'] == 'critical':
                return JsonResponse({
                    'error': 'Application blocked due to fraud detection',
                    'fraud_detected': True,
                    'risk_level': fraud_results['risk_level'],
                    'indicators': fraud_results['indicators'],
                    'recommendations': fraud_results['recommendations']
                }, status=403)

        # Create renewal request
        renewal = IDCardRenewal.objects.create(
            citizen=citizen,
            old_id_card=id_card,
            application_method=data.get('application_method', 'physical'),
            reason=data.get('reason', 'ID card expired - requesting renewal'),
            kebele_reviewer=data.get('kebele_reviewer', ''),
        )

        response_data = {
            'success': True,
            'message': 'Renewal request submitted successfully',
            'request_id': renewal.id,
            'status': renewal.status
        }

        # Include fraud detection results if any risk detected
        if current_tenant_id and fraud_results['risk_level'] != 'low':
            response_data['fraud_warning'] = {
                'risk_level': fraud_results['risk_level'],
                'indicators': fraud_results['indicators'],
                'recommendations': fraud_results['recommendations']
            }

        return JsonResponse(response_data)

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def apply_replacement_service(request):
    """Apply for damaged ID card replacement"""
    try:
        data = json.loads(request.body)
        
        # Get citizen by digital ID
        digital_id = data.get('digital_id')
        if not digital_id:
            return JsonResponse({'error': 'Digital ID is required'}, status=400)
        
        citizen = get_object_or_404(Citizen, digital_id=digital_id)
        
        # Get the current ID card
        try:
            id_card = IDCard.objects.get(citizen=citizen)
        except IDCard.DoesNotExist:
            return JsonResponse({'error': 'No ID card found for this citizen'}, status=404)
        
        # Create replacement request
        replacement = IDCardReplacement.objects.create(
            citizen=citizen,
            old_id_card=id_card,
            application_method=data.get('application_method', 'online'),
            reason=data.get('reason', 'ID card damaged - requesting replacement'),
            damage_description=data.get('damage_description', ''),
            kebele_reviewer=data.get('kebele_reviewer', ''),
        )
        
        return JsonResponse({
            'success': True,
            'message': 'Replacement request submitted successfully',
            'request_id': replacement.id,
            'status': replacement.status
        })
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def apply_reprint_service(request):
    """Apply for lost ID card reprint"""
    try:
        data = json.loads(request.body)
        
        # Get citizen by digital ID
        digital_id = data.get('digital_id')
        if not digital_id:
            return JsonResponse({'error': 'Digital ID is required'}, status=400)
        
        citizen = get_object_or_404(Citizen, digital_id=digital_id)
        
        # Get the current ID card
        try:
            id_card = IDCard.objects.get(citizen=citizen)
        except IDCard.DoesNotExist:
            return JsonResponse({'error': 'No ID card found for this citizen'}, status=404)
        
        # Create reprint request
        reprint = IDCardReprint.objects.create(
            citizen=citizen,
            old_id_card=id_card,
            application_method=data.get('application_method', 'online'),
            reason=data.get('reason', 'ID card lost - requesting reprint'),
            police_report_number=data.get('police_report_number', ''),
            loss_date=data.get('loss_date'),
            loss_location=data.get('loss_location', ''),
            kebele_reviewer=data.get('kebele_reviewer', ''),
        )
        
        return JsonResponse({
            'success': True,
            'message': 'Reprint request submitted successfully',
            'request_id': reprint.id,
            'status': reprint.status
        })
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@require_http_methods(["GET"])
def get_service_requests(request):
    """Get all service requests for kebele leader review"""
    try:
        # Get all pending service requests
        requests = IDCardServiceRequest.objects.filter(
            status='pending'
        ).order_by('-created_at')
        
        requests_data = []
        for req in requests:
            requests_data.append({
                'id': req.id,
                'citizen_name': req.citizen.get_full_name(),
                'citizen_digital_id': req.citizen.digital_id,
                'service_type': req.get_service_type_display(),
                'application_method': req.get_application_method_display(),
                'reason': req.reason,
                'requested_at': req.requested_at.isoformat(),
                'status': req.get_status_display(),
            })
        
        return JsonResponse({
            'success': True,
            'requests': requests_data
        })
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def approve_service_request(request, request_id):
    """Approve service request by kebele leader"""
    try:
        data = json.loads(request.body)
        
        service_request = get_object_or_404(IDCardServiceRequest, id=request_id)
        
        # Update request status
        service_request.status = 'kebele_approved'
        service_request.kebele_reviewed_at = timezone.now()
        service_request.kebele_notes = data.get('notes', '')
        service_request.save()
        
        return JsonResponse({
            'success': True,
            'message': 'Service request approved and forwarded to subcity',
            'status': service_request.status
        })
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def reject_service_request(request, request_id):
    """Reject service request by kebele leader"""
    try:
        data = json.loads(request.body)
        
        service_request = get_object_or_404(IDCardServiceRequest, id=request_id)
        
        # Update request status
        service_request.status = 'rejected'
        service_request.kebele_reviewed_at = timezone.now()
        service_request.rejection_reason = data.get('reason', '')
        service_request.save()
        
        return JsonResponse({
            'success': True,
            'message': 'Service request rejected',
            'status': service_request.status
        })
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@require_http_methods(["GET"])
def check_renewal_eligibility(request, digital_id):
    """Check if citizen is eligible for renewal"""
    try:
        citizen = get_object_or_404(Citizen, digital_id=digital_id)
        
        try:
            id_card = IDCard.objects.get(citizen=citizen)
            
            # Check if expired or expiring within 30 days
            is_expired = id_card.expiry_date <= timezone.now().date()
            days_to_expiry = (id_card.expiry_date - timezone.now().date()).days
            is_expiring_soon = days_to_expiry <= 30
            
            eligible = is_expired or is_expiring_soon
            
            return JsonResponse({
                'success': True,
                'eligible': eligible,
                'is_expired': is_expired,
                'is_expiring_soon': is_expiring_soon,
                'days_to_expiry': days_to_expiry,
                'expiry_date': id_card.expiry_date.isoformat(),
                'card_status': id_card.status
            })
            
        except IDCard.DoesNotExist:
            return JsonResponse({
                'success': True,
                'eligible': False,
                'error': 'No ID card found for this citizen'
            })
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def check_fraud_indicators(request):
    """
    Dedicated endpoint for fraud detection checks.
    Can be used before submitting any ID card application.
    """
    try:
        data = json.loads(request.body)

        # Get current tenant ID
        current_tenant_id = getattr(connection.tenant, 'id', None)
        if not current_tenant_id:
            return JsonResponse({'error': 'Tenant context required'}, status=400)

        # Extract citizen data from request
        citizen_data = {
            'digital_id': data.get('digital_id'),
            'first_name': data.get('first_name'),
            'last_name': data.get('last_name'),
            'date_of_birth': data.get('date_of_birth'),
            'phone': data.get('phone'),
            'email': data.get('email'),
            'biometric_data': data.get('biometric_data')  # Optional
        }

        # Validate required fields
        required_fields = ['first_name', 'last_name', 'date_of_birth']
        missing_fields = [field for field in required_fields if not citizen_data.get(field)]
        if missing_fields:
            return JsonResponse({
                'error': f'Missing required fields: {", ".join(missing_fields)}'
            }, status=400)

        # Run fraud detection
        fraud_results = fraud_detection_service.check_fraud_indicators(citizen_data, current_tenant_id)

        return JsonResponse({
            'success': True,
            'fraud_detection': fraud_results
        })

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@require_http_methods(["GET"])
def get_fraud_statistics(request):
    """
    Get fraud detection statistics for the current tenant.
    """
    try:
        current_tenant_id = getattr(connection.tenant, 'id', None)
        if not current_tenant_id:
            return JsonResponse({'error': 'Tenant context required'}, status=400)

        # Get statistics for the last 30 days
        from datetime import timedelta
        recent_date = timezone.now() - timedelta(days=30)

        # Count recent applications
        recent_applications = IDCardServiceRequest.objects.filter(
            requested_at__gte=recent_date
        ).count()

        # Count rejected applications (potential fraud cases)
        rejected_applications = IDCardServiceRequest.objects.filter(
            requested_at__gte=recent_date,
            status='rejected'
        ).count()

        # Calculate fraud detection rate
        fraud_rate = (rejected_applications / recent_applications * 100) if recent_applications > 0 else 0

        return JsonResponse({
            'success': True,
            'statistics': {
                'recent_applications': recent_applications,
                'rejected_applications': rejected_applications,
                'fraud_detection_rate': round(fraud_rate, 2),
                'period_days': 30
            }
        })

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def apply_public_service(request):
    """Apply for ID card services through public portal with kebele selection"""
    try:
        # Parse request data
        data = json.loads(request.body)

        # Required fields
        required_fields = ['service_type', 'kebele_id']
        for field in required_fields:
            if field not in data:
                return JsonResponse({'error': f'Missing required field: {field}'}, status=400)

        service_type = data['service_type']
        kebele_id = data['kebele_id']

        # Handle both new format (with citizen_data) and legacy format (direct fields)
        if 'citizen_data' in data:
            citizen_data = data['citizen_data']
        else:
            # Legacy format - construct citizen_data from direct fields
            citizen_data = {
                'digital_id': data.get('digital_id', ''),
            }
            # Add optional fields if present
            for field in ['first_name', 'last_name', 'phone_number']:
                if field in data:
                    citizen_data[field] = data[field]

        # Validate service type
        valid_service_types = ['renewal', 'replacement', 'reprint']
        if service_type not in valid_service_types:
            return JsonResponse({'error': f'Invalid service type. Must be one of: {valid_service_types}'}, status=400)

        # Validate citizen data - only digital_id is required for public services
        if 'digital_id' not in citizen_data or not citizen_data['digital_id']:
            return JsonResponse({'error': 'Missing required citizen field: digital_id'}, status=400)

        # Get kebele tenant
        try:
            from tenants.models import Tenant
            kebele_tenant = Tenant.objects.get(id=kebele_id, type='kebele')
        except Tenant.DoesNotExist:
            return JsonResponse({'error': 'Invalid kebele ID'}, status=400)

        # Switch to kebele schema context to process the request
        from django_tenants.utils import schema_context
        with schema_context(kebele_tenant.schema_name):
            # Look up citizen in kebele
            try:
                citizen = Citizen.objects.get(digital_id=citizen_data['digital_id'])
            except Citizen.DoesNotExist:
                return JsonResponse({'error': 'Citizen not found in selected kebele'}, status=404)

            # Verify citizen data matches (only if provided)
            if ('first_name' in citizen_data and citizen_data['first_name'] and
                citizen.first_name.lower() != citizen_data['first_name'].lower()):
                return JsonResponse({'error': 'First name does not match records'}, status=400)

            if ('last_name' in citizen_data and citizen_data['last_name'] and
                citizen.last_name.lower() != citizen_data['last_name'].lower()):
                return JsonResponse({'error': 'Last name does not match records'}, status=400)

            # Check for fraud indicators using the fraud detection service
            try:
                from fraud_detection.services import fraud_detection_service
                citizen_fraud_data = {
                    'digital_id': citizen.digital_id,
                    'first_name': citizen.first_name,
                    'last_name': citizen.last_name,
                    'date_of_birth': citizen.date_of_birth.isoformat() if citizen.date_of_birth else None,
                    'phone': citizen_data.get('phone_number') or getattr(citizen, 'phone', None),
                    'email': getattr(citizen, 'email', None)
                }
                fraud_results = fraud_detection_service.check_fraud_indicators(citizen_fraud_data, kebele_id)

                if fraud_results.get('is_suspicious', False):
                    return JsonResponse({
                        'error': 'Application flagged for review',
                        'fraud_indicators': fraud_results.get('indicators', [])
                    }, status=400)
            except ImportError:
                # Fraud detection service not available, continue without check
                pass
            except Exception as fraud_error:
                # Log fraud detection error but don't block the application
                print(f"Fraud detection error: {fraud_error}")
                pass

            # Create service request
            contact_info = citizen_data.get('phone_number') or getattr(citizen, 'phone', 'No contact provided')
            service_request = IDCardServiceRequest.objects.create(
                citizen=citizen,
                service_type=service_type,
                application_method='online',
                status='pending',
                reason=f"Public portal application for {service_type}. Contact: {contact_info}"
            )

            # Get parent subcity for forwarding
            parent_subcity = kebele_tenant.parent
            if not parent_subcity:
                return JsonResponse({'error': 'No parent subcity found for kebele'}, status=500)

            return JsonResponse({
                'success': True,
                'message': f'{service_type.title()} request submitted successfully',
                'request_id': service_request.id,
                'status': 'pending',
                'kebele': kebele_tenant.name,
                'forwarded_to': parent_subcity.name,
                'estimated_processing_time': '3-5 business days'
            })

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON data'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)





@csrf_exempt
@require_http_methods(["GET"])
def test_public_service(request):
    """Test endpoint to verify public service functionality"""
    return JsonResponse({'success': True, 'message': 'Public service endpoint is working'})
