<!DOCTYPE html>
<html>
<head>
    <title>Test Dynamic Image Fetching</title>
</head>
<body>
    <h1>Test Dynamic Image Fetching</h1>
    <div id="results"></div>

    <script>
        async function testImageFetching() {
            const resultsDiv = document.getElementById('results');
            
            try {
                // Test Kebele14 which has profile data
                console.log('Testing Kebele14 image fetching...');
                const response = await fetch('http://localhost:8000/api/tenants/7/');
                const data = await response.json();
                
                console.log('API Response:', data);
                
                const profileData = data.profile_data;
                if (profileData) {
                    resultsDiv.innerHTML += `<h2>Kebele14 Profile Data Found:</h2>`;
                    resultsDiv.innerHTML += `<p>Logo: ${profileData.logo}</p>`;
                    resultsDiv.innerHTML += `<p>Signature: ${profileData.mayor_signature}</p>`;
                    resultsDiv.innerHTML += `<p>Pattern: ${profileData.pattern_image}</p>`;
                    
                    // Test if images are accessible
                    if (profileData.logo) {
                        const logoUrl = profileData.logo.startsWith('http') ? profileData.logo : `http://localhost:8000${profileData.logo}`;
                        resultsDiv.innerHTML += `<h3>Logo Test:</h3>`;
                        resultsDiv.innerHTML += `<img src="${logoUrl}" style="max-width: 200px;" onload="console.log('Logo loaded successfully')" onerror="console.error('Logo failed to load')">`;
                    }
                    
                    if (profileData.mayor_signature) {
                        const signatureUrl = profileData.mayor_signature.startsWith('http') ? profileData.mayor_signature : `http://localhost:8000${profileData.mayor_signature}`;
                        resultsDiv.innerHTML += `<h3>Signature Test:</h3>`;
                        resultsDiv.innerHTML += `<img src="${signatureUrl}" style="max-width: 200px;" onload="console.log('Signature loaded successfully')" onerror="console.error('Signature failed to load')">`;
                    }
                    
                    if (profileData.pattern_image) {
                        const patternUrl = profileData.pattern_image.startsWith('http') ? profileData.pattern_image : `http://localhost:8000${profileData.pattern_image}`;
                        resultsDiv.innerHTML += `<h3>Pattern Test:</h3>`;
                        resultsDiv.innerHTML += `<img src="${patternUrl}" style="max-width: 200px;" onload="console.log('Pattern loaded successfully')" onerror="console.error('Pattern failed to load')">`;
                    }
                } else {
                    resultsDiv.innerHTML += `<p style="color: red;">No profile_data found!</p>`;
                }
                
            } catch (error) {
                console.error('Error:', error);
                resultsDiv.innerHTML += `<p style="color: red;">Error: ${error.message}</p>`;
            }
        }
        
        // Run test when page loads
        testImageFetching();
    </script>
</body>
</html>
