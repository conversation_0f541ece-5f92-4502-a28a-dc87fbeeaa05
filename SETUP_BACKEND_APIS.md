# Backend API Setup Guide

## Issue Resolution

The frontend is getting 404 errors when trying to access the tenant role management APIs. Here's how to fix this:

## 1. Verify Backend Server is Running

First, make sure the Django development server is running:

```bash
cd backend
python manage.py runserver
```

The server should be accessible at `http://localhost:8000`

## 2. Run Database Migrations

Make sure all migrations are applied:

```bash
cd backend
python manage.py makemigrations users
python manage.py migrate users
python manage.py migrate
```

## 3. Setup Permissions and Roles

Run the management commands to set up the dynamic permissions system:

```bash
cd backend
python manage.py setup_permissions
python manage.py setup_dynamic_roles
```

## 4. Test API Endpoints

Test that the endpoints are accessible:

### Test Permissions Endpoint
```bash
# Should return 401 Unauthorized (not 404)
curl -X GET http://localhost:8000/api/auth/permissions/
```

### Test Roles Endpoint
```bash
# Should return 401 Unauthorized (not 404)
curl -X GET http://localhost:8000/api/auth/roles/
```

### Test Tenant Role Management
```bash
# Should return 401 Unauthorized (not 404)
curl -X GET http://localhost:8000/api/auth/tenant-role-management/manageable_tenants/
```

## 5. Create Test User and Get Token

Create a test user to get a valid JWT token:

```bash
cd backend
python manage.py shell
```

```python
from users.models import User, UserRole
from tenants.models import Tenant

# Get or create a subcity tenant
subcity = Tenant.objects.filter(type='subcity').first()
if not subcity:
    print("No subcity tenant found. Create one first.")
else:
    # Create test subcity admin
    user = User.objects.create_user(
        username='test_subcity_admin',
        email='<EMAIL>',
        password='password123',
        role=UserRole.SUBCITY_ADMIN,
        tenant=subcity,
        first_name='Test',
        last_name='Admin'
    )
    print(f"Created user: {user.email}")
```

## 6. Test with Authentication

Get a JWT token and test the APIs:

```bash
# Get token
curl -X POST http://localhost:8000/api/auth/token/ \
  -H "Content-Type: application/json" \
  -d '{"username": "<EMAIL>", "password": "password123"}'
```

Use the returned access token to test the APIs:

```bash
# Replace YOUR_TOKEN with the actual token
curl -X GET http://localhost:8000/api/auth/permissions/ \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 7. Frontend Configuration

The frontend has been updated with:

1. **Correct API URLs**: All endpoints now use `/api/auth/` prefix
2. **Mock Data Fallback**: If APIs fail, mock data is used for testing
3. **Checkbox Permission Selection**: Permissions are now selectable via checkboxes grouped by category
4. **Error Handling**: Better error handling for API failures

## 8. Expected API Responses

### Permissions API (`/api/auth/permissions/`)
```json
{
  "results": [
    {
      "codename": "view_citizens",
      "name": "View Citizens",
      "description": "Can view citizen records"
    },
    {
      "codename": "create_citizens", 
      "name": "Create Citizens",
      "description": "Can register new citizens"
    }
  ]
}
```

### Tenant Roles API (`/api/auth/tenant-role-management/tenant_roles/?tenant_id=X`)
```json
{
  "assignable_roles": [
    {
      "codename": "clerk",
      "name": "Clerk", 
      "type": "legacy",
      "level": 10,
      "is_global": true
    },
    {
      "codename": "emergency_coordinator_kebele_3",
      "name": "Emergency Coordinator - Kebele 3",
      "type": "dynamic", 
      "level": 50,
      "is_global": false,
      "scope_tenant": "Kebele 3"
    }
  ]
}
```

## 9. Troubleshooting

### 404 Not Found Errors
- Check that the Django server is running
- Verify URL patterns in `backend/users/urls.py`
- Ensure the main `backend/goid/urls.py` includes the users URLs

### 401 Unauthorized Errors
- This is expected without a valid JWT token
- Create a test user and get a token as shown above

### 500 Internal Server Errors
- Check Django logs for detailed error messages
- Ensure database migrations are applied
- Verify permissions and roles are set up

### Permission Denied Errors
- Ensure the user has the correct role (subcity_admin)
- Check that the user's tenant has child kebeles

## 10. Mock Data for Testing

The frontend now includes mock data that will be used if the APIs are not available:

- **Permissions**: 13 common permissions for citizens, ID cards, and users
- **Roles**: 5 roles including legacy (clerk, kebele_leader) and dynamic (emergency_coordinator, field_officer)

This allows you to test the UI functionality even if the backend is not fully set up.

## Next Steps

1. Start the Django server: `python manage.py runserver`
2. Run the setup commands above
3. Test the frontend role management functionality
4. Create custom roles and assign them to users
5. Grant individual permissions as needed

The enhanced user management page now provides a complete role management solution integrated into the familiar kebele user management interface!
