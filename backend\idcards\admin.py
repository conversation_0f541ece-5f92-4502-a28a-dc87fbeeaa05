from django.contrib import admin
from .models import IDCard, IDCardTemplate


@admin.register(IDCardTemplate)
class IDCardTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_default', 'is_active', 'created_at')
    list_filter = ('is_active', 'is_default', 'logo_position', 'photo_position')
    search_fields = ('name', 'description')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Template Information', {
            'fields': ('name', 'description', 'is_active', 'is_default')
        }),
        ('Design Settings', {
            'fields': ('background_color', 'text_color', 'accent_color')
        }),
        ('Layout Settings', {
            'fields': ('logo_position', 'photo_position')
        }),
        ('Content Settings', {
            'fields': ('header_text', 'subtitle_text', 'footer_text')
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at')
        }),
    )


@admin.register(IDCard)
class IDCardAdmin(admin.ModelAdmin):
    list_display = ('card_number', 'citizen', 'template', 'status', 'issue_date', 'expiry_date', 'created_at')
    list_filter = ('status', 'template', 'issue_date', 'expiry_date')
    search_fields = ('card_number', 'citizen__first_name', 'citizen__middle_name', 'citizen__last_name')
    readonly_fields = ('card_number', 'uuid', 'created_at', 'updated_at')
    fieldsets = (
        ('Card Information', {
            'fields': ('card_number', 'citizen', 'template', 'issue_date', 'expiry_date', 'status', 'uuid')
        }),
        ('QR Code and Files', {
            'fields': ('qr_code', 'pdf_file')
        }),
        ('System Information', {
            'fields': ('created_by', 'approved_by', 'issued_by', 'created_at', 'updated_at')
        }),
    )
