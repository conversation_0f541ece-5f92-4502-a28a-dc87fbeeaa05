# GoID Production Users & Tenants Guide

## 🎯 **What Gets Created Automatically**

When you deploy GoID with `deploy_fixed.bat`, the system automatically creates:

### **👤 Default Users**

| Role | Email | Password | Access Level |
|------|-------|----------|--------------|
| **Super Admin** | <EMAIL> | admin123 | Full system access |
| **City Admin** | <EMAIL> | password123 | City-level management |
| **Subcity Admin** | <EMAIL> | password123 | Subcity-level management |
| **Kebele Admin** | <EMAIL> | password123 | Kebele-level management |
| **Clerk** | <EMAIL> | password123 | Citizen registration |

### **🏢 Sample Tenants**

| Type | Name | Schema | Domain | Purpose |
|------|------|--------|---------|---------|
| **City** | Addis Ababa | city_addis_ababa | addis.goid.local | Main city tenant |
| **Subcity** | Bole | subcity_bole | bole.goid.local | Subcity under Addis Ababa |
| **Kebele** | Bole 01 | kebele_bole_01 | bole01.goid.local | Kebele under Bole subcity |

### **👥 Sample Citizens**

The system creates **5 sample citizens** in the Kebele tenant with:
- Complete personal information
- Biometric data (simulated)
- ID cards in various statuses
- Workflow logs

## 🚀 **Getting Started**

### **1. Access the System**
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **pgAdmin**: http://localhost:5050

### **2. Login Options**

**For System Administration:**
- Login: <EMAIL> / admin123
- Access: Full system, tenant management

**For City Management:**
- Login: <EMAIL> / password123
- Access: City-level operations

**For Citizen Registration:**
- Login: <EMAIL> / password123
- Access: Citizen registration, ID card processing

### **3. Test Biometric Integration**
1. Install local biometric service on workstation
2. Login as clerk
3. Register new citizen
4. Test fingerprint capture

## 🔧 **User Permissions**

### **Super Admin (<EMAIL>)**
- ✅ Create/manage all tenants
- ✅ Create/manage all users
- ✅ System configuration
- ✅ Access all data across tenants

### **City Admin (<EMAIL>)**
- ✅ Manage subcity tenants
- ✅ Create subcity users
- ✅ View city-wide reports
- ❌ Cannot access other cities

### **Subcity Admin (<EMAIL>)**
- ✅ Manage kebele tenants
- ✅ Create kebele users
- ✅ View subcity reports
- ❌ Cannot access other subcities

### **Kebele Admin (<EMAIL>)**
- ✅ Manage kebele operations
- ✅ Create clerks
- ✅ Approve ID cards
- ❌ Cannot access other kebeles

### **Clerk (<EMAIL>)**
- ✅ Register citizens
- ✅ Capture biometrics
- ✅ Submit ID card applications
- ❌ Cannot approve ID cards

## 📊 **Sample Data Overview**

### **Citizens Created:**
1. **Abebe Kebede Tadesse** - Male, employed
2. **Almaz Tesfaye Bekele** - Female, student
3. **Dawit Haile Mariam** - Male, self-employed
4. **Hanan Ahmed Mohammed** - Female, employed
5. **Yohannes Girma Wolde** - Male, unemployed

### **ID Card Statuses:**
- **Draft**: Ready for submission
- **Pending Approval**: Waiting for kebele admin approval
- **Approved**: Ready for printing
- **Printed**: Completed cards

## 🔄 **Workflow Testing**

### **Complete ID Card Workflow:**
1. **Login as Clerk** → Register new citizen
2. **Capture Biometrics** → Test fingerprint device
3. **Submit Application** → Send for approval
4. **Login as Kebele Admin** → Approve application
5. **Print ID Card** → Complete the process

### **Multi-Tenant Testing:**
1. **Create New Subcity** (as city admin)
2. **Create New Kebele** (as subcity admin)
3. **Add Users** to new tenants
4. **Test Isolation** between tenants

## 🚨 **Important Notes**

### **Security:**
- **Change default passwords** in production
- **Use strong passwords** for real deployment
- **Restrict network access** as needed

### **Data:**
- Sample data is for **testing only**
- **Delete sample citizens** before production use
- **Create real tenants** for actual deployment

### **Biometric Integration:**
- Sample citizens have **simulated biometric data**
- **Real device integration** requires local biometric service
- **Test thoroughly** before production use

---

**Next Steps:**
1. Test all user roles and permissions
2. Verify biometric device integration
3. Create real tenants for production
4. Train users on the system
