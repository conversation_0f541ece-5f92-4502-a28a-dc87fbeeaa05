# GoID Biometric Development Setup
# This script sets up the development environment with backend on Windows host for USB device access

Write-Host "🔧 GoID Biometric Development Setup" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green

# Check if Python is installed
Write-Host "📋 Checking Python installation..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ Python found: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python not found. Please install Python 3.8+ first." -ForegroundColor Red
    exit 1
}

# Check if Docker is running
Write-Host "📋 Checking Docker..." -ForegroundColor Yellow
try {
    docker version | Out-Null
    Write-Host "✅ Docker is running" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not running. Please start Docker Desktop." -ForegroundColor Red
    exit 1
}

# Stop any existing backend container
Write-Host "🛑 Stopping existing backend container..." -ForegroundColor Yellow
docker-compose stop backend 2>$null

# Start database and frontend services
Write-Host "🚀 Starting database and frontend services..." -ForegroundColor Yellow
docker-compose -f docker-compose-no-backend.yml up -d

# Wait for database to be ready
Write-Host "⏳ Waiting for database to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Install Python dependencies
Write-Host "📦 Installing Python dependencies..." -ForegroundColor Yellow
Set-Location backend
pip install -r requirements.txt

# Set environment variables
Write-Host "🔧 Setting environment variables..." -ForegroundColor Yellow
$env:DJANGO_SETTINGS_MODULE = "goid.settings"
$env:DEBUG = "True"
$env:DATABASE_URL = "postgresql://goid_user:goid_password@localhost:5432/goid_db"
$env:REDIS_URL = "redis://localhost:6379/0"

# Run migrations
Write-Host "🗄️ Running database migrations..." -ForegroundColor Yellow
python manage.py migrate

# Create superuser if it doesn't exist
Write-Host "👤 Setting up superuser..." -ForegroundColor Yellow
python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(email='<EMAIL>').exists():
    User.objects.create_superuser('<EMAIL>', 'admin123')
    print('Superuser created: <EMAIL> / admin123')
else:
    print('Superuser already exists')
"

# Start the backend server
Write-Host "🚀 Starting Django backend server..." -ForegroundColor Green
Write-Host "Backend will run on: http://localhost:8000" -ForegroundColor Cyan
Write-Host "Frontend will run on: http://localhost:3000" -ForegroundColor Cyan
Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Yellow
Write-Host "" -ForegroundColor White

python manage.py runserver 0.0.0.0:8000
