"""
Photo Duplicate Detection Middleware

This middleware automatically checks for photo duplicates during citizen registration
and blocks registration if identical photos are detected.
"""

import json
import logging
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin
from .photo_duplicate_detection import photo_duplicate_detector

logger = logging.getLogger(__name__)


class PhotoDuplicateDetectionMiddleware(MiddlewareMixin):
    """
    Middleware to automatically detect photo duplicates during citizen registration.
    
    This middleware intercepts POST requests to citizen registration endpoints
    and checks for duplicate photos before allowing the registration to proceed.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.registration_endpoints = [
            '/api/citizens/register/',
            '/api/citizens/create/',
            '/citizens/register/',
            '/citizens/create/'
        ]
        super().__init__(get_response)
    
    def process_request(self, request):
        """
        Process incoming requests to check for photo duplicates during registration.
        """
        # Only check POST requests to registration endpoints
        if request.method != 'POST':
            return None
        
        # Check if this is a registration endpoint
        is_registration = any(
            endpoint in request.path 
            for endpoint in self.registration_endpoints
        )
        
        if not is_registration:
            return None
        
        try:
            # Try to get photo data from request
            photo_data = None
            
            # Check different possible locations for photo data
            if hasattr(request, 'data') and request.data:
                photo_data = request.data.get('photo')
            elif request.content_type == 'application/json':
                try:
                    body = json.loads(request.body.decode('utf-8'))
                    photo_data = body.get('photo')
                except (json.JSONDecodeError, UnicodeDecodeError):
                    pass
            
            # If no photo data found, allow request to proceed
            if not photo_data:
                return None
            
            logger.info("🔍 Photo duplicate check triggered by middleware")
            
            # Check for photo duplicates
            duplicate_result = photo_duplicate_detector.check_photo_duplicates(photo_data)
            
            # If duplicates found, block the registration
            if duplicate_result['has_duplicates']:
                logger.warning(f"🚨 MIDDLEWARE BLOCKING: Photo duplicate detected")
                logger.warning(f"   Found {duplicate_result['total_matches']} duplicate(s)")
                
                # Log details of matches
                for match in duplicate_result['matches'][:3]:  # Log first 3 matches
                    logger.warning(f"   📍 Match: {match['citizen_name']} in {match['tenant_name']}")
                
                # Return error response to block registration
                return JsonResponse({
                    'success': False,
                    'error': 'Registration blocked due to duplicate photo',
                    'error_code': 'DUPLICATE_PHOTO',
                    'duplicate_data': {
                        'has_duplicates': True,
                        'total_matches': duplicate_result['total_matches'],
                        'matches': duplicate_result['matches'],
                        'message': 'A citizen with an identical photo is already registered in the system',
                        'blocked_by': 'Photo Duplicate Detection Middleware'
                    }
                }, status=409)  # 409 Conflict
            
            # No duplicates found, allow registration to proceed
            logger.info("✅ Photo duplicate check passed - no duplicates found")
            return None
            
        except Exception as e:
            # Log error but don't block registration due to middleware failure
            logger.error(f"❌ Photo duplicate middleware error: {e}")
            logger.error("⚠️ Allowing registration to proceed despite middleware error")
            return None
    
    def process_response(self, request, response):
        """
        Process response to add photo duplicate detection headers.
        """
        # Add header to indicate photo duplicate checking was performed
        if (request.method == 'POST' and 
            any(endpoint in request.path for endpoint in self.registration_endpoints)):
            response['X-Photo-Duplicate-Check'] = 'performed'
        
        return response


class PhotoDuplicateAuditMiddleware(MiddlewareMixin):
    """
    Middleware to log all photo-related operations for audit purposes.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def process_request(self, request):
        """
        Log photo-related operations for audit trail.
        """
        if request.method == 'POST' and 'photo' in str(request.body).lower():
            try:
                user_info = 'Anonymous'
                if hasattr(request, 'user') and request.user.is_authenticated:
                    user_info = f"{request.user.username} (ID: {request.user.id})"
                
                tenant_info = 'Unknown'
                if hasattr(request, 'tenant'):
                    tenant_info = request.tenant.name
                
                logger.info(f"📸 PHOTO AUDIT: {request.method} {request.path}")
                logger.info(f"   User: {user_info}")
                logger.info(f"   Tenant: {tenant_info}")
                logger.info(f"   IP: {request.META.get('REMOTE_ADDR', 'Unknown')}")
                logger.info(f"   User-Agent: {request.META.get('HTTP_USER_AGENT', 'Unknown')[:100]}")
                
            except Exception as e:
                logger.error(f"Photo audit middleware error: {e}")
        
        return None


class PhotoDuplicateCacheMiddleware(MiddlewareMixin):
    """
    Middleware to manage photo duplicate detection cache invalidation.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.cache_invalidation_endpoints = [
            '/api/citizens/register/',
            '/api/citizens/create/',
            '/api/citizens/update/',
            '/citizens/register/',
            '/citizens/create/',
            '/citizens/update/'
        ]
        super().__init__(get_response)
    
    def process_response(self, request, response):
        """
        Invalidate photo cache when citizens are created/updated.
        """
        # Only invalidate cache for successful operations
        if (response.status_code in [200, 201] and 
            request.method in ['POST', 'PUT', 'PATCH'] and
            any(endpoint in request.path for endpoint in self.cache_invalidation_endpoints)):
            
            try:
                # Check if the request involved photo data
                has_photo = False
                
                if hasattr(request, 'data') and request.data:
                    has_photo = 'photo' in request.data
                elif request.content_type == 'application/json':
                    try:
                        body = json.loads(request.body.decode('utf-8'))
                        has_photo = 'photo' in body
                    except (json.JSONDecodeError, UnicodeDecodeError):
                        pass
                
                if has_photo:
                    logger.info("🗑️ Invalidating photo duplicate cache due to citizen photo update")
                    photo_duplicate_detector.clear_cache()
                    response['X-Photo-Cache-Invalidated'] = 'true'
                
            except Exception as e:
                logger.error(f"Photo cache middleware error: {e}")
        
        return response
