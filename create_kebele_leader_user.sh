#!/bin/bash

# Create Kebele Leader User Script
# This script creates a proper kebele leader user for testing the approval workflow

echo "🔧 Creating Kebele Leader User for Testing"
echo "=========================================="

echo "📍 This script will:"
echo "1. Create a kebele leader user in a kebele tenant"
echo "2. Test the approval workflow with the kebele leader"
echo "3. Verify permissions are working correctly"
echo ""

# Step 1: Create kebele leader user
echo "📍 Step 1: Creating kebele leader user..."

docker-compose exec -T backend python manage.py shell << 'EOF'
from django.contrib.auth import get_user_model
from tenants.models import Tenant
from django_tenants.utils import schema_context

User = get_user_model()

print("=== CREATING KEBELE LEADER USER ===")

# Get a kebele tenant
try:
    kebele_tenant = Tenant.objects.filter(type='kebele').first()
    if not kebele_tenant:
        print("❌ No kebele tenant found!")
        exit()
    
    print(f"✅ Found kebele tenant: {kebele_tenant.name} (ID: {kebele_tenant.id})")
    print(f"   Schema: {kebele_tenant.schema_name}")
    
    # Create kebele leader user in the kebele's schema
    with schema_context(kebele_tenant.schema_name):
        # Check if kebele leader already exists
        existing_leader = User.objects.filter(role='kebele_leader').first()
        
        if existing_leader:
            print(f"✅ Kebele leader already exists: {existing_leader.email}")
            kebele_leader = existing_leader
        else:
            # Create new kebele leader
            kebele_leader = User.objects.create_user(
                username='kebele_leader',
                email='<EMAIL>',
                password='leader123',
                first_name='Kebele',
                last_name='Leader',
                role='kebele_leader',
                is_active=True
            )
            print(f"✅ Created kebele leader: {kebele_leader.email}")
        
        print(f"   User ID: {kebele_leader.id}")
        print(f"   Role: {kebele_leader.role}")
        print(f"   Active: {kebele_leader.is_active}")
        print(f"   Superuser: {kebele_leader.is_superuser}")
        
        # Also check if there are any ID cards to approve
        from idcards.models import IDCard
        pending_cards = IDCard.objects.filter(status='pending_approval')
        print(f"   Pending ID cards in this tenant: {pending_cards.count()}")
        
        if pending_cards.count() == 0:
            # Create a test ID card for approval
            draft_cards = IDCard.objects.filter(status='draft')
            if draft_cards.exists():
                test_card = draft_cards.first()
                test_card.status = 'pending_approval'
                test_card.save()
                print(f"   ✅ Set ID card {test_card.id} to pending_approval for testing")
            else:
                print("   ⚠️  No ID cards available for testing")

except Exception as e:
    print(f"❌ Error creating kebele leader: {e}")
    import traceback
    traceback.print_exc()

print("\n=== TESTING USER PERMISSIONS ===")

# Test the permission logic
from common.permissions import CanApproveIDCards

class MockRequest:
    def __init__(self, user):
        self.user = user

class MockView:
    action = 'approval_action'

# Test with kebele leader
try:
    with schema_context(kebele_tenant.schema_name):
        kebele_leader = User.objects.filter(role='kebele_leader').first()
        if kebele_leader:
            request = MockRequest(kebele_leader)
            view = MockView()
            permission = CanApproveIDCards()
            
            has_permission = permission.has_permission(request, view)
            print(f"Kebele leader permission test: {has_permission}")
            
            # Test the specific role check from the view
            roles_allowed = ['kebele_leader', 'kebele_admin', 'subcity_admin', 'superadmin']
            role_check = kebele_leader.role in roles_allowed or kebele_leader.is_superuser
            print(f"Role check (should be True): {role_check}")
            print(f"User role: {kebele_leader.role}")
            print(f"Is superuser: {kebele_leader.is_superuser}")
        else:
            print("❌ No kebele leader found for testing")
            
except Exception as e:
    print(f"❌ Error testing permissions: {e}")

EOF

# Step 2: Test authentication with kebele leader
echo ""
echo "📍 Step 2: Testing authentication with kebele leader..."

AUTH_RESPONSE=$(curl -s -X POST http://************:8000/api/auth/token/ \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"leader123"}')

if echo "$AUTH_RESPONSE" | grep -q "access"; then
    echo "✅ Kebele leader authentication successful"
    
    TOKEN=$(echo "$AUTH_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin)['access'])" 2>/dev/null)
    
    if [ ! -z "$TOKEN" ]; then
        echo "✅ Token extracted successfully"
        
        # Decode token to check user info
        echo ""
        echo "📍 Step 3: Checking token contents..."
        python3 -c "
import base64
import json

token = '$TOKEN'
try:
    # Decode JWT payload
    payload = token.split('.')[1]
    # Add padding if needed
    payload += '=' * (4 - len(payload) % 4)
    decoded = base64.b64decode(payload)
    data = json.loads(decoded)
    
    print('Token contents:')
    print(f'  User ID: {data.get(\"user_id\")}')
    print(f'  Email: {data.get(\"email\")}')
    print(f'  Role: {data.get(\"role\")}')
    print(f'  Tenant ID: {data.get(\"tenant_id\")}')
    print(f'  Is Superuser: {data.get(\"is_superuser\")}')
except Exception as e:
    print(f'Error decoding token: {e}')
"
        
        # Test ID card approval with kebele leader token
        echo ""
        echo "📍 Step 4: Testing ID card approval with kebele leader..."
        
        # Get kebele tenant ID
        KEBELE_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
          "http://************:8000/api/tenants/?type=kebele")
        
        KEBELE_ID=$(echo "$KEBELE_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, dict) and 'results' in data and len(data['results']) > 0:
        print(data['results'][0]['id'])
    else:
        print('none')
except:
    print('none')
" 2>/dev/null)
        
        if [ "$KEBELE_ID" != "none" ]; then
            echo "Using kebele ID: $KEBELE_ID"
            
            # Get ID cards
            IDCARDS_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
              http://************:8000/api/tenants/$KEBELE_ID/idcards/)
            
            IDCARD_ID=$(echo "$IDCARDS_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, list) and len(data) > 0:
        print(data[0]['id'])
    elif isinstance(data, dict) and 'results' in data and len(data['results']) > 0:
        print(data['results'][0]['id'])
    else:
        print('none')
except:
    print('none')
" 2>/dev/null)
            
            if [ "$IDCARD_ID" != "none" ]; then
                echo "Testing approval with ID card: $IDCARD_ID"
                
                APPROVAL_RESPONSE=$(curl -s -X POST \
                  -H "Authorization: Bearer $TOKEN" \
                  -H "Content-Type: application/json" \
                  -d '{"action":"approve","comment":"Test approval by kebele leader","approval_pattern":"diagonal_stripes"}' \
                  http://************:8000/api/tenants/$KEBELE_ID/idcards/$IDCARD_ID/approval_action/ \
                  -w "HTTP_STATUS:%{http_code}")
                
                HTTP_STATUS=$(echo "$APPROVAL_RESPONSE" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
                RESPONSE_BODY=$(echo "$APPROVAL_RESPONSE" | sed 's/HTTP_STATUS:[0-9]*$//')
                
                echo "Approval status: $HTTP_STATUS"
                
                if [ "$HTTP_STATUS" = "200" ]; then
                    echo "✅ SUCCESS! Kebele leader can approve ID cards"
                    echo "Response: $RESPONSE_BODY"
                elif [ "$HTTP_STATUS" = "403" ]; then
                    echo "❌ Still getting 403 Forbidden"
                    echo "Response: $RESPONSE_BODY"
                else
                    echo "⚠️  Unexpected status: $HTTP_STATUS"
                    echo "Response: $RESPONSE_BODY"
                fi
            else
                echo "No ID cards found for testing"
            fi
        else
            echo "No kebele tenant found"
        fi
    else
        echo "❌ Could not extract token"
    fi
else
    echo "❌ Kebele leader authentication failed"
    echo "Response: $AUTH_RESPONSE"
fi

echo ""
echo "✅ Kebele leader user creation and testing completed!"
echo ""
echo "🧪 Manual testing steps:"
echo "1. Login with: <EMAIL> / leader123"
echo "2. Go to ID Cards list"
echo "3. Find ID card with 'pending_approval' status"
echo "4. Click Approve button"
echo "5. Should work without 403 error"
echo ""
echo "💡 If still getting 403:"
echo "- Check browser console for detailed error"
echo "- Verify user role is 'kebele_leader'"
echo "- Check backend logs: docker-compose logs backend | grep approval"
