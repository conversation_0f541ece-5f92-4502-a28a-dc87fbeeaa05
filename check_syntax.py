#!/usr/bin/env python3
"""
Simple syntax checker for the working_jar_bridge.py file
"""

import ast
import sys

def check_python_syntax(filename):
    """Check if a Python file has valid syntax"""
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            source_code = f.read()
        
        # Try to parse the AST
        ast.parse(source_code)
        
        print(f"✅ SYNTAX CHECK PASSED: {filename}")
        print("   No syntax errors found")
        return True
        
    except SyntaxError as e:
        print(f"❌ SYNTAX ERROR in {filename}:")
        print(f"   Line {e.lineno}: {e.text.strip() if e.text else 'N/A'}")
        print(f"   Error: {e.msg}")
        return False
        
    except Exception as e:
        print(f"❌ ERROR checking {filename}: {e}")
        return False

if __name__ == "__main__":
    # Check the working_jar_bridge.py file
    filename = "local-biometric-service/working_jar_bridge.py"
    
    print("🔍 CHECKING PYTHON SYNTAX")
    print("=" * 40)
    
    success = check_python_syntax(filename)
    
    if success:
        print("\n🎉 SUCCESS!")
        print("✅ The indentation error has been fixed")
        print("✅ working_jar_bridge.py has valid Python syntax")
        print("✅ The service should now start properly")
        
        print("\n💡 NEXT STEPS:")
        print("1. Run the service using your preferred Python interpreter")
        print("2. Test fingerprint capture with the fixed duplicate detection")
        print("3. Verify that same person cannot register twice")
    else:
        print("\n❌ SYNTAX ERRORS FOUND")
        print("The file needs additional fixes before it can run")
