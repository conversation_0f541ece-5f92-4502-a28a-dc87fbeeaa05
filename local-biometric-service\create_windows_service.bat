@echo off
echo ========================================
echo   GoID Biometric Windows Service Setup
echo ========================================
echo.
echo This script will install GoID Biometric Service as a Windows Service
echo using NSSM (Non-Sucking Service Manager)
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ This script must be run as Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo ✅ Running as Administrator
echo.

REM Set paths
set SERVICE_NAME=GoIDBiometricService
set SERVICE_DISPLAY_NAME=GoID Biometric Service
set SCRIPT_DIR=%~dp0
set PYTHON_EXE=%SCRIPT_DIR%python.exe
set SERVICE_SCRIPT=%SCRIPT_DIR%improved_biometric_service.py
set NSSM_EXE=%SCRIPT_DIR%nssm.exe

REM Check if Python exists
if not exist "%PYTHON_EXE%" (
    echo ❌ Python executable not found at: %PYTHON_EXE%
    echo Please ensure Python is installed or update the path
    pause
    exit /b 1
)

REM Check if service script exists
if not exist "%SERVICE_SCRIPT%" (
    echo ❌ Service script not found at: %SERVICE_SCRIPT%
    pause
    exit /b 1
)

REM Download NSSM if not exists
if not exist "%NSSM_EXE%" (
    echo Downloading NSSM...
    powershell -Command "& {Invoke-WebRequest -Uri 'https://nssm.cc/release/nssm-2.24.zip' -OutFile 'nssm.zip'}"
    powershell -Command "& {Expand-Archive -Path 'nssm.zip' -DestinationPath '.' -Force}"
    copy "nssm-2.24\win64\nssm.exe" "%NSSM_EXE%"
    rmdir /s /q "nssm-2.24"
    del "nssm.zip"
    echo ✅ NSSM downloaded
)

REM Stop and remove existing service if it exists
echo Checking for existing service...
sc query "%SERVICE_NAME%" >nul 2>&1
if %errorLevel% equ 0 (
    echo Stopping existing service...
    "%NSSM_EXE%" stop "%SERVICE_NAME%"
    timeout /t 5 /nobreak >nul
    echo Removing existing service...
    "%NSSM_EXE%" remove "%SERVICE_NAME%" confirm
)

echo.
echo Installing GoID Biometric Service...

REM Install the service
"%NSSM_EXE%" install "%SERVICE_NAME%" "%PYTHON_EXE%" "%SERVICE_SCRIPT%"

REM Configure service parameters
"%NSSM_EXE%" set "%SERVICE_NAME%" DisplayName "%SERVICE_DISPLAY_NAME%"
"%NSSM_EXE%" set "%SERVICE_NAME%" Description "GoID Biometric Fingerprint Capture Service for Kebele Workstations"
"%NSSM_EXE%" set "%SERVICE_NAME%" Start SERVICE_AUTO_START
"%NSSM_EXE%" set "%SERVICE_NAME%" AppDirectory "%SCRIPT_DIR%"

REM Configure service recovery
"%NSSM_EXE%" set "%SERVICE_NAME%" AppThrottle 1500
"%NSSM_EXE%" set "%SERVICE_NAME%" AppRestartDelay 5000
"%NSSM_EXE%" set "%SERVICE_NAME%" AppExit Default Restart

REM Configure logging
"%NSSM_EXE%" set "%SERVICE_NAME%" AppStdout "%SCRIPT_DIR%logs\service_stdout.log"
"%NSSM_EXE%" set "%SERVICE_NAME%" AppStderr "%SCRIPT_DIR%logs\service_stderr.log"
"%NSSM_EXE%" set "%SERVICE_NAME%" AppRotateFiles 1
"%NSSM_EXE%" set "%SERVICE_NAME%" AppRotateOnline 1
"%NSSM_EXE%" set "%SERVICE_NAME%" AppRotateSeconds 86400
"%NSSM_EXE%" set "%SERVICE_NAME%" AppRotateBytes 1048576

REM Create logs directory
if not exist "%SCRIPT_DIR%logs" mkdir "%SCRIPT_DIR%logs"

echo.
echo ✅ Service installed successfully!
echo.
echo Service Details:
echo - Name: %SERVICE_NAME%
echo - Display Name: %SERVICE_DISPLAY_NAME%
echo - Executable: %PYTHON_EXE%
echo - Script: %SERVICE_SCRIPT%
echo - Working Directory: %SCRIPT_DIR%
echo - Logs: %SCRIPT_DIR%logs\
echo.

REM Start the service
echo Starting the service...
"%NSSM_EXE%" start "%SERVICE_NAME%"

REM Wait a moment and check status
timeout /t 5 /nobreak >nul
sc query "%SERVICE_NAME%" | find "RUNNING" >nul
if %errorLevel% equ 0 (
    echo ✅ Service is running successfully!
    echo.
    echo The GoID Biometric Service is now running as a Windows Service.
    echo It will automatically start with Windows and restart if it crashes.
    echo.
    echo Web Interface: http://localhost:8001
    echo Service Management: services.msc
    echo Logs Location: %SCRIPT_DIR%logs\
) else (
    echo ❌ Service failed to start. Check the logs for details.
    echo Logs location: %SCRIPT_DIR%logs\
)

echo.
echo ========================================
echo   Installation Complete
echo ========================================
echo.
echo To manage the service:
echo - Use Windows Services (services.msc)
echo - Or use NSSM commands:
echo   "%NSSM_EXE%" start %SERVICE_NAME%
echo   "%NSSM_EXE%" stop %SERVICE_NAME%
echo   "%NSSM_EXE%" restart %SERVICE_NAME%
echo   "%NSSM_EXE%" remove %SERVICE_NAME% confirm
echo.
pause
