/**
 * RTL Utilities Hook
 * Provides RTL-aware utilities and helpers for components
 */

import { useMemo } from 'react';
import { useLocalization } from '../contexts/LocalizationContext';

/**
 * Hook for RTL-aware styling and utilities
 */
export const useRTLUtils = () => {
  const { currentLanguage, isRTL, getDirection, getDirectionHelpers } = useLocalization();

  const rtlUtils = useMemo(() => {
    const isRtl = isRTL ? isRTL() : false;
    const direction = getDirection ? getDirection() : 'ltr';
    const helpers = getDirectionHelpers ? getDirectionHelpers() : {};

    return {
      // Basic direction info
      isRTL: isRtl,
      direction,
      textAlign: isRtl ? 'right' : 'left',
      
      // Style helpers
      getMarginStart: (value) => ({
        [isRtl ? 'marginRight' : 'marginLeft']: value
      }),
      
      getMarginEnd: (value) => ({
        [isRtl ? 'marginLeft' : 'marginRight']: value
      }),
      
      getPaddingStart: (value) => ({
        [isRtl ? 'paddingRight' : 'paddingLeft']: value
      }),
      
      getPaddingEnd: (value) => ({
        [isRtl ? 'paddingLeft' : 'paddingRight']: value
      }),
      
      getBorderStart: (value) => ({
        [isRtl ? 'borderRight' : 'borderLeft']: value
      }),
      
      getBorderEnd: (value) => ({
        [isRtl ? 'borderLeft' : 'borderRight']: value
      }),
      
      // Position helpers
      getStart: (value) => ({
        [isRtl ? 'right' : 'left']: value
      }),
      
      getEnd: (value) => ({
        [isRtl ? 'left' : 'right']: value
      }),
      
      // Transform helpers
      getTranslateX: (value) => `translateX(${isRtl ? -value : value}px)`,
      
      // Flex helpers
      getFlexDirection: (direction) => {
        if (direction === 'row' && isRtl) return 'row-reverse';
        if (direction === 'row-reverse' && isRtl) return 'row';
        return direction;
      },
      
      // Text direction helpers
      getTextDirection: () => direction,
      getOppositeDirection: () => isRtl ? 'ltr' : 'rtl',
      
      // Icon helpers
      getArrowIcon: (direction) => {
        const iconMap = {
          left: isRtl ? '→' : '←',
          right: isRtl ? '←' : '→',
          up: '↑',
          down: '↓'
        };
        return iconMap[direction] || direction;
      },
      
      // Class name helpers
      getDirectionClass: (baseClass) => `${baseClass} ${baseClass}-${direction}`,
      
      // Animation helpers
      getSlideDirection: (direction) => {
        if (direction === 'left' && isRtl) return 'right';
        if (direction === 'right' && isRtl) return 'left';
        return direction;
      },
      
      // Layout helpers
      getLayoutDirection: () => ({
        direction,
        textAlign: isRtl ? 'right' : 'left'
      }),
      
      // Form helpers
      getInputAlignment: () => ({
        textAlign: isRtl ? 'right' : 'left',
        direction
      }),
      
      // All direction helpers from service
      ...helpers
    };
  }, [currentLanguage, isRTL, getDirection, getDirectionHelpers]);

  return rtlUtils;
};

/**
 * Hook for RTL-aware CSS-in-JS styles
 */
export const useRTLStyles = () => {
  const rtlUtils = useRTLUtils();

  const createRTLStyles = (styles) => {
    if (!rtlUtils.isRTL) {
      return styles;
    }

    const rtlStyles = { ...styles };

    // Convert logical properties
    const propertyMap = {
      marginLeft: 'marginRight',
      marginRight: 'marginLeft',
      paddingLeft: 'paddingRight',
      paddingRight: 'paddingLeft',
      borderLeft: 'borderRight',
      borderRight: 'borderLeft',
      borderLeftWidth: 'borderRightWidth',
      borderRightWidth: 'borderLeftWidth',
      borderLeftColor: 'borderRightColor',
      borderRightColor: 'borderLeftColor',
      borderLeftStyle: 'borderRightStyle',
      borderRightStyle: 'borderLeftStyle',
      left: 'right',
      right: 'left'
    };

    Object.entries(propertyMap).forEach(([ltr, rtl]) => {
      if (styles[ltr] !== undefined) {
        rtlStyles[rtl] = styles[ltr];
        delete rtlStyles[ltr];
      }
    });

    // Handle text alignment
    if (styles.textAlign === 'left') {
      rtlStyles.textAlign = 'right';
    } else if (styles.textAlign === 'right') {
      rtlStyles.textAlign = 'left';
    }

    // Handle transforms
    if (styles.transform && styles.transform.includes('translateX')) {
      rtlStyles.transform = styles.transform.replace(
        /translateX\(([^)]+)\)/g,
        (match, value) => {
          const numValue = parseFloat(value);
          return `translateX(${-numValue}${value.replace(/[0-9.-]/g, '')})`;
        }
      );
    }

    return rtlStyles;
  };

  return {
    ...rtlUtils,
    createRTLStyles,
    rtl: createRTLStyles
  };
};

/**
 * Hook for RTL-aware animations
 */
export const useRTLAnimations = () => {
  const { isRTL } = useRTLUtils();

  const getAnimationDirection = (direction) => {
    if (!isRTL) return direction;
    
    const directionMap = {
      'slide-left': 'slide-right',
      'slide-right': 'slide-left',
      'fade-left': 'fade-right',
      'fade-right': 'fade-left',
      'bounce-left': 'bounce-right',
      'bounce-right': 'bounce-left'
    };

    return directionMap[direction] || direction;
  };

  const getKeyframes = (animationName) => {
    if (!isRTL) return animationName;

    const keyframeMap = {
      slideInLeft: 'slideInRight',
      slideInRight: 'slideInLeft',
      slideOutLeft: 'slideOutRight',
      slideOutRight: 'slideOutLeft'
    };

    return keyframeMap[animationName] || animationName;
  };

  return {
    isRTL,
    getAnimationDirection,
    getKeyframes
  };
};

export default useRTLUtils;
