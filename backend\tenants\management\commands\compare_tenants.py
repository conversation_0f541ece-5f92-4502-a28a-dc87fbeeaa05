from django.core.management.base import BaseCommand
from django_tenants.utils import schema_context
from tenants.models import Tenant
from tenants.models.citizen import Citizen
from idcards.models import IDCard, IDCardTemplate
from django.db import connection


class Command(BaseCommand):
    help = 'Compare tenant 42 and 46 to find differences'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🔍 Comparing tenants 42 and 46...'))
        
        tenant_ids = [42, 46]
        tenant_data = {}
        
        for tenant_id in tenant_ids:
            self.stdout.write(f'\n📋 === ANALYZING TENANT {tenant_id} ===')
            
            try:
                tenant = Tenant.objects.get(id=tenant_id)
                self.stdout.write(f'✅ Tenant {tenant_id} exists: {tenant.name} ({tenant.schema_name})')
                self.stdout.write(f'   - Type: {type(tenant)}')
                self.stdout.write(f'   - Has name: {hasattr(tenant, "name")}')
                self.stdout.write(f'   - Has schema_name: {hasattr(tenant, "schema_name")}')
                
                tenant_info = {
                    'exists': True,
                    'name': getattr(tenant, 'name', 'NO_NAME'),
                    'schema_name': getattr(tenant, 'schema_name', 'NO_SCHEMA'),
                    'type': str(type(tenant)),
                }
                
                # Check schema exists in database
                with connection.cursor() as cursor:
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT 1 FROM information_schema.schemata 
                            WHERE schema_name = %s
                        )
                    """, [tenant.schema_name])
                    schema_exists = cursor.fetchone()[0]
                    tenant_info['schema_exists'] = schema_exists
                    self.stdout.write(f'   - Schema exists in DB: {schema_exists}')
                
                # Check domain configuration
                from django_tenants.models import Domain
                domains = Domain.objects.filter(tenant=tenant)
                tenant_info['domain_count'] = domains.count()
                self.stdout.write(f'   - Domains: {domains.count()}')
                for domain in domains:
                    self.stdout.write(f'     * {domain.domain} (primary: {domain.is_primary})')
                
                # Try schema context
                try:
                    with schema_context(tenant.schema_name):
                        self.stdout.write(f'   - Schema context: ✅ Works')
                        
                        # Check current schema
                        with connection.cursor() as cursor:
                            cursor.execute("SELECT current_schema()")
                            current_schema = cursor.fetchone()[0]
                            self.stdout.write(f'   - Current schema: {current_schema}')
                            tenant_info['current_schema'] = current_schema
                        
                        # Check citizens table
                        try:
                            with connection.cursor() as cursor:
                                cursor.execute(f'SELECT COUNT(*) FROM "{tenant.schema_name}".citizens_citizen')
                                citizen_count_raw = cursor.fetchone()[0]
                                tenant_info['citizens_raw_sql'] = citizen_count_raw
                                self.stdout.write(f'   - Citizens (Raw SQL): {citizen_count_raw}')
                        except Exception as e:
                            tenant_info['citizens_raw_sql'] = f'ERROR: {str(e)}'
                            self.stdout.write(f'   - Citizens (Raw SQL): ERROR - {str(e)}')
                        
                        # Check citizens via ORM
                        try:
                            citizen_count_orm = Citizen.objects.count()
                            tenant_info['citizens_orm'] = citizen_count_orm
                            self.stdout.write(f'   - Citizens (ORM): {citizen_count_orm}')
                            
                            # List some citizens
                            citizens = Citizen.objects.all()[:3]
                            self.stdout.write(f'   - Sample citizens:')
                            for c in citizens:
                                self.stdout.write(f'     * ID {c.id}: {c.get_full_name()}')
                                
                        except Exception as e:
                            tenant_info['citizens_orm'] = f'ERROR: {str(e)}'
                            self.stdout.write(f'   - Citizens (ORM): ERROR - {str(e)}')
                        
                        # Check ID card templates
                        try:
                            template_count = IDCardTemplate.objects.count()
                            tenant_info['templates'] = template_count
                            self.stdout.write(f'   - ID Card Templates: {template_count}')
                        except Exception as e:
                            tenant_info['templates'] = f'ERROR: {str(e)}'
                            self.stdout.write(f'   - ID Card Templates: ERROR - {str(e)}')
                        
                        # Check ID cards
                        try:
                            idcard_count = IDCard.objects.count()
                            tenant_info['idcards'] = idcard_count
                            self.stdout.write(f'   - ID Cards: {idcard_count}')
                        except Exception as e:
                            tenant_info['idcards'] = f'ERROR: {str(e)}'
                            self.stdout.write(f'   - ID Cards: ERROR - {str(e)}')
                            
                except Exception as e:
                    tenant_info['schema_context_error'] = str(e)
                    self.stdout.write(f'   - Schema context: ❌ ERROR - {str(e)}')
                
                tenant_data[tenant_id] = tenant_info
                
            except Tenant.DoesNotExist:
                self.stdout.write(f'❌ Tenant {tenant_id} does not exist!')
                tenant_data[tenant_id] = {'exists': False}
            except Exception as e:
                self.stdout.write(f'❌ Error with tenant {tenant_id}: {str(e)}')
                tenant_data[tenant_id] = {'exists': False, 'error': str(e)}
        
        # Compare the tenants
        self.stdout.write(f'\n🔍 === COMPARISON ===')
        if 42 in tenant_data and 46 in tenant_data:
            tenant_42 = tenant_data[42]
            tenant_46 = tenant_data[46]
            
            # Compare key fields
            comparisons = [
                ('exists', 'Tenant exists'),
                ('type', 'Tenant type'),
                ('schema_exists', 'Schema exists in DB'),
                ('domain_count', 'Domain count'),
                ('current_schema', 'Current schema'),
                ('citizens_raw_sql', 'Citizens (Raw SQL)'),
                ('citizens_orm', 'Citizens (ORM)'),
                ('templates', 'ID Card Templates'),
                ('idcards', 'ID Cards'),
            ]
            
            for field, description in comparisons:
                val_42 = tenant_42.get(field, 'N/A')
                val_46 = tenant_46.get(field, 'N/A')
                
                if val_42 == val_46:
                    self.stdout.write(f'✅ {description}: SAME ({val_42})')
                else:
                    self.stdout.write(f'❌ {description}: DIFFERENT')
                    self.stdout.write(f'   - Tenant 42: {val_42}')
                    self.stdout.write(f'   - Tenant 46: {val_46}')
        
        self.stdout.write(self.style.SUCCESS('\n🎉 Comparison completed!'))
