"""
Comprehensive Test Suite for Duplicate Detection System

This test suite covers:
- Unit tests for duplicate detection service
- Integration tests with FMatcher
- Performance tests
- Real-world scenario testing
- API endpoint testing
"""

import json
import base64
import tempfile
import os
from unittest.mock import patch, MagicMock
from django.test import TestCase, TransactionTestCase
from django.test.client import Client
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django_tenants.test.cases import TenantTestCase
from django_tenants.utils import schema_context

from .duplicate_detection_service import EnhancedDuplicateDetectionService
from .fingerprint_processing import FingerprintProcessor
from citizens.models import Citizen, Biometric
from tenants.models import Tenant

User = get_user_model()


class DuplicateDetectionServiceTests(TestCase):
    """Unit tests for the EnhancedDuplicateDetectionService"""
    
    def setUp(self):
        self.service = EnhancedDuplicateDetectionService()
        cache.clear()  # Clear cache before each test
    
    def test_service_initialization(self):
        """Test that the service initializes correctly"""
        self.assertEqual(self.service.match_threshold, 40)
        self.assertEqual(self.service.cache_timeout, 3600)
        self.assertEqual(self.service.batch_size, 100)
        self.assertIsNotNone(self.service.fmatcher_jar_path)
    
    def test_template_hash_generation(self):
        """Test template hash generation"""
        template1 = "test_template_data_123"
        template2 = "test_template_data_456"
        template3 = "test_template_data_123"  # Same as template1
        
        hash1 = self.service._get_template_hash(template1)
        hash2 = self.service._get_template_hash(template2)
        hash3 = self.service._get_template_hash(template3)
        
        self.assertNotEqual(hash1, hash2)
        self.assertEqual(hash1, hash3)
        self.assertEqual(len(hash1), 32)  # MD5 hash length
    
    def test_exact_duplicate_detection(self):
        """Test detection of exact duplicate templates"""
        # Create test fingerprint data
        test_template = base64.b64encode(json.dumps({
            'version': '2.0',
            'ansi_iso_template': base64.b64encode(b'test_ansi_data').decode(),
            'device_info': {'model': 'Test Device'}
        }).encode()).decode()
        
        fingerprint_data = {
            'left_thumb_fingerprint': test_template,
            'right_thumb_fingerprint': test_template
        }
        
        # Mock the stored templates to include the same template
        mock_templates = [{
            'template_data': test_template,
            'template_hash': self.service._get_template_hash(test_template),
            'thumb_type': 'left',
            'tenant_id': 1,
            'tenant_name': 'Test Tenant',
            'citizen_id': 1,
            'citizen_name': 'Test Citizen',
            'citizen_digital_id': 'TEST001',
            'biometric_id': 1
        }]
        
        with patch.object(self.service, '_get_cached_templates', return_value=mock_templates):
            result = self.service.check_duplicate_registration(fingerprint_data)
        
        self.assertTrue(result['has_duplicates'])
        self.assertEqual(len(result['matches']), 1)
        self.assertEqual(result['matches'][0]['match_score'], 1000.0)
        self.assertEqual(result['matches'][0]['match_quality'], 'Perfect')
    
    def test_no_duplicates_found(self):
        """Test when no duplicates are found"""
        test_template = base64.b64encode(json.dumps({
            'version': '2.0',
            'ansi_iso_template': base64.b64encode(b'unique_ansi_data').decode(),
            'device_info': {'model': 'Test Device'}
        }).encode()).decode()
        
        fingerprint_data = {
            'left_thumb_fingerprint': test_template
        }
        
        # Mock empty stored templates
        with patch.object(self.service, '_get_cached_templates', return_value=[]):
            result = self.service.check_duplicate_registration(fingerprint_data)
        
        self.assertFalse(result['has_duplicates'])
        self.assertEqual(len(result['matches']), 0)
    
    def test_exclude_citizen_functionality(self):
        """Test excluding a specific citizen from duplicate check"""
        test_template = base64.b64encode(json.dumps({
            'version': '2.0',
            'ansi_iso_template': base64.b64encode(b'test_ansi_data').decode(),
            'device_info': {'model': 'Test Device'}
        }).encode()).decode()
        
        fingerprint_data = {
            'left_thumb_fingerprint': test_template
        }
        
        # Mock stored templates with the citizen to exclude
        mock_templates = [{
            'template_data': test_template,
            'template_hash': self.service._get_template_hash(test_template),
            'thumb_type': 'left',
            'tenant_id': 1,
            'tenant_name': 'Test Tenant',
            'citizen_id': 123,  # This citizen will be excluded
            'citizen_name': 'Test Citizen',
            'citizen_digital_id': 'TEST001',
            'biometric_id': 1
        }]
        
        with patch.object(self.service, '_get_cached_templates', return_value=mock_templates):
            # Check without exclusion - should find duplicate
            result1 = self.service.check_duplicate_registration(fingerprint_data)
            self.assertTrue(result1['has_duplicates'])
            
            # Check with exclusion - should not find duplicate
            result2 = self.service.check_duplicate_registration(fingerprint_data, exclude_citizen_id=123)
            self.assertFalse(result2['has_duplicates'])
    
    def test_cache_functionality(self):
        """Test that caching works correctly"""
        mock_templates = [{'test': 'data'}]
        
        with patch.object(self.service, '_get_all_stored_templates', return_value=mock_templates) as mock_get:
            # First call should hit the database
            result1 = self.service._get_cached_templates()
            self.assertEqual(mock_get.call_count, 1)
            
            # Second call should use cache
            result2 = self.service._get_cached_templates()
            self.assertEqual(mock_get.call_count, 1)  # Still 1, not called again
            
            self.assertEqual(result1, result2)
    
    def test_statistics_tracking(self):
        """Test that statistics are tracked correctly"""
        initial_stats = self.service.stats.copy()
        
        # Mock a duplicate check
        with patch.object(self.service, '_get_cached_templates', return_value=[]):
            self.service.check_duplicate_registration({'left_thumb_fingerprint': 'test'})
        
        # Check that stats were updated
        self.assertEqual(self.service.stats['total_checks'], initial_stats['total_checks'] + 1)
        self.assertGreater(self.service.stats['processing_time'], initial_stats['processing_time'])


class FMatcherIntegrationTests(TestCase):
    """Integration tests with FMatcher.jar"""
    
    def setUp(self):
        self.service = EnhancedDuplicateDetectionService()
    
    def test_fmatcher_availability(self):
        """Test that FMatcher.jar is available"""
        self.assertTrue(os.path.exists(self.service.fmatcher_jar_path), 
                       f"FMatcher.jar not found at {self.service.fmatcher_jar_path}")
    
    def test_fmatcher_comparison(self):
        """Test FMatcher comparison with sample data"""
        # Create sample ANSI template data
        sample_data1 = b'ANSI_TEMPLATE_DATA_1' + b'\x00' * 100
        sample_data2 = b'ANSI_TEMPLATE_DATA_2' + b'\x00' * 100
        
        # Test comparison
        score = self.service._compare_with_fmatcher(sample_data1, sample_data2)
        
        # Score should be a number (even if low for test data)
        self.assertIsInstance(score, (int, float))
        self.assertGreaterEqual(score, 0)
    
    def test_fmatcher_identical_templates(self):
        """Test FMatcher with identical templates"""
        sample_data = b'IDENTICAL_ANSI_TEMPLATE' + b'\x00' * 100
        
        score = self.service._compare_with_fmatcher(sample_data, sample_data)
        
        # Identical templates should have a high score
        self.assertIsInstance(score, (int, float))
        # Note: Actual score depends on FMatcher implementation
    
    def test_fmatcher_error_handling(self):
        """Test FMatcher error handling with invalid data"""
        invalid_data1 = b'invalid'
        invalid_data2 = b'data'
        
        score = self.service._compare_with_fmatcher(invalid_data1, invalid_data2)
        
        # Should handle errors gracefully
        self.assertIsNone(score)


class DuplicateDetectionAPITests(TestCase):
    """Test the duplicate detection API endpoints"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.client.force_login(self.user)
    
    def test_check_duplicates_endpoint(self):
        """Test the check duplicates API endpoint"""
        test_data = {
            'left_thumb_fingerprint': 'test_template_data',
            'right_thumb_fingerprint': 'test_template_data'
        }
        
        response = self.client.post(
            '/api/biometrics/check-duplicates/',
            data=json.dumps(test_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertTrue(data['success'])
        self.assertIn('data', data)
    
    def test_real_time_duplicate_check_endpoint(self):
        """Test the real-time duplicate check endpoint"""
        test_data = {
            'fingerprint_data': {
                'left_thumb_fingerprint': 'test_template_data'
            },
            'citizen_data': {
                'first_name': 'John',
                'last_name': 'Doe'
            }
        }
        
        response = self.client.post(
            '/api/biometrics/real-time-duplicate-check/',
            data=json.dumps(test_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertTrue(data['success'])
        self.assertIn('duplicate_check', data['data'])
        self.assertIn('fraud_analysis', data['data'])
    
    def test_duplicate_detection_stats_endpoint(self):
        """Test the duplicate detection statistics endpoint"""
        response = self.client.get('/api/biometrics/duplicate-detection-stats/')
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertTrue(data['success'])
        self.assertIn('service_stats', data['data'])
        self.assertIn('configuration', data['data'])
    
    def test_clear_cache_endpoint(self):
        """Test the clear cache endpoint"""
        response = self.client.post('/api/biometrics/clear-duplicate-cache/')
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertTrue(data['success'])
        self.assertIn('cache cleared', data['message'].lower())


class PerformanceTests(TestCase):
    """Performance tests for duplicate detection"""
    
    def setUp(self):
        self.service = EnhancedDuplicateDetectionService()
    
    def test_large_template_set_performance(self):
        """Test performance with a large set of templates"""
        import time
        
        # Create mock large template set
        large_template_set = []
        for i in range(1000):
            large_template_set.append({
                'template_data': f'template_data_{i}',
                'template_hash': self.service._get_template_hash(f'template_data_{i}'),
                'thumb_type': 'left',
                'tenant_id': 1,
                'tenant_name': 'Test Tenant',
                'citizen_id': i,
                'citizen_name': f'Citizen {i}',
                'citizen_digital_id': f'TEST{i:03d}',
                'biometric_id': i
            })
        
        test_fingerprint = {'left_thumb_fingerprint': 'new_template_data'}
        
        with patch.object(self.service, '_get_cached_templates', return_value=large_template_set):
            start_time = time.time()
            result = self.service.check_duplicate_registration(test_fingerprint)
            end_time = time.time()
        
        processing_time = end_time - start_time
        
        # Should complete within reasonable time (adjust threshold as needed)
        self.assertLess(processing_time, 5.0, f"Processing took {processing_time:.2f}s, which is too slow")
        self.assertFalse(result['has_duplicates'])  # Should not find duplicates in mock data
