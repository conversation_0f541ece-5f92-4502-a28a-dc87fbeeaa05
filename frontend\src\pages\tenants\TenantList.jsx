import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useLocalization } from '../../contexts/LocalizationContext';
import axios from '../../utils/axios';
import {
  Box,
  Button,
  Card,
  CardContent,
  Typography,
  TextField,
  InputAdornment,
  IconButton,
  Grid,
  Chip,
  Pagination,
  CircularProgress,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Snackbar,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Business as BusinessIcon,
  LocationCity as CityIcon,
  Apartment as SubcityIcon,
  Home as KebeleIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';

const TenantList = () => {
  const navigate = useNavigate();
  const { t } = useLocalization();
  const [tenants, setTenants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [typeFilter, setTypeFilter] = useState('');

  // Delete functionality state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [tenantToDelete, setTenantToDelete] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  const fetchTenants = async () => {
    try {
      setLoading(true);
      setError('');
      const response = await axios.get('/api/tenants/');
      const data = response.data.results || response.data;
      const tenantsArray = Array.isArray(data) ? data : [];
      setTenants(tenantsArray);

      // Calculate total pages safely
      const totalCount = response.data.count || tenantsArray.length || 0;
      const calculatedPages = Math.max(1, Math.ceil(totalCount / 10));
      setTotalPages(calculatedPages);
    } catch (error) {
      console.error('Failed to fetch tenants:', error);
      setError('Failed to load tenants. Please try again.');
      setTenants([]);
      setTotalPages(1); // Set safe default for pagination
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTenants();
  }, []);

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    // In a real implementation, this would trigger an API call with the search term
  };

  const handlePageChange = (event, value) => {
    setPage(value);
    // In a real implementation, this would fetch the next page of data
  };

  const handleTypeFilterChange = (type) => {
    setTypeFilter(type === typeFilter ? '' : type);
    // In a real implementation, this would trigger an API call with the filter
  };

  const handleViewTenant = (id) => {
    navigate(`/tenants/${id}/details`);
  };

  const handleEditTenant = (id) => {
    navigate(`/tenants/${id}/edit`);
  };

  const handleDeleteTenant = (tenant) => {
    setTenantToDelete(tenant);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!tenantToDelete) return;

    try {
      setDeleteLoading(true);
      await axios.delete(`/api/tenants/${tenantToDelete.id}/`);

      setSnackbar({
        open: true,
        message: `Tenant "${tenantToDelete.name}" has been successfully deleted.`,
        severity: 'success'
      });

      // Refresh the tenant list
      fetchTenants();

    } catch (error) {
      console.error('Failed to delete tenant:', error);
      setSnackbar({
        open: true,
        message: error.response?.data?.detail || 'Failed to delete tenant. Please try again.',
        severity: 'error'
      });
    } finally {
      setDeleteLoading(false);
      setDeleteDialogOpen(false);
      setTenantToDelete(null);
    }
  };

  const cancelDelete = () => {
    setDeleteDialogOpen(false);
    setTenantToDelete(null);
  };

  const handleSnackbarClose = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'city':
        return <CityIcon />;
      case 'subcity':
        return <SubcityIcon />;
      case 'kebele':
        return <KebeleIcon />;
      default:
        return <BusinessIcon />;
    }
  };

  const getTypeChip = (type) => {
    switch (type) {
      case 'city':
        return <Chip icon={<CityIcon />} label="City" color="primary" size="small" />;
      case 'subcity':
        return <Chip icon={<SubcityIcon />} label="Sub City" color="secondary" size="small" />;
      case 'kebele':
        return <Chip icon={<KebeleIcon />} label="Kebele" color="success" size="small" />;
      default:
        return <Chip icon={<BusinessIcon />} label={type} size="small" />;
    }
  };

  const filteredTenants = tenants.filter(tenant =>
    (typeFilter ? tenant.type === typeFilter : true) &&
    (searchTerm ?
      tenant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      tenant.schema_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (tenant.parent_name && tenant.parent_name.toLowerCase().includes(searchTerm.toLowerCase()))
      : true
    )
  );

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Tenants
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Tooltip title={loading ? "Loading..." : "Refresh tenant list"}>
            <span>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={fetchTenants}
                disabled={loading}
              >
                Refresh
              </Button>
            </span>
          </Tooltip>
          <Button
            variant="outlined"
            startIcon={<BusinessIcon />}
            onClick={() => navigate('/tenants/register')}
          >
            Register New Tenant
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => navigate('/tenants/create')}
          >
            Add New Tenant
          </Button>
        </Box>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Search and Filter Bar */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="Search tenants by name, schema, or parent..."
                value={searchTerm}
                onChange={handleSearch}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Chip
                  icon={<CityIcon />}
                  label="City"
                  color={typeFilter === 'city' ? 'primary' : 'default'}
                  onClick={() => handleTypeFilterChange('city')}
                  clickable
                />
                <Chip
                  icon={<SubcityIcon />}
                  label="Sub City"
                  color={typeFilter === 'subcity' ? 'primary' : 'default'}
                  onClick={() => handleTypeFilterChange('subcity')}
                  clickable
                />
                <Chip
                  icon={<KebeleIcon />}
                  label="Kebele"
                  color={typeFilter === 'kebele' ? 'primary' : 'default'}
                  onClick={() => handleTypeFilterChange('kebele')}
                  clickable
                />
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Tenants Table */}
      <TableContainer component={Paper} sx={{ mb: 4 }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>{t('name', 'Name')}</TableCell>
              <TableCell>{t('tenant_type', 'Type')}</TableCell>
              <TableCell>{t('parent', 'Parent')}</TableCell>
              <TableCell>{t('schema_name', 'Schema Name')}</TableCell>
              <TableCell>{t('created_on', 'Created On')}</TableCell>
              <TableCell align="right">{t('actions', 'Actions')}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredTenants.length > 0 ? (
              filteredTenants.map((tenant) => (
                <TableRow key={tenant.id}>
                  <TableCell>{tenant.name}</TableCell>
                  <TableCell>{getTypeChip(tenant.type)}</TableCell>
                  <TableCell>{tenant.parent_name || '-'}</TableCell>
                  <TableCell>{tenant.schema_name}</TableCell>
                  <TableCell>{new Date(tenant.created_on).toLocaleDateString()}</TableCell>
                  <TableCell align="right">
                    <Tooltip title="View Tenant">
                      <IconButton
                        color="primary"
                        onClick={() => handleViewTenant(tenant.id)}
                      >
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Edit Tenant">
                      <IconButton
                        color="secondary"
                        onClick={() => handleEditTenant(tenant.id)}
                      >
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete Tenant">
                      <IconButton
                        color="error"
                        onClick={() => handleDeleteTenant(tenant)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} align="center" sx={{ py: 4 }}>
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    {tenants.length === 0 ? 'No tenants found' : 'No tenants match your search criteria'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {tenants.length === 0
                      ? 'Get started by registering your first tenant.'
                      : 'Try adjusting your search terms or filters.'}
                  </Typography>
                  {tenants.length === 0 && (
                    <Button
                      variant="contained"
                      startIcon={<AddIcon />}
                      onClick={() => navigate('/tenants/register')}
                      sx={{ mt: 2 }}
                    >
                      Register First Tenant
                    </Button>
                  )}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination */}
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <Pagination
          count={totalPages}
          page={page}
          onChange={handlePageChange}
          color="primary"
        />
      </Box>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={cancelDelete}
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <DialogTitle id="delete-dialog-title">
          Delete Tenant
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="delete-dialog-description">
            Are you sure you want to delete the tenant "{tenantToDelete?.name}"?
            <br /><br />
            <strong>Warning:</strong> This action will permanently delete:
            <br />• The tenant record
            <br />• The tenant's database schema
            <br />• All data associated with this tenant
            <br /><br />
            This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={cancelDelete} disabled={deleteLoading}>
            Cancel
          </Button>
          <Button
            onClick={confirmDelete}
            color="error"
            variant="contained"
            disabled={deleteLoading}
            startIcon={deleteLoading ? <CircularProgress size={20} /> : <DeleteIcon />}
          >
            {deleteLoading ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default TenantList;
