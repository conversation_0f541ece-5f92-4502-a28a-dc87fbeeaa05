# Distributed Biometric Deployment Guide

This guide explains how to deploy GoID with biometric services across multiple machines.

## Architecture Overview

```
Machine A (Biometric Device)          Machine B (Docker Deployment)
┌─────────────────────────────┐      ┌─────────────────────────────┐
│  🖐️ Futronic FS88H Device   │      │  🐳 Docker Containers       │
│  📡 Biometric Service       │◄────►│  🌐 GoID Web Application    │
│  Port: 8001                 │      │  🎨 Frontend (Port 3000)    │
│                             │      │  🔧 Backend (Port 8000)     │
└─────────────────────────────┘      └─────────────────────────────┘
```

## Setup Instructions

### Machine A: Biometric Service Setup

1. **Install Python and Dependencies**
   ```bash
   # Ensure Python 3.7+ is installed
   python --version
   
   # Install required packages
   pip install flask flask-cors
   ```

2. **Copy Futronic SDK Files**
   - Place `ftrScanAPI.dll`, `ftrMathAPI.dll`, and `FTRAPI.dll` in the `local-biometric-service` folder
   - Connect the Futronic FS88H device via USB

3. **Start the Biometric Service**
   ```bash
   cd local-biometric-service
   python real_capture_only.py
   ```
   
   The service will:
   - ✅ Start capture mode (LED blinking)
   - ✅ Monitor for finger placement
   - ✅ Capture real fingerprints
   - ✅ Stop capture mode
   - 🌐 Run on `http://localhost:8001`

4. **Test the Service**
   - Open `http://localhost:8001` in a browser
   - Test fingerprint capture to ensure it works

### Machine B: Docker Deployment Setup

1. **Configure Biometric Service URL**
   
   **Option A: Automatic Detection (Recommended)**
   - The frontend will automatically try common IP addresses
   - No configuration needed
   
   **Option B: Manual Configuration**
   ```bash
   # Edit frontend/.env.production
   VITE_BIOMETRIC_SERVICE_URL=http://[MACHINE_A_IP]:8001
   
   # Example:
   VITE_BIOMETRIC_SERVICE_URL=http://*************:8001
   ```

2. **Deploy with Docker**
   ```bash
   # Production deployment
   docker-compose -f docker-compose.production.yml up -d
   
   # Or development deployment
   docker-compose up -d
   ```

3. **Access the Application**
   - Frontend: `http://localhost:3000`
   - Backend: `http://localhost:8000`

## Network Configuration

### Find Machine A's IP Address

**Windows:**
```cmd
ipconfig
```

**Linux/Mac:**
```bash
ifconfig
ip addr show
```

### Firewall Configuration

**Machine A (Biometric Service):**
- Allow incoming connections on port 8001
- Windows: Add firewall rule for port 8001
- Linux: `sudo ufw allow 8001`

**Machine B (Docker):**
- Ensure it can reach Machine A on port 8001
- Test: `telnet [MACHINE_A_IP] 8001`

## Testing the Setup

### 1. Test Biometric Service Connectivity

From Machine B, test if the biometric service is reachable:

```bash
# Test device status
curl http://[MACHINE_A_IP]:8001/api/device/status

# Expected response:
{
  "success": true,
  "device_connected": true,
  "service_type": "real_capture_only"
}
```

### 2. Test Frontend Integration

1. Open the GoID frontend on Machine B
2. Navigate to citizen registration
3. Click on fingerprint capture
4. The system should:
   - ✅ Detect the biometric service on Machine A
   - ✅ Show "Device Connected" status
   - ✅ Allow fingerprint capture

### 3. Test Real Fingerprint Capture

1. Place finger on the Futronic device (Machine A)
2. Click capture in the frontend (Machine B)
3. Verify:
   - ✅ LED starts blinking on Machine A
   - ✅ Finger detection works
   - ✅ Capture completes successfully
   - ✅ LED stops blinking

## Troubleshooting

### Frontend Can't Find Biometric Service

**Symptoms:**
- "Biometric device service not available" error
- Device status shows as disconnected

**Solutions:**
1. Check network connectivity: `ping [MACHINE_A_IP]`
2. Verify biometric service is running: `curl http://[MACHINE_A_IP]:8001/api/device/status`
3. Check firewall settings on both machines
4. Set explicit biometric service URL in `.env.production`

### Fingerprint Capture Fails

**Symptoms:**
- Device connects but capture fails
- LED doesn't blink or doesn't stop blinking

**Solutions:**
1. Check Futronic SDK files are present
2. Verify device is properly connected via USB
3. Test capture directly on Machine A: `http://localhost:8001`
4. Check device permissions and drivers

### CORS Errors

**Symptoms:**
- Network requests blocked by browser
- "Access-Control-Allow-Origin" errors

**Solutions:**
- The biometric service includes CORS headers
- Ensure both machines are on the same network
- Check browser security settings

## Production Deployment Tips

1. **Static IP Addresses**: Use static IPs for both machines
2. **Service Auto-Start**: Configure biometric service to start automatically
3. **Monitoring**: Set up health checks for both services
4. **Backup**: Keep backup copies of SDK files and configurations
5. **Security**: Use VPN or secure network for production deployments

## Service Endpoints

### Biometric Service (Machine A - Port 8001)

- `GET /api/device/status` - Check device status
- `POST /api/device/initialize` - Initialize device
- `POST /api/capture/fingerprint` - Capture fingerprint
- `GET /` - Web interface for testing

### GoID Application (Machine B)

- `http://localhost:3000` - Frontend interface
- `http://localhost:8000` - Backend API
- `http://localhost:5432` - PostgreSQL database
