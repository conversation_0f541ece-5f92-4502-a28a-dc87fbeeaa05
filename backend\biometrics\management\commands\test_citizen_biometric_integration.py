"""
Management command to test citizen registration with biometric integration.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from django_tenants.utils import schema_context, get_tenant_model
from biometrics.device_integration import get_device
from tenants.models.citizen import Citizen, Biometric


class Command(BaseCommand):
    help = 'Test citizen registration with biometric integration'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant',
            type=str,
            help='Tenant schema name to use for testing (default: first kebele tenant)',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🧪 Testing Citizen Registration with Biometric Integration')
        )
        self.stdout.write('=' * 70)

        # Get tenant
        tenant_schema = options.get('tenant')
        if not tenant_schema:
            Tenant = get_tenant_model()
            kebele_tenants = Tenant.objects.filter(type='kebele')
            if not kebele_tenants.exists():
                self.stdout.write(
                    self.style.ERROR('❌ No kebele tenants found. Please create a kebele tenant first.')
                )
                return
            tenant = kebele_tenants.first()
            tenant_schema = tenant.schema_name
        else:
            try:
                Tenant = get_tenant_model()
                tenant = Tenant.objects.get(schema_name=tenant_schema)
            except Tenant.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'❌ Tenant with schema "{tenant_schema}" not found.')
                )
                return

        self.stdout.write(f'📍 Using tenant: {tenant.name} (schema: {tenant_schema})')

        # Test within tenant context
        with schema_context(tenant_schema):
            self.test_citizen_creation_with_biometrics()

        self.stdout.write(
            self.style.SUCCESS('\n✅ Citizen biometric integration test completed successfully!')
        )

    def test_citizen_creation_with_biometrics(self):
        """Test creating a citizen with biometric data."""
        self.stdout.write('\n👤 Testing Citizen Creation with Biometrics...')
        
        try:
            # Initialize device
            device = get_device()
            device.initialize()
            
            # Capture fingerprints
            self.stdout.write('  📸 Capturing fingerprints...')
            left_template = device.capture_fingerprint('left')
            right_template = device.capture_fingerprint('right')
            
            self.stdout.write(f'    Left thumb quality: {left_template.quality_score}%')
            self.stdout.write(f'    Right thumb quality: {right_template.quality_score}%')
            
            # Create citizen
            self.stdout.write('  👤 Creating citizen record...')
            citizen = Citizen.objects.create(
                first_name='Test',
                last_name='Biometric',
                date_of_birth='1990-01-01',
                gender='male',
                phone='+251911000001',
                email='<EMAIL>'
            )
            
            self.stdout.write(f'    Created citizen: {citizen.first_name} {citizen.last_name}')
            self.stdout.write(f'    Digital ID: {citizen.digital_id}')
            
            # Create biometric record
            self.stdout.write('  🔐 Creating biometric record...')
            biometric = Biometric.objects.create(
                citizen=citizen,
                left_thumb_fingerprint=left_template.template_data,
                right_thumb_fingerprint=right_template.template_data
            )
            
            self.stdout.write(f'    Biometric record ID: {biometric.id}')
            
            # Verify relationships
            self.stdout.write('  🔍 Verifying relationships...')
            
            # Check citizen has biometric record
            citizen_biometric = getattr(citizen, 'biometric_record', None)
            if citizen_biometric:
                self.stdout.write('    ✅ Citizen has biometric record')
            else:
                self.stdout.write('    ❌ Citizen missing biometric record')
            
            # Check biometric has citizen
            if biometric.citizen == citizen:
                self.stdout.write('    ✅ Biometric record linked to citizen')
            else:
                self.stdout.write('    ❌ Biometric record not linked to citizen')
            
            # Test fingerprint validation
            self.stdout.write('  🔍 Testing fingerprint validation...')
            left_valid, left_message = device.validate_quality(left_template)
            right_valid, right_message = device.validate_quality(right_template)
            
            self.stdout.write(f'    Left thumb validation: {left_valid} - {left_message}')
            self.stdout.write(f'    Right thumb validation: {right_valid} - {right_message}')
            
            # Test duplicate detection
            self.stdout.write('  🔍 Testing duplicate detection...')
            from biometrics.fingerprint_processing import FingerprintProcessor
            
            processor = FingerprintProcessor()
            duplicate_result = processor.check_duplicates({
                'left_thumb_fingerprint': left_template.template_data,
                'right_thumb_fingerprint': right_template.template_data
            })
            
            if duplicate_result['has_duplicates']:
                self.stdout.write(f'    ⚠️ Found {duplicate_result["total_matches"]} potential duplicates')
                for match in duplicate_result['matches']:
                    self.stdout.write(f'      - {match["citizen_name"]} in {match["tenant_name"]}')
            else:
                self.stdout.write('    ✅ No duplicates found - fingerprints are unique')
            
            # Test statistics
            self.stdout.write('  📊 Testing biometric statistics...')
            stats = processor.get_biometric_statistics()
            
            if 'error' in stats:
                self.stdout.write(f'    ⚠️ Statistics error: {stats["error"]}')
            else:
                self.stdout.write(f'    Total citizens: {stats["total_citizens"]}')
                self.stdout.write(f'    Biometric records: {stats["total_biometric_records"]}')
                self.stdout.write(f'    Coverage: {stats["biometric_coverage_percent"]}%')
            
            # Cleanup test data
            self.stdout.write('  🧹 Cleaning up test data...')
            biometric.delete()
            citizen.delete()
            self.stdout.write('    ✅ Test data cleaned up')
            
            self.stdout.write(self.style.SUCCESS('  ✅ Citizen biometric integration test passed'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ Citizen biometric integration test failed: {e}'))
            
            # Try to cleanup on error
            try:
                if 'biometric' in locals():
                    biometric.delete()
                if 'citizen' in locals():
                    citizen.delete()
                self.stdout.write('    🧹 Cleaned up test data after error')
            except:
                pass
