#!/usr/bin/env python3
"""
Check Database Templates

This script directly checks the database for fingerprint templates
without requiring API authentication.
"""

import os
import sys
import django
from datetime import datetime

# Setup Django environment
sys.path.append('backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django_tenants.utils import schema_context, get_tenant_model
from citizens.models import Citizen, Biometric


def check_templates_in_database():
    """Check how many fingerprint templates are actually stored"""
    print("🗄️ Checking Database for Fingerprint Templates")
    print("=" * 60)
    
    try:
        Tenant = get_tenant_model()
        tenants = Tenant.objects.filter(type='kebele')
        
        total_citizens = 0
        total_biometrics = 0
        total_left_thumbs = 0
        total_right_thumbs = 0
        
        print(f"Found {len(tenants)} kebele tenants:")
        
        for tenant in tenants:
            try:
                with schema_context(tenant.schema_name):
                    citizens = Citizen.objects.all()
                    biometrics = Biometric.objects.all()
                    
                    left_thumbs = biometrics.filter(
                        left_thumb_fingerprint__isnull=False
                    ).exclude(left_thumb_fingerprint='')
                    
                    right_thumbs = biometrics.filter(
                        right_thumb_fingerprint__isnull=False
                    ).exclude(right_thumb_fingerprint='')
                    
                    tenant_citizens = len(citizens)
                    tenant_biometrics = len(biometrics)
                    tenant_left = len(left_thumbs)
                    tenant_right = len(right_thumbs)
                    
                    print(f"\n📍 Tenant: {tenant.name} ({tenant.schema_name})")
                    print(f"   Citizens: {tenant_citizens}")
                    print(f"   Biometric Records: {tenant_biometrics}")
                    print(f"   Left Thumb Templates: {tenant_left}")
                    print(f"   Right Thumb Templates: {tenant_right}")
                    
                    # Show sample template data
                    if tenant_left > 0:
                        sample = left_thumbs.first()
                        template_preview = sample.left_thumb_fingerprint[:100] + "..." if len(sample.left_thumb_fingerprint) > 100 else sample.left_thumb_fingerprint
                        print(f"   Sample Template: {template_preview}")
                    
                    total_citizens += tenant_citizens
                    total_biometrics += tenant_biometrics
                    total_left_thumbs += tenant_left
                    total_right_thumbs += tenant_right
                    
            except Exception as e:
                print(f"   ❌ Error checking {tenant.name}: {e}")
        
        print(f"\n📊 TOTAL SUMMARY:")
        print(f"   Total Citizens: {total_citizens}")
        print(f"   Total Biometric Records: {total_biometrics}")
        print(f"   Total Left Thumb Templates: {total_left_thumbs}")
        print(f"   Total Right Thumb Templates: {total_right_thumbs}")
        print(f"   Total Templates: {total_left_thumbs + total_right_thumbs}")
        
        if total_left_thumbs + total_right_thumbs == 0:
            print("\n🚨 NO FINGERPRINT TEMPLATES FOUND!")
            print("   This explains why duplicate detection isn't working.")
            print("   You need to register citizens with fingerprints first.")
            return False
        else:
            print(f"\n✅ Found {total_left_thumbs + total_right_thumbs} fingerprint templates")
            print("   Templates exist - duplicate detection should work")
            return True
            
    except Exception as e:
        print(f"❌ Database check failed: {e}")
        return False


def test_template_format():
    """Check if existing templates have the correct format"""
    print("\n📋 Checking Template Format")
    print("=" * 60)
    
    try:
        Tenant = get_tenant_model()
        tenants = Tenant.objects.filter(type='kebele')
        
        valid_templates = 0
        invalid_templates = 0
        format_issues = []
        
        for tenant in tenants:
            try:
                with schema_context(tenant.schema_name):
                    biometrics = Biometric.objects.filter(
                        left_thumb_fingerprint__isnull=False
                    ).exclude(left_thumb_fingerprint='')[:5]  # Check first 5
                    
                    for biometric in biometrics:
                        try:
                            template_data = biometric.left_thumb_fingerprint
                            
                            # Try to parse as JSON
                            import json
                            import base64
                            
                            # Check if it's base64 encoded JSON
                            try:
                                decoded = json.loads(base64.b64decode(template_data + '==').decode())
                                if 'ansi_iso_template' in decoded:
                                    valid_templates += 1
                                    print(f"   ✅ Valid template format: Citizen {biometric.citizen.id}")
                                else:
                                    invalid_templates += 1
                                    format_issues.append(f"Missing ansi_iso_template: Citizen {biometric.citizen.id}")
                            except:
                                # Try direct JSON parse
                                decoded = json.loads(template_data)
                                if 'ansi_iso_template' in decoded:
                                    valid_templates += 1
                                    print(f"   ✅ Valid template format: Citizen {biometric.citizen.id}")
                                else:
                                    invalid_templates += 1
                                    format_issues.append(f"Missing ansi_iso_template: Citizen {biometric.citizen.id}")
                                    
                        except Exception as e:
                            invalid_templates += 1
                            format_issues.append(f"Invalid JSON format: Citizen {biometric.citizen.id} - {e}")
                            
            except Exception as e:
                print(f"   ❌ Error checking templates in {tenant.name}: {e}")
        
        print(f"\n📊 Template Format Summary:")
        print(f"   Valid Templates: {valid_templates}")
        print(f"   Invalid Templates: {invalid_templates}")
        
        if format_issues:
            print(f"\n⚠️ Format Issues Found:")
            for issue in format_issues[:5]:  # Show first 5
                print(f"     - {issue}")
        
        return invalid_templates == 0
        
    except Exception as e:
        print(f"❌ Template format check failed: {e}")
        return False


def create_test_template():
    """Create a test template to verify the duplicate detection system"""
    print("\n🧪 Creating Test Template for Duplicate Detection")
    print("=" * 60)
    
    try:
        import json
        import base64
        
        # Create a test ANSI template
        test_ansi_data = b'TEST_ANSI_TEMPLATE_DATA_' + b'\x00' * 200
        
        # Create the template in the correct format
        template_data = {
            'version': '2.0',
            'ansi_iso_template': base64.b64encode(test_ansi_data).decode(),
            'device_info': {
                'model': 'Futronic FS88H',
                'serial': 'TEST001'
            },
            'thumb_type': 'left',
            'capture_time': datetime.now().isoformat(),
            'quality_metrics': {
                'clarity': 85
            },
            'minutiae_points': [
                {'x': 100, 'y': 150, 'angle': 45, 'type': 'ridge_ending', 'quality': 0.9},
                {'x': 200, 'y': 250, 'angle': 90, 'type': 'bifurcation', 'quality': 0.8}
            ]
        }
        
        # Encode as base64 JSON (GoID format)
        encoded_template = base64.b64encode(json.dumps(template_data).encode()).decode()
        
        print(f"   Created test template: {len(encoded_template)} characters")
        print(f"   Template preview: {encoded_template[:100]}...")
        
        # Test the duplicate detection service with this template
        from biometrics.duplicate_detection_service import EnhancedDuplicateDetectionService
        
        service = EnhancedDuplicateDetectionService()
        
        # Test with identical templates (should detect duplicate)
        result = service.check_duplicate_registration({
            'left_thumb_fingerprint': encoded_template,
            'right_thumb_fingerprint': encoded_template
        })
        
        print(f"\n🔍 Duplicate Detection Test Result:")
        print(f"   Has Duplicates: {result['has_duplicates']}")
        print(f"   Total Matches: {result['total_matches']}")
        print(f"   Message: {result['message']}")
        
        if result['has_duplicates']:
            print("   ✅ Duplicate detection is working!")
            for match in result['matches']:
                print(f"     - Match Score: {match['match_score']}")
                print(f"     - Match Quality: {match['match_quality']}")
        else:
            print("   ⚠️ No duplicates detected (expected with test data)")
        
        return True
        
    except Exception as e:
        print(f"❌ Test template creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main function"""
    print("🔍 DATABASE TEMPLATE CHECKER")
    print("=" * 60)
    print(f"Check started at: {datetime.now().isoformat()}")
    
    # Check if templates exist
    templates_exist = check_templates_in_database()
    
    if templates_exist:
        # Check template format
        format_valid = test_template_format()
        
        if format_valid:
            print("\n✅ Templates exist and have valid format")
            print("   Duplicate detection should be working")
        else:
            print("\n⚠️ Templates exist but have format issues")
            print("   This may cause duplicate detection problems")
    else:
        print("\n🚨 NO TEMPLATES FOUND - Creating test template")
        create_test_template()
    
    print("\n🎯 NEXT STEPS:")
    if not templates_exist:
        print("1. Register citizens with fingerprints using the frontend")
        print("2. Ensure local biometric service is running on port 5000")
        print("3. Test fingerprint capture: http://localhost:3000/citizens/register")
    else:
        print("1. Test duplicate detection API with authentication")
        print("2. Check Django logs for any errors")
        print("3. Verify middleware is properly configured")
    
    return 0


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
