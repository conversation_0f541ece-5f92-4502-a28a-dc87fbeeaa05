@echo off
echo Starting GoID Backend on Windows Host for USB Device Access
echo ============================================================

cd backend

echo Installing Python dependencies...
pip install -r requirements.txt

echo Setting environment variables...
set DJANGO_SETTINGS_MODULE=goid.settings
set DEBUG=True
set DATABASE_URL=postgresql://goid_user:goid_password@localhost:5432/goid_db
set REDIS_URL=redis://localhost:6379/0

echo Starting Django development server...
python manage.py runserver 0.0.0.0:8000

pause
