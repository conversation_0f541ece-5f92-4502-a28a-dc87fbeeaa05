"""
Management command to test the fraud detection system.
This command creates test scenarios to demonstrate fraud detection capabilities.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from django_tenants.utils import schema_context
from tenants.models import Tenant
from citizens.models import Citizen, Biometric
from idcards.fraud_detection import fraud_detection_service
from datetime import date
import random
import string


class Command(BaseCommand):
    help = 'Test fraud detection system with various scenarios'

    def add_arguments(self, parser):
        parser.add_argument(
            '--scenario',
            type=str,
            choices=['digital_id_duplicate', 'personal_info_match', 'biometric_match', 'all'],
            default='all',
            help='Which fraud scenario to test'
        )
        parser.add_argument(
            '--cleanup',
            action='store_true',
            help='Clean up test data after running tests'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🔍 Starting Fraud Detection System Tests'))
        
        scenario = options['scenario']
        cleanup = options['cleanup']
        
        # Get kebele tenants for testing
        kebele_tenants = list(Tenant.objects.filter(type='kebele')[:3])
        
        if len(kebele_tenants) < 2:
            self.stdout.write(self.style.ERROR('❌ Need at least 2 kebele tenants for testing'))
            return
        
        self.stdout.write(f'📋 Using tenants: {[t.name for t in kebele_tenants]}')
        
        test_results = {}
        
        if scenario in ['digital_id_duplicate', 'all']:
            test_results['digital_id_duplicate'] = self.test_digital_id_duplicate(kebele_tenants)
        
        if scenario in ['personal_info_match', 'all']:
            test_results['personal_info_match'] = self.test_personal_info_match(kebele_tenants)
        
        if scenario in ['biometric_match', 'all']:
            test_results['biometric_match'] = self.test_biometric_match(kebele_tenants)
        
        # Display results
        self.display_test_results(test_results)
        
        if cleanup:
            self.cleanup_test_data(kebele_tenants)
        
        self.stdout.write(self.style.SUCCESS('✅ Fraud Detection Tests Completed'))

    def test_digital_id_duplicate(self, tenants):
        """Test digital ID duplicate detection"""
        self.stdout.write('\n🔍 Testing Digital ID Duplicate Detection...')
        
        # Create a citizen in first tenant
        tenant1 = tenants[0]
        tenant2 = tenants[1]
        
        test_digital_id = 'TEST123456789'
        
        try:
            # Create citizen in first tenant
            with schema_context(tenant1.schema_name):
                citizen1 = Citizen.objects.create(
                    digital_id=test_digital_id,
                    first_name='John',
                    last_name='Doe',
                    date_of_birth=date(1990, 1, 1),
                    phone='+251911123456',
                    email='<EMAIL>'
                )
                self.stdout.write(f'✅ Created test citizen in {tenant1.name}')
            
            # Try to create similar citizen in second tenant
            citizen_data = {
                'digital_id': test_digital_id,  # Same digital ID
                'first_name': 'John',
                'last_name': 'Doe',
                'date_of_birth': date(1990, 1, 1),
                'phone': '+251911123456',
                'email': '<EMAIL>'
            }
            
            # Run fraud detection
            fraud_results = fraud_detection_service.check_fraud_indicators(citizen_data, tenant2.id)
            
            # Verify results
            success = (
                fraud_results['is_fraud_detected'] and
                'digital_id_duplicate' in fraud_results['indicators'] and
                fraud_results['risk_level'] == 'critical'
            )
            
            self.stdout.write(f'📊 Digital ID Duplicate Test: {"✅ PASSED" if success else "❌ FAILED"}')
            self.stdout.write(f'   Risk Level: {fraud_results["risk_level"]}')
            self.stdout.write(f'   Indicators: {fraud_results["indicators"]}')
            self.stdout.write(f'   Matches Found: {len(fraud_results["matches"])}')
            
            return {
                'success': success,
                'risk_level': fraud_results['risk_level'],
                'indicators': fraud_results['indicators'],
                'matches': len(fraud_results['matches'])
            }
            
        except Exception as e:
            self.stdout.write(f'❌ Digital ID Duplicate Test Failed: {str(e)}')
            return {'success': False, 'error': str(e)}

    def test_personal_info_match(self, tenants):
        """Test personal information matching using reliable identifiers"""
        self.stdout.write('\n🔍 Testing Personal Information Matching with Reliable Identifiers...')

        tenant1 = tenants[0]
        tenant2 = tenants[1]

        try:
            # Create citizen with parent information in first tenant
            with schema_context(tenant1.schema_name):
                from citizens.models import Parent

                citizen1 = Citizen.objects.create(
                    digital_id=self.generate_random_digital_id(),
                    first_name='Jane',
                    middle_name='Alemu',
                    last_name='Smith',
                    date_of_birth=date(1985, 5, 15)
                )

                # Create parent records
                Parent.objects.create(
                    citizen=citizen1,
                    first_name='Almaz',
                    middle_name='Tadesse',
                    last_name='Bekele',
                    relationship='mother'
                )

                Parent.objects.create(
                    citizen=citizen1,
                    first_name='Getachew',
                    middle_name='Haile',
                    last_name='Mariam',
                    relationship='father'
                )

                self.stdout.write(f'✅ Created test citizen with parent info in {tenant1.name}')

            # Try to create similar citizen in second tenant with same family info
            citizen_data = {
                'digital_id': self.generate_random_digital_id(),  # Different digital ID
                'first_name': 'Jane',  # Same name
                'middle_name': 'Alemu',  # Same middle name
                'last_name': 'Smith',  # Same last name
                'date_of_birth': date(1985, 5, 15),  # Same DOB
                'mother_full_name': 'Almaz Tadesse Bekele',  # Same mother
                'father_full_name': 'Getachew Haile Mariam'  # Same father
            }
            
            # Run fraud detection
            fraud_results = fraud_detection_service.check_fraud_indicators(citizen_data, tenant2.id)
            
            # Verify results
            success = (
                fraud_results['is_fraud_detected'] and
                'personal_info_match' in fraud_results['indicators'] and
                fraud_results['risk_level'] in ['high', 'critical']
            )
            
            self.stdout.write(f'📊 Personal Info Match Test: {"✅ PASSED" if success else "❌ FAILED"}')
            self.stdout.write(f'   Risk Level: {fraud_results["risk_level"]}')
            self.stdout.write(f'   Indicators: {fraud_results["indicators"]}')
            self.stdout.write(f'   Matches Found: {len(fraud_results["matches"])}')
            
            return {
                'success': success,
                'risk_level': fraud_results['risk_level'],
                'indicators': fraud_results['indicators'],
                'matches': len(fraud_results['matches'])
            }
            
        except Exception as e:
            self.stdout.write(f'❌ Personal Info Match Test Failed: {str(e)}')
            return {'success': False, 'error': str(e)}

    def test_biometric_match(self, tenants):
        """Test biometric matching"""
        self.stdout.write('\n🔍 Testing Biometric Matching...')
        
        tenant1 = tenants[0]
        tenant2 = tenants[1]
        
        try:
            # Create citizen with biometric data in first tenant
            with schema_context(tenant1.schema_name):
                citizen1 = Citizen.objects.create(
                    digital_id=self.generate_random_digital_id(),
                    first_name='Bob',
                    last_name='Johnson',
                    date_of_birth=date(1980, 12, 25),
                    phone='+251933456789',
                    email='<EMAIL>'
                )
                
                # Create biometric data
                biometric1 = Biometric.objects.create(
                    citizen=citizen1,
                    left_thumb_fingerprint='test_fingerprint_data_123',
                    right_thumb_fingerprint='test_fingerprint_data_456'
                )
                self.stdout.write(f'✅ Created test citizen with biometrics in {tenant1.name}')
            
            # Try to create citizen with same biometric data in second tenant
            citizen_data = {
                'digital_id': self.generate_random_digital_id(),
                'first_name': 'Robert',  # Different name
                'last_name': 'Johns',    # Different name
                'date_of_birth': date(1981, 1, 1),  # Different DOB
                'phone': '+251944567890',  # Different phone
                'email': '<EMAIL>',  # Different email
                'biometric_data': {
                    'left_thumb_fingerprint': 'test_fingerprint_data_123',  # Same fingerprint
                    'right_thumb_fingerprint': 'test_fingerprint_data_456'  # Same fingerprint
                }
            }
            
            # Run fraud detection
            fraud_results = fraud_detection_service.check_fraud_indicators(citizen_data, tenant2.id)
            
            # Verify results
            success = (
                fraud_results['is_fraud_detected'] and
                'biometric_match' in fraud_results['indicators'] and
                fraud_results['risk_level'] == 'critical'
            )
            
            self.stdout.write(f'📊 Biometric Match Test: {"✅ PASSED" if success else "❌ FAILED"}')
            self.stdout.write(f'   Risk Level: {fraud_results["risk_level"]}')
            self.stdout.write(f'   Indicators: {fraud_results["indicators"]}')
            self.stdout.write(f'   Matches Found: {len(fraud_results["matches"])}')
            
            return {
                'success': success,
                'risk_level': fraud_results['risk_level'],
                'indicators': fraud_results['indicators'],
                'matches': len(fraud_results['matches'])
            }
            
        except Exception as e:
            self.stdout.write(f'❌ Biometric Match Test Failed: {str(e)}')
            return {'success': False, 'error': str(e)}

    def generate_random_digital_id(self):
        """Generate a random digital ID for testing"""
        return 'TEST' + ''.join(random.choices(string.digits, k=10))

    def display_test_results(self, results):
        """Display comprehensive test results"""
        self.stdout.write('\n' + '='*60)
        self.stdout.write(self.style.SUCCESS('📊 FRAUD DETECTION TEST RESULTS'))
        self.stdout.write('='*60)
        
        total_tests = len(results)
        passed_tests = sum(1 for r in results.values() if r.get('success', False))
        
        self.stdout.write(f'Total Tests: {total_tests}')
        self.stdout.write(f'Passed: {passed_tests}')
        self.stdout.write(f'Failed: {total_tests - passed_tests}')
        self.stdout.write(f'Success Rate: {(passed_tests/total_tests*100):.1f}%' if total_tests > 0 else 'N/A')
        
        for test_name, result in results.items():
            self.stdout.write(f'\n📋 {test_name.replace("_", " ").title()}:')
            if result.get('success'):
                self.stdout.write(f'   ✅ Status: PASSED')
                self.stdout.write(f'   🚨 Risk Level: {result.get("risk_level", "N/A")}')
                self.stdout.write(f'   🔍 Indicators: {result.get("indicators", [])}')
                self.stdout.write(f'   📊 Matches: {result.get("matches", 0)}')
            else:
                self.stdout.write(f'   ❌ Status: FAILED')
                if 'error' in result:
                    self.stdout.write(f'   💥 Error: {result["error"]}')

    def cleanup_test_data(self, tenants):
        """Clean up test data created during testing"""
        self.stdout.write('\n🧹 Cleaning up test data...')
        
        for tenant in tenants:
            try:
                with schema_context(tenant.schema_name):
                    # Delete test citizens (those with digital IDs starting with 'TEST')
                    test_citizens = Citizen.objects.filter(digital_id__startswith='TEST')
                    count = test_citizens.count()
                    test_citizens.delete()
                    self.stdout.write(f'   🗑️  Deleted {count} test citizens from {tenant.name}')
            except Exception as e:
                self.stdout.write(f'   ❌ Error cleaning {tenant.name}: {str(e)}')
        
        self.stdout.write('✅ Cleanup completed')
