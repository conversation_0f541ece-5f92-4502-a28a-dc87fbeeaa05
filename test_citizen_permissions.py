#!/usr/bin/env python3
"""
Test script to verify citizen permissions are working correctly.
"""

import requests
import json

def test_citizen_permissions():
    """Test the citizen permissions for different user roles."""
    
    base_url = "http://************:8000"
    
    print("🧪 Testing Citizen Permissions")
    print("=" * 40)
    
    # Test users with different roles
    test_users = [
        {
            "name": "Clerk",
            "username": "<EMAIL>",
            "password": "password123",
            "expected_access": True
        },
        {
            "name": "Kebele Admin",
            "username": "<EMAIL>", 
            "password": "password123",
            "expected_access": True
        },
        {
            "name": "Superadmin",
            "email": "<EMAIL>",
            "password": "admin123",
            "expected_access": True
        }
    ]
    
    for user_info in test_users:
        print(f"\n📍 Testing {user_info['name']} access...")
        
        # Step 1: Login
        try:
            login_data = {}
            if 'email' in user_info:
                login_data = {
                    "email": user_info['email'],
                    "password": user_info['password']
                }
            else:
                login_data = {
                    "username": user_info['username'],
                    "password": user_info['password']
                }
            
            login_response = requests.post(
                f"{base_url}/api/auth/token/",
                json=login_data,
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            if login_response.status_code == 200:
                login_result = login_response.json()
                access_token = login_result.get('access')
                tenant_id = login_result.get('tenant_id')
                user_role = login_result.get('role')
                
                print(f"✅ {user_info['name']} login successful")
                print(f"   Role: {user_role}")
                print(f"   Tenant ID: {tenant_id}")
                
                if not access_token:
                    print("❌ No access token received")
                    continue
                    
                headers = {
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json"
                }
                
                # Step 2: Test citizen list access
                if tenant_id:
                    citizen_url = f"{base_url}/api/tenants/{tenant_id}/citizens/"
                    citizen_response = requests.get(citizen_url, headers=headers, timeout=10)
                    
                    print(f"   Citizens list status: {citizen_response.status_code}")
                    
                    if citizen_response.status_code == 200:
                        print("   ✅ Can access citizens list")
                        citizens_data = citizen_response.json()
                        print(f"   Found {len(citizens_data.get('results', citizens_data))} citizens")
                    elif citizen_response.status_code == 403:
                        print("   ❌ Access forbidden (403)")
                    elif citizen_response.status_code == 404:
                        print("   ❌ Endpoint not found (404)")
                    else:
                        print(f"   ❌ Unexpected status: {citizen_response.status_code}")
                    
                    # Step 3: Test citizen stats access
                    stats_url = f"{base_url}/api/tenants/{tenant_id}/citizens/stats/"
                    stats_response = requests.get(stats_url, headers=headers, timeout=10)
                    
                    print(f"   Citizens stats status: {stats_response.status_code}")
                    
                    if stats_response.status_code == 200:
                        print("   ✅ Can access citizens stats")
                    elif stats_response.status_code == 403:
                        print("   ❌ Stats access forbidden (403)")
                    elif stats_response.status_code == 404:
                        print("   ❌ Stats endpoint not found (404)")
                    else:
                        print(f"   ❌ Unexpected stats status: {stats_response.status_code}")
                else:
                    print("   ⚠️  No tenant ID found, skipping tenant-specific tests")
                    
            else:
                print(f"❌ {user_info['name']} login failed: {login_response.status_code}")
                print(f"   Response: {login_response.text}")
                
        except Exception as e:
            print(f"❌ {user_info['name']} test error: {e}")
    
    # Test shared endpoints (should work for all authenticated users)
    print(f"\n📍 Testing shared endpoints...")
    try:
        # Use the last successful login token if available
        shared_url = f"{base_url}/api/shared/countries/"
        shared_response = requests.get(shared_url, timeout=10)
        
        print(f"Shared countries status: {shared_response.status_code}")
        
        if shared_response.status_code == 200:
            print("✅ Shared endpoints working")
        else:
            print(f"❌ Shared endpoints failed: {shared_response.status_code}")
            
    except Exception as e:
        print(f"❌ Shared endpoints error: {e}")

if __name__ == "__main__":
    test_citizen_permissions()
    
    print("\n" + "=" * 40)
    print("🔧 If citizen permissions are still not working:")
    print("1. Restart Django server: docker-compose restart backend")
    print("2. Check Django logs: docker-compose logs backend")
    print("3. Verify permission classes are updated")
    print("4. Check user roles and tenant assignments")
    print("\n🌐 Expected results:")
    print("   Clerk: Should have access to citizens in their tenant")
    print("   Kebele Admin: Should have access to citizens in their tenant")
    print("   Superadmin: Should have access to all endpoints")
    print("\n🔐 Test credentials:")
    print("   Clerk: <EMAIL> / password123")
    print("   Kebele Admin: <EMAIL> / password123")
    print("   Superadmin: <EMAIL> / admin123")
