# Generated migration for adding Ethiopian calendar support

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('citizens', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='citizen',
            name='date_of_birth_ethiopian',
            field=models.Char<PERSON><PERSON>(blank=True, help_text='Ethiopian calendar date in DD/MM/YYYY format', max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='child',
            name='date_of_birth_ethiopian',
            field=models.CharField(blank=True, help_text='Ethiopian calendar date in DD/MM/YYYY format', max_length=20, null=True),
        ),
    ]
