#!/usr/bin/env python3
"""
Test script to verify group-based navigation works correctly for all user types
"""
import os
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from users.models_groups import TenantGroup, GroupMembership
from tenants.models import Tenant, TenantType
from django_tenants.utils import schema_context, get_public_schema_name

User = get_user_model()

def test_group_navigation():
    """Test navigation for each group type"""
    
    print("🧪 Testing Group-Based Navigation System")
    print("=" * 60)
    
    # Expected navigation for each group
    expected_navigation = {
        'super_admin': ['Dashboard', 'Tenants', 'System Users', 'System Settings'],
        'city_admin': ['Dashboard', 'Citizen Directory', 'Subcity Users', 'Reports'],
        'subcity_admin': ['Dashboard', 'Citizens', 'ID Cards', 'Print Queue', 'Service Requests', 'Kebele Users', 'Reports'],
        'kebele_leader': ['Dashboard', 'Citizens', 'ID Cards', 'Clearance', 'Transfer'],
        'clerk': ['Dashboard', 'Citizens', 'ID Cards']
    }
    
    # Test each group
    test_results = {}
    
    for group_name, expected_menus in expected_navigation.items():
        print(f"\n🔍 Testing {group_name} navigation...")
        
        # Test login and navigation
        result = test_group_login_and_navigation(group_name, expected_menus)
        test_results[group_name] = result
        
        if result['success']:
            print(f"   ✅ {group_name} navigation test PASSED")
        else:
            print(f"   ❌ {group_name} navigation test FAILED")
            print(f"   Expected: {expected_menus}")
            print(f"   Got: {result.get('actual_menus', [])}")
            print(f"   Error: {result.get('error', 'Unknown error')}")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Navigation Test Summary:")
    
    passed = sum(1 for r in test_results.values() if r['success'])
    total = len(test_results)
    
    for group_name, result in test_results.items():
        status = "✅ PASS" if result['success'] else "❌ FAIL"
        print(f"   {group_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All navigation tests PASSED!")
        print("✅ Group-based navigation is working correctly")
    else:
        print("❌ Some navigation tests FAILED!")
        print("🔧 Please check the group permissions and navigation logic")
    
    return passed == total

def test_group_login_and_navigation(group_name, expected_menus):
    """Test login and navigation for a specific group"""
    
    try:
        # Find a user in this group or create one for testing
        test_user = get_or_create_test_user(group_name)
        if not test_user:
            return {'success': False, 'error': f'Could not create test user for {group_name}'}
        
        # Test login
        login_response = requests.post('http://localhost:8000/api/auth/token/', json={
            'email': test_user['email'],
            'password': test_user['password']
        })
        
        if login_response.status_code != 200:
            return {'success': False, 'error': f'Login failed: {login_response.status_code}'}
        
        token_data = login_response.json()
        access_token = token_data['access']
        
        # Decode JWT token to check groups and permissions
        import jwt
        decoded = jwt.decode(access_token, options={"verify_signature": False})
        
        user_groups = decoded.get('groups', [])
        user_permissions = decoded.get('permissions', [])
        
        print(f"   User groups: {user_groups}")
        print(f"   User permissions count: {len(user_permissions)}")
        
        # Check if user is in expected group
        if group_name not in user_groups:
            return {'success': False, 'error': f'User not in {group_name} group. Groups: {user_groups}'}
        
        # For now, we'll simulate the frontend navigation logic
        # In a real test, you'd call the frontend API or test the navigation component
        actual_menus = simulate_frontend_navigation(decoded)
        
        # Check if navigation matches expected
        navigation_matches = check_navigation_match(expected_menus, actual_menus)
        
        # Clean up test user
        cleanup_test_user(test_user)
        
        return {
            'success': navigation_matches,
            'actual_menus': actual_menus,
            'user_groups': user_groups,
            'permissions_count': len(user_permissions)
        }
        
    except Exception as e:
        return {'success': False, 'error': str(e)}

def get_or_create_test_user(group_name):
    """Get or create a test user for the specified group"""
    
    # Map groups to test user data
    test_users = {
        'super_admin': {
            'email': '<EMAIL>',
            'password': 'admin123',
            'role': 'superadmin',
            'tenant': None
        },
        'city_admin': {
            'email': '<EMAIL>',
            'password': 'testpass123',
            'role': 'city_admin',
            'tenant_type': 'city'
        },
        'subcity_admin': {
            'email': '<EMAIL>',
            'password': 'testpass123',
            'role': 'subcity_admin',
            'tenant_type': 'subcity'
        },
        'kebele_leader': {
            'email': '<EMAIL>',
            'password': 'testpass123',
            'role': 'kebele_leader',
            'tenant_type': 'kebele'
        },
        'clerk': {
            'email': '<EMAIL>',
            'password': 'testpass123',
            'role': 'clerk',
            'tenant_type': 'kebele'
        }
    }
    
    return test_users.get(group_name)

def simulate_frontend_navigation(decoded_token):
    """Simulate the frontend navigation logic"""
    
    groups = decoded_token.get('groups', [])
    permissions = decoded_token.get('permissions', [])
    is_superuser = decoded_token.get('is_superuser', False)
    
    # Simulate the navigation logic from usePermissions.js
    navigation_items = ['Dashboard']  # Always present
    
    def has_permission(perm):
        return perm in permissions
    
    # Super Admin Navigation
    if is_superuser or 'super_admin' in groups:
        if has_permission('manage_tenants'):
            navigation_items.append('Tenants')
        if has_permission('manage_all_users'):
            navigation_items.append('System Users')
        if has_permission('manage_system_settings'):
            navigation_items.append('System Settings')
        return navigation_items
    
    # City Admin Navigation
    if 'city_admin' in groups:
        if has_permission('view_child_subcities_data'):
            navigation_items.append('Citizen Directory')
        if has_permission('create_subcity_users'):
            navigation_items.append('Subcity Users')
        if has_permission('view_city_reports'):
            navigation_items.append('Reports')
        return navigation_items
    
    # SubCity Admin Navigation
    if 'subcity_admin' in groups:
        if has_permission('view_child_kebeles_data'):
            navigation_items.append('Citizens')
        if has_permission('view_id_cards_list'):
            navigation_items.append('ID Cards')
        if has_permission('print_id_cards'):
            navigation_items.append('Print Queue')
        if has_permission('view_service_requests'):
            navigation_items.append('Service Requests')
        if has_permission('create_kebele_users'):
            navigation_items.append('Kebele Users')
        if has_permission('view_subcity_reports'):
            navigation_items.append('Reports')
        return navigation_items
    
    # Kebele Leader Navigation
    if 'kebele_leader' in groups:
        if has_permission('view_citizens_list'):
            navigation_items.append('Citizens')
        if has_permission('view_id_cards_list'):
            navigation_items.append('ID Cards')
        if has_permission('create_clearances'):
            navigation_items.append('Clearance')
        if has_permission('create_transfers'):
            navigation_items.append('Transfer')
        return navigation_items
    
    # Clerk Navigation
    if 'clerk' in groups:
        if has_permission('register_citizens'):
            navigation_items.append('Citizens')
        if has_permission('generate_id_cards'):
            navigation_items.append('ID Cards')
        return navigation_items
    
    return navigation_items

def check_navigation_match(expected, actual):
    """Check if actual navigation matches expected"""
    return set(expected) == set(actual)

def cleanup_test_user(test_user):
    """Clean up test user if it was created for testing"""
    # For now, we'll skip cleanup for existing <NAME_EMAIL>
    pass

def main():
    """Main test function"""
    success = test_group_navigation()
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
