from django.db import models
from django_tenants.models import TenantMixin, DomainMixin


class TenantType(models.TextChoices):
    CITY = 'city', 'City'
    SUBCITY = 'subcity', 'Sub City'
    KEBELE = 'kebele', 'Kebele'


class Tenant(TenantMixin):
    name = models.CharField(max_length=100)
    name_am = models.CharField(max_length=100, blank=True, null=True, verbose_name="Name (Amharic)")
    type = models.CharField(max_length=10, choices=TenantType.choices)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='children')
    created_on = models.DateTimeField(auto_now_add=True)

    # Theme customization fields
    primary_color = models.CharField(max_length=7, default='#ff8f00', help_text='Primary color in hex format (e.g., #ff8f00)')
    secondary_color = models.Char<PERSON>ield(max_length=7, default='#ef6c00', help_text='Secondary color in hex format (e.g., #ef6c00)')

    # Public access settings
    public_id_application_enabled = models.BooleanField(default=False, help_text='Allow public access to ID card application services for citizens')

    # Default true, schema will be automatically created and synced when it is saved
    auto_create_schema = True
    
    def __str__(self):
        return self.name
    
    @property
    def is_city(self):
        return self.type == TenantType.CITY
    
    @property
    def is_subcity(self):
        return self.type == TenantType.SUBCITY
    
    @property
    def is_kebele(self):
        return self.type == TenantType.KEBELE
    
    @property
    def city(self):
        if self.is_city:
            return self
        elif self.is_subcity:
            return self.parent
        elif self.is_kebele:
            return self.parent.parent
        return None
    
    @property
    def subcity(self):
        if self.is_subcity:
            return self
        elif self.is_kebele:
            return self.parent
        return None

    def get_ancestor_tenants(self):
        """Get all ancestor tenants (parent, grandparent, etc.)"""
        ancestors = []
        current = self.parent
        while current:
            ancestors.append(current)
            current = current.parent
        return ancestors

    def get_child_tenants(self, include_self=False):
        """Get all child tenants recursively"""
        children = []
        if include_self:
            children.append(self)

        for child in self.children.all():
            children.append(child)
            children.extend(child.get_child_tenants())

        return children

    def can_manage_tenant(self, other_tenant):
        """Check if this tenant can manage another tenant"""
        if self == other_tenant:
            return True

        # Check if other_tenant is a child
        return other_tenant in self.get_child_tenants()


class Domain(DomainMixin):
    pass
