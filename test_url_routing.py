#!/usr/bin/env python3
"""
Test script to verify URL routing for the approval workflow.
This script checks if the URLs are properly configured.
"""

import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.urls import reverse, resolve
from django.test import RequestFactory
from django.contrib.auth import get_user_model

def test_url_routing():
    """Test URL routing for approval workflow"""
    
    print("🧪 Testing URL Routing for Approval Workflow")
    print("=" * 50)
    
    # Test tenant-specific URLs
    tenant_id = 1
    id_card_id = 1
    
    try:
        # Test tenant-specific approval action URL
        approval_url = f'/api/tenants/{tenant_id}/idcards/{id_card_id}/approval_action/'
        print(f"✅ Tenant approval URL: {approval_url}")
        
        # Test if URL can be resolved
        try:
            resolved = resolve(approval_url)
            print(f"✅ URL resolves to: {resolved.func.__name__}")
            print(f"✅ View class: {resolved.func.view_class.__name__}")
            print(f"✅ URL name: {resolved.url_name}")
        except Exception as e:
            print(f"❌ URL resolution failed: {e}")
        
        # Test main ID card approval URL
        main_approval_url = f'/api/idcards/{id_card_id}/approval_action/'
        print(f"\n✅ Main approval URL: {main_approval_url}")
        
        try:
            resolved = resolve(main_approval_url)
            print(f"✅ URL resolves to: {resolved.func.__name__}")
            print(f"✅ View class: {resolved.func.view_class.__name__}")
        except Exception as e:
            print(f"❌ URL resolution failed: {e}")
            
    except Exception as e:
        print(f"❌ Error testing URLs: {e}")
    
    print("\n" + "=" * 50)
    print("🏁 URL routing test completed!")

def test_permissions():
    """Test permission classes"""
    
    print("\n🧪 Testing Permission Classes")
    print("=" * 30)
    
    try:
        from common.permissions import CanApproveIDCards
        from django.contrib.auth.models import AnonymousUser
        
        # Create a mock request
        factory = RequestFactory()
        request = factory.post('/test/')
        
        # Test with anonymous user
        request.user = AnonymousUser()
        permission = CanApproveIDCards()
        result = permission.has_permission(request, None)
        print(f"✅ Anonymous user permission: {result} (should be False)")
        
        # Test with authenticated user (mock)
        User = get_user_model()
        
        # Create mock users with different roles
        roles_to_test = ['clerk', 'kebele_leader', 'kebele_admin', 'superadmin']
        
        for role in roles_to_test:
            # Create a mock user object
            class MockUser:
                def __init__(self, role):
                    self.role = role
                    self.is_authenticated = True
                    self.is_superuser = False
            
            request.user = MockUser(role)
            result = permission.has_permission(request, None)
            print(f"✅ {role} permission: {result}")
            
    except Exception as e:
        print(f"❌ Error testing permissions: {e}")

if __name__ == "__main__":
    test_url_routing()
    test_permissions()
