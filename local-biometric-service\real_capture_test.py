#!/usr/bin/env python3
"""
Real Fingerprint Capture Test
Attempts to capture actual fingerprint images using ftrScanGetFrame
"""

import os
import sys
import time
import ctypes
import base64
from datetime import datetime

def test_real_fingerprint_capture():
    """Test real fingerprint capture using ftrScanGetFrame"""
    
    try:
        print("=== REAL FINGERPRINT CAPTURE TEST ===")
        print("Loading Futronic SDK DLLs...")
        
        # Load SDK
        if not os.path.exists('./ftrScanAPI.dll'):
            print("ERROR: ftrScanAPI.dll not found")
            return False
        
        scan_api = ctypes.cdll.LoadLibrary('./ftrScanAPI.dll')
        print("ftrScanAPI.dll loaded successfully")
        
        # Setup function signatures
        scan_api.ftrScanOpenDevice.restype = ctypes.c_void_p
        scan_api.ftrScanCloseDevice.argtypes = [ctypes.c_void_p]
        scan_api.ftrScanCloseDevice.restype = ctypes.c_int
        
        # Setup image size function
        try:
            scan_api.ftrScanGetImageSize.argtypes = [ctypes.c_void_p, ctypes.POINTER(ctypes.c_int), ctypes.POINTER(ctypes.c_int)]
            scan_api.ftrScanGetImageSize.restype = ctypes.c_int
            print("Image size function available")
        except:
            print("WARNING: Image size function not available")
        
        # Setup frame capture function
        try:
            scan_api.ftrScanGetFrame.argtypes = [ctypes.c_void_p, ctypes.POINTER(ctypes.c_ubyte), ctypes.POINTER(ctypes.c_uint)]
            scan_api.ftrScanGetFrame.restype = ctypes.c_int
            print("Frame capture function available")
        except:
            print("WARNING: Frame capture function not available")
        
        # Connect to device
        print("Connecting to device...")
        device_handle = scan_api.ftrScanOpenDevice()
        
        if not device_handle or device_handle == 0:
            print("ERROR: Failed to connect to device")
            return False
        
        print(f"Device connected! Handle: {device_handle}")
        
        # Get image dimensions
        print("Getting image dimensions...")
        try:
            width = ctypes.c_int()
            height = ctypes.c_int()
            result = scan_api.ftrScanGetImageSize(device_handle, ctypes.byref(width), ctypes.byref(height))
            
            if result == 0:
                img_width = width.value
                img_height = height.value
                print(f"Image size: {img_width}x{img_height}")
            else:
                print(f"Failed to get image size, using defaults. Error code: {result}")
                img_width = 320
                img_height = 480
        except Exception as e:
            print(f"Image size error: {e}")
            img_width = 320
            img_height = 480
        
        # Calculate buffer size
        buffer_size = img_width * img_height
        print(f"Buffer size needed: {buffer_size} bytes")
        
        # Prepare buffer for fingerprint data
        print("Preparing buffer for fingerprint capture...")
        buffer = (ctypes.c_ubyte * buffer_size)()
        buffer_size_var = ctypes.c_uint(buffer_size)
        
        print("=== ATTEMPTING REAL FINGERPRINT CAPTURE ===")
        print("PLACE YOUR FINGER FIRMLY ON THE SCANNER!")
        print("Press and hold your finger down firmly...")

        # Multiple capture attempts with better timing
        max_attempts = 10
        capture_success = False

        for attempt in range(1, max_attempts + 1):
            print(f"\nAttempt {attempt}/{max_attempts}")
            print("Make sure finger is pressed firmly on scanner...")
            time.sleep(1)  # Give time to adjust finger

            print("CAPTURING NOW...")

            # Attempt real fingerprint capture
            try:
                # Reset buffer size for each attempt
                buffer_size_var = ctypes.c_uint(buffer_size)
                result = scan_api.ftrScanGetFrame(device_handle, buffer, ctypes.byref(buffer_size_var))
                print(f"ftrScanGetFrame returned: {result}")

                if result == 0:  # Success
                    capture_success = True
                    break
                elif result == 1:
                    print("No finger detected - press finger more firmly")
                elif result == 2:
                    print("Finger moved - keep finger steady")
                elif result == 3:
                    print("Capture failed - try again")
                else:
                    print(f"Unknown error code: {result}")

                if attempt < max_attempts:
                    print("Trying again in 2 seconds...")
                    time.sleep(2)

            except Exception as e:
                print(f"CRASH on attempt {attempt}: {e}")
                break

        if capture_success and result == 0:  # Success
                actual_size = buffer_size_var.value
                print(f"SUCCESS! Captured {actual_size} bytes of fingerprint data")
                
                # Convert to bytes
                fingerprint_data = bytes(buffer[:actual_size])
                print(f"Fingerprint data length: {len(fingerprint_data)} bytes")
                
                # Encode to base64 for storage
                fingerprint_b64 = base64.b64encode(fingerprint_data).decode()
                print(f"Base64 encoded length: {len(fingerprint_b64)} characters")
                
                # Save to file for verification
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"real_fingerprint_{timestamp}.txt"
                
                with open(filename, 'w') as f:
                    f.write(f"Real Fingerprint Capture\n")
                    f.write(f"Timestamp: {datetime.now().isoformat()}\n")
                    f.write(f"Image Size: {img_width}x{img_height}\n")
                    f.write(f"Data Size: {actual_size} bytes\n")
                    f.write(f"Base64 Data:\n{fingerprint_b64}\n")
                
                print(f"Real fingerprint data saved to: {filename}")
                print("SUCCESS: REAL FINGERPRINT CAPTURED!")

                # Close device
                scan_api.ftrScanCloseDevice(device_handle)
                return True

        # If we get here, all attempts failed
        print(f"\nFAILED: All {max_attempts} capture attempts failed")
        print("Tips for better capture:")
        print("  - Press finger more firmly on scanner")
        print("  - Make sure finger covers the scanner area")
        print("  - Keep finger completely still during capture")
        print("  - Clean the scanner surface")
        print("  - Try a different finger if this one doesn't work")
        
        # Close device
        print("Closing device...")
        scan_api.ftrScanCloseDevice(device_handle)
        return False
        
    except Exception as e:
        print(f"FATAL ERROR: {e}")
        return False

if __name__ == '__main__':
    try:
        print("REAL FINGERPRINT CAPTURE TEST")
        print("This will attempt to capture actual fingerprint data")
        print("Make sure your finger is ready to place on scanner")
        print()
        
        input("Press Enter when ready to start capture test...")
        
        success = test_real_fingerprint_capture()
        
        if success:
            print("\n=== TEST RESULT: SUCCESS ===")
            print("Real fingerprint capture is working!")
            print("Check the generated file for actual fingerprint data")
        else:
            print("\n=== TEST RESULT: FAILED ===")
            print("Real fingerprint capture failed")
            print("This confirms the SDK compatibility issue")
        
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"\nTest error: {e}")
    finally:
        print("\nTest completed")
        input("Press Enter to exit...")
