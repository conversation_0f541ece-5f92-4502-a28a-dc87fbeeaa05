import { Outlet } from 'react-router-dom';
import { Box, CssBaseline, Toolbar } from '@mui/material';
import LandingNavbar from '../components/navigation/LandingNavbar';

const LandingLayout = () => {
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      <CssBaseline />
      <LandingNavbar />
      <Toolbar /> {/* Spacer for fixed navbar */}
      <Outlet />
    </Box>
  );
};

export default LandingLayout;
