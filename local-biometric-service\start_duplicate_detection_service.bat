@echo off
echo ========================================
echo    DUPLICATE DETECTION SERVICE
echo ========================================
echo.
echo 🔍 SOLVES THE DUPLICATE CITIZEN PROBLEM
echo.
echo ❌ PREVIOUS ISSUE:
echo   - Random fingerprint data each time
echo   - Same person = different fingerprints
echo   - No way to detect duplicate registrations
echo   - Citizens could register multiple times
echo.
echo ✅ NEW SOLUTION - DUPLICATE DETECTION:
echo   1. CONSISTENT FINGERPRINTS per person
echo   2. NATURAL VARIATION (realistic capture differences)
echo   3. SIMILARITY MATCHING (85%% threshold)
echo   4. DUPLICATE ALERTS when same person detected
echo.
echo 🎯 HOW IT WORKS:
echo   - Person identifier creates consistent fingerprint
echo   - Natural variation simulates real capture differences
echo   - Similarity algorithm compares fingerprints
echo   - 85%%+ similarity = potential duplicate alert
echo.
echo 🧪 TESTING DUPLICATE DETECTION:
echo   1. Capture without person ID = new person
echo   2. Use same person ID = similar fingerprint
echo   3. Different person ID = different fingerprint
echo   4. System alerts when duplicates found
echo.
echo 📱 SERVICE URL: http://localhost:8001
echo.

echo 🚀 Starting duplicate detection service...
echo.
echo 🔍 Duplicate detection enabled
echo ✅ Consistent fingerprints per person
echo 📋 Keep this window open while testing
echo.

python duplicate_detection_service.py

echo.
echo Service stopped.
pause
