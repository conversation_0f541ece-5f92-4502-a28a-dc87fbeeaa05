import os
import sys
import django
from django.db import connection
from django.core.management import call_command

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from tenants.models import Tenant, Domain, TenantType
from django_tenants.utils import schema_context
from citizens.models import Citizen, Gender, MaritalStatus, BloodType
from idcards.models import IDCard, IDCardStatus
from workflows.models import WorkflowLog, ApprovalAction
from datetime import datetime, timedelta

User = get_user_model()

def create_superuser():
    """Create a superuser if it doesn't exist."""
    if not User.objects.filter(username='admin').exists():
        User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            role='superadmin'
        )
        print("Superuser created successfully.")
    else:
        print("Superuser already exists.")

def create_public_tenant():
    """Create the public tenant if it doesn't exist."""
    if not Tenant.objects.filter(schema_name='public').exists():
        tenant = Tenant(
            schema_name='public',
            name='Public',
            type=TenantType.CITY
        )
        tenant.save()

        domain = Domain()
        domain.domain = 'localhost'
        domain.tenant = tenant
        domain.is_primary = True
        domain.save()

        print("Public tenant created successfully.")
    else:
        print("Public tenant already exists.")

def create_sample_tenants():
    """Create sample tenants for testing."""
    # Create a city
    if not Tenant.objects.filter(name='Addis Ababa').exists():
        city = Tenant(
            schema_name='city_addis_ababa',
            name='Addis Ababa',
            type=TenantType.CITY
        )
        city.save()

        domain = Domain()
        domain.domain = 'addis.goid.local'
        domain.tenant = city
        domain.is_primary = True
        domain.save()

        print("City tenant created successfully.")

        # Create a subcity
        subcity = Tenant(
            schema_name='subcity_bole',
            name='Bole',
            type=TenantType.SUBCITY,
            parent=city
        )
        subcity.save()

        domain = Domain()
        domain.domain = 'bole.goid.local'
        domain.tenant = subcity
        domain.is_primary = True
        domain.save()

        print("Subcity tenant created successfully.")

        # Create a kebele
        kebele = Tenant(
            schema_name='kebele_bole_01',
            name='Bole 01',
            type=TenantType.KEBELE,
            parent=subcity
        )
        kebele.save()

        domain = Domain()
        domain.domain = 'bole01.goid.local'
        domain.tenant = kebele
        domain.is_primary = True
        domain.save()

        print("Kebele tenant created successfully.")

        # Create users for each tenant
        with schema_context('city_addis_ababa'):
            if not User.objects.filter(username='city_admin').exists():
                User.objects.create_user(
                    username='city_admin',
                    email='<EMAIL>',
                    password='password123',
                    role='city_admin',
                    tenant=city
                )
                print("City admin created successfully.")

        with schema_context('subcity_bole'):
            if not User.objects.filter(username='subcity_admin').exists():
                User.objects.create_user(
                    username='subcity_admin',
                    email='<EMAIL>',
                    password='password123',
                    role='subcity_admin',
                    tenant=subcity
                )
                print("Subcity admin created successfully.")

        with schema_context('kebele_bole_01'):
            if not User.objects.filter(username='kebele_admin').exists():
                User.objects.create_user(
                    username='kebele_admin',
                    email='<EMAIL>',
                    password='password123',
                    role='kebele_admin',
                    tenant=kebele
                )
                print("Kebele admin created successfully.")

            if not User.objects.filter(username='clerk').exists():
                User.objects.create_user(
                    username='clerk',
                    email='<EMAIL>',
                    password='password123',
                    role='clerk',
                    tenant=kebele
                )
                print("Clerk created successfully.")
    else:
        print("Sample tenants already exist.")

def create_sample_citizens():
    """Create sample citizens and ID cards for testing."""
    # Create citizens in the kebele tenant
    try:
        kebele = Tenant.objects.get(name='Bole 01')

        with schema_context(kebele.schema_name):
            # Get the clerk user
            clerk = User.objects.filter(role='clerk').first()

            if not clerk:
                print("No clerk found in the kebele tenant.")
                return

            # Create citizens if they don't exist
            if not Citizen.objects.exists():
                # Create 5 sample citizens
                citizens = [
                    {
                        'first_name': 'Abebe',
                        'middle_name': 'Kebede',
                        'last_name': 'Tadesse',
                        'date_of_birth': '1985-05-15',
                        'gender': Gender.MALE,
                        'phone_number': '+251911234567',
                        'email': '<EMAIL>',
                        'nationality': 'Ethiopian',
                        'marital_status': MaritalStatus.MARRIED,
                        'occupation': 'Teacher',
                        'blood_type': BloodType.A_POSITIVE,
                    },
                    {
                        'first_name': 'Sara',
                        'middle_name': 'Mohammed',
                        'last_name': 'Ali',
                        'date_of_birth': '1990-08-22',
                        'gender': Gender.FEMALE,
                        'phone_number': '+251922345678',
                        'email': '<EMAIL>',
                        'nationality': 'Ethiopian',
                        'marital_status': MaritalStatus.SINGLE,
                        'occupation': 'Doctor',
                        'blood_type': BloodType.B_POSITIVE,
                    },
                    {
                        'first_name': 'Daniel',
                        'middle_name': 'Tesfaye',
                        'last_name': 'Bekele',
                        'date_of_birth': '1978-12-10',
                        'gender': Gender.MALE,
                        'phone_number': '+251933456789',
                        'email': '<EMAIL>',
                        'nationality': 'Ethiopian',
                        'marital_status': MaritalStatus.DIVORCED,
                        'occupation': 'Engineer',
                        'blood_type': BloodType.O_NEGATIVE,
                    },
                    {
                        'first_name': 'Hiwot',
                        'middle_name': 'Girma',
                        'last_name': 'Haile',
                        'date_of_birth': '1995-03-28',
                        'gender': Gender.FEMALE,
                        'phone_number': '+251944567890',
                        'email': '<EMAIL>',
                        'nationality': 'Ethiopian',
                        'marital_status': MaritalStatus.SINGLE,
                        'occupation': 'Student',
                        'blood_type': BloodType.AB_POSITIVE,
                    },
                    {
                        'first_name': 'Yonas',
                        'middle_name': 'Alemu',
                        'last_name': 'Gebre',
                        'date_of_birth': '1982-11-05',
                        'gender': Gender.MALE,
                        'phone_number': '+251955678901',
                        'email': '<EMAIL>',
                        'nationality': 'Ethiopian',
                        'marital_status': MaritalStatus.MARRIED,
                        'occupation': 'Businessman',
                        'blood_type': BloodType.A_NEGATIVE,
                    },
                ]

                created_citizens = []
                for citizen_data in citizens:
                    citizen = Citizen.objects.create(
                        **citizen_data,
                        created_by=clerk
                    )
                    created_citizens.append(citizen)
                    print(f"Created citizen: {citizen.first_name} {citizen.middle_name}")

                # Create ID cards for some citizens
                # Get the kebele admin
                kebele_admin = User.objects.filter(role='kebele_admin').first()

                if kebele_admin:
                    # Create ID cards with different statuses
                    id_cards = [
                        {
                            'citizen': created_citizens[0],
                            'status': IDCardStatus.APPROVED,
                            'issue_date': datetime.now().date(),
                            'expiry_date': datetime.now().date() + timedelta(days=5*365),
                            'created_by': clerk,
                            'approved_by': kebele_admin,
                        },
                        {
                            'citizen': created_citizens[1],
                            'status': IDCardStatus.PENDING_APPROVAL,
                            'created_by': clerk,
                        },
                        {
                            'citizen': created_citizens[2],
                            'status': IDCardStatus.DRAFT,
                            'created_by': clerk,
                        },
                    ]

                    for id_card_data in id_cards:
                        id_card = IDCard.objects.create(**id_card_data)
                        print(f"Created ID card: {id_card.card_number} for {id_card.citizen.first_name}")

                        # Create workflow logs for the ID card
                        if id_card.status == IDCardStatus.APPROVED:
                            # Create submit log
                            WorkflowLog.objects.create(
                                id_card=id_card,
                                action=ApprovalAction.SUBMIT,
                                from_status=IDCardStatus.DRAFT,
                                to_status=IDCardStatus.PENDING_APPROVAL,
                                comment='Submitted for approval',
                                performed_by=clerk,
                                performed_at=datetime.now() - timedelta(days=2)
                            )

                            # Create approve log
                            WorkflowLog.objects.create(
                                id_card=id_card,
                                action=ApprovalAction.APPROVE,
                                from_status=IDCardStatus.PENDING_APPROVAL,
                                to_status=IDCardStatus.APPROVED,
                                comment='Approved',
                                performed_by=kebele_admin,
                                performed_at=datetime.now() - timedelta(days=1)
                            )
                        elif id_card.status == IDCardStatus.PENDING_APPROVAL:
                            # Create submit log
                            WorkflowLog.objects.create(
                                id_card=id_card,
                                action=ApprovalAction.SUBMIT,
                                from_status=IDCardStatus.DRAFT,
                                to_status=IDCardStatus.PENDING_APPROVAL,
                                comment='Submitted for approval',
                                performed_by=clerk,
                                performed_at=datetime.now() - timedelta(days=1)
                            )

                print(f"Created {len(created_citizens)} citizens and {len(id_cards)} ID cards.")
            else:
                print("Citizens already exist in the kebele tenant.")
    except Tenant.DoesNotExist:
        print("Kebele tenant not found.")


if __name__ == '__main__':
    print("Initializing data...")
    create_superuser()
    create_public_tenant()
    create_sample_tenants()
    create_sample_citizens()
    print("Data initialization complete.")
