#!/usr/bin/env python3
"""
Translation String Extractor for GoID System
Extracts all translatable strings from the codebase and creates templates
"""

import os
import re
import json
from pathlib import Path
from collections import defaultdict

class TranslationExtractor:
    def __init__(self, base_path='/app'):
        self.base_path = Path(base_path)
        self.extracted_strings = set()
        self.string_contexts = defaultdict(list)
        
    def extract_from_python_files(self):
        """Extract translatable strings from Python files"""
        patterns = [
            (r'_\([\'"]([^\'"]+)[\'"]\)', 'gettext'),
            (r'gettext\([\'"]([^\'"]+)[\'"]\)', 'gettext'),
            (r'ngettext\([\'"]([^\'"]+)[\'"]', 'ngettext'),
            (r'gettext_lazy\([\'"]([^\'"]+)[\'"]\)', 'gettext_lazy'),
            (r'ugettext\([\'"]([^\'"]+)[\'"]\)', 'ugettext'),
            (r'ugettext_lazy\([\'"]([^\'"]+)[\'"]\)', 'ugettext_lazy'),
        ]
        
        for root, dirs, files in os.walk(self.base_path):
            # Skip certain directories
            skip_dirs = ['__pycache__', '.git', 'node_modules', 'staticfiles', 'media', 'locale']
            if any(skip in root for skip in skip_dirs):
                continue
                
            for file in files:
                if file.endswith('.py'):
                    file_path = Path(root) / file
                    self._extract_from_file(file_path, patterns)
    
    def extract_from_templates(self):
        """Extract translatable strings from Django templates"""
        template_patterns = [
            (r'{%\s*trans\s+[\'"]([^\'"]+)[\'"]\s*%}', 'trans tag'),
            (r'{%\s*blocktrans\s*%}(.*?){%\s*endblocktrans\s*%}', 'blocktrans tag'),
            (r'\|\s*trans', 'trans filter'),
        ]
        
        for root, dirs, files in os.walk(self.base_path):
            if any(skip in root for skip in ['__pycache__', '.git', 'node_modules']):
                continue
                
            for file in files:
                if file.endswith('.html'):
                    file_path = Path(root) / file
                    self._extract_from_file(file_path, template_patterns)
    
    def _extract_from_file(self, file_path, patterns):
        """Extract strings from a specific file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            for pattern, context_type in patterns:
                matches = re.findall(pattern, content, re.MULTILINE | re.DOTALL)
                for match in matches:
                    if isinstance(match, tuple):
                        match = match[0]  # Take first group if multiple groups
                    
                    # Clean up the string
                    clean_string = match.strip()
                    if clean_string and len(clean_string) > 1:
                        self.extracted_strings.add(clean_string)
                        self.string_contexts[clean_string].append({
                            'file': str(file_path.relative_to(self.base_path)),
                            'type': context_type
                        })
                        
        except Exception as e:
            print(f"Warning: Could not process {file_path}: {e}")
    
    def generate_po_template(self, output_file):
        """Generate a .po template file with all extracted strings"""
        po_content = '''# GoID Translation Template
# Copyright (C) 2025 GoID Project
# This file is distributed under the same license as the GoID package.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: GoID 1.0\\n"
"Report-Msgid-Bugs-To: \\n"
"POT-Creation-Date: 2025-06-26 16:30+0000\\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\\n"
"Language-Team: LANGUAGE <<EMAIL>>\\n"
"Language: \\n"
"MIME-Version: 1.0\\n"
"Content-Type: text/plain; charset=UTF-8\\n"
"Content-Transfer-Encoding: 8bit\\n"

'''
        
        # Sort strings alphabetically
        sorted_strings = sorted(self.extracted_strings)
        
        for string in sorted_strings:
            # Add context comments
            contexts = self.string_contexts[string]
            for context in contexts[:3]:  # Limit to first 3 contexts
                po_content += f'#: {context["file"]} ({context["type"]})\n'
            
            # Escape quotes in the string
            escaped_string = string.replace('"', '\\"')
            po_content += f'msgid "{escaped_string}"\n'
            po_content += f'msgstr ""\n\n'
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(po_content)
        
        return len(sorted_strings)
    
    def generate_json_export(self, output_file):
        """Generate JSON export of all strings with contexts"""
        export_data = {
            'metadata': {
                'total_strings': len(self.extracted_strings),
                'extraction_date': '2025-06-26',
                'source': 'GoID System'
            },
            'strings': {}
        }
        
        for string in sorted(self.extracted_strings):
            export_data['strings'][string] = {
                'contexts': self.string_contexts[string],
                'translated': False,
                'priority': self._get_string_priority(string)
            }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        return len(self.extracted_strings)
    
    def _get_string_priority(self, string):
        """Determine priority of a string for translation"""
        high_priority_keywords = [
            'login', 'password', 'username', 'error', 'success', 'save', 'cancel',
            'dashboard', 'citizens', 'id cards', 'delete', 'edit', 'add'
        ]
        
        string_lower = string.lower()
        for keyword in high_priority_keywords:
            if keyword in string_lower:
                return 'high'
        
        if len(string) < 20:
            return 'medium'
        
        return 'low'
    
    def run_extraction(self):
        """Run the complete extraction process"""
        print("🔍 Starting translation string extraction...")
        
        print("📄 Extracting from Python files...")
        self.extract_from_python_files()
        python_count = len(self.extracted_strings)
        
        print("🌐 Extracting from template files...")
        self.extract_from_templates()
        total_count = len(self.extracted_strings)
        template_count = total_count - python_count
        
        print(f"✅ Extraction complete!")
        print(f"   Python files: {python_count} strings")
        print(f"   Template files: {template_count} strings")
        print(f"   Total unique: {total_count} strings")
        
        return total_count

def main():
    extractor = TranslationExtractor()
    
    # Run extraction
    total_strings = extractor.run_extraction()
    
    if total_strings > 0:
        # Generate .po template
        po_file = '/app/locale/template.pot'
        po_count = extractor.generate_po_template(po_file)
        print(f"📝 Generated .pot template: {po_file} ({po_count} strings)")
        
        # Generate JSON export
        json_file = '/app/locale/strings_export.json'
        json_count = extractor.generate_json_export(json_file)
        print(f"📊 Generated JSON export: {json_file} ({json_count} strings)")
        
        # Show some statistics
        print(f"\n📈 String Analysis:")
        high_priority = sum(1 for s in extractor.extracted_strings 
                           if extractor._get_string_priority(s) == 'high')
        medium_priority = sum(1 for s in extractor.extracted_strings 
                             if extractor._get_string_priority(s) == 'medium')
        low_priority = total_strings - high_priority - medium_priority
        
        print(f"   High priority: {high_priority}")
        print(f"   Medium priority: {medium_priority}")
        print(f"   Low priority: {low_priority}")
        
        print(f"\n💡 Next steps:")
        print(f"   1. Review the generated template.pot file")
        print(f"   2. Use it to update existing .po files")
        print(f"   3. Focus on high-priority strings first")
        print(f"   4. Use the JSON export for translation management tools")
    else:
        print("❌ No translatable strings found!")

if __name__ == '__main__':
    main()
