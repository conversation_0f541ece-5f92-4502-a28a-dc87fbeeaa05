import django_filters
from .models import IDCard


class IDCardFilter(django_filters.FilterSet):
    """Filter for IDCard model."""
    card_number = django_filters.CharFilter(lookup_expr='icontains')
    citizen_name = django_filters.CharFilter(method='filter_citizen_name')
    status = django_filters.ChoiceFilter(choices=IDCard._meta.get_field('status').choices)
    issue_date_from = django_filters.DateFilter(field_name='issue_date', lookup_expr='gte')
    issue_date_to = django_filters.DateFilter(field_name='issue_date', lookup_expr='lte')
    expiry_date_from = django_filters.DateFilter(field_name='expiry_date', lookup_expr='gte')
    expiry_date_to = django_filters.DateFilter(field_name='expiry_date', lookup_expr='lte')
    created_at_from = django_filters.DateFilter(field_name='created_at', lookup_expr='gte')
    created_at_to = django_filters.DateFilter(field_name='created_at', lookup_expr='lte')
    created_by = django_filters.NumberFilter(field_name='created_by__id')
    approved_by = django_filters.NumberFilter(field_name='approved_by__id')
    
    class Meta:
        model = IDCard
        fields = [
            'card_number', 'citizen', 'citizen_name', 'status',
            'issue_date_from', 'issue_date_to', 'expiry_date_from', 'expiry_date_to',
            'created_at_from', 'created_at_to', 'created_by', 'approved_by'
        ]
    
    def filter_citizen_name(self, queryset, name, value):
        """Filter ID cards by citizen name."""
        return queryset.filter(
            citizen__first_name__icontains=value
        ) | queryset.filter(
            citizen__middle_name__icontains=value
        ) | queryset.filter(
            citizen__last_name__icontains=value
        )
