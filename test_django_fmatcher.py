#!/usr/bin/env python3
"""
Test Django FMatcher integration
"""

import sys
import os
import django

# Add the backend directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from biometrics.fingerprint_processing import FingerprintProcessor
import json
import base64

def test_django_fmatcher():
    """Test the Django FMatcher integration"""
    print("🧪 Testing Django FMatcher Integration")
    print("=" * 50)
    
    try:
        processor = FingerprintProcessor()
        print(f"FMatcher JAR path: {processor.fmatcher_jar_path}")
        print(f"FMatcher directory: {processor.fmatcher_dir}")
        print(f"Match threshold: {processor.match_threshold}")
        
        # Check if FMatcher.jar exists
        if not os.path.exists(processor.fmatcher_jar_path):
            print(f"❌ FMatcher.jar not found at {processor.fmatcher_jar_path}")
            return False
        
        print("✅ FMatcher.jar found")
        
        # Create test template data (simulating real capture format)
        test_template_data = {
            'thumb_type': 'left',
            'capture_method': 'java_bridge', 
            'capture_time': '2025-06-26T02:15:13.570825',
            'ansi_iso_template': 'VGhpcyBpcyBhIHRlc3QgQU5TSS9JU08gdGVtcGxhdGUgZm9yIGR1cGxpY2F0ZSBkZXRlY3Rpb24=',
            'fingerprint_image': 'VGVzdCBpbWFnZSBkYXRh',
            'device_info': {
                'model': 'Futronic FS88H',
                'interface': 'java_bridge',
                'real_device': True,
                'ansi_sdk': True
            }
        }
        
        template_json = json.dumps(test_template_data)
        
        # Test duplicate checking with no existing templates
        print("\n🔍 Testing duplicate check with no existing templates...")
        result = processor.check_duplicates({
            'left_thumb_fingerprint': template_json,
            'right_thumb_fingerprint': None
        })
        
        print("Result:", result)
        
        if result['has_duplicates']:
            print("⚠️ Unexpected: Found duplicates when none should exist")
        else:
            print("✅ Correctly found no duplicates in empty database")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_django_fmatcher()
    if success:
        print("\n🎉 Django FMatcher integration test completed!")
        print("\n📝 Next steps:")
        print("   1. Register a citizen with fingerprints")
        print("   2. Try to register the same person again")
        print("   3. Check Django logs for duplicate detection messages")
    else:
        print("\n❌ Integration test failed")
