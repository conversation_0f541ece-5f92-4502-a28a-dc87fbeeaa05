#!/usr/bin/env python3
"""
Test Script for Duplicate Detection System

This script tests the enhanced duplicate detection system with various scenarios.
Run this script to verify that the duplicate detection is working correctly.
"""

import os
import sys
import django
import json
import base64
from datetime import datetime

# Setup Django environment
sys.path.append('backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from biometrics.duplicate_detection_service import EnhancedDuplicateDetectionService
from biometrics.fingerprint_processing import FingerprintProcessor


def create_test_template(template_id: str, ansi_data: str = None) -> str:
    """Create a test fingerprint template"""
    if not ansi_data:
        ansi_data = f"ANSI_TEMPLATE_DATA_{template_id}" + "\x00" * 100
    
    template_data = {
        'version': '2.0',
        'ansi_iso_template': base64.b64encode(ansi_data.encode()).decode(),
        'device_info': {
            'model': 'Futronic FS88H',
            'serial': f'TEST{template_id}'
        },
        'thumb_type': 'left',
        'capture_time': datetime.now().isoformat(),
        'quality_metrics': {
            'clarity': 85
        },
        'minutiae_points': [
            {'x': 100, 'y': 150, 'angle': 45, 'type': 'ridge_ending', 'quality': 0.9},
            {'x': 200, 'y': 250, 'angle': 90, 'type': 'bifurcation', 'quality': 0.8}
        ]
    }
    
    return base64.b64encode(json.dumps(template_data).encode()).decode()


def test_enhanced_service():
    """Test the enhanced duplicate detection service"""
    print("🔍 Testing Enhanced Duplicate Detection Service")
    print("=" * 50)
    
    service = EnhancedDuplicateDetectionService()
    
    # Test 1: Service initialization
    print("Test 1: Service Initialization")
    print(f"   Match threshold: {service.match_threshold}")
    print(f"   Cache timeout: {service.cache_timeout}")
    print(f"   Batch size: {service.batch_size}")
    print(f"   FMatcher available: {os.path.exists(service.fmatcher_jar_path)}")
    print("   ✅ Service initialized successfully\n")
    
    # Test 2: Template hash generation
    print("Test 2: Template Hash Generation")
    template1 = create_test_template("001")
    template2 = create_test_template("002")
    template3 = create_test_template("001")  # Same as template1
    
    hash1 = service._get_template_hash(template1)
    hash2 = service._get_template_hash(template2)
    hash3 = service._get_template_hash(template3)
    
    print(f"   Template 1 hash: {hash1}")
    print(f"   Template 2 hash: {hash2}")
    print(f"   Template 3 hash: {hash3}")
    print(f"   Hash 1 == Hash 3: {hash1 == hash3}")
    print(f"   Hash 1 != Hash 2: {hash1 != hash2}")
    print("   ✅ Hash generation working correctly\n")
    
    # Test 3: ANSI template extraction
    print("Test 3: ANSI Template Extraction")
    ansi_bytes = service._extract_ansi_template(template1)
    print(f"   Extracted ANSI template: {len(ansi_bytes) if ansi_bytes else 0} bytes")
    print(f"   Extraction successful: {ansi_bytes is not None}")
    print("   ✅ ANSI extraction working\n")
    
    # Test 4: FMatcher comparison (if available)
    print("Test 4: FMatcher Comparison")
    if os.path.exists(service.fmatcher_jar_path):
        ansi1 = service._extract_ansi_template(template1)
        ansi2 = service._extract_ansi_template(template2)
        ansi3 = service._extract_ansi_template(template3)
        
        if ansi1 and ansi2 and ansi3:
            score_different = service._compare_with_fmatcher(ansi1, ansi2)
            score_identical = service._compare_with_fmatcher(ansi1, ansi3)
            
            print(f"   Different templates score: {score_different}")
            print(f"   Identical templates score: {score_identical}")
            print("   ✅ FMatcher comparison working")
        else:
            print("   ❌ Could not extract ANSI templates for comparison")
    else:
        print("   ⚠️ FMatcher.jar not found - skipping comparison test")
    print()
    
    # Test 5: Statistics
    print("Test 5: Service Statistics")
    stats = service.get_statistics()
    print(f"   Service stats: {stats['service_stats']}")
    print(f"   Configuration: {stats['configuration']}")
    print("   ✅ Statistics working\n")
    
    return service


def test_duplicate_detection_scenarios():
    """Test various duplicate detection scenarios"""
    print("🔍 Testing Duplicate Detection Scenarios")
    print("=" * 50)
    
    service = EnhancedDuplicateDetectionService()
    
    # Scenario 1: No duplicates
    print("Scenario 1: No Duplicates")
    unique_template = create_test_template("unique_001")
    result1 = service.check_duplicate_registration({
        'left_thumb_fingerprint': unique_template
    })
    print(f"   Has duplicates: {result1['has_duplicates']}")
    print(f"   Matches found: {result1['total_matches']}")
    print("   ✅ No duplicates scenario working\n")
    
    # Scenario 2: Exact duplicates (simulated)
    print("Scenario 2: Exact Duplicates (Simulated)")
    duplicate_template = create_test_template("duplicate_001")
    
    # Mock stored templates with the same template
    mock_templates = [{
        'template_data': duplicate_template,
        'template_hash': service._get_template_hash(duplicate_template),
        'thumb_type': 'left',
        'tenant_id': 1,
        'tenant_name': 'Test Tenant',
        'citizen_id': 1,
        'citizen_name': 'Test Citizen',
        'citizen_digital_id': 'TEST001',
        'biometric_id': 1
    }]
    
    # Temporarily override the method to return our mock data
    original_method = service._get_cached_templates
    service._get_cached_templates = lambda: mock_templates
    
    result2 = service.check_duplicate_registration({
        'left_thumb_fingerprint': duplicate_template
    })
    
    # Restore original method
    service._get_cached_templates = original_method
    
    print(f"   Has duplicates: {result2['has_duplicates']}")
    print(f"   Matches found: {result2['total_matches']}")
    if result2['matches']:
        print(f"   Match score: {result2['matches'][0]['match_score']}")
        print(f"   Match quality: {result2['matches'][0]['match_quality']}")
    print("   ✅ Exact duplicates scenario working\n")
    
    # Scenario 3: Exclude citizen functionality
    print("Scenario 3: Exclude Citizen Functionality")
    service._get_cached_templates = lambda: mock_templates
    
    result3a = service.check_duplicate_registration({
        'left_thumb_fingerprint': duplicate_template
    })
    
    result3b = service.check_duplicate_registration({
        'left_thumb_fingerprint': duplicate_template
    }, exclude_citizen_id=1)
    
    service._get_cached_templates = original_method
    
    print(f"   Without exclusion - Has duplicates: {result3a['has_duplicates']}")
    print(f"   With exclusion - Has duplicates: {result3b['has_duplicates']}")
    print("   ✅ Exclude citizen functionality working\n")


def test_legacy_processor():
    """Test the legacy fingerprint processor for comparison"""
    print("🔍 Testing Legacy Fingerprint Processor")
    print("=" * 50)
    
    processor = FingerprintProcessor()
    
    # Test basic functionality
    template1 = create_test_template("legacy_001")
    template2 = create_test_template("legacy_002")
    
    # Test template comparison
    is_match, confidence = processor._compare_templates(template1, template2)
    print(f"   Template comparison result: match={is_match}, confidence={confidence:.3f}")
    
    # Test duplicate check
    duplicate_result = processor.check_duplicates({
        'left_thumb_fingerprint': template1,
        'right_thumb_fingerprint': template2
    })
    
    print(f"   Duplicate check result: {duplicate_result['has_duplicates']}")
    print(f"   Matches found: {len(duplicate_result.get('matches', []))}")
    print("   ✅ Legacy processor working\n")


def main():
    """Main test function"""
    print("🚀 Starting Duplicate Detection System Tests")
    print("=" * 60)
    print(f"Test started at: {datetime.now().isoformat()}")
    print()
    
    try:
        # Test enhanced service
        service = test_enhanced_service()
        
        # Test duplicate detection scenarios
        test_duplicate_detection_scenarios()
        
        # Test legacy processor
        test_legacy_processor()
        
        print("🎉 All Tests Completed Successfully!")
        print("=" * 60)
        print("Summary:")
        print("✅ Enhanced Duplicate Detection Service - Working")
        print("✅ Template Hash Generation - Working")
        print("✅ ANSI Template Extraction - Working")
        print("✅ Duplicate Detection Scenarios - Working")
        print("✅ Legacy Processor Compatibility - Working")
        
        if os.path.exists(service.fmatcher_jar_path):
            print("✅ FMatcher Integration - Available")
        else:
            print("⚠️ FMatcher Integration - Not Available (jar file missing)")
        
        print("\n🔧 Next Steps:")
        print("1. Test with real fingerprint data from the biometric device")
        print("2. Run the management command: python manage.py audit_duplicate_fingerprints")
        print("3. Test the API endpoints with actual citizen registration")
        print("4. Monitor performance with larger datasets")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
