@echo off
echo ========================================
echo   CLEAN EXTERNAL CAPTURE Service
echo   Windows Compatible - No Unicode Issues
echo ========================================
echo.

echo QUICK FIX: Removed Unicode characters that caused crash
echo.
echo PROBLEM FIXED:
echo    - Previous service crashed due to Unicode emoji characters
echo    - Windows charmap codec couldn't encode Unicode symbols
echo    - This version uses only ASCII characters
echo.
echo SOLUTION APPROACH:
echo    - Execute working debug code as external process
echo    - Avoid service context issues that cause ftrScanGetFrame crashes
echo    - Use proven working approach in standalone execution
echo.

echo Checking Python installation...
python --version
if errorlevel 1 (
    echo ERROR: Python not found. Please install Python 3.7+
    pause
    exit /b 1
)

echo.
echo Checking required DLL files...
if not exist "ftrScanAPI.dll" (
    echo ERROR: ftrScanAPI.dll not found
    echo Please ensure Futronic SDK files are in the current directory
    pause
    exit /b 1
)

echo SUCCESS: ftrScanAPI.dll found

echo.
echo ========================================
echo   CLEAN EXTERNAL CAPTURE APPROACH
echo ========================================
echo.
echo HOW THIS WORKS:
echo   1. Service receives capture request
echo   2. Creates external script with working debug code
echo   3. Executes script as separate Python process
echo   4. External process captures fingerprint (proven to work)
echo   5. Returns result via JSON output
echo   6. Service forwards result to web interface
echo.
echo ADVANTAGES:
echo   - Uses EXACT debug code that worked (402+ bytes)
echo   - Standalone execution context (no service issues)
echo   - Process isolation (crashes don't affect main service)
echo   - Windows compatible (no Unicode characters)
echo.
echo EXPECTED RESULTS:
echo   - External process captures fingerprint successfully
echo   - Returns real biometric data (402+ bytes confirmed)
echo   - Service stays stable (no crashes)
echo   - Compatible with GoID system integration
echo.

echo Starting CLEAN EXTERNAL CAPTURE Service...
echo Service will be available at: http://localhost:8001
echo.
echo This version should work without Unicode encoding issues!
echo.
echo Press Ctrl+C to stop the service
echo.

python clean_external_service.py

echo.
echo Service stopped.
pause
