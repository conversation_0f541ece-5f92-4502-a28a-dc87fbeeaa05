import React from 'react';
import { useLocalization } from '../contexts/LocalizationContext';

const SimpleTranslationTest = () => {
  const { t, currentLanguage, changeLanguage } = useLocalization();

  return (
    <div style={{ 
      position: 'fixed', 
      bottom: 10, 
      left: 10, 
      background: 'yellow', 
      padding: '20px', 
      border: '2px solid black',
      zIndex: 9999,
      fontFamily: currentLanguage === 'am' ? 'Noto Sans Ethiopic, serif' : 'Arial'
    }}>
      <h3>🧪 Simple Translation Test</h3>
      <p><strong>Current Language:</strong> {currentLanguage}</p>
      
      <div style={{ margin: '10px 0' }}>
        <button onClick={() => changeLanguage('en')} style={{ margin: '5px', padding: '10px' }}>
          English
        </button>
        <button onClick={() => changeLanguage('am')} style={{ margin: '5px', padding: '10px' }}>
          አማርኛ
        </button>
      </div>
      
      <div style={{ border: '1px solid black', padding: '10px', background: 'white' }}>
        <p><strong>Dashboard:</strong> {t('dashboard', 'DEFAULT_DASHBOARD')}</p>
        <p><strong>Citizens:</strong> {t('citizens', 'DEFAULT_CITIZENS')}</p>
        <p><strong>Login:</strong> {t('login', 'DEFAULT_LOGIN')}</p>
        <p><strong>Password:</strong> {t('password', 'DEFAULT_PASSWORD')}</p>
        <p><strong>Save:</strong> {t('save', 'DEFAULT_SAVE')}</p>
      </div>
      
      <div style={{ marginTop: '10px', fontSize: '12px' }}>
        <strong>Raw Test:</strong><br/>
        {currentLanguage === 'en' ? 'This is English' : 'ይህ አማርኛ ነው'}
      </div>
    </div>
  );
};

export default SimpleTranslationTest;
