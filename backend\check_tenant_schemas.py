#!/usr/bin/env python3
"""
Check tenant schemas and user tables.
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
sys.path.append('/app')
django.setup()

from django.db import connection
from django_tenants.utils import schema_context

def check_schemas():
    """Check tenant schemas and user tables."""
    cursor = connection.cursor()
    
    print('=== Checking Tenant Schemas ===')
    
    # List all tenant schemas
    cursor.execute("SELECT schema_name FROM tenants_tenant WHERE schema_name != 'public';")
    schemas = cursor.fetchall()
    print(f'Available tenant schemas: {len(schemas)}')
    for schema in schemas:
        print(f'  - {schema[0]}')
    
    # Check tables in each tenant schema
    for schema in schemas:
        schema_name = schema[0]
        print(f'\n=== Schema: {schema_name} ===')
        
        cursor.execute(f"SELECT table_name FROM information_schema.tables WHERE table_schema = '{schema_name}';")
        tables = cursor.fetchall()
        print(f'Tables: {len(tables)}')
        
        # Check specifically for auth_user table
        has_user_table = any(table[0] == 'auth_user' for table in tables)
        print(f'Has auth_user table: {has_user_table}')
        
        if has_user_table:
            cursor.execute(f"SELECT COUNT(*) FROM {schema_name}.auth_user;")
            user_count = cursor.fetchone()[0]
            print(f'Users in schema: {user_count}')
            
            if user_count > 0:
                cursor.execute(f"SELECT username, email FROM {schema_name}.auth_user LIMIT 5;")
                users = cursor.fetchall()
                print('Sample users:')
                for user in users:
                    print(f'  - {user[0]} ({user[1]})')
    
    print('\n=== Domain Check ===')
    cursor.execute("SELECT domain, tenant_id FROM tenants_domain ORDER BY tenant_id;")
    domains = cursor.fetchall()
    print('Domains:')
    for domain in domains:
        print(f'  - {domain[0]} (tenant: {domain[1]})')
    
    return True

if __name__ == "__main__":
    check_schemas()
