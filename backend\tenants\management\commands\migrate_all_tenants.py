from django.core.management.base import BaseCommand
from django.core.management import call_command
from tenants.models import Tenant
from django_tenants.utils import schema_context


class Command(BaseCommand):
    help = 'Migrate all tenant schemas'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Migrating all tenant schemas...'))
        
        # Get all tenants
        tenants = Tenant.objects.all()
        
        # Migrate each tenant schema
        for tenant in tenants:
            self.stdout.write(self.style.SUCCESS(f'Migrating schema for tenant: {tenant.name} ({tenant.schema_name})'))
            with schema_context(tenant.schema_name):
                call_command('migrate', verbosity=1)
        
        self.stdout.write(self.style.SUCCESS('All tenant schemas migrated successfully.'))
