# Transfer Approval Testing Guide 🧪

## 🎯 **Issues Fixed**

### **✅ Issue 1: Transfer Approval Buttons Not Visible**
**Root Cause**: Permission logic was correct, but needed proper test data and user setup.

**Solution Applied**:
- ✅ **Workflow tables created** in tenant schemas via `migrate_schemas`
- ✅ **Test transfer request exists**: TR2025060001 (Kebele14 → Kebele15, Status: pending)
- ✅ **Kebele15 leader created**: `<EMAIL>` / `password123`
- ✅ **Permission logic verified**: Only kebele leaders can approve transfers

### **✅ Issue 2: Navigation Menu Visibility for Subcity Admins**
**Root Cause**: Navigation used generic permissions (`manage_users`) instead of role-specific checks.

**Solution Applied**:
```javascript
// BEFORE: Generic permission check
if (hasPermission('approve_idcards') || hasPermission('manage_users')) {
  // Show Transfer/Clearance menus
}

// AFTER: Role and tenant-specific check
if (user?.tenant?.type === 'kebele' && (user?.role === 'kebele_leader' || user?.role === 'clerk')) {
  // Show Transfer/Clearance menus only for kebele users
}
```

## 🧪 **Testing Instructions**

### **Test 1: Transfer Approval Buttons (Destination Kebele Leader)**

#### **Step 1: Login as Destination Kebele Leader**
```
URL: http://localhost:3000/login
Email: <EMAIL>
Password: password123
```

#### **Step 2: Navigate to Transfers**
```
URL: http://localhost:3000/transfers
Expected: Should see pending transfer request TR2025060001
```

#### **Step 3: Verify Approval Buttons**
```
✅ Should see "Accept" button (green checkmark icon)
✅ Should see "Reject" button (red X icon)
✅ Buttons should be clickable
```

#### **Step 4: Test Accept Functionality**
```
1. Click "Accept" button
2. Fill review notes in dialog
3. Click "Accept" in dialog
4. Expected: Transfer should be completed immediately
5. Expected: Citizen should appear in Kebele15 citizens list
```

#### **Step 5: Test Reject Functionality** (Alternative)
```
1. Click "Reject" button
2. Fill review notes in dialog
3. Click "Reject" in dialog
4. Expected: Transfer status should change to 'rejected'
5. Expected: Citizen should remain in Kebele14
```

### **Test 2: Navigation Menu Visibility**

#### **Test 2A: Kebele Users (Should See Menus)**
```
Login as: <EMAIL> (kebele_leader)
Expected Navigation:
✅ Dashboard
✅ Citizens
✅ ID Cards
✅ Transfers ← Should be visible
✅ Clearances ← Should be visible
✅ Reports
```

```
Login as: <EMAIL> (kebele_leader, Kebele14)
Expected Navigation:
✅ Dashboard
✅ Citizens
✅ ID Cards
✅ Transfers ← Should be visible
✅ Clearances ← Should be visible
✅ Reports
```

#### **Test 2B: Subcity Users (Should NOT See Transfer/Clearance Menus)**
```
Login as: [subcity admin user]
Expected Navigation:
✅ Dashboard
✅ Citizens (if applicable)
✅ ID Cards (if applicable)
❌ Transfers ← Should NOT be visible
❌ Clearances ← Should NOT be visible
✅ Users
✅ Reports
```

### **Test 3: Cross-Kebele Transfer Workflow**

#### **Step 1: Create New Transfer (Source Kebele)**
```
1. Login as: <EMAIL> (Kebele14 leader)
2. Navigate to: /citizens
3. Select any citizen
4. Click "Transfer Citizen"
5. Select destination: Kebele15
6. Fill reason and description
7. Submit transfer request
8. Expected: Transfer created with 'pending' status
```

#### **Step 2: Review Transfer (Destination Kebele)**
```
1. Login as: <EMAIL> (Kebele15 leader)
2. Navigate to: /transfers
3. Should see new pending transfer request
4. Should see Accept/Reject buttons
5. Test approval workflow
```

## 📊 **Expected Results**

### **✅ Transfer Approval Buttons**
```
Destination Kebele Leader:
✅ Can see Accept/Reject buttons for pending transfers
✅ Can approve transfers → Citizen transferred immediately
✅ Can reject transfers → Citizen remains in source kebele

Source Kebele Leader:
✅ Can see Cancel button for pending outgoing transfers
✅ Can see Complete button for accepted transfers (if applicable)

Other Users:
❌ Cannot see approval buttons (no permission)
```

### **✅ Navigation Menu Visibility**
```
Kebele Users (Leaders & Clerks):
✅ Transfer menu visible
✅ Clearance menu visible
✅ Can access /transfers and /clearances pages

Subcity Users:
❌ Transfer menu hidden
❌ Clearance menu hidden
❌ Cannot access /transfers and /clearances pages (should redirect or show error)

City Users:
❌ Transfer menu hidden
❌ Clearance menu hidden
❌ Cannot access /transfers and /clearances pages
```

### **✅ Workflow Functionality**
```
Transfer Creation:
✅ Source kebele leader can create transfer requests
✅ Transfer request appears in both source and destination schemas
✅ Status starts as 'pending'

Transfer Approval:
✅ Destination kebele leader can approve/reject
✅ Approval immediately transfers citizen data
✅ Rejection keeps citizen in source kebele
✅ Status updates correctly ('completed' or 'rejected')

Data Transfer:
✅ Complete citizen data transferred (personal info, family, documents)
✅ New digital ID assigned in destination kebele
✅ Original citizen marked inactive in source kebele
✅ Transfer history maintained for audit
```

## 🔧 **Troubleshooting**

### **If Approval Buttons Not Visible**
```
1. Check user role: Must be 'kebele_leader'
2. Check tenant type: Must be 'kebele'
3. Check transfer destination: Must match user's tenant ID
4. Check transfer status: Must be 'pending'
5. Check browser console for JavaScript errors
```

### **If Navigation Menus Visible for Subcity Users**
```
1. Check user tenant type: Should be 'subcity'
2. Check navigation logic in Navbar.jsx
3. Clear browser cache and refresh
4. Check user context in browser dev tools
```

### **If Transfer Workflow Fails**
```
1. Check backend logs for errors
2. Verify tenant schemas have workflow tables
3. Check user permissions in database
4. Verify API endpoints are accessible
5. Check network requests in browser dev tools
```

## 🎉 **Success Criteria**

### **✅ All Tests Pass When:**
```
1. Kebele leaders can see and use transfer approval buttons
2. Subcity admins cannot see transfer/clearance navigation menus
3. Transfer workflow completes successfully (approve/reject)
4. Citizen data transfers correctly between kebeles
5. Navigation menus show appropriate items for each user type
6. No JavaScript errors in browser console
7. Backend API responses are successful (200/201 status codes)
```

---

**Ready for comprehensive testing! All fixes have been applied and the system should work as specified.** 🚀

**Key Test Users:**
- **Kebele14 Leader**: `<EMAIL>` / `password123`
- **Kebele15 Leader**: `<EMAIL>` / `password123`
- **Test Transfer**: TR2025060001 (Bell Estrada, Kebele14 → Kebele15, Status: pending)
