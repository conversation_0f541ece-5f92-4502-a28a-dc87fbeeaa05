# Generated manually for dynamic permissions system

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Permission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('codename', models.CharField(help_text='Unique permission code', max_length=100, unique=True)),
                ('name', models.CharField(help_text='Human readable permission name', max_length=255)),
                ('description', models.TextField(blank=True, help_text='Detailed description of what this permission allows')),
                ('category', models.CharField(help_text="Permission category (e.g., 'id_cards', 'citizens', 'users')", max_length=50)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Permission',
                'verbose_name_plural': 'Permissions',
                'ordering': ['category', 'name'],
            },
        ),
        migrations.CreateModel(
            name='RolePermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('superadmin', 'Super Admin'), ('city_admin', 'City Admin'), ('subcity_admin', 'Sub City Admin'), ('kebele_admin', 'Kebele Admin'), ('kebele_leader', 'Kebele Leader'), ('clerk', 'Clerk')], max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('permission', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='users.permission')),
            ],
            options={
                'verbose_name': 'Role Permission',
                'verbose_name_plural': 'Role Permissions',
            },
        ),
        migrations.CreateModel(
            name='UserPermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('granted', models.BooleanField(default=True, help_text='True to grant permission, False to explicitly deny')),
                ('granted_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField(blank=True, help_text='Optional expiration date for temporary permissions', null=True)),
                ('reason', models.TextField(blank=True, help_text='Reason for granting/denying this permission')),
                ('granted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='granted_permissions', to='users.user')),
                ('permission', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='users.permission')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='custom_permissions', to='users.user')),
            ],
            options={
                'verbose_name': 'User Permission',
                'verbose_name_plural': 'User Permissions',
            },
        ),
        migrations.AddConstraint(
            model_name='userpermission',
            constraint=models.UniqueConstraint(fields=('user', 'permission'), name='unique_user_permission'),
        ),
        migrations.AddConstraint(
            model_name='rolepermission',
            constraint=models.UniqueConstraint(fields=('role', 'permission'), name='unique_role_permission'),
        ),
    ]
