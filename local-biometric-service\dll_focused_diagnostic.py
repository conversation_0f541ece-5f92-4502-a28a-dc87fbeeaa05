#!/usr/bin/env python3
"""
DLL FOCUSED DIAGNOSTIC
Focus on making the DLL files work directly
Test every possible approach systematically
"""

import ctypes
from ctypes import cdll, c_int, c_uint, c_ulong, c_void_p, c_ubyte, c_bool, POINTER, byref, Structure
import os
import time

def test_all_dll_functions():
    """Test all available functions in the DLL"""
    
    try:
        print("=== DLL FOCUSED DIAGNOSTIC ===")
        print("Testing all available functions systematically")
        print()
        
        # Load DLL
        dll_path = os.path.abspath('./ftrScanAPI.dll')
        scan_api = cdll.LoadLibrary(dll_path)
        print("SUCCESS: DLL loaded")
        
        # Get all function names from DLL
        print("\n=== DISCOVERING ALL DLL FUNCTIONS ===")
        
        # Common Futronic function names to test
        function_names = [
            # Device management
            'ftrScanOpenDevice',
            'ftrScanCloseDevice',
            'ftrScanGetDeviceStatus',
            'ftrScanIsDeviceConnected',
            
            # Image capture functions
            'ftrScanGetFrame',
            'ftrScanGetImage', 
            'ftrScanGetImageEx',
            'ftrScanCaptureImage',
            'ftrScanAcquireImage',
            'ftrScanTakeImage',
            'ftrScanCapture',
            'ftrScanAcquire',
            
            # Finger detection
            'ftrScanIsFingerPresent',
            'ftrScanDetectFinger',
            'ftrScanCheckFinger',
            'ftrScanWaitForFinger',
            
            # Alternative approaches
            'ftrScanGetRawImage',
            'ftrScanGetBitmap',
            'ftrScanGetData',
            'ftrScanReadFrame',
            'ftrScanReadImage',
            
            # Version and info
            'ftrScanGetVersion',
            'ftrScanGetAPIVersion',
            'ftrScanGetDeviceInfo'
        ]
        
        available_functions = []
        for func_name in function_names:
            try:
                func = getattr(scan_api, func_name)
                available_functions.append(func_name)
                print(f"FOUND: {func_name}")
            except AttributeError:
                print(f"NOT FOUND: {func_name}")
        
        print(f"\nTotal functions found: {len(available_functions)}")
        
        # Initialize device first
        print("\n=== DEVICE INITIALIZATION ===")
        scan_api.ftrScanOpenDevice.restype = c_void_p
        scan_api.ftrScanOpenDevice.argtypes = []
        
        device_handle = scan_api.ftrScanOpenDevice()
        if not device_handle:
            print("ERROR: Cannot initialize device")
            return
        
        print(f"SUCCESS: Device initialized - Handle: {device_handle}")
        
        # Test each capture function systematically
        print("\n=== TESTING CAPTURE FUNCTIONS ===")
        
        capture_functions = [
            'ftrScanGetFrame',
            'ftrScanGetImage', 
            'ftrScanGetImageEx',
            'ftrScanCaptureImage',
            'ftrScanAcquireImage',
            'ftrScanTakeImage',
            'ftrScanCapture',
            'ftrScanAcquire',
            'ftrScanGetRawImage',
            'ftrScanGetBitmap',
            'ftrScanGetData',
            'ftrScanReadFrame',
            'ftrScanReadImage'
        ]
        
        for func_name in capture_functions:
            if func_name in available_functions:
                print(f"\n--- Testing {func_name} ---")
                test_capture_function(scan_api, device_handle, func_name)
        
        # Test alternative approaches
        print("\n=== TESTING ALTERNATIVE APPROACHES ===")
        test_alternative_approaches(scan_api, device_handle)
        
        # Close device
        scan_api.ftrScanCloseDevice.restype = c_int
        scan_api.ftrScanCloseDevice.argtypes = [c_void_p]
        scan_api.ftrScanCloseDevice(device_handle)
        print("\nDevice closed")
        
    except Exception as e:
        print(f"ERROR: DLL diagnostic failed: {e}")

def test_capture_function(scan_api, device_handle, func_name):
    """Test a specific capture function with different signatures"""
    
    try:
        func = getattr(scan_api, func_name)
        
        # Try different function signatures
        signatures = [
            # Signature 1: Standard (handle, buffer, size)
            {
                'restype': c_int,
                'argtypes': [c_void_p, POINTER(c_ubyte), POINTER(c_uint)],
                'name': 'Standard'
            },
            # Signature 2: With boolean return
            {
                'restype': c_bool,
                'argtypes': [c_void_p, POINTER(c_ubyte), POINTER(c_uint)],
                'name': 'Boolean return'
            },
            # Signature 3: Different size type
            {
                'restype': c_int,
                'argtypes': [c_void_p, POINTER(c_ubyte), c_uint],
                'name': 'Direct size'
            },
            # Signature 4: No size parameter
            {
                'restype': c_int,
                'argtypes': [c_void_p, POINTER(c_ubyte)],
                'name': 'No size'
            }
        ]
        
        for sig in signatures:
            try:
                print(f"  Trying {sig['name']} signature...")
                
                func.restype = sig['restype']
                func.argtypes = sig['argtypes']
                
                # Test with different buffer sizes
                buffer_sizes = [1024, 320*480, 65536]
                
                for buffer_size in buffer_sizes:
                    try:
                        print(f"    Buffer size: {buffer_size}")
                        
                        image_buffer = (c_ubyte * buffer_size)()
                        
                        if len(sig['argtypes']) == 3 and 'POINTER' in str(sig['argtypes'][2]):
                            # Has size pointer
                            frame_size = c_uint(buffer_size)
                            result = func(device_handle, image_buffer, byref(frame_size))
                            actual_size = frame_size.value
                        elif len(sig['argtypes']) == 3:
                            # Direct size
                            result = func(device_handle, image_buffer, buffer_size)
                            actual_size = buffer_size
                        else:
                            # No size parameter
                            result = func(device_handle, image_buffer)
                            actual_size = buffer_size
                        
                        print(f"    SUCCESS: {func_name} returned {result}, size: {actual_size}")
                        
                        if actual_size > 0:
                            print(f"    EXCELLENT: Got {actual_size} bytes of data!")
                            # Check first few bytes
                            first_bytes = [image_buffer[i] for i in range(min(10, actual_size))]
                            print(f"    First bytes: {first_bytes}")
                            return True  # Found working approach!
                        
                    except Exception as e:
                        print(f"    Failed: {e}")
                        continue
                
            except Exception as e:
                print(f"  Signature failed: {e}")
                continue
        
        print(f"  {func_name}: All signatures failed")
        return False
        
    except Exception as e:
        print(f"  ERROR: {func_name} test failed: {e}")
        return False

def test_alternative_approaches(scan_api, device_handle):
    """Test alternative approaches to get fingerprint data"""
    
    print("Testing alternative data access methods...")
    
    # Approach 1: Try to get device info/status that might contain data
    try:
        print("\n--- Approach 1: Device Status/Info ---")
        
        # Try different info functions
        info_functions = ['ftrScanGetDeviceStatus', 'ftrScanGetDeviceInfo']
        
        for func_name in info_functions:
            try:
                if hasattr(scan_api, func_name):
                    func = getattr(scan_api, func_name)
                    func.restype = c_int
                    func.argtypes = [c_void_p, POINTER(c_ubyte), c_uint]
                    
                    info_buffer = (c_ubyte * 1024)()
                    result = func(device_handle, info_buffer, 1024)
                    
                    print(f"  {func_name}: result={result}")
                    if result == 0:
                        data = bytes(info_buffer[:100])
                        print(f"  Data: {data[:20]}...")
                        
            except Exception as e:
                print(f"  {func_name} failed: {e}")
    
    except Exception as e:
        print(f"Approach 1 failed: {e}")
    
    # Approach 2: Try memory-mapped access
    try:
        print("\n--- Approach 2: Direct Memory Access ---")
        
        # Try to access device memory directly
        memory_sizes = [1024, 4096, 16384, 65536]
        
        for size in memory_sizes:
            try:
                # Create buffer and try to read directly
                buffer = (c_ubyte * size)()
                
                # Try different memory access patterns
                for i in range(0, size, 100):
                    buffer[i] = 0
                
                print(f"  Memory buffer {size}: Created successfully")
                
                # Check if any data appears in buffer
                non_zero = sum(1 for b in buffer if b != 0)
                if non_zero > 0:
                    print(f"  Found {non_zero} non-zero bytes!")
                
            except Exception as e:
                print(f"  Memory size {size} failed: {e}")
    
    except Exception as e:
        print(f"Approach 2 failed: {e}")
    
    # Approach 3: Try callback-based approach
    try:
        print("\n--- Approach 3: Callback Functions ---")
        
        # Define callback function type
        CALLBACK_FUNC = ctypes.WINFUNCTYPE(None, POINTER(c_ubyte), c_uint)
        
        def data_callback(data_ptr, size):
            try:
                print(f"  Callback received: {size} bytes")
                if size > 0:
                    # Try to read the data
                    data = ctypes.string_at(data_ptr, min(size, 100))
                    print(f"  Callback data: {data[:20]}...")
            except Exception as e:
                print(f"  Callback error: {e}")
        
        callback = CALLBACK_FUNC(data_callback)
        
        # Try to find callback-based functions
        callback_functions = [
            'ftrScanSetCallback',
            'ftrScanRegisterCallback', 
            'ftrScanStartCapture',
            'ftrScanStartAcquisition'
        ]
        
        for func_name in callback_functions:
            try:
                if hasattr(scan_api, func_name):
                    func = getattr(scan_api, func_name)
                    func.restype = c_int
                    func.argtypes = [c_void_p, CALLBACK_FUNC]
                    
                    result = func(device_handle, callback)
                    print(f"  {func_name}: result={result}")
                    
                    if result == 0:
                        print(f"  {func_name}: Callback registered, waiting...")
                        time.sleep(2)  # Wait for callback
                        
            except Exception as e:
                print(f"  {func_name} failed: {e}")
    
    except Exception as e:
        print(f"Approach 3 failed: {e}")

if __name__ == '__main__':
    print("=== DLL FOCUSED DIAGNOSTIC ===")
    print("Testing all possible approaches to make DLL work")
    print("Focus: Find ANY working method to get fingerprint data")
    print()
    
    test_all_dll_functions()
    
    print("\n=== DIAGNOSTIC COMPLETE ===")
    print("Review results above to find working approaches")
    print("Look for 'SUCCESS' and 'EXCELLENT' messages")
    
    input("\nPress Enter to exit...")
