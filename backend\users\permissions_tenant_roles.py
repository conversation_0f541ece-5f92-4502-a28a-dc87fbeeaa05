from rest_framework import permissions
from django.db import connection
from tenants.models import Tenant


class CanManageTenantRoles(permissions.BasePermission):
    """
    Permission class for managing roles within tenant hierarchy.
    
    - Superadmins can manage all roles
    - City admins can manage roles in their city and child subcities/kebeles
    - Subcity admins can manage roles in their subcity and child kebeles
    - Kebele admins can manage roles in their kebele only
    """
    
    def has_permission(self, request, view):
        user = request.user
        
        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False
        
        # Superusers and superadmins have full permission
        if user.is_superuser or user.effective_role == 'superadmin':
            return True
        
        # Check if user has role management permissions
        if hasattr(user, 'has_custom_permission'):
            if user.has_custom_permission('manage_permissions'):
                return True
        
        # Check role-based permissions for tenant hierarchy
        if user.effective_role in ['city_admin', 'subcity_admin', 'kebele_admin']:
            return True
        
        return False
    
    def has_object_permission(self, request, view, obj):
        user = request.user
        
        # Superusers and superadmins have full permission
        if user.is_superuser or user.effective_role == 'superadmin':
            return True
        
        # For Role objects, check if user can manage this role
        if hasattr(obj, 'can_be_managed_by_user'):
            return obj.can_be_managed_by_user(user)
        
        return False


class CanAssignTenantRoles(permissions.BasePermission):
    """
    Permission class for assigning roles to users within tenant hierarchy.
    """
    
    def has_permission(self, request, view):
        user = request.user
        
        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False
        
        # Superusers and superadmins have full permission
        if user.is_superuser or user.effective_role == 'superadmin':
            return True
        
        # Check role-based permissions for tenant hierarchy
        if user.effective_role in ['city_admin', 'subcity_admin', 'kebele_admin']:
            return True
        
        return False
    
    def _can_manage_user_in_tenant(self, manager_user, target_user, target_tenant=None):
        """Check if manager can manage target user in specific tenant context"""
        manager_tenant = manager_user.tenant
        if not manager_tenant:
            return False
        
        # Use target_user's tenant if target_tenant not specified
        if not target_tenant:
            target_tenant = target_user.tenant
        
        if not target_tenant:
            return False
        
        # Check tenant hierarchy
        if manager_user.effective_role == 'city_admin':
            # City admin can manage users in their city and child tenants
            if target_tenant == manager_tenant:  # Same city
                return True
            if target_tenant.type == 'subcity' and target_tenant.parent == manager_tenant:  # Child subcity
                return True
            if (target_tenant.type == 'kebele' and target_tenant.parent and 
                target_tenant.parent.parent == manager_tenant):  # Child kebele
                return True
        
        elif manager_user.effective_role == 'subcity_admin':
            # Subcity admin can manage users in their subcity and child kebeles
            if target_tenant == manager_tenant:  # Same subcity
                return True
            if target_tenant.type == 'kebele' and target_tenant.parent == manager_tenant:  # Child kebele
                return True
        
        elif manager_user.effective_role == 'kebele_admin':
            # Kebele admin can only manage users in their kebele
            if target_tenant == manager_tenant:
                return True
        
        return False


class TenantAwareRolePermission(permissions.BasePermission):
    """
    Tenant-aware permission class that considers both role permissions and tenant hierarchy.
    """
    
    def has_permission(self, request, view):
        user = request.user
        
        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False
        
        # Superusers and superadmins have full permission
        if user.is_superuser or user.effective_role == 'superadmin':
            return True
        
        # Get current tenant context
        current_tenant = getattr(request, 'tenant', None)
        if hasattr(connection, 'tenant') and connection.tenant:
            current_tenant = connection.tenant
        
        # Check if user has access to current tenant
        if current_tenant and not self._has_tenant_access(user, current_tenant):
            return False
        
        return True
    
    def _has_tenant_access(self, user, tenant):
        """Check if user has access to the specified tenant"""
        user_tenant = user.tenant
        if not user_tenant:
            return False
        
        # Direct match
        if user_tenant == tenant:
            return True
        
        # Check hierarchy based on user's role
        if user.effective_role == 'city_admin':
            # City admin can access subcities and kebeles under their city
            if tenant.type == 'subcity' and tenant.parent == user_tenant:
                return True
            if tenant.type == 'kebele' and tenant.parent and tenant.parent.parent == user_tenant:
                return True
        
        elif user.effective_role == 'subcity_admin':
            # Subcity admin can access kebeles under their subcity
            if tenant.type == 'kebele' and tenant.parent == user_tenant:
                return True
        
        return False


def get_manageable_tenants_for_user(user):
    """Get all tenants that a user can manage based on their role and tenant"""
    if user.is_superuser or user.effective_role == 'superadmin':
        return Tenant.objects.all()
    
    user_tenant = user.tenant
    if not user_tenant:
        return Tenant.objects.none()
    
    manageable_tenants = [user_tenant]  # User can always manage their own tenant
    
    if user.effective_role == 'city_admin':
        # City admin can manage their city and all child subcities/kebeles
        manageable_tenants.extend(user_tenant.get_child_tenants())
    
    elif user.effective_role == 'subcity_admin':
        # Subcity admin can manage their subcity and child kebeles
        manageable_tenants.extend(user_tenant.get_child_tenants())
    
    # Kebele admin can only manage their own kebele (already included)
    
    return Tenant.objects.filter(id__in=[t.id for t in manageable_tenants])


def get_users_in_manageable_tenants(user):
    """Get all users that the current user can manage based on tenant hierarchy"""
    from users.models import User
    
    if user.is_superuser or user.effective_role == 'superadmin':
        return User.objects.all()
    
    manageable_tenants = get_manageable_tenants_for_user(user)
    return User.objects.filter(tenant__in=manageable_tenants, is_active=True)


def can_user_manage_role_in_tenant(user, role, tenant):
    """Check if a user can manage a specific role in a specific tenant"""
    # Superusers can manage everything
    if user.is_superuser or user.effective_role == 'superadmin':
        return True
    
    # Check if user can manage the role
    if not role.can_be_managed_by_user(user):
        return False
    
    # Check if role can be assigned in the tenant
    if not role.can_be_assigned_in_tenant(tenant):
        return False
    
    # Check if user has access to the tenant
    manageable_tenants = get_manageable_tenants_for_user(user)
    return tenant in manageable_tenants


def get_assignable_roles_for_user_in_tenant(user, tenant):
    """Get all roles that a user can assign in a specific tenant"""
    from users.models import Role
    
    if user.is_superuser or user.effective_role == 'superadmin':
        # Superusers can assign any active role
        return Role.objects.filter(is_active=True)
    
    # Get roles that user can manage
    manageable_roles = Role.get_manageable_roles_for_user(user, tenant)
    
    # Filter roles that can be assigned in the target tenant
    assignable_roles = []
    for role in manageable_roles:
        if role.can_be_assigned_in_tenant(tenant):
            assignable_roles.append(role)
    
    return Role.objects.filter(id__in=[r.id for r in assignable_roles])
