import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Grid,
  TextField,
  Button,
  Switch,
  FormControlLabel,
  Typography,
  Alert,
  Chip,
  Divider,
  IconButton,
  Tooltip,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Palette as PaletteIcon,
  Public as PublicIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Preview as PreviewIcon,
} from '@mui/icons-material';
import axios from '../../utils/axios';
import { useAuth } from '../../contexts/AuthContext';

const TenantThemeManager = () => {
  const { user } = useAuth();
  const [tenants, setTenants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [previewOpen, setPreviewOpen] = useState(false);
  const [selectedTenant, setSelectedTenant] = useState(null);

  useEffect(() => {
    fetchTenants();
  }, []);

  const fetchTenants = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/tenants/');
      setTenants(response.data.results || response.data);
      setError('');
    } catch (err) {
      setError('Failed to fetch tenants');
      console.error('Error fetching tenants:', err);
    } finally {
      setLoading(false);
    }
  };

  const updateTheme = async (tenantId, themeData) => {
    try {
      setSaving(true);
      await axios.patch(`/api/tenants/${tenantId}/update_theme/`, themeData);
      
      // Update local state
      setTenants(prev => prev.map(tenant => 
        tenant.id === tenantId 
          ? { ...tenant, ...themeData }
          : tenant
      ));
      
      setSuccess('Theme updated successfully!');
      setTimeout(() => setSuccess(''), 3000);
    } catch (err) {
      console.error('Error updating theme:', err);

      // Check if it's a permission error
      if (err.response?.status === 403) {
        setError('Access denied: Only superadmins can update tenant themes');
      } else {
        setError('Failed to update theme');
      }
    } finally {
      setSaving(false);
    }
  };

  const updatePublicAccess = async (tenantId, accessData) => {
    try {
      setSaving(true);
      await axios.patch(`/api/tenants/${tenantId}/update_public_access/`, accessData);
      
      // Update local state
      setTenants(prev => prev.map(tenant => 
        tenant.id === tenantId 
          ? { ...tenant, ...accessData }
          : tenant
      ));
      
      setSuccess('Public access settings updated successfully!');
      setTimeout(() => setSuccess(''), 3000);
    } catch (err) {
      console.error('Error updating public access:', err);

      // Check if it's a permission error
      if (err.response?.status === 403) {
        setError('Access denied: Only superadmins can update tenant settings');
      } else {
        setError('Failed to update public access settings');
      }
    } finally {
      setSaving(false);
    }
  };

  const handleColorChange = (tenantId, colorType, colorValue) => {
    updateTheme(tenantId, { [colorType]: colorValue });
  };

  const handlePublicAccessToggle = (tenantId, setting, value) => {
    updatePublicAccess(tenantId, { [setting]: value });
  };

  const ColorPicker = ({ tenant, colorType, label }) => {
    const currentColor = tenant[colorType] || '#ff8f00';

    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <TextField
          label={label}
          value={currentColor}
          InputProps={{
            readOnly: true,
            startAdornment: (
              <Box
                sx={{
                  width: 24,
                  height: 24,
                  backgroundColor: currentColor,
                  border: '1px solid #ccc',
                  borderRadius: 1,
                  mr: 1,
                }}
              />
            ),
          }}
          sx={{ minWidth: 200 }}
          size="small"
        />

        <input
          type="color"
          value={currentColor}
          onChange={(e) => handleColorChange(tenant.id, colorType, e.target.value)}
          style={{
            width: 50,
            height: 40,
            border: 'none',
            borderRadius: 8,
            cursor: 'pointer',
            backgroundColor: 'transparent',
          }}
          title={`Pick ${label}`}
        />
      </Box>
    );
  };

  const TenantCard = ({ tenant }) => (
    <Card 
      sx={{ 
        mb: 2, 
        border: `2px solid ${tenant.primary_color || '#ff8f00'}`,
        '&:hover': { 
          boxShadow: `0 4px 20px ${tenant.primary_color || '#ff8f00'}40` 
        }
      }}
    >
      <CardHeader
        title={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="h6">{tenant.name}</Typography>
            <Chip 
              label={tenant.type} 
              size="small" 
              sx={{ 
                backgroundColor: tenant.primary_color || '#ff8f00',
                color: 'white',
                fontWeight: 'bold'
              }}
            />
          </Box>
        }
        action={
          <Tooltip title="Preview Theme">
            <IconButton 
              onClick={() => {
                setSelectedTenant(tenant);
                setPreviewOpen(true);
              }}
              sx={{ color: tenant.primary_color || '#ff8f00' }}
            >
              <PreviewIcon />
            </IconButton>
          </Tooltip>
        }
        sx={{ 
          backgroundColor: `${tenant.primary_color || '#ff8f00'}10`,
          borderBottom: `1px solid ${tenant.primary_color || '#ff8f00'}30`
        }}
      />
      <CardContent>
        <Grid container spacing={3}>
          {/* Theme Colors Section */}
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <PaletteIcon sx={{ mr: 1, color: tenant.primary_color || '#ff8f00' }} />
              <Typography variant="subtitle1" fontWeight="bold">
                Theme Colors
              </Typography>
            </Box>
            
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <ColorPicker 
                  tenant={tenant} 
                  colorType="primary_color" 
                  label="Primary Color" 
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <ColorPicker 
                  tenant={tenant} 
                  colorType="secondary_color" 
                  label="Secondary Color" 
                />
              </Grid>
            </Grid>
          </Grid>

          {/* Public Access Section */}
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <PublicIcon sx={{ mr: 1, color: tenant.secondary_color || '#ef6c00' }} />
              <Typography variant="subtitle1" fontWeight="bold">
                Public Access Settings
              </Typography>
            </Box>
            
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={tenant.public_id_application_enabled || false}
                    onChange={(e) => handlePublicAccessToggle(
                      tenant.id,
                      'public_id_application_enabled',
                      e.target.checked
                    )}
                    sx={{
                      '& .MuiSwitch-switchBase.Mui-checked': {
                        color: tenant.primary_color || '#ff8f00',
                      },
                      '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                        backgroundColor: tenant.primary_color || '#ff8f00',
                      },
                    }}
                  />
                }
                label="Allow Public ID Card Applications"
              />

              <Typography variant="body2" color="text.secondary" sx={{ ml: 4, mt: 1 }}>
                When enabled, citizens can apply for ID cards from this tenant through the public portal
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );

  // Check if user is superadmin
  if (!user?.is_superuser && user?.role !== 'superadmin') {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          <Typography variant="h6" gutterBottom>
            Access Denied
          </Typography>
          <Typography>
            Only superadmins can access the Tenant Theme Manager.
            You need superadmin privileges to manage tenant themes and public access settings.
          </Typography>
        </Alert>
      </Box>
    );
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" fontWeight="bold">
          Tenant Theme & Access Manager
        </Typography>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={fetchTenants}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>

      {/* Alerts */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}
      
      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      {/* Tenant Cards */}
      {tenants.map((tenant) => (
        <TenantCard key={tenant.id} tenant={tenant} />
      ))}

      {/* Preview Dialog */}
      <Dialog 
        open={previewOpen} 
        onClose={() => setPreviewOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Theme Preview: {selectedTenant?.name}
        </DialogTitle>
        <DialogContent>
          {selectedTenant && (
            <Box sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom>
                Color Scheme Preview
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Box
                    sx={{
                      p: 3,
                      backgroundColor: selectedTenant.primary_color || '#ff8f00',
                      color: 'white',
                      borderRadius: 2,
                      textAlign: 'center',
                    }}
                  >
                    <Typography variant="h6">Primary Color</Typography>
                    <Typography variant="body2">
                      {selectedTenant.primary_color || '#ff8f00'}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box
                    sx={{
                      p: 3,
                      backgroundColor: selectedTenant.secondary_color || '#ef6c00',
                      color: 'white',
                      borderRadius: 2,
                      textAlign: 'center',
                    }}
                  >
                    <Typography variant="h6">Secondary Color</Typography>
                    <Typography variant="body2">
                      {selectedTenant.secondary_color || '#ef6c00'}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Loading Overlay */}
      {saving && (
        <Box
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 9999,
          }}
        >
          <CircularProgress />
        </Box>
      )}
    </Box>
  );
};

export default TenantThemeManager;
