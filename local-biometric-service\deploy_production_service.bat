@echo off
echo ========================================
echo    GOID PRODUCTION DEPLOYMENT
echo ========================================
echo.
echo 🚀 DEPLOYING PRODUCTION BIOMETRIC SERVICE
echo.
echo 📦 PRODUCTION PACKAGE INCLUDES:
echo   ✅ Final Working Service (crash-proof)
echo   ✅ System Tray Application
echo   ✅ Auto-startup with Windows
echo   ✅ Auto-restart on crashes
echo   ✅ Professional web interface
echo.
echo 🎯 DEPLOYMENT STEPS:
echo   1. Installing Python dependencies
echo   2. Setting up system tray service
echo   3. Configuring auto-startup
echo   4. Starting production service
echo.

echo 📦 Installing required packages...
pip install flask flask-cors pystray pillow

echo.
echo 🔧 Setting up production service...

echo.
echo 🚀 Starting GoID Biometric Tray Service...
echo.
echo ✅ PRODUCTION FEATURES:
echo   - Crash-proof biometric service
echo   - 3-second finger detection timeout
echo   - Smart behavioral detection
echo   - System tray management
echo   - Auto-restart functionality
echo   - Windows startup integration
echo.
echo 📱 WEB INTERFACE: http://localhost:8001
echo 🔧 TRAY ICON: Right-click for options
echo.
echo Keep this window open during initial setup...
echo.

python tray_service.py

echo.
echo Production deployment completed.
pause
