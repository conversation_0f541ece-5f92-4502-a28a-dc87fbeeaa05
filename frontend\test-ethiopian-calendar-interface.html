<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ethiopian Calendar Interface Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .calendar-picker {
            display: flex;
            gap: 15px;
            margin: 20px 0;
        }
        .select-group {
            flex: 1;
        }
        select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 4px;
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        .conversion {
            margin: 10px 0;
            padding: 10px;
            background-color: #f0f0f0;
            border-radius: 4px;
        }
        .today-btn {
            background-color: #4caf50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 0;
        }
        .today-btn:hover {
            background-color: #45a049;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <h1>Ethiopian Calendar Interface Test</h1>
    
    <div class="highlight">
        <h3>🎯 True Ethiopian Calendar Experience</h3>
        <p><strong>Users see:</strong> Ethiopian Year 2017, Meskerem/Tikimt months, 1-30 days</p>
        <p><strong>System gets:</strong> Converted Gregorian date for storage</p>
    </div>

    <div class="card">
        <h2>Current Date Information</h2>
        <div id="current-date-info"></div>
    </div>

    <div class="card">
        <h2>Ethiopian Calendar Picker</h2>
        <p>Select a date using Ethiopian calendar (Year 2017 = Current Year)</p>
        
        <div class="calendar-picker">
            <div class="select-group">
                <label for="year-select">Ethiopian Year</label>
                <select id="year-select">
                    <option value="">Select Year</option>
                </select>
            </div>
            <div class="select-group">
                <label for="month-select">Ethiopian Month</label>
                <select id="month-select">
                    <option value="">Select Month</option>
                </select>
            </div>
            <div class="select-group">
                <label for="day-select">Day</label>
                <select id="day-select">
                    <option value="">Select Day</option>
                </select>
            </div>
        </div>

        <button class="today-btn" onclick="setToday()">Set to Today (Ethiopian Calendar)</button>

        <div id="selection-result"></div>
    </div>

    <div class="card">
        <h2>Conversion Results</h2>
        <div id="conversion-results"></div>
    </div>

    <script>
        // Ethiopian calendar constants and functions
        const ETHIOPIAN_MONTHS_EN = [
            'Meskerem', 'Tikimt', 'Hidar', 'Tahsas', 'Tir', 'Yekatit',
            'Megabit', 'Miazia', 'Ginbot', 'Sene', 'Hamle', 'Nehase', 'Pagume'
        ];

        const ETHIOPIAN_MONTHS_AM = [
            'መስከረም', 'ጥቅምት', 'ሕዳር', 'ታሕሳስ', 'ጥር', 'የካቲት',
            'መጋቢት', 'ሚያዝያ', 'ግንቦት', 'ሰኔ', 'ሐምሌ', 'ነሐሴ', 'ጳጉሜ'
        ];

        // Ethiopian calendar conversion functions
        function gregorianToEthiopian(gregorianDate) {
            const EPOCH_OFFSET = 1723856;
            const gregorianYear = gregorianDate.getFullYear();
            const gregorianMonth = gregorianDate.getMonth() + 1;
            const gregorianDay = gregorianDate.getDate();
            
            let daysSinceGregorianEpoch = 0;
            
            for (let year = 1; year < gregorianYear; year++) {
                daysSinceGregorianEpoch += isGregorianLeapYear(year) ? 366 : 365;
            }
            
            const daysInGregorianMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
            if (isGregorianLeapYear(gregorianYear)) {
                daysInGregorianMonth[1] = 29;
            }
            
            for (let month = 1; month < gregorianMonth; month++) {
                daysSinceGregorianEpoch += daysInGregorianMonth[month - 1];
            }
            
            daysSinceGregorianEpoch += gregorianDay;
            const daysSinceEthiopianEpoch = daysSinceGregorianEpoch - EPOCH_OFFSET;
            
            if (daysSinceEthiopianEpoch < 1) return null;
            
            let ethiopianYear = 1;
            let remainingDays = daysSinceEthiopianEpoch;
            
            while (remainingDays > getDaysInEthiopianYear(ethiopianYear)) {
                remainingDays -= getDaysInEthiopianYear(ethiopianYear);
                ethiopianYear++;
            }
            
            let ethiopianMonth = 1;
            while (remainingDays > getDaysInEthiopianMonth(ethiopianMonth, ethiopianYear)) {
                remainingDays -= getDaysInEthiopianMonth(ethiopianMonth, ethiopianYear);
                ethiopianMonth++;
            }
            
            return {
                year: ethiopianYear,
                month: ethiopianMonth,
                day: remainingDays
            };
        }

        function ethiopianToGregorian(year, month, day) {
            const EPOCH_OFFSET = 1723856;
            let daysSinceEthiopianEpoch = 0;
            
            for (let y = 1; y < year; y++) {
                daysSinceEthiopianEpoch += getDaysInEthiopianYear(y);
            }
            
            for (let m = 1; m < month; m++) {
                daysSinceEthiopianEpoch += getDaysInEthiopianMonth(m, year);
            }
            
            daysSinceEthiopianEpoch += day;
            const daysSinceGregorianEpoch = daysSinceEthiopianEpoch + EPOCH_OFFSET;
            
            let gregorianYear = 1;
            let remainingDays = daysSinceGregorianEpoch;
            
            while (remainingDays > (isGregorianLeapYear(gregorianYear) ? 366 : 365)) {
                remainingDays -= isGregorianLeapYear(gregorianYear) ? 366 : 365;
                gregorianYear++;
            }
            
            const daysInGregorianMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
            if (isGregorianLeapYear(gregorianYear)) {
                daysInGregorianMonth[1] = 29;
            }
            
            let gregorianMonth = 1;
            while (remainingDays > daysInGregorianMonth[gregorianMonth - 1]) {
                remainingDays -= daysInGregorianMonth[gregorianMonth - 1];
                gregorianMonth++;
            }
            
            return new Date(gregorianYear, gregorianMonth - 1, remainingDays);
        }

        function isGregorianLeapYear(year) {
            return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
        }

        function getDaysInEthiopianYear(year) {
            return (year % 4 === 3) ? 366 : 365;
        }

        function getDaysInEthiopianMonth(month, year) {
            if (month <= 12) return 30;
            return (year % 4 === 3) ? 6 : 5;
        }

        function formatEthiopianDate(ethiopianDate, format = 'DD/MM/YYYY', language = 'en') {
            const monthNames = language === 'am' ? ETHIOPIAN_MONTHS_AM : ETHIOPIAN_MONTHS_EN;
            const monthName = monthNames[ethiopianDate.month - 1] || '';
            
            const day = ethiopianDate.day.toString().padStart(2, '0');
            const month = ethiopianDate.month.toString().padStart(2, '0');
            const year = ethiopianDate.year.toString();
            
            return format
                .replace('DD', day)
                .replace('MM', month)
                .replace('MMM', monthName)
                .replace('YYYY', year);
        }

        // UI Functions
        function initializeSelectors() {
            const yearSelect = document.getElementById('year-select');
            const monthSelect = document.getElementById('month-select');
            
            // Populate years (2010-2025 Ethiopian)
            for (let year = 2010; year <= 2025; year++) {
                const option = document.createElement('option');
                option.value = year;
                option.textContent = year + (year === getCurrentEthiopianYear() ? ' (Current)' : '');
                yearSelect.appendChild(option);
            }
            
            // Populate months
            ETHIOPIAN_MONTHS_EN.forEach((month, index) => {
                const option = document.createElement('option');
                option.value = index + 1;
                option.textContent = month;
                monthSelect.appendChild(option);
            });
            
            // Add event listeners
            yearSelect.addEventListener('change', updateDaySelector);
            monthSelect.addEventListener('change', updateDaySelector);
            document.getElementById('day-select').addEventListener('change', updateResult);
        }

        function updateDaySelector() {
            const year = parseInt(document.getElementById('year-select').value);
            const month = parseInt(document.getElementById('month-select').value);
            const daySelect = document.getElementById('day-select');
            
            daySelect.innerHTML = '<option value="">Select Day</option>';
            
            if (year && month) {
                const maxDays = getDaysInEthiopianMonth(month, year);
                for (let day = 1; day <= maxDays; day++) {
                    const option = document.createElement('option');
                    option.value = day;
                    option.textContent = day;
                    daySelect.appendChild(option);
                }
            }
            
            updateResult();
        }

        function updateResult() {
            const year = parseInt(document.getElementById('year-select').value);
            const month = parseInt(document.getElementById('month-select').value);
            const day = parseInt(document.getElementById('day-select').value);
            
            const resultDiv = document.getElementById('selection-result');
            const conversionDiv = document.getElementById('conversion-results');
            
            if (year && month && day) {
                const ethiopianDate = { year, month, day };
                const gregorianDate = ethiopianToGregorian(year, month, day);
                
                resultDiv.innerHTML = `
                    <div class="result">
                        <h4>Selected Ethiopian Date:</h4>
                        <p><strong>Ethiopian:</strong> ${formatEthiopianDate(ethiopianDate, 'DD MMM YYYY', 'en')}</p>
                        <p><strong>Amharic:</strong> ${formatEthiopianDate(ethiopianDate, 'DD MMM YYYY', 'am')}</p>
                    </div>
                `;
                
                conversionDiv.innerHTML = `
                    <div class="conversion">
                        <h4>🔄 Automatic Conversion:</h4>
                        <p><strong>User Selected:</strong> Ethiopian ${year}/${month}/${day}</p>
                        <p><strong>System Stores:</strong> Gregorian ${gregorianDate.toISOString().split('T')[0]}</p>
                        <p><strong>Display Format:</strong> ${gregorianDate.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}</p>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = '';
                conversionDiv.innerHTML = '<p>Select year, month, and day to see conversion.</p>';
            }
        }

        function setToday() {
            const today = new Date();
            const ethiopianToday = gregorianToEthiopian(today);
            
            document.getElementById('year-select').value = ethiopianToday.year;
            document.getElementById('month-select').value = ethiopianToday.month;
            updateDaySelector();
            document.getElementById('day-select').value = ethiopianToday.day;
            updateResult();
        }

        function getCurrentEthiopianYear() {
            return gregorianToEthiopian(new Date()).year;
        }

        function displayCurrentDate() {
            const today = new Date();
            const ethiopianToday = gregorianToEthiopian(today);
            
            document.getElementById('current-date-info').innerHTML = `
                <div class="conversion">
                    <h4>📅 Today's Date:</h4>
                    <p><strong>Ethiopian:</strong> ${formatEthiopianDate(ethiopianToday, 'DD MMM YYYY', 'en')} (Year ${ethiopianToday.year})</p>
                    <p><strong>Amharic:</strong> ${formatEthiopianDate(ethiopianToday, 'DD MMM YYYY', 'am')}</p>
                    <p><strong>Gregorian:</strong> ${today.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}</p>
                </div>
            `;
        }

        // Initialize the interface
        document.addEventListener('DOMContentLoaded', function() {
            initializeSelectors();
            displayCurrentDate();
        });
    </script>
</body>
</html>
