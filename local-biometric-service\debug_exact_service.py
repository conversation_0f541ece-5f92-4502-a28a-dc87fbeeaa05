#!/usr/bin/env python3
"""
DEBUG EXACT Service
Uses the EXACT same approach as the successful debug script
No Flask - just basic HTTP server with working SDK calls
"""

import ctypes
from ctypes import cdll, c_int, c_uint, c_void_p, c_ubyte, POINTER, byref
import time
import base64
import json
import os
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler

class DebugExactSDKManager:
    """SDK manager using EXACT debug approach"""
    
    def __init__(self):
        self.scan_api = None
        self.device_handle = None
        self.is_initialized = False
        
        print("=== LOADING SDK WITH EXACT DEBUG CONFIGURATION ===")
        self._load_sdk_debug_exact()
    
    def _load_sdk_debug_exact(self):
        """Load SDK EXACTLY like successful debug"""
        try:
            # Step 1: Load SDK (EXACT same as debug)
            print("Step 1: Loading SDK...")
            dll_path = os.path.abspath('./ftrScanAPI.dll')
            self.scan_api = cdll.LoadLibrary(dll_path)
            print("✅ SDK loaded successfully")
            
            # Step 2: Configure function signatures (EXACT same as debug)
            print("Step 2: Configuring function signatures...")
            self.scan_api.ftrScanOpenDevice.restype = c_void_p
            self.scan_api.ftrScanOpenDevice.argtypes = []
            
            self.scan_api.ftrScanCloseDevice.restype = c_int
            self.scan_api.ftrScanCloseDevice.argtypes = [c_void_p]
            
            self.scan_api.ftrScanGetFrame.restype = c_int
            self.scan_api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(c_ubyte), POINTER(c_uint)]
            
            self.scan_api.ftrScanIsFingerPresent.restype = c_int
            self.scan_api.ftrScanIsFingerPresent.argtypes = [c_void_p, POINTER(c_int)]
            
            print("✅ Function signatures configured")
            
            # Step 3: Initialize device (EXACT same as debug)
            print("Step 3: Initializing device...")
            self.device_handle = self.scan_api.ftrScanOpenDevice()
            
            if self.device_handle:
                self.is_initialized = True
                print(f"✅ Device initialized - Handle: {self.device_handle}")
            else:
                print("❌ Failed to open device")
            
        except Exception as e:
            print(f"❌ SDK loading failed: {e}")
    
    def finger_detection_debug_exact(self):
        """Finger detection - EXACT same as debug"""
        if not self.is_initialized:
            return False
        
        try:
            finger_status = c_int(0)
            result = self.scan_api.ftrScanIsFingerPresent(self.device_handle, byref(finger_status))
            
            print(f"Finger detection: result={result}, status={finger_status.value}")
            
            if result == 1:
                return finger_status.value > 0
            else:
                return False
                
        except Exception as e:
            print(f"Finger detection error: {e}")
            return False
    
    def frame_capture_debug_exact(self):
        """Frame capture - EXACT same as successful debug"""
        if not self.is_initialized:
            return None
        
        try:
            print("=== FRAME CAPTURE - EXACT DEBUG APPROACH ===")
            
            # EXACT same as debug: Approach 1: Minimal Buffer (1KB)
            print("--- Approach 1: Minimal Buffer (1KB) ---")
            buffer_size = 1024
            image_buffer = (c_ubyte * buffer_size)()
            frame_size = c_uint(buffer_size)
            
            print(f"Buffer created: {buffer_size} bytes")
            print(f"Initial frame_size: {frame_size.value}")
            print("Calling ftrScanGetFrame...")
            
            # EXACT same call as debug
            result = self.scan_api.ftrScanGetFrame(self.device_handle, image_buffer, byref(frame_size))
            
            print(f"✅ SUCCESS: ftrScanGetFrame returned {result}")
            print(f"Frame size after call: {frame_size.value}")
            
            if frame_size.value > 0:
                print(f"✅ Got {frame_size.value} bytes of data!")
                
                # Analyze first few bytes (same as debug)
                first_bytes = [image_buffer[i] for i in range(min(10, frame_size.value))]
                print(f"First 10 bytes: {first_bytes}")
                
                # Extract image data
                image_data = bytes(image_buffer[:frame_size.value])
                
                # Calculate quality
                non_zero_bytes = sum(1 for b in image_data if b > 0)
                quality_score = int((non_zero_bytes / frame_size.value) * 100) if frame_size.value > 0 else 0
                
                print(f"Quality analysis: {non_zero_bytes}/{frame_size.value} non-zero bytes ({quality_score}%)")
                
                return {
                    'success': True,
                    'image_data': image_data,
                    'frame_size': frame_size.value,
                    'quality_score': quality_score,
                    'result_code': result,
                    'first_bytes': first_bytes
                }
            else:
                print("⚠️ Frame size is 0 - no data captured")
                return {
                    'success': False,
                    'error': 'Frame size is 0',
                    'result_code': result
                }
                
        except Exception as e:
            print(f"❌ Frame capture failed: {e}")
            return None
    
    def capture_fingerprint_debug_exact(self, thumb_type):
        """Complete fingerprint capture using EXACT debug approach"""
        print(f"=== CAPTURING {thumb_type.upper()} THUMB - DEBUG EXACT ===")
        
        try:
            # Step 1: Wait for finger (optional)
            print("Waiting for finger placement...")
            finger_detected = False
            
            for i in range(30):  # 15 seconds
                if self.finger_detection_debug_exact():
                    finger_detected = True
                    print("✅ Finger detected")
                    break
                time.sleep(0.5)
            
            if not finger_detected:
                print("⚠️ No finger detected - proceeding anyway")
            
            # Step 2: Capture frame using EXACT debug approach
            capture_result = self.frame_capture_debug_exact()
            
            if not capture_result or not capture_result.get('success'):
                return {
                    'success': False,
                    'error': 'Frame capture failed',
                    'debug_exact': True
                }
            
            # Step 3: Create template data
            image_data = capture_result['image_data']
            quality_score = capture_result['quality_score']
            frame_size = capture_result['frame_size']
            
            # Create template
            template_dict = {
                'version': '3.0',
                'thumb_type': thumb_type,
                'image_data': base64.b64encode(image_data).decode(),
                'quality_score': quality_score,
                'capture_time': datetime.now().isoformat(),
                'frame_size': frame_size,
                'result_code': capture_result['result_code'],
                'first_bytes': capture_result['first_bytes'],
                'debug_exact': True,
                'device_info': {
                    'model': 'Futronic FS88H',
                    'handle': self.device_handle,
                    'sdk_version': 'Debug Exact'
                }
            }
            
            template_data = base64.b64encode(json.dumps(template_dict).encode()).decode()
            
            result = {
                'success': True,
                'thumb_type': thumb_type,
                'template_data': template_data,
                'quality_score': quality_score,
                'frame_size': frame_size,
                'capture_time': template_dict['capture_time'],
                'debug_exact': True,
                'image_data': base64.b64encode(image_data).decode()
            }
            
            print(f"✅ SUCCESS: {thumb_type} thumb captured - {frame_size} bytes, Quality: {quality_score}%")
            return result
            
        except Exception as e:
            print(f"❌ Capture failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'debug_exact': True
            }

# Global SDK manager
sdk_manager = DebugExactSDKManager()

class DebugExactHandler(BaseHTTPRequestHandler):
    """Simple HTTP handler for debug exact service"""
    
    def do_GET(self):
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            
            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Debug Exact Service</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .status {{ background: #f0f8ff; padding: 15px; margin: 10px 0; border-radius: 5px; }}
                    .btn {{ padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }}
                    .results {{ background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; font-family: monospace; }}
                </style>
            </head>
            <body>
                <h1>🔍 DEBUG EXACT Service</h1>
                <p>Using the EXACT same approach as successful debug script</p>
                
                <div class="status">
                    <h3>Device Status</h3>
                    <p><strong>Connected:</strong> {sdk_manager.is_initialized}</p>
                    <p><strong>Handle:</strong> {sdk_manager.device_handle}</p>
                    <p><strong>Debug Exact:</strong> True</p>
                </div>
                
                <h3>Test Fingerprint Capture</h3>
                <button class="btn" onclick="testCapture('left')">Test Left Thumb</button>
                <button class="btn" onclick="testCapture('right')">Test Right Thumb</button>
                
                <div id="results" class="results" style="display: none;">
                    <h4>Results:</h4>
                    <pre id="resultsContent"></pre>
                </div>
                
                <script>
                async function testCapture(thumbType) {{
                    const button = event.target;
                    const originalText = button.textContent;
                    
                    button.textContent = 'Capturing with DEBUG EXACT...';
                    button.disabled = true;
                    
                    try {{
                        const response = await fetch('/capture', {{
                            method: 'POST',
                            headers: {{ 'Content-Type': 'application/json' }},
                            body: JSON.stringify({{ thumb_type: thumbType }})
                        }});
                        
                        const result = await response.json();
                        
                        document.getElementById('results').style.display = 'block';
                        document.getElementById('resultsContent').textContent = JSON.stringify(result, null, 2);
                        
                        if (result.success) {{
                            alert('✅ SUCCESS: ' + thumbType + ' thumb captured!\\n\\nFrame Size: ' + result.frame_size + ' bytes\\nQuality: ' + result.quality_score + '%\\nDebug Exact: ' + result.debug_exact);
                        }} else {{
                            alert('❌ Capture failed: ' + result.error);
                        }}
                    }} catch (error) {{
                        alert('❌ Network error: ' + error.message);
                    }} finally {{
                        button.textContent = originalText;
                        button.disabled = false;
                    }}
                }}
                </script>
            </body>
            </html>
            """
            
            self.wfile.write(html.encode())
        
        elif self.path == '/status':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            status = {
                'connected': sdk_manager.is_initialized,
                'handle': sdk_manager.device_handle,
                'debug_exact': True
            }
            
            self.wfile.write(json.dumps(status).encode())
    
    def do_POST(self):
        if self.path == '/capture':
            try:
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length)
                data = json.loads(post_data.decode())
                
                thumb_type = data.get('thumb_type', 'left')
                
                print(f"\n=== CAPTURE REQUEST: {thumb_type} ===")
                
                # Use debug exact capture
                result = sdk_manager.capture_fingerprint_debug_exact(thumb_type)
                
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                
                self.wfile.write(json.dumps(result).encode())
                
            except Exception as e:
                print(f"❌ Capture request error: {e}")
                
                self.send_response(500)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                
                error_response = {
                    'success': False,
                    'error': str(e),
                    'debug_exact': True
                }
                self.wfile.write(json.dumps(error_response).encode())
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def log_message(self, format, *args):
        # Suppress default HTTP logging
        pass

if __name__ == '__main__':
    try:
        print("=== DEBUG EXACT SERVICE ===")
        print("Using EXACT same approach as successful debug script")
        print(f"Device initialized: {sdk_manager.is_initialized}")
        print("Starting server on http://localhost:8001")
        print("This uses NO Flask - just basic HTTP server")
        
        server = HTTPServer(('0.0.0.0', 8001), DebugExactHandler)
        server.serve_forever()
        
    except KeyboardInterrupt:
        print("\nDebug exact service stopped")
    except Exception as e:
        print(f"Service error: {e}")
    finally:
        if sdk_manager.device_handle:
            try:
                sdk_manager.scan_api.ftrScanCloseDevice(sdk_manager.device_handle)
                print("Device closed")
            except:
                pass
