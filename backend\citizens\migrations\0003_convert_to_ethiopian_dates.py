# Data migration to convert existing Gregorian dates to Ethiopian dates

from django.db import migrations
from django.apps import apps


def convert_gregorian_to_ethiopian(apps, schema_editor):
    """Convert existing Gregorian dates to Ethiopian dates"""
    from common.ethiopian_calendar import gregorian_to_ethiopian, format_ethiopian_date
    
    Citizen = apps.get_model('citizens', 'Citizen')
    Child = apps.get_model('citizens', 'Child')
    
    # Convert citizen birth dates
    for citizen in Citizen.objects.filter(date_of_birth__isnull=False, date_of_birth_ethiopian__isnull=True):
        try:
            ethiopian_date = gregorian_to_ethiopian(citizen.date_of_birth)
            if ethiopian_date:
                citizen.date_of_birth_ethiopian = format_ethiopian_date(ethiopian_date)
                citizen.save(update_fields=['date_of_birth_ethiopian'])
                print(f"Converted citizen {citizen.id}: {citizen.date_of_birth} -> {citizen.date_of_birth_ethiopian}")
        except Exception as e:
            print(f"Error converting citizen {citizen.id}: {e}")
    
    # Convert child birth dates
    for child in Child.objects.filter(date_of_birth__isnull=False, date_of_birth_ethiopian__isnull=True):
        try:
            ethiopian_date = gregorian_to_ethiopian(child.date_of_birth)
            if ethiopian_date:
                child.date_of_birth_ethiopian = format_ethiopian_date(ethiopian_date)
                child.save(update_fields=['date_of_birth_ethiopian'])
                print(f"Converted child {child.id}: {child.date_of_birth} -> {child.date_of_birth_ethiopian}")
        except Exception as e:
            print(f"Error converting child {child.id}: {e}")


def reverse_conversion(apps, schema_editor):
    """Reverse the conversion by clearing Ethiopian dates"""
    Citizen = apps.get_model('citizens', 'Citizen')
    Child = apps.get_model('citizens', 'Child')
    
    Citizen.objects.update(date_of_birth_ethiopian=None)
    Child.objects.update(date_of_birth_ethiopian=None)


class Migration(migrations.Migration):

    dependencies = [
        ('citizens', '0002_add_ethiopian_date_fields'),
    ]

    operations = [
        migrations.RunPython(convert_gregorian_to_ethiopian, reverse_conversion),
    ]
