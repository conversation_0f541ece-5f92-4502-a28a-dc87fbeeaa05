#!/bin/bash

# Fix Citizen Permissions 403 Erro<PERSON>
# This script fixes the citizen endpoint permission issues

echo "🔧 Fixing Citizen Permissions 403 Errors"
echo "========================================="

# Step 1: Test current permissions
echo "📍 Step 1: Testing current citizen permissions..."
python test_citizen_permissions.py

# Step 2: Restart backend service to apply permission changes
echo ""
echo "📍 Step 2: Restarting backend service to apply permission changes..."
docker-compose restart backend

# Step 3: Wait for service to restart
echo "⏳ Waiting for backend to restart..."
sleep 25

# Step 4: Test again
echo ""
echo "📍 Step 3: Testing after restart..."
python test_citizen_permissions.py

# Step 5: Check if we need to run migrations or init data
echo ""
echo "📍 Step 4: Checking database and user setup..."
docker-compose exec backend python manage.py init_data

# Step 6: Final test
echo ""
echo "📍 Step 5: Final test after database check..."
python test_citizen_permissions.py

echo ""
echo "✅ Citizen permissions fix completed!"
echo ""
echo "🔍 Manual testing:"
echo "1. Login to frontend: http://10.139.8.141:3000/"
echo "   Use: <EMAIL> / password123"
echo ""
echo "2. Navigate to Citizens section"
echo "   Should load without 403 errors"
echo ""
echo "3. Test API directly:"
echo "   # First get auth token:"
echo "   curl -X POST http://10.139.8.141:8000/api/auth/token/ \\"
echo "     -H 'Content-Type: application/json' \\"
echo "     -d '{\"username\": \"<EMAIL>\", \"password\": \"password123\"}'"
echo ""
echo "   # Then test citizens endpoint (replace TOKEN and TENANT_ID):"
echo "   curl -H 'Authorization: Bearer TOKEN' \\"
echo "     http://10.139.8.141:8000/api/tenants/TENANT_ID/citizens/"
echo ""
echo "💡 Expected results:"
echo "- Clerk: Should get 200 (success) for citizens in their tenant"
echo "- Kebele Admin: Should get 200 (success) for citizens in their tenant"
echo "- Superadmin: Should get 200 (success) for all endpoints"
echo ""
echo "💡 If still getting 403 errors:"
echo "- Check Docker logs: docker-compose logs backend"
echo "- Verify user has correct role and tenant assignment"
echo "- Ensure CanManageCitizens permission allows the user's role"
echo "- Check that user's tenant matches the tenant in the URL"
