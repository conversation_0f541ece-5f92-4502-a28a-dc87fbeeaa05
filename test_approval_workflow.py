#!/usr/bin/env python3
"""
Test script for the kebele leader approval workflow functionality.
This script tests the new approval endpoints and workflow.
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:8000"
TENANT_ID = "1"  # Replace with actual tenant ID

def test_approval_workflow():
    """Test the complete approval workflow"""
    
    print("🧪 Testing Kebele Leader Approval Workflow")
    print("=" * 50)
    
    # Test data
    test_data = {
        "submit_for_approval": {
            "action": "submit_for_approval",
            "comment": "Submitted for kebele leader approval"
        },
        "approve": {
            "action": "approve",
            "comment": "ID card approved after document verification",
            "approval_pattern": "diagonal_stripes"
        },
        "reject": {
            "action": "reject",
            "comment": "Missing required documents"
        }
    }
    
    # Test endpoints
    endpoints = [
        f"{BASE_URL}/api/tenants/{TENANT_ID}/idcards/1/approval_action/",
        f"{BASE_URL}/api/idcards/1/approval_action/"
    ]
    
    for endpoint in endpoints:
        print(f"\n📍 Testing endpoint: {endpoint}")
        
        for action_name, data in test_data.items():
            print(f"\n🔄 Testing action: {action_name}")
            print(f"📤 Request data: {json.dumps(data, indent=2)}")
            
            try:
                # Note: In a real test, you would need proper authentication headers
                headers = {
                    'Content-Type': 'application/json',
                    # 'Authorization': 'Bearer YOUR_JWT_TOKEN'
                }
                
                response = requests.post(endpoint, json=data, headers=headers)
                
                print(f"📥 Response status: {response.status_code}")
                
                if response.status_code == 200:
                    print("✅ Success!")
                    response_data = response.json()
                    print(f"📄 Response data: {json.dumps(response_data, indent=2)}")
                else:
                    print(f"❌ Error: {response.status_code}")
                    print(f"📄 Error details: {response.text}")
                    
            except requests.exceptions.ConnectionError:
                print("❌ Connection error - make sure the Django server is running")
            except Exception as e:
                print(f"❌ Unexpected error: {e}")
    
    print("\n" + "=" * 50)
    print("🏁 Test completed!")

if __name__ == "__main__":
    test_approval_workflow()
