#!/usr/bin/env python3
"""
Simple script to test backend connectivity and diagnose issues.
"""

import requests
import time
import socket

def test_backend_connectivity():
    """Test backend connectivity step by step."""
    
    base_url = "http://************:8000"
    
    print("🧪 Testing Backend Connectivity")
    print("=" * 40)
    
    # Step 1: Test basic network connectivity
    print("📍 Step 1: Testing network connectivity...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(('************', 8000))
        sock.close()
        
        if result == 0:
            print("  ✅ Port 8000 is open and reachable")
        else:
            print("  ❌ Port 8000 is not reachable")
            print("  💡 Check if backend container is running")
            return
    except Exception as e:
        print(f"  ❌ Network error: {e}")
        return
    
    # Step 2: Test basic HTTP response
    print("\n📍 Step 2: Testing basic HTTP response...")
    try:
        response = requests.get(f"{base_url}/", timeout=10)
        print(f"  Status: {response.status_code}")
        print(f"  Headers: {dict(response.headers)}")
        if response.status_code == 200:
            print("  ✅ Backend is responding")
        else:
            print(f"  ⚠️  Backend responding but with status {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("  ❌ Connection refused - backend not responding")
        return
    except requests.exceptions.Timeout:
        print("  ❌ Request timeout - backend too slow")
        return
    except Exception as e:
        print(f"  ❌ HTTP error: {e}")
        return
    
    # Step 3: Test admin endpoint
    print("\n📍 Step 3: Testing admin endpoint...")
    try:
        response = requests.get(f"{base_url}/admin/", timeout=10)
        print(f"  Admin status: {response.status_code}")
        if response.status_code in [200, 302]:
            print("  ✅ Admin endpoint working")
        else:
            print(f"  ❌ Admin endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"  ❌ Admin error: {e}")
    
    # Step 4: Test auth endpoint
    print("\n📍 Step 4: Testing auth endpoint...")
    try:
        response = requests.post(
            f"{base_url}/api/auth/token/",
            json={"email": "<EMAIL>", "password": "admin123"},
            timeout=10
        )
        print(f"  Auth status: {response.status_code}")
        print(f"  Auth response: {response.text[:200]}...")
        
        if response.status_code == 200:
            print("  ✅ Authentication working")
            return response.json().get('access')
        elif response.status_code == 400:
            print("  ❌ Bad request - check credentials format")
        elif response.status_code == 401:
            print("  ❌ Unauthorized - invalid credentials")
        elif response.status_code == 500:
            print("  ❌ Internal server error - backend issue")
        else:
            print(f"  ❌ Unexpected status: {response.status_code}")
            
    except Exception as e:
        print(f"  ❌ Auth error: {e}")
        return None
    
    return None

def test_with_token(token):
    """Test endpoints that require authentication."""
    if not token:
        print("\n❌ No token available for authenticated tests")
        return
    
    base_url = "http://************:8000"
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n📍 Step 5: Testing authenticated endpoints...")
    
    endpoints = [
        "/api/tenants/",
        "/api/tenants/subcities/",
        "/api/tenants/kebeles/"
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", headers=headers, timeout=10)
            print(f"  {endpoint}: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                count = len(data) if isinstance(data, list) else len(data.get('results', []))
                print(f"    ✅ Success - {count} items")
            else:
                print(f"    ❌ Failed - {response.text[:100]}...")
                
        except Exception as e:
            print(f"    ❌ Error: {e}")

if __name__ == "__main__":
    token = test_backend_connectivity()
    test_with_token(token)
    
    print("\n" + "=" * 40)
    print("🔧 Troubleshooting Guide:")
    print("1. If port not reachable:")
    print("   - Check: docker-compose ps")
    print("   - Run: docker-compose up -d backend")
    print("")
    print("2. If connection refused:")
    print("   - Check: docker-compose logs backend")
    print("   - Run: docker-compose restart backend")
    print("")
    print("3. If 500 errors:")
    print("   - Check: docker-compose logs backend | grep ERROR")
    print("   - Run: docker-compose exec backend python manage.py check")
    print("   - Run: docker-compose exec backend python manage.py migrate")
    print("")
    print("4. If auth fails:")
    print("   - Run: docker-compose exec backend python manage.py createsuperuser")
    print("   - Email: <EMAIL>, Password: admin123")
    print("")
    print("5. If still failing:")
    print("   - Run: docker-compose down && docker-compose up -d")
    print("   - Check: netstat -tlnp | grep 8000")
    print("   - Verify: Docker network configuration")
