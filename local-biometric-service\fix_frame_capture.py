#!/usr/bin/env python3
"""
Fix Frame Capture Function
Analyze and fix the ftrScanGetFrame function call
"""

import ctypes
from ctypes import cdll, c_int, c_uint, c_void_p, c_ubyte, POINTER, byref
import sys
import os
import time

def test_frame_capture_signatures():
    """Test different approaches for ftrScanGetFrame"""
    
    try:
        # Load SDK
        dll_path = os.path.abspath('./ftrScanAPI.dll')
        scan_api = cdll.LoadLibrary(dll_path)
        
        # Initialize device
        print("Initializing device...")
        device_handle = scan_api.ftrScanOpenDevice()
        
        if not device_handle:
            print("ERROR: Failed to open device")
            return
        
        print(f"Device handle: {device_handle}")
        
        # Test different buffer sizes and approaches
        test_cases = [
            {
                'name': 'Small buffer (1KB)',
                'buffer_size': 1024
            },
            {
                'name': 'Medium buffer (64KB)',
                'buffer_size': 65536
            },
            {
                'name': 'Standard buffer (320x480)',
                'buffer_size': 320 * 480
            },
            {
                'name': 'Large buffer (1MB)',
                'buffer_size': 1024 * 1024
            }
        ]
        
        # Configure function signature
        scan_api.ftrScanGetFrame.restype = c_int
        scan_api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(c_ubyte), POINTER(c_uint)]
        
        for test_case in test_cases:
            print(f"\n=== Testing {test_case['name']} ===")
            
            try:
                buffer_size = test_case['buffer_size']
                print(f"Buffer size: {buffer_size} bytes")
                
                # Create buffer
                image_buffer = (c_ubyte * buffer_size)()
                frame_size = c_uint(buffer_size)
                
                print(f"Initial frame_size value: {frame_size.value}")
                
                # Test frame capture
                print("Calling ftrScanGetFrame...")
                start_time = time.time()
                
                result = scan_api.ftrScanGetFrame(device_handle, image_buffer, byref(frame_size))
                
                elapsed_time = time.time() - start_time
                actual_size = frame_size.value
                
                print(f"✅ Call completed in {elapsed_time:.3f} seconds")
                print(f"Result code: {result}")
                print(f"Returned frame size: {actual_size}")
                
                # Analyze result
                if result == 0:  # FTR_OK
                    print("✅ SUCCESS: Frame captured successfully")
                    if actual_size > 0:
                        # Analyze captured data
                        image_data = bytes(image_buffer[:actual_size])
                        non_zero_bytes = sum(1 for b in image_data if b > 0)
                        avg_value = sum(image_data) / len(image_data)
                        
                        print(f"  Data size: {actual_size} bytes")
                        print(f"  Non-zero bytes: {non_zero_bytes}")
                        print(f"  Average value: {avg_value:.2f}")
                        print(f"  Data quality: {(non_zero_bytes/actual_size)*100:.1f}% meaningful")
                    else:
                        print("  WARNING: Frame size is 0")
                        
                elif result == 1:  # FTR_ERROR_EMPTY_FRAME
                    print("ℹ️ Empty frame (no finger detected)")
                    
                elif result == 3:  # FTR_ERROR_NO_FRAME
                    print("ℹ️ No frame available")
                    
                else:
                    print(f"⚠️ Other result code: {result}")
                
                # Test multiple calls for stability
                print("Testing stability with multiple calls...")
                for i in range(3):
                    frame_size.value = buffer_size  # Reset size
                    result = scan_api.ftrScanGetFrame(device_handle, image_buffer, byref(frame_size))
                    print(f"  Call {i+1}: Result={result}, Size={frame_size.value}")
                    time.sleep(0.1)
                
                print(f"✅ {test_case['name']} is STABLE")
                
            except Exception as e:
                print(f"❌ {test_case['name']} FAILED: {e}")
                continue
        
        # Test with different function signatures
        print("\n=== Testing Alternative Function Signatures ===")
        
        alt_signatures = [
            {
                'name': 'Alternative 1: Different return type',
                'restype': c_uint,
                'argtypes': [c_void_p, POINTER(c_ubyte), POINTER(c_uint)]
            },
            {
                'name': 'Alternative 2: Different size parameter type',
                'restype': c_int,
                'argtypes': [c_void_p, POINTER(c_ubyte), POINTER(c_int)]
            }
        ]
        
        for alt_sig in alt_signatures:
            print(f"\n--- {alt_sig['name']} ---")
            
            try:
                # Configure alternative signature
                scan_api.ftrScanGetFrame.restype = alt_sig['restype']
                scan_api.ftrScanGetFrame.argtypes = alt_sig['argtypes']
                
                # Test with standard buffer
                buffer_size = 320 * 480
                image_buffer = (c_ubyte * buffer_size)()
                
                if alt_sig['argtypes'][2] == POINTER(c_int):
                    frame_size = c_int(buffer_size)
                else:
                    frame_size = c_uint(buffer_size)
                
                print("Testing alternative signature...")
                result = scan_api.ftrScanGetFrame(device_handle, image_buffer, byref(frame_size))
                
                print(f"✅ Alternative signature works: Result={result}, Size={frame_size.value}")
                
            except Exception as e:
                print(f"❌ Alternative signature failed: {e}")
        
        # Close device
        print("\nClosing device...")
        scan_api.ftrScanCloseDevice(device_handle)
        print("Device closed successfully")
        
    except Exception as e:
        print(f"ERROR: Frame capture test failed: {e}")

def test_capture_with_timing():
    """Test frame capture with different timing approaches"""
    
    try:
        # Load SDK
        dll_path = os.path.abspath('./ftrScanAPI.dll')
        scan_api = cdll.LoadLibrary(dll_path)
        
        # Initialize device
        print("\n=== TESTING CAPTURE TIMING ===")
        print("Initializing device...")
        device_handle = scan_api.ftrScanOpenDevice()
        
        if not device_handle:
            print("ERROR: Failed to open device")
            return
        
        # Configure function
        scan_api.ftrScanGetFrame.restype = c_int
        scan_api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(c_ubyte), POINTER(c_uint)]
        
        # Test rapid successive calls
        print("\n--- Testing Rapid Successive Calls ---")
        buffer_size = 320 * 480
        
        for i in range(5):
            try:
                image_buffer = (c_ubyte * buffer_size)()
                frame_size = c_uint(buffer_size)
                
                start_time = time.time()
                result = scan_api.ftrScanGetFrame(device_handle, image_buffer, byref(frame_size))
                elapsed_time = time.time() - start_time
                
                print(f"Call {i+1}: Result={result}, Size={frame_size.value}, Time={elapsed_time:.3f}s")
                
                # Small delay between calls
                time.sleep(0.1)
                
            except Exception as e:
                print(f"Call {i+1} failed: {e}")
                break
        
        # Test with delays
        print("\n--- Testing With Delays ---")
        delays = [0.5, 1.0, 2.0]
        
        for delay in delays:
            try:
                print(f"Testing with {delay}s delay...")
                time.sleep(delay)
                
                image_buffer = (c_ubyte * buffer_size)()
                frame_size = c_uint(buffer_size)
                
                result = scan_api.ftrScanGetFrame(device_handle, image_buffer, byref(frame_size))
                print(f"  Result={result}, Size={frame_size.value}")
                
            except Exception as e:
                print(f"  Failed with {delay}s delay: {e}")
        
        # Close device
        print("\nClosing device...")
        scan_api.ftrScanCloseDevice(device_handle)
        print("Device closed successfully")
        
    except Exception as e:
        print(f"ERROR: Timing test failed: {e}")

if __name__ == '__main__':
    print("=== FRAME CAPTURE FUNCTION FIX TOOL ===")
    print("Analyzing ftrScanGetFrame function call issues")
    print()
    
    # Test different approaches for frame capture
    test_frame_capture_signatures()
    
    # Test timing-related issues
    test_capture_with_timing()
    
    print("\n=== ANALYSIS COMPLETE ===")
    print("Check the results above to identify the working approach")
    print("for frame capture functionality.")
