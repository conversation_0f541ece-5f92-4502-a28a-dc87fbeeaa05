import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar,
  IconButton,
  Tooltip,
  Alert,
} from '@mui/material';
import {
  Visibility as ViewIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  ArrowBack as BackIcon,
} from '@mui/icons-material';
import axios from '../../utils/axios';
import { useAuth } from '../../contexts/AuthContext';

const SubcityPendingApprovals = () => {
  const [pendingCards, setPendingCards] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const { user } = useAuth();
  const navigate = useNavigate();

  const getTenantId = () => {
    console.log('🔍 Getting tenant ID...');

    // Get tenant ID from multiple sources for reliability
    let tenantId = user?.tenant_id;
    console.log('🔍 From user context:', tenantId);

    if (!tenantId) {
      // Fallback: Get from localStorage user object
      try {
        const storedUser = JSON.parse(localStorage.getItem('user') || '{}');
        tenantId = storedUser.tenant_id;
        console.log('🔍 From localStorage user:', tenantId);
      } catch (e) {
        console.warn('Could not parse stored user data');
      }
    }

    if (!tenantId) {
      // Fallback: Get from JWT token
      try {
        const accessToken = localStorage.getItem('accessToken');
        if (accessToken) {
          const base64Url = accessToken.split('.')[1];
          const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
          const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
          }).join(''));
          const tokenData = JSON.parse(jsonPayload);
          tenantId = tokenData.tenant_id;
          console.log('🔍 From JWT token:', tenantId);
        }
      } catch (e) {
        console.warn('Could not decode JWT token');
      }
    }

    if (!tenantId) {
      console.error('❌ No tenant ID found in any source');
      return null;
    }

    console.log('✅ Final tenant ID:', tenantId);
    return tenantId;
  };

  useEffect(() => {
    fetchPendingApprovals();
  }, []);

  const fetchPendingApprovals = async () => {
    try {
      setLoading(true);
      const tenantId = getTenantId();

      if (!tenantId) {
        setError('No tenant selected');
        return;
      }

      console.log('🔍 Fetching pending subcity approvals for tenant:', tenantId);

      const response = await axios.get(`/api/tenants/${tenantId}/idcards/pending_subcity_approval/`);

      console.log('📋 Pending approvals response:', response.data);
      setPendingCards(response.data.results || []);
    } catch (error) {
      console.error('Error fetching pending approvals:', error);
      setError(error.response?.data?.error || 'Failed to fetch pending approvals');
    } finally {
      setLoading(false);
    }
  };

  const handleViewIDCard = (cardId, kebeleId) => {
    // Navigate to the ID card view in the kebele tenant context
    navigate(`/tenants/${kebeleId}/idcards/${cardId}`);
  };

  const handleApprove = async (cardId, kebeleId) => {
    try {
      console.log('🔍 Subcity approving ID card:', { cardId, kebeleId });

      await axios.post(`/api/tenants/${kebeleId}/idcards/${cardId}/approval_action/`, {
        action: 'subcity_approve',
        comment: 'Approved by subcity admin'
      });

      // Refresh the list
      fetchPendingApprovals();
    } catch (error) {
      console.error('Error approving ID card:', error);
      setError(error.response?.data?.error || 'Failed to approve ID card');
    }
  };

  const handleReject = async (cardId, kebeleId) => {
    try {
      console.log('🔍 Subcity rejecting ID card:', { cardId, kebeleId });

      await axios.post(`/api/tenants/${kebeleId}/idcards/${cardId}/approval_action/`, {
        action: 'reject',
        comment: 'Rejected by subcity admin'
      });

      // Refresh the list
      fetchPendingApprovals();
    } catch (error) {
      console.error('Error rejecting ID card:', error);
      setError(error.response?.data?.error || 'Failed to reject ID card');
    }
  };

  const getCitizenName = (card) => {
    return card.citizen_name ||
           [card.citizen_first_name, card.citizen_middle_name, card.citizen_last_name]
             .filter(Boolean)
             .join(' ') ||
           'Unknown Citizen';
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Button
            variant="outlined"
            startIcon={<BackIcon />}
            onClick={() => navigate('/idcards')}
            sx={{ mr: 2 }}
          >
            Back
          </Button>
          <Typography variant="h4" component="h1">
            Pending Subcity Approvals
          </Typography>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            ID Cards Pending Subcity Approval ({pendingCards.length})
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            These ID cards have been approved by kebele leaders and are waiting for your final approval for printing.
          </Typography>

          {pendingCards.length === 0 ? (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Typography variant="h6" color="text.secondary">
                No ID cards pending subcity approval
              </Typography>
              <Typography variant="body2" color="text.secondary">
                All ID cards from child kebeles have been processed.
              </Typography>
            </Box>
          ) : (
            <TableContainer component={Paper} variant="outlined">
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Citizen</TableCell>
                    <TableCell>Kebele</TableCell>
                    <TableCell>Kebele Approved Date</TableCell>
                    <TableCell>Kebele Approved By</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {pendingCards.map((card) => (
                    <TableRow key={`${card.kebele_tenant.id}-${card.id}`} hover>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Avatar
                            sx={{
                              bgcolor: 'primary.main',
                              width: 40,
                              height: 40,
                              fontSize: '1rem'
                            }}
                          >
                            {getCitizenName(card)?.charAt(0)?.toUpperCase() || 'U'}
                          </Avatar>
                          <Box>
                            <Typography variant="subtitle2" fontWeight="bold">
                              {getCitizenName(card)}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              ID: {card.citizen_digital_id || 'N/A'}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontWeight="medium">
                          {card.kebele_tenant.name}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {formatDate(card.kebele_approved_at)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {card.kebele_approved_by_username || 'Unknown'}
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
                          <Tooltip title="View Details">
                            <IconButton
                              onClick={() => handleViewIDCard(card.id, card.kebele_tenant.id)}
                              size="small"
                              sx={{
                                bgcolor: 'primary.main',
                                color: 'white',
                                '&:hover': { bgcolor: 'primary.dark' }
                              }}
                            >
                              <ViewIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Subcity Approve">
                            <IconButton
                              onClick={() => handleApprove(card.id, card.kebele_tenant.id)}
                              size="small"
                              sx={{
                                bgcolor: 'success.main',
                                color: 'white',
                                '&:hover': { bgcolor: 'success.dark' }
                              }}
                            >
                              <ApproveIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Reject">
                            <IconButton
                              onClick={() => handleReject(card.id, card.kebele_tenant.id)}
                              size="small"
                              sx={{
                                bgcolor: 'error.main',
                                color: 'white',
                                '&:hover': { bgcolor: 'error.dark' }
                              }}
                            >
                              <RejectIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default SubcityPendingApprovals;
