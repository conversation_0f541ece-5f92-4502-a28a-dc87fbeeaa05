"""
Ethiopian Calendar Utilities for Backend

The Ethiopian calendar has 13 months:
- 12 months of 30 days each
- 1 month (Pagume) of 5 days (6 in leap years)
- Ethiopian year starts on September 11 (or 12 in leap years) of Gregorian calendar
- Ethiopian calendar is approximately 7-8 years behind Gregorian calendar
"""

from datetime import date, datetime, timedelta
import math

# Ethiopian month names
ETHIOPIAN_MONTHS = [
    'መስከረም', 'ጥቅምት', 'ኅዳር', 'ታኅሳስ', 'ጥር', 'የካቲት',
    'መጋቢት', 'ሚያዝያ', 'ግንቦት', 'ሰኔ', 'ሐምሌ', 'ነሐሴ', 'ጳጉሜ'
]

ETHIOPIAN_MONTHS_EN = [
    'Meskerem', 'Tikimt', 'Hidar', '<PERSON>hs<PERSON>', 'Tir', 'Yekatit',
    'Megabit', '<PERSON><PERSON>', 'G<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Pagum<PERSON>'
]


def is_ethiopian_leap_year(ethiopian_year):
    """Check if an Ethiopian year is a leap year"""
    return (ethiopian_year % 4) == 3


def get_days_in_ethiopian_month(month, year):
    """Get the number of days in an Ethiopian month"""
    if 1 <= month <= 12:
        return 30
    elif month == 13:
        return 6 if is_ethiopian_leap_year(year) else 5
    else:
        raise ValueError('Invalid Ethiopian month')


def gregorian_to_ethiopian(gregorian_date):
    """Convert Gregorian date to Ethiopian date using accurate algorithm"""
    if not gregorian_date:
        return None
    
    if isinstance(gregorian_date, datetime):
        gregorian_date = gregorian_date.date()
    
    year = gregorian_date.year
    month = gregorian_date.month
    day = gregorian_date.day

    # Convert to Julian Day Number first for accuracy
    adjusted_year = year
    adjusted_month = month

    if month <= 2:
        adjusted_year -= 1
        adjusted_month += 12

    a = math.floor(adjusted_year / 100)
    b = 2 - a + math.floor(a / 4)

    jdn = (math.floor(365.25 * (adjusted_year + 4716)) +
           math.floor(30.6001 * (adjusted_month + 1)) +
           day + b - 1524)

    # Convert Julian Day Number to Ethiopian date
    # Ethiopian epoch JDN is 1724221 (September 11, 8 AD Gregorian)
    ethiopian_jdn = jdn - 1724220

    if ethiopian_jdn < 1:
        return None

    # Calculate Ethiopian year
    ethiopian_year = math.floor((ethiopian_jdn - 1) / 365) + 1

    # Adjust for leap years
    leap_adjustments = math.floor((ethiopian_year - 1) / 4)
    ethiopian_year = math.floor((ethiopian_jdn - 1 - leap_adjustments) / 365) + 1

    # Calculate day of Ethiopian year
    leap_adjustments = math.floor((ethiopian_year - 1) / 4)
    day_of_year = ethiopian_jdn - ((ethiopian_year - 1) * 365 + leap_adjustments)

    # Calculate Ethiopian month and day
    if day_of_year <= 360:  # First 12 months (30 days each)
        ethiopian_month = math.floor((day_of_year - 1) / 30) + 1
        ethiopian_day = ((day_of_year - 1) % 30) + 1
    else:  # Pagume (13th month)
        ethiopian_month = 13
        ethiopian_day = day_of_year - 360

    return {
        'year': int(ethiopian_year),
        'month': int(ethiopian_month),
        'day': int(ethiopian_day)
    }


def ethiopian_to_gregorian(year, month, day):
    """Convert Ethiopian date to Gregorian date using accurate algorithm"""
    if not year or not month or not day:
        return None

    # Validate Ethiopian date
    if month < 1 or month > 13 or day < 1:
        raise ValueError('Invalid Ethiopian date')

    if month <= 12 and day > 30:
        raise ValueError('Invalid day for Ethiopian month')

    if month == 13 and day > get_days_in_ethiopian_month(13, year):
        raise ValueError('Invalid day for Pagume')

    # Calculate day of Ethiopian year
    if month <= 12:
        day_of_year = (month - 1) * 30 + day
    else:
        day_of_year = 360 + day

    # Calculate leap year adjustments
    leap_adjustments = math.floor((year - 1) / 4)

    # Calculate Ethiopian Julian Day Number
    ethiopian_jdn = (year - 1) * 365 + leap_adjustments + day_of_year + 1724220

    # Convert Julian Day Number to Gregorian date
    a = ethiopian_jdn + 32044
    b = math.floor((4 * a + 3) / 146097)
    c = a - math.floor((146097 * b) / 4)
    d = math.floor((4 * c + 3) / 1461)
    e = c - math.floor((1461 * d) / 4)
    m = math.floor((5 * e + 2) / 153)

    gregorian_day = e - math.floor((153 * m + 2) / 5) + 1
    gregorian_month = m + 3 - 12 * math.floor(m / 10)
    gregorian_year = 100 * b + d - 4800 + math.floor(m / 10)

    try:
        return date(int(gregorian_year), int(gregorian_month), int(gregorian_day))
    except ValueError:
        return None


def format_ethiopian_date(ethiopian_date, format_str='DD/MM/YYYY', language='am'):
    """Format Ethiopian date as string"""
    if not ethiopian_date or not all(k in ethiopian_date for k in ['year', 'month', 'day']):
        return ''

    year = ethiopian_date['year']
    month = ethiopian_date['month']
    day = ethiopian_date['day']
    
    month_names = ETHIOPIAN_MONTHS if language == 'am' else ETHIOPIAN_MONTHS_EN
    
    padded_day = str(day).zfill(2)
    padded_month = str(month).zfill(2)
    
    if format_str == 'DD/MM/YYYY':
        return f"{padded_day}/{padded_month}/{year}"
    elif format_str == 'DD-MM-YYYY':
        return f"{padded_day}-{padded_month}-{year}"
    elif format_str == 'DD MMM YYYY':
        return f"{padded_day} {month_names[month - 1]} {year}"
    elif format_str == 'MMM DD, YYYY':
        return f"{month_names[month - 1]} {padded_day}, {year}"
    elif format_str == 'YYYY-MM-DD':
        return f"{year}-{padded_month}-{padded_day}"
    else:
        return f"{padded_day}/{padded_month}/{year}"


def get_current_ethiopian_date():
    """Get current Ethiopian date"""
    return gregorian_to_ethiopian(date.today())


def add_years_to_ethiopian_date(ethiopian_date, years):
    """Add years to Ethiopian date"""
    if not ethiopian_date or years == 0:
        return ethiopian_date

    year = ethiopian_date['year'] + years
    month = ethiopian_date['month']
    day = ethiopian_date['day']

    # Adjust day if it's Pagume 6th and new year is not leap
    if month == 13 and day == 6 and not is_ethiopian_leap_year(year):
        day = 5

    return {
        'year': year,
        'month': month,
        'day': day
    }


def add_months_to_ethiopian_date(ethiopian_date, months):
    """Add months to Ethiopian date"""
    if not ethiopian_date or months == 0:
        return ethiopian_date

    year = ethiopian_date['year']
    month = ethiopian_date['month'] + months
    day = ethiopian_date['day']

    # Handle year overflow
    while month > 13:
        month -= 13
        year += 1

    while month < 1:
        month += 13
        year -= 1

    # Adjust day if it exceeds the month's days
    max_days = get_days_in_ethiopian_month(month, year)
    if day > max_days:
        day = max_days

    return {
        'year': year,
        'month': month,
        'day': day
    }


def is_valid_ethiopian_date(ethiopian_date):
    """Check if Ethiopian date is valid"""
    if not ethiopian_date or not all(k in ethiopian_date for k in ['year', 'month', 'day']):
        return False

    year = ethiopian_date['year']
    month = ethiopian_date['month']
    day = ethiopian_date['day']

    if year < 1 or month < 1 or month > 13 or day < 1:
        return False

    max_days = get_days_in_ethiopian_month(month, year)
    return day <= max_days
