#!/usr/bin/env python3
"""
Debug Duplicate Detection Issues

This script diagnoses why duplicate detection is not working
and provides specific fixes for the identified problems.
"""

import os
import sys
import json
import base64
import requests
from datetime import datetime

def test_api_endpoint():
    """Test if the duplicate detection API endpoint is working"""
    print("🔍 Testing Duplicate Detection API Endpoint")
    print("-" * 50)
    
    # Test data
    test_template = base64.b64encode(json.dumps({
        'version': '2.0',
        'ansi_iso_template': base64.b64encode(b'TEST_ANSI_DATA_123').decode(),
        'device_info': {'model': 'Futronic FS88H'},
        'thumb_type': 'left',
        'capture_time': datetime.now().isoformat()
    }).encode()).decode()
    
    test_data = {
        'left_thumb_fingerprint': test_template,
        'right_thumb_fingerprint': test_template  # Same template = should detect duplicate
    }
    
    try:
        # Test the enhanced endpoint
        response = requests.post(
            'http://localhost:8000/api/biometrics/check-duplicates/',
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"   Status Code: {response.status_code}")
        print(f"   Response: {response.text[:200]}...")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                duplicate_data = data.get('data', {})
                print(f"   ✅ API Working - Has Duplicates: {duplicate_data.get('has_duplicates', False)}")
                print(f"   Templates Checked: {duplicate_data.get('templates_checked', 0)}")
                return True
            else:
                print(f"   ❌ API Error: {data.get('error', 'Unknown error')}")
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("   ❌ Connection Error: Django backend not running on localhost:8000")
    except Exception as e:
        print(f"   ❌ Request Error: {e}")
    
    return False


def test_database_templates():
    """Test if there are templates in the database"""
    print("\n🗄️ Testing Database Templates")
    print("-" * 50)
    
    try:
        # Test the debug endpoint
        response = requests.get(
            'http://localhost:8000/api/biometrics/debug-fingerprint-data/',
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                debug_data = data.get('data', {})
                tenants_checked = debug_data.get('tenants_checked', [])
                
                total_templates = 0
                for tenant in tenants_checked:
                    tenant_count = tenant.get('biometric_count', 0)
                    total_templates += tenant_count
                    print(f"   Tenant {tenant.get('name', 'Unknown')}: {tenant_count} biometric records")
                
                print(f"   ✅ Total Templates in Database: {total_templates}")
                
                if total_templates == 0:
                    print("   ⚠️ NO TEMPLATES FOUND - This is why duplicate detection isn't working!")
                    print("   💡 Solution: Register some citizens with fingerprints first")
                
                return total_templates > 0
            else:
                print(f"   ❌ Debug API Error: {data.get('error', 'Unknown error')}")
        else:
            print(f"   ❌ Debug API HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Database Test Error: {e}")
    
    return False


def test_fmatcher_availability():
    """Test if FMatcher.jar is available and working"""
    print("\n🔧 Testing FMatcher Availability")
    print("-" * 50)
    
    fmatcher_path = os.path.join('backend', 'fpmatcher', 'FMatcher.jar')
    
    if os.path.exists(fmatcher_path):
        print(f"   ✅ FMatcher.jar found at: {fmatcher_path}")
        
        # Test FMatcher execution
        try:
            import subprocess
            result = subprocess.run(
                ['java', '-jar', 'FMatcher.jar', 'template1.iso', 'template2.iso'],
                cwd=os.path.join('backend', 'fpmatcher'),
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                score = float(result.stdout.strip())
                print(f"   ✅ FMatcher Working - Test Score: {score:.2f}")
                return True
            else:
                print(f"   ❌ FMatcher Error: {result.stderr}")
        except Exception as e:
            print(f"   ❌ FMatcher Test Failed: {e}")
    else:
        print(f"   ❌ FMatcher.jar NOT FOUND at: {fmatcher_path}")
        print("   💡 Solution: Ensure FMatcher.jar is in backend/fpmatcher/")
    
    return False


def test_cache_system():
    """Test if Redis cache is working"""
    print("\n💾 Testing Cache System")
    print("-" * 50)
    
    try:
        # Test cache stats endpoint
        response = requests.get(
            'http://localhost:8000/api/biometrics/duplicate-detection-stats/',
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                stats = data.get('data', {})
                cache_info = stats.get('cache_info', {})
                service_stats = stats.get('service_stats', {})
                
                print(f"   Cache Timeout: {cache_info.get('timeout', 'Unknown')}s")
                print(f"   Cache Hits: {service_stats.get('cache_hits', 0)}")
                print(f"   Cache Misses: {service_stats.get('cache_misses', 0)}")
                print(f"   ✅ Cache System Accessible")
                return True
            else:
                print(f"   ❌ Stats API Error: {data.get('error', 'Unknown error')}")
        else:
            print(f"   ❌ Stats API HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Cache Test Error: {e}")
    
    return False


def test_template_format():
    """Test if stored templates have the correct format"""
    print("\n📋 Testing Template Format")
    print("-" * 50)
    
    try:
        # Test template extraction
        response = requests.get(
            'http://localhost:8000/api/biometrics/debug-fingerprint-data/',
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                debug_data = data.get('data', {})
                tenants_checked = debug_data.get('tenants_checked', [])
                
                template_issues = []
                valid_templates = 0
                
                for tenant in tenants_checked:
                    citizens = tenant.get('citizens', [])
                    for citizen in citizens:
                        # Check left thumb template
                        if citizen.get('left_thumb_preview'):
                            try:
                                # Try to decode the template
                                template_data = citizen['left_thumb_preview']
                                decoded = json.loads(base64.b64decode(template_data + '==').decode())
                                
                                if 'ansi_iso_template' in decoded:
                                    valid_templates += 1
                                    print(f"   ✅ Valid template: {citizen.get('citizen_name', 'Unknown')}")
                                else:
                                    template_issues.append(f"Missing ansi_iso_template: {citizen.get('citizen_name', 'Unknown')}")
                                    
                            except Exception as e:
                                template_issues.append(f"Invalid template format: {citizen.get('citizen_name', 'Unknown')} - {e}")
                
                print(f"   Valid Templates: {valid_templates}")
                print(f"   Template Issues: {len(template_issues)}")
                
                if template_issues:
                    print("   ⚠️ TEMPLATE FORMAT ISSUES FOUND:")
                    for issue in template_issues[:5]:  # Show first 5
                        print(f"     - {issue}")
                    
                    if len(template_issues) > 5:
                        print(f"     ... and {len(template_issues) - 5} more issues")
                
                return len(template_issues) == 0
                
    except Exception as e:
        print(f"   ❌ Template Format Test Error: {e}")
    
    return False


def provide_solutions():
    """Provide specific solutions based on the issues found"""
    print("\n🔧 SOLUTIONS TO FIX DUPLICATE DETECTION")
    print("=" * 60)
    
    print("\n1. **If NO TEMPLATES in Database:**")
    print("   - Register some citizens with fingerprints first")
    print("   - Use the frontend to capture and save fingerprints")
    print("   - Check: http://localhost:3000/citizens/register")
    
    print("\n2. **If FMatcher NOT WORKING:**")
    print("   - Ensure Java is installed: java -version")
    print("   - Check FMatcher.jar exists in backend/fpmatcher/")
    print("   - Test manually: cd backend/fpmatcher && java -jar FMatcher.jar template1.iso template2.iso")
    
    print("\n3. **If TEMPLATE FORMAT Issues:**")
    print("   - Templates missing 'ansi_iso_template' field")
    print("   - Re-capture fingerprints with updated biometric service")
    print("   - Check local biometric service is running on port 5000")
    
    print("\n4. **If API NOT RESPONDING:**")
    print("   - Restart Django backend: python manage.py runserver")
    print("   - Check Django logs for errors")
    print("   - Verify middleware is enabled in settings.py")
    
    print("\n5. **If CACHE Issues:**")
    print("   - Clear cache: curl -X POST http://localhost:8000/api/biometrics/clear-duplicate-cache/")
    print("   - Check Redis is running (if using Redis cache)")
    print("   - Restart Django to reset in-memory cache")
    
    print("\n6. **Quick Test Commands:**")
    print("   # Test duplicate detection with identical templates")
    print("   curl -X POST http://localhost:8000/api/biometrics/check-duplicates/ \\")
    print("     -H 'Content-Type: application/json' \\")
    print("     -d '{\"left_thumb_fingerprint\": \"test123\", \"right_thumb_fingerprint\": \"test123\"}'")
    
    print("\n   # Check database templates")
    print("   curl http://localhost:8000/api/biometrics/debug-fingerprint-data/")
    
    print("\n   # Get service statistics")
    print("   curl http://localhost:8000/api/biometrics/duplicate-detection-stats/")


def main():
    """Main diagnostic function"""
    print("🚨 DUPLICATE DETECTION DIAGNOSTIC TOOL")
    print("=" * 60)
    print(f"Diagnostic started at: {datetime.now().isoformat()}")
    print()
    
    # Run all tests
    api_working = test_api_endpoint()
    templates_exist = test_database_templates()
    fmatcher_working = test_fmatcher_availability()
    cache_working = test_cache_system()
    templates_valid = test_template_format()
    
    # Summary
    print("\n📊 DIAGNOSTIC SUMMARY")
    print("=" * 60)
    print(f"✅ API Endpoint Working: {api_working}")
    print(f"✅ Templates in Database: {templates_exist}")
    print(f"✅ FMatcher Available: {fmatcher_working}")
    print(f"✅ Cache System Working: {cache_working}")
    print(f"✅ Template Format Valid: {templates_valid}")
    
    # Determine main issue
    if not templates_exist:
        print("\n🎯 MAIN ISSUE: NO FINGERPRINT TEMPLATES IN DATABASE")
        print("   This is why duplicate detection isn't working!")
        print("   Solution: Register citizens with fingerprints first")
    elif not fmatcher_working:
        print("\n🎯 MAIN ISSUE: FMATCHER NOT WORKING")
        print("   Templates exist but can't be compared")
        print("   Solution: Fix FMatcher.jar setup")
    elif not templates_valid:
        print("\n🎯 MAIN ISSUE: INVALID TEMPLATE FORMAT")
        print("   Templates exist but missing required fields")
        print("   Solution: Re-capture fingerprints with updated service")
    elif not api_working:
        print("\n🎯 MAIN ISSUE: API NOT RESPONDING")
        print("   Backend service issues")
        print("   Solution: Check Django backend and logs")
    else:
        print("\n🎯 SYSTEM STATUS: ALL COMPONENTS WORKING")
        print("   Duplicate detection should be functional")
        print("   If still not working, check middleware configuration")
    
    # Provide solutions
    provide_solutions()
    
    return 0


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
