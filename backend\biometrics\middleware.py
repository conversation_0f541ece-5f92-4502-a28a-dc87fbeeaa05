"""
Biometric Duplicate Detection Middleware

This middleware automatically checks for duplicate fingerprints during
citizen registration and biometric data updates.
"""

import json
import logging
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin
from .duplicate_detection_service import EnhancedDuplicateDetectionService

logger = logging.getLogger(__name__)


class BiometricDuplicateDetectionMiddleware(MiddlewareMixin):
    """
    Middleware to automatically detect duplicate fingerprints during registration.
    
    This middleware intercepts requests to citizen registration endpoints and
    automatically checks for duplicate fingerprints before allowing the registration
    to proceed.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.detection_service = EnhancedDuplicateDetectionService()
        
        # Endpoints that should trigger duplicate detection
        self.monitored_endpoints = [
            '/api/citizens/register/',
            '/api/citizens/create/',
            '/api/biometrics/save/',
            '/api/biometrics/update/'
        ]
        
        super().__init__(get_response)
    
    def process_request(self, request):
        """
        Process incoming requests to check for duplicate detection triggers.
        """
        # Only process POST requests to monitored endpoints
        if request.method != 'POST':
            return None
        
        # Check if this is a monitored endpoint
        if not any(endpoint in request.path for endpoint in self.monitored_endpoints):
            return None
        
        # Skip if this is already a duplicate check request
        if 'duplicate' in request.path.lower():
            return None
        
        try:
            # Parse request body to extract biometric data
            if hasattr(request, 'body') and request.body:
                try:
                    data = json.loads(request.body.decode('utf-8'))
                except (json.JSONDecodeError, UnicodeDecodeError):
                    # If we can't parse the body, let the request continue
                    return None
                
                # Extract fingerprint data
                fingerprint_data = self._extract_fingerprint_data(data)
                
                if fingerprint_data:
                    # Perform duplicate check
                    duplicate_result = self.detection_service.check_duplicate_registration(fingerprint_data)
                    
                    if duplicate_result['has_duplicates']:
                        # Block the registration due to duplicates
                        logger.warning(f"🚨 BLOCKING REGISTRATION: Duplicate fingerprints detected")
                        logger.warning(f"   Request path: {request.path}")
                        logger.warning(f"   Duplicates found: {len(duplicate_result['matches'])}")
                        
                        return JsonResponse({
                            'success': False,
                            'error': 'Duplicate fingerprints detected',
                            'error_code': 'DUPLICATE_BIOMETRIC',
                            'duplicate_data': {
                                'has_duplicates': True,
                                'matches': duplicate_result['matches'],
                                'total_matches': duplicate_result['total_matches'],
                                'message': 'Registration blocked due to duplicate fingerprints'
                            },
                            'fraud_prevention': {
                                'risk_level': 'critical',
                                'block_registration': True,
                                'recommendations': [
                                    '🚨 REGISTRATION BLOCKED: Identical fingerprints found in system',
                                    'Please verify citizen identity and check for existing registrations'
                                ]
                            }
                        }, status=409)  # 409 Conflict
        
        except Exception as e:
            logger.error(f"Error in duplicate detection middleware: {e}")
            # Don't block the request if there's an error in duplicate detection
            return None
        
        return None
    
    def _extract_fingerprint_data(self, data: dict) -> dict:
        """
        Extract fingerprint data from request payload.
        
        Args:
            data (dict): Request payload
            
        Returns:
            dict: Extracted fingerprint data
        """
        fingerprint_data = {}
        
        # Check various possible locations for fingerprint data
        biometric_data = data.get('biometric_data', {})
        if biometric_data:
            fingerprint_data.update({
                'left_thumb_fingerprint': biometric_data.get('left_thumb_fingerprint'),
                'right_thumb_fingerprint': biometric_data.get('right_thumb_fingerprint')
            })
        
        # Check direct fields
        if data.get('left_thumb_fingerprint'):
            fingerprint_data['left_thumb_fingerprint'] = data['left_thumb_fingerprint']
        
        if data.get('right_thumb_fingerprint'):
            fingerprint_data['right_thumb_fingerprint'] = data['right_thumb_fingerprint']
        
        # Check nested citizen data
        citizen_data = data.get('citizen_data', {})
        if citizen_data:
            citizen_biometric = citizen_data.get('biometric_data', {})
            if citizen_biometric:
                fingerprint_data.update({
                    'left_thumb_fingerprint': citizen_biometric.get('left_thumb_fingerprint'),
                    'right_thumb_fingerprint': citizen_biometric.get('right_thumb_fingerprint')
                })
        
        # Filter out None values
        return {k: v for k, v in fingerprint_data.items() if v}


class BiometricAuditMiddleware(MiddlewareMixin):
    """
    Middleware to audit biometric operations for security and compliance.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def process_response(self, request, response):
        """
        Log biometric operations for audit purposes.
        """
        # Only audit biometric-related endpoints
        if '/api/biometrics/' in request.path or '/api/citizens/' in request.path:
            try:
                user = getattr(request, 'user', None)
                user_info = f"{user.username} ({user.id})" if user and user.is_authenticated else "Anonymous"
                
                # Log the operation
                logger.info(f"🔐 BIOMETRIC AUDIT: {request.method} {request.path}")
                logger.info(f"   User: {user_info}")
                logger.info(f"   Status: {response.status_code}")
                logger.info(f"   IP: {request.META.get('REMOTE_ADDR', 'Unknown')}")
                
                # Log duplicate detection results if present
                if hasattr(response, 'content') and response.content:
                    try:
                        response_data = json.loads(response.content.decode('utf-8'))
                        if response_data.get('duplicate_data'):
                            logger.info(f"   Duplicate Check: {response_data['duplicate_data']['has_duplicates']}")
                            if response_data['duplicate_data']['has_duplicates']:
                                logger.warning(f"   🚨 SECURITY ALERT: Duplicate fingerprints detected")
                    except (json.JSONDecodeError, UnicodeDecodeError):
                        pass
                        
            except Exception as e:
                logger.error(f"Error in biometric audit middleware: {e}")
        
        return response


class BiometricCacheInvalidationMiddleware(MiddlewareMixin):
    """
    Middleware to automatically invalidate duplicate detection cache when
    biometric data is modified.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.detection_service = EnhancedDuplicateDetectionService()
        
        # Endpoints that modify biometric data
        self.cache_invalidation_endpoints = [
            '/api/citizens/register/',
            '/api/citizens/create/',
            '/api/citizens/update/',
            '/api/biometrics/save/',
            '/api/biometrics/update/',
            '/api/citizens/delete/',
            '/api/biometrics/delete/'
        ]
        
        super().__init__(get_response)
    
    def process_response(self, request, response):
        """
        Invalidate cache after successful biometric data modifications.
        """
        # Only process successful requests (2xx status codes)
        if not (200 <= response.status_code < 300):
            return response
        
        # Check if this endpoint modifies biometric data
        if any(endpoint in request.path for endpoint in self.cache_invalidation_endpoints):
            try:
                # Clear the duplicate detection cache
                self.detection_service.clear_cache()
                logger.info(f"🗑️ Cache invalidated due to biometric data modification: {request.path}")
                
            except Exception as e:
                logger.error(f"Error invalidating duplicate detection cache: {e}")
        
        return response
