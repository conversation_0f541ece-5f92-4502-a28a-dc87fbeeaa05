#!/bin/bash

# Fix Authentication 404 Error <PERSON>
# This script fixes the authentication endpoint 404 issue

echo "🔧 Fixing Authentication 404 Error"
echo "==================================="

# Step 1: Test current state
echo "📍 Step 1: Testing current authentication endpoint..."
python test_auth_endpoint.py

# Step 2: Restart backend service to apply middleware changes
echo ""
echo "📍 Step 2: Restarting backend service..."
docker-compose restart backend

# Step 3: Wait for service to restart
echo "⏳ Waiting for backend to restart..."
sleep 20

# Step 4: Test again
echo ""
echo "📍 Step 3: Testing after restart..."
python test_auth_endpoint.py

# Step 5: If still not working, do a full rebuild
echo ""
echo "📍 Step 4: If still not working, doing full rebuild..."
read -p "Do you want to do a full rebuild? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "Stopping services..."
    docker-compose down
    
    echo "Rebuilding backend..."
    docker-compose build backend
    
    echo "Starting services..."
    docker-compose up -d
    
    echo "Waiting for services to start..."
    sleep 30
    
    echo "Running final test..."
    python test_auth_endpoint.py
fi

echo ""
echo "✅ Authentication fix completed!"
echo ""
echo "🔍 Manual testing:"
echo "1. Test health check:"
echo "   curl http://************:8000/"
echo ""
echo "2. Test auth endpoint:"
echo "   curl -X POST http://************:8000/api/auth/token/ \\"
echo "     -H 'Content-Type: application/json' \\"
echo "     -d '{\"email\": \"<EMAIL>\", \"password\": \"admin123\"}'"
echo ""
echo "3. Test in browser:"
echo "   Frontend: http://************:3000/"
echo "   Try logging in with: <EMAIL> / admin123"
echo ""
echo "💡 If still not working:"
echo "- Check Docker logs: docker-compose logs backend"
echo "- Verify middleware changes are applied"
echo "- Ensure public tenant exists with IP domain"
