import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Grid,
  Alert,
  Chip,
  Divider,
  Button
} from '@mui/material';
import {
  CalendarToday as CalendarIcon,
  Today as TodayIcon,
  Event as EventIcon
} from '@mui/icons-material';
import EthiopianCalendarPicker, { EthiopianCalendarGrid } from '../common/EthiopianCalendarPicker';
import {
  getCurrentEthiopianDate,
  formatEthiopianDate,
  gregorianToEthiopian
} from '../../utils/ethiopianCalendar';

/**
 * Ethiopian Calendar Demo Component
 * 
 * Demonstrates the true Ethiopian calendar interface where:
 * - Users see Ethiopian years (2017 = current year)
 * - Users see Ethiopian months (Meskerem, Tikimt, etc.)
 * - Users see Ethiopian days (1-30 for regular months)
 * - System converts to Gregorian for storage
 */
const EthiopianCalendarDemo = () => {
  const [selectedDate, setSelectedDate] = useState(null);
  const [showGrid, setShowGrid] = useState(false);
  
  const currentEthiopian = getCurrentEthiopianDate();
  const today = new Date();
  const todayEthiopian = gregorianToEthiopian(today);

  const handleDateChange = (gregorianDate) => {
    setSelectedDate(gregorianDate);
    console.log('Ethiopian Calendar Selection:', {
      gregorian: gregorianDate.toISOString(),
      ethiopian: gregorianToEthiopian(gregorianDate),
      formatted: formatEthiopianDate(gregorianToEthiopian(gregorianDate), 'DD MMM YYYY', 'en')
    });
  };

  const setToday = () => {
    setSelectedDate(new Date());
  };

  const getSelectedInfo = () => {
    if (!selectedDate) return null;
    
    const ethiopianDate = gregorianToEthiopian(selectedDate);
    return {
      gregorian: {
        date: selectedDate,
        formatted: selectedDate.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        })
      },
      ethiopian: {
        date: ethiopianDate,
        formatted: formatEthiopianDate(ethiopianDate, 'DD MMM YYYY', 'en'),
        formattedAmharic: formatEthiopianDate(ethiopianDate, 'DD MMM YYYY', 'am')
      }
    };
  };

  const selectedInfo = getSelectedInfo();

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        True Ethiopian Calendar Interface
      </Typography>
      
      <Alert severity="success" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>Ethiopian Calendar Interface:</strong> Users select dates using Ethiopian calendar 
          (Year {currentEthiopian.year}, Month {formatEthiopianDate(currentEthiopian, 'MMM', 'en')}). 
          System automatically converts to Gregorian for storage.
        </Typography>
      </Alert>

      <Grid container spacing={3}>
        {/* Current Date Info */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <TodayIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Today's Date
              </Typography>
              
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', mb: 2 }}>
                <Chip
                  icon={<CalendarIcon />}
                  label={`Ethiopian: ${formatEthiopianDate(todayEthiopian, 'DD MMM YYYY', 'en')}`}
                  color="primary"
                  size="medium"
                />
                <Chip
                  icon={<CalendarIcon />}
                  label={`Gregorian: ${today.toLocaleDateString('en-US')}`}
                  color="secondary"
                  size="medium"
                />
              </Box>

              <Typography variant="body2" color="text.secondary">
                Ethiopian Year: <strong>{todayEthiopian.year}</strong> • 
                Month: <strong>{formatEthiopianDate(todayEthiopian, 'MMM', 'en')}</strong> • 
                Day: <strong>{todayEthiopian.day}</strong>
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Ethiopian Calendar Picker */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <EventIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Ethiopian Calendar Picker
              </Typography>
              
              <EthiopianCalendarPicker
                label="Select Date (Ethiopian Calendar)"
                value={selectedDate}
                onChange={handleDateChange}
                language="en"
                showConversion={true}
                helperText="Select year, month, and day using Ethiopian calendar"
                minYear={2010}
                maxYear={2025}
              />

              <Box sx={{ mt: 2 }}>
                <Button
                  variant="outlined"
                  startIcon={<TodayIcon />}
                  onClick={setToday}
                  size="small"
                >
                  Set to Today (Ethiopian {currentEthiopian.year})
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Selected Date Information */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Selected Date Information
              </Typography>

              {selectedInfo ? (
                <Box>
                  <Typography variant="subtitle2" color="primary" gutterBottom>
                    Ethiopian Calendar:
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 1 }}>
                    {selectedInfo.ethiopian.formatted}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Amharic: {selectedInfo.ethiopian.formattedAmharic}
                  </Typography>

                  <Divider sx={{ my: 2 }} />

                  <Typography variant="subtitle2" color="secondary" gutterBottom>
                    Gregorian Calendar (System Storage):
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 1 }}>
                    {selectedInfo.gregorian.formatted}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    ISO: {selectedInfo.gregorian.date.toISOString().split('T')[0]}
                  </Typography>

                  <Divider sx={{ my: 2 }} />

                  <Typography variant="subtitle2" gutterBottom>
                    Technical Details:
                  </Typography>
                  <Box sx={{ p: 1, bgcolor: 'grey.50', borderRadius: 1 }}>
                    <Typography variant="caption" color="text.secondary">
                      Ethiopian: {selectedInfo.ethiopian.date.year}/{selectedInfo.ethiopian.date.month}/{selectedInfo.ethiopian.date.day}<br />
                      Gregorian: {selectedInfo.gregorian.date.toISOString()}<br />
                      User Experience: Ethiopian Calendar Interface<br />
                      System Storage: Gregorian Date Object
                    </Typography>
                  </Box>
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  Select a date using the Ethiopian calendar picker to see the conversion details.
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Calendar Grid Demo */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Ethiopian Calendar Grid
                </Typography>
                <Button
                  variant="outlined"
                  onClick={() => setShowGrid(!showGrid)}
                  size="small"
                >
                  {showGrid ? 'Hide' : 'Show'} Calendar Grid
                </Button>
              </Box>

              {showGrid && (
                <EthiopianCalendarGrid
                  selectedDate={selectedDate}
                  onDateSelect={handleDateChange}
                  language="en"
                />
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Usage Instructions */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                How the Ethiopian Calendar Interface Works
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <CalendarIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="subtitle2">1. Ethiopian Interface</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Users see and select Ethiopian years (2017), months (Meskerem), and days (1-30)
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={4}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <EventIcon color="secondary" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="subtitle2">2. Automatic Conversion</Typography>
                    <Typography variant="body2" color="text.secondary">
                      System automatically converts Ethiopian date to Gregorian equivalent
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={4}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <TodayIcon color="success" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="subtitle2">3. System Storage</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Gregorian Date object is used for system operations and storage
                    </Typography>
                  </Box>
                </Grid>
              </Grid>

              <Alert severity="info" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  <strong>Key Point:</strong> Users never see Gregorian calendar interface. 
                  They work exclusively with Ethiopian calendar (Year 2017, Meskerem, Tikimt, etc.) 
                  while the system handles Gregorian conversion automatically.
                </Typography>
              </Alert>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default EthiopianCalendarDemo;
