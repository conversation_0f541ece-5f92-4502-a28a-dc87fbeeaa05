# Kebele Biometric Deployment Guide

## Architecture Overview

Your GoID system uses a **hybrid deployment architecture**:

```
┌─────────────────────────────────────────────────────────────┐
│                    CENTRAL SERVER                           │
│  ┌─────────────────────────────────────────────────────────┐│
│  │              Docker Containers                          ││
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    ││
│  │  │  Frontend   │  │   Backend   │  │  Database   │    ││
│  │  │   (Web)     │  │  (Django)   │  │ (PostgreSQL)│    ││
│  │  └─────────────┘  └─────────────┘  └─────────────┘    ││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
                              │
                         NETWORK ACCESS
                              │
┌─────────────────────────────────────────────────────────────┐
│                    KEBELE WORKSTATIONS                      │
│                                                             │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   Kebele A PC   │    │   Kebele B PC   │    ...         │
│  │                 │    │                 │                │
│  │  Web Browser ←──┼────┼─→ GoID System   │                │
│  │       ↓         │    │                 │                │
│  │  Local Service  │    │  Local Service  │                │
│  │       ↓         │    │       ↓         │                │
│  │  Futronic FS88H │    │  Futronic FS88H │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

## Deployment Steps

### 1. Central Server Setup

**On your central server (Docker host):**

```bash
# Start the GoID system
docker-compose -f docker-compose.production.yml up -d

# Verify services are running
docker-compose -f docker-compose.production.yml ps

# Access the system
# Frontend: http://your-server-ip:3000
# Backend API: http://your-server-ip:8000
```

### 2. Kebele Workstation Setup

**On each kebele PC with Futronic FS88H device:**

#### 2.1 Install Prerequisites
```bash
# Install Python 3.8+
# Download from: https://python.org

# Install required packages
cd local-biometric-service
pip install -r requirements.txt
```

#### 2.2 Copy Futronic SDK Files
Copy these files to the `local-biometric-service` directory:
- `ftrScanAPI.dll`
- `ftrMathAPI.dll` (if available)
- Any other Futronic SDK DLL files

#### 2.3 Start Kebele Biometric Service
```bash
# Run the startup script
start_kebele_biometric_service.bat

# OR manually:
cd local-biometric-service
python kebele_biometric_service.py
```

#### 2.4 Verify Local Service
- Open browser: `http://localhost:8001`
- Should show: "🟢 Connected" if device is working
- Health check: `http://localhost:8001/api/health`

### 3. Network Configuration

#### 3.1 Firewall Settings (Windows)
```
Allow Python.exe through Windows Firewall:
1. Windows Security → Firewall & network protection
2. Allow an app through firewall
3. Add Python.exe
4. Check both Private and Public networks
```

#### 3.2 Test Network Access
From kebele PC browser:
```
# Access central GoID system
http://your-server-ip:3000

# Verify local biometric service
http://localhost:8001
```

## Usage Workflow

### For Kebele Staff:

1. **Start Local Service**
   ```bash
   start_kebele_biometric_service.bat
   ```
   Keep this window open during work hours.

2. **Access GoID System**
   - Open web browser
   - Navigate to: `http://your-server-ip:3000`
   - Login with kebele credentials

3. **Register Citizens**
   - Go to Citizens → Register New Citizen
   - Fill out citizen information
   - At Biometric Capture step:
     - System automatically detects local device
     - Click "Capture Left Thumb"
     - Place finger on scanner when prompted
     - Click "Capture Right Thumb"
     - Place finger on scanner when prompted
   - Complete registration

## System Behavior

### With Local Device Available:
```
Console Log:
✅ Using local biometric device at kebele workstation
✅ Local device initialized at kebele workstation
✅ left thumb captured via local device at kebele workstation
✅ right thumb captured via local device at kebele workstation
```

### Without Local Device (Fallback):
```
Console Log:
Local biometric device not available: Failed to fetch
✅ Server simulation initialized
✅ left thumb captured via server simulation
✅ right thumb captured via server simulation
```

## Troubleshooting

### Local Service Issues

**Service won't start:**
- Check Python installation
- Verify DLL files are present
- Ensure device is connected via USB
- Check device drivers are installed

**Device not detected:**
- Verify Futronic FS88H is connected
- Check Device Manager for device
- Restart device (unplug/replug USB)
- Try running as Administrator

**Capture fails:**
- Clean fingerprint scanner surface
- Ensure proper finger placement
- Check service console for error messages
- Restart local service

### Network Access Issues

**Can't access GoID system:**
- Verify server IP address
- Check network connectivity
- Ensure Docker services are running
- Check firewall settings

**Local service not accessible:**
- Verify service is running on port 8001
- Check Windows Firewall settings
- Test with: `http://localhost:8001`

### Browser Issues

**Biometric capture not working:**
- Check browser console for errors
- Verify local service is running
- Test local service directly
- Clear browser cache

## Production Considerations

### Security
- Configure proper firewall rules
- Use HTTPS for production deployment
- Implement proper authentication
- Regular security updates

### Backup
- Regular database backups on central server
- Document kebele service configurations
- Keep spare Futronic devices

### Monitoring
- Monitor central server resources
- Check kebele service logs regularly
- Set up alerts for service failures

### Scaling
- Each kebele needs its own biometric service
- Central server can handle multiple kebeles
- Consider load balancing for high usage

## Support Contacts

For technical issues:
1. Check service logs first
2. Verify device connections
3. Test network connectivity
4. Contact system administrator

## File Locations

**Central Server:**
- Docker Compose: `docker-compose.production.yml`
- Application logs: `docker-compose logs`

**Kebele Workstations:**
- Service files: `local-biometric-service/`
- Startup script: `start_kebele_biometric_service.bat`
- Service logs: Console window output
