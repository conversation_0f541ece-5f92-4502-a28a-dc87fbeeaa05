#!/usr/bin/env python3
"""
Test script to verify shared data endpoints are working correctly.
"""

import requests
import j<PERSON>

def test_shared_data_endpoints():
    """Test the shared data endpoints."""
    
    base_url = "http://************:8000"
    
    print("🧪 Testing Shared Data Endpoints")
    print("=" * 40)
    
    # Test endpoints that should work without authentication
    shared_endpoints = [
        {
            "name": "Countries",
            "url": "/api/shared/countries/",
            "auth_required": False
        },
        {
            "name": "Regions", 
            "url": "/api/shared/regions/",
            "auth_required": False
        },
        {
            "name": "Religions",
            "url": "/api/shared/religions/",
            "auth_required": False
        },
        {
            "name": "Citizen Statuses",
            "url": "/api/shared/citizen-statuses/",
            "auth_required": False
        },
        {
            "name": "Marital Statuses",
            "url": "/api/shared/marital-statuses/",
            "auth_required": False
        },
        {
            "name": "Document Types",
            "url": "/api/shared/document-types/",
            "auth_required": False
        },
        {
            "name": "Employment Types",
            "url": "/api/shared/employment-types/",
            "auth_required": False
        }
    ]
    
    # Test tenant-specific endpoints that may require authentication
    tenant_endpoints = [
        {
            "name": "Subcities",
            "url": "/api/tenants/subcities/",
            "auth_required": True
        },
        {
            "name": "Kebeles",
            "url": "/api/tenants/kebeles/",
            "auth_required": True
        },
        {
            "name": "Ketenas",
            "url": "/api/shared/ketenas/",
            "auth_required": False
        }
    ]
    
    # Test shared endpoints first
    print("📍 Testing shared endpoints (no auth required)...")
    for endpoint in shared_endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint['url']}", timeout=10)
            
            print(f"  {endpoint['name']}: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                count = len(data) if isinstance(data, list) else len(data.get('results', []))
                print(f"    ✅ Success - {count} items")
            else:
                print(f"    ❌ Failed - {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ Error - {e}")
    
    # Get authentication token for tenant endpoints
    print(f"\n📍 Getting authentication token...")
    try:
        login_response = requests.post(
            f"{base_url}/api/auth/token/",
            json={
                "username": "<EMAIL>",
                "password": "password123"
            },
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if login_response.status_code == 200:
            login_data = login_response.json()
            access_token = login_data.get('access')
            print("  ✅ Authentication successful")
            
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
        else:
            print(f"  ❌ Authentication failed: {login_response.status_code}")
            headers = {}
            
    except Exception as e:
        print(f"  ❌ Authentication error: {e}")
        headers = {}
    
    # Test tenant endpoints
    print(f"\n📍 Testing tenant endpoints...")
    for endpoint in tenant_endpoints:
        try:
            if endpoint['auth_required'] and headers:
                response = requests.get(f"{base_url}{endpoint['url']}", headers=headers, timeout=10)
            else:
                response = requests.get(f"{base_url}{endpoint['url']}", timeout=10)
            
            print(f"  {endpoint['name']}: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                count = len(data) if isinstance(data, list) else len(data.get('results', []))
                print(f"    ✅ Success - {count} items")
            elif response.status_code == 404:
                print(f"    ❌ Not Found - Check URL pattern")
            elif response.status_code == 403:
                print(f"    ❌ Forbidden - Check permissions")
            else:
                print(f"    ❌ Failed - {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ Error - {e}")
    
    # Test the corrected URLs specifically
    print(f"\n📍 Testing corrected URLs specifically...")
    corrected_urls = [
        "/api/tenants/subcities/",
        "/api/tenants/kebeles/"
    ]
    
    for url in corrected_urls:
        try:
            if headers:
                response = requests.get(f"{base_url}{url}", headers=headers, timeout=10)
            else:
                response = requests.get(f"{base_url}{url}", timeout=10)
            
            print(f"  {url}: {response.status_code}")
            
            if response.status_code == 200:
                print(f"    ✅ URL is working correctly")
            elif response.status_code == 404:
                print(f"    ❌ URL not found - check backend routing")
            elif response.status_code == 403:
                print(f"    ❌ Access forbidden - check permissions")
            else:
                print(f"    ❌ Unexpected status: {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ Error: {e}")

if __name__ == "__main__":
    test_shared_data_endpoints()
    
    print("\n" + "=" * 40)
    print("🔧 If shared data endpoints are still not working:")
    print("1. Restart frontend: docker-compose restart frontend")
    print("2. Check backend logs: docker-compose logs backend")
    print("3. Verify URL patterns in backend routing")
    print("4. Clear browser cache and reload")
    print("\n🌐 Expected working URLs:")
    print("   Shared: http://************:8000/api/shared/countries/")
    print("   Subcities: http://************:8000/api/tenants/subcities/")
    print("   Kebeles: http://************:8000/api/tenants/kebeles/")
    print("\n💡 Frontend should now call correct URLs without double 'tenants'")
