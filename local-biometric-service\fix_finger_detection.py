#!/usr/bin/env python3
"""
Fix Finger Detection Function
Analyze and fix the ftrScanIsFingerPresent function call
"""

import ctypes
from ctypes import cdll, c_int, c_uint, c_void_p, c_ubyte, c_bool, c_char, POINTER, byref
import sys
import os
import time

def test_finger_detection_signatures():
    """Test different function signatures for ftrScanIsFingerPresent"""
    
    try:
        # Load SDK
        dll_path = os.path.abspath('./ftrScanAPI.dll')
        scan_api = cdll.LoadLibrary(dll_path)
        
        # Initialize device
        print("Initializing device...")
        device_handle = scan_api.ftrScanOpenDevice()
        
        if not device_handle:
            print("ERROR: Failed to open device")
            return
        
        print(f"Device handle: {device_handle}")
        
        # Test different function signatures for ftrScanIsFingerPresent
        signatures_to_test = [
            {
                'name': 'Signature 1: (handle, POINTER(c_int))',
                'restype': c_int,
                'argtypes': [c_void_p, POINTER(c_int)]
            },
            {
                'name': 'Signature 2: (handle, POINTER(c_uint))',
                'restype': c_int,
                'argtypes': [c_void_p, POINTER(c_uint)]
            },
            {
                'name': 'Signature 3: (handle, POINTER(c_bool))',
                'restype': c_int,
                'argtypes': [c_void_p, POINTER(c_bool)]
            },
            {
                'name': 'Signature 4: (handle, POINTER(c_char))',
                'restype': c_int,
                'argtypes': [c_void_p, POINTER(c_char)]
            },
            {
                'name': 'Signature 5: (handle, POINTER(c_ubyte))',
                'restype': c_int,
                'argtypes': [c_void_p, POINTER(c_ubyte)]
            },
            {
                'name': 'Signature 6: (handle) - no output parameter',
                'restype': c_int,
                'argtypes': [c_void_p]
            },
            {
                'name': 'Signature 7: (handle) - returns bool directly',
                'restype': c_bool,
                'argtypes': [c_void_p]
            }
        ]
        
        for i, sig in enumerate(signatures_to_test):
            print(f"\n=== Testing {sig['name']} ===")
            
            try:
                # Configure function signature
                scan_api.ftrScanIsFingerPresent.restype = sig['restype']
                scan_api.ftrScanIsFingerPresent.argtypes = sig['argtypes']
                
                print("Function signature configured successfully")
                
                # Test the function call
                if len(sig['argtypes']) == 1:
                    # No output parameter
                    print("Calling function without output parameter...")
                    result = scan_api.ftrScanIsFingerPresent(device_handle)
                    print(f"SUCCESS: Function returned: {result}")
                    
                else:
                    # With output parameter
                    if sig['argtypes'][1] == POINTER(c_int):
                        finger_status = c_int(0)
                    elif sig['argtypes'][1] == POINTER(c_uint):
                        finger_status = c_uint(0)
                    elif sig['argtypes'][1] == POINTER(c_bool):
                        finger_status = c_bool(False)
                    elif sig['argtypes'][1] == POINTER(c_char):
                        finger_status = c_char(b'0')
                    elif sig['argtypes'][1] == POINTER(c_ubyte):
                        finger_status = c_ubyte(0)
                    
                    print("Calling function with output parameter...")
                    result = scan_api.ftrScanIsFingerPresent(device_handle, byref(finger_status))
                    print(f"SUCCESS: Function returned: {result}, Output: {finger_status.value}")
                
                print(f"✅ Signature {i+1} WORKS!")
                
                # Test multiple calls to ensure stability
                print("Testing multiple calls for stability...")
                for j in range(3):
                    if len(sig['argtypes']) == 1:
                        result = scan_api.ftrScanIsFingerPresent(device_handle)
                    else:
                        if sig['argtypes'][1] == POINTER(c_int):
                            finger_status = c_int(0)
                        elif sig['argtypes'][1] == POINTER(c_uint):
                            finger_status = c_uint(0)
                        elif sig['argtypes'][1] == POINTER(c_bool):
                            finger_status = c_bool(False)
                        elif sig['argtypes'][1] == POINTER(c_char):
                            finger_status = c_char(b'0')
                        elif sig['argtypes'][1] == POINTER(c_ubyte):
                            finger_status = c_ubyte(0)
                        
                        result = scan_api.ftrScanIsFingerPresent(device_handle, byref(finger_status))
                    
                    print(f"  Call {j+1}: Result = {result}")
                    time.sleep(0.1)
                
                print(f"✅ Signature {i+1} is STABLE!")
                
            except Exception as e:
                print(f"❌ Signature {i+1} FAILED: {e}")
                continue
        
        # Close device
        print("\nClosing device...")
        scan_api.ftrScanCloseDevice(device_handle)
        print("Device closed successfully")
        
    except Exception as e:
        print(f"ERROR: Test failed: {e}")

def test_alternative_approaches():
    """Test alternative approaches to finger detection"""
    
    try:
        # Load SDK
        dll_path = os.path.abspath('./ftrScanAPI.dll')
        scan_api = cdll.LoadLibrary(dll_path)
        
        # Initialize device
        print("\n=== TESTING ALTERNATIVE APPROACHES ===")
        print("Initializing device...")
        device_handle = scan_api.ftrScanOpenDevice()
        
        if not device_handle:
            print("ERROR: Failed to open device")
            return
        
        print(f"Device handle: {device_handle}")
        
        # Alternative 1: Use frame capture with small timeout to detect finger
        print("\n--- Alternative 1: Frame Capture Detection ---")
        try:
            # Configure frame capture
            scan_api.ftrScanGetFrame.restype = c_int
            scan_api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(c_ubyte), POINTER(c_uint)]
            
            # Small buffer for quick test
            buffer_size = 1000
            image_buffer = (c_ubyte * buffer_size)()
            frame_size = c_uint(buffer_size)
            
            print("Testing quick frame capture for finger detection...")
            result = scan_api.ftrScanGetFrame(device_handle, image_buffer, byref(frame_size))
            
            print(f"Frame capture result: {result}")
            print(f"Frame size: {frame_size.value}")
            
            if result == 0:  # FTR_OK
                print("✅ Frame capture works - can use for finger detection")
            elif result == 1:  # FTR_ERROR_EMPTY_FRAME
                print("✅ Empty frame detected - finger detection via frame capture works")
            else:
                print(f"Frame capture returned code: {result}")
                
        except Exception as e:
            print(f"❌ Frame capture approach failed: {e}")
        
        # Alternative 2: Check if there are other finger detection functions
        print("\n--- Alternative 2: Other Finger Detection Functions ---")
        possible_functions = [
            'ftrScanIsLiveFinger',
            'ftrScanDetectFinger',
            'ftrScanCheckFinger',
            'ftrScanFingerPresent',
            'ftrIsFingerPresent',
            'ftrDetectFinger',
            'ftrCheckFinger'
        ]
        
        for func_name in possible_functions:
            try:
                func = getattr(scan_api, func_name)
                print(f"✅ Found alternative function: {func_name}")
                
                # Try to call it with basic signature
                func.restype = c_int
                func.argtypes = [c_void_p]
                
                result = func(device_handle)
                print(f"  {func_name} returned: {result}")
                
            except AttributeError:
                print(f"❌ Function {func_name} not found")
            except Exception as e:
                print(f"❌ Function {func_name} failed: {e}")
        
        # Close device
        print("\nClosing device...")
        scan_api.ftrScanCloseDevice(device_handle)
        print("Device closed successfully")
        
    except Exception as e:
        print(f"ERROR: Alternative test failed: {e}")

if __name__ == '__main__':
    print("=== FINGER DETECTION FUNCTION FIX TOOL ===")
    print("Analyzing ftrScanIsFingerPresent function call issues")
    print()
    
    # Test different function signatures
    test_finger_detection_signatures()
    
    # Test alternative approaches
    test_alternative_approaches()
    
    print("\n=== ANALYSIS COMPLETE ===")
    print("Check the results above to identify the working signature")
    print("or alternative approach for finger detection.")
