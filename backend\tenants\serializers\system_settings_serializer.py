from rest_framework import serializers
from tenants.models import SystemSettings


class SystemSettingsSerializer(serializers.ModelSerializer):
    """
    Serializer for global system settings.
    """
    
    class Meta:
        model = SystemSettings
        fields = [
            'id',
            'primary_color',
            'secondary_color', 
            'public_services_enabled',
            'updated_at',
            'updated_by'
        ]
        read_only_fields = ['id', 'updated_at', 'updated_by']
    
    def validate_primary_color(self, value):
        """Validate primary color is a valid hex color."""
        import re
        if not re.match(r'^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$', value):
            raise serializers.ValidationError(
                "Primary color must be a valid hex color (e.g., #ff8f00)."
            )
        return value
    
    def validate_secondary_color(self, value):
        """Validate secondary color is a valid hex color."""
        import re
        if not re.match(r'^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$', value):
            raise serializers.ValidationError(
                "Secondary color must be a valid hex color (e.g., #ef6c00)."
            )
        return value
