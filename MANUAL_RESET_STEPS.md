# Manual Database Reset and Migration Steps

Follow these steps to completely reset the database and test the migration fix:

## Step 1: Complete Cleanup
```bash
# Stop all containers and remove volumes (this deletes the database)
docker-compose down -v

# Remove orphaned containers
docker container prune -f

# Remove unused volumes
docker volume prune -f
```

## Step 2: Rebuild Backend
```bash
# Rebuild the backend image without cache to ensure our fixes are included
docker-compose build --no-cache backend
```

## Step 3: Start Database First
```bash
# Start just the database to let it initialize
docker-compose up -d db

# Wait 10 seconds for database to be ready
# (Windows: timeout /t 10, Linux/Mac: sleep 10)
```

## Step 4: Start All Services
```bash
# Start all services
docker-compose up -d

# Wait 30 seconds for backend to complete initialization
# (Windows: timeout /t 30, Linux/Mac: sleep 30)
```

## Step 5: Check Status
```bash
# Check if all containers are running
docker-compose ps

# Expected output: All containers should show "Up" status
```

## Step 6: Monitor Backend Logs
```bash
# View backend logs to see migration progress
docker-compose logs backend

# Look for these success messages:
# ✅ "Database connection successful"
# ✅ "Running shared schema migrations..."
# ✅ "Running tenant schema migrations..."
# ✅ "Data initialization complete"
# ✅ "Starting Django development server..."
```

## Step 7: Test the Fix
```bash
# Test if the citizen clearance table exists
docker exec goid-backend-1 python manage.py shell -c "
from workflows.models import CitizenClearanceRequest
from django_tenants.utils import schema_context
from tenants.models import Tenant

try:
    print('Testing CitizenClearanceRequest model...')
    print('✅ CitizenClearanceRequest model imported successfully')
    
    tenants = Tenant.objects.filter(schema_name__in=['kebele_bole_01'])
    for tenant in tenants:
        with schema_context(tenant.schema_name):
            count = CitizenClearanceRequest.objects.count()
            print(f'✅ citizen_clearance_requests table exists in {tenant.schema_name} with {count} records')
            
    print('🎉 All tests passed! The fix is working!')
except Exception as e:
    print(f'❌ Error: {e}')
"
```

## Step 8: Test the Application
```bash
# Test if the backend API is responding
curl http://localhost:8000

# Or open in browser:
# Frontend: http://localhost:3000
# Backend API: http://localhost:8000
```

## Expected Results

After following these steps, you should see:

1. ✅ All containers running (docker-compose ps)
2. ✅ No "relation does not exist" errors in logs
3. ✅ Backend responding on port 8000
4. ✅ Frontend accessible on port 3000
5. ✅ citizen_clearance_requests table exists in tenant schemas

## If Something Goes Wrong

### Backend Container Exits
```bash
# Check what went wrong
docker-compose logs backend

# Try starting just the backend to see errors
docker-compose up backend
```

### Database Connection Issues
```bash
# Check if database is running
docker-compose ps db

# Check database logs
docker-compose logs db
```

### Migration Errors
```bash
# Run migrations manually
docker exec goid-backend-1 python manage.py migrate_schemas --shared
docker exec goid-backend-1 python manage.py migrate_schemas --tenant
```

## Quick Reset Command

If you want to start over completely:
```bash
docker-compose down -v && docker-compose build --no-cache backend && docker-compose up -d
```

---

**Note**: The key fix we implemented is using `get_or_create()` instead of manual tenant creation, which prevents the cascade deletion issues that were causing the "citizen_clearance_requests does not exist" error.
