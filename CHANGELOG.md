# Changelog

All notable changes to the GoID project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- New management command `create_idcard_templates` for creating default ID card templates across all tenant schemas
- Enhanced test management command `test_idcard_creation` for verifying ID card functionality with actual card creation
- Comprehensive ID card template system with three default templates per tenant

### Fixed
- **CRITICAL**: Fixed database error `relation "idcards_idcardtemplate" does not exist` that was preventing ID card creation
- **CRITICAL**: Fixed frontend using wrong API endpoints - now uses tenant-specific endpoints for proper schema context
- **CRITICAL**: Fixed serializer validation happening outside tenant schema context by overriding create method in TenantSpecificIDCardViewSet
- **CRITICAL**: Fixed Django ORM vs Raw SQL mismatch where ORM queries returned different results than raw SQL in tenant schemas
- Resolved missing ID card template tables in tenant-specific database schemas
- Fixed multi-tenant schema migration issues for ID card functionality
- Fixed citizen foreign key validation error "Invalid pk - object does not exist" by using raw SQL validation in serializer

### Changed
- Enhanced `migrate_tenant_idcards` management command with better verification and reporting
- Updated frontend ID card creation to use tenant-specific endpoints (`/api/tenants/{tenant_id}/idcards/`)
- Updated frontend template fetching to use tenant-specific endpoints (`/api/tenants/{tenant_id}/idcards/templates/`)
- Improved error handling and logging for tenant-specific operations

### Technical Details
**Root Cause**: The frontend was using global API endpoints (`/api/idcards/`) instead of tenant-specific endpoints (`/api/tenants/{tenant_id}/idcards/`). This caused the backend to look for citizens in the wrong database schema context, resulting in "Invalid pk - object does not exist" errors.

**Solution**:
1. Updated frontend to use proper tenant-specific endpoints
2. Ensured all ID card operations happen within the correct tenant schema context
3. Verified that ID card templates exist in all tenant schemas
4. Fixed serializer validation by overriding the `create` method to ensure validation happens within tenant schema context
5. Implemented raw SQL validation in serializer to bypass Django ORM schema context issues

**Root Cause of Django ORM Issue**: Django ORM was returning different results than raw SQL queries in tenant schemas, suggesting model table name configuration problems or schema context switching issues during ORM queries.

**Files Modified**:
- `frontend/src/pages/idcards/IDCardCreateNew.jsx` - Fixed endpoint URLs
- `frontend/src/pages/idcards/IDCardList.jsx` - Fixed endpoint URLs
- `backend/tenants/views/tenant_idcard_views.py` - Fixed schema context and added raw SQL validation
- `backend/tenants/management/commands/test_idcard_creation.py` - Enhanced testing
- `backend/tenants/management/commands/debug_citizen_13.py` - Added comprehensive debugging

## [2025-05-28] - Database Schema Fix

### Fixed
- **Database Schema Issue**: Resolved critical error where ID card template tables were missing from tenant schemas
  - Error: `django.db.utils.ProgrammingError: relation "idcards_idcardtemplate" does not exist`
  - Impact: ID card creation was completely broken across all tenants
  - Solution: Applied migrations to all 7 tenant schemas and created default templates

### Technical Details
- Applied ID card migrations to tenant schemas: `city_gondar`, `subcity_zoble`, `kebele_kebele14`, `city_addis_ababa`, `subcity_bole`, `kebele_bole_01`
- Created 3 default ID card templates per tenant:
  - Default Template (official government styling)
  - Modern Digital Template (contemporary design)
  - Classic Template (traditional styling)
- Verified table creation: `idcards_idcard` and `idcards_idcardtemplate` now exist in all tenant schemas

### Migration Commands Used
```bash
docker-compose exec backend python manage.py migrate_tenant_idcards
docker-compose exec backend python manage.py create_idcard_templates
```

## Previous Releases

### [2025-05-27] - Multi-Tenant Citizen Management
- Enhanced citizen registration with family information (parents, spouse, children, emergency contacts)
- Implemented tenant-specific citizen data storage
- Added citizen search functionality with gender filtering
- Improved citizen details display with comprehensive family information

### [2025-05-26] - Role-Based Navigation
- Implemented role-based menu visibility
- Fixed navigation permissions for different user roles (superadmin, city_admin, subcity_admin, clerk)
- Enhanced tenant management access control

### [2025-05-25] - Domain-Based Authentication
- Added domain-based login system (<EMAIL> format)
- Implemented tenant detection from domain during login
- Enhanced JWT token handling with proper tenant_id inclusion

### [2025-05-24] - Tenant Hierarchy System
- Established hierarchical tenant structure (City → SubCity → Kebele)
- Implemented schema-based multi-tenancy with PostgreSQL
- Added tenant registration and management functionality

### [2025-05-23] - Initial Project Setup
- Created Django backend with REST API
- Set up React frontend with Material-UI
- Configured Docker development environment
- Implemented basic authentication system

## Migration Notes

### Database Migrations
- Always run `migrate_tenant_idcards` after adding new tenants to ensure ID card functionality
- Use `create_idcard_templates` to populate new tenants with default templates
- Monitor tenant schema creation and ensure all required tables are present

### Breaking Changes
- None in current release

### Deprecations
- None in current release

## Development Guidelines

### Adding New Features
1. Ensure multi-tenant compatibility
2. Test across all tenant schemas
3. Update relevant management commands
4. Document schema changes

### Database Changes
1. Create migrations for both shared and tenant-specific models
2. Test migration rollback procedures
3. Verify data integrity across all tenant schemas
4. Update migration documentation

### Testing
1. Test functionality in multiple tenant contexts
2. Verify role-based access controls
3. Check cross-tenant data isolation
4. Validate API endpoints with proper tenant context

## Support

For issues related to:
- **Database Errors**: Check tenant schema migrations and table existence
- **Multi-Tenancy**: Verify tenant context and schema switching
- **ID Card Creation**: Ensure templates exist in tenant schema
- **Authentication**: Check JWT tokens and domain-based login

## Contributors

- Augment Agent - Database schema fixes and multi-tenant enhancements
- Development Team - Core application development

---

**Note**: This changelog follows semantic versioning. Major version changes indicate breaking changes, minor versions add functionality, and patch versions fix bugs.
