@echo off
echo ========================================
echo   GoID JAR Bridge Service Uninstaller
echo ========================================
echo.

REM Change to the directory where this batch file is located
cd /d "%~dp0"
echo Current directory: %CD%

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Running as Administrator
) else (
    echo ❌ Administrator privileges required
    echo 💡 Right-click and "Run as Administrator"
    pause
    exit /b 1
)

echo.
echo Stopping and uninstalling Windows service...
python "%~dp0install_windows_service.py" uninstall

echo.
echo ========================================
echo    Uninstallation Complete!
echo ========================================
echo.
pause
