#!/usr/bin/env python3
"""
Test script to verify all tenant-related endpoints are working correctly.
"""

import requests
import json

def test_all_tenant_endpoints():
    """Test all tenant-related endpoints."""
    
    base_url = "http://************:8000"
    
    print("🧪 Testing All Tenant Endpoints")
    print("=" * 50)
    
    # Test public tenant management endpoints (should work without auth)
    public_endpoints = [
        {
            "name": "Tenant List",
            "url": "/api/tenants/",
            "auth_required": True
        },
        {
            "name": "Subcities List", 
            "url": "/api/tenants/subcities/",
            "auth_required": True
        },
        {
            "name": "Kebeles List",
            "url": "/api/tenants/kebeles/",
            "auth_required": True
        },
        {
            "name": "Cities List",
            "url": "/api/tenants/cities/",
            "auth_required": True
        },
        {
            "name": "Domains List",
            "url": "/api/tenants/domains/",
            "auth_required": True
        },
        {
            "name": "Countries List",
            "url": "/api/tenants/countries/",
            "auth_required": False
        },
        {
            "name": "Regions List",
            "url": "/api/tenants/regions/",
            "auth_required": False
        }
    ]
    
    # Get authentication token
    print("📍 Step 1: Getting authentication token...")
    try:
        login_response = requests.post(
            f"{base_url}/api/auth/token/",
            json={
                "email": "<EMAIL>",
                "password": "admin123"
            },
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if login_response.status_code == 200:
            login_data = login_response.json()
            access_token = login_data.get('access')
            print("  ✅ Authentication successful")
            
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
        else:
            print(f"  ❌ Authentication failed: {login_response.status_code}")
            headers = {}
            
    except Exception as e:
        print(f"  ❌ Authentication error: {e}")
        headers = {}
    
    # Test public tenant endpoints
    print(f"\n📍 Step 2: Testing tenant management endpoints...")
    for endpoint in public_endpoints:
        try:
            if endpoint['auth_required'] and headers:
                response = requests.get(f"{base_url}{endpoint['url']}", headers=headers, timeout=10)
            else:
                response = requests.get(f"{base_url}{endpoint['url']}", timeout=10)
            
            print(f"  {endpoint['name']}: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                count = len(data) if isinstance(data, list) else len(data.get('results', []))
                print(f"    ✅ Success - {count} items")
            elif response.status_code == 404:
                print(f"    ❌ Not Found - Check URL pattern and middleware")
            elif response.status_code == 403:
                print(f"    ❌ Forbidden - Check permissions")
            elif response.status_code == 401:
                print(f"    ❌ Unauthorized - Check authentication")
            else:
                print(f"    ❌ Failed - {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ Error - {e}")
    
    # Test shared endpoints for comparison
    print(f"\n📍 Step 3: Testing shared endpoints for comparison...")
    shared_endpoints = [
        "/api/shared/countries/",
        "/api/shared/regions/",
        "/api/shared/religions/"
    ]
    
    for url in shared_endpoints:
        try:
            response = requests.get(f"{base_url}{url}", timeout=10)
            print(f"  {url}: {response.status_code}")
            
            if response.status_code == 200:
                print(f"    ✅ Shared endpoint working")
            else:
                print(f"    ❌ Shared endpoint failed")
                
        except Exception as e:
            print(f"    ❌ Error: {e}")
    
    # Test tenant-specific endpoints (if we have a tenant)
    if headers:
        print(f"\n📍 Step 4: Testing tenant-specific endpoints...")
        
        # First try to get a tenant ID
        try:
            tenant_response = requests.get(f"{base_url}/api/tenants/", headers=headers, timeout=10)
            if tenant_response.status_code == 200:
                tenant_data = tenant_response.json()
                tenants = tenant_data.get('results', tenant_data) if isinstance(tenant_data, dict) else tenant_data
                
                if tenants and len(tenants) > 0:
                    tenant_id = tenants[0]['id']
                    print(f"  Using tenant ID: {tenant_id}")
                    
                    # Test tenant-specific endpoints
                    tenant_specific_endpoints = [
                        f"/api/tenants/{tenant_id}/citizens/",
                        f"/api/tenants/{tenant_id}/idcards/",
                        f"/api/tenants/{tenant_id}/citizens/stats/"
                    ]
                    
                    for url in tenant_specific_endpoints:
                        try:
                            response = requests.get(f"{base_url}{url}", headers=headers, timeout=10)
                            print(f"  {url}: {response.status_code}")
                            
                            if response.status_code == 200:
                                print(f"    ✅ Tenant-specific endpoint working")
                            elif response.status_code == 404:
                                print(f"    ❌ Tenant-specific endpoint not found")
                            elif response.status_code == 403:
                                print(f"    ❌ Tenant-specific endpoint forbidden")
                            else:
                                print(f"    ❌ Tenant-specific endpoint failed: {response.status_code}")
                                
                        except Exception as e:
                            print(f"    ❌ Error: {e}")
                else:
                    print("  ⚠️  No tenants found to test tenant-specific endpoints")
            else:
                print(f"  ❌ Could not fetch tenants: {tenant_response.status_code}")
                
        except Exception as e:
            print(f"  ❌ Error testing tenant-specific endpoints: {e}")

if __name__ == "__main__":
    test_all_tenant_endpoints()
    
    print("\n" + "=" * 50)
    print("🔧 If endpoints are still not working:")
    print("1. Restart Django server: docker-compose restart backend")
    print("2. Check Django logs: docker-compose logs backend")
    print("3. Verify middleware changes are applied")
    print("4. Check URL patterns in backend/tenants/urls.py")
    print("\n🌐 Expected working URLs:")
    print("   Tenant List: http://************:8000/api/tenants/")
    print("   Subcities: http://************:8000/api/tenants/subcities/")
    print("   Kebeles: http://************:8000/api/tenants/kebeles/")
    print("   Shared Countries: http://************:8000/api/shared/countries/")
    print("\n💡 All tenant management endpoints should return 200 OK with proper authentication")
