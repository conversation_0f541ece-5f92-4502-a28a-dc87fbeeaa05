version: '3.8'

services:
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: goid_db
      POSTGRES_USER: goid_user
      POSTGRES_PASSWORD: goid_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - goid_network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - goid_network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - CHOKIDAR_USEPOLLING=true
      - REACT_APP_API_URL=http://localhost:8000
    depends_on:
      - db
      - redis
    networks:
      - goid_network

volumes:
  postgres_data:

networks:
  goid_network:
    driver: bridge
