import django_filters
from .models import WorkflowLog


class WorkflowLogFilter(django_filters.FilterSet):
    """Filter for WorkflowLog model."""
    id_card = django_filters.NumberFilter(field_name='id_card__id')
    card_number = django_filters.CharFilter(field_name='id_card__card_number', lookup_expr='icontains')
    action = django_filters.ChoiceFilter(choices=WorkflowLog._meta.get_field('action').choices)
    from_status = django_filters.ChoiceFilter(choices=WorkflowLog._meta.get_field('from_status').choices)
    to_status = django_filters.ChoiceFilter(choices=WorkflowLog._meta.get_field('to_status').choices)
    performed_by = django_filters.NumberFilter(field_name='performed_by__id')
    performed_at_from = django_filters.DateTimeFilter(field_name='performed_at', lookup_expr='gte')
    performed_at_to = django_filters.DateTimeFilter(field_name='performed_at', lookup_expr='lte')
    
    class Meta:
        model = WorkflowLog
        fields = [
            'id_card', 'card_number', 'action', 'from_status', 'to_status',
            'performed_by', 'performed_at_from', 'performed_at_to'
        ]
