# GoID API Documentation

This document provides comprehensive documentation for the GoID API endpoints.

## Base URL

All API endpoints are relative to the base URL:

```
http://localhost:8000/api/
```

## Authentication

The API uses JWT (JSON Web Token) for authentication.

### Login

```
POST /auth/token/
```

Request body:
```json
{
  "email": "<EMAIL>",
  "password": "admin123"
}
```

Response:
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "id": 1,
  "email": "<EMAIL>",
  "username": "admin",
  "first_name": "",
  "last_name": "",
  "role": "superadmin",
  "tenant": {
    "id": 1,
    "name": "Public",
    "type": "city",
    "schema_name": "public"
  },
  "is_superuser": true
}
```

### Refresh Token

```
POST /auth/token/refresh/
```

Request body:
```json
{
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

Response:
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

## Tenants

### Get All Tenants

```
GET /tenants/
```

Response:
```json
{
  "count": 3,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "name": "Public",
      "type": "city",
      "parent": null,
      "parent_name": null,
      "schema_name": "public",
      "domains": [
        {
          "id": 1,
          "domain": "localhost",
          "is_primary": true
        }
      ],
      "created_on": "2023-11-15T12:00:00Z"
    },
    {
      "id": 2,
      "name": "Addis Ababa",
      "type": "city",
      "parent": null,
      "parent_name": null,
      "schema_name": "city_addis_ababa",
      "domains": [
        {
          "id": 2,
          "domain": "addis.goid.local",
          "is_primary": true
        }
      ],
      "created_on": "2023-11-15T12:00:00Z"
    },
    {
      "id": 3,
      "name": "Bole",
      "type": "subcity",
      "parent": 2,
      "parent_name": "Addis Ababa",
      "schema_name": "subcity_bole",
      "domains": [
        {
          "id": 3,
          "domain": "bole.goid.local",
          "is_primary": true
        }
      ],
      "created_on": "2023-11-15T12:00:00Z"
    }
  ]
}
```

### Get Tenant by ID

```
GET /tenants/{id}/
```

Response:
```json
{
  "id": 2,
  "name": "Addis Ababa",
  "type": "city",
  "parent": null,
  "parent_name": null,
  "schema_name": "city_addis_ababa",
  "domains": [
    {
      "id": 2,
      "domain": "addis.goid.local",
      "is_primary": true
    }
  ],
  "created_on": "2023-11-15T12:00:00Z"
}
```

### Create Tenant

```
POST /tenants/
```

Request body:
```json
{
  "name": "New City",
  "type": "city"
}
```

Response:
```json
{
  "id": 4,
  "name": "New City",
  "type": "city",
  "parent": null,
  "parent_name": null,
  "schema_name": "city_new_city",
  "domains": [
    {
      "id": 4,
      "domain": "city_new_city.goid.local",
      "is_primary": true
    }
  ],
  "created_on": "2023-11-15T12:00:00Z"
}
```

### Get Tenant Hierarchy

```
GET /tenants/hierarchy/
```

Response:
```json
[
  {
    "id": 1,
    "name": "Public",
    "type": "city",
    "schema_name": "public",
    "children": []
  },
  {
    "id": 2,
    "name": "Addis Ababa",
    "type": "city",
    "schema_name": "city_addis_ababa",
    "children": [
      {
        "id": 3,
        "name": "Bole",
        "type": "subcity",
        "schema_name": "subcity_bole",
        "children": [
          {
            "id": 4,
            "name": "Bole 01",
            "type": "kebele",
            "schema_name": "kebele_bole_01",
            "children": []
          }
        ]
      }
    ]
  }
]
```

### Switch Tenant

```
POST /tenants/switch/
```

Request body:
```json
{
  "tenant_id": 2
}
```

Response:
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "tenant": {
    "id": 2,
    "name": "Addis Ababa",
    "type": "city",
    "schema_name": "city_addis_ababa"
  }
}
```

## Users

### Get All Users

```
GET /auth/users/
```

Response:
```json
{
  "count": 5,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "email": "<EMAIL>",
      "username": "admin",
      "first_name": "",
      "last_name": "",
      "role": "superadmin",
      "tenant": null,
      "tenant_name": null,
      "phone_number": null,
      "profile_picture": null,
      "is_active": true,
      "date_joined": "2023-11-15T12:00:00Z"
    },
    {
      "id": 2,
      "email": "<EMAIL>",
      "username": "city_admin",
      "first_name": "",
      "last_name": "",
      "role": "city_admin",
      "tenant": 2,
      "tenant_name": "Addis Ababa",
      "phone_number": null,
      "profile_picture": null,
      "is_active": true,
      "date_joined": "2023-11-15T12:00:00Z"
    }
  ]
}
```

### Get User by ID

```
GET /auth/users/{id}/
```

Response:
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "username": "admin",
  "first_name": "",
  "last_name": "",
  "role": "superadmin",
  "tenant": null,
  "tenant_name": null,
  "phone_number": null,
  "profile_picture": null,
  "is_active": true,
  "date_joined": "2023-11-15T12:00:00Z"
}
```

### Create User

```
POST /auth/users/
```

Request body:
```json
{
  "email": "<EMAIL>",
  "username": "new_user",
  "password": "password123",
  "password2": "password123",
  "first_name": "New",
  "last_name": "User",
  "role": "clerk",
  "tenant": 4
}
```

Response:
```json
{
  "id": 6,
  "email": "<EMAIL>",
  "username": "new_user",
  "first_name": "New",
  "last_name": "User",
  "role": "clerk",
  "tenant": 4,
  "tenant_name": "Bole 01",
  "phone_number": null,
  "profile_picture": null,
  "is_active": true,
  "date_joined": "2023-11-15T12:00:00Z"
}
```

### Get Current User

```
GET /auth/users/me/
```

Response:
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "username": "admin",
  "first_name": "",
  "last_name": "",
  "role": "superadmin",
  "tenant": null,
  "tenant_name": null,
  "phone_number": null,
  "profile_picture": null,
  "is_active": true,
  "date_joined": "2023-11-15T12:00:00Z"
}
```

### Change Password

```
POST /auth/users/{id}/change_password/
```

Request body:
```json
{
  "old_password": "admin123",
  "new_password": "newpassword123",
  "new_password2": "newpassword123"
}
```

Response:
```json
{
  "message": "Password updated successfully"
}
```

## Citizens

### Get All Citizens

```
GET /citizens/
```

Response:
```json
{
  "count": 5,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "first_name": "Abebe",
      "middle_name": "Kebede",
      "last_name": "Tadesse",
      "full_name": "Abebe Kebede Tadesse",
      "date_of_birth": "1985-05-15",
      "age": 38,
      "gender": "male",
      "phone_number": "+************",
      "has_id_card": true
    },
    {
      "id": 2,
      "first_name": "Sara",
      "middle_name": "Mohammed",
      "last_name": "Ali",
      "full_name": "Sara Mohammed Ali",
      "date_of_birth": "1990-08-22",
      "age": 33,
      "gender": "female",
      "phone_number": "+251922345678",
      "has_id_card": true
    }
  ]
}
```

### Get Citizen by ID

```
GET /citizens/{id}/
```

Response:
```json
{
  "id": 1,
  "first_name": "Abebe",
  "middle_name": "Kebede",
  "last_name": "Tadesse",
  "full_name": "Abebe Kebede Tadesse",
  "date_of_birth": "1985-05-15",
  "age": 38,
  "gender": "male",
  "photo": null,
  "phone_number": "+************",
  "email": "<EMAIL>",
  "house_number": null,
  "street": null,
  "nationality": "Ethiopian",
  "marital_status": "married",
  "occupation": "Teacher",
  "blood_type": "A+",
  "emergency_contact_name": null,
  "emergency_contact_phone": null,
  "emergency_contact_relation": null,
  "created_by": 5,
  "created_by_username": "clerk",
  "created_at": "2023-11-15T12:00:00Z",
  "updated_at": "2023-11-15T12:00:00Z",
  "has_id_card": true
}
```

### Create Citizen

```
POST /citizens/
```

Request body:
```json
{
  "first_name": "New",
  "middle_name": "Test",
  "last_name": "Citizen",
  "date_of_birth": "1995-01-01",
  "gender": "male",
  "phone_number": "+251966778899",
  "email": "<EMAIL>",
  "nationality": "Ethiopian",
  "marital_status": "single",
  "occupation": "Student"
}
```

Response:
```json
{
  "id": 6,
  "first_name": "New",
  "middle_name": "Test",
  "last_name": "Citizen",
  "full_name": "New Test Citizen",
  "date_of_birth": "1995-01-01",
  "age": 28,
  "gender": "male",
  "photo": null,
  "phone_number": "+251966778899",
  "email": "<EMAIL>",
  "house_number": null,
  "street": null,
  "nationality": "Ethiopian",
  "marital_status": "single",
  "occupation": "Student",
  "blood_type": "unknown",
  "emergency_contact_name": null,
  "emergency_contact_phone": null,
  "emergency_contact_relation": null,
  "created_by": 5,
  "created_by_username": "clerk",
  "created_at": "2023-11-15T12:00:00Z",
  "updated_at": "2023-11-15T12:00:00Z",
  "has_id_card": false
}
```

### Get Citizen ID Cards

```
GET /citizens/{id}/id_cards/
```

Response:
```json
[
  {
    "id": 1,
    "card_number": "ETH-KEBELE_BOLE_01-123456",
    "citizen": 1,
    "citizen_name": "Abebe Kebede Tadesse",
    "issue_date": "2023-11-15",
    "expiry_date": "2028-11-14",
    "status": "approved",
    "created_at": "2023-11-15T12:00:00Z"
  }
]
```

### Get Citizen Statistics

```
GET /citizens/statistics/
```

Response:
```json
{
  "total_citizens": 5,
  "citizens_with_id_cards": 3,
  "citizens_without_id_cards": 2,
  "gender_distribution": {
    "male": 3,
    "female": 2
  },
  "age_groups": {
    "under_18": 0,
    "age_18_to_30": 1,
    "age_30_to_50": 4,
    "age_50_plus": 0
  }
}
```

## ID Cards

### Get All ID Cards

```
GET /idcards/
```

Response:
```json
{
  "count": 3,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "card_number": "ETH-KEBELE_BOLE_01-123456",
      "citizen": 1,
      "citizen_name": "Abebe Kebede Tadesse",
      "issue_date": "2023-11-15",
      "expiry_date": "2028-11-14",
      "status": "approved",
      "created_at": "2023-11-15T12:00:00Z"
    },
    {
      "id": 2,
      "card_number": "ETH-KEBELE_BOLE_01-234567",
      "citizen": 2,
      "citizen_name": "Sara Mohammed Ali",
      "issue_date": null,
      "expiry_date": null,
      "status": "pending_approval",
      "created_at": "2023-11-15T12:00:00Z"
    }
  ]
}
```

### Get ID Card by ID

```
GET /idcards/{id}/
```

Response:
```json
{
  "id": 1,
  "card_number": "ETH-KEBELE_BOLE_01-123456",
  "citizen": 1,
  "citizen_name": "Abebe Kebede Tadesse",
  "citizen_details": {
    "id": 1,
    "first_name": "Abebe",
    "middle_name": "Kebede",
    "last_name": "Tadesse",
    "full_name": "Abebe Kebede Tadesse",
    "date_of_birth": "1985-05-15",
    "age": 38,
    "gender": "male",
    "photo": null,
    "phone_number": "+************",
    "email": "<EMAIL>",
    "house_number": null,
    "street": null,
    "nationality": "Ethiopian",
    "marital_status": "married",
    "occupation": "Teacher",
    "blood_type": "A+",
    "emergency_contact_name": null,
    "emergency_contact_phone": null,
    "emergency_contact_relation": null,
    "created_by": 5,
    "created_by_username": "clerk",
    "created_at": "2023-11-15T12:00:00Z",
    "updated_at": "2023-11-15T12:00:00Z",
    "has_id_card": true
  },
  "issue_date": "2023-11-15",
  "expiry_date": "2028-11-14",
  "status": "approved",
  "qr_code": "/media/id_card_qr_codes/qr_ETH-KEBELE_BOLE_01-123456.png",
  "uuid": "550e8400-e29b-41d4-a716-************",
  "created_by": 5,
  "created_by_username": "clerk",
  "approved_by": 4,
  "approved_by_username": "kebele_admin",
  "issued_by": null,
  "issued_by_username": null,
  "created_at": "2023-11-15T12:00:00Z",
  "updated_at": "2023-11-15T12:00:00Z",
  "pdf_file": "/media/id_card_pdfs/id_card_ETH-KEBELE_BOLE_01-123456.pdf"
}
```

### Create ID Card

```
POST /idcards/
```

Request body:
```json
{
  "citizen": 3,
  "status": "draft"
}
```

Response:
```json
{
  "id": 4,
  "card_number": "ETH-KEBELE_BOLE_01-345678",
  "citizen": 3,
  "citizen_name": "Daniel Tesfaye Bekele",
  "citizen_details": {
    "id": 3,
    "first_name": "Daniel",
    "middle_name": "Tesfaye",
    "last_name": "Bekele",
    "full_name": "Daniel Tesfaye Bekele",
    "date_of_birth": "1978-12-10",
    "age": 44,
    "gender": "male",
    "photo": null,
    "phone_number": "+************",
    "email": "<EMAIL>",
    "house_number": null,
    "street": null,
    "nationality": "Ethiopian",
    "marital_status": "divorced",
    "occupation": "Engineer",
    "blood_type": "O-",
    "emergency_contact_name": null,
    "emergency_contact_phone": null,
    "emergency_contact_relation": null,
    "created_by": 5,
    "created_by_username": "clerk",
    "created_at": "2023-11-15T12:00:00Z",
    "updated_at": "2023-11-15T12:00:00Z",
    "has_id_card": true
  },
  "issue_date": null,
  "expiry_date": null,
  "status": "draft",
  "qr_code": null,
  "uuid": "660e8400-e29b-41d4-a716-************",
  "created_by": 5,
  "created_by_username": "clerk",
  "approved_by": null,
  "approved_by_username": null,
  "issued_by": null,
  "issued_by_username": null,
  "created_at": "2023-11-15T12:00:00Z",
  "updated_at": "2023-11-15T12:00:00Z",
  "pdf_file": null
}
```

### Update ID Card Status

```
POST /idcards/{id}/update_status/
```

Request body:
```json
{
  "status": "pending_approval",
  "comment": "Submitting for approval"
}
```

Response:
```json
{
  "id": 4,
  "card_number": "ETH-KEBELE_BOLE_01-345678",
  "citizen": 3,
  "citizen_name": "Daniel Tesfaye Bekele",
  "citizen_details": {
    "id": 3,
    "first_name": "Daniel",
    "middle_name": "Tesfaye",
    "last_name": "Bekele",
    "full_name": "Daniel Tesfaye Bekele",
    "date_of_birth": "1978-12-10",
    "age": 44,
    "gender": "male",
    "photo": null,
    "phone_number": "+************",
    "email": "<EMAIL>",
    "house_number": null,
    "street": null,
    "nationality": "Ethiopian",
    "marital_status": "divorced",
    "occupation": "Engineer",
    "blood_type": "O-",
    "emergency_contact_name": null,
    "emergency_contact_phone": null,
    "emergency_contact_relation": null,
    "created_by": 5,
    "created_by_username": "clerk",
    "created_at": "2023-11-15T12:00:00Z",
    "updated_at": "2023-11-15T12:00:00Z",
    "has_id_card": true
  },
  "issue_date": null,
  "expiry_date": null,
  "status": "pending_approval",
  "qr_code": null,
  "uuid": "660e8400-e29b-41d4-a716-************",
  "created_by": 5,
  "created_by_username": "clerk",
  "approved_by": null,
  "approved_by_username": null,
  "issued_by": null,
  "issued_by_username": null,
  "created_at": "2023-11-15T12:00:00Z",
  "updated_at": "2023-11-15T12:00:00Z",
  "pdf_file": null
}
```

### Generate PDF

```
POST /idcards/{id}/generate_pdf/
```

Response:
```json
{
  "id": 1,
  "card_number": "ETH-KEBELE_BOLE_01-123456",
  "citizen": 1,
  "citizen_name": "Abebe Kebede Tadesse",
  "citizen_details": {
    "id": 1,
    "first_name": "Abebe",
    "middle_name": "Kebede",
    "last_name": "Tadesse",
    "full_name": "Abebe Kebede Tadesse",
    "date_of_birth": "1985-05-15",
    "age": 38,
    "gender": "male",
    "photo": null,
    "phone_number": "+************",
    "email": "<EMAIL>",
    "house_number": null,
    "street": null,
    "nationality": "Ethiopian",
    "marital_status": "married",
    "occupation": "Teacher",
    "blood_type": "A+",
    "emergency_contact_name": null,
    "emergency_contact_phone": null,
    "emergency_contact_relation": null,
    "created_by": 5,
    "created_by_username": "clerk",
    "created_at": "2023-11-15T12:00:00Z",
    "updated_at": "2023-11-15T12:00:00Z",
    "has_id_card": true
  },
  "issue_date": "2023-11-15",
  "expiry_date": "2028-11-14",
  "status": "approved",
  "qr_code": "/media/id_card_qr_codes/qr_ETH-KEBELE_BOLE_01-123456.png",
  "uuid": "550e8400-e29b-41d4-a716-************",
  "created_by": 5,
  "created_by_username": "clerk",
  "approved_by": 4,
  "approved_by_username": "kebele_admin",
  "issued_by": null,
  "issued_by_username": null,
  "created_at": "2023-11-15T12:00:00Z",
  "updated_at": "2023-11-15T12:00:00Z",
  "pdf_file": "/media/id_card_pdfs/id_card_ETH-KEBELE_BOLE_01-123456.pdf"
}
```

### Download PDF

```
GET /idcards/{id}/download_pdf/
```

Response: PDF file download

### Get ID Card Workflow Logs

```
GET /idcards/{id}/workflow_logs/
```

Response:
```json
[
  {
    "id": 1,
    "id_card": 1,
    "action": "submit",
    "from_status": "draft",
    "to_status": "pending_approval",
    "comment": "Submitted for approval",
    "performed_by": 5,
    "performed_by_username": "clerk",
    "performed_at": "2023-11-13T12:00:00Z"
  },
  {
    "id": 2,
    "id_card": 1,
    "action": "approve",
    "from_status": "pending_approval",
    "to_status": "approved",
    "comment": "Approved",
    "performed_by": 4,
    "performed_by_username": "kebele_admin",
    "performed_at": "2023-11-14T12:00:00Z"
  }
]
```

### Get ID Card Statistics

```
GET /idcards/statistics/
```

Response:
```json
{
  "total_id_cards": 3,
  "status_distribution": {
    "draft": 1,
    "pending_approval": 1,
    "approved": 1,
    "rejected": 0,
    "printed": 0,
    "issued": 0,
    "expired": 0,
    "revoked": 0
  },
  "monthly_statistics": [
    {
      "month": "November 2023",
      "count": 3
    }
  ]
}
```

## Workflows

### Get All Workflow Logs

```
GET /workflows/
```

Response:
```json
{
  "count": 3,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "id_card": 1,
      "action": "submit",
      "from_status": "draft",
      "to_status": "pending_approval",
      "comment": "Submitted for approval",
      "performed_by": 5,
      "performed_by_username": "clerk",
      "performed_at": "2023-11-13T12:00:00Z"
    },
    {
      "id": 2,
      "id_card": 1,
      "action": "approve",
      "from_status": "pending_approval",
      "to_status": "approved",
      "comment": "Approved",
      "performed_by": 4,
      "performed_by_username": "kebele_admin",
      "performed_at": "2023-11-14T12:00:00Z"
    },
    {
      "id": 3,
      "id_card": 2,
      "action": "submit",
      "from_status": "draft",
      "to_status": "pending_approval",
      "comment": "Submitted for approval",
      "performed_by": 5,
      "performed_by_username": "clerk",
      "performed_at": "2023-11-14T12:00:00Z"
    }
  ]
}
```

### Get Workflow Statistics

```
GET /workflows/statistics/
```

Response:
```json
{
  "action_distribution": [
    {
      "action": "submit",
      "count": 2
    },
    {
      "action": "approve",
      "count": 1
    }
  ],
  "top_users": [
    {
      "performed_by__username": "clerk",
      "count": 2
    },
    {
      "performed_by__username": "kebele_admin",
      "count": 1
    }
  ],
  "status_transitions": [
    {
      "from_status": "draft",
      "to_status": "pending_approval",
      "count": 2
    },
    {
      "from_status": "pending_approval",
      "to_status": "approved",
      "count": 1
    }
  ]
}
```
