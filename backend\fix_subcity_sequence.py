#!/usr/bin/env python3
"""
Fix SubCity sequence to prevent IntegrityError.
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
sys.path.append('/app')
django.setup()

from django.db import connection

def fix_subcity_sequence():
    """Fix SubCity sequence to match existing data."""
    cursor = connection.cursor()
    
    print('=== Fixing SubCity Sequence ===')
    
    # Check current state
    cursor.execute('SELECT last_value, is_called FROM tenants_subcity_id_seq;')
    current_seq = cursor.fetchone()
    print(f'Current sequence: last_value={current_seq[0]}, is_called={current_seq[1]}')
    
    cursor.execute('SELECT MAX(id) FROM tenants_subcity;')
    max_id = cursor.fetchone()[0]
    print(f'Max SubCity ID in table: {max_id}')
    
    # Set sequence to max_id + 1
    next_id = (max_id or 0) + 1
    cursor.execute(f"SELECT setval('tenants_subcity_id_seq', {next_id});")
    print(f'✅ Set sequence to: {next_id}')
    
    # Verify fix
    cursor.execute('SELECT last_value, is_called FROM tenants_subcity_id_seq;')
    new_seq = cursor.fetchone()
    print(f'New sequence: last_value={new_seq[0]}, is_called={new_seq[1]}')
    
    print('\n🎉 SubCity sequence fixed! Ready for new registrations.')
    return True

if __name__ == "__main__":
    fix_subcity_sequence()
