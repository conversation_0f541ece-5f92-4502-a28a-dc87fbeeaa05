#!/bin/bash

# Network Configuration Script for GoID
# This script helps configure the application for different network environments

echo "🌐 GoID Network Configuration Script"
echo "===================================="

# Get the current IP address
CURRENT_IP=$(hostname -I | awk '{print $1}')
echo "📍 Detected IP address: $CURRENT_IP"

# Ask user for the IP they want to use
echo ""
echo "Please choose your configuration:"
echo "1. Use localhost (127.0.0.1) - for local development"
echo "2. Use detected IP ($CURRENT_IP) - for network access"
echo "3. Use custom IP (************) - for your specific setup"
echo "4. Enter custom IP address"

read -p "Enter your choice (1-4): " choice

case $choice in
    1)
        API_URL="http://localhost:8000"
        FRONTEND_URL="http://localhost:3000"
        ;;
    2)
        API_URL="http://$CURRENT_IP:8000"
        FRONTEND_URL="http://$CURRENT_IP:3000"
        ;;
    3)
        API_URL="http://************:8000"
        FRONTEND_URL="http://************:3000"
        ;;
    4)
        read -p "Enter your custom IP address: " CUSTOM_IP
        API_URL="http://$CUSTOM_IP:8000"
        FRONTEND_URL="http://$CUSTOM_IP:3000"
        ;;
    *)
        echo "❌ Invalid choice. Using default localhost configuration."
        API_URL="http://localhost:8000"
        FRONTEND_URL="http://localhost:3000"
        ;;
esac

echo ""
echo "🔧 Configuring with:"
echo "   API URL: $API_URL"
echo "   Frontend URL: $FRONTEND_URL"

# Update frontend environment file
echo "📝 Updating frontend/.env.development..."
cat > frontend/.env.development << EOF
# Development environment configuration
VITE_API_URL=$API_URL
NODE_ENV=development
EOF

# Update Docker Compose
echo "📝 Updating docker-compose.yml..."
sed -i "s|VITE_API_URL=.*|VITE_API_URL=$API_URL|g" docker-compose.yml

# Update Dockerfile
echo "📝 Updating frontend/Dockerfile..."
sed -i "s|ENV VITE_API_URL=.*|ENV VITE_API_URL=$API_URL|g" frontend/Dockerfile

# Update Django CORS settings
echo "📝 Updating Django CORS settings..."
CORS_ORIGIN="\"$FRONTEND_URL\""

# Create a temporary Python script to update CORS settings
cat > update_cors.py << EOF
import re

# Read the settings file
with open('backend/goid/settings.py', 'r') as f:
    content = f.read()

# Update CORS_ALLOWED_ORIGINS
cors_pattern = r'CORS_ALLOWED_ORIGINS = \[(.*?)\]'
new_cors = f'''CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "$FRONTEND_URL",
    "${API_URL%:8000}",  # Backend host without port
]'''

content = re.sub(cors_pattern, new_cors, content, flags=re.DOTALL)

# Write back to file
with open('backend/goid/settings.py', 'w') as f:
    f.write(content)

print("✅ CORS settings updated")
EOF

python update_cors.py
rm update_cors.py

echo ""
echo "✅ Configuration completed!"
echo ""
echo "🚀 Next steps:"
echo "1. Restart your Docker containers:"
echo "   docker-compose down"
echo "   docker-compose up --build"
echo ""
echo "2. Or if running manually:"
echo "   - Restart Django server: cd backend && python manage.py runserver 0.0.0.0:8000"
echo "   - Restart frontend: cd frontend && npm run dev"
echo ""
echo "3. Access the application at: $FRONTEND_URL"
echo "4. API will be available at: $API_URL"
echo ""
echo "🔐 Login troubleshooting:"
echo "- Make sure you're using the correct credentials"
echo "- Check browser console for any CORS errors"
echo "- Verify the backend is accessible at $API_URL/admin/"
