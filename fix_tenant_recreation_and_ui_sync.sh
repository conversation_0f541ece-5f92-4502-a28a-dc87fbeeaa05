#!/bin/bash

# Fix Tenant Recreation and UI Sync Issues Script
# This script fixes both the auto-recreating tenants and UI data sync issues

echo "🔧 Fixing Tenant Recreation and UI Sync Issues"
echo "=============================================="

echo "📍 Issues Identified:"
echo "1. Docker Compose runs 'init_data' command on every backend start"
echo "2. Frontend React state not syncing with API response"
echo "3. Deleted tenants recreated on container rebuild"
echo ""

# Step 1: Fix Docker Compose to not run init_data every time
echo "📍 Step 1: Fixing Docker Compose configuration..."

# Create a new docker-compose.yml without the init_data command
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  db:
    image: postgres:14
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_DB=goid
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    ports:
      - "5432:5432"

  backend:
    build: ./backend
    command: >
      sh -c "python manage.py makemigrations users tenants citizens idcards workflows &&
             python manage.py migrate_schemas --shared &&
             python manage.py migrate_schemas &&
             python manage.py collectstatic --noinput &&
             python manage.py runserver 0.0.0.0:8000"
    volumes:
      - ./backend:/app
    ports:
      - "8000:8000"
    depends_on:
      - db
    environment:
      - DATABASE_URL=************************************/goid
      - DEBUG=1

  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://10.139.8.141:8000
    depends_on:
      - backend

  pgadmin:
    image: dpage/pgadmin4:latest
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin
      - PGADMIN_CONFIG_SERVER_MODE=False
    ports:
      - "5050:80"
    depends_on:
      - db
    volumes:
      - pgadmin_data:/var/lib/pgadmin

volumes:
  postgres_data:
  pgadmin_data:
EOF

echo "✅ Docker Compose configuration updated (removed auto init_data)"

# Step 2: Delete the problematic tenants from database
echo ""
echo "📍 Step 2: Deleting problematic tenants from database..."

docker-compose exec -T backend python manage.py shell << 'EOF'
from tenants.models import Tenant, Domain

print("=== DELETING PROBLEMATIC TENANTS ===")

# List of tenant names to delete
tenant_names_to_delete = ['Addis Ababa', 'Bole', 'Bole 01']

for tenant_name in tenant_names_to_delete:
    try:
        tenant = Tenant.objects.get(name=tenant_name)
        print(f"Found tenant: {tenant.name} (ID: {tenant.id}, Schema: {tenant.schema_name})")
        
        # Delete associated domains first
        domains = Domain.objects.filter(tenant=tenant)
        for domain in domains:
            print(f"  Deleting domain: {domain.domain}")
            domain.delete()
        
        # Delete the tenant (this should also drop the schema)
        print(f"  Deleting tenant: {tenant.name}")
        tenant.delete()
        print(f"✅ Deleted tenant: {tenant_name}")
        
    except Tenant.DoesNotExist:
        print(f"⚠️  Tenant '{tenant_name}' not found (already deleted)")
    except Exception as e:
        print(f"❌ Error deleting tenant '{tenant_name}': {e}")

print("\n=== REMAINING TENANTS ===")
remaining_tenants = Tenant.objects.all()
for tenant in remaining_tenants:
    print(f"- {tenant.name} (ID: {tenant.id}, Type: {tenant.type}, Schema: {tenant.schema_name})")

print(f"\nTotal remaining tenants: {remaining_tenants.count()}")
EOF

# Step 3: Restart services with new configuration
echo ""
echo "📍 Step 3: Restarting services with new configuration..."

echo "Stopping all services..."
docker-compose down

echo "Starting database..."
docker-compose up -d db
sleep 15

echo "Starting backend (without auto init_data)..."
docker-compose up -d backend
sleep 30

echo "Starting frontend..."
docker-compose up -d frontend
sleep 20

# Step 4: Ensure superuser exists (manual one-time setup)
echo ""
echo "📍 Step 4: Ensuring superuser exists (one-time setup)..."

docker-compose exec -T backend python manage.py shell << 'EOF'
from django.contrib.auth import get_user_model

User = get_user_model()

# Ensure superuser exists
try:
    admin_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'username': 'admin',
            'first_name': 'Super',
            'last_name': 'Admin',
            'is_superuser': True,
            'is_staff': True,
            'is_active': True
        }
    )
    
    if created:
        admin_user.set_password('admin123')
        admin_user.save()
        print("✅ Superuser created")
    else:
        # Ensure existing user has correct properties
        admin_user.is_superuser = True
        admin_user.is_staff = True
        admin_user.is_active = True
        admin_user.set_password('admin123')
        admin_user.save()
        print("✅ Superuser updated")
        
    print(f"Superuser: {admin_user.email}")
    print(f"Is superuser: {admin_user.is_superuser}")
    
except Exception as e:
    print(f"❌ Error with superuser: {e}")
EOF

# Step 5: Test the API to verify current state
echo ""
echo "📍 Step 5: Testing API to verify current state..."

echo "Getting authentication token..."
AUTH_RESPONSE=$(curl -s -X POST http://10.139.8.141:8000/api/auth/token/ \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}')

if echo "$AUTH_RESPONSE" | grep -q "access"; then
    echo "✅ Authentication successful"
    
    # Extract token
    TOKEN=$(echo "$AUTH_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin)['access'])" 2>/dev/null)
    
    if [ ! -z "$TOKEN" ]; then
        echo ""
        echo "Testing tenant endpoints..."
        
        # Test all tenants
        echo "1. All tenants:"
        ALL_TENANTS_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
          http://10.139.8.141:8000/api/tenants/)
        
        echo "$ALL_TENANTS_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, list):
        tenants = data
    elif isinstance(data, dict) and 'results' in data:
        tenants = data.get('results', [])
    else:
        tenants = []
    
    print(f'Total tenants: {len(tenants)}')
    for tenant in tenants:
        print(f'  - {tenant.get(\"name\")} (ID: {tenant.get(\"id\")}, Type: {tenant.get(\"type\")})')
except Exception as e:
    print(f'Error parsing response: {e}')
"
        
        # Test kebele tenants specifically
        echo ""
        echo "2. Kebele tenants:"
        KEBELE_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
          "http://10.139.8.141:8000/api/tenants/?type=kebele")
        
        echo "$KEBELE_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, list):
        kebeles = data
    elif isinstance(data, dict) and 'results' in data:
        kebeles = data.get('results', [])
    else:
        kebeles = []
    
    print(f'Total kebeles: {len(kebeles)}')
    for kebele in kebeles:
        print(f'  - {kebele.get(\"name\")} (ID: {kebele.get(\"id\")}, Parent: {kebele.get(\"parent\")})')
except Exception as e:
    print(f'Error parsing response: {e}')
"
        
    else
        echo "❌ Could not extract token"
    fi
else
    echo "❌ Authentication failed"
    echo "Response: $AUTH_RESPONSE"
fi

# Step 6: Instructions for manual init_data if needed
echo ""
echo "📍 Step 6: Manual data initialization (if needed)..."

echo ""
echo "✅ Tenant recreation and UI sync fix completed!"
echo ""
echo "🔧 What was fixed:"
echo "1. ✅ Removed auto-running init_data from Docker Compose"
echo "2. ✅ Deleted problematic tenants from database"
echo "3. ✅ Services restarted with clean configuration"
echo "4. ✅ Superuser ensured to exist"
echo ""
echo "🧪 Manual testing steps:"
echo "1. Clear browser cache: Ctrl+Shift+R"
echo "2. Open: http://10.139.8.141:3000/users/kebele-management"
echo "3. Should see only the actual kebeles (matching API response)"
echo "4. Rebuild Docker images: docker-compose build"
echo "5. Restart: docker-compose up -d"
echo "6. Verify deleted tenants don't come back"
echo ""
echo "🔍 Expected results:"
echo "- ✅ UI shows same number of kebeles as API response"
echo "- ✅ Deleted tenants stay deleted after Docker rebuild"
echo "- ✅ No more 'Addis Ababa', 'Bole', 'Bole 01' auto-creation"
echo "- ✅ Frontend state syncs with API data"
echo ""
echo "💡 If you need sample data:"
echo "Run manually: docker-compose exec backend python manage.py init_data"
echo "This will create sample tenants only if they don't exist"
echo ""
echo "🚨 Important notes:"
echo "- init_data command now only runs when manually executed"
echo "- Existing tenants and data are preserved"
echo "- Docker rebuilds won't recreate deleted tenants"
echo "- Frontend should now show accurate data from API"
