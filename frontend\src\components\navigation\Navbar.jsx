import { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  AppBar,
  Box,
  Toolbar,
  IconButton,
  Typography,
  Menu,
  Container,
  Avatar,
  Button,
  Tooltip,
  MenuItem,
  Divider,
  ListItemIcon,
  useMediaQuery,
  useTheme,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Person as PersonIcon,
  CreditCard as CardIcon,
  Business as BusinessIcon,
  Brightness4 as DarkModeIcon,
  Brightness7 as LightModeIcon,
  AccountCircle,
  Settings,
  Logout,
  Assessment as ReportIcon,
  Pending as PendingIcon,
  LocalPrintshop as PrintIcon,
  MoreVert as MoreIcon,
  SupervisorAccount as SupervisorIcon,
  SwapHoriz as SwapHorizIcon,
  Palette as PaletteIcon,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme as useAppTheme } from '../../contexts/ThemeContext';
import { usePermissions } from '../../hooks/usePermissions';
import { useLocalization } from '../../contexts/LocalizationContext';

const Navbar = () => {
  const { user, logout } = useAuth();
  const { mode, toggleTheme } = useAppTheme();
  const { hasPermission } = usePermissions();
  const { t, currentLanguage, changeLanguage } = useLocalization();
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Check if this is a public page (no authentication required)
  const isPublicPage = location.pathname === '/services' ||
                       location.pathname.startsWith('/services/') ||
                       location.pathname.startsWith('/idcards/services') ||
                       !user;

  const [mobileOpen, setMobileOpen] = useState(false);
  const [anchorElUser, setAnchorElUser] = useState(null);
  const [anchorElNav, setAnchorElNav] = useState(null);
  const [anchorElMore, setAnchorElMore] = useState(null);

  // Build menu items based on user permissions (only for authenticated pages)
  const menuItems = [];
  const moreMenuItems = []; // Initialize moreMenuItems outside the conditional

  // Only show navigation menu items if not on a public page
  if (!isPublicPage) {
    // Dashboard - always show if user has any basic permissions
    if (hasPermission('view_dashboard')) {
      menuItems.push({ text: t('dashboard', 'Dashboard'), icon: <DashboardIcon />, path: '/dashboard' });
    }

    // Citizens - show appropriate view based on hierarchical permissions
    if (hasPermission('view_citizens')) {
      if (hasPermission('view_cross_subcity')) {
        // City admins get citizen dictionary instead of citizen navigation
        menuItems.push({
          text: t('citizen_dictionary', 'Citizen Dictionary'),
          icon: <PersonIcon />,
          path: '/citizens/citizen-book'
        });
      } else if (hasPermission('view_cross_kebele')) {
        // Subcity admins get cross-kebele view
        menuItems.push({
          text: t('citizens', 'Citizens'),
          icon: <PersonIcon />,
          path: '/citizens/all-kebeles'
        });
      } else {
        // Regular users get kebele-specific view
        menuItems.push({ text: t('citizens', 'Citizens'), icon: <PersonIcon />, path: '/citizens' });
      }
    }

    // ID Cards - show appropriate view based on hierarchical permissions
    if (hasPermission('view_idcards')) {
      if (hasPermission('view_cross_kebele') || hasPermission('view_cross_subcity')) {
        // Subcity/City admins get cross-kebele view
        menuItems.push({
          text: t('id_cards', 'ID Cards'),
          icon: <CardIcon />,
          path: '/idcards/all-kebeles'
        });
      } else {
        // Regular users get kebele-specific view
        menuItems.push({ text: t('id_cards', 'ID Cards'), icon: <CardIcon />, path: '/idcards' });
      }
    }

    // Printing Queue - for users who can print ID cards
    if (hasPermission('print_idcards')) {
      menuItems.push({
        text: t('print_queue', 'Printing Queue'),
        icon: <PrintIcon />,
        path: '/idcards/printing-queue'
      });
    }

    // Service Requests - for users who can approve ID cards
    if (hasPermission('approve_idcards')) {
      menuItems.push({
        text: t('service_requests', 'Service Requests'),
        icon: <PendingIcon />,
        path: '/idcards/service-requests'
      });
    }

    // Transfers - only for kebele users (leaders and clerks)
    if (user?.tenant?.type === 'kebele' && (user?.role === 'kebele_leader' || user?.role === 'clerk')) {
      menuItems.push({
        text: t('transfer', 'Transfers'),
        icon: <SwapHorizIcon />,
        path: '/transfers'
      });
    }

    // Clearances - only for kebele users (leaders and clerks)
    if (user?.tenant?.type === 'kebele' && (user?.role === 'kebele_leader' || user?.role === 'clerk')) {
      menuItems.push({
        text: t('clearance', 'Clearances'),
        icon: <SwapHorizIcon />,
        path: '/clearances'
      });
    }

    // Reports - for users who can view reports
    if (hasPermission('view_reports')) {
      menuItems.push({ text: t('reports', 'Reports'), icon: <ReportIcon />, path: '/reports' });
    }

    // Users - show appropriate user management based on hierarchical permissions
    if (hasPermission('manage_users')) {
      if (hasPermission('view_tenants')) {
        // Super admins get full user management
        menuItems.push({ text: t('system_users', 'Users'), icon: <PeopleIcon />, path: '/users' });
      } else if (hasPermission('view_cross_subcity')) {
        // City admins get city-wide user management
        menuItems.push({ text: t('system_users', 'Users'), icon: <PeopleIcon />, path: '/users/city-management' });
      } else {
        // Subcity admins get kebele user management
        menuItems.push({
          text: t('kebele_users', 'Users'),
          icon: <SupervisorIcon />,
          path: '/users/kebele-management'
        });
      }
    }

    // Tenants - for users who can view tenants
    if (hasPermission('view_tenants')) {
      menuItems.push({ text: t('tenants', 'Tenants'), icon: <BusinessIcon />, path: '/tenants' });
    }

    // System Settings - ONLY for public tenant superadmin (separate condition)
    if ((user?.role === 'superadmin' || user?.is_superuser) && (!user?.tenant || user?.tenant?.type === 'public')) {
      menuItems.push({ text: t('system_settings', 'System Settings'), icon: <PaletteIcon />, path: '/system/settings' });
    }

    // More menu items for users with administrative permissions
    if (hasPermission('manage_users') && !hasPermission('view_tenants')) {
      // Additional items for subcity admins (less frequently used)
      if (hasPermission('approve_idcards')) {
        moreMenuItems.push({ text: 'Pending Approvals', icon: <PendingIcon />, path: '/idcards/pending-subcity-approval' });
      }
      if (hasPermission('view_reports')) {
        moreMenuItems.push({ text: 'Advanced Reports', icon: <ReportIcon />, path: '/reports/advanced' });
      }
      if (hasPermission('manage_users')) {
        moreMenuItems.push({ text: 'User Permissions', icon: <SupervisorIcon />, path: '/users/permissions' });
      }
      if (hasPermission('view_idcards')) {
        moreMenuItems.push({ text: 'Approval History', icon: <ReportIcon />, path: '/idcards/approval-history' });
      }
    }

    // Add "More" menu if there are additional items
    if (moreMenuItems.length > 0) {
      menuItems.push({ text: 'More', icon: <MoreIcon />, isMore: true });
    }
  } // End of !isPublicPage condition

  const handleOpenNavMenu = (event) => {
    setAnchorElNav(event.currentTarget);
  };

  const handleOpenUserMenu = (event) => {
    setAnchorElUser(event.currentTarget);
  };

  const handleCloseNavMenu = () => {
    setAnchorElNav(null);
  };

  const handleCloseUserMenu = () => {
    setAnchorElUser(null);
  };

  const handleOpenMoreMenu = (event) => {
    setAnchorElMore(event.currentTarget);
  };

  const handleCloseMoreMenu = () => {
    setAnchorElMore(null);
  };

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleLogout = () => {
    logout();
    navigate('/login');
    handleCloseUserMenu();
  };

  const handleProfileClick = () => {
    navigate('/profile');
    handleCloseUserMenu();
  };

  const drawer = (
    <Box onClick={handleDrawerToggle} sx={{ textAlign: 'center' }}>
      <Typography variant="h6" sx={{ my: 2, fontWeight: 'bold' }}>
        {user?.tenant?.name || 'GoID'}
      </Typography>
      <Divider />
      <List>
        {menuItems.map((item) => (
          item.isMore ? (
            <Box key={item.text}>
              <ListItem disablePadding>
                <ListItemButton sx={{ textAlign: 'center' }}>
                  <ListItemIcon sx={{ minWidth: 'auto', mr: 1 }}>
                    {item.icon}
                  </ListItemIcon>
                  <ListItemText primary={item.text} />
                </ListItemButton>
              </ListItem>
              {moreMenuItems.map((moreItem) => (
                <ListItem key={moreItem.text} disablePadding sx={{ pl: 2 }}>
                  <ListItemButton
                    sx={{ textAlign: 'center' }}
                    onClick={() => navigate(moreItem.path)}
                  >
                    <ListItemIcon sx={{ minWidth: 'auto', mr: 1 }}>
                      {moreItem.icon}
                    </ListItemIcon>
                    <ListItemText primary={moreItem.text} />
                  </ListItemButton>
                </ListItem>
              ))}
            </Box>
          ) : (
            <ListItem key={item.text} disablePadding>
              <ListItemButton
                sx={{ textAlign: 'center' }}
                onClick={() => navigate(item.path)}
              >
                <ListItemIcon sx={{ minWidth: 'auto', mr: 1 }}>
                  {item.icon}
                </ListItemIcon>
                <ListItemText primary={item.text} />
              </ListItemButton>
            </ListItem>
          )
        ))}
      </List>
    </Box>
  );

  return (
    <>
      <AppBar position="fixed" sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }}>
        <Container maxWidth="xl">
          <Toolbar disableGutters>
            {/* Logo - Desktop */}
            <Typography
              variant="h6"
              noWrap
              component="div"
              sx={{
                mr: 2,
                display: { xs: 'none', md: 'flex' },
                fontWeight: 'bold',
                color: 'inherit',
                cursor: 'pointer',
              }}
              onClick={() => navigate('/dashboard')}
            >
              {user?.tenant?.name || 'GoID'}
            </Typography>

            {/* Mobile menu icon - only show for authenticated pages */}
            {!isPublicPage && (
              <Box sx={{ flexGrow: 0, display: { xs: 'flex', md: 'none' } }}>
                <IconButton
                  size="large"
                  aria-label="menu"
                  aria-controls="menu-appbar"
                  aria-haspopup="true"
                  onClick={handleDrawerToggle}
                  color="inherit"
                >
                  <MenuIcon />
                </IconButton>
              </Box>
            )}

            {/* Logo - Mobile */}
            <Typography
              variant="h6"
              noWrap
              component="div"
              sx={{
                flexGrow: 1,
                display: { xs: 'flex', md: 'none' },
                fontWeight: 'bold',
              }}
              onClick={() => navigate('/dashboard')}
            >
              {user?.tenant?.name || 'GoID'}
            </Typography>

            {/* Desktop navigation */}
            <Box sx={{ flexGrow: 1, display: { xs: 'none', md: 'flex' } }}>
              {menuItems.map((item) => (
                item.isMore ? (
                  <Box key={item.text}>
                    <Button
                      onClick={handleOpenMoreMenu}
                      sx={{
                        my: 2,
                        color: 'white',
                        display: 'flex',
                        alignItems: 'center',
                        textTransform: 'none',
                        '&:hover': {
                          backgroundColor: 'rgba(255, 255, 255, 0.1)',
                        }
                      }}
                      startIcon={item.icon}
                    >
                      {item.text}
                    </Button>
                    <Menu
                      anchorEl={anchorElMore}
                      open={Boolean(anchorElMore)}
                      onClose={handleCloseMoreMenu}
                      sx={{ mt: '45px' }}
                    >
                      {moreMenuItems.map((moreItem) => (
                        <MenuItem
                          key={moreItem.text}
                          onClick={() => {
                            navigate(moreItem.path);
                            handleCloseMoreMenu();
                          }}
                        >
                          <ListItemIcon>
                            {moreItem.icon}
                          </ListItemIcon>
                          {moreItem.text}
                        </MenuItem>
                      ))}
                    </Menu>
                  </Box>
                ) : (
                  <Button
                    key={item.text}
                    onClick={() => navigate(item.path)}
                    sx={{
                      my: 2,
                      color: 'white',
                      display: 'flex',
                      alignItems: 'center',
                      textTransform: 'none',
                      '&:hover': {
                        backgroundColor: 'rgba(255, 255, 255, 0.1)',
                      }
                    }}
                    startIcon={item.icon}
                  >
                    {item.text}
                  </Button>
                )
              ))}
            </Box>

            {/* Tenant name - only show for authenticated pages */}
            {!isPublicPage && (
              <Typography
                variant="body1"
                sx={{
                  mr: 2,
                  display: { xs: 'none', sm: 'block' }
                }}
              >
                {user?.tenant_name ? user.tenant_name : ''}
              </Typography>
            )}

            {/* Theme toggle */}
            <Tooltip title={`Switch to ${mode === 'light' ? 'dark' : 'light'} mode`}>
              <IconButton color="inherit" onClick={toggleTheme} sx={{ mr: 1 }}>
                {mode === 'light' ? <DarkModeIcon /> : <LightModeIcon />}
              </IconButton>
            </Tooltip>

            {/* User menu for authenticated pages OR login button for public pages */}
            {!isPublicPage ? (
              <Box sx={{ flexGrow: 0, display: 'flex', alignItems: 'center', gap: 1 }}>
              {/* Quick Language Toggle */}
              <Button 
                variant="outlined" 
                size="small" 
                onClick={() => {
                  const newLang = currentLanguage === 'en' ? 'am' : 'en';
                  console.log(`🔄 Quick toggle to ${newLang}`);
                  changeLanguage(newLang);
                }}
                sx={{ minWidth: 60, fontSize: '0.75rem' }}
              >
                {currentLanguage === 'en' ? 'አማርኛ' : 'ENG'}
              </Button>
              <Tooltip title="Open settings">
                <IconButton onClick={handleOpenUserMenu} sx={{ p: 0 }}>
                  <Avatar
                    alt={user?.username || 'User'}
                    src={user?.profile_picture || ''}
                    sx={{ bgcolor: 'secondary.main' }}
                  >
                    {user?.username ? user.username.charAt(0).toUpperCase() : 'U'}
                  </Avatar>
                </IconButton>
              </Tooltip>
              <Menu
                sx={{ mt: '45px' }}
                id="menu-appbar"
                anchorEl={anchorElUser}
                anchorOrigin={{
                  vertical: 'top',
                  horizontal: 'right',
                }}
                keepMounted
                transformOrigin={{
                  vertical: 'top',
                  horizontal: 'right',
                }}
                open={Boolean(anchorElUser)}
                onClose={handleCloseUserMenu}
              >
                <MenuItem onClick={handleProfileClick}>
                  <ListItemIcon>
                    <AccountCircle fontSize="small" />
                  </ListItemIcon>
                  {t('user_profile', 'Profile')}
                </MenuItem>
                <MenuItem onClick={handleCloseUserMenu}>
                  <ListItemIcon>
                    <Settings fontSize="small" />
                  </ListItemIcon>
                  {t('system_settings', 'Settings')}
                </MenuItem>
                <Divider />
                <MenuItem onClick={handleLogout}>
                  <ListItemIcon>
                    <Logout fontSize="small" />
                  </ListItemIcon>
                  {t('logout', 'Logout')}
                </MenuItem>
              </Menu>
            </Box>
            ) : (
              <Button
                variant="contained"
                color="secondary"
                onClick={() => navigate('/login')}
                sx={{ ml: 2 }}
              >
                {t('login', 'Login')}
              </Button>
            )}
          </Toolbar>
        </Container>
      </AppBar>

      {/* Mobile drawer - only show for authenticated pages */}
      {!isPublicPage && (
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true, // Better open performance on mobile
          }}
          sx={{
            display: { xs: 'block', md: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: 240 },
          }}
        >
          {drawer}
        </Drawer>
      )}
    </>
  );
};

export default Navbar;
