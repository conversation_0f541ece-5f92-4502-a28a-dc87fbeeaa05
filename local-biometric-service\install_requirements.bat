@echo off
echo ========================================
echo   INSTALLING PYTHON REQUIREMENTS
echo   For JAR Bridge Service
echo ========================================
echo.

echo Installing required Python modules...
echo.

echo Installing requests module...
pip install requests

echo Installing flask module...
pip install flask

echo Installing flask-cors module...
pip install flask-cors

echo.
if %errorlevel% == 0 (
    echo ✅ All requirements installed successfully!
) else (
    echo ❌ Some installations failed. Please check the output above.
)

echo.
echo Requirements installation completed.
pause
