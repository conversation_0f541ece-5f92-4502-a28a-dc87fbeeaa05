<svg width="300" height="200" viewBox="0 0 300 200" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Definitions -->
  <defs>
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#357ABD;stop-opacity:1" />
    </linearGradient>
    <filter id="cardShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="6" stdDeviation="4" flood-opacity="0.25"/>
    </filter>
    <filter id="browserShadow" x="-10%" y="-10%" width="120%" height="120%">
      <feDropShadow dx="2" dy="4" stdDeviation="3" flood-opacity="0.2"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="300" height="200" fill="url(#blueGradient)"/>
  
  <!-- Browser Window -->
  <rect x="20" y="20" width="140" height="90" rx="8" fill="#6B73FF" opacity="0.9" filter="url(#browserShadow)"/>
  
  <!-- Browser Header -->
  <rect x="20" y="20" width="140" height="20" rx="8" fill="#5A63D8"/>
  <rect x="20" y="35" width="140" height="75" rx="0 0 8 8" fill="#6B73FF" opacity="0.9"/>
  
  <!-- Browser Dots -->
  <circle cx="30" cy="30" r="2.5" fill="white" opacity="0.8"/>
  <circle cx="40" cy="30" r="2.5" fill="white" opacity="0.8"/>
  <circle cx="50" cy="30" r="2.5" fill="white" opacity="0.8"/>
  
  <!-- Browser Content Lines -->
  <rect x="30" y="50" width="120" height="2" rx="1" fill="white" opacity="0.6"/>
  <rect x="30" y="58" width="100" height="2" rx="1" fill="white" opacity="0.6"/>
  <rect x="30" y="66" width="110" height="2" rx="1" fill="white" opacity="0.6"/>
  <rect x="30" y="74" width="90" height="2" rx="1" fill="white" opacity="0.5"/>
  <rect x="30" y="82" width="80" height="2" rx="1" fill="white" opacity="0.4"/>
  <rect x="30" y="90" width="95" height="2" rx="1" fill="white" opacity="0.4"/>
  
  <!-- ID Card 2 (Back Card) -->
  <rect x="130" y="100" width="120" height="75" rx="12" fill="white" opacity="0.85" filter="url(#cardShadow)" transform="rotate(-3 190 137.5)"/>
  
  <!-- ID Card 1 (Front Card) -->
  <rect x="110" y="80" width="120" height="75" rx="12" fill="white" opacity="0.95" filter="url(#cardShadow)" transform="rotate(2 170 117.5)"/>
  
  <!-- ID Card 1 Content -->
  <g transform="rotate(2 170 117.5)">
    <!-- Person Avatar Circle -->
    <circle cx="135" cy="105" r="12" fill="#6B73FF" opacity="0.9"/>
    
    <!-- Person Icon -->
    <path d="M131 101 C131 98.5, 132.8 96.5, 135 96.5 C137.2 96.5, 139 98.5, 139 101 C139 103.5, 137.2 105.5, 135 105.5 C132.8 105.5, 131 103.5, 131 101 Z M127 113 C127 109, 130.5 106, 135 106 C139.5 106, 143 109, 143 113 L127 113 Z" fill="white"/>
    
    <!-- ID Card Text Lines -->
    <rect x="155" y="95" width="60" height="3" rx="1.5" fill="#E0E0E0"/>
    <rect x="155" y="103" width="45" height="2.5" rx="1.25" fill="#E0E0E0"/>
    <rect x="155" y="111" width="50" height="2.5" rx="1.25" fill="#E0E0E0"/>
    <rect x="155" y="119" width="40" height="2.5" rx="1.25" fill="#E0E0E0"/>
    
    <!-- ID Number -->
    <rect x="120" y="135" width="90" height="3" rx="1.5" fill="#4A90E2" opacity="0.8"/>
  </g>
  
  <!-- ID Card 2 Content (Partially Visible) -->
  <g transform="rotate(-3 190 137.5)">
    <circle cx="155" cy="125" r="10" fill="#5A63D8" opacity="0.7"/>
    <rect x="170" y="118" width="50" height="2.5" rx="1.25" fill="#E0E0E0" opacity="0.7"/>
    <rect x="170" y="125" width="40" height="2.5" rx="1.25" fill="#E0E0E0" opacity="0.7"/>
    <rect x="170" y="132" width="45" height="2.5" rx="1.25" fill="#E0E0E0" opacity="0.7"/>
  </g>
  
  <!-- Decorative Elements -->
  <circle cx="50" cy="150" r="15" fill="white" opacity="0.1"/>
  <circle cx="250" cy="50" r="12" fill="white" opacity="0.12"/>
  <circle cx="270" cy="150" r="20" fill="white" opacity="0.08"/>
  <circle cx="25" cy="80" r="8" fill="white" opacity="0.15"/>
  
  <!-- Small Floating Dots -->
  <circle cx="220" cy="120" r="3" fill="white" opacity="0.2"/>
  <circle cx="80" cy="40" r="2" fill="white" opacity="0.18"/>
  <circle cx="260" cy="90" r="4" fill="white" opacity="0.15"/>
  <circle cx="40" cy="170" r="3" fill="white" opacity="0.12"/>
</svg>
