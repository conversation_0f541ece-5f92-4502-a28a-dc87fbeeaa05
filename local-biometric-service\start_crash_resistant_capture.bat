@echo off
echo ========================================
echo   CRASH-RESISTANT REAL CAPTURE
echo   Subprocess Isolation Protection
echo ========================================
echo.

echo MISSION: Capture REAL fingerprints without service crashes
echo.
echo PROTECTION FEATURES:
echo   - Subprocess isolation prevents DLL crashes
echo   - Service continues even if capture process crashes
echo   - Automatic retry with different signatures
echo   - Safe testing of all DLL functions
echo.

echo Checking Python installation...
python --version
if errorlevel 1 (
    echo ERROR: Python not found. Please install Python 3.7+
    pause
    exit /b 1
)

echo.
echo Checking required DLL files...
if not exist "ftrScanAPI.dll" (
    echo ERROR: ftrScanAPI.dll not found
    echo Please ensure Futronic SDK files are in the current directory
    pause
    exit /b 1
)

echo SUCCESS: ftrScanAPI.dll found

echo.
echo Starting CRASH-RESISTANT Real Capture Service...
echo Service will be available at: http://localhost:8001
echo.
echo This service uses subprocess isolation to prevent crashes!
echo.
echo Press Ctrl+C to stop the service
echo.

python crash_resistant_real_capture.py

echo.
echo Service stopped.
pause
