#!/usr/bin/env python3
"""
Local Biometric Service for GoID System
Runs on each workstation with a Futronic FS88H device
Provides HTTP API for web browser to access the biometric device
"""

import json
import base64
import logging
from datetime import datetime
from flask import Flask, request, jsonify
from flask_cors import CORS
import threading
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)  # Enable CORS for web browser access

class BiometricDeviceManager:
    def __init__(self):
        self.device = None
        self.device_connected = False
        self.device_initialized = False
        self.device_info = {
            'model': 'Futronic FS88H',
            'serial_number': 'Unknown',
            'firmware_version': 'Unknown'
        }
        self.initialize_device()

    def initialize_device(self):
        """Initialize the Futronic FS88H device using official SDK"""
        try:
            # Import the Futronic SDK implementation
            from futronic_sdk import FutronicSDK

            logger.info("Initializing Futronic FS88H device with official SDK...")

            # Create device instance
            self.device = FutronicSDK()

            # Initialize the device
            if self.device.initialize():
                status = self.device.get_device_status()
                self.device_connected = status['connected']
                self.device_initialized = status['initialized']
                self.device_info = {
                    'model': status['model'],
                    'serial_number': status['serial_number'],
                    'firmware_version': status['firmware_version']
                }

                if status['real_device']:
                    logger.info("✅ Real Futronic FS88H device detected and initialized!")
                    logger.info(f"Device: {self.device_info['model']}")
                    logger.info(f"Serial: {self.device_info['serial_number']}")
                    logger.info(f"Interface: {status.get('interface', 'unknown')}")
                    logger.info("🔧 Using official Futronic SDK DLLs")
                else:
                    logger.warning("⚠️ Device initialized in simulation mode")
                    logger.warning("Real Futronic FS88H device not detected or SDK DLLs not found")
                    self.device_connected = False  # Mark as not connected for simulation

                return True
            else:
                raise Exception("Device initialization failed")

        except ImportError:
            logger.error("❌ Futronic SDK module not found")
            logger.info("Please ensure futronic_sdk.py is in the same directory")
            return False
        except Exception as e:
            logger.error(f"❌ Failed to initialize device: {e}")
            self.device_connected = False
            self.device_initialized = False
            return False
    
    def capture_fingerprint(self, thumb_type='left'):
        """Capture fingerprint from the device using SDK"""
        try:
            if not self.device_connected:
                return {
                    'success': False,
                    'error': 'Device not connected'
                }

            if not self.device_initialized:
                return {
                    'success': False,
                    'error': 'Device not initialized'
                }

            if not self.device:
                return {
                    'success': False,
                    'error': 'Device instance not available'
                }

            logger.info(f"Capturing {thumb_type} thumb fingerprint using SDK...")

            # Use the SDK device to capture fingerprint with error handling
            try:
                template = self.device.capture_fingerprint(thumb_type)

                if template:
                    logger.info(f"Fingerprint captured: Quality {template.quality_score}%")
                    return {
                        'success': True,
                        'data': {
                            'template_data': template.template_data,
                            'quality_score': template.quality_score,
                            'quality_valid': template.quality_score >= 75,
                            'quality_message': f'Quality score: {template.quality_score}%',
                            'capture_time': template.capture_time,
                            'thumb_type': template.thumb_type,
                            'minutiae_count': template.minutiae_count,
                            'device_info': template.device_info
                        }
                    }
                else:
                    return {
                        'success': False,
                        'error': 'Failed to capture fingerprint template'
                    }

            except Exception as capture_error:
                logger.error(f"SDK capture error: {capture_error}")
                return {
                    'success': False,
                    'error': f'Capture failed: {str(capture_error)}'
                }

        except Exception as e:
            logger.error(f"Fingerprint capture failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    


# Global device manager
device_manager = BiometricDeviceManager()

@app.route('/api/device/status', methods=['GET'])
def get_device_status():
    """Get device connection and initialization status"""
    return jsonify({
        'success': True,
        'data': {
            'connected': device_manager.device_connected,
            'initialized': device_manager.device_initialized,
            'device_info': device_manager.device_info
        }
    })

@app.route('/api/device/initialize', methods=['POST'])
def initialize_device():
    """Initialize the biometric device"""
    success = device_manager.initialize_device()
    
    if success:
        return jsonify({
            'success': True,
            'message': 'Device initialized successfully'
        })
    else:
        return jsonify({
            'success': False,
            'error': 'Failed to initialize device'
        }), 500

@app.route('/api/capture/fingerprint', methods=['GET', 'POST'])
def capture_fingerprint():
    """Capture fingerprint from the device"""
    # Handle both GET and POST requests
    if request.method == 'GET':
        thumb_type = request.args.get('thumb_type', 'left')
        skip_detection = request.args.get('skip_detection', 'false').lower() == 'true'
    else:
        data = request.get_json() or {}
        thumb_type = data.get('thumb_type', 'left')
        skip_detection = data.get('skip_detection', False)

    if thumb_type not in ['left', 'right']:
        return jsonify({
            'success': False,
            'error': 'Invalid thumb_type. Must be "left" or "right"'
        }), 400

    logger.info(f"Starting fingerprint capture for {thumb_type} thumb...")
    if skip_detection:
        logger.info("⚡ Skipping finger detection - direct capture")

    result = device_manager.capture_fingerprint(thumb_type)

    if result['success']:
        logger.info(f"Fingerprint captured successfully!")
        return jsonify(result)
    else:
        logger.error(f"Fingerprint capture failed: {result.get('error', 'Unknown error')}")
        return jsonify(result), 500

@app.route('/api/capture/direct', methods=['GET', 'POST'])
def capture_direct():
    """Direct capture without finger detection"""
    # Handle both GET and POST requests
    if request.method == 'GET':
        thumb_type = request.args.get('thumb_type', 'left')
    else:
        data = request.get_json() or {}
        thumb_type = data.get('thumb_type', 'left')

    if thumb_type not in ['left', 'right']:
        return jsonify({
            'success': False,
            'error': 'Invalid thumb_type. Must be "left" or "right"'
        }), 400

    logger.info(f"Direct capture for {thumb_type} thumb (no finger detection)...")

    # Call the device directly for immediate capture
    try:
        if not device_manager.device_connected or not device_manager.device:
            return jsonify({
                'success': False,
                'error': 'Device not connected'
            }), 503

        # Skip finger detection and go straight to frame capture
        template = device_manager.device._capture_direct_frame(thumb_type)

        if template:
            return jsonify({
                'success': True,
                'data': {
                    'template_data': template.template_data,
                    'quality_score': template.quality_score,
                    'quality_valid': template.quality_score >= 75,
                    'quality_message': f'Quality score: {template.quality_score}%',
                    'capture_time': template.capture_time,
                    'thumb_type': template.thumb_type,
                    'minutiae_count': template.minutiae_count,
                    'device_info': template.device_info
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Direct capture failed'
            }), 500

    except Exception as e:
        logger.error(f"Direct capture error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/test/frame', methods=['GET'])
def test_frame_capture():
    """Test frame capture without any processing"""
    try:
        if not device_manager.device_connected or not device_manager.device:
            return jsonify({
                'success': False,
                'error': 'Device not connected'
            }), 503

        logger.info("🧪 Testing basic frame capture...")

        # Get device handle and try basic frame capture
        device = device_manager.device
        if not device.device_handle:
            return jsonify({
                'success': False,
                'error': 'Device handle not available'
            }), 503

        # Try to capture a frame directly
        import ctypes
        from ctypes import c_uint, byref

        FTR_IMAGE_SIZE = 320 * 480
        image_buffer = (ctypes.c_ubyte * FTR_IMAGE_SIZE)()
        frame_size = c_uint(FTR_IMAGE_SIZE)

        logger.info("📸 Attempting direct frame capture...")
        result = device.scan_api.ftrScanGetFrame(device.device_handle, image_buffer, byref(frame_size))

        # Calculate basic statistics
        if frame_size.value > 0:
            # Sample first 1000 pixels for analysis
            sample_size = min(1000, frame_size.value)
            pixel_sum = sum(image_buffer[i] for i in range(sample_size))
            avg_pixel = pixel_sum / sample_size if sample_size > 0 else 0

            # Check for non-zero pixels (indicates actual image data)
            non_zero_pixels = sum(1 for i in range(sample_size) if image_buffer[i] > 10)
            data_percentage = (non_zero_pixels / sample_size) * 100 if sample_size > 0 else 0
        else:
            avg_pixel = 0
            data_percentage = 0

        return jsonify({
            'success': True,
            'data': {
                'result_code': result,
                'result_message': 'OK' if result == 0 else f'Error {result}',
                'frame_size': frame_size.value,
                'average_pixel_value': round(avg_pixel, 2),
                'data_percentage': round(data_percentage, 2),
                'has_image_data': data_percentage > 5,  # More than 5% non-zero pixels
                'timestamp': datetime.now().isoformat()
            }
        })

    except Exception as e:
        logger.error(f"Frame test error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/', methods=['GET'])
def index():
    """Simple web interface for testing"""
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>GoID Biometric Service</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #2c3e50; text-align: center; }
            .status { padding: 15px; margin: 20px 0; border-radius: 5px; }
            .status.connected { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
            .status.disconnected { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
            button { background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; margin: 10px 5px; font-size: 16px; }
            button:hover { background: #0056b3; }
            button:disabled { background: #6c757d; cursor: not-allowed; }
            .result { margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #007bff; }
            .error { border-left-color: #dc3545; background: #f8d7da; }
            .success { border-left-color: #28a745; background: #d4edda; }
            pre { background: #f1f1f1; padding: 10px; border-radius: 3px; overflow-x: auto; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🔒 GoID Biometric Service</h1>

            <div id="status" class="status">
                <strong>Device Status:</strong> <span id="device-status">Checking...</span>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <button onclick="checkStatus()">🔄 Refresh Status</button>
                <button onclick="captureFingerprint('left')" id="capture-left">👈 Capture Left Thumb</button>
                <button onclick="captureFingerprint('right')" id="capture-right">👉 Capture Right Thumb</button>
            </div>

            <div id="result"></div>

            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                <h3>API Endpoints:</h3>
                <ul>
                    <li><strong>Device Status:</strong> <a href="/api/device/status" target="_blank">/api/device/status</a></li>
                    <li><strong>Health Check:</strong> <a href="/api/health" target="_blank">/api/health</a></li>
                    <li><strong>Capture Fingerprint:</strong> /api/capture/fingerprint (POST)</li>
                </ul>
            </div>
        </div>

        <script>
            async function checkStatus() {
                try {
                    const response = await fetch('/api/device/status');
                    const data = await response.json();

                    const statusDiv = document.getElementById('status');
                    const statusSpan = document.getElementById('device-status');

                    if (data.success && data.data.connected) {
                        statusDiv.className = 'status connected';
                        statusSpan.textContent = `Connected - ${data.data.device_info.model}`;
                        document.getElementById('capture-left').disabled = false;
                        document.getElementById('capture-right').disabled = false;
                    } else {
                        statusDiv.className = 'status disconnected';
                        statusSpan.textContent = 'Disconnected';
                        document.getElementById('capture-left').disabled = true;
                        document.getElementById('capture-right').disabled = true;
                    }
                } catch (error) {
                    document.getElementById('device-status').textContent = 'Error checking status';
                }
            }

            async function captureFingerprint(thumbType) {
                const resultDiv = document.getElementById('result');
                resultDiv.innerHTML = `<div class="result">📸 Capturing ${thumbType} thumb... Please place your finger on the scanner.</div>`;

                try {
                    const response = await fetch(`/api/capture/fingerprint?thumb_type=${thumbType}`);
                    const data = await response.json();

                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="result success">
                                <h4>✅ Fingerprint Captured Successfully!</h4>
                                <p><strong>Thumb:</strong> ${data.data.thumb_type}</p>
                                <p><strong>Quality:</strong> ${data.data.quality_score}%</p>
                                <p><strong>Minutiae:</strong> ${data.data.minutiae_count}</p>
                                <p><strong>Capture Time:</strong> ${data.data.capture_time}</p>
                                <p><strong>Device:</strong> ${data.data.device_info.real_device ? 'Real Device' : 'Simulation'}</p>
                                <details>
                                    <summary>Template Data</summary>
                                    <pre>${JSON.stringify(data.data, null, 2)}</pre>
                                </details>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="result error">
                                <h4>❌ Capture Failed</h4>
                                <p>${data.error}</p>
                            </div>
                        `;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h4>❌ Network Error</h4>
                            <p>${error.message}</p>
                        </div>
                    `;
                }
            }

            // Check status on page load
            checkStatus();
        </script>
    </body>
    </html>
    """
    return html

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'Local Biometric Service',
        'timestamp': datetime.now().isoformat(),
        'device_connected': device_manager.device_connected
    })

if __name__ == '__main__':
    try:
        logger.info("🚀 Starting Local Biometric Service")
        logger.info("📱 Service will be available at: http://localhost:8001")
        logger.info("🔧 Make sure Futronic FS88H device is connected")

        # Initialize device manager
        logger.info("🔧 Initializing device manager...")
        try:
            init_result = device_manager.initialize_device()
            logger.info(f"🔧 Device manager initialization returned: {init_result}")

            if init_result:
                logger.info("✅ Device manager initialized successfully")
                logger.info(f"🔧 Device connected: {device_manager.device_connected}")
                logger.info(f"🔧 Device initialized: {device_manager.device_initialized}")
            else:
                logger.warning("⚠️ Device manager initialization failed - service will run in simulation mode")
        except Exception as init_error:
            logger.error(f"❌ Device manager initialization error: {init_error}")
            logger.warning("⚠️ Service will run in simulation mode")

        # Run the service
        logger.info("🌐 Starting Flask service...")
        app.run(
            host='0.0.0.0',  # Allow access from any IP
            port=8001,       # Different port from main app
            debug=False,     # Set to False for production
            threaded=True    # Handle multiple requests
        )

    except KeyboardInterrupt:
        logger.info("🛑 Service stopped by user")
    except Exception as e:
        logger.error(f"❌ Service startup error: {e}")
        logger.error("💡 Check device connection and drivers")
        input("Press Enter to exit...")
    finally:
        # Cleanup
        try:
            if device_manager.device:
                device_manager.device.disconnect()
                logger.info("✅ Device disconnected cleanly")
        except:
            pass
