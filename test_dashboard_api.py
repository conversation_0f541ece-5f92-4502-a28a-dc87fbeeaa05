#!/usr/bin/env python3
"""
Test script to check dashboard API endpoints
"""
import requests
import json

# Configuration
BASE_URL = "http://localhost:8000"
LOGIN_URL = f"{BASE_URL}/api/auth/token/"

def test_login():
    """Test login and get token"""
    # Try different login credentials
    test_credentials = [
        {"username": "<EMAIL>", "password": "admin123"},
        {"email": "<EMAIL>", "password": "admin123"},
        {"username": "<EMAIL>", "password": "clerk123"},
    ]
    
    for creds in test_credentials:
        print(f"\n🔍 Testing login with: {creds}")
        try:
            response = requests.post(LOGIN_URL, json=creds)
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Login successful!")
                print(f"User data: {json.dumps(data, indent=2)}")
                return data.get('access'), data
            else:
                print(f"❌ Login failed: {response.text}")
        except Exception as e:
            print(f"❌ Error: {e}")
    
    return None, None

def test_dashboard_endpoints(token, user_data):
    """Test dashboard API endpoints"""
    if not token:
        print("❌ No token available for testing")
        return
    
    headers = {"Authorization": f"Bearer {token}"}
    tenant_id = user_data.get('tenant_id')
    
    if not tenant_id:
        print("❌ No tenant_id in user data")
        return
    
    print(f"\n🔍 Testing dashboard endpoints for tenant: {tenant_id}")
    
    # Test endpoints
    endpoints = [
        f"/api/tenants/{tenant_id}/citizens/stats/",
        f"/api/tenants/{tenant_id}/idcards/stats/",
        f"/api/tenants/{tenant_id}/citizens/?limit=5&ordering=-created_at",
        f"/api/tenants/{tenant_id}/idcards/?limit=5&ordering=-created_at",
    ]
    
    for endpoint in endpoints:
        url = f"{BASE_URL}{endpoint}"
        print(f"\n📡 Testing: {endpoint}")
        try:
            response = requests.get(url, headers=headers)
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Success! Data: {json.dumps(data, indent=2)[:200]}...")
            else:
                print(f"❌ Failed: {response.text}")
        except Exception as e:
            print(f"❌ Error: {e}")

def main():
    print("🚀 Testing Dashboard API Endpoints")
    print("=" * 50)
    
    # Test login
    token, user_data = test_login()
    
    # Test dashboard endpoints
    if token and user_data:
        test_dashboard_endpoints(token, user_data)
    else:
        print("❌ Could not get valid token, skipping endpoint tests")

if __name__ == "__main__":
    main()
