"""
Script to run Django migrations for all tenants.
"""

import os
import sys
import django
from django.core.management import call_command

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from tenants.models import Tenant
from django_tenants.utils import schema_context


def run_migrations():
    """Run migrations for all tenants."""
    # Run migrations for public schema first
    print("Running migrations for public schema...")
    call_command('migrate_schemas', schema_name='public', verbosity=1)
    
    # Run migrations for all other tenants
    tenants = Tenant.objects.exclude(schema_name='public')
    for tenant in tenants:
        print(f"Running migrations for tenant: {tenant.name} ({tenant.schema_name})...")
        with schema_context(tenant.schema_name):
            call_command('migrate', verbosity=1)
    
    print("All migrations completed successfully.")


if __name__ == '__main__':
    run_migrations()
