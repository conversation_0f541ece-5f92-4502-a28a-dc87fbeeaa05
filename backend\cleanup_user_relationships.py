#!/usr/bin/env python3
"""
Script to safely delete a user by first cleaning up foreign key relationships in tenant schemas.
This prevents the "relation citizen_clearance_requests does not exist" error.
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.db import transaction
from django_tenants.utils import schema_context
from tenants.models import Tenant
from users.models import User

def cleanup_user_relationships(user_id_or_email):
    """
    Clean up all foreign key relationships for a user in tenant schemas before deletion.
    
    Args:
        user_id_or_email: Either the user ID (int) or email (str) of the user to clean up
    """
    try:
        # Get the user
        if isinstance(user_id_or_email, int) or user_id_or_email.isdigit():
            user = User.objects.get(id=int(user_id_or_email))
        else:
            user = User.objects.get(email=user_id_or_email)
        
        print(f"🔍 Found user: {user.email} (ID: {user.id})")
        print(f"   Role: {user.role}")
        print(f"   Tenant: {user.tenant.name if user.tenant else 'None'}")
        print()
        
        # Get all kebele tenants where relationships might exist
        kebele_tenants = Tenant.objects.filter(type='kebele')
        print(f"🏢 Found {kebele_tenants.count()} kebele tenants to check")
        print()
        
        total_cleaned = 0
        
        for tenant in kebele_tenants:
            print(f"🔍 Checking tenant: {tenant.name} ({tenant.schema_name})")
            
            try:
                with schema_context(tenant.schema_name):
                    # Import models within schema context
                    from workflows.models import CitizenClearanceRequest, WorkflowLog
                    
                    # Clean up CitizenClearanceRequest relationships
                    clearance_count = 0
                    
                    # Requests made by this user
                    requested_clearances = CitizenClearanceRequest.objects.filter(requested_by=user)
                    if requested_clearances.exists():
                        count = requested_clearances.count()
                        requested_clearances.delete()
                        clearance_count += count
                        print(f"   ✅ Deleted {count} clearance requests made by user")
                    
                    # Requests reviewed by this user (SET_NULL, so just update)
                    reviewed_clearances = CitizenClearanceRequest.objects.filter(reviewed_by=user)
                    if reviewed_clearances.exists():
                        count = reviewed_clearances.count()
                        reviewed_clearances.update(reviewed_by=None)
                        print(f"   ✅ Cleared reviewer from {count} clearance requests")
                    
                    # Requests issued by this user (SET_NULL, so just update)
                    issued_clearances = CitizenClearanceRequest.objects.filter(issued_by=user)
                    if issued_clearances.exists():
                        count = issued_clearances.count()
                        issued_clearances.update(issued_by=None)
                        print(f"   ✅ Cleared issuer from {count} clearance requests")
                    
                    # Clean up WorkflowLog relationships (SET_NULL, so just update)
                    workflow_logs = WorkflowLog.objects.filter(performed_by=user)
                    if workflow_logs.exists():
                        count = workflow_logs.count()
                        workflow_logs.update(performed_by=None)
                        print(f"   ✅ Cleared performer from {count} workflow logs")
                    
                    tenant_total = clearance_count
                    total_cleaned += tenant_total
                    
                    if tenant_total == 0:
                        print(f"   ✅ No relationships found in {tenant.name}")
                    
            except Exception as e:
                print(f"   ❌ Error cleaning up {tenant.schema_name}: {e}")
        
        print()
        print(f"🎉 Cleanup completed! Total relationships cleaned: {total_cleaned}")
        print(f"✅ User {user.email} can now be safely deleted from the admin interface")
        
        return user, total_cleaned
        
    except User.DoesNotExist:
        print(f"❌ User not found: {user_id_or_email}")
        return None, 0
    except Exception as e:
        print(f"❌ Error during cleanup: {e}")
        return None, 0

def delete_user_safely(user_id_or_email, confirm=False):
    """
    Clean up relationships and delete the user.
    
    Args:
        user_id_or_email: Either the user ID (int) or email (str) of the user to delete
        confirm: If True, actually delete the user. If False, just clean up relationships.
    """
    user, cleaned_count = cleanup_user_relationships(user_id_or_email)
    
    if user and confirm:
        print()
        print(f"🗑️  Deleting user: {user.email}")
        
        try:
            with transaction.atomic():
                user.delete()
                print(f"✅ User {user.email} deleted successfully!")
        except Exception as e:
            print(f"❌ Error deleting user: {e}")
    elif user and not confirm:
        print()
        print("⚠️  User relationships cleaned but user not deleted (confirm=False)")
        print("   You can now safely delete the user from the Django admin interface")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python cleanup_user_relationships.py <user_id_or_email> [--delete]")
        print("Examples:")
        print("  python cleanup_user_relationships.py <EMAIL>")
        print("  python cleanup_user_relationships.py 123")
        print("  python cleanup_user_relationships.py <EMAIL> --delete")
        sys.exit(1)
    
    user_identifier = sys.argv[1]
    should_delete = "--delete" in sys.argv
    
    if should_delete:
        print("⚠️  WARNING: This will permanently delete the user!")
        response = input("Are you sure? Type 'yes' to confirm: ")
        if response.lower() != 'yes':
            print("❌ Deletion cancelled")
            sys.exit(1)
    
    delete_user_safely(user_identifier, confirm=should_delete)
