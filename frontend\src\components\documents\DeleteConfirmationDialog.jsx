import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Alert,
  Chip,
  Divider
} from '@mui/material';
import {
  Warning as WarningIcon,
  Delete as DeleteIcon,
  Cancel as CancelIcon,
  Description as DocumentIcon
} from '@mui/icons-material';

const DeleteConfirmationDialog = ({ 
  open, 
  onClose, 
  onConfirm, 
  document, 
  loading = false 
}) => {
  if (!document) return null;

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      return 'N/A';
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: 3
        }
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        gap: 2, 
        pb: 2,
        backgroundColor: 'error.main',
        color: 'white'
      }}>
        <WarningIcon sx={{ fontSize: 28 }} />
        <Typography variant="h6" fontWeight="bold">
          Delete Document
        </Typography>
      </DialogTitle>

      <DialogContent sx={{ pt: 3 }}>
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="body2" fontWeight="medium">
            This action cannot be undone. The document will be permanently deleted from the system.
          </Typography>
        </Alert>

        {/* Document Information */}
        <Box sx={{ 
          p: 2, 
          backgroundColor: 'grey.50', 
          borderRadius: 1, 
          border: '1px solid',
          borderColor: 'grey.200',
          mb: 2
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
            <DocumentIcon color="primary" />
            <Typography variant="h6" fontWeight="bold">
              {document.document_type_name}
            </Typography>
          </Box>

          <Divider sx={{ my: 2 }} />

          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography variant="body2" color="text.secondary">
                Document ID:
              </Typography>
              <Typography variant="body2" fontWeight="medium">
                #{document.id}
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography variant="body2" color="text.secondary">
                Issue Date:
              </Typography>
              <Typography variant="body2" fontWeight="medium">
                {formatDate(document.issue_date)}
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography variant="body2" color="text.secondary">
                Expiry Date:
              </Typography>
              <Typography variant="body2" fontWeight="medium">
                {formatDate(document.expiry_date)}
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography variant="body2" color="text.secondary">
                Uploaded:
              </Typography>
              <Typography variant="body2" fontWeight="medium">
                {formatDate(document.created_at)}
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                Status:
              </Typography>
              <Chip
                label={document.is_active ? 'Active' : 'Inactive'}
                color={document.is_active ? 'success' : 'default'}
                size="small"
              />
            </Box>
          </Box>
        </Box>

        <Typography variant="body1" color="text.primary" sx={{ mb: 2 }}>
          Are you sure you want to delete this document?
        </Typography>

        <Typography variant="body2" color="text.secondary">
          This will permanently remove the document file and all associated data from the system.
        </Typography>
      </DialogContent>

      <DialogActions sx={{ p: 3, gap: 1 }}>
        <Button
          onClick={onClose}
          variant="outlined"
          startIcon={<CancelIcon />}
          disabled={loading}
          sx={{ minWidth: 120 }}
        >
          Cancel
        </Button>
        <Button
          onClick={onConfirm}
          variant="contained"
          color="error"
          startIcon={<DeleteIcon />}
          disabled={loading}
          sx={{ minWidth: 120 }}
        >
          {loading ? 'Deleting...' : 'Delete Document'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DeleteConfirmationDialog;
