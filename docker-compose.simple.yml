version: '3.8'

services:
  # Backend service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DJANGO_SETTINGS_MODULE=goid.settings
      - DEBUG=True
      - DATABASE_URL=********************************************/goid_db
      - REDIS_URL=redis://redis:6379/0
      - ALLOWED_HOSTS=*
    depends_on:
      - db
      - redis
    volumes:
      - ./backend:/app
      - ./backend/media:/app/media
      - ./backend/static:/app/static
    networks:
      - goid_network
    restart: unless-stopped

  # Frontend service (development mode)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - CHOKIDAR_USEPOLLING=true
      - REACT_APP_API_URL=http://localhost:8000
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - goid_network
    restart: unless-stopped

  # Database
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: goid_db
      POSTGRES_USER: goid_user
      POSTGRES_PASSWORD: goid_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - goid_network
    restart: unless-stopped

  # Redis
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - goid_network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  goid_network:
    driver: bridge
