# Simple test for Django Admin CSS fix
Write-Host "Testing Django Admin CSS Fix" -ForegroundColor Cyan

$baseUrl = "http://localhost:8000"

# Test CSS file
Write-Host "Testing CSS file serving..." -ForegroundColor Yellow
try {
    $cssResponse = Invoke-WebRequest -Uri "$baseUrl/static/admin/css/base.css" -UseBasicParsing -TimeoutSec 10
    $contentType = $cssResponse.Headers["Content-Type"]
    
    Write-Host "Status Code: $($cssResponse.StatusCode)" -ForegroundColor White
    Write-Host "Content-Type: $contentType" -ForegroundColor White
    
    if ($cssResponse.StatusCode -eq 200 -and $contentType -match "text/css") {
        Write-Host "SUCCESS: CSS file is being served correctly!" -ForegroundColor Green
        
        # Check content is not HTML
        $content = $cssResponse.Content.Substring(0, [Math]::Min(100, $cssResponse.Content.Length))
        if ($content -match "<!DOCTYPE" -or $content -match "<html") {
            Write-Host "ERROR: CSS file contains HTML content!" -ForegroundColor Red
        } else {
            Write-Host "SUCCESS: CSS file contains actual CSS content!" -ForegroundColor Green
        }
    } else {
        Write-Host "ERROR: CSS file not served correctly" -ForegroundColor Red
    }
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

# Test JS file
Write-Host "`nTesting JavaScript file serving..." -ForegroundColor Yellow
try {
    $jsResponse = Invoke-WebRequest -Uri "$baseUrl/static/admin/js/core.js" -UseBasicParsing -TimeoutSec 10
    $contentType = $jsResponse.Headers["Content-Type"]
    
    Write-Host "Status Code: $($jsResponse.StatusCode)" -ForegroundColor White
    Write-Host "Content-Type: $contentType" -ForegroundColor White
    
    if ($jsResponse.StatusCode -eq 200) {
        Write-Host "SUCCESS: JavaScript file is being served correctly!" -ForegroundColor Green
    } else {
        Write-Host "ERROR: JavaScript file not served correctly" -ForegroundColor Red
    }
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nTest completed!" -ForegroundColor Cyan
