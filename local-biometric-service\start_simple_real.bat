@echo off
echo ========================================
echo   SIMPLE REAL CAPTURE
echo   Using Working Signature We Discovered
echo ========================================
echo.

echo BREAKTHROUGH: We found the working signature!
echo.
echo WORKING METHOD DISCOVERED:
echo   - Function: ftrScanGetFrame
echo   - Signature: Standard c_ulong pointer
echo   - Device initialization: WORKS
echo   - Finger detection: WORKS (LED lights activate)
echo   - Result: 0 (SUCCESS)
echo.
echo SIMPLE APPROACH:
echo   - No complex error handling
echo   - Focus on the working signature only
echo   - Direct capture with known working method
echo   - Real fingerprint data capture
echo.

echo Checking Python installation...
python --version
if errorlevel 1 (
    echo ERROR: Python not found. Please install Python 3.7+
    pause
    exit /b 1
)

echo.
echo Checking required DLL files...
if not exist "ftrScanAPI.dll" (
    echo ERROR: ftrScanAPI.dll not found
    echo Please ensure Futronic SDK files are in the current directory
    pause
    exit /b 1
)

echo SUCCESS: ftrScanAPI.dll found

echo.
echo ========================================
echo   SIMPLE REAL CAPTURE APPROACH
echo ========================================
echo.
echo WHAT THIS DOES:
echo   1. Initialize device with working approach
echo   2. Use discovered working signature
echo   3. Capture real fingerprint data
echo   4. Analyze data quality and patterns
echo   5. Return GoID-compatible results
echo.
echo EXPECTED RESULTS:
echo   - Device initialization: SUCCESS
echo   - Finger placement prompt
echo   - Real data capture (non-zero bytes)
echo   - Quality analysis (percentage)
echo   - Unique value patterns
echo   - GoID-compatible template data
echo.
echo INSTRUCTIONS:
echo   1. Service will start and initialize device
echo   2. Click capture button in web interface
echo   3. Place finger FIRMLY on scanner when prompted
echo   4. Keep finger still during capture
echo   5. Check results for real fingerprint data
echo.

echo Starting SIMPLE REAL CAPTURE Service...
echo Service will be available at: http://localhost:8001
echo.
echo This service uses the WORKING signature we discovered
echo to capture REAL fingerprints from your hardware!
echo.
echo Press Ctrl+C to stop the service
echo.

python simple_real_capture.py

echo.
echo Service stopped.
pause
