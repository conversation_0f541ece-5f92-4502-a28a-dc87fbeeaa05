#!/usr/bin/env python3
"""
Test Duplicate Detection with Authentication
"""

import urllib.request
import urllib.parse
import json
import base64

def test_backend_connection():
    """Test if GoID backend is accessible"""
    
    print("🔍 Testing GoID Backend Connection...")
    
    endpoints_to_test = [
        "http://localhost:8000/",
        "http://localhost:8000/api/",
        "http://localhost:8000/api/health/",
        "http://localhost:3000/",  # Frontend
    ]
    
    for endpoint in endpoints_to_test:
        try:
            response = urllib.request.urlopen(endpoint, timeout=5)
            print(f"✅ {endpoint} - Status: {response.getcode()}")
        except urllib.error.HTTPError as e:
            print(f"⚠️ {endpoint} - HTTP Error: {e.code}")
        except urllib.error.URLError as e:
            print(f"❌ {endpoint} - Connection Error: {e}")
        except Exception as e:
            print(f"❌ {endpoint} - Error: {e}")

def test_duplicate_detection_endpoints():
    """Test different duplicate detection endpoints"""
    
    print(f"\n🔍 Testing Duplicate Detection Endpoints...")
    
    # Sample real templates (from our successful capture)
    left_template = "eyJ2ZXJzaW9uIjogIjIuMCIsICJ0aHVtYl90eXBlIjogInVua25vd24iLCAibWludXRpYWVfcG9pbnRzIjogW3sieCI6IDEzMSwgInkiOiAxMDYsICJhbmdsZSI6IDMxNiwgInR5cGUiOiAicmlkZ2VfZW5kaW5nIiwgInF1YWxpdHkiOiAwLjkyOTk5OTk5OTk5OTk5OTl9XSwgImltYWdlX2RpbWVuc2lvbnMiOiB7IndpZHRoIjogMTYwLCAiaGVpZ2h0IjogNDgwfSwgInRvdGFsX3BpeGVscyI6IDc2ODAwfQ=="
    right_template = "eyJ2ZXJzaW9uIjogIjIuMCIsICJ0aHVtYl90eXBlIjogInVua25vd24iLCAibWludXRpYWVfcG9pbnRzIjogW3sieCI6IDEwMCwgInkiOiAyMDAsICJhbmdsZSI6IDE4MCwgInR5cGUiOiAiYmlmdXJjYXRpb24iLCAicXVhbGl0eSI6IDAuODV9XSwgImltYWdlX2RpbWVuc2lvbnMiOiB7IndpZHRoIjogMTYwLCAiaGVpZ2h0IjogNDgwfSwgInRvdGFsX3BpeGVscyI6IDc2ODAwfQ=="
    
    # Different endpoint variations to try
    endpoints = [
        "http://localhost:8000/api/biometrics/check-duplicates/",
        "http://localhost:8000/api/biometric/check-duplicates/",
        "http://localhost:8000/api/duplicates/check/",
        "http://localhost:8000/api/citizens/check-duplicates/",
        "http://localhost:8000/biometrics/check-duplicates/",
    ]
    
    # Different data formats to try
    data_formats = [
        {
            "left_thumb_fingerprint": left_template,
            "right_thumb_fingerprint": right_template
        },
        {
            "left_thumb": left_template,
            "right_thumb": right_template
        },
        {
            "fingerprints": {
                "left_thumb": left_template,
                "right_thumb": right_template
            }
        },
        {
            "biometric_data": {
                "left_thumb_fingerprint": left_template,
                "right_thumb_fingerprint": right_template
            }
        }
    ]
    
    for endpoint in endpoints:
        print(f"\n📡 Testing endpoint: {endpoint}")
        
        for i, data_format in enumerate(data_formats, 1):
            try:
                req_data = json.dumps(data_format).encode()
                
                req = urllib.request.Request(
                    endpoint,
                    data=req_data,
                    headers={
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'User-Agent': 'GoID-Biometric-Service/1.0'
                    }
                )
                
                response = urllib.request.urlopen(req, timeout=10)
                
                if response.getcode() == 200:
                    result = json.loads(response.read().decode())
                    print(f"   ✅ Format {i}: Success - {result}")
                    return True  # Found working endpoint
                else:
                    print(f"   ⚠️ Format {i}: HTTP {response.getcode()}")
                    
            except urllib.error.HTTPError as e:
                if e.code == 401:
                    print(f"   🔐 Format {i}: Authentication required")
                elif e.code == 404:
                    print(f"   ❌ Format {i}: Endpoint not found")
                elif e.code == 405:
                    print(f"   ❌ Format {i}: Method not allowed")
                else:
                    print(f"   ⚠️ Format {i}: HTTP {e.code}")
            except urllib.error.URLError as e:
                print(f"   ❌ Format {i}: Connection error")
                break  # No point trying other formats if can't connect
            except Exception as e:
                print(f"   ❌ Format {i}: Error - {e}")
    
    return False

def test_with_authentication():
    """Test duplicate detection with authentication"""
    
    print(f"\n🔐 Testing with Authentication...")
    
    # Try to get authentication token first
    auth_endpoints = [
        "http://localhost:8000/api/auth/login/",
        "http://localhost:8000/api/token/",
        "http://localhost:8000/auth/login/",
        "http://localhost:8000/login/",
    ]
    
    # Sample credentials (adjust as needed)
    credentials = [
        {"username": "admin", "password": "admin"},
        {"email": "<EMAIL>", "password": "admin123"},
        {"username": "test", "password": "test123"},
    ]
    
    for auth_endpoint in auth_endpoints:
        print(f"\n🔑 Trying auth endpoint: {auth_endpoint}")
        
        for cred in credentials:
            try:
                auth_data = json.dumps(cred).encode()
                
                req = urllib.request.Request(
                    auth_endpoint,
                    data=auth_data,
                    headers={'Content-Type': 'application/json'}
                )
                
                response = urllib.request.urlopen(req, timeout=10)
                
                if response.getcode() == 200:
                    result = json.loads(response.read().decode())
                    token = result.get('token') or result.get('access_token') or result.get('access')
                    
                    if token:
                        print(f"   ✅ Authentication successful!")
                        print(f"   🎫 Token: {token[:20]}...")
                        
                        # Now try duplicate detection with token
                        return test_duplicate_with_token(token)
                    else:
                        print(f"   ⚠️ Login successful but no token received")
                else:
                    print(f"   ⚠️ HTTP {response.getcode()}")
                    
            except urllib.error.HTTPError as e:
                if e.code == 404:
                    print(f"   ❌ Endpoint not found")
                    break
                else:
                    print(f"   ⚠️ HTTP {e.code} with {cred['username']}")
            except Exception as e:
                print(f"   ❌ Error: {e}")
                break
    
    return False

def test_duplicate_with_token(token):
    """Test duplicate detection with authentication token"""
    
    print(f"\n🔍 Testing duplicate detection with token...")
    
    left_template = "eyJ2ZXJzaW9uIjogIjIuMCIsICJ0aHVtYl90eXBlIjogInVua25vd24iLCAibWludXRpYWVfcG9pbnRzIjogW3sieCI6IDEzMSwgInkiOiAxMDYsICJhbmdsZSI6IDMxNiwgInR5cGUiOiAicmlkZ2VfZW5kaW5nIiwgInF1YWxpdHkiOiAwLjkyOTk5OTk5OTk5OTk5OTl9XSwgImltYWdlX2RpbWVuc2lvbnMiOiB7IndpZHRoIjogMTYwLCAiaGVpZ2h0IjogNDgwfSwgInRvdGFsX3BpeGVscyI6IDc2ODAwfQ=="
    right_template = "eyJ2ZXJzaW9uIjogIjIuMCIsICJ0aHVtYl90eXBlIjogInVua25vd24iLCAibWludXRpYWVfcG9pbnRzIjogW3sieCI6IDEwMCwgInkiOiAyMDAsICJhbmdsZSI6IDE4MCwgInR5cGUiOiAiYmlmdXJjYXRpb24iLCAicXVhbGl0eSI6IDAuODV9XSwgImltYWdlX2RpbWVuc2lvbnMiOiB7IndpZHRoIjogMTYwLCAiaGVpZ2h0IjogNDgwfSwgInRvdGFsX3BpeGVscyI6IDc2ODAwfQ=="
    
    duplicate_data = {
        "left_thumb_fingerprint": left_template,
        "right_thumb_fingerprint": right_template
    }
    
    req_data = json.dumps(duplicate_data).encode()
    
    req = urllib.request.Request(
        "http://localhost:8000/api/biometrics/check-duplicates/",
        data=req_data,
        headers={
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {token}',
            'Accept': 'application/json'
        }
    )
    
    try:
        response = urllib.request.urlopen(req, timeout=15)
        
        if response.getcode() == 200:
            result = json.loads(response.read().decode())
            print(f"✅ Duplicate detection successful!")
            print(f"   Response: {result}")
            return True
        else:
            print(f"⚠️ HTTP {response.getcode()}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main function"""
    
    print("GoID Duplicate Detection Authentication Test")
    print("=" * 45)
    
    print("This will test duplicate detection with proper authentication")
    print()
    
    # Test backend connection
    test_backend_connection()
    
    # Test duplicate detection endpoints
    if test_duplicate_detection_endpoints():
        print(f"\n🎉 SUCCESS!")
        print(f"✅ Found working duplicate detection endpoint")
    else:
        print(f"\n🔐 No unauthenticated endpoints found, trying with authentication...")
        
        if test_with_authentication():
            print(f"\n🎉 SUCCESS!")
            print(f"✅ Duplicate detection working with authentication")
        else:
            print(f"\n❌ AUTHENTICATION FAILED")
            print(f"💡 Possible solutions:")
            print(f"1. Start GoID backend: docker-compose up")
            print(f"2. Check authentication credentials")
            print(f"3. Verify API endpoint URLs")
            print(f"4. Check if CORS is configured")
    
    print(f"\n💡 Next steps:")
    print(f"1. Ensure GoID backend is running")
    print(f"2. Configure proper authentication")
    print(f"3. Test with real citizen registration")
    print(f"4. Verify cross-kebele duplicate prevention")
    
    input("\nPress Enter to exit...")

if __name__ == '__main__':
    main()
