#!/usr/bin/env python3
"""
Simple test script to debug fraud detection issues.
Run this script to test fraud detection with sample data.
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:8000"
TENANT_ID = 8  # Adjust this to your kebele tenant ID

def test_fraud_detection():
    """Test fraud detection with sample citizen data."""
    
    # Sample citizen data that should trigger fraud detection
    test_data = {
        "first_name": "<PERSON>",
        "middle_name": "<PERSON><PERSON><PERSON>", 
        "last_name": "<PERSON><PERSON>",
        "date_of_birth": "1990-01-01",
        "mother_full_name": "<PERSON>",
        "father_full_name": "<PERSON>"
    }
    
    print("🔍 Testing Fraud Detection")
    print(f"   Tenant ID: {TENANT_ID}")
    print(f"   Test Data: {test_data}")
    print()
    
    # Test the fraud detection endpoint
    url = f"{BASE_URL}/api/tenants/{TENANT_ID}/citizens/test-fraud-detection/"
    
    try:
        print(f"📡 Making request to: {url}")
        response = requests.post(url, json=test_data, timeout=30)
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Success!")
            print(f"   Fraud Results: {json.dumps(result.get('fraud_results', {}), indent=2)}")
        else:
            print("❌ Error!")
            print(f"   Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {str(e)}")
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")

def test_regular_fraud_endpoint():
    """Test the regular fraud detection endpoint."""
    
    test_data = {
        "first_name": "John",
        "middle_name": "Alemu",
        "last_name": "Doe", 
        "date_of_birth": "1990-01-01"
    }
    
    print("\n🔍 Testing Regular Fraud Detection Endpoint")
    print(f"   Test Data: {test_data}")
    
    url = f"{BASE_URL}/api/tenants/{TENANT_ID}/citizens/check-registration-fraud/"
    
    try:
        print(f"📡 Making request to: {url}")
        response = requests.post(url, json=test_data, timeout=30)
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Success!")
            print(f"   Results: {json.dumps(result, indent=2)}")
        else:
            print("❌ Error!")
            print(f"   Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {str(e)}")
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")

if __name__ == "__main__":
    print("🚀 GoID Fraud Detection Test Script")
    print("=" * 50)
    
    # Test both endpoints
    test_fraud_detection()
    test_regular_fraud_endpoint()
    
    print("\n" + "=" * 50)
    print("✅ Test completed!")
