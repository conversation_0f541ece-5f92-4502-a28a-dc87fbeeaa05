#!/usr/bin/env python3
"""
Fix Paused Service Issue
"""

import subprocess
import time
import urllib.request

def fix_paused_service():
    """Fix the paused service issue"""
    
    print("🔧 Fixing Paused Service Issue...")
    print("=" * 40)
    
    service_name = "GoIDJarBridge"
    
    # Step 1: Check current status
    print("📊 Current service status:")
    result = subprocess.run(["sc", "query", service_name], capture_output=True, text=True)
    print(result.stdout)
    
    # Step 2: Try to continue (resume) the service
    print("\n🔄 Attempting to resume paused service...")
    result = subprocess.run(["sc", "continue", service_name], capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ Service resume command sent")
    else:
        print(f"⚠️ Resume failed: {result.stderr}")
        
        # Step 3: If resume fails, try stop and start
        print("\n🔄 Attempting to restart service...")
        
        # Stop service
        print("⏹️ Stopping service...")
        subprocess.run(["sc", "stop", service_name], capture_output=True)
        
        # Wait
        print("⏰ Waiting 5 seconds...")
        time.sleep(5)
        
        # Start service
        print("▶️ Starting service...")
        result = subprocess.run(["sc", "start", service_name], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Service restart successful")
        else:
            print(f"❌ Service restart failed: {result.stderr}")
    
    # Step 4: Wait and check final status
    print("\n⏰ Waiting 10 seconds for service to stabilize...")
    time.sleep(10)
    
    print("\n📊 Final service status:")
    result = subprocess.run(["sc", "query", service_name], capture_output=True, text=True)
    print(result.stdout)
    
    # Step 5: Test the service
    print("\n🧪 Testing service connectivity...")
    
    for attempt in range(3):
        try:
            response = urllib.request.urlopen("http://localhost:8001/api/device/status", timeout=10)
            
            if response.getcode() == 200:
                print("✅ Service is responding correctly!")
                print("🌐 API endpoint: http://localhost:8001/api/device/status")
                return True
            else:
                print(f"⚠️ Service responded with status: {response.getcode()}")
                
        except Exception as e:
            print(f"❌ Attempt {attempt + 1}: {e}")
            if attempt < 2:
                print("⏰ Waiting 5 seconds before retry...")
                time.sleep(5)
    
    print("\n💡 Service is still not responding. Let's check logs...")
    return False

def check_service_logs():
    """Check service logs for errors"""
    
    print("\n📄 Checking service logs...")
    
    import os
    from pathlib import Path
    
    log_dir = Path("logs")
    
    if not log_dir.exists():
        print("❌ Logs directory not found")
        return
    
    # Check output log
    output_log = log_dir / "service_output.log"
    if output_log.exists() and output_log.stat().st_size > 0:
        print(f"\n📋 Service Output Log (last 20 lines):")
        try:
            with open(output_log, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                for line in lines[-20:]:
                    print(f"   {line.strip()}")
        except Exception as e:
            print(f"❌ Error reading output log: {e}")
    
    # Check error log
    error_log = log_dir / "service_error.log"
    if error_log.exists() and error_log.stat().st_size > 0:
        print(f"\n🚨 Service Error Log (last 20 lines):")
        try:
            with open(error_log, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                for line in lines[-20:]:
                    print(f"   {line.strip()}")
        except Exception as e:
            print(f"❌ Error reading error log: {e}")
    else:
        print("📋 No error log found (this is good)")

def test_manual_startup():
    """Test if the JAR bridge works manually"""
    
    print("\n🧪 Testing manual JAR bridge startup...")
    print("💡 This will test if the issue is with the service or the JAR bridge itself")
    
    from pathlib import Path
    
    jar_bridge_script = Path("working_jar_bridge.py")
    
    if not jar_bridge_script.exists():
        print("❌ working_jar_bridge.py not found")
        return False
    
    print(f"📝 To test manually, run this command in a separate window:")
    print(f"   python {jar_bridge_script}")
    print(f"💡 If it works manually but not as a service, it's a service configuration issue")
    
    return True

def main():
    """Main function"""
    
    print("GoID JAR Bridge Service Fixer")
    print("=" * 35)
    
    # Try to fix the service
    success = fix_paused_service()
    
    if not success:
        # Check logs for clues
        check_service_logs()
        
        # Suggest manual testing
        test_manual_startup()
        
        print("\n💡 Troubleshooting suggestions:")
        print("1. Try manual startup: python working_jar_bridge.py")
        print("2. Check Windows Event Viewer for service errors")
        print("3. Reinstall service: python simple_service_installer.py uninstall && python simple_service_installer.py install")
        print("4. Check if Java is installed and accessible")
        print("5. Verify Futronic device is connected")
    else:
        print("\n🎉 Service is now working correctly!")
        print("✅ You can now use the biometric capture functionality")
    
    input("\nPress Enter to exit...")

if __name__ == '__main__':
    main()
