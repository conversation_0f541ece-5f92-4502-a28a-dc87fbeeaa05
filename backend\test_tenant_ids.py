#!/usr/bin/env python
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append('/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from tenants.models import Tenant

def check_tenant_ids():
    print('=== Checking Tenant IDs ===')
    
    tenants = Tenant.objects.all().order_by('id')
    for tenant in tenants:
        print(f'ID: {tenant.id} - Name: {tenant.name} - Schema: {tenant.schema_name} - Type: {tenant.type}')

if __name__ == '__main__':
    check_tenant_ids()
