@echo off
echo ========================================
echo   SOCKET Biometric Service
echo ========================================
echo.
echo 🎯 NO FLASK - Pure Socket Server
echo.
echo Flask was causing crashes after successful captures.
echo This version uses a pure Python socket server that
echo should be fully compatible with the Futronic SDK.
echo.
echo FEATURES:
echo - No Flask framework conflicts
echo - Pure socket-based HTTP server
echo - Direct SDK integration
echo - Same API endpoints
echo - Maximum stability
echo.

cd local-biometric-service

echo 🚀 Starting SOCKET service...
echo 📱 Service URL: http://localhost:8001
echo 🔧 Socket-based HTTP server
echo.
echo This should finally stay alive for both captures!
echo.

python socket_biometric_service.py

echo.
echo Socket service stopped.
pause
