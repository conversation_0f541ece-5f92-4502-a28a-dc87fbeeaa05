@echo off
echo ========================================
echo   AUTO-RESTART Biometric Service
echo ========================================
echo.
echo 🔄 PRACTICAL PRODUCTION SOLUTION
echo.
echo Instead of trying to prevent crashes, this service
echo automatically restarts after each successful capture.
echo This ensures the service is always available for
echo the next capture.
echo.
echo HOW IT WORKS:
echo 1. Service starts and captures left thumb
echo 2. After successful capture, service auto-restarts
echo 3. Fresh service instance captures right thumb
echo 4. Clean state for each capture
echo.
echo BENEFITS:
echo - Always available for next capture
echo - Clean state prevents conflicts
echo - No manual restart needed
echo - Production-ready approach
echo.

cd local-biometric-service

echo 🚀 Starting AUTO-RESTART service...
echo 📱 Service URL: http://localhost:8001
echo 🔄 Auto-restarts after each capture
echo.
echo This should work reliably for both thumbs!
echo.

python auto_restart_service.py

echo.
echo Auto-restart service stopped.
pause
