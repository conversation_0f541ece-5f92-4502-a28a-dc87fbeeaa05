#!/usr/bin/env python3
"""
Test script to check shared data endpoints
"""
import requests
import json

# Test the shared data endpoints
base_url = "http://localhost:8000"

def test_shared_data():
    try:
        # Test countries endpoint
        response = requests.get(f"{base_url}/api/shared/countries/")
        print(f"Countries endpoint status: {response.status_code}")
        if response.status_code == 200:
            countries = response.json()
            print(f"Countries count: {len(countries)}")
            if countries:
                print(f"First country: {countries[0]}")
        else:
            print(f"Countries response: {response.text}")
        
        # Test regions endpoint
        response = requests.get(f"{base_url}/api/shared/regions/")
        print(f"Regions endpoint status: {response.status_code}")
        if response.status_code == 200:
            regions = response.json()
            print(f"Regions count: {len(regions)}")
            if regions:
                print(f"First region: {regions[0]}")
        else:
            print(f"Regions response: {response.text}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_shared_data()
