#!/usr/bin/env python3
"""
Simple Test for JAR Bridge (no external dependencies)
"""

import urllib.request
import urllib.parse
import json
import time

def test_service_simple():
    """Test the JAR bridge service using only built-in modules"""
    
    base_url = "http://localhost:8001"
    
    print("=== SIMPLE JAR BRIDGE TEST ===")
    print(f"Testing service at: {base_url}")
    print()
    
    # Test 1: Check if service is running
    print("1. Testing if service is running...")
    try:
        response = urllib.request.urlopen(f"{base_url}/api/device/status", timeout=5)
        if response.getcode() == 200:
            data = json.loads(response.read().decode())
            print("✅ Service is running!")
            print(f"   Service type: {data.get('service_type')}")
            print(f"   Device connected: {data.get('device_connected')}")
            print(f"   Message: {data.get('message')}")
            return True
        else:
            print(f"❌ Service returned status: {response.getcode()}")
            return False
    except Exception as e:
        print(f"❌ Service is not running: {e}")
        print()
        print("💡 To start the service, run:")
        print("   python working_jar_bridge.py")
        print()
        return False

def test_jar_files():
    """Test if JAR files are present"""
    
    import os
    
    print("\n2. Testing JAR files...")
    
    required_files = [
        "fingerPrint/GonderFingerPrint.jar",
        "fingerPrint/ftrScanAPI.dll",
        "fingerPrint/ftrAnsiSdk.dll",
        "fingerPrint/ftrAnsiSDKJni.dll",
        "fingerPrint/lib/AnsiSDKLib.jar"
    ]
    
    all_present = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} - MISSING")
            all_present = False
    
    if all_present:
        print("✅ All required files are present!")
        return True
    else:
        print("❌ Some required files are missing!")
        return False

def test_java():
    """Test if Java is available"""
    
    import subprocess
    
    print("\n3. Testing Java availability...")
    
    java_commands = ['java', 'java.exe']
    
    for java_cmd in java_commands:
        try:
            result = subprocess.run([java_cmd, '-version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print(f"✅ Java found: {java_cmd}")
                # Extract version from stderr (Java outputs version to stderr)
                version_output = result.stderr.split('\n')[0] if result.stderr else "Unknown version"
                print(f"   Version: {version_output}")
                return True
        except:
            continue
    
    print("❌ Java not found in PATH")
    print("💡 Java is required to run GonderFingerPrint.jar")
    return False

if __name__ == '__main__':
    print("JAR Bridge Simple Test")
    print("=" * 30)
    
    # Test files first
    files_ok = test_jar_files()
    
    # Test Java
    java_ok = test_java()
    
    # Test service
    service_ok = test_service_simple()
    
    print("\n" + "=" * 30)
    print("TEST SUMMARY:")
    print(f"   Files present: {'✅' if files_ok else '❌'}")
    print(f"   Java available: {'✅' if java_ok else '❌'}")
    print(f"   Service running: {'✅' if service_ok else '❌'}")
    
    if files_ok and java_ok and service_ok:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ JAR Bridge is ready for use!")
        
        print("\nNext steps:")
        print("1. Start your GoID system: docker-compose up")
        print("2. Open GoID frontend: http://localhost:3000")
        print("3. Try fingerprint capture in citizen registration")
        
    elif files_ok and java_ok and not service_ok:
        print("\n⚠️ Files and Java OK, but service not running")
        print("💡 Start the service with: python working_jar_bridge.py")
        
    else:
        print("\n❌ SETUP INCOMPLETE")
        if not files_ok:
            print("❌ Missing required files")
        if not java_ok:
            print("❌ Java not available")
        if not service_ok:
            print("❌ Service not running")
    
    print("\nTest completed.")
    input("Press Enter to exit...")
