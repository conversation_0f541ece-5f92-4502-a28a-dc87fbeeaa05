#!/usr/bin/env python3
"""
Minimal Real SDK Service - Just test basic functionality
"""

import os
import sys
import time
import ctypes
import logging
from datetime import datetime
from flask import Flask, request, jsonify
from flask_cors import CORS

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Global variables
scan_api = None
ftr_api = None
device_available = False

def initialize_sdk():
    """Initialize SDK without holding device handles"""
    global scan_api, ftr_api, device_available
    
    try:
        logger.info("🎯 Initializing minimal real SDK...")
        
        # Load DLLs
        if os.path.exists('./ftrScanAPI.dll'):
            scan_api = ctypes.cdll.LoadLibrary('./ftrScanAPI.dll')
            logger.info("✅ ftrScanAPI.dll loaded")
        else:
            logger.error("❌ ftrScanAPI.dll not found")
            return False
        
        if os.path.exists('./FTRAPI.dll'):
            ftr_api = ctypes.cdll.LoadLibrary('./FTRAPI.dll')
            logger.info("✅ FTRAPI.dll loaded")
        
        # Setup basic functions
        scan_api.ftrScanOpenDevice.restype = ctypes.c_void_p
        scan_api.ftrScanCloseDevice.argtypes = [ctypes.c_void_p]
        scan_api.ftrScanCloseDevice.restype = ctypes.c_int
        
        # Test device connection briefly
        logger.info("🔌 Testing device connection...")
        device_handle = scan_api.ftrScanOpenDevice()
        
        if device_handle and device_handle != 0:
            logger.info(f"✅ Device connected! Handle: {device_handle}")
            # Close immediately - don't hold the handle
            close_result = scan_api.ftrScanCloseDevice(device_handle)
            logger.info(f"✅ Device closed: {close_result}")
            device_available = True
            return True
        else:
            logger.error("❌ Device connection failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ SDK initialization failed: {e}")
        return False

def test_finger_detection():
    """Test finger detection without holding device handle"""
    global scan_api, device_available
    
    if not device_available or not scan_api:
        return False
    
    try:
        # Open device for detection
        device_handle = scan_api.ftrScanOpenDevice()
        if not device_handle:
            return False
        
        # Setup finger detection function
        try:
            scan_api.ftrScanIsFingerPresent.argtypes = [ctypes.c_void_p, ctypes.POINTER(ctypes.c_int)]
            scan_api.ftrScanIsFingerPresent.restype = ctypes.c_int
            
            finger_present = ctypes.c_int()
            result = scan_api.ftrScanIsFingerPresent(device_handle, ctypes.byref(finger_present))
            
            # Close device immediately
            scan_api.ftrScanCloseDevice(device_handle)
            
            if result == 0:  # Success
                logger.info(f"✅ Finger detection result: {finger_present.value}")
                return finger_present.value > 0
            else:
                logger.warning(f"Finger detection failed: {result}")
                return False
                
        except Exception as e:
            logger.warning(f"Finger detection error: {e}")
            scan_api.ftrScanCloseDevice(device_handle)
            return False
            
    except Exception as e:
        logger.error(f"Detection test failed: {e}")
        return False

@app.route('/api/device/status', methods=['GET'])
def get_device_status():
    """Get device status"""
    return jsonify({
        'success': True, 
        'data': {
            'connected': device_available,
            'scan_api': scan_api is not None,
            'ftr_api': ftr_api is not None,
            'model': 'Futronic FS88H',
            'interface': 'minimal_real_sdk'
        }
    })

@app.route('/api/capture/fingerprint', methods=['POST'])
def capture_fingerprint():
    """Minimal fingerprint capture test"""
    try:
        data = request.get_json() or {}
        thumb_type = data.get('thumb_type', 'left')
        
        logger.info(f"🎯 Minimal real SDK: {thumb_type} thumb test")
        
        if not device_available:
            return jsonify({
                'success': False,
                'error': 'Device not available',
                'error_code': 'DEVICE_NOT_AVAILABLE'
            }), 400
        
        # Test finger detection
        finger_detected = test_finger_detection()
        
        if finger_detected:
            logger.info("✅ Minimal SDK: Finger detected!")
            return jsonify({
                'success': True,
                'data': {
                    'thumb_type': thumb_type,
                    'real_detection': True,
                    'capture_time': datetime.now().isoformat(),
                    'device_info': {
                        'model': 'Futronic FS88H',
                        'interface': 'minimal_real_sdk',
                        'real_device': True
                    }
                }
            })
        else:
            logger.info("❌ Minimal SDK: No finger detected")
            return jsonify({
                'success': False,
                'error': 'No finger detected on scanner',
                'error_code': 'NO_FINGER_DETECTED'
            }), 400
            
    except Exception as e:
        logger.error(f"Capture error: {e}")
        return jsonify({
            'success': False,
            'error': f'Error: {str(e)}',
            'error_code': 'API_ERROR'
        }), 500

@app.route('/', methods=['GET'])
def index():
    """Simple status page"""
    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Minimal Real SDK Service</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; background: #f0f8ff; }}
            .container {{ max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }}
            h1 {{ color: #2c3e50; text-align: center; }}
            button {{ padding: 15px 30px; margin: 10px; font-size: 16px; border: none; border-radius: 5px; cursor: pointer; background: #3498db; color: white; }}
            .result {{ margin: 20px 0; padding: 15px; border-radius: 5px; }}
            .success {{ background: #d4edda; color: #155724; }}
            .error {{ background: #f8d7da; color: #721c24; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎯 Minimal Real SDK Service</h1>
            
            <div style="background: #e7f3ff; color: #004085; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h3>✅ REAL SDK WORKING!</h3>
                <p>Device Available: {device_available}</p>
                <p>Scan API: {scan_api is not None}</p>
                <p>FTR API: {ftr_api is not None}</p>
            </div>
            
            <div style="text-align: center;">
                <h3>Test Real Finger Detection</h3>
                <button onclick="testCapture('left')">Test Left Thumb</button>
                <button onclick="testCapture('right')">Test Right Thumb</button>
                <div id="result"></div>
            </div>
        </div>

        <script>
            async function testCapture(thumbType) {{
                const button = event.target;
                const startTime = Date.now();
                
                button.textContent = 'Testing...';
                button.disabled = true;
                
                try {{
                    const response = await fetch('/api/capture/fingerprint', {{
                        method: 'POST',
                        headers: {{ 'Content-Type': 'application/json' }},
                        body: JSON.stringify({{ thumb_type: thumbType }})
                    }});
                    
                    const data = await response.json();
                    const endTime = Date.now();
                    const duration = ((endTime - startTime) / 1000).toFixed(1);
                    const resultDiv = document.getElementById('result');
                    
                    if (data.success) {{
                        resultDiv.innerHTML = `<div class="result success">
                            ✅ SUCCESS: ${{thumbType}} thumb - Real finger detected in ${{duration}}s!<br>
                            Real Detection: ${{data.data.real_detection}}
                        </div>`;
                    }} else {{
                        resultDiv.innerHTML = `<div class="result error">
                            ❌ ${{data.error}}<br>
                            Code: ${{data.error_code}}<br>
                            Time: ${{duration}}s
                        </div>`;
                    }}
                }} catch (error) {{
                    const endTime = Date.now();
                    const duration = ((endTime - startTime) / 1000).toFixed(1);
                    document.getElementById('result').innerHTML = `<div class="result error">
                        Network Error after ${{duration}}s: ${{error.message}}
                    </div>`;
                }} finally {{
                    button.textContent = `Test ${{thumbType.charAt(0).toUpperCase() + thumbType.slice(1)}} Thumb`;
                    button.disabled = false;
                }}
            }}
        </script>
    </body>
    </html>
    """

if __name__ == '__main__':
    try:
        logger.info("🎯 Starting Minimal Real SDK Service")
        
        # Initialize SDK
        if initialize_sdk():
            logger.info("✅ SDK initialized successfully")
            logger.info("🌐 Starting Flask on http://localhost:8001")
            logger.info("✅ Service is running - keep this window open!")
            
            # Start Flask
            app.run(host='0.0.0.0', port=8001, debug=False, threaded=True)
        else:
            logger.error("❌ SDK initialization failed")
            input("Press Enter to exit...")
        
    except KeyboardInterrupt:
        logger.info("Service stopped by user")
    except Exception as e:
        logger.error(f"Service error: {e}")
        input("Press Enter to exit...")
    finally:
        logger.info("Service shutdown")
