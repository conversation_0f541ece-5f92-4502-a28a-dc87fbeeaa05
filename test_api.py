#!/usr/bin/env python3
"""
Test script to check the API endpoints
"""
import requests
import json

# Test the cities endpoint
base_url = "http://localhost:8000"

# First, let's try to get a token
def get_token():
    login_data = {
        "email": "<EMAIL>",
        "password": "admin123"
    }
    
    try:
        response = requests.post(f"{base_url}/api/auth/token/", json=login_data)
        print(f"Login response status: {response.status_code}")
        print(f"Login response: {response.text}")
        
        if response.status_code == 200:
            return response.json().get('access')
        else:
            print("Failed to get token")
            return None
    except Exception as e:
        print(f"Error getting token: {e}")
        return None

# Test the cities endpoint
def test_cities_endpoint():
    token = get_token()
    if not token:
        print("Cannot test without token")
        return
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Test GET request first
    try:
        response = requests.get(f"{base_url}/api/tenants/cities/", headers=headers)
        print(f"GET /api/tenants/cities/ status: {response.status_code}")
        print(f"GET response: {response.text}")
    except Exception as e:
        print(f"Error with GET request: {e}")
    
    # Test POST request
    test_data = {
        "city_name": "Test City",
        "tenant_name": "Test City Administration",
        "country": 1,
        "region": 1,
        "contact_email": "<EMAIL>",
        "contact_phone": "+251911123456",
        "is_active": True
    }
    
    try:
        response = requests.post(f"{base_url}/api/tenants/cities/", json=test_data, headers=headers)
        print(f"POST /api/tenants/cities/ status: {response.status_code}")
        print(f"POST response: {response.text}")
    except Exception as e:
        print(f"Error with POST request: {e}")

if __name__ == "__main__":
    test_cities_endpoint()
