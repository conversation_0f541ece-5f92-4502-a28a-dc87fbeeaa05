"""
Management command to create custom groups with specific permissions.
Allows flexible permission assignment for different organizational needs.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission
from django.db import transaction
from users.models_groups import TenantGroup, GroupMembership
from tenants.models import Tenant
from tenants.models_workflow import TenantPermissionOverride


class Command(BaseCommand):
    help = 'Create custom groups with specific permissions'

    def add_arguments(self, parser):
        parser.add_argument(
            '--group-name',
            type=str,
            required=True,
            help='Name of the custom group to create',
        )
        parser.add_argument(
            '--description',
            type=str,
            default='',
            help='Description of the custom group',
        )
        parser.add_argument(
            '--permissions',
            type=str,
            nargs='+',
            required=True,
            help='List of permission codenames to assign',
        )
        parser.add_argument(
            '--tenant',
            type=str,
            help='Tenant name/slug for tenant-specific group',
        )
        parser.add_argument(
            '--base-group',
            type=str,
            help='Base system group to extend (clerk, kebele_leader, etc.)',
        )
        parser.add_argument(
            '--group-type',
            type=str,
            choices=['operational', 'administrative', 'functional', 'custom'],
            default='custom',
            help='Type of group',
        )
        parser.add_argument(
            '--level',
            type=int,
            default=25,
            help='Permission level (1-100)',
        )

    def handle(self, *args, **options):
        group_name = options['group_name']
        description = options['description']
        permissions = options['permissions']
        tenant_name = options.get('tenant')
        base_group = options.get('base_group')
        group_type = options['group_type']
        level = options['level']

        self.stdout.write(
            self.style.SUCCESS(f'🔧 Creating custom group: {group_name}')
        )

        try:
            with transaction.atomic():
                # Create the custom group
                group_info = self.create_custom_group(
                    group_name, description, permissions, 
                    tenant_name, base_group, group_type, level
                )
                
                self.stdout.write(
                    self.style.SUCCESS(f'✅ Successfully created custom group: {group_name}')
                )
                self.stdout.write(f'   Group ID: {group_info["group"].id}')
                self.stdout.write(f'   Permissions: {len(group_info["permissions"])}')
                self.stdout.write(f'   Type: {group_type}')
                self.stdout.write(f'   Level: {level}')
                
                if tenant_name:
                    self.stdout.write(f'   Tenant: {tenant_name}')

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Failed to create custom group: {e}')
            )

    def create_custom_group(self, group_name, description, permissions, 
                          tenant_name=None, base_group=None, group_type='custom', level=25):
        """Create a custom group with specified permissions."""
        
        # Get or create Django Group
        django_group, created = Group.objects.get_or_create(name=group_name)
        
        if not created:
            self.stdout.write(
                self.style.WARNING(f'⚠️ Group {group_name} already exists, updating...')
            )
        
        # Get base permissions if extending a system group
        base_permissions = []
        if base_group:
            try:
                base_django_group = Group.objects.get(name=base_group)
                base_permissions = list(base_django_group.permissions.all())
                self.stdout.write(f'   Extending base group: {base_group} ({len(base_permissions)} permissions)')
            except Group.DoesNotExist:
                self.stdout.write(
                    self.style.WARNING(f'⚠️ Base group {base_group} not found, creating standalone group')
                )
        
        # Get requested permissions
        requested_permissions = []
        for perm_codename in permissions:
            try:
                permission = Permission.objects.get(codename=perm_codename)
                requested_permissions.append(permission)
                self.stdout.write(f'   ✅ Added permission: {perm_codename}')
            except Permission.DoesNotExist:
                self.stdout.write(
                    self.style.WARNING(f'   ⚠️ Permission not found: {perm_codename}')
                )
        
        # Combine base and requested permissions
        all_permissions = base_permissions + requested_permissions
        
        # Remove duplicates
        unique_permissions = []
        seen_codenames = set()
        for perm in all_permissions:
            if perm.codename not in seen_codenames:
                unique_permissions.append(perm)
                seen_codenames.add(perm.codename)
        
        # Assign permissions to Django group
        django_group.permissions.set(unique_permissions)
        
        # Create or update TenantGroup
        tenant_group, created = TenantGroup.objects.get_or_create(
            group=django_group,
            defaults={
                'description': description or f'Custom group: {group_name}',
                'is_system_group': False,
                'group_type': group_type,
                'level': level,
            }
        )
        
        if not created:
            # Update existing TenantGroup
            tenant_group.description = description or tenant_group.description
            tenant_group.group_type = group_type
            tenant_group.level = level
            tenant_group.save()
        
        # Create tenant-specific permission override if tenant specified
        if tenant_name:
            try:
                tenant = Tenant.objects.get(name=tenant_name)
                override, created = TenantPermissionOverride.objects.get_or_create(
                    tenant=tenant,
                    group_name=group_name,
                    defaults={
                        'additional_permissions': permissions,
                        'reason': f'Custom group creation for {tenant_name}'
                    }
                )
                
                if not created:
                    override.additional_permissions = permissions
                    override.save()
                
                self.stdout.write(f'   ✅ Created tenant override for: {tenant_name}')
                
            except Tenant.DoesNotExist:
                self.stdout.write(
                    self.style.WARNING(f'   ⚠️ Tenant not found: {tenant_name}')
                )
        
        return {
            'group': django_group,
            'tenant_group': tenant_group,
            'permissions': unique_permissions
        }


# Example usage commands:
EXAMPLE_COMMANDS = """
# Create autonomous kebele clerk with printing permissions
python manage.py create_custom_group \\
    --group-name "autonomous_clerk" \\
    --description "Clerk with local ID card printing capabilities" \\
    --base-group "clerk" \\
    --permissions "print_id_cards" \\
    --group-type "operational" \\
    --level 15

# Create document specialist role
python manage.py create_custom_group \\
    --group-name "document_specialist" \\
    --description "Specialized role for document verification" \\
    --permissions "verify_documents" "view_citizens_list" "view_citizen_details" \\
    --group-type "functional" \\
    --level 20

# Create kebele-specific printing officer
python manage.py create_custom_group \\
    --group-name "printing_officer" \\
    --description "Officer responsible for ID card printing" \\
    --permissions "print_id_cards" "view_id_cards_list" "approve_id_cards" \\
    --tenant "kebele15" \\
    --group-type "functional" \\
    --level 30

# Create senior clerk with additional permissions
python manage.py create_custom_group \\
    --group-name "senior_clerk" \\
    --description "Senior clerk with enhanced capabilities" \\
    --base-group "clerk" \\
    --permissions "verify_documents" "approve_id_cards" \\
    --group-type "operational" \\
    --level 20
"""
