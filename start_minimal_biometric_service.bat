@echo off
echo ========================================
echo   GoID Minimal Biometric Service
echo ========================================
echo.
echo Focused on core fingerprint capture
echo functionality with retry logic.
echo.

cd local-biometric-service

echo 🚀 Starting MINIMAL service on port 8001...
echo 📱 Service URL: http://localhost:8001
echo 🌐 Health Check: http://localhost:8001/api/health
echo.
echo ⚠️  MINIMAL MODE:
echo    - Multiple buffer size attempts
echo    - Retry logic for failed captures
echo    - 3-second delay for finger placement
echo    - Simplified error handling
echo.
echo 📋 USAGE:
echo    1. Start this service
echo    2. Test at http://localhost:8001
echo    3. Place finger when prompted
echo    4. Service will try multiple approaches
echo.

python minimal_working_service.py

echo.
echo Minimal service stopped.
pause
