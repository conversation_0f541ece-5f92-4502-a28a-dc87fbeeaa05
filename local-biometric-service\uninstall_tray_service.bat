@echo off
echo ========================================
echo   GoID Biometric Tray Service Uninstaller
echo ========================================
echo.
echo This will remove the GoID Biometric Service from your system.
echo.

REM Stop the service if running
echo Stopping GoID Biometric Service...
taskkill /f /im "python.exe" /fi "WINDOWTITLE eq GoID*" 2>nul
taskkill /f /im "pythonw.exe" /fi "WINDOWTITLE eq GoID*" 2>nul

REM Remove from Windows startup
echo Removing from Windows startup...
reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "GoID_Biometric_Service" /f 2>nul

if %errorLevel% equ 0 (
    echo ✅ Removed from Windows startup
) else (
    echo ⚠️ Not found in Windows startup (may already be removed)
)

REM Remove desktop shortcut
echo Removing desktop shortcut...
set DESKTOP=%USERPROFILE%\Desktop
del "%DESKTOP%\GoID Biometric Service.lnk" 2>nul

if exist "%DESKTOP%\GoID Biometric Service.lnk" (
    echo ⚠️ Could not remove desktop shortcut
) else (
    echo ✅ Desktop shortcut removed
)

REM Remove startup batch file if it exists
set SERVICE_DIR=%~dp0
del "%SERVICE_DIR%tray_service_startup.bat" 2>nul

echo.
echo ========================================
echo   Uninstallation Complete!
echo ========================================
echo.
echo ✅ GoID Biometric Service has been removed from:
echo - Windows startup
echo - Desktop shortcuts
echo - System tray
echo.
echo Note: Service files remain in the current directory.
echo You can manually delete this folder if no longer needed.
echo.
echo The service will no longer start automatically with Windows.
echo.
pause
