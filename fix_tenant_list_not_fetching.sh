#!/bin/bash

# Fix Tenant List Not Fetching Script
# This script diagnoses and fixes the specific tenant list fetching issue

echo "🔧 Fixing Tenant List Not Fetching Issue"
echo "========================================"

# Step 1: Diagnose the specific issue
echo "📍 Step 1: Diagnosing the specific issue..."
python debug_tenant_list_specific.py

echo ""
echo "📍 Step 2: Checking database and permissions..."
chmod +x check_tenant_database.sh
./check_tenant_database.sh

# Step 3: Fix common issues
echo ""
echo "📍 Step 3: Applying common fixes..."

# Fix 1: Ensure superuser exists and is properly configured
echo "Ensuring superuser is properly configured..."
docker-compose exec -T backend python manage.py shell << 'EOF'
from django.contrib.auth import get_user_model

User = get_user_model()

try:
    # Get or create admin user
    admin_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'username': 'admin',
            'first_name': 'Super',
            'last_name': 'Admin',
            'is_superuser': True,
            'is_staff': True,
            'is_active': True
        }
    )
    
    # Ensure user is properly configured
    admin_user.is_superuser = True
    admin_user.is_staff = True
    admin_user.is_active = True
    admin_user.set_password('admin123')
    admin_user.save()
    
    if created:
        print("✅ Admin user created")
    else:
        print("✅ Admin user updated")
        
    print(f"User: {admin_user.email}")
    print(f"Superuser: {admin_user.is_superuser}")
    print(f"Staff: {admin_user.is_staff}")
    print(f"Active: {admin_user.is_active}")
    
except Exception as e:
    print(f"❌ Error with admin user: {e}")
EOF

# Fix 2: Create test tenants if none exist
echo ""
echo "Creating test tenants if database is empty..."
docker-compose exec -T backend python manage.py shell << 'EOF'
from tenants.models import Tenant, Domain

tenant_count = Tenant.objects.count()
print(f"Current tenant count: {tenant_count}")

if tenant_count == 0:
    print("Creating test tenants...")
    
    try:
        # Create city tenant
        city = Tenant.objects.create(
            name="Test City",
            type="city",
            schema_name="city_test",
            is_active=True
        )
        Domain.objects.create(
            domain="testcity.goid.local",
            tenant=city,
            is_primary=True
        )
        print(f"✅ Created city: {city.name}")
        
        # Create subcity tenant
        subcity = Tenant.objects.create(
            name="Test Subcity",
            type="subcity", 
            schema_name="subcity_test",
            parent=city,
            is_active=True
        )
        Domain.objects.create(
            domain="testsubcity.goid.local",
            tenant=subcity,
            is_primary=True
        )
        print(f"✅ Created subcity: {subcity.name}")
        
        # Create kebele tenant
        kebele = Tenant.objects.create(
            name="Test Kebele",
            type="kebele",
            schema_name="kebele_test", 
            parent=subcity,
            is_active=True
        )
        Domain.objects.create(
            domain="testkebele.goid.local",
            tenant=kebele,
            is_primary=True
        )
        print(f"✅ Created kebele: {kebele.name}")
        
        print(f"✅ Created {Tenant.objects.count()} test tenants")
        
    except Exception as e:
        print(f"❌ Error creating tenants: {e}")
else:
    print("✅ Tenants already exist")
EOF

# Fix 3: Test the API endpoint after fixes
echo ""
echo "📍 Step 4: Testing API endpoint after fixes..."

# Get auth token
echo "Getting authentication token..."
AUTH_RESPONSE=$(curl -s -X POST http://10.139.8.141:8000/api/auth/token/ \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}')

if echo "$AUTH_RESPONSE" | grep -q "access"; then
    echo "✅ Authentication successful"
    
    # Extract token
    TOKEN=$(echo "$AUTH_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin)['access'])" 2>/dev/null)
    
    if [ ! -z "$TOKEN" ]; then
        echo "Testing tenant endpoint..."
        TENANT_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
          -H "Origin: http://10.139.8.141:3000" \
          http://10.139.8.141:8000/api/tenants/)
        
        echo "Tenant endpoint response:"
        echo "$TENANT_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$TENANT_RESPONSE"
        
        if echo "$TENANT_RESPONSE" | grep -q -E '\[|\{'; then
            echo "✅ Tenant endpoint returning data"
        else
            echo "❌ Tenant endpoint not returning proper data"
        fi
    else
        echo "❌ Could not extract token"
    fi
else
    echo "❌ Authentication failed"
    echo "Response: $AUTH_RESPONSE"
fi

# Step 5: Restart frontend to clear any caching issues
echo ""
echo "📍 Step 5: Restarting frontend to clear cache..."
docker-compose restart frontend
sleep 20

# Step 6: Final test
echo ""
echo "📍 Step 6: Final comprehensive test..."
python debug_tenant_list_specific.py

echo ""
echo "✅ Tenant list fix completed!"
echo ""
echo "🧪 Manual testing steps:"
echo "1. Open: http://10.139.8.141:3000/"
echo "2. Login with: <EMAIL> / admin123"
echo "3. Navigate to: Tenants section"
echo "4. Check: Should see list of tenants"
echo ""
echo "🔍 Expected results:"
echo "- ✅ Login works without errors"
echo "- ✅ Tenant list page loads"
echo "- ✅ Shows at least test tenants"
echo "- ✅ No ERR_EMPTY_RESPONSE errors"
echo "- ✅ No 403/401 permission errors"
echo ""
echo "💡 If still not working:"
echo "1. Check browser console for specific errors"
echo "2. Check network tab for failed requests"
echo "3. Verify user has superuser status in Django admin"
echo "4. Check if tenants exist: http://10.139.8.141:8000/admin/tenants/tenant/"
echo ""
echo "🚨 Common remaining issues:"
echo "- Frontend caching old failed requests"
echo "- Browser CORS cache issues"
echo "- JWT token not including proper user info"
echo "- ViewSet queryset filtering out all tenants"
echo ""
echo "Quick browser fixes:"
echo "- Hard refresh: Ctrl+Shift+R"
echo "- Clear cache and reload"
echo "- Try incognito/private mode"
