# GoID Biometric Service - Deployment Guide

## 🎯 Overview

This guide covers deploying the GoID Biometric Service to multiple kebele workstations using three different approaches:

1. **System Tray Application** - User-friendly desktop app
2. **Standalone Executable** - PyInstaller packaged app
3. **Windows Service** - Background service using NSSM

## 📦 Deployment Options

### Option 1: System Tray Application (Recommended)

**Best for:** User-friendly deployment with visual feedback

**Features:**
- ✅ System tray icon with status indication
- ✅ Right-click menu for service control
- ✅ Auto-restart on crash
- ✅ Web interface access
- ✅ Easy start/stop/restart
- ✅ Status monitoring

**Deployment:**
```bash
# Start the tray service
python tray_service.py
```

**User Experience:**
- Green icon = Service running
- Red icon = Service stopped/error
- Gray icon = Starting/stopping
- Right-click for menu options
- Left-click for status info

### Option 2: Standalone Executable

**Best for:** Environments without Python installed

**Features:**
- ✅ No Python dependencies
- ✅ Single executable file
- ✅ Professional installer
- ✅ Desktop shortcuts
- ✅ Start menu integration
- ✅ Auto-startup option

**Build Process:**
```bash
# Build the executable package
python build_package.py
```

**Deployment:**
1. Copy `deployment` folder to target machine
2. Run `install.bat` as Administrator
3. Service installs and starts automatically

### Option 3: Windows Service

**Best for:** Server environments requiring background operation

**Features:**
- ✅ Runs as Windows Service
- ✅ Auto-start with Windows
- ✅ Service recovery on crash
- ✅ Centralized logging
- ✅ No user interaction required
- ✅ Enterprise-grade reliability

**Installation:**
```bash
# Install as Windows Service
create_windows_service.bat
```

## 🚀 Quick Start Deployment

### For Single Kebele (Development/Testing)

```bash
# Option 1: Tray Application
cd local-biometric-service
python tray_service.py
```

### For Multiple Kebeles (Production)

```bash
# Step 1: Build deployment package
cd local-biometric-service
python build_package.py

# Step 2: Deploy to each kebele
# Copy 'deployment' folder to each workstation
# Run install.bat on each machine
```

## 📋 Pre-Deployment Checklist

### Hardware Requirements
- ✅ Windows 10/11 workstation
- ✅ Futronic FS88H fingerprint scanner
- ✅ USB port for device connection
- ✅ Network connectivity to GoID server

### Software Requirements
- ✅ Python 3.8+ (for source deployment)
- ✅ Futronic SDK DLLs (ftrScanAPI.dll, ftrMathAPI.dll)
- ✅ Administrator privileges (for service installation)

### Network Requirements
- ✅ Port 8001 available for biometric service
- ✅ Access to GoID server (typically port 8000)
- ✅ No firewall blocking localhost:8001

## 🔧 Installation Steps

### Method 1: Tray Application

1. **Prepare Files:**
   ```
   kebele-workstation/
   ├── tray_service.py
   ├── improved_biometric_service.py
   ├── ftrScanAPI.dll
   ├── ftrMathAPI.dll
   └── logs/ (created automatically)
   ```

2. **Install Dependencies:**
   ```bash
   pip install pystray Pillow flask flask-cors
   ```

3. **Start Service:**
   ```bash
   python tray_service.py
   ```

4. **Verify Installation:**
   - Check system tray for green fingerprint icon
   - Visit http://localhost:8001
   - Test fingerprint capture

### Method 2: Standalone Executable

1. **Build Package:**
   ```bash
   python build_package.py
   ```

2. **Deploy to Kebele:**
   - Copy `deployment` folder to workstation
   - Run `install.bat` as Administrator
   - Follow installation prompts

3. **Verify Installation:**
   - Check desktop shortcut
   - Check Start Menu > GoID
   - Service should auto-start

### Method 3: Windows Service

1. **Prepare Environment:**
   - Ensure Python is installed
   - Copy all service files to target directory
   - Run as Administrator

2. **Install Service:**
   ```bash
   create_windows_service.bat
   ```

3. **Verify Installation:**
   - Check Windows Services (services.msc)
   - Look for "GoID Biometric Service"
   - Status should be "Running"

## 🔍 Troubleshooting

### Common Issues

**Service Won't Start:**
- Check if port 8001 is available
- Verify Futronic DLLs are present
- Check Windows Event Viewer
- Review service logs

**Device Not Detected:**
- Ensure Futronic FS88H is connected
- Check USB connection
- Verify device drivers
- Test with Futronic software

**Frontend Can't Connect:**
- Verify service is running on port 8001
- Check firewall settings
- Test with http://localhost:8001
- Review browser console for errors

### Log Locations

**Tray Application:**
- `logs/tray_service.log`

**Standalone Executable:**
- `%PROGRAMFILES%/GoID/BiometricService/logs/`

**Windows Service:**
- `logs/service_stdout.log`
- `logs/service_stderr.log`

## 📊 Monitoring and Maintenance

### Health Checks

**Manual Verification:**
1. Visit http://localhost:8001
2. Check device status (should show "Connected")
3. Test fingerprint capture
4. Verify web interface functionality

**Automated Monitoring:**
- Service auto-restarts on crash
- Logs all operations
- Status visible in system tray
- Web interface provides real-time status

### Updates and Maintenance

**Updating Service:**
1. Stop current service
2. Replace service files
3. Restart service
4. Verify functionality

**Log Rotation:**
- Logs automatically rotate daily
- Maximum 10MB per log file
- Old logs archived automatically

## 🏢 Enterprise Deployment

### Mass Deployment Script

```batch
@echo off
REM Deploy to multiple workstations
for /f %%i in (kebele_workstations.txt) do (
    echo Deploying to %%i
    xcopy /s deployment \\%%i\c$\GoID\
    psexec \\%%i -s cmd /c "cd c:\GoID && install.bat"
)
```

### Group Policy Deployment

1. Create MSI package using WiX or similar
2. Deploy via Group Policy Software Installation
3. Configure auto-startup via Group Policy
4. Monitor via SCCM or similar tools

## 🔒 Security Considerations

- Service runs with minimal privileges
- No network services exposed except localhost:8001
- Biometric data encrypted in transit
- Local logs contain no sensitive data
- Service isolated from other applications

## 📞 Support

For deployment issues:
1. Check logs first
2. Verify hardware connections
3. Test with diagnostic tools
4. Contact technical support with log files

---

**Deployment Status Checklist:**
- [ ] Hardware verified
- [ ] Software installed
- [ ] Service running
- [ ] Device connected
- [ ] Frontend integration tested
- [ ] Monitoring configured
- [ ] Documentation provided to users
