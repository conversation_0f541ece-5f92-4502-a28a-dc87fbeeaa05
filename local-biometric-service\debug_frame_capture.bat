@echo off
echo ========================================
echo   DEBUG ftrScanGetFrame() FUNCTION
echo   ESSENTIAL for Real Fingerprint Capture
echo ========================================
echo.

echo ⚠️  IMPORTANT: ftrScanGetFrame() is THE CORE FUNCTION
echo    - This function captures the actual fingerprint image
echo    - Without it, we get NO real biometric data
echo    - We MUST make this function work - no alternatives!
echo.

echo Checking Python installation...
python --version
if errorlevel 1 (
    echo ERROR: Python not found. Please install Python 3.7+
    pause
    exit /b 1
)

echo.
echo Checking required DLL files...
if not exist "ftrScanAPI.dll" (
    echo ERROR: ftrScanAPI.dll not found
    echo Please ensure Futronic SDK files are in the current directory
    pause
    exit /b 1
)

echo SUCCESS: ftrScanAPI.dll found

echo.
echo ========================================
echo   DEBUGGING STRATEGY
echo ========================================
echo.
echo This tool will test ftrScanGetFrame() with:
echo   1. Different buffer sizes (1KB, 320x480, 1MB)
echo   2. Multiple calling approaches
echo   3. With and without finger detection
echo   4. Interactive testing with your finger
echo.
echo The goal is to find the EXACT approach that works
echo so we can capture REAL fingerprint data.
echo.

pause

echo Starting ftrScanGetFrame() debug analysis...
echo.

python debug_frame_capture_only.py

echo.
echo ========================================
echo   DEBUG COMPLETE
echo ========================================
echo.
echo Review the results above to identify:
echo   - Which buffer size works
echo   - Which calling approach succeeds
echo   - Whether finger detection helps
echo   - What the actual error/crash point is
echo.
echo This information is CRITICAL because ftrScanGetFrame()
echo is the ONLY way to get real fingerprint data!
echo.
pause
