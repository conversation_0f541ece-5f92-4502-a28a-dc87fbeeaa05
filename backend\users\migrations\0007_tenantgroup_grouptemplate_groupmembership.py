# Generated by Django 4.2.7 on 2025-06-15 12:32

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('tenants', '0009_kebele_logo_subcity_logo'),
        ('auth', '0012_alter_user_first_name_max_length'),
        ('users', '0006_alter_role_codename'),
    ]

    operations = [
        migrations.CreateModel(
            name='TenantGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField(blank=True, help_text="Detailed description of the group's purpose")),
                ('is_system_group', models.BooleanField(default=False, help_text='True for system-defined groups')),
                ('group_type', models.CharField(choices=[('administrative', 'Administrative'), ('operational', 'Operational'), ('functional', 'Functional'), ('custom', 'Custom')], default='custom', max_length=50)),
                ('level', models.IntegerField(default=0, help_text='Group hierarchy level (higher = more authority)')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_groups', to=settings.AUTH_USER_MODEL)),
                ('group', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='tenant_group', to='auth.group')),
                ('parent_groups', models.ManyToManyField(blank=True, help_text='Parent groups that this group inherits from', related_name='child_groups', to='users.tenantgroup')),
                ('tenant', models.ForeignKey(blank=True, help_text='Tenant that owns this group (null for global groups)', null=True, on_delete=django.db.models.deletion.CASCADE, to='tenants.tenant')),
            ],
            options={
                'verbose_name': 'Tenant Group',
                'verbose_name_plural': 'Tenant Groups',
                'ordering': ['-level', 'group__name'],
                'unique_together': {('group', 'tenant')},
            },
        ),
        migrations.CreateModel(
            name='GroupTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField()),
                ('group_type', models.CharField(choices=[('administrative', 'Administrative'), ('operational', 'Operational'), ('functional', 'Functional'), ('custom', 'Custom')], max_length=50)),
                ('level', models.IntegerField(default=0)),
                ('is_system_template', models.BooleanField(default=False)),
                ('tenant_types', models.JSONField(blank=True, default=list, help_text='List of tenant types this template applies to')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('permissions', models.ManyToManyField(blank=True, to='auth.permission')),
            ],
            options={
                'verbose_name': 'Group Template',
                'verbose_name_plural': 'Group Templates',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='GroupMembership',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('assigned_at', models.DateTimeField(auto_now_add=True)),
                ('reason', models.TextField(blank=True, help_text='Reason for group assignment')),
                ('is_primary', models.BooleanField(default=False, help_text='Primary group for the user')),
                ('is_active', models.BooleanField(default=True)),
                ('expires_at', models.DateTimeField(blank=True, help_text='Optional expiration date', null=True)),
                ('assigned_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_memberships', to=settings.AUTH_USER_MODEL)),
                ('group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='users.tenantgroup')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Group Membership',
                'verbose_name_plural': 'Group Memberships',
                'ordering': ['-is_primary', '-assigned_at'],
                'unique_together': {('user', 'group')},
            },
        ),
    ]
