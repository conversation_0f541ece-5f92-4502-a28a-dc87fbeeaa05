#!/usr/bin/env python
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append('/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from django_tenants.utils import schema_context
from django.test import Client
from django.urls import reverse
from rest_framework_simplejwt.tokens import RefreshToken
import json

User = get_user_model()

def test_user_endpoint():
    print('=== Testing User API Endpoint ===')
    
    # Test with clerk user in kebele15
    with schema_context('kebele_kebele15'):
        try:
            user = User.objects.get(email='<EMAIL>')
            print(f'\nTesting user endpoint for: {user.email} (ID: {user.id})')
            
            # Create JWT token for authentication
            refresh = RefreshToken.for_user(user)
            access_token = str(refresh.access_token)
            
            # Test with Django test client (simulates the frontend request)
            client = Client()
            
            # Set the host header to match the tenant
            response = client.get(
                f'/api/users/{user.id}/',
                HTTP_AUTHORIZATION=f'Bearer {access_token}',
                HTTP_HOST='kebele15.localhost:8000'
            )
            
            print(f'Response status: {response.status_code}')
            
            if response.status_code == 200:
                try:
                    data = json.loads(response.content)
                    print(f'✅ User endpoint working!')
                    print(f'User ID: {data.get("id")}')
                    print(f'Email: {data.get("email")}')
                    print(f'Role: {data.get("role")}')
                    print(f'Permissions: {len(data.get("permissions", []))}')
                    
                    if data.get('permissions'):
                        print(f'First 5 permissions: {[p["codename"] for p in data.get("permissions", [])[:5]]}')
                    
                except json.JSONDecodeError as e:
                    print(f'❌ JSON decode error: {e}')
                    print(f'Response content: {response.content}')
            else:
                print(f'❌ User endpoint failed')
                print(f'Response content: {response.content}')
                
            # Test the users list endpoint
            print(f'\n--- Testing Users List Endpoint ---')
            response = client.get(
                '/api/users/',
                HTTP_AUTHORIZATION=f'Bearer {access_token}',
                HTTP_HOST='kebele15.localhost:8000'
            )
            
            print(f'Users list response status: {response.status_code}')
            
            if response.status_code == 200:
                try:
                    data = json.loads(response.content)
                    if isinstance(data, dict) and 'results' in data:
                        users = data['results']
                        print(f'✅ Users list working! Found {len(users)} users')
                    elif isinstance(data, list):
                        print(f'✅ Users list working! Found {len(data)} users')
                    else:
                        print(f'✅ Users list working! Response type: {type(data)}')
                except json.JSONDecodeError as e:
                    print(f'❌ JSON decode error: {e}')
            else:
                print(f'❌ Users list failed')
                print(f'Response content: {response.content}')
                
        except User.DoesNotExist:
            print('<EMAIL> not found')
        except Exception as e:
            print(f'Error: {e}')
            import traceback
            traceback.print_exc()

    # Test with different tenant to see if routing works
    print(f'\n--- Testing Cross-Tenant Access ---')
    with schema_context('subcity_subcity1'):
        try:
            user = User.objects.get(email='<EMAIL>')
            print(f'Testing user endpoint for: {user.email} (ID: {user.id})')
            
            # Create JWT token for authentication
            refresh = RefreshToken.for_user(user)
            access_token = str(refresh.access_token)
            
            client = Client()
            response = client.get(
                f'/api/users/{user.id}/',
                HTTP_AUTHORIZATION=f'Bearer {access_token}',
                HTTP_HOST='subcity1.localhost:8000'
            )
            
            print(f'Subcity user response status: {response.status_code}')
            
            if response.status_code == 200:
                try:
                    data = json.loads(response.content)
                    print(f'✅ Subcity user endpoint working!')
                    print(f'Permissions: {len(data.get("permissions", []))}')
                except json.JSONDecodeError as e:
                    print(f'❌ JSON decode error: {e}')
            else:
                print(f'❌ Subcity user endpoint failed')
                
        except User.DoesNotExist:
            print('<EMAIL> not found')
        except Exception as e:
            print(f'Error: {e}')

if __name__ == '__main__':
    test_user_endpoint()
