import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  Grid,
  Alert,
  CircularProgress,
  Chip,
  Container,
  Paper,
  Divider,
  Avatar,
  IconButton,
} from '@mui/material';
import {
  CreditCard as IDCardIcon,
  Public as PublicIcon,
  CheckCircle as CheckIcon,
  Cancel as CancelIcon,
  Info as InfoIcon,
  LocationCity as CityIcon,
  Business as BusinessIcon,
  Home as HomeIcon,
  ArrowForward as ArrowIcon,
  Security as SecurityIcon,
  Assignment as AssignmentIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { publicAxios } from '../../utils/axios';
import { useTheme as useAppTheme } from '../../contexts/ThemeContext';

const PublicServicePortal = () => {
  const [tenants, setTenants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const { tenantColors } = useAppTheme();

  useEffect(() => {
    fetchPublicTenants();
  }, []);

  const fetchPublicTenants = async () => {
    try {
      setLoading(true);

      // First check if public services are enabled globally
      const systemSettingsResponse = await publicAxios.get('/api/tenants/system-settings/');
      const systemSettings = systemSettingsResponse.data;

      if (!systemSettings.public_services_enabled) {
        setError('Public services are currently disabled system-wide');
        setTenants([]);
        return;
      }

      // Get all tenants if public services are enabled globally
      const tenantsResponse = await publicAxios.get('/api/tenants/');
      const allTenants = tenantsResponse.data.results || tenantsResponse.data;

      // Show all tenants when public services are globally enabled
      console.log('🔍 PublicServicePortal: All tenants fetched:', allTenants);
      console.log('🔍 PublicServicePortal: Tenants with public access:', 
        allTenants.filter(t => t.public_id_application_enabled || t.public_service_portal_enabled));
      setTenants(allTenants);
      setError('');
    } catch (err) {
      setError('Failed to fetch available services');
      console.error('Error fetching services:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleServiceAccess = (tenant, serviceType) => {
    if (serviceType === 'id_application') {
      // Navigate to public ID card application
      navigate(`/services/idcard?tenant=${tenant.id}`);
    } else if (serviceType === 'service_portal') {
      // Navigate to public service portal
      navigate(`/services/portal?tenant=${tenant.id}`);
    }
  };

  const getTenantIcon = (tenantType) => {
    switch (tenantType) {
      case 'city':
        return <CityIcon sx={{ fontSize: 40 }} />;
      case 'subcity':
        return <BusinessIcon sx={{ fontSize: 40 }} />;
      case 'kebele':
        return <HomeIcon sx={{ fontSize: 40 }} />;
      default:
        return <PublicIcon sx={{ fontSize: 40 }} />;
    }
  };

  const TenantServiceCard = ({ tenant }) => {
    const hasIDApplication = tenant.public_id_application_enabled;
    const hasServicePortal = tenant.public_service_portal_enabled;

    console.log(`🔍 TenantServiceCard ${tenant.name}: hasIDApplication=${hasIDApplication}, hasServicePortal=${hasServicePortal}`);

    if (!hasIDApplication && !hasServicePortal) {
      console.log(`❌ TenantServiceCard ${tenant.name}: No public access - card will not render`);
      return null;
    }

    const primaryColor = tenant.primary_color || tenantColors.primary;
    const secondaryColor = tenant.secondary_color || tenantColors.secondary;

    return (
      <Card
        sx={{
          mb: 4,
          borderRadius: 4,
          overflow: 'hidden',
          background: 'linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)',
          border: `2px solid ${primaryColor}20`,
          boxShadow: `0 8px 32px ${primaryColor}15`,
          '&:hover': {
            boxShadow: `0 16px 48px ${primaryColor}25`,
            transform: 'translateY(-8px)',
            border: `2px solid ${primaryColor}40`,
          },
          transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
        }}
      >
        {/* Hero Header with Gradient */}
        <Box
          sx={{
            background: `linear-gradient(135deg, ${primaryColor} 0%, ${secondaryColor} 100%)`,
            color: 'white',
            p: 4,
            position: 'relative',
            overflow: 'hidden',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'url(/images/pattern.svg)',
              backgroundSize: 'cover',
              opacity: 0.1,
            },
          }}
        >
          <Box sx={{ position: 'relative', zIndex: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Avatar
                sx={{
                  width: 64,
                  height: 64,
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  mr: 3,
                  backdropFilter: 'blur(10px)',
                  border: '2px solid rgba(255, 255, 255, 0.3)',
                }}
              >
                {getTenantIcon(tenant.type)}
              </Avatar>
              <Box sx={{ flexGrow: 1 }}>
                <Typography variant="h4" fontWeight="bold" gutterBottom sx={{ textShadow: '0 2px 4px rgba(0,0,0,0.3)' }}>
                  {tenant.name}
                </Typography>
                <Chip
                  label={`${tenant.type.toUpperCase()} ADMINISTRATION`}
                  sx={{
                    backgroundColor: 'rgba(255, 255, 255, 0.2)',
                    color: 'white',
                    fontWeight: 'bold',
                    backdropFilter: 'blur(10px)',
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                  }}
                />
              </Box>
            </Box>
            <Typography variant="body1" sx={{ opacity: 0.9, fontSize: '1.1rem' }}>
              Access digital government services and apply for official documents online
            </Typography>
          </Box>
        </Box>

        <CardContent sx={{ p: 4 }}>

          {/* Available Services */}
          <Typography variant="h5" gutterBottom sx={{ mb: 4, fontWeight: 'bold', color: primaryColor }}>
            Available Public Services
          </Typography>

          <Grid container spacing={3}>
            {/* ID Card Application Service */}
            <Grid item xs={12} md={6}>
              <Paper
                elevation={0}
                sx={{
                  p: 4,
                  textAlign: 'center',
                  borderRadius: 3,
                  background: hasIDApplication
                    ? `linear-gradient(135deg, ${primaryColor}08 0%, ${secondaryColor}08 100%)`
                    : 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
                  border: hasIDApplication
                    ? `2px solid ${primaryColor}30`
                    : '2px solid #e0e0e0',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: hasIDApplication ? 'translateY(-4px)' : 'none',
                    boxShadow: hasIDApplication ? `0 12px 32px ${primaryColor}20` : 'none',
                  }
                }}
              >
                <Box
                  sx={{
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    backgroundColor: hasIDApplication ? `${primaryColor}15` : '#f5f5f5',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    margin: '0 auto 16px auto',
                    border: hasIDApplication ? `3px solid ${primaryColor}30` : '3px solid #e0e0e0',
                  }}
                >
                  <IDCardIcon
                    sx={{
                      fontSize: 40,
                      color: hasIDApplication ? primaryColor : '#ccc',
                    }}
                  />
                </Box>
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold', mb: 1 }}>
                  ID Card Services
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3, lineHeight: 1.6 }}>
                  Apply for new ID cards, renewals, replacements, and reprints
                </Typography>

                {hasIDApplication ? (
                  <Box>
                    <Chip
                      icon={<CheckIcon />}
                      label="Service Available"
                      color="success"
                      size="small"
                      sx={{ mb: 3, fontWeight: 'bold' }}
                    />
                    <br />
                    <Button
                      variant="contained"
                      endIcon={<ArrowIcon />}
                      onClick={() => handleServiceAccess(tenant, 'id_application')}
                      sx={{
                        backgroundColor: primaryColor,
                        borderRadius: 2,
                        px: 4,
                        py: 1.5,
                        fontWeight: 'bold',
                        textTransform: 'none',
                        fontSize: '1rem',
                        boxShadow: `0 4px 16px ${primaryColor}30`,
                        '&:hover': {
                          backgroundColor: secondaryColor,
                          transform: 'translateY(-2px)',
                          boxShadow: `0 8px 24px ${secondaryColor}40`,
                        }
                      }}
                    >
                      Apply Now
                    </Button>
                  </Box>
                ) : (
                  <Chip
                    icon={<CancelIcon />}
                    label="Service Unavailable"
                    color="error"
                    size="small"
                    sx={{ fontWeight: 'bold' }}
                  />
                )}
              </Paper>
            </Grid>

            {/* General Service Portal */}
            <Grid item xs={12} md={6}>
              <Paper
                elevation={0}
                sx={{
                  p: 4,
                  textAlign: 'center',
                  borderRadius: 3,
                  background: hasServicePortal
                    ? `linear-gradient(135deg, ${secondaryColor}08 0%, ${primaryColor}08 100%)`
                    : 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
                  border: hasServicePortal
                    ? `2px solid ${secondaryColor}30`
                    : '2px solid #e0e0e0',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: hasServicePortal ? 'translateY(-4px)' : 'none',
                    boxShadow: hasServicePortal ? `0 12px 32px ${secondaryColor}20` : 'none',
                  }
                }}
              >
                <Box
                  sx={{
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    backgroundColor: hasServicePortal ? `${secondaryColor}15` : '#f5f5f5',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    margin: '0 auto 16px auto',
                    border: hasServicePortal ? `3px solid ${secondaryColor}30` : '3px solid #e0e0e0',
                  }}
                >
                  <AssignmentIcon
                    sx={{
                      fontSize: 40,
                      color: hasServicePortal ? secondaryColor : '#ccc',
                    }}
                  />
                </Box>
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold', mb: 1 }}>
                  General Services
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3, lineHeight: 1.6 }}>
                  Access various government services, forms, and information
                </Typography>

                {hasServicePortal ? (
                  <Box>
                    <Chip
                      icon={<CheckIcon />}
                      label="Service Available"
                      color="success"
                      size="small"
                      sx={{ mb: 3, fontWeight: 'bold' }}
                    />
                    <br />
                    <Button
                      variant="outlined"
                      endIcon={<ArrowIcon />}
                      onClick={() => handleServiceAccess(tenant, 'service_portal')}
                      sx={{
                        borderColor: secondaryColor,
                        color: secondaryColor,
                        borderRadius: 2,
                        px: 4,
                        py: 1.5,
                        fontWeight: 'bold',
                        textTransform: 'none',
                        fontSize: '1rem',
                        borderWidth: 2,
                        '&:hover': {
                          backgroundColor: secondaryColor,
                          color: 'white',
                          transform: 'translateY(-2px)',
                          boxShadow: `0 8px 24px ${secondaryColor}40`,
                          borderColor: secondaryColor,
                        }
                      }}
                    >
                      Access Portal
                    </Button>
                  </Box>
                ) : (
                  <Chip
                    icon={<CancelIcon />}
                    label="Service Unavailable"
                    color="error"
                    size="small"
                    sx={{ fontWeight: 'bold' }}
                  />
                )}
              </Paper>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    );
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
          <CircularProgress size={60} />
        </Box>
      </Container>
    );
  }

  return (
    <Box sx={{ minHeight: '100vh', backgroundColor: '#f8f9fa' }}>
      {/* Hero Header */}
      <Box
        sx={{
          background: `linear-gradient(135deg, ${tenantColors.primary} 0%, ${tenantColors.secondary} 100%)`,
          color: 'white',
          py: 8,
          position: 'relative',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'url(/images/pattern.svg)',
            backgroundSize: 'cover',
            opacity: 0.1,
          },
        }}
      >
        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 1 }}>
          <Box sx={{ textAlign: 'center' }}>
            <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
              <Avatar
                sx={{
                  width: 80,
                  height: 80,
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  backdropFilter: 'blur(10px)',
                  border: '3px solid rgba(255, 255, 255, 0.3)',
                }}
              >
                <SecurityIcon sx={{ fontSize: 48 }} />
              </Avatar>
            </Box>
            <Typography variant="h2" fontWeight="bold" gutterBottom sx={{ textShadow: '0 2px 4px rgba(0,0,0,0.3)' }}>
              Public Services Portal
            </Typography>
            <Typography variant="h5" sx={{ opacity: 0.9, mb: 4, maxWidth: 800, mx: 'auto' }}>
              Access digital government services from participating administrative areas across Ethiopia
            </Typography>
            <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, flexWrap: 'wrap' }}>
              <Chip
                icon={<SecurityIcon />}
                label="Secure & Verified"
                sx={{
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  fontWeight: 'bold',
                  backdropFilter: 'blur(10px)',
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                }}
              />
              <Chip
                icon={<CheckIcon />}
                label="24/7 Available"
                sx={{
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  fontWeight: 'bold',
                  backdropFilter: 'blur(10px)',
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                }}
              />
            </Box>
          </Box>
        </Container>
      </Box>

      <Container maxWidth="lg" sx={{ py: 6 }}>

        {/* Error Alert */}
        {error && (
          <Alert
            severity="error"
            sx={{
              mb: 4,
              borderRadius: 3,
              boxShadow: '0 4px 16px rgba(0,0,0,0.1)',
            }}
          >
            {error}
          </Alert>
        )}

        {/* Info Alert */}
        <Alert
          severity="info"
          sx={{
            mb: 6,
            borderRadius: 3,
            backgroundColor: `${tenantColors.primary}08`,
            border: `1px solid ${tenantColors.primary}20`,
            boxShadow: '0 4px 16px rgba(0,0,0,0.05)',
          }}
          icon={<InfoIcon />}
        >
          <Typography variant="body1" sx={{ fontWeight: 500 }}>
            <strong>Important:</strong> Public access to services is controlled by each administrative area.
            Only areas that have enabled public access will appear below. All services are secure and verified.
          </Typography>
        </Alert>

        {/* Tenant Service Cards */}
        {tenants.length > 0 ? (
          <Box>
            <Typography variant="h4" gutterBottom sx={{ mb: 4, fontWeight: 'bold', color: tenantColors.primary, textAlign: 'center' }}>
              Available Administrative Areas
            </Typography>
            {console.log('🔍 PublicServicePortal: Rendering', tenants.length, 'tenant cards')}
            {tenants.map((tenant) => (
              <TenantServiceCard key={tenant.id} tenant={tenant} />
            ))}
          </Box>
        ) : (
          <Paper
            sx={{
              p: 6,
              textAlign: 'center',
              borderRadius: 4,
              background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
              border: '2px dashed #dee2e6',
            }}
          >
            <Avatar
              sx={{
                width: 120,
                height: 120,
                backgroundColor: '#e9ecef',
                margin: '0 auto 24px auto',
              }}
            >
              <PublicIcon sx={{ fontSize: 60, color: '#6c757d' }} />
            </Avatar>
            <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold', color: '#495057' }}>
              No Public Services Available
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ fontSize: '1.1rem', maxWidth: 600, mx: 'auto' }}>
              Currently, no administrative areas have enabled public access to their services.
              Please check back later or contact your local administration for more information.
            </Typography>
          </Paper>
        )}
      </Container>
    </Box>
  );
};

export default PublicServicePortal;
