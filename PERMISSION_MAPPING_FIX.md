# Permission Mapping Fix

## 🐛 **Issue Identified**

Subcity admin user was getting "Access Denied" when trying to access citizens and ID cards pages because the permission mappings didn't align with the actual backend permissions.

### **Error Details**:
```
🔍 Group-based permission check: manage_citizens
  required: ['register_citizens']
  userHas: ['view_citizens_list', 'view_citizen_details', 'view_id_cards_list', 'approve_id_cards', 'create_kebele_users', 'view_child_kebeles_data', 'view_subcity_dashboard', 'view_subcity_reports']
  result: false

Access Denied
Your role: subcity_admin
Required permission: manage_citizens
```

## 🔍 **Root Cause Analysis**

### **Permission Mapping Mismatch**:
- **Route**: `/citizens/all-kebeles` required `manage_citizens` permission
- **Mapping**: `manage_citizens` was mapped to `['register_citizens']`
- **User Permissions**: Subcity admin had `view_citizens_list`, `view_citizen_details`, etc. but NOT `register_citizens`
- **Organizational Logic**: Subcity admins should manage (oversee) citizens, not register them directly

### **Hierarchical Permission Logic**:
```
🏢 City Admin     → Manages city-wide data
🏢 Subcity Admin  → Manages cross-kebele data (oversees kebeles)
🏢 Kebele Admin   → Manages kebele-specific data (registers citizens)
🏢 Clerk          → Performs operational tasks (registers, generates)
```

## 🔧 **Fixes Applied**

### **Fix 1: Updated Citizens Permission Mapping**

**Before (Incorrect)**:
```javascript
'manage_citizens': ['register_citizens'], // Wrong - subcity admins don't register
```

**After (Correct)**:
```javascript
'manage_citizens': ['view_citizens_list', 'view_citizen_details', 'view_child_kebeles_data'], // Subcity admins can manage (view/oversee) citizens
'register_citizens': ['register_citizens'], // Only clerks can register
```

### **Fix 2: Updated ID Cards Permission Mapping**

**Before (Incorrect)**:
```javascript
'manage_idcards': ['generate_id_cards', 'approve_id_cards'], // Wrong - subcity admins don't generate
```

**After (Correct)**:
```javascript
'manage_idcards': ['view_id_cards_list', 'approve_id_cards'], // Subcity admins can manage (view/approve) ID cards
'create_idcards': ['generate_id_cards'], // Only clerks can generate
```

### **Fix 3: Updated Role-Based Fallback Permissions**

**Subcity Admin**:
```javascript
subcity_admin: [
  'view_dashboard',
  'view_citizens',
  'manage_citizens', // ✅ Added - can manage (oversee) citizens
  'view_idcards',
  'manage_idcards', // ✅ Added - can manage (approve) ID cards
  'approve_idcards',
  'view_reports',
  'manage_users',
  'create_users',
  'view_workflows',
  'view_cross_kebele'
],
```

**City Admin**:
```javascript
city_admin: [
  'view_dashboard',
  'view_citizens',
  'manage_citizens', // ✅ Added - can manage citizens city-wide
  'view_idcards',
  'manage_idcards', // ✅ Added - can manage ID cards city-wide
  'view_reports',
  'manage_users',
  'create_users',
  'view_workflows',
  'view_cross_subcity'
],
```

## 🎯 **Permission Logic by Role**

### **🏢 Subcity Admin Permissions**

#### **Citizens Management**:
- ✅ **View Citizens**: `view_citizens_list`, `view_citizen_details`
- ✅ **Manage Citizens**: Can oversee citizens from child kebeles
- ✅ **Cross-Kebele Access**: `view_child_kebeles_data`
- ❌ **Register Citizens**: Cannot register (done at kebele level)

#### **ID Cards Management**:
- ✅ **View ID Cards**: `view_id_cards_list`
- ✅ **Approve ID Cards**: `approve_id_cards`
- ✅ **Manage ID Cards**: Can oversee ID card workflow
- ❌ **Generate ID Cards**: Cannot generate (done at kebele level)

#### **User Management**:
- ✅ **Create Kebele Users**: `create_kebele_users`
- ✅ **Manage Users**: Can manage users in child kebeles

### **🏢 Kebele Level Permissions**

#### **Clerks & Kebele Leaders**:
- ✅ **Register Citizens**: `register_citizens`
- ✅ **Generate ID Cards**: `generate_id_cards`
- ✅ **View Local Data**: Kebele-specific access

## 📊 **Route Access Matrix**

### **Citizens Routes**:
```javascript
'/citizens'              → 'view_citizens'     ✅ Subcity Admin
'/citizens/all-kebeles'  → 'manage_citizens'   ✅ Subcity Admin (Fixed!)
'/citizens/citizen-book' → 'manage_citizens'   ✅ City Admin
'/citizens/create'       → 'register_citizens' ❌ Subcity Admin (Correct)
```

### **ID Cards Routes**:
```javascript
'/idcards'               → 'view_idcards'      ✅ Subcity Admin
'/idcards/all-kebeles'   → 'manage_idcards'    ✅ Subcity Admin (Fixed!)
'/idcards/create'        → 'create_idcards'    ❌ Subcity Admin (Correct)
'/idcards/pending'       → 'approve_idcards'   ✅ Subcity Admin
```

### **User Management Routes**:
```javascript
'/users/kebele-management' → 'manage_users'    ✅ Subcity Admin
'/users/city-management'   → 'manage_users'    ✅ City Admin
'/users'                   → 'manage_users'    ✅ Super Admin
```

## ✅ **Expected Behavior After Fix**

### **For Subcity Admin User**:

#### **✅ Citizens Access**:
- **Dashboard** → ✅ Accessible
- **Citizens** → ✅ Accessible (local view)
- **Citizens/All Kebeles** → ✅ Accessible (cross-kebele view) 🎯 **Fixed!**
- **Citizens/Create** → ❌ Not accessible (correct - done at kebele level)

#### **✅ ID Cards Access**:
- **ID Cards** → ✅ Accessible (local view)
- **ID Cards/All Kebeles** → ✅ Accessible (cross-kebele view) 🎯 **Fixed!**
- **ID Cards/Create** → ❌ Not accessible (correct - done at kebele level)
- **ID Cards/Pending** → ✅ Accessible (can approve)

#### **✅ User Management Access**:
- **Users/Kebele Management** → ✅ Accessible (can create kebele users)

### **Navigation Menu Items**:
```
✅ Dashboard
✅ Citizens → /citizens/all-kebeles (cross-kebele view)
✅ ID Cards → /idcards/all-kebeles (cross-kebele view)
  ✅ Pending Approval
✅ Kebele Users → /users/kebele-management
✅ Reports
```

## 🧪 **Testing Results**

### **Permission Checks Now Pass**:
```javascript
🔍 Group-based permission check: manage_citizens
  required: ['view_citizens_list', 'view_citizen_details', 'view_child_kebeles_data']
  userHas: ['view_citizens_list', 'view_citizen_details', 'view_child_kebeles_data', ...]
  result: true ✅

🔍 Group-based permission check: manage_idcards
  required: ['view_id_cards_list', 'approve_id_cards']
  userHas: ['view_id_cards_list', 'approve_id_cards', ...]
  result: true ✅
```

### **Route Access Works**:
- ✅ `/citizens/all-kebeles` → Accessible
- ✅ `/idcards/all-kebeles` → Accessible
- ✅ `/users/kebele-management` → Accessible

## 🚀 **Benefits Achieved**

### **✅ Correct Organizational Hierarchy**:
- **Subcity admins** can oversee and manage data from child kebeles
- **Kebele users** handle direct citizen registration and ID card generation
- **City admins** have city-wide oversight capabilities

### **✅ Proper Permission Alignment**:
- **Backend permissions** match frontend permission mappings
- **Route protection** aligns with user capabilities
- **Navigation items** appear for accessible features

### **✅ User Experience**:
- **No more access denied errors** for legitimate access
- **Appropriate views** based on organizational level
- **Intuitive navigation** that matches job responsibilities

### **✅ Security Maintained**:
- **Granular permissions** still enforced
- **Hierarchical access** properly implemented
- **Role separation** maintained (subcity admins can't register citizens directly)

---

**The permission system now correctly reflects the organizational hierarchy where subcity admins can manage (oversee) citizens and ID cards from their child kebeles, while actual registration and generation happens at the kebele level!** 🎉
