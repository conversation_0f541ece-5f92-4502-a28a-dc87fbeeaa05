import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Grid,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert,
  Tabs,
  Tab,
  Autocomplete,
  IconButton,
  Tooltip,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction
} from '@mui/material';
import {
  Add,
  Edit,
  Security,
  Group,
  Business,
  Assignment,
  PersonAdd,
  VpnKey,
  Visibility
} from '@mui/icons-material';

const TenantRoleManagement = () => {
  const [tabValue, setTabValue] = useState(0);
  const [manageableTenants, setManageableTenants] = useState([]);
  const [selectedTenant, setSelectedTenant] = useState(null);
  const [tenantUsers, setTenantUsers] = useState([]);
  const [tenantRoles, setTenantRoles] = useState([]);
  const [permissions, setPermissions] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState(''); // 'create_role', 'assign_role', 'grant_permission'
  const [formData, setFormData] = useState({
    // Role creation
    codename: '',
    name: '',
    description: '',
    role_type: 'custom',
    level: 10,
    allowed_tenant_types: [],
    permission_codenames: [],
    // Role assignment
    user_email: '',
    role_codename: '',
    // Permission granting
    permission_codename: '',
    reason: ''
  });
  const [loading, setLoading] = useState(false);
  const [alert, setAlert] = useState({ show: false, message: '', severity: 'info' });

  const roleTypes = [
    { value: 'operational', label: 'Operational Role' },
    { value: 'custom', label: 'Custom Role' }
  ];

  useEffect(() => {
    fetchManageableTenants();
    fetchPermissions();
  }, []);

  useEffect(() => {
    if (selectedTenant) {
      fetchTenantUsers();
      fetchTenantRoles();
    }
  }, [selectedTenant]);

  const fetchManageableTenants = async () => {
    setLoading(true);
    try {
      // Simulate API call - replace with actual API call
      const mockTenants = [
        {
          id: 1,
          name: 'Subcity 1',
          type: 'subcity',
          parent: 'Gondar City',
          users_count: 25,
          can_create_roles: true
        },
        {
          id: 2,
          name: 'Kebele 1-A',
          type: 'kebele',
          parent: 'Subcity 1',
          users_count: 8,
          can_create_roles: true
        },
        {
          id: 3,
          name: 'Kebele 1-B',
          type: 'kebele',
          parent: 'Subcity 1',
          users_count: 12,
          can_create_roles: true
        }
      ];

      setManageableTenants(mockTenants);
      if (mockTenants.length > 0) {
        setSelectedTenant(mockTenants[0]);
      }
    } catch (error) {
      showAlert('Error fetching manageable tenants', 'error');
    } finally {
      setLoading(false);
    }
  };

  const fetchTenantUsers = async () => {
    if (!selectedTenant) return;

    try {
      // Simulate API call
      const mockUsers = [
        {
          id: 1,
          email: '<EMAIL>',
          first_name: 'Ahmed',
          last_name: 'Hassan',
          effective_role: 'clerk',
          effective_role_name: 'Clerk',
          tenant_name: selectedTenant.name,
          custom_permissions_count: 0,
          can_be_managed: true
        },
        {
          id: 2,
          email: '<EMAIL>',
          first_name: 'Fatima',
          last_name: 'Ali',
          effective_role: 'kebele_leader',
          effective_role_name: 'Kebele Leader',
          tenant_name: selectedTenant.name,
          custom_permissions_count: 2,
          can_be_managed: true
        },
        {
          id: 3,
          email: '<EMAIL>',
          first_name: 'Mohammed',
          last_name: 'Ibrahim',
          effective_role: 'senior_clerk',
          effective_role_name: 'Senior Clerk',
          tenant_name: selectedTenant.name,
          custom_permissions_count: 1,
          can_be_managed: true
        }
      ];

      setTenantUsers(mockUsers);
    } catch (error) {
      showAlert('Error fetching tenant users', 'error');
    }
  };

  const fetchTenantRoles = async () => {
    if (!selectedTenant) return;

    try {
      // Simulate API call
      const mockRoles = [
        { codename: 'clerk', name: 'Clerk', type: 'legacy', level: 10, is_global: true },
        { codename: 'kebele_leader', name: 'Kebele Leader', type: 'legacy', level: 30, is_global: true },
        { codename: 'kebele_admin', name: 'Kebele Admin', type: 'legacy', level: 40, is_global: true },
        { codename: 'senior_clerk_kebele_1', name: 'Senior Clerk', type: 'dynamic', level: 15, is_global: false, scope_tenant: selectedTenant.name },
        { codename: 'field_officer_kebele_1', name: 'Field Officer', type: 'dynamic', level: 20, is_global: false, scope_tenant: selectedTenant.name }
      ];

      setTenantRoles(mockRoles);
    } catch (error) {
      showAlert('Error fetching tenant roles', 'error');
    }
  };

  const fetchPermissions = async () => {
    try {
      // Simulate API call
      const mockPermissions = [
        { codename: 'view_citizens', name: 'View Citizens', category: 'citizens' },
        { codename: 'create_citizens', name: 'Create Citizens', category: 'citizens' },
        { codename: 'edit_citizens', name: 'Edit Citizens', category: 'citizens' },
        { codename: 'view_id_cards', name: 'View ID Cards', category: 'id_cards' },
        { codename: 'create_id_cards', name: 'Create ID Cards', category: 'id_cards' },
        { codename: 'approve_id_cards', name: 'Approve ID Cards', category: 'id_cards' },
        { codename: 'print_id_cards', name: 'Print ID Cards', category: 'id_cards' }
      ];

      setPermissions(mockPermissions);
    } catch (error) {
      showAlert('Error fetching permissions', 'error');
    }
  };

  const showAlert = (message, severity = 'info') => {
    setAlert({ show: true, message, severity });
    setTimeout(() => setAlert({ show: false, message: '', severity: 'info' }), 5000);
  };

  const handleCreateTenantRole = async () => {
    if (!formData.codename || !formData.name || !selectedTenant) {
      showAlert('Please fill in required fields', 'error');
      return;
    }

    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      showAlert(`Successfully created role: ${formData.name} for ${selectedTenant.name}`, 'success');
      setOpenDialog(false);
      resetForm();
      fetchTenantRoles();
    } catch (error) {
      showAlert('Error creating role', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleAssignRole = async () => {
    if (!formData.user_email || !formData.role_codename || !selectedTenant) {
      showAlert('Please select a user and role', 'error');
      return;
    }

    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      showAlert(`Successfully assigned role to ${formData.user_email}`, 'success');
      setOpenDialog(false);
      resetForm();
      fetchTenantUsers();
    } catch (error) {
      showAlert('Error assigning role', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleGrantPermission = async () => {
    if (!formData.user_email || !formData.permission_codename || !selectedTenant) {
      showAlert('Please select a user and permission', 'error');
      return;
    }

    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      showAlert(`Successfully granted permission to ${formData.user_email}`, 'success');
      setOpenDialog(false);
      resetForm();
      fetchTenantUsers();
    } catch (error) {
      showAlert('Error granting permission', 'error');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      codename: '',
      name: '',
      description: '',
      role_type: 'custom',
      level: 10,
      allowed_tenant_types: [],
      permission_codenames: [],
      user_email: '',
      role_codename: '',
      permission_codename: '',
      reason: ''
    });
  };

  const openCreateRoleDialog = () => {
    setDialogType('create_role');
    resetForm();
    setOpenDialog(true);
  };

  const openAssignRoleDialog = () => {
    setDialogType('assign_role');
    resetForm();
    setOpenDialog(true);
  };

  const openGrantPermissionDialog = () => {
    setDialogType('grant_permission');
    resetForm();
    setOpenDialog(true);
  };

  const TabPanel = ({ children, value, index }) => (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );

  const getTenantTypeColor = (type) => {
    const colors = {
      'city': 'primary',
      'subcity': 'secondary',
      'kebele': 'success'
    };
    return colors[type] || 'default';
  };

  const getRoleTypeColor = (type) => {
    const colors = {
      'legacy': 'info',
      'dynamic': 'success'
    };
    return colors[type] || 'default';
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        <Business sx={{ mr: 1, verticalAlign: 'middle' }} />
        Tenant Role Management
      </Typography>

      {alert.show && (
        <Alert severity={alert.severity} sx={{ mb: 2 }}>
          {alert.message}
        </Alert>
      )}

      {/* Tenant Selection */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Select Tenant to Manage
          </Typography>
          <Grid container spacing={2}>
            {manageableTenants.map((tenant) => (
              <Grid item xs={12} sm={6} md={4} key={tenant.id}>
                <Card 
                  sx={{ 
                    cursor: 'pointer',
                    border: selectedTenant?.id === tenant.id ? '2px solid #1976d2' : '1px solid #e0e0e0',
                    '&:hover': { boxShadow: 3 }
                  }}
                  onClick={() => setSelectedTenant(tenant)}
                >
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                      <Typography variant="h6">{tenant.name}</Typography>
                      <Chip 
                        label={tenant.type} 
                        size="small" 
                        color={getTenantTypeColor(tenant.type)}
                      />
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Parent: {tenant.parent || 'None'}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Users: {tenant.users_count}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      {selectedTenant && (
        <>
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
            <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
              <Tab label="Users" />
              <Tab label="Available Roles" />
              <Tab label="Role Actions" />
            </Tabs>
          </Box>

          <TabPanel value={tabValue} index={0}>
            <Typography variant="h6" gutterBottom>
              Users in {selectedTenant.name}
            </Typography>
            
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>User</TableCell>
                    <TableCell>Current Role</TableCell>
                    <TableCell>Custom Permissions</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {tenantUsers.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <Box>
                          <Typography variant="subtitle2">
                            {user.first_name} {user.last_name}
                          </Typography>
                          <Typography variant="caption" color="textSecondary">
                            {user.email}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={user.effective_role_name} 
                          size="small" 
                          color="primary"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={`${user.custom_permissions_count} permissions`} 
                          size="small" 
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Tooltip title="Change Role">
                          <IconButton size="small" onClick={openAssignRoleDialog}>
                            <Assignment />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Grant Permission">
                          <IconButton size="small" onClick={openGrantPermissionDialog}>
                            <VpnKey />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <Typography variant="h6" gutterBottom>
              Available Roles for {selectedTenant.name}
            </Typography>
            
            <Grid container spacing={2}>
              {tenantRoles.map((role) => (
                <Grid item xs={12} sm={6} md={4} key={role.codename}>
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                        <Typography variant="h6">{role.name}</Typography>
                        <Chip 
                          label={role.type} 
                          size="small" 
                          color={getRoleTypeColor(role.type)}
                        />
                      </Box>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Level: {role.level}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Scope: {role.is_global ? 'Global' : role.scope_tenant || 'Tenant-specific'}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </TabPanel>

          <TabPanel value={tabValue} index={2}>
            <Typography variant="h6" gutterBottom>
              Role Management Actions for {selectedTenant.name}
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      <Add sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Create Custom Role
                    </Typography>
                    <Typography variant="body2" color="text.secondary" paragraph>
                      Create a new custom role specific to this tenant with tailored permissions.
                    </Typography>
                    <Button 
                      variant="contained" 
                      fullWidth 
                      onClick={openCreateRoleDialog}
                    >
                      Create Role
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} sm={4}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      <PersonAdd sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Assign Role
                    </Typography>
                    <Typography variant="body2" color="text.secondary" paragraph>
                      Assign existing roles to users in this tenant.
                    </Typography>
                    <Button 
                      variant="contained" 
                      fullWidth 
                      onClick={openAssignRoleDialog}
                    >
                      Assign Role
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} sm={4}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      <VpnKey sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Grant Permission
                    </Typography>
                    <Typography variant="body2" color="text.secondary" paragraph>
                      Grant additional permissions to specific users.
                    </Typography>
                    <Button 
                      variant="contained" 
                      fullWidth 
                      onClick={openGrantPermissionDialog}
                    >
                      Grant Permission
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>
        </>
      )}

      {/* Create Role Dialog */}
      <Dialog open={openDialog && dialogType === 'create_role'} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Create Custom Role for {selectedTenant?.name}</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Role Code *"
                  value={formData.codename}
                  onChange={(e) => setFormData({ ...formData, codename: e.target.value })}
                  helperText="Unique identifier (e.g., 'emergency_coordinator')"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Role Name *"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  helperText="Display name (e.g., 'Emergency Coordinator')"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  multiline
                  rows={3}
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Role Type</InputLabel>
                  <Select
                    value={formData.role_type}
                    onChange={(e) => setFormData({ ...formData, role_type: e.target.value })}
                    label="Role Type"
                  >
                    {roleTypes.map((type) => (
                      <MenuItem key={type.value} value={type.value}>
                        {type.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Hierarchy Level"
                  type="number"
                  value={formData.level}
                  onChange={(e) => setFormData({ ...formData, level: parseInt(e.target.value) })}
                  helperText="1-100 (higher = more authority)"
                  inputProps={{ min: 1, max: 100 }}
                />
              </Grid>
              <Grid item xs={12}>
                <Autocomplete
                  multiple
                  options={permissions}
                  getOptionLabel={(option) => option.name}
                  value={permissions.filter(p => formData.permission_codenames.includes(p.codename))}
                  onChange={(event, newValue) => 
                    setFormData({ ...formData, permission_codenames: newValue.map(v => v.codename) })
                  }
                  renderInput={(params) => (
                    <TextField {...params} label="Permissions" />
                  )}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button onClick={handleCreateTenantRole} variant="contained" disabled={loading}>
            Create Role
          </Button>
        </DialogActions>
      </Dialog>

      {/* Assign Role Dialog */}
      <Dialog open={openDialog && dialogType === 'assign_role'} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Assign Role in {selectedTenant?.name}</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Autocomplete
              options={tenantUsers}
              getOptionLabel={(option) => `${option.first_name} ${option.last_name} (${option.email})`}
              value={tenantUsers.find(u => u.email === formData.user_email) || null}
              onChange={(event, newValue) => 
                setFormData({ ...formData, user_email: newValue?.email || '' })
              }
              renderInput={(params) => <TextField {...params} label="Select User" fullWidth />}
              sx={{ mb: 2 }}
            />

            <Autocomplete
              options={tenantRoles}
              getOptionLabel={(option) => option.name}
              value={tenantRoles.find(r => r.codename === formData.role_codename) || null}
              onChange={(event, newValue) => 
                setFormData({ ...formData, role_codename: newValue?.codename || '' })
              }
              renderInput={(params) => <TextField {...params} label="Select Role" fullWidth />}
              sx={{ mb: 2 }}
            />

            <TextField
              fullWidth
              label="Reason (optional)"
              multiline
              rows={3}
              value={formData.reason}
              onChange={(e) => setFormData({ ...formData, reason: e.target.value })}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button onClick={handleAssignRole} variant="contained" disabled={loading}>
            Assign Role
          </Button>
        </DialogActions>
      </Dialog>

      {/* Grant Permission Dialog */}
      <Dialog open={openDialog && dialogType === 'grant_permission'} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Grant Permission in {selectedTenant?.name}</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Autocomplete
              options={tenantUsers}
              getOptionLabel={(option) => `${option.first_name} ${option.last_name} (${option.email})`}
              value={tenantUsers.find(u => u.email === formData.user_email) || null}
              onChange={(event, newValue) => 
                setFormData({ ...formData, user_email: newValue?.email || '' })
              }
              renderInput={(params) => <TextField {...params} label="Select User" fullWidth />}
              sx={{ mb: 2 }}
            />

            <Autocomplete
              options={permissions}
              getOptionLabel={(option) => option.name}
              value={permissions.find(p => p.codename === formData.permission_codename) || null}
              onChange={(event, newValue) => 
                setFormData({ ...formData, permission_codename: newValue?.codename || '' })
              }
              renderInput={(params) => <TextField {...params} label="Select Permission" fullWidth />}
              sx={{ mb: 2 }}
            />

            <TextField
              fullWidth
              label="Reason (optional)"
              multiline
              rows={3}
              value={formData.reason}
              onChange={(e) => setFormData({ ...formData, reason: e.target.value })}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button onClick={handleGrantPermission} variant="contained" disabled={loading}>
            Grant Permission
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TenantRoleManagement;
