import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Grid,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert,
  Tabs,
  Tab,
  Autocomplete,
  IconButton,
  Tooltip,
  FormHelperText,
  Divider
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Security,
  Group,
  AdminPanelSettings,
  Visibility,
  Assignment
} from '@mui/icons-material';

const RoleManagement = () => {
  const [tabValue, setTabValue] = useState(0);
  const [roles, setRoles] = useState([]);
  const [permissions, setPermissions] = useState([]);
  const [users, setUsers] = useState([]);
  const [roleHierarchy, setRoleHierarchy] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState(''); // 'create', 'edit', 'assign'
  const [selectedRole, setSelectedRole] = useState(null);
  const [formData, setFormData] = useState({
    codename: '',
    name: '',
    description: '',
    role_type: 'custom',
    level: 10,
    allowed_tenant_types: [],
    can_manage_role_codenames: [],
    permission_codenames: []
  });
  const [assignmentData, setAssignmentData] = useState({
    user_email: '',
    role_codename: '',
    reason: ''
  });
  const [loading, setLoading] = useState(false);
  const [alert, setAlert] = useState({ show: false, message: '', severity: 'info' });

  const roleTypes = [
    { value: 'system', label: 'System Role' },
    { value: 'administrative', label: 'Administrative Role' },
    { value: 'operational', label: 'Operational Role' },
    { value: 'custom', label: 'Custom Role' }
  ];

  const tenantTypes = [
    { value: 'city', label: 'City' },
    { value: 'subcity', label: 'Sub City' },
    { value: 'kebele', label: 'Kebele' }
  ];

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {
      // Simulate API calls - replace with actual API calls
      const mockRoles = [
        {
          id: 1,
          codename: 'emergency_coordinator',
          name: 'Emergency Coordinator',
          description: 'Special role for emergency situations',
          role_type: 'custom',
          level: 50,
          is_built_in: false,
          users_count: 2,
          permissions_count: 8
        },
        {
          id: 2,
          codename: 'field_officer',
          name: 'Field Officer',
          description: 'Mobile officer for field operations',
          role_type: 'operational',
          level: 20,
          is_built_in: false,
          users_count: 5,
          permissions_count: 6
        },
        {
          id: 3,
          codename: 'senior_clerk',
          name: 'Senior Clerk',
          description: 'Experienced clerk with additional permissions',
          role_type: 'operational',
          level: 15,
          is_built_in: false,
          users_count: 3,
          permissions_count: 7
        }
      ];

      const mockPermissions = [
        { id: 1, codename: 'view_citizens', name: 'View Citizens', category: 'citizens' },
        { id: 2, codename: 'create_citizens', name: 'Create Citizens', category: 'citizens' },
        { id: 3, codename: 'edit_citizens', name: 'Edit Citizens', category: 'citizens' },
        { id: 4, codename: 'view_id_cards', name: 'View ID Cards', category: 'id_cards' },
        { id: 5, codename: 'create_id_cards', name: 'Create ID Cards', category: 'id_cards' },
        { id: 6, codename: 'approve_id_cards', name: 'Approve ID Cards', category: 'id_cards' },
        { id: 7, codename: 'print_id_cards', name: 'Print ID Cards', category: 'id_cards' }
      ];

      const mockUsers = [
        { id: 1, email: '<EMAIL>', first_name: 'John', last_name: 'Doe' },
        { id: 2, email: '<EMAIL>', first_name: 'Jane', last_name: 'Smith' },
        { id: 3, email: '<EMAIL>', first_name: 'Bob', last_name: 'Johnson' }
      ];

      const mockHierarchy = {
        roles: [
          { codename: 'superadmin', name: 'Super Admin', type: 'legacy', level: 100, users_count: 1 },
          { codename: 'city_admin', name: 'City Admin', type: 'legacy', level: 80, users_count: 2 },
          { codename: 'emergency_coordinator', name: 'Emergency Coordinator', type: 'dynamic', level: 50, users_count: 2 },
          { codename: 'subcity_admin', name: 'Sub City Admin', type: 'legacy', level: 60, users_count: 3 },
          { codename: 'kebele_admin', name: 'Kebele Admin', type: 'legacy', level: 40, users_count: 5 },
          { codename: 'kebele_leader', name: 'Kebele Leader', type: 'legacy', level: 30, users_count: 8 },
          { codename: 'field_officer', name: 'Field Officer', type: 'dynamic', level: 20, users_count: 5 },
          { codename: 'senior_clerk', name: 'Senior Clerk', type: 'dynamic', level: 15, users_count: 3 },
          { codename: 'clerk', name: 'Clerk', type: 'legacy', level: 10, users_count: 15 }
        ]
      };

      setRoles(mockRoles);
      setPermissions(mockPermissions);
      setUsers(mockUsers);
      setRoleHierarchy(mockHierarchy);
    } catch (error) {
      showAlert('Error fetching data', 'error');
    } finally {
      setLoading(false);
    }
  };

  const showAlert = (message, severity = 'info') => {
    setAlert({ show: true, message, severity });
    setTimeout(() => setAlert({ show: false, message: '', severity: 'info' }), 5000);
  };

  const handleCreateRole = async () => {
    if (!formData.codename || !formData.name) {
      showAlert('Please fill in required fields', 'error');
      return;
    }

    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      showAlert(`Successfully created role: ${formData.name}`, 'success');
      setOpenDialog(false);
      resetForm();
      fetchData();
    } catch (error) {
      showAlert('Error creating role', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleAssignRole = async () => {
    if (!assignmentData.user_email || !assignmentData.role_codename) {
      showAlert('Please select a user and role', 'error');
      return;
    }

    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      showAlert(`Successfully assigned role to ${assignmentData.user_email}`, 'success');
      setOpenDialog(false);
      resetAssignmentForm();
    } catch (error) {
      showAlert('Error assigning role', 'error');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      codename: '',
      name: '',
      description: '',
      role_type: 'custom',
      level: 10,
      allowed_tenant_types: [],
      can_manage_role_codenames: [],
      permission_codenames: []
    });
    setSelectedRole(null);
  };

  const resetAssignmentForm = () => {
    setAssignmentData({
      user_email: '',
      role_codename: '',
      reason: ''
    });
  };

  const openCreateDialog = () => {
    setDialogType('create');
    resetForm();
    setOpenDialog(true);
  };

  const openAssignDialog = () => {
    setDialogType('assign');
    resetAssignmentForm();
    setOpenDialog(true);
  };

  const TabPanel = ({ children, value, index }) => (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );

  const getRoleTypeColor = (type) => {
    const colors = {
      'system': 'error',
      'administrative': 'warning',
      'operational': 'info',
      'custom': 'success'
    };
    return colors[type] || 'default';
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        <AdminPanelSettings sx={{ mr: 1, verticalAlign: 'middle' }} />
        Dynamic Role Management
      </Typography>

      {alert.show && (
        <Alert severity={alert.severity} sx={{ mb: 2 }}>
          {alert.message}
        </Alert>
      )}

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
          <Tab label="Custom Roles" />
          <Tab label="Role Hierarchy" />
          <Tab label="Role Assignment" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <Box sx={{ mb: 2 }}>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={openCreateDialog}
          >
            Create Custom Role
          </Button>
        </Box>

        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Role Name</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Level</TableCell>
                <TableCell>Users</TableCell>
                <TableCell>Permissions</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {roles.map((role) => (
                <TableRow key={role.id}>
                  <TableCell>
                    <Box>
                      <Typography variant="subtitle2">{role.name}</Typography>
                      <Typography variant="caption" color="textSecondary">
                        {role.codename}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={role.role_type} 
                      size="small" 
                      color={getRoleTypeColor(role.role_type)}
                    />
                  </TableCell>
                  <TableCell>{role.level}</TableCell>
                  <TableCell>{role.users_count}</TableCell>
                  <TableCell>{role.permissions_count}</TableCell>
                  <TableCell>
                    <Tooltip title="Edit Role">
                      <IconButton size="small">
                        <Edit />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="View Permissions">
                      <IconButton size="small">
                        <Visibility />
                      </IconButton>
                    </Tooltip>
                    {!role.is_built_in && (
                      <Tooltip title="Delete Role">
                        <IconButton size="small" color="error">
                          <Delete />
                        </IconButton>
                      </Tooltip>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        {roleHierarchy && (
          <Grid container spacing={2}>
            {roleHierarchy.roles.map((role) => (
              <Grid item xs={12} sm={6} md={4} key={role.codename}>
                <Card 
                  sx={{ 
                    height: '100%',
                    border: role.type === 'dynamic' ? '2px solid #4caf50' : '1px solid #e0e0e0'
                  }}
                >
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                      <Typography variant="h6" component="div">
                        {role.name}
                      </Typography>
                      <Chip 
                        label={role.type} 
                        size="small" 
                        color={role.type === 'dynamic' ? 'success' : 'default'}
                      />
                    </Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Level: {role.level}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Users: {role.users_count}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <Box sx={{ mb: 2 }}>
          <Button
            variant="contained"
            startIcon={<Assignment />}
            onClick={openAssignDialog}
          >
            Assign Role to User
          </Button>
        </Box>

        <Typography variant="body1" color="text.secondary">
          Use this section to assign roles to users. You can assign both built-in legacy roles 
          and custom dynamic roles to users based on their responsibilities.
        </Typography>
      </TabPanel>

      {/* Create Role Dialog */}
      <Dialog open={openDialog && dialogType === 'create'} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Create Custom Role</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Role Code *"
                  value={formData.codename}
                  onChange={(e) => setFormData({ ...formData, codename: e.target.value })}
                  helperText="Unique identifier for the role (e.g., 'emergency_coordinator')"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Role Name *"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  helperText="Human-readable name (e.g., 'Emergency Coordinator')"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  multiline
                  rows={3}
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  helperText="Detailed description of the role's responsibilities"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Role Type</InputLabel>
                  <Select
                    value={formData.role_type}
                    onChange={(e) => setFormData({ ...formData, role_type: e.target.value })}
                    label="Role Type"
                  >
                    {roleTypes.map((type) => (
                      <MenuItem key={type.value} value={type.value}>
                        {type.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Hierarchy Level"
                  type="number"
                  value={formData.level}
                  onChange={(e) => setFormData({ ...formData, level: parseInt(e.target.value) })}
                  helperText="Higher numbers = more authority (1-100)"
                  inputProps={{ min: 1, max: 100 }}
                />
              </Grid>
              <Grid item xs={12}>
                <Autocomplete
                  multiple
                  options={tenantTypes}
                  getOptionLabel={(option) => option.label}
                  value={tenantTypes.filter(t => formData.allowed_tenant_types.includes(t.value))}
                  onChange={(event, newValue) => 
                    setFormData({ ...formData, allowed_tenant_types: newValue.map(v => v.value) })
                  }
                  renderInput={(params) => (
                    <TextField 
                      {...params} 
                      label="Allowed Tenant Types" 
                      helperText="Types of tenants this role can work in"
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12}>
                <Autocomplete
                  multiple
                  options={permissions}
                  getOptionLabel={(option) => option.name}
                  value={permissions.filter(p => formData.permission_codenames.includes(p.codename))}
                  onChange={(event, newValue) => 
                    setFormData({ ...formData, permission_codenames: newValue.map(v => v.codename) })
                  }
                  renderInput={(params) => (
                    <TextField 
                      {...params} 
                      label="Permissions" 
                      helperText="Permissions to assign to this role"
                    />
                  )}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button onClick={handleCreateRole} variant="contained" disabled={loading}>
            Create Role
          </Button>
        </DialogActions>
      </Dialog>

      {/* Assign Role Dialog */}
      <Dialog open={openDialog && dialogType === 'assign'} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Assign Role to User</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Autocomplete
              options={users}
              getOptionLabel={(option) => `${option.first_name} ${option.last_name} (${option.email})`}
              value={users.find(u => u.email === assignmentData.user_email) || null}
              onChange={(event, newValue) => 
                setAssignmentData({ ...assignmentData, user_email: newValue?.email || '' })
              }
              renderInput={(params) => <TextField {...params} label="Select User" fullWidth />}
              sx={{ mb: 2 }}
            />

            <TextField
              fullWidth
              label="Role Code"
              value={assignmentData.role_codename}
              onChange={(e) => setAssignmentData({ ...assignmentData, role_codename: e.target.value })}
              helperText="Enter role codename (e.g., 'emergency_coordinator', 'clerk')"
              sx={{ mb: 2 }}
            />

            <TextField
              fullWidth
              label="Reason (optional)"
              multiline
              rows={3}
              value={assignmentData.reason}
              onChange={(e) => setAssignmentData({ ...assignmentData, reason: e.target.value })}
              helperText="Reason for role assignment"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button onClick={handleAssignRole} variant="contained" disabled={loading}>
            Assign Role
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default RoleManagement;
