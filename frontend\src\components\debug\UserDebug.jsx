import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { usePermissions } from '../../hooks/usePermissions';
import { Box, Typography, Paper, Accordion, AccordionSummary, AccordionDetails } from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

const UserDebug = () => {
  const { user, isAuthenticated, loading } = useAuth();
  const { hasPermission } = usePermissions();

  // Test permissions
  const testPermissions = [
    'view_dashboard',
    'view_citizens',
    'view_idcards',
    'manage_users',
    'view_reports',
    'view_tenants'
  ];

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h4" gutterBottom>
        🔍 User Debug Information
      </Typography>

      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="h6">Authentication Status</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>Loading: {loading ? 'Yes' : 'No'}</Typography>
          <Typography>Authenticated: {isAuthenticated ? 'Yes' : 'No'}</Typography>
          <Typography>User Object: {user ? 'Present' : 'Missing'}</Typography>
        </AccordionDetails>
      </Accordion>

      {user && (
        <>
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">User Information</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography>ID: {user.id}</Typography>
              <Typography>Email: {user.email}</Typography>
              <Typography>Role: {user.role}</Typography>
              <Typography>Tenant: {user.tenant_name} (ID: {user.tenant_id})</Typography>
              <Typography>Is Superuser: {user.is_superuser ? 'Yes' : 'No'}</Typography>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">Permissions ({user.permissions?.length || 0})</Typography>
            </AccordionSummary>
            <AccordionDetails>
              {user.permissions && user.permissions.length > 0 ? (
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    First 10 permissions:
                  </Typography>
                  {user.permissions.slice(0, 10).map((perm, index) => (
                    <Typography key={index} variant="body2">
                      • {perm.codename}: {perm.name}
                    </Typography>
                  ))}
                  {user.permissions.length > 10 && (
                    <Typography variant="body2" sx={{ mt: 1, fontStyle: 'italic' }}>
                      ... and {user.permissions.length - 10} more
                    </Typography>
                  )}
                </Box>
              ) : (
                <Typography color="error">No permissions found!</Typography>
              )}
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">Permission Tests</Typography>
            </AccordionSummary>
            <AccordionDetails>
              {testPermissions.map((permission) => {
                const hasAccess = hasPermission(permission);
                return (
                  <Typography 
                    key={permission} 
                    variant="body2"
                    sx={{ 
                      color: hasAccess ? 'green' : 'red',
                      mb: 0.5
                    }}
                  >
                    {hasAccess ? '✅' : '❌'} {permission}
                  </Typography>
                );
              })}
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">Expected Navigation Items</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Box>
                {hasPermission('view_dashboard') && (
                  <Typography variant="body2" sx={{ color: 'green' }}>
                    ✅ Dashboard should show
                  </Typography>
                )}
                {hasPermission('view_citizens') && (
                  <Typography variant="body2" sx={{ color: 'green' }}>
                    ✅ Citizens should show
                  </Typography>
                )}
                {hasPermission('view_idcards') && (
                  <Typography variant="body2" sx={{ color: 'green' }}>
                    ✅ ID Cards should show
                  </Typography>
                )}
                {hasPermission('manage_users') && (
                  <Typography variant="body2" sx={{ color: 'green' }}>
                    ✅ Users should show
                  </Typography>
                )}
                {hasPermission('view_reports') && (
                  <Typography variant="body2" sx={{ color: 'green' }}>
                    ✅ Reports should show
                  </Typography>
                )}
                {hasPermission('view_tenants') && (
                  <Typography variant="body2" sx={{ color: 'green' }}>
                    ✅ Tenants should show
                  </Typography>
                )}
                
                {!hasPermission('view_dashboard') && 
                 !hasPermission('view_citizens') && 
                 !hasPermission('view_idcards') && (
                  <Typography variant="body2" sx={{ color: 'red' }}>
                    ❌ No navigation items should show - check permissions!
                  </Typography>
                )}
              </Box>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">Raw User Object</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Paper sx={{ p: 2, bgcolor: 'grey.100' }}>
                <pre style={{ fontSize: '12px', overflow: 'auto' }}>
                  {JSON.stringify(user, null, 2)}
                </pre>
              </Paper>
            </AccordionDetails>
          </Accordion>
        </>
      )}

      {!user && !loading && (
        <Paper sx={{ p: 2, bgcolor: 'error.light' }}>
          <Typography color="error">
            No user object found. Please log in.
          </Typography>
        </Paper>
      )}
    </Box>
  );
};

export default UserDebug;
