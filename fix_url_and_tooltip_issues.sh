#!/bin/bash

# Fix URL and Tooltip Issues Script
# This script fixes the duplicate "tenants" URL issue and MUI Tooltip warning

echo "🔧 Fixing URL and Tooltip Issues"
echo "================================="

# Step 1: Test current state
echo "📍 Step 1: Testing current endpoints..."
echo "Testing tenant list endpoint..."
curl -s -o /dev/null -w "Tenant List Status: %{http_code}\n" http://************:8000/api/tenants/

# Step 2: Restart frontend service to apply URL fixes
echo ""
echo "📍 Step 2: Restarting frontend service to apply fixes..."
docker-compose restart frontend

# Step 3: Wait for service to restart
echo "⏳ Waiting for frontend to restart..."
sleep 20

# Step 4: Test again
echo ""
echo "📍 Step 3: Testing after restart..."
echo "Testing tenant list endpoint again..."
curl -s -o /dev/null -w "Tenant List Status: %{http_code}\n" http://************:8000/api/tenants/

echo ""
echo "✅ URL and Tooltip fixes completed!"
echo ""
echo "🔍 Issues Fixed:"
echo "1. ✅ Fixed duplicate 'tenants' in URL paths:"
echo "   - Before: /api/tenants/tenants/ (❌ incorrect)"
echo "   - After:  /api/tenants/ (✅ correct)"
echo ""
echo "2. ✅ Fixed MUI Tooltip warning for disabled buttons:"
echo "   - Wrapped disabled refresh button with <span> element"
echo "   - Added conditional tooltip text based on loading state"
echo ""
echo "🌐 Fixed URLs:"
echo "   Tenant List: /api/tenants/"
echo "   Tenant Delete: /api/tenants/{id}/"
echo ""
echo "💡 Manual testing:"
echo "1. Open frontend: http://************:3000/"
echo "2. Navigate to Tenants section"
echo "3. Check browser console (F12) for:"
echo "   - No more 404 errors for tenant endpoints"
echo "   - No more MUI Tooltip warnings"
echo "4. Test refresh button tooltip (should show different text when loading)"
echo ""
echo "🔍 If still seeing issues:"
echo "- Clear browser cache and reload"
echo "- Check browser console for any remaining errors"
echo "- Verify backend URL patterns are correct"
