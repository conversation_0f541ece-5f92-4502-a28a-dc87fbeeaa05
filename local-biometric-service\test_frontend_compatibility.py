#!/usr/bin/env python3
"""
Test Frontend Compatibility After Fix
"""

import urllib.request
import json

def test_device_status_response():
    """Test the device status response format"""
    
    print("=== TESTING FRONTEND COMPATIBILITY ===")
    print("Testing device status response format...")
    
    try:
        response = urllib.request.urlopen("http://localhost:8001/api/device/status", timeout=5)
        if response.getcode() == 200:
            data = json.loads(response.read().decode())
            
            print("✅ Service responded successfully")
            print("\nResponse structure:")
            print(json.dumps(data, indent=2))
            
            # Check for required fields
            required_fields = ['success', 'device_connected', 'connected']
            missing_fields = []
            
            for field in required_fields:
                if field not in data:
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"\n❌ Missing required fields: {missing_fields}")
                return False
            
            # Check data structure
            if 'data' in data:
                data_obj = data['data']
                print(f"\n✅ Data object present")
                print(f"   - connected: {data_obj.get('connected')}")
                print(f"   - device_connected: {data_obj.get('device_connected')}")
                print(f"   - initialized: {data_obj.get('initialized')}")
                print(f"   - model: {data_obj.get('model')}")
            
            # Test frontend compatibility
            print(f"\n🔍 Frontend Compatibility Check:")
            print(f"   - statusResult.success: {data.get('success')}")
            print(f"   - deviceStatus.device_connected: {data.get('device_connected')}")
            print(f"   - deviceStatus.connected: {data.get('connected')}")
            print(f"   - deviceData.device_connected: {data.get('data', {}).get('device_connected')}")
            print(f"   - deviceData.connected: {data.get('data', {}).get('connected')}")
            
            # Simulate frontend logic
            device_status = data.get('data') or data
            is_connected = device_status.get('device_connected') or device_status.get('connected') or False
            is_initialized = device_status.get('initialized', True)
            
            print(f"\n🎯 Frontend Logic Simulation:")
            print(f"   - Final is_connected: {is_connected}")
            print(f"   - Final is_initialized: {is_initialized}")
            print(f"   - Device will show as: {'Connected' if is_connected and is_initialized else 'Disconnected'}")
            
            if is_connected and is_initialized:
                print("\n🎉 SUCCESS! Frontend should now connect properly!")
                return True
            else:
                print("\n❌ FAILED! Frontend will still show as disconnected")
                return False
            
        else:
            print(f"❌ Service returned status: {response.getcode()}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == '__main__':
    print("Frontend Compatibility Test")
    print("=" * 40)
    
    success = test_device_status_response()
    
    if success:
        print("\n🎉 COMPATIBILITY TEST PASSED!")
        print("✅ The frontend should now connect to the JAR bridge service")
        print("\nNext steps:")
        print("1. Restart your GoID frontend if it's running")
        print("2. Navigate to citizen registration")
        print("3. The biometric capture should now show 'Connected'")
    else:
        print("\n❌ COMPATIBILITY TEST FAILED!")
        print("❌ There may still be connection issues")
    
    print("\nTest completed.")
    input("Press Enter to exit...")
