@echo off
echo 🧪 Testing GoID Deployment
echo ==========================
echo.

REM Stop any existing containers
echo 🛑 Stopping existing containers...
docker-compose -f docker-compose.production.yml down

REM Remove old containers and images to ensure clean build
echo 🧹 Cleaning up old containers...
docker-compose -f docker-compose.production.yml rm -f

REM Build and start services
echo 🔨 Building and starting services...
docker-compose -f docker-compose.production.yml up -d --build

REM Wait for services to start
echo ⏳ Waiting for services to start...
timeout /t 45 /nobreak >nul

REM Check service status
echo 🔍 Checking service status...
docker-compose -f docker-compose.production.yml ps

echo.
echo 🧪 Testing Services...
echo =====================

REM Test backend health
echo 📡 Testing backend API...
curl -s http://localhost:8000/api/health/ || echo "❌ Backend not responding"

REM Test frontend
echo 🌐 Testing frontend...
curl -s http://localhost:3000/health || echo "❌ Frontend not responding"

REM Test pgAdmin
echo 🗄️ Testing pgAdmin...
curl -s http://localhost:5050/misc/ping || echo "❌ pgAdmin not responding"

echo.
echo 📋 Service URLs:
echo ================
echo 🌐 Frontend: http://localhost:3000
echo 📡 Backend: http://localhost:8000
echo 🗄️ pgAdmin: http://localhost:5050
echo.

REM Show logs if there are issues
echo 📜 Recent logs:
echo ===============
docker-compose -f docker-compose.production.yml logs --tail=10

echo.
echo ✅ Test complete! Check the URLs above.
pause
