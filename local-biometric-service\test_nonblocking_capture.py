#!/usr/bin/env python3
"""
Test non-blocking frame capture with timeout
"""

import ctypes
from ctypes import cdll, c_int, c_uint, c_void_p, POINTER, byref
import logging
import time
import threading
import queue

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Constants
FTR_OK = 0
FTR_IMAGE_WIDTH = 320
FTR_IMAGE_HEIGHT = 480
FTR_IMAGE_SIZE = FTR_IMAGE_WIDTH * FTR_IMAGE_HEIGHT

class NonBlockingCapture:
    def __init__(self):
        self.scan_api = None
        self.device_handle = None
        
    def initialize(self):
        """Initialize the device"""
        try:
            # Load scan API
            self.scan_api = cdll.LoadLibrary('./ftrScanAPI.dll')
            
            # Setup function signatures
            self.scan_api.ftrScanOpenDevice.restype = c_void_p
            self.scan_api.ftrScanCloseDevice.argtypes = [c_void_p]
            self.scan_api.ftrScanCloseDevice.restype = c_int
            self.scan_api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(ctypes.c_ubyte), POINTER(c_uint)]
            self.scan_api.ftrScanGetFrame.restype = c_int
            
            # Open device
            logger.info("📱 Opening device...")
            self.device_handle = self.scan_api.ftrScanOpenDevice()
            
            if not self.device_handle:
                logger.error("❌ Failed to open device")
                return False
            
            logger.info(f"✅ Device opened! Handle: {self.device_handle}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Initialization failed: {e}")
            return False
    
    def capture_frame_thread(self, result_queue, timeout_seconds=5):
        """Capture frame in a separate thread"""
        try:
            logger.info(f"📸 Starting frame capture (timeout: {timeout_seconds}s)...")
            
            # Create buffer
            image_buffer = (ctypes.c_ubyte * FTR_IMAGE_SIZE)()
            frame_size = c_uint(FTR_IMAGE_SIZE)
            
            # Attempt capture
            start_time = time.time()
            result = self.scan_api.ftrScanGetFrame(self.device_handle, image_buffer, byref(frame_size))
            end_time = time.time()
            
            # Put result in queue
            result_queue.put({
                'success': True,
                'result_code': result,
                'frame_size': frame_size.value,
                'capture_time': end_time - start_time,
                'image_buffer': image_buffer if result == FTR_OK else None
            })
            
        except Exception as e:
            logger.error(f"❌ Capture thread error: {e}")
            result_queue.put({
                'success': False,
                'error': str(e)
            })
    
    def test_capture_with_timeout(self, timeout_seconds=5):
        """Test frame capture with timeout"""
        try:
            # Create queue for results
            result_queue = queue.Queue()
            
            # Start capture in separate thread
            capture_thread = threading.Thread(
                target=self.capture_frame_thread,
                args=(result_queue, timeout_seconds)
            )
            capture_thread.daemon = True
            capture_thread.start()
            
            # Wait for result with timeout
            try:
                result = result_queue.get(timeout=timeout_seconds)
                
                if result['success']:
                    logger.info(f"✅ Capture completed in {result['capture_time']:.2f}s")
                    logger.info(f"📊 Result code: {result['result_code']}")
                    logger.info(f"📊 Frame size: {result['frame_size']}")
                    
                    if result['result_code'] == FTR_OK and result['frame_size'] > 0:
                        # Analyze image data
                        image_buffer = result['image_buffer']
                        sample_size = min(1000, result['frame_size'])
                        
                        if image_buffer:
                            pixel_sum = sum(image_buffer[i] for i in range(sample_size))
                            avg_pixel = pixel_sum / sample_size
                            
                            non_zero_pixels = sum(1 for i in range(sample_size) if image_buffer[i] > 5)
                            data_percentage = (non_zero_pixels / sample_size) * 100
                            
                            logger.info(f"📊 Average pixel: {avg_pixel:.2f}")
                            logger.info(f"📊 Data percentage: {data_percentage:.1f}%")
                            logger.info(f"📊 Has image data: {'Yes' if data_percentage > 5 else 'No'}")
                            
                            return {
                                'success': True,
                                'result_code': result['result_code'],
                                'frame_size': result['frame_size'],
                                'avg_pixel': avg_pixel,
                                'data_percentage': data_percentage,
                                'has_data': data_percentage > 5
                            }
                    
                    return {
                        'success': True,
                        'result_code': result['result_code'],
                        'frame_size': result['frame_size'],
                        'has_data': False
                    }
                else:
                    logger.error(f"❌ Capture failed: {result['error']}")
                    return {'success': False, 'error': result['error']}
                    
            except queue.Empty:
                logger.error(f"❌ Capture timed out after {timeout_seconds} seconds")
                return {'success': False, 'error': 'Timeout'}
                
        except Exception as e:
            logger.error(f"❌ Test capture error: {e}")
            return {'success': False, 'error': str(e)}
    
    def close(self):
        """Close the device"""
        if self.device_handle and self.scan_api:
            try:
                result = self.scan_api.ftrScanCloseDevice(self.device_handle)
                logger.info(f"🔒 Device closed (result: {result})")
            except Exception as e:
                logger.error(f"❌ Close error: {e}")

def main():
    """Main test function"""
    logger.info("🚀 Non-Blocking Capture Test")
    logger.info("=" * 30)
    
    capture = NonBlockingCapture()
    
    # Initialize device
    if not capture.initialize():
        logger.error("❌ Failed to initialize device")
        return False
    
    # Test 1: Capture without finger
    logger.info("\n📋 Test 1: Capture without finger")
    result1 = capture.test_capture_with_timeout(5)
    
    if result1['success']:
        logger.info("✅ Capture completed (no timeout)")
        if result1.get('has_data', False):
            logger.info("🎉 Image data detected!")
        else:
            logger.info("⚠️ No image data (expected without finger)")
    else:
        logger.error(f"❌ Capture failed: {result1.get('error', 'Unknown')}")
    
    # Test 2: Capture with finger
    logger.info("\n📋 Test 2: Capture with finger")
    logger.info("👆 Please place your finger on the scanner...")
    input("Press Enter when finger is placed...")
    
    result2 = capture.test_capture_with_timeout(5)
    
    if result2['success']:
        logger.info("✅ Capture completed")
        if result2.get('has_data', False):
            logger.info("🎉 Fingerprint data captured!")
        else:
            logger.info("⚠️ No fingerprint data detected")
    else:
        logger.error(f"❌ Capture failed: {result2.get('error', 'Unknown')}")
    
    # Summary
    logger.info("\n📊 Test Summary:")
    logger.info("=" * 20)
    logger.info(f"Without finger: {'✅ Success' if result1['success'] else '❌ Failed'}")
    logger.info(f"With finger: {'✅ Success' if result2['success'] else '❌ Failed'}")
    
    if result1['success'] and result2['success']:
        logger.info("🎉 Device capture is working!")
        logger.info("💡 The hanging issue is resolved with timeout")
    elif result1['success']:
        logger.info("⚠️ Device responds but may need driver/hardware check")
    else:
        logger.info("❌ Device capture has fundamental issues")
    
    # Cleanup
    capture.close()
    
    return result1['success'] or result2['success']

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    exit(0 if success else 1)
