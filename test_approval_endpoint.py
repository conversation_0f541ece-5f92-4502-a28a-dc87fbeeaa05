#!/usr/bin/env python3
"""
Simple test script to verify the approval endpoint is working.
Run this after starting the Django server.
"""

import requests
import json

def test_approval_endpoint():
    """Test the approval endpoint with a simple request"""
    
    print("🧪 Testing Approval Endpoint")
    print("=" * 40)
    
    # Test configuration
    BASE_URL = "http://localhost:8000"
    TENANT_ID = 1  # Replace with your actual tenant ID
    ID_CARD_ID = 1  # Replace with your actual ID card ID
    
    # Test URL
    url = f"{BASE_URL}/api/tenants/{TENANT_ID}/idcards/{ID_CARD_ID}/approval_action/"
    
    print(f"🔗 Testing URL: {url}")
    
    # Test data
    test_data = {
        "action": "submit_for_approval",
        "comment": "Test submission"
    }
    
    headers = {
        'Content-Type': 'application/json',
        # Note: You'll need to add authentication headers in a real test
        # 'Authorization': 'Bearer YOUR_JWT_TOKEN'
    }
    
    try:
        print(f"📤 Sending POST request...")
        print(f"📄 Data: {json.dumps(test_data, indent=2)}")
        
        response = requests.post(url, json=test_data, headers=headers, timeout=10)
        
        print(f"📥 Response Status: {response.status_code}")
        
        if response.status_code == 404:
            print("❌ 404 Error - URL not found!")
            print("🔍 Possible causes:")
            print("   1. Django server not restarted after URL changes")
            print("   2. URL pattern not registered correctly")
            print("   3. Tenant ID or ID card ID doesn't exist")
            print("   4. URL path is incorrect")
            
        elif response.status_code == 401:
            print("🔐 401 Error - Authentication required (this is expected)")
            print("✅ URL is working! You just need to authenticate")
            
        elif response.status_code == 403:
            print("🚫 403 Error - Permission denied")
            print("✅ URL is working! Check user permissions")
            
        elif response.status_code == 400:
            print("📝 400 Error - Bad request")
            print("✅ URL is working! Check request data format")
            
        else:
            print(f"📄 Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error!")
        print("🔍 Make sure Django server is running:")
        print("   cd backend")
        print("   python manage.py runserver")
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def test_url_variations():
    """Test different URL variations to find the correct one"""
    
    print("\n🔍 Testing URL Variations")
    print("=" * 30)
    
    BASE_URL = "http://localhost:8000"
    TENANT_ID = 1
    ID_CARD_ID = 1
    
    # Different URL patterns to test
    urls_to_test = [
        f"{BASE_URL}/api/tenants/{TENANT_ID}/idcards/{ID_CARD_ID}/approval_action/",
        f"{BASE_URL}/api/tenants/{TENANT_ID}/idcards/{ID_CARD_ID}/approval-action/",
        f"{BASE_URL}/api/idcards/{ID_CARD_ID}/approval_action/",
        f"{BASE_URL}/api/idcards/{ID_CARD_ID}/approval-action/",
    ]
    
    for url in urls_to_test:
        print(f"\n🔗 Testing: {url}")
        try:
            response = requests.get(url, timeout=5)
            if response.status_code != 404:
                print(f"✅ Found! Status: {response.status_code}")
            else:
                print("❌ 404 - Not found")
        except requests.exceptions.ConnectionError:
            print("❌ Connection error")
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_approval_endpoint()
    test_url_variations()
    
    print("\n" + "=" * 50)
    print("🏁 Testing completed!")
    print("\n💡 Quick fixes to try:")
    print("1. Restart Django server: python manage.py runserver")
    print("2. Apply migrations: python manage.py migrate")
    print("3. Check if tenant and ID card exist in database")
    print("4. Verify authentication token is valid")
