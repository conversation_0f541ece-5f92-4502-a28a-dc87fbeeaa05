import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  TextField,
  InputAdornment,
  IconButton,
  Grid,
  Chip,
  Pagination,
  CircularProgress,
  Tooltip,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  CreditCard as CardIcon,
} from '@mui/icons-material';
import axios from '../../utils/axios';
import { useAuth } from '../../contexts/AuthContext';
import { useLocalization } from '../../contexts/LocalizationContext';
import { canCreateCitizens, canEditCitizens, canCreateIDCards } from '../../utils/permissions';

// Mock data for citizens
const mockCitizens = [
  {
    id: 1,
    first_name: '<PERSON><PERSON>',
    middle_name: '<PERSON><PERSON><PERSON>',
    last_name: '<PERSON><PERSON><PERSON>',
    gender: 'male',
    date_of_birth: '1985-05-15',
    phone_number: '+251911234567',
    has_id_card: true,
  },
  {
    id: 2,
    first_name: '<PERSON>',
    middle_name: '<PERSON>',
    last_name: '<PERSON>',
    gender: 'female',
    date_of_birth: '1990-08-22',
    phone_number: '+251922345678',
    has_id_card: true,
  },
  {
    id: 3,
    first_name: 'Daniel',
    middle_name: 'Tesfaye',
    last_name: 'Bekele',
    gender: 'male',
    date_of_birth: '1978-12-10',
    phone_number: '+251933456789',
    has_id_card: false,
  },
  {
    id: 4,
    first_name: 'Hiwot',
    middle_name: 'Girma',
    last_name: 'Haile',
    gender: 'female',
    date_of_birth: '1995-03-28',
    phone_number: '+251944567890',
    has_id_card: false,
  },
];

const CitizenList = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { t } = useLocalization();
  const [citizens, setCitizens] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    fetchCitizens();
  }, [page, searchTerm]);

  const fetchCitizens = async () => {
    try {
      setLoading(true);
      const tenantId = user?.tenant_id;

      if (!tenantId) {
        console.error('No tenant ID found');
        setLoading(false);
        return;
      }

      console.log('🔍 Fetching citizens for tenant:', tenantId);

      const response = await axios.get(`/api/tenants/${tenantId}/citizens/`, {
        params: {
          page: page,
          search: searchTerm,
          page_size: 12  // Show 12 citizens per page (3x4 grid)
        }
      });

      console.log('🔍 Citizens response:', response.data);

      if (response.data.results) {
        setCitizens(response.data.results);
        setTotalPages(Math.ceil(response.data.count / 12));
      } else {
        setCitizens(response.data);
        setTotalPages(1);
      }

      setLoading(false);
    } catch (error) {
      console.error('Error fetching citizens:', error);
      // Fallback to mock data if API fails
      setCitizens(mockCitizens);
      setTotalPages(Math.ceil(mockCitizens.length / 12));
      setLoading(false);
    }
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    setPage(1); // Reset to first page when searching
  };

  const handlePageChange = (event, value) => {
    setPage(value);
  };

  const handleViewCitizen = (id) => {
    navigate(`/citizens/${id}/view`);
  };

  const handleEditCitizen = (id) => {
    navigate(`/citizens/${id}`);
  };

  const handleCreateIDCard = (id) => {
    navigate(`/citizens/${id}/idcards/create`);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          {t('citizens', 'Citizens')}
        </Typography>
        {canCreateCitizens(user) && (
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => navigate('/citizens/create')}
          >
            {t('register_new_citizen', 'Register New Citizen')}
          </Button>
        )}
      </Box>

      {/* Search Bar */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <TextField
            fullWidth
            placeholder={t('search_citizens_placeholder', 'Search citizens by name, ID, or phone number...')}
            value={searchTerm}
            onChange={handleSearch}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
        </CardContent>
      </Card>

      {/* Citizens List */}
      <Grid container spacing={3}>
        {citizens.map((citizen) => (
          <Grid item xs={12} sm={6} md={4} key={citizen.id}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="h6" component="h2" sx={{ mb: 0.5 }}>
                      {`${citizen.first_name} ${citizen.middle_name}`}
                    </Typography>
                    {/* Amharic Name Display */}
                    {(citizen.first_name_am || citizen.middle_name_am || citizen.last_name_am) && (
                      <Typography variant="body2" color="primary.main" sx={{ fontWeight: 'medium', mb: 1 }}>
                        {[citizen.first_name_am, citizen.middle_name_am, citizen.last_name_am]
                          .filter(Boolean)
                          .join(' ')}
                      </Typography>
                    )}
                  </Box>
                  <Chip
                    label={citizen.has_id_card ? 'Has ID Card' : 'No ID Card'}
                    color={citizen.has_id_card ? 'success' : 'warning'}
                    size="small"
                  />
                </Box>
                <Typography color="text.secondary" gutterBottom>
                  {citizen.last_name}
                </Typography>
                <Typography variant="body2" sx={{ mb: 1, fontFamily: 'monospace', color: 'success.main' }}>
                  ID: {citizen.digital_id || 'Not generated'}
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  Gender: {citizen.gender === 'male' ? 'Male' : 'Female'}
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  DOB: {new Date(citizen.date_of_birth).toLocaleDateString()}
                </Typography>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  Phone: {citizen.phone || 'Not provided'}
                </Typography>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Tooltip title="View Citizen">
                    <IconButton
                      color="primary"
                      onClick={() => handleViewCitizen(citizen.id)}
                    >
                      <ViewIcon />
                    </IconButton>
                  </Tooltip>
                  {canEditCitizens(user) && (
                    <Tooltip title="Edit Citizen">
                      <IconButton
                        color="secondary"
                        onClick={() => handleEditCitizen(citizen.id)}
                      >
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                  )}
                  {citizen.has_id_card ? (
                    <Tooltip title="View ID Card">
                      <IconButton
                        color="success"
                        onClick={() => handleCreateIDCard(citizen.id)}
                      >
                        <CardIcon />
                      </IconButton>
                    </Tooltip>
                  ) : canCreateIDCards(user) && (
                    <Tooltip title="Create ID Card">
                      <IconButton
                        color="success"
                        onClick={() => handleCreateIDCard(citizen.id)}
                      >
                        <CardIcon />
                      </IconButton>
                    </Tooltip>
                  )}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Pagination */}
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <Pagination
          count={totalPages}
          page={page}
          onChange={handlePageChange}
          color="primary"
        />
      </Box>
    </Box>
  );
};

export default CitizenList;
