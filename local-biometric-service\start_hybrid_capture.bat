@echo off
echo ========================================
echo   HYBRID CAPTURE Service
echo   Official Software + Automated Database
echo ========================================
echo.

echo BREAKTHROUGH: Official software opens successfully!
echo.
echo NEXT STEPS: Bridge official software to GoID database
echo.
echo HYBRID SOLUTION:
echo   1. Official Futronic software captures fingerprints (WORKING)
echo   2. File watcher detects saved fingerprint images
echo   3. Automatic processing and quality analysis
echo   4. Direct integration with GoID database
echo   5. Real-time fingerprint data storage
echo.

echo Checking Python installation...
python --version
if errorlevel 1 (
    echo ERROR: Python not found. Please install Python 3.7+
    pause
    exit /b 1
)

echo.
echo Installing required packages...
pip install -r requirements_hybrid.txt

echo.
echo Checking for official Futronic executable...
if exist "ftrScanApiEx.exe" (
    echo SUCCESS: ftrScanApiEx.exe found
) else if exist "ftrScanAPI.exe" (
    echo SUCCESS: ftrScanAPI.exe found
) else (
    echo WARNING: Official Futronic executable not found
    echo Please ensure ftrScanApiEx.exe is in the current directory
    echo.
    pause
)

echo.
echo ========================================
echo   HYBRID CAPTURE WORKFLOW
echo ========================================
echo.
echo STEP 1: Start Hybrid Service
echo   - Service starts with file watcher
echo   - Creates captured_fingerprints directory
echo   - Ready to detect new fingerprint files
echo.
echo STEP 2: Capture Fingerprints
echo   - Click "Start Left Thumb Capture"
echo   - Official software opens automatically
echo   - Capture fingerprint in official software
echo   - Save to suggested filename
echo.
echo STEP 3: Automatic Processing
echo   - File watcher detects saved image
echo   - Automatic quality analysis
echo   - GoID-compatible template generation
echo   - Direct database storage
echo.
echo STEP 4: GoID Integration
echo   - Fingerprint data sent to GoID API
echo   - Compatible with existing citizen registration
echo   - Real-time biometric duplicate detection
echo   - Production-ready data format
echo.

echo Starting HYBRID CAPTURE Service...
echo Service will be available at: http://localhost:8001
echo.
echo This service combines the working official software
echo with automated GoID database integration!
echo.
echo Press Ctrl+C to stop the service
echo.

python hybrid_capture_service.py

echo.
echo Service stopped.
pause
