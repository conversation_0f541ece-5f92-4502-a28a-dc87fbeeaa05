import React from 'react';
import { useLocalization } from '../contexts/LocalizationContext';
import LanguageSwitcher from '../components/common/LanguageSwitcher';
import { 
  Box, 
  Paper, 
  Typography, 
  Grid, 
  Button,
  Card,
  CardContent,
  Divider
} from '@mui/material';

const TranslationTest = () => {
  const { t, currentLanguage, isAmharic } = useLocalization();

  const testKeys = [
    'dashboard',
    'citizens', 
    'id_cards',
    'login',
    'password',
    'save',
    'cancel',
    'loading',
    'success',
    'error'
  ];

  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>
      <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          Translation System Test
        </Typography>
        
        <Typography variant="body1" sx={{ mb: 2 }}>
          Current Language: <strong>{currentLanguage}</strong> 
          {isAmharic ? ' (አማርኛ)' : ' (English)'}
        </Typography>
        
        <Box sx={{ mb: 3 }}>
          <LanguageSwitcher />
        </Box>
        
        <Divider sx={{ my: 3 }} />
        
        <Typography variant="h5" gutterBottom>
          Translation Test Results
        </Typography>
        
        <Grid container spacing={2}>
          {testKeys.map((key) => (
            <Grid item xs={12} sm={6} md={4} key={key}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="body2" color="text.secondary">
                    Key: {key}
                  </Typography>
                  <Typography variant="h6" sx={{ 
                    fontFamily: isAmharic ? 'Noto Sans Ethiopic, serif' : 'inherit'
                  }}>
                    {t(key, `[Missing: ${key}]`)}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
        
        <Divider sx={{ my: 3 }} />
        
        <Typography variant="h6" gutterBottom>
          Navigation Menu Items
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
          {['dashboard', 'citizens', 'id_cards', 'clearance', 'transfer', 'print_queue'].map((key) => (
            <Button 
              key={key}
              variant="outlined" 
              size="small"
              sx={{ 
                fontFamily: isAmharic ? 'Noto Sans Ethiopic, serif' : 'inherit'
              }}
            >
              {t(key, key)}
            </Button>
          ))}
        </Box>
        
        <Typography variant="h6" gutterBottom>
          Authentication Items  
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
          {['login', 'password', 'email', 'username', 'remember_me', 'forgot_password'].map((key) => (
            <Button 
              key={key}
              variant="outlined" 
              size="small"
              sx={{ 
                fontFamily: isAmharic ? 'Noto Sans Ethiopic, serif' : 'inherit'
              }}
            >
              {t(key, key)}
            </Button>
          ))}
        </Box>
        
        <Typography variant="h6" gutterBottom>
          Action Buttons
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          {['save', 'cancel', 'submit', 'edit', 'delete', 'view', 'search'].map((key) => (
            <Button 
              key={key}
              variant="contained" 
              size="small"
              sx={{ 
                fontFamily: isAmharic ? 'Noto Sans Ethiopic, serif' : 'inherit'
              }}
            >
              {t(key, key)}
            </Button>
          ))}
        </Box>
      </Paper>
    </Box>
  );
};

export default TranslationTest;
