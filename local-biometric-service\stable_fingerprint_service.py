#!/usr/bin/env python3
"""
Stable Fingerprint Service - Simplified version for better reliability
"""

import logging
import ctypes
from ctypes import cdll, c_int, c_uint, c_void_p, POINTER, byref
import time
import base64
import json
from datetime import datetime
from flask import Flask, jsonify, request
from flask_cors import CORS

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Enable CORS for all routes to allow access from Docker containers
CORS(app, origins=['http://localhost:3000', 'http://127.0.0.1:3000', 'http://0.0.0.0:3000'])

# Constants
FTR_OK = 0
FTR_IMAGE_WIDTH = 320
FTR_IMAGE_HEIGHT = 480
FTR_IMAGE_SIZE = FTR_IMAGE_WIDTH * FTR_IMAGE_HEIGHT

class StableFingerprintDevice:
    def __init__(self):
        self.scan_api = None
        self.device_handle = None
        self.connected = False
        self.initialized = False
    
    def initialize(self):
        """Initialize the Futronic device"""
        try:
            logger.info("🔧 Loading Futronic SDK...")
            self.scan_api = cdll.LoadLibrary('./ftrScanAPI.dll')
            
            # Setup function signatures
            self.scan_api.ftrScanOpenDevice.restype = c_void_p
            self.scan_api.ftrScanCloseDevice.argtypes = [c_void_p]
            self.scan_api.ftrScanCloseDevice.restype = c_int
            self.scan_api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(ctypes.c_ubyte), POINTER(c_uint)]
            self.scan_api.ftrScanGetFrame.restype = c_int
            
            logger.info("📱 Opening device...")
            self.device_handle = self.scan_api.ftrScanOpenDevice()
            
            if self.device_handle:
                logger.info(f"✅ Device opened! Handle: {self.device_handle}")
                self.connected = True
                self.initialized = True
                return True
            else:
                logger.error("❌ Failed to open device")
                return False
                
        except Exception as e:
            logger.error(f"❌ Initialization error: {e}")
            return False
    
    def capture_fingerprint_direct(self, thumb_type='left'):
        """Capture fingerprint directly without finger detection"""
        try:
            if not self.connected:
                logger.error("❌ Device not connected")
                return {
                    'success': False,
                    'error': 'Device not connected'
                }
            
            logger.info(f"🔍 Starting {thumb_type} thumb capture (direct mode)...")
            logger.info("👆 Please place your finger on the scanner and press capture...")
            
            # Give user time to place finger
            time.sleep(2)
            
            # Capture frame directly
            image_buffer = (ctypes.c_ubyte * FTR_IMAGE_SIZE)()
            frame_size = c_uint(FTR_IMAGE_SIZE)
            
            logger.info("📸 Capturing fingerprint frame...")
            result = self.scan_api.ftrScanGetFrame(self.device_handle, image_buffer, byref(frame_size))
            
            if result == FTR_OK:
                logger.info(f"✅ Frame captured! Size: {frame_size.value} bytes")
                
                # Convert to bytes
                image_data = bytes(image_buffer[:frame_size.value])
                
                # Analyze quality
                quality_info = self.analyze_image_quality(image_data, frame_size.value)
                
                # Create template data
                template_data = {
                    'version': '1.0',
                    'thumb_type': thumb_type,
                    'image_width': FTR_IMAGE_WIDTH,
                    'image_height': FTR_IMAGE_HEIGHT,
                    'frame_size': frame_size.value,
                    'quality_score': quality_info['quality_score'],
                    'capture_time': datetime.now().isoformat(),
                    'device_info': {
                        'model': 'Futronic FS88H',
                        'interface': 'stable_sdk',
                        'real_device': True
                    },
                    'image_hash': hash(image_data) & 0x7FFFFFFF
                }
                
                # Encode template
                template_encoded = base64.b64encode(json.dumps(template_data).encode()).decode()
                
                logger.info(f"✅ Fingerprint captured successfully!")
                logger.info(f"📊 Quality: {quality_info['quality_score']}%")
                logger.info(f"📊 Frame size: {frame_size.value} bytes")
                
                return {
                    'success': True,
                    'data': {
                        'thumb_type': thumb_type,
                        'template_data': template_encoded,
                        'quality_score': quality_info['quality_score'],
                        'minutiae_count': quality_info['estimated_minutiae'],
                        'capture_time': template_data['capture_time'],
                        'device_info': template_data['device_info'],
                        'frame_size': frame_size.value
                    }
                }
            else:
                logger.error(f"❌ Frame capture failed with code: {result}")
                return {
                    'success': False,
                    'error': f'Frame capture failed with code: {result}. Please ensure your finger is on the scanner.'
                }
                
        except Exception as e:
            logger.error(f"❌ Capture error: {e}")
            import traceback
            logger.error(f"   Traceback: {traceback.format_exc()}")
            return {
                'success': False,
                'error': f'Capture error: {str(e)}'
            }
    
    def analyze_image_quality(self, image_data, frame_size):
        """Analyze captured image quality"""
        try:
            if not image_data or frame_size < 100:
                return {'quality_score': 0, 'estimated_minutiae': 0}
            
            # Sample pixels for analysis
            sample_size = min(2000, len(image_data))
            pixels = list(image_data[:sample_size])
            
            # Calculate statistics
            non_zero_pixels = sum(1 for p in pixels if p > 10)
            data_percentage = (non_zero_pixels / sample_size) * 100
            
            avg_pixel = sum(pixels) / len(pixels)
            pixel_variance = sum((p - avg_pixel) ** 2 for p in pixels) / len(pixels)
            
            # Calculate quality score
            quality_score = min(100, max(0, 
                (data_percentage * 0.6) + 
                (min(pixel_variance / 10, 40)) +
                (min(avg_pixel / 2, 20))
            ))
            
            # Estimate minutiae count based on quality
            estimated_minutiae = max(20, min(60, int(quality_score * 0.8)))
            
            return {
                'quality_score': int(quality_score),
                'estimated_minutiae': estimated_minutiae,
                'data_percentage': round(data_percentage, 1),
                'avg_pixel': round(avg_pixel, 1),
                'variance': round(pixel_variance, 1)
            }
            
        except Exception as e:
            logger.error(f"Quality analysis error: {e}")
            return {'quality_score': 50, 'estimated_minutiae': 30}
    
    def get_status(self):
        """Get device status"""
        return {
            'connected': self.connected,
            'initialized': self.initialized,
            'handle': self.device_handle,
            'model': 'Futronic FS88H',
            'interface': 'stable_sdk'
        }
    
    def close(self):
        """Close device"""
        if self.device_handle and self.scan_api:
            try:
                self.scan_api.ftrScanCloseDevice(self.device_handle)
                logger.info("🔒 Device closed")
            except Exception as e:
                logger.error(f"Close error: {e}")

# Global device
device = StableFingerprintDevice()

@app.route('/api/device/status', methods=['GET'])
def get_device_status():
    """Get device status"""
    try:
        status = device.get_status()
        return jsonify({
            'success': True,
            'data': status
        })
    except Exception as e:
        logger.error(f"❌ Device status error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/capture/fingerprint', methods=['GET', 'POST'])
def capture_fingerprint():
    """Capture real fingerprint using direct mode"""
    try:
        if request.method == 'GET':
            thumb_type = request.args.get('thumb_type', 'left')
        else:
            data = request.get_json() or {}
            thumb_type = data.get('thumb_type', 'left')
        
        if thumb_type not in ['left', 'right']:
            return jsonify({
                'success': False,
                'error': 'Invalid thumb_type. Must be "left" or "right"'
            }), 400
        
        logger.info(f"🌐 API: Received {thumb_type} thumb capture request")
        
        # Check device status before capture
        if not device.connected:
            logger.warning("⚠️ Device not connected, attempting to reconnect...")
            if not device.initialize():
                return jsonify({
                    'success': False,
                    'error': 'Device not connected and reconnection failed'
                }), 503
        
        result = device.capture_fingerprint_direct(thumb_type)
        
        if result['success']:
            logger.info(f"✅ API: {thumb_type} thumb capture successful")
            return jsonify(result)
        else:
            logger.warning(f"⚠️ API: {thumb_type} thumb capture failed: {result.get('error', 'Unknown error')}")
            return jsonify(result), 500
            
    except Exception as e:
        logger.error(f"❌ API: Capture endpoint error: {e}")
        return jsonify({
            'success': False,
            'error': f'Service error: {str(e)}'
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check"""
    return jsonify({
        'status': 'healthy',
        'service': 'Stable Fingerprint Service',
        'timestamp': datetime.now().isoformat(),
        'device_connected': device.connected
    })

@app.route('/', methods=['GET'])
def index():
    """Web interface"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Stable Fingerprint Service</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            button { padding: 15px 30px; margin: 10px; font-size: 16px; }
            .result { margin: 20px 0; padding: 15px; border-radius: 5px; }
            .success { background: #d4edda; color: #155724; }
            .error { background: #f8d7da; color: #721c24; }
        </style>
    </head>
    <body>
        <h1>🔒 Stable Fingerprint Service</h1>
        <p>This service captures fingerprints using direct frame capture (no finger detection).</p>
        <p><strong>Instructions:</strong> Place your finger on the scanner BEFORE clicking capture.</p>
        
        <button onclick="captureFingerprint('left')">👈 Capture Left Thumb</button>
        <button onclick="captureFingerprint('right')">👉 Capture Right Thumb</button>
        
        <div id="result"></div>
        
        <script>
            async function captureFingerprint(thumbType) {
                const resultDiv = document.getElementById('result');
                resultDiv.innerHTML = '<div class="result">📸 Starting capture... Make sure your finger is on the scanner!</div>';
                
                try {
                    const response = await fetch(`/api/capture/fingerprint?thumb_type=${thumbType}`);
                    const data = await response.json();
                    
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="result success">
                                <h3>✅ Fingerprint Captured Successfully!</h3>
                                <p><strong>Thumb:</strong> ${data.data.thumb_type}</p>
                                <p><strong>Quality:</strong> ${data.data.quality_score}%</p>
                                <p><strong>Frame Size:</strong> ${data.data.frame_size} bytes</p>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="result error">
                                <h3>❌ Capture Failed</h3>
                                <p>${data.error}</p>
                            </div>
                        `;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ Network Error</h3>
                            <p>${error.message}</p>
                        </div>
                    `;
                }
            }
        </script>
    </body>
    </html>
    """

if __name__ == '__main__':
    try:
        logger.info("🚀 Starting Stable Fingerprint Service")
        logger.info("=" * 50)
        
        # Initialize device
        logger.info("📱 Initializing Futronic FS88H device...")
        if device.initialize():
            logger.info("✅ Device ready for fingerprint capture")
            logger.info(f"📊 Device handle: {device.device_handle}")
            logger.info(f"🔗 Device connected: {device.connected}")
        else:
            logger.error("❌ Device initialization failed")
            exit(1)
        
        # Start service
        logger.info("=" * 50)
        logger.info("🌐 Service running at http://localhost:8001")
        logger.info("🔍 Health check: http://localhost:8001/api/health")
        logger.info("📱 Device status: http://localhost:8001/api/device/status")
        logger.info("👆 Ready for direct fingerprint capture!")
        logger.info("⚠️  Note: Place finger on scanner BEFORE clicking capture")
        logger.info("=" * 50)
        
        app.run(host='0.0.0.0', port=8001, debug=False, threaded=True, use_reloader=False)
        
    except KeyboardInterrupt:
        logger.info("🛑 Service stopped by user")
    except Exception as e:
        logger.error(f"❌ Service error: {e}")
    finally:
        logger.info("🔒 Closing device connection...")
        device.close()
        logger.info("👋 Service shutdown complete")
