#!/usr/bin/env python3
"""
Test Improved JAR Capture Workflow
"""

import urllib.request
import urllib.parse
import json
import time

def test_capture_workflow():
    """Test the improved capture workflow"""
    
    print("=== TESTING IMPROVED JAR CAPTURE ===")
    print("This will test the new timeout handling and process management")
    print()
    
    # Test device status first
    print("1. Testing device status...")
    try:
        response = urllib.request.urlopen("http://localhost:8001/api/device/status", timeout=5)
        if response.getcode() == 200:
            data = json.loads(response.read().decode())
            print("✅ Device status OK")
            print(f"   Connected: {data.get('connected', False)}")
        else:
            print("❌ Device status failed")
            return False
    except Exception as e:
        print(f"❌ Device status error: {e}")
        return False
    
    print()
    
    # Test capture
    print("2. Testing fingerprint capture...")
    print("💡 This will open the JAR application")
    print("💡 Please capture your fingerprint and close the application")
    print("💡 The test will wait up to 30 seconds")
    
    try:
        # Prepare capture request
        capture_data = json.dumps({"thumb_type": "left"}).encode()
        
        req = urllib.request.Request(
            "http://localhost:8001/api/capture/fingerprint",
            data=capture_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print("\n📡 Sending capture request...")
        start_time = time.time()
        
        response = urllib.request.urlopen(req, timeout=40)  # 40 second timeout
        
        if response.getcode() == 200:
            result = json.loads(response.read().decode())
            elapsed = time.time() - start_time
            
            print(f"✅ Capture completed in {elapsed:.1f} seconds")
            print(f"   Success: {result.get('success')}")
            print(f"   Method: {result.get('data', {}).get('method')}")
            print(f"   Quality: {result.get('data', {}).get('quality_score')}")
            print(f"   Real capture: {result.get('data', {}).get('real_capture')}")
            print(f"   Message: {result.get('message')}")
            
            if result.get('success'):
                print("\n🎉 CAPTURE TEST SUCCESSFUL!")
                return True
            else:
                print(f"\n❌ Capture failed: {result.get('error')}")
                return False
        else:
            print(f"❌ Capture request failed: {response.getcode()}")
            return False
            
    except urllib.error.URLError as e:
        if "timed out" in str(e):
            print("⏰ Capture request timed out")
            print("💡 This might mean the JAR application is still running")
            print("💡 Please close the JAR application manually")
        else:
            print(f"❌ Capture request error: {e}")
        return False
    except Exception as e:
        print(f"❌ Capture test error: {e}")
        return False

def main():
    print("Improved JAR Capture Test")
    print("=" * 30)
    
    print("This test will:")
    print("1. Check if the JAR bridge service is running")
    print("2. Test the improved capture workflow")
    print("3. Verify timeout handling works correctly")
    print()
    
    input("Press Enter to start the test...")
    print()
    
    success = test_capture_workflow()
    
    print("\n" + "=" * 30)
    if success:
        print("🎉 TEST PASSED!")
        print("✅ Improved capture workflow is working")
        print("✅ Ready for GoID integration")
        
        print("\nNext steps:")
        print("1. The JAR bridge service is working correctly")
        print("2. Try fingerprint capture in GoID frontend")
        print("3. The timeout issues should be resolved")
        
    else:
        print("❌ TEST FAILED!")
        print("❌ There may still be issues with the capture workflow")
        
        print("\nTroubleshooting:")
        print("1. Make sure the JAR bridge service is running")
        print("2. Ensure the JAR application can open properly")
        print("3. Check that Java is installed and working")
    
    print("\nTest completed.")
    input("Press Enter to exit...")

if __name__ == '__main__':
    main()
