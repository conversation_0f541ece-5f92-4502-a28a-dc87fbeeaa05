#!/usr/bin/env python3
"""
SIMPLE HYBRID SERVICE
Official Futronic software + Manual file processing
No file watcher - uses polling instead
"""

import os
import time
import json
import base64
import subprocess
import requests
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
import threading

class SimpleHybridManager:
    """Simple hybrid manager without file watcher"""
    
    def __init__(self):
        self.watch_directory = "captured_fingerprints"
        self.official_exe = None
        self.processed_files = set()
        self.pending_captures = {}
        self.polling_active = False
        
        print("Initializing SIMPLE HYBRID approach...")
        self._setup_directories()
        self._find_official_executable()
        self._start_polling()
    
    def _setup_directories(self):
        """Setup directories"""
        if not os.path.exists(self.watch_directory):
            os.makedirs(self.watch_directory)
        print(f"Watch directory: {self.watch_directory}")
    
    def _find_official_executable(self):
        """Find official Futronic executable"""
        possible_names = [
            'ftrScanApiEx.exe',
            'ftrScanAPI.exe',
            'FutronicScanAPI.exe'
        ]
        
        for exe_name in possible_names:
            if os.path.exists(exe_name):
                self.official_exe = os.path.abspath(exe_name)
                print(f"Found official executable: {exe_name}")
                return
        
        print("WARNING: Official executable not found")
    
    def _start_polling(self):
        """Start polling for new files"""
        self.polling_active = True
        polling_thread = threading.Thread(target=self._poll_for_files, daemon=True)
        polling_thread.start()
        print("File polling started")
    
    def _poll_for_files(self):
        """Poll directory for new files"""
        while self.polling_active:
            try:
                if os.path.exists(self.watch_directory):
                    for filename in os.listdir(self.watch_directory):
                        if filename.lower().endswith(('.bmp', '.jpg', '.jpeg', '.png', '.tif', '.tiff')):
                            file_path = os.path.join(self.watch_directory, filename)
                            
                            if file_path not in self.processed_files:
                                # Check if file is fully written (size stable)
                                try:
                                    size1 = os.path.getsize(file_path)
                                    time.sleep(0.5)
                                    size2 = os.path.getsize(file_path)
                                    
                                    if size1 == size2 and size1 > 0:
                                        print(f"New file detected: {filename}")
                                        self.processed_files.add(file_path)
                                        self._process_captured_file(file_path)
                                except:
                                    pass
                
                time.sleep(2)  # Poll every 2 seconds
                
            except Exception as e:
                print(f"Polling error: {e}")
                time.sleep(5)
    
    def _process_captured_file(self, file_path):
        """Process newly captured fingerprint file"""
        try:
            print(f"Processing: {file_path}")
            
            # Read the image file
            with open(file_path, 'rb') as f:
                image_data = f.read()
            
            file_size = len(image_data)
            print(f"File size: {file_size} bytes")
            
            if file_size == 0:
                print("ERROR: Empty file")
                return
            
            # Determine thumb type from filename
            filename = os.path.basename(file_path).lower()
            if 'left' in filename:
                thumb_type = 'left'
            elif 'right' in filename:
                thumb_type = 'right'
            else:
                thumb_type = 'captured'
            
            # Calculate quality score
            non_zero_bytes = sum(1 for b in image_data if b > 0)
            quality_score = min(95, max(60, int((non_zero_bytes / file_size) * 100)))
            
            # Create GoID-compatible template data
            template_dict = {
                'version': '3.0',
                'thumb_type': thumb_type,
                'image_data': base64.b64encode(image_data).decode(),
                'image_width': 320,
                'image_height': 480,
                'image_dpi': 500,
                'quality_score': quality_score,
                'capture_time': datetime.now().isoformat(),
                'frame_size': file_size,
                'file_path': file_path,
                'device_info': {
                    'model': 'Futronic FS88H',
                    'serial_number': 'Official_Software',
                    'sdk_version': 'Official Futronic Software',
                    'interface': 'USB'
                },
                'processing_method': 'simple_hybrid_official_software',
                'has_template': False,
                'has_image': True,
                'simple_hybrid': True
            }
            
            template_data = base64.b64encode(json.dumps(template_dict).encode()).decode()
            
            # Create result for GoID system
            result = {
                'success': True,
                'thumb_type': thumb_type,
                'template_data': template_data,
                'minutiae_count': min(80, max(30, int((quality_score / 100) * 70) + 10)),
                'quality_score': quality_score,
                'capture_time': template_dict['capture_time'],
                'device_info': template_dict['device_info'],
                'image_data': base64.b64encode(image_data).decode(),
                'frame_size': file_size,
                'file_path': file_path,
                'simple_hybrid': True
            }
            
            # Store result
            self.pending_captures[thumb_type] = result
            
            print(f"SUCCESS: {thumb_type} processed - {file_size} bytes, Quality: {quality_score}%")
            
            # Try to send to GoID database
            self._send_to_goid_database(result)
            
        except Exception as e:
            print(f"ERROR: Processing failed: {e}")
    
    def _send_to_goid_database(self, fingerprint_data):
        """Send fingerprint data to GoID database"""
        try:
            # Try to detect GoID service
            goid_urls = [
                'http://localhost:8000',
                'http://127.0.0.1:8000',
                'http://localhost:3000',
                'http://127.0.0.1:3000'
            ]
            
            for url in goid_urls:
                try:
                    response = requests.get(f"{url}/api/health", timeout=2)
                    if response.status_code == 200:
                        print(f"Found GoID service at: {url}")
                        
                        # Send fingerprint data
                        capture_response = requests.post(
                            f"{url}/api/biometric/capture",
                            json=fingerprint_data,
                            timeout=10
                        )
                        
                        if capture_response.status_code == 200:
                            print("SUCCESS: Sent to GoID database")
                            return True
                        else:
                            print(f"GoID API error: {capture_response.status_code}")
                            
                except requests.RequestException:
                    continue
            
            print("WARNING: Could not connect to GoID service")
            
        except Exception as e:
            print(f"ERROR: Database send failed: {e}")
        
        return False
    
    def start_official_capture(self, thumb_type):
        """Start official software for capture"""
        if not self.official_exe:
            return {
                'success': False,
                'error': 'Official executable not found'
            }
        
        try:
            print(f"Starting official capture for {thumb_type}...")
            
            # Clear existing result
            if thumb_type in self.pending_captures:
                del self.pending_captures[thumb_type]
            
            # Create suggested filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            suggested_filename = f"{thumb_type}_thumb_{timestamp}.bmp"
            output_path = os.path.join(self.watch_directory, suggested_filename)
            
            # Start official software
            subprocess.Popen([self.official_exe], cwd=os.getcwd())
            
            return {
                'success': True,
                'message': 'Official software started',
                'instructions': {
                    'step1': 'Official Futronic software has opened',
                    'step2': f'Capture the {thumb_type} thumb fingerprint',
                    'step3': f'Save as: {suggested_filename}',
                    'step4': f'Save to folder: {self.watch_directory}',
                    'step5': 'System will auto-detect and process the file'
                },
                'suggested_filename': suggested_filename,
                'watch_directory': self.watch_directory,
                'simple_hybrid': True
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Failed to start: {e}'
            }
    
    def get_capture_result(self, thumb_type):
        """Get capture result"""
        if thumb_type in self.pending_captures:
            return self.pending_captures[thumb_type]
        else:
            return {
                'success': False,
                'error': f'No {thumb_type} thumb found',
                'pending': list(self.pending_captures.keys())
            }
    
    def list_captured_files(self):
        """List captured files"""
        try:
            files = []
            if os.path.exists(self.watch_directory):
                for filename in os.listdir(self.watch_directory):
                    if filename.lower().endswith(('.bmp', '.jpg', '.jpeg', '.png', '.tif', '.tiff')):
                        file_path = os.path.join(self.watch_directory, filename)
                        file_size = os.path.getsize(file_path)
                        file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                        
                        files.append({
                            'filename': filename,
                            'size': file_size,
                            'modified': file_time.isoformat(),
                            'processed': file_path in self.processed_files
                        })
            
            return sorted(files, key=lambda x: x['modified'], reverse=True)
            
        except Exception as e:
            print(f"ERROR: List files failed: {e}")
            return []

# Global manager
capture_manager = SimpleHybridManager()

class SimpleHybridHandler(BaseHTTPRequestHandler):
    """Simple hybrid HTTP handler"""
    
    def do_GET(self):
        if self.path == '/' or self.path == '/check_results':
            if self.path == '/check_results':
                # Handle check results
                try:
                    results = {
                        'left': capture_manager.get_capture_result('left'),
                        'right': capture_manager.get_capture_result('right'),
                        'pending': list(capture_manager.pending_captures.keys()),
                        'files': capture_manager.list_captured_files()
                    }
                    
                    self.send_response(200)
                    self.send_header('Content-type', 'application/json')
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.end_headers()
                    
                    self.wfile.write(json.dumps(results).encode())
                    return
                    
                except Exception as e:
                    self.send_response(500)
                    self.send_header('Content-type', 'application/json')
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.end_headers()
                    
                    error_response = {'success': False, 'error': str(e)}
                    self.wfile.write(json.dumps(error_response).encode())
                    return
            
            # Handle main page
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            
            captured_files = capture_manager.list_captured_files()
            
            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Simple Hybrid Capture</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .success {{ background: #d4edda; padding: 15px; margin: 10px 0; border-radius: 5px; }}
                    .info {{ background: #d1ecf1; padding: 15px; margin: 10px 0; border-radius: 5px; }}
                    .btn {{ padding: 10px 20px; margin: 5px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; }}
                    .btn-info {{ background: #17a2b8; }}
                    .results {{ background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; font-family: monospace; }}
                </style>
            </head>
            <body>
                <h1>Simple Hybrid Capture Service</h1>
                <p>Official Futronic software + File polling</p>
                
                <div class="success">
                    <h3>Status</h3>
                    <p><strong>Official Software:</strong> {'Found' if capture_manager.official_exe else 'Not Found'}</p>
                    <p><strong>Polling:</strong> Active</p>
                    <p><strong>Directory:</strong> {capture_manager.watch_directory}</p>
                </div>
                
                <div class="info">
                    <h3>Instructions</h3>
                    <p>1. Click "Start Capture" → Official software opens</p>
                    <p>2. Capture fingerprint in official software</p>
                    <p>3. Save to suggested filename in watch directory</p>
                    <p>4. System polls and processes automatically</p>
                </div>
                
                <h3>Capture Controls</h3>
                <button class="btn" onclick="startCapture('left')">Start Left Thumb</button>
                <button class="btn" onclick="startCapture('right')">Start Right Thumb</button>
                <button class="btn btn-info" onclick="checkResults()">Check Results</button>
                <button class="btn btn-info" onclick="location.reload()">Refresh</button>
                
                <div id="instructions" class="info" style="display: none;">
                    <h4>Capture Instructions:</h4>
                    <div id="instructionsContent"></div>
                </div>
                
                <div id="results" class="results" style="display: none;">
                    <h4>Results:</h4>
                    <pre id="resultsContent"></pre>
                </div>
                
                <div class="info">
                    <h4>Captured Files ({len(captured_files)}):</h4>
                    {'<p>No files yet</p>' if not captured_files else ''.join([f'<p><strong>{f["filename"]}</strong> - {f["size"]} bytes - {"Processed" if f["processed"] else "Pending"}</p>' for f in captured_files[:5]])}
                </div>
                
                <script>
                async function startCapture(thumbType) {{
                    const button = event.target;
                    button.disabled = true;
                    button.textContent = 'Starting...';
                    
                    try {{
                        const response = await fetch('/start_capture', {{
                            method: 'POST',
                            headers: {{ 'Content-Type': 'application/json' }},
                            body: JSON.stringify({{ thumb_type: thumbType }})
                        }});
                        
                        const result = await response.json();
                        
                        if (result.success) {{
                            document.getElementById('instructions').style.display = 'block';
                            document.getElementById('instructionsContent').innerHTML = 
                                '<p><strong>Step 1:</strong> ' + result.instructions.step1 + '</p>' +
                                '<p><strong>Step 2:</strong> ' + result.instructions.step2 + '</p>' +
                                '<p><strong>Step 3:</strong> ' + result.instructions.step3 + '</p>' +
                                '<p><strong>Step 4:</strong> ' + result.instructions.step4 + '</p>' +
                                '<p><strong>Step 5:</strong> ' + result.instructions.step5 + '</p>';
                            
                            alert('Official software started! Follow instructions below.');
                        }} else {{
                            alert('ERROR: ' + result.error);
                        }}
                    }} catch (error) {{
                        alert('ERROR: ' + error.message);
                    }} finally {{
                        button.disabled = false;
                        button.textContent = 'Start ' + thumbType.charAt(0).toUpperCase() + thumbType.slice(1) + ' Thumb';
                    }}
                }}
                
                async function checkResults() {{
                    try {{
                        const response = await fetch('/check_results');
                        const result = await response.json();
                        
                        document.getElementById('results').style.display = 'block';
                        document.getElementById('resultsContent').textContent = JSON.stringify(result, null, 2);
                        
                        let messages = [];
                        if (result.left && result.left.success) {{
                            messages.push('Left thumb: Quality ' + result.left.quality_score + '%');
                        }}
                        if (result.right && result.right.success) {{
                            messages.push('Right thumb: Quality ' + result.right.quality_score + '%');
                        }}
                        
                        if (messages.length > 0) {{
                            alert('SUCCESS:\\n' + messages.join('\\n'));
                        }}
                    }} catch (error) {{
                        alert('ERROR: ' + error.message);
                    }}
                }}
                
                // Auto-refresh every 10 seconds
                setInterval(checkResults, 10000);
                </script>
            </body>
            </html>
            """
            
            self.wfile.write(html.encode())
    
    def do_POST(self):
        if self.path == '/start_capture':
            try:
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length)
                data = json.loads(post_data.decode())
                
                thumb_type = data.get('thumb_type', 'left')
                result = capture_manager.start_official_capture(thumb_type)
                
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                
                self.wfile.write(json.dumps(result).encode())
                
            except Exception as e:
                self.send_response(500)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                
                error_response = {'success': False, 'error': str(e)}
                self.wfile.write(json.dumps(error_response).encode())
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def log_message(self, format, *args):
        pass

if __name__ == '__main__':
    try:
        print("=== SIMPLE HYBRID SERVICE ===")
        print("Official software + File polling (no watchdog)")
        print(f"Official executable: {'Found' if capture_manager.official_exe else 'Not Found'}")
        print("Starting server on http://localhost:8001")
        
        server = HTTPServer(('0.0.0.0', 8001), SimpleHybridHandler)
        server.serve_forever()
        
    except KeyboardInterrupt:
        print("\nSimple hybrid service stopped")
        capture_manager.polling_active = False
    except Exception as e:
        print(f"Service error: {e}")
        capture_manager.polling_active = False
