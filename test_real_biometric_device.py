#!/usr/bin/env python3
"""
Test script for real Futronic biometric device functionality
"""

import sys
import os
import json
from datetime import datetime

def test_real_device_backend():
    """Test the backend real device implementation"""
    
    print("🔍 Testing Real Futronic Device Backend Integration")
    print("=" * 60)
    
    try:
        # Add backend to path
        backend_path = os.path.join(os.path.dirname(__file__), 'backend')
        sys.path.append(backend_path)
        
        print("📁 SDK Files Check:")
        sdk_path = os.path.join(os.path.dirname(__file__), 'local-biometric-service', 'fingerPrint')
        print(f"   SDK Path: {sdk_path}")
        
        if os.path.exists(sdk_path):
            print("   ✅ fingerPrint folder found")
            dll_file = os.path.join(sdk_path, 'ftrScanAPI.dll')
            if os.path.exists(dll_file):
                print(f"   ✅ ftrScanAPI.dll found: {dll_file}")
            else:
                print(f"   ❌ ftrScanAPI.dll NOT found at: {dll_file}")
                return False
        else:
            print(f"   ❌ fingerPrint folder NOT found: {sdk_path}")
            return False
        
        print("\n🔧 Importing Real Device Module...")
        from biometrics.futronic_real_device import RealFutronicDevice
        
        print("✅ Real device module imported successfully")
        
        print("\n📱 Creating Device Instance...")
        device = RealFutronicDevice()
        
        print("🔧 Initializing Real Device...")
        initialized = device.initialize()
        
        if initialized:
            print("✅ Real device initialized successfully!")
            
            print("\n📊 Getting Device Status...")
            status = device.get_device_status()
            print("Device Status:")
            for key, value in status.items():
                print(f"   {key}: {value}")
            
            if status.get('connected', False):
                print("\n🔍 Testing Fingerprint Capture...")
                print("Please place your finger on the scanner when prompted...")
                
                try:
                    template = device.capture_fingerprint("left")
                    if template:
                        print("✅ Fingerprint captured successfully!")
                        print(f"   Quality Score: {template.quality_score}%")
                        print(f"   Minutiae Count: {template.minutiae_count}")
                        print(f"   Thumb Type: {template.thumb_type}")
                        print(f"   Template Size: {len(template.template_data)} characters")
                        print(f"   Capture Time: {template.capture_time}")
                        print(f"   Device Info: {template.device_info}")
                        
                        # Test template decoding
                        print("\n🔍 Testing Template Decoding...")
                        try:
                            import base64
                            decoded_data = json.loads(base64.b64decode(template.template_data).decode())
                            print(f"   Template Version: {decoded_data.get('version', 'Unknown')}")
                            print(f"   Frame Size: {decoded_data.get('frame_size', 0)} bytes")
                            print(f"   Has Fingerprint Image: {'fingerprint_image' in decoded_data}")
                            
                            if 'fingerprint_image' in decoded_data:
                                image_data = base64.b64decode(decoded_data['fingerprint_image'])
                                print(f"   Fingerprint Image Size: {len(image_data)} bytes")
                            
                        except Exception as e:
                            print(f"   ❌ Template decoding failed: {e}")
                        
                    else:
                        print("❌ Fingerprint capture failed")
                        
                except Exception as e:
                    print(f"❌ Fingerprint capture error: {e}")
            else:
                print("⚠️ Device not connected - cannot test capture")
            
            print("\n🔒 Disconnecting Device...")
            device.disconnect()
            print("✅ Device disconnected")
            
        else:
            print("❌ Real device initialization failed")
            print("Please ensure:")
            print("  - Futronic FS88H device is connected via USB")
            print("  - Device drivers are installed")
            print("  - No other software is using the device")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please ensure you're running from the GoID root directory")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def test_device_integration():
    """Test the device integration layer"""
    
    print("\n🔗 Testing Device Integration Layer")
    print("=" * 60)
    
    try:
        backend_path = os.path.join(os.path.dirname(__file__), 'backend')
        sys.path.append(backend_path)
        
        from biometrics.device_integration import get_device
        
        print("📱 Getting Device Instance...")
        device = get_device()
        
        print("✅ Device instance obtained")
        print(f"   Device Type: {type(device).__name__}")
        
        print("\n📊 Getting Device Status...")
        status = device.get_device_status()
        print("Device Status:")
        for key, value in status.items():
            print(f"   {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Device integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    
    print("🚀 Real Futronic Biometric Device Test Suite")
    print(f"⏰ Started at: {datetime.now()}")
    print("=" * 70)
    
    results = []
    
    # Test 1: Real device backend
    print("TEST 1: Real Device Backend")
    result1 = test_real_device_backend()
    results.append(("Real Device Backend", result1))
    
    # Test 2: Device integration
    print("\nTEST 2: Device Integration Layer")
    result2 = test_device_integration()
    results.append(("Device Integration", result2))
    
    # Summary
    print(f"\n📊 Test Results Summary")
    print("=" * 70)
    for test_name, passed in results:
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    total_passed = sum(results)
    total_tests = len(results)
    print(f"\nTotal: {total_passed}/{total_tests} tests passed")
    
    if total_passed == total_tests:
        print("🎉 All tests passed! Real device functionality is working.")
    else:
        print("⚠️ Some tests failed. Check the error messages above.")
    
    print(f"\n✅ Test suite completed at: {datetime.now()}")

if __name__ == "__main__":
    main()
