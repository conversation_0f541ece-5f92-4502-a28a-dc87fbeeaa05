#!/usr/bin/env python3
"""
Check DLL architecture and Python compatibility
"""

import os
import platform
import struct

def check_python_architecture():
    """Check Python architecture"""
    print("🐍 Python Information:")
    print(f"  Architecture: {platform.architecture()[0]}")
    print(f"  Machine: {platform.machine()}")
    print(f"  Platform: {platform.platform()}")
    print(f"  Processor: {platform.processor()}")
    
    # Check if Python is 32-bit or 64-bit
    is_64bit = struct.calcsize("P") * 8 == 64
    print(f"  Python is: {'64-bit' if is_64bit else '32-bit'}")
    return is_64bit

def check_dll_architecture(dll_path):
    """Check DLL architecture by reading PE header"""
    try:
        with open(dll_path, 'rb') as f:
            # Read DOS header
            dos_header = f.read(64)
            if dos_header[:2] != b'MZ':
                return "Not a valid PE file"
            
            # Get PE header offset
            pe_offset = struct.unpack('<I', dos_header[60:64])[0]
            
            # Read PE signature
            f.seek(pe_offset)
            pe_signature = f.read(4)
            if pe_signature != b'PE\x00\x00':
                return "Not a valid PE file"
            
            # Read machine type from COFF header
            machine_type = struct.unpack('<H', f.read(2))[0]
            
            # Machine types
            machine_types = {
                0x014c: "32-bit (i386)",
                0x8664: "64-bit (x86_64)",
                0x0200: "64-bit (Itanium)",
                0x01c0: "ARM",
                0xaa64: "ARM64"
            }
            
            return machine_types.get(machine_type, f"Unknown ({hex(machine_type)})")
            
    except Exception as e:
        return f"Error reading file: {e}"

def main():
    print("🔍 Architecture Compatibility Check")
    print("=" * 40)
    
    # Check Python
    python_is_64bit = check_python_architecture()
    
    print("\n📋 DLL Architecture Check:")
    
    dlls_to_check = [
        'ftrScanAPI.dll',
        'ftrMathAPI.dll',
        'ftrWSQ.dll',
        'LiveFinger2.dll'
    ]
    
    dll_architectures = {}
    
    for dll in dlls_to_check:
        if os.path.exists(dll):
            arch = check_dll_architecture(dll)
            dll_architectures[dll] = arch
            print(f"  {dll}: {arch}")
        else:
            print(f"  {dll}: Not found")
    
    print("\n📊 Compatibility Analysis:")
    print("=" * 30)
    
    # Check compatibility
    compatible = True
    for dll, arch in dll_architectures.items():
        if "32-bit" in arch and python_is_64bit:
            print(f"❌ {dll} is 32-bit but Python is 64-bit")
            compatible = False
        elif "64-bit" in arch and not python_is_64bit:
            print(f"❌ {dll} is 64-bit but Python is 32-bit")
            compatible = False
        elif "32-bit" in arch or "64-bit" in arch:
            print(f"✅ {dll} is compatible")
    
    if compatible:
        print("\n🎉 All DLLs are compatible with your Python!")
    else:
        print("\n❌ Architecture mismatch detected!")
        print("\n🔧 Solutions:")
        
        # Determine what's needed
        dll_32bit = any("32-bit" in arch for arch in dll_architectures.values())
        dll_64bit = any("64-bit" in arch for arch in dll_architectures.values())
        
        if dll_32bit and python_is_64bit:
            print("1. Install 32-bit Python to match 32-bit DLLs")
            print("2. OR get 64-bit versions of the Futronic SDK DLLs")
        elif dll_64bit and not python_is_64bit:
            print("1. Install 64-bit Python to match 64-bit DLLs")
            print("2. OR get 32-bit versions of the Futronic SDK DLLs")
        
        print("\n📋 Recommended:")
        if python_is_64bit:
            print("- Use 64-bit Futronic SDK DLLs with your 64-bit Python")
        else:
            print("- Use 32-bit Futronic SDK DLLs with your 32-bit Python")
    
    return compatible

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
