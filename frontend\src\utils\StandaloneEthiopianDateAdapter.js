import { enUS } from 'date-fns/locale';
import {
  gregorianToEthiopian,
  ethiopianToGregorian,
  formatEthiopianDate,
  parseEthiopianDate,
  addDaysToEthiopianDate,
  addMonthsToEthiopianDate,
  addYearsToEthiopianDate,
  isValidEthiopianDate,
  getCurrentEthiopianDate,
  ETHIOPIAN_MONTHS,
  ETHIOPIAN_MONTHS_EN,
  getDaysInEthiopianMonth
} from './ethiopianCalendar';

// Create a minimal locale object that satisfies date-fns requirements
const createEthiopianLocale = (language = 'en') => {
  const baseLocale = enUS; // Use English US as base
  
  return {
    ...baseLocale,
    code: language === 'am' ? 'am-ET' : 'en-ET',
    localize: {
      ...baseLocale.localize,
      month: (monthIndex) => {
        const monthNames = language === 'am' ? ETHIOPIAN_MONTHS : ETHIOPIAN_MONTHS_EN;
        return monthNames[monthIndex] || monthNames[0];
      },
      day: baseLocale.localize.day,
      dayPeriod: baseLocale.localize.dayPeriod,
      era: baseLocale.localize.era,
      quarter: baseLocale.localize.quarter,
    },
    formatLong: baseLocale.formatLong,
    formatRelative: baseLocale.formatRelative,
    match: baseLocale.match,
  };
};

/**
 * Standalone Ethiopian Date Adapter for MUI X Date Pickers
 * This adapter implements the required interface without extending AdapterDateFns
 */
export class StandaloneEthiopianDateAdapter {
  constructor({ locale, formats } = {}) {
    this.lib = 'ethiopian-calendar';
    this.locale = locale || 'en';
    this.dateFnsLocale = createEthiopianLocale(this.locale);
    this.formats = formats || {};
    
    this.formatTokenMap = {
      d: 'day', dd: 'day', D: 'day', DD: 'day',
      M: 'month', MM: 'month', MMM: 'month', MMMM: 'month',
      y: 'year', yy: 'year', yyyy: 'year', YYYY: 'year'
    };
  }

  // Core date creation and parsing methods
  date(value) {
    if (value === null) return null;
    if (value === undefined) return new Date();
    if (typeof value === 'string') {
      const ethiopianDate = parseEthiopianDate(value);
      if (ethiopianDate) {
        return ethiopianToGregorian(ethiopianDate.year, ethiopianDate.month, ethiopianDate.day);
      }
      return new Date(value);
    }
    if (value instanceof Date) return value;
    if (value && typeof value === 'object' && value.year && value.month && value.day) {
      return ethiopianToGregorian(value.year, value.month, value.day);
    }
    return new Date(value);
  }

  parse(value, format) {
    if (!value) return null;
    if (typeof value === 'string') {
      const ethiopianDate = parseEthiopianDate(value);
      if (ethiopianDate && isValidEthiopianDate(ethiopianDate)) {
        return ethiopianToGregorian(ethiopianDate.year, ethiopianDate.month, ethiopianDate.day);
      }
    }
    try {
      const parsedDate = new Date(value);
      return isNaN(parsedDate.getTime()) ? null : parsedDate;
    } catch (error) {
      return null;
    }
  }

  // Formatting methods - completely independent
  format(date, formatKey) {
    if (!date) return '';
    if (!(date instanceof Date) || isNaN(date.getTime())) return '';

    try {
      const ethiopianDate = gregorianToEthiopian(date);
      if (!ethiopianDate) return date.toLocaleDateString('en-US');

      const language = this.locale === 'am' ? 'am' : 'en';

      switch (formatKey) {
        case 'fullDate':
        case 'fullDateWithWeekday':
          return formatEthiopianDate(ethiopianDate, 'DD MMM YYYY', language);
        case 'keyboardDate':
        case 'normalDate':
        case 'shortDate':
        case 'inputDate':
          return formatEthiopianDate(ethiopianDate, 'DD/MM/YYYY', language);
        case 'year':
          return ethiopianDate.year.toString();
        case 'month':
          const monthNames = language === 'am' ? ETHIOPIAN_MONTHS : ETHIOPIAN_MONTHS_EN;
          return monthNames[ethiopianDate.month - 1] || '';
        case 'monthShort':
          const monthNamesShort = language === 'am' ? ETHIOPIAN_MONTHS : ETHIOPIAN_MONTHS_EN;
          return (monthNamesShort[ethiopianDate.month - 1] || '').substring(0, 3);
        case 'monthAndYear':
          const monthNamesForYear = language === 'am' ? ETHIOPIAN_MONTHS : ETHIOPIAN_MONTHS_EN;
          return `${monthNamesForYear[ethiopianDate.month - 1]} ${ethiopianDate.year}`;
        case 'dayOfMonth':
          return ethiopianDate.day.toString();
        case 'weekday':
          const weekdays = this.getWeekdays();
          const dayOfWeek = date.getDay();
          return weekdays[dayOfWeek] || '';
        case 'weekdayShort':
          const weekdaysShort = this.getWeekdays();
          const dayOfWeekShort = date.getDay();
          return (weekdaysShort[dayOfWeekShort] || '').substring(0, 3);
        default:
          return formatEthiopianDate(ethiopianDate, 'DD/MM/YYYY', language);
      }
    } catch (error) {
      console.warn('Ethiopian date formatting error:', error);
      return date.toLocaleDateString('en-US');
    }
  }

  formatByString(date, formatString) {
    if (!date) return '';
    if (!(date instanceof Date) || isNaN(date.getTime())) return '';

    try {
      const ethiopianDate = gregorianToEthiopian(date);
      if (!ethiopianDate) return date.toLocaleDateString('en-US');

      const language = this.locale === 'am' ? 'am' : 'en';
      
      if (formatString) {
        return formatEthiopianDate(ethiopianDate, formatString, language);
      } else {
        return formatEthiopianDate(ethiopianDate, 'DD/MM/YYYY', language);
      }
    } catch (error) {
      console.warn('Ethiopian date formatByString error:', error);
      return date.toLocaleDateString('en-US');
    }
  }

  // Date manipulation methods
  addDays(date, amount) {
    if (!date) return null;
    if (!(date instanceof Date) || isNaN(date.getTime())) return null;

    try {
      const ethiopianDate = gregorianToEthiopian(date);
      if (!ethiopianDate) {
        const newDate = new Date(date);
        newDate.setDate(newDate.getDate() + amount);
        return newDate;
      }

      const newEthiopianDate = addDaysToEthiopianDate(ethiopianDate, amount);
      return ethiopianToGregorian(newEthiopianDate.year, newEthiopianDate.month, newEthiopianDate.day);
    } catch (error) {
      const newDate = new Date(date);
      newDate.setDate(newDate.getDate() + amount);
      return newDate;
    }
  }

  addWeeks(date, amount) {
    return this.addDays(date, amount * 7);
  }

  addMonths(date, amount) {
    if (!date) return null;
    if (!(date instanceof Date) || isNaN(date.getTime())) return null;

    try {
      const ethiopianDate = gregorianToEthiopian(date);
      if (!ethiopianDate) {
        const newDate = new Date(date);
        newDate.setMonth(newDate.getMonth() + amount);
        return newDate;
      }

      const newEthiopianDate = addMonthsToEthiopianDate(ethiopianDate, amount);
      return ethiopianToGregorian(newEthiopianDate.year, newEthiopianDate.month, newEthiopianDate.day);
    } catch (error) {
      const newDate = new Date(date);
      newDate.setMonth(newDate.getMonth() + amount);
      return newDate;
    }
  }

  addYears(date, amount) {
    if (!date) return null;
    if (!(date instanceof Date) || isNaN(date.getTime())) return null;

    try {
      const ethiopianDate = gregorianToEthiopian(date);
      if (!ethiopianDate) {
        const newDate = new Date(date);
        newDate.setFullYear(newDate.getFullYear() + amount);
        return newDate;
      }

      const newEthiopianDate = addYearsToEthiopianDate(ethiopianDate, amount);
      return ethiopianToGregorian(newEthiopianDate.year, newEthiopianDate.month, newEthiopianDate.day);
    } catch (error) {
      const newDate = new Date(date);
      newDate.setFullYear(newDate.getFullYear() + amount);
      return newDate;
    }
  }

  // Date comparison methods
  isEqual(date1, date2) {
    if (!date1 || !date2) return false;
    const eth1 = gregorianToEthiopian(date1);
    const eth2 = gregorianToEthiopian(date2);
    if (!eth1 || !eth2) return false;
    return eth1.year === eth2.year && eth1.month === eth2.month && eth1.day === eth2.day;
  }

  isSameDay(date1, date2) {
    return this.isEqual(date1, date2);
  }

  isSameMonth(date1, date2) {
    if (!date1 || !date2) return false;
    const eth1 = gregorianToEthiopian(date1);
    const eth2 = gregorianToEthiopian(date2);
    if (!eth1 || !eth2) return false;
    return eth1.year === eth2.year && eth1.month === eth2.month;
  }

  isSameYear(date1, date2) {
    if (!date1 || !date2) return false;
    const eth1 = gregorianToEthiopian(date1);
    const eth2 = gregorianToEthiopian(date2);
    if (!eth1 || !eth2) return false;
    return eth1.year === eth2.year;
  }

  isAfter(date1, date2) {
    if (!date1 || !date2) return false;
    return date1.getTime() > date2.getTime();
  }

  isBefore(date1, date2) {
    if (!date1 || !date2) return false;
    return date1.getTime() < date2.getTime();
  }

  // Date extraction methods
  getYear(date) {
    if (!date) return 0;
    const ethiopianDate = gregorianToEthiopian(date);
    return ethiopianDate ? ethiopianDate.year : 0;
  }

  getMonth(date) {
    if (!date) return 0;
    const ethiopianDate = gregorianToEthiopian(date);
    return ethiopianDate ? ethiopianDate.month - 1 : 0; // MUI expects 0-based months
  }

  getDate(date) {
    if (!date) return 0;
    const ethiopianDate = gregorianToEthiopian(date);
    return ethiopianDate ? ethiopianDate.day : 0;
  }

  getDaysInMonth(date) {
    if (!date) return 30;
    const ethiopianDate = gregorianToEthiopian(date);
    if (!ethiopianDate) return 30;
    return getDaysInEthiopianMonth(ethiopianDate.month, ethiopianDate.year);
  }

  // Validation methods
  isValid(date) {
    if (!date || !(date instanceof Date)) return false;
    if (isNaN(date.getTime())) return false;
    const ethiopianDate = gregorianToEthiopian(date);
    return ethiopianDate ? isValidEthiopianDate(ethiopianDate) : false;
  }

  isNull(date) {
    return date === null;
  }

  // Calendar navigation methods
  getWeekdays() {
    const weekdays = this.locale === 'am'
      ? ['እሑድ', 'ሰኞ', 'ማክሰኞ', 'ረቡዕ', 'ሐሙስ', 'ዓርብ', 'ቅዳሜ']
      : ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    return weekdays;
  }

  getWeekArray(date) {
    if (!date) return [];
    try {
      const startOfWeek = this.startOfDay(date);
      const week = [];
      for (let i = 0; i < 7; i++) {
        week.push(this.addDays(startOfWeek, i));
      }
      return [week];
    } catch (error) {
      return [];
    }
  }

  // Format helper methods
  expandFormat(format) {
    return [
      { type: 'day', value: 'DD' },
      { type: 'literal', value: '/' },
      { type: 'month', value: 'MM' },
      { type: 'literal', value: '/' },
      { type: 'year', value: 'YYYY' }
    ];
  }

  getFormatHelperText(format) {
    return 'DD/MM/YYYY (Ethiopian Calendar)';
  }

  getDateSeparator() {
    return '/';
  }

  // Utility methods
  startOfDay(date) {
    if (!date) return null;
    const newDate = new Date(date);
    newDate.setHours(0, 0, 0, 0);
    return newDate;
  }

  endOfDay(date) {
    if (!date) return null;
    const newDate = new Date(date);
    newDate.setHours(23, 59, 59, 999);
    return newDate;
  }

  startOfMonth(date) {
    if (!date) return null;
    const ethiopianDate = gregorianToEthiopian(date);
    if (!ethiopianDate) return null;
    return ethiopianToGregorian(ethiopianDate.year, ethiopianDate.month, 1);
  }

  endOfMonth(date) {
    if (!date) return null;
    const ethiopianDate = gregorianToEthiopian(date);
    if (!ethiopianDate) return null;
    const daysInMonth = getDaysInEthiopianMonth(ethiopianDate.month, ethiopianDate.year);
    return ethiopianToGregorian(ethiopianDate.year, ethiopianDate.month, daysInMonth);
  }

  startOfYear(date) {
    if (!date) return null;
    const ethiopianDate = gregorianToEthiopian(date);
    if (!ethiopianDate) return null;
    return ethiopianToGregorian(ethiopianDate.year, 1, 1);
  }

  endOfYear(date) {
    if (!date) return null;
    const ethiopianDate = gregorianToEthiopian(date);
    if (!ethiopianDate) return null;
    const daysInPagume = getDaysInEthiopianMonth(13, ethiopianDate.year);
    return ethiopianToGregorian(ethiopianDate.year, 13, daysInPagume);
  }
}

export default StandaloneEthiopianDateAdapter;
