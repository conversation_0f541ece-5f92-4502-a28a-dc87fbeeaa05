"""
Tests for biometrics app functionality.
"""

import json
import base64
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth import get_user_model
from django_tenants.test.cases import TenantTestCase
from django_tenants.utils import schema_context

from tenants.models import Tenant, Domain
from tenants.models.citizen import Citizen, Biometric
from .device_integration import FutronicFS88H, get_device
from .fingerprint_processing import FingerprintProcessor

User = get_user_model()


class FutronicFS88HTestCase(TestCase):
    """Test cases for Futronic FS88H device integration."""
    
    def setUp(self):
        self.device = FutronicFS88H()
    
    def test_device_initialization(self):
        """Test device initialization."""
        result = self.device.initialize()
        self.assertTrue(result)
        self.assertTrue(self.device.is_initialized)
        self.assertTrue(self.device.device_info.is_connected)
    
    def test_device_status(self):
        """Test getting device status."""
        self.device.initialize()
        status = self.device.get_device_status()
        
        self.assertIn('connected', status)
        self.assertIn('initialized', status)
        self.assertIn('model', status)
        self.assertEqual(status['model'], 'Futronic FS88H')
    
    def test_fingerprint_capture(self):
        """Test fingerprint capture functionality."""
        self.device.initialize()
        
        # Test left thumb capture
        template = self.device.capture_fingerprint('left')
        self.assertIsNotNone(template)
        self.assertEqual(template.thumb_type, 'left')
        self.assertGreater(template.quality_score, 0)
        self.assertGreater(template.minutiae_count, 0)
        
        # Test right thumb capture
        template = self.device.capture_fingerprint('right')
        self.assertIsNotNone(template)
        self.assertEqual(template.thumb_type, 'right')
    
    def test_invalid_thumb_type(self):
        """Test capture with invalid thumb type."""
        self.device.initialize()
        
        with self.assertRaises(ValueError):
            self.device.capture_fingerprint('invalid')
    
    def test_capture_without_initialization(self):
        """Test capture without device initialization."""
        with self.assertRaises(Exception):
            self.device.capture_fingerprint('left')
    
    def test_template_validation(self):
        """Test fingerprint template quality validation."""
        self.device.initialize()
        template = self.device.capture_fingerprint('left')
        
        is_valid, message = self.device.validate_quality(template)
        self.assertIsInstance(is_valid, bool)
        self.assertIsInstance(message, str)
    
    def test_template_matching(self):
        """Test fingerprint template matching."""
        self.device.initialize()
        
        template1 = self.device.capture_fingerprint('left')
        template2 = self.device.capture_fingerprint('left')
        
        # Test matching same template
        is_match, confidence = self.device.match_templates(
            template1.template_data, 
            template1.template_data
        )
        self.assertTrue(is_match)
        self.assertEqual(confidence, 1.0)
        
        # Test matching different templates
        is_match, confidence = self.device.match_templates(
            template1.template_data, 
            template2.template_data
        )
        self.assertIsInstance(is_match, bool)
        self.assertIsInstance(confidence, float)


class FingerprintProcessorTestCase(TestCase):
    """Test cases for fingerprint processing functionality."""
    
    def setUp(self):
        self.processor = FingerprintProcessor()
        self.device = FutronicFS88H()
        self.device.initialize()
    
    def test_template_quality_validation(self):
        """Test fingerprint template quality validation."""
        template = self.device.capture_fingerprint('left')
        result = self.processor.validate_template_quality(template.template_data)
        
        self.assertIn('is_valid', result)
        self.assertIn('quality_score', result)
        self.assertIn('minutiae_count', result)
        self.assertIn('validation_time', result)
    
    def test_invalid_template_validation(self):
        """Test validation with invalid template data."""
        result = self.processor.validate_template_quality('invalid_data')
        
        self.assertFalse(result['is_valid'])
        self.assertIn('issues', result)
        self.assertGreater(len(result['issues']), 0)
    
    def test_biometric_statistics(self):
        """Test getting biometric statistics."""
        stats = self.processor.get_biometric_statistics()
        
        self.assertIn('total_citizens', stats)
        self.assertIn('total_biometric_records', stats)
        self.assertIn('fingerprint_stats', stats)
        self.assertIn('generated_at', stats)


class BiometricsAPITestCase(TenantTestCase):
    """Test cases for biometrics API endpoints."""
    
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        
        # Create tenant
        cls.tenant = Tenant.objects.create(
            name="Test Kebele",
            type="kebele",
            schema_name="test_kebele"
        )
        
        # Create domain
        cls.domain = Domain.objects.create(
            domain="test-kebele.localhost",
            tenant=cls.tenant,
            is_primary=True
        )
    
    def setUp(self):
        super().setUp()
        
        # Create test user
        with schema_context(self.tenant.schema_name):
            self.user = User.objects.create_user(
                username='testuser',
                email='<EMAIL>',
                password='testpass123'
            )
            
            # Create test citizen
            self.citizen = Citizen.objects.create(
                first_name='John',
                last_name='Doe',
                date_of_birth='1990-01-01',
                gender='male',
                phone='+251911123456',
                email='<EMAIL>'
            )
    
    def test_device_status_endpoint(self):
        """Test device status API endpoint."""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('device_status')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('success', response.data)
        self.assertIn('data', response.data)
    
    def test_initialize_device_endpoint(self):
        """Test device initialization API endpoint."""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('initialize_device')
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
    
    def test_capture_fingerprint_endpoint(self):
        """Test fingerprint capture API endpoint."""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('capture_fingerprint')
        data = {'thumb_type': 'left'}
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['thumb_type'], 'left')
    
    def test_capture_invalid_thumb_type(self):
        """Test capture with invalid thumb type."""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('capture_fingerprint')
        data = {'thumb_type': 'invalid'}
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
    
    def test_validate_quality_endpoint(self):
        """Test fingerprint quality validation endpoint."""
        self.client.force_authenticate(user=self.user)
        
        # First capture a fingerprint
        device = get_device()
        device.initialize()
        template = device.capture_fingerprint('left')
        
        url = reverse('validate_quality')
        data = {'fingerprint_data': template.template_data}
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
    
    def test_biometric_stats_endpoint(self):
        """Test biometric statistics endpoint."""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('biometric_stats')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('data', response.data)


class BiometricIntegrationTestCase(TenantTestCase):
    """Integration tests for biometric functionality with citizen registration."""
    
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        
        # Create tenant
        cls.tenant = Tenant.objects.create(
            name="Test Kebele",
            type="kebele", 
            schema_name="test_kebele_integration"
        )
        
        # Create domain
        cls.domain = Domain.objects.create(
            domain="test-kebele-integration.localhost",
            tenant=cls.tenant,
            is_primary=True
        )
    
    def setUp(self):
        super().setUp()
        
        with schema_context(self.tenant.schema_name):
            self.user = User.objects.create_user(
                username='testuser',
                email='<EMAIL>', 
                password='testpass123'
            )
    
    def test_citizen_creation_with_biometrics(self):
        """Test creating a citizen with biometric data."""
        with schema_context(self.tenant.schema_name):
            # Create citizen with biometric data
            citizen = Citizen.objects.create(
                first_name='Jane',
                last_name='Smith',
                date_of_birth='1985-05-15',
                gender='female',
                phone='+251922654321',
                email='<EMAIL>'
            )
            
            # Capture fingerprints
            device = get_device()
            device.initialize()
            
            left_template = device.capture_fingerprint('left')
            right_template = device.capture_fingerprint('right')
            
            # Create biometric record
            biometric = Biometric.objects.create(
                citizen=citizen,
                left_thumb_fingerprint=left_template.template_data,
                right_thumb_fingerprint=right_template.template_data
            )
            
            # Verify biometric record
            self.assertEqual(biometric.citizen, citizen)
            self.assertIsNotNone(biometric.left_thumb_fingerprint)
            self.assertIsNotNone(biometric.right_thumb_fingerprint)
            
            # Verify citizen has biometric record
            self.assertTrue(hasattr(citizen, 'biometric_record'))
            self.assertEqual(citizen.biometric_record, biometric)
