import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { enUS } from 'date-fns/locale';
import {
  gregorianToEthiopian,
  ethiopianToGregorian,
  formatEthiopianDate,
  parseEthiopianDate,
  addDaysToEthiopianDate,
  addMonthsToEthiopianDate,
  addYearsToEthiopianDate,
  isValidEthiopianDate,
  getCurrentEthiopianDate,
  ETHIOPIAN_MONTHS,
  ETHIOPIAN_MONTHS_EN,
  getDaysInEthiopianMonth
} from './ethiopianCalendar';

/**
 * Fixed Ethiopian Date Adapter for MUI X Date Pickers
 * This adapter properly extends AdapterDateFns with complete method overrides
 *
 * User Flow:
 * 1. User sees Ethiopian calendar dates in the UI
 * 2. User selects Ethiopian dates through the date picker
 * 3. System stores both Ethiopian date and converted Gregorian date
 * 4. All internal operations work with both calendars
 */
export class FixedEthiopianDateAdapter extends AdapterDateFns {
  constructor({ locale, formats } = {}) {
    // Always use a valid date-fns locale to prevent errors
    super({ locale: enUS, formats });

    // Store our Ethiopian locale preference
    this.ethiopianLocale = locale || 'en';
    this.lib = 'ethiopian-calendar';

    // Store conversion metadata for debugging
    this._conversions = new Map();
  }

  // Helper method to log conversions for debugging
  _logConversion(gregorianDate, ethiopianDate, operation) {
    if (process.env.NODE_ENV === 'development') {
      console.log(`Ethiopian Calendar ${operation}:`, {
        gregorian: gregorianDate?.toISOString?.() || gregorianDate,
        ethiopian: ethiopianDate,
        formatted: ethiopianDate ? formatEthiopianDate(ethiopianDate, 'DD/MM/YYYY', this.ethiopianLocale) : null
      });
    }
  }

  // Override ALL formatting methods to show Ethiopian dates to user
  format(date, formatKey) {
    if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
      return '';
    }

    try {
      // Convert Gregorian date to Ethiopian for display
      const ethiopianDate = gregorianToEthiopian(date);
      if (!ethiopianDate) {
        return date.toLocaleDateString('en-US');
      }

      this._logConversion(date, ethiopianDate, 'Format Display');
      const language = this.ethiopianLocale === 'am' ? 'am' : 'en';

      switch (formatKey) {
        case 'fullDate':
        case 'fullDateWithWeekday':
          return formatEthiopianDate(ethiopianDate, 'DD MMM YYYY', language);
        case 'keyboardDate':
        case 'normalDate':
        case 'shortDate':
        case 'inputDate':
          return formatEthiopianDate(ethiopianDate, 'DD/MM/YYYY', language);
        case 'year':
          return ethiopianDate.year.toString();
        case 'month':
          const monthNames = language === 'am' ? ETHIOPIAN_MONTHS : ETHIOPIAN_MONTHS_EN;
          return monthNames[ethiopianDate.month - 1] || '';
        case 'monthShort':
          const monthNamesShort = language === 'am' ? ETHIOPIAN_MONTHS : ETHIOPIAN_MONTHS_EN;
          return (monthNamesShort[ethiopianDate.month - 1] || '').substring(0, 3);
        case 'monthAndYear':
          const monthNamesForYear = language === 'am' ? ETHIOPIAN_MONTHS : ETHIOPIAN_MONTHS_EN;
          return `${monthNamesForYear[ethiopianDate.month - 1]} ${ethiopianDate.year}`;
        case 'dayOfMonth':
          return ethiopianDate.day.toString();
        case 'weekday':
          const weekdays = this.getWeekdays();
          const dayOfWeek = date.getDay();
          return weekdays[dayOfWeek] || '';
        case 'weekdayShort':
          const weekdaysShort = this.getWeekdays();
          const dayOfWeekShort = date.getDay();
          return (weekdaysShort[dayOfWeekShort] || '').substring(0, 3);
        default:
          return formatEthiopianDate(ethiopianDate, 'DD/MM/YYYY', language);
      }
    } catch (error) {
      console.warn('Ethiopian date formatting error:', error);
      return date.toLocaleDateString('en-US');
    }
  }

  formatByString(date, formatString) {
    if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
      return '';
    }

    try {
      // Convert Gregorian date to Ethiopian for user display
      const ethiopianDate = gregorianToEthiopian(date);
      if (!ethiopianDate) {
        return date.toLocaleDateString('en-US');
      }

      this._logConversion(date, ethiopianDate, 'Format By String');
      const language = this.ethiopianLocale === 'am' ? 'am' : 'en';

      if (formatString) {
        return formatEthiopianDate(ethiopianDate, formatString, language);
      } else {
        return formatEthiopianDate(ethiopianDate, 'DD/MM/YYYY', language);
      }
    } catch (error) {
      console.warn('Ethiopian date formatByString error:', error);
      return date.toLocaleDateString('en-US');
    }
  }

  // Core date creation method - handles Ethiopian date input
  date(value) {
    if (value === null) return null;
    if (value === undefined) return new Date();

    // Handle Ethiopian date string input (DD/MM/YYYY format)
    if (typeof value === 'string') {
      const ethiopianDate = parseEthiopianDate(value);
      if (ethiopianDate && isValidEthiopianDate(ethiopianDate)) {
        const gregorianDate = ethiopianToGregorian(ethiopianDate.year, ethiopianDate.month, ethiopianDate.day);
        this._logConversion(gregorianDate, ethiopianDate, 'Parse Ethiopian String');
        return gregorianDate;
      }

      // Fallback to regular date parsing
      try {
        const parsedDate = new Date(value);
        return isNaN(parsedDate.getTime()) ? null : parsedDate;
      } catch (error) {
        return null;
      }
    }

    // Handle Date object
    if (value instanceof Date) {
      return value;
    }

    // Handle Ethiopian date object {year, month, day}
    if (value && typeof value === 'object' && value.year && value.month && value.day) {
      const gregorianDate = ethiopianToGregorian(value.year, value.month, value.day);
      this._logConversion(gregorianDate, value, 'Parse Ethiopian Object');
      return gregorianDate;
    }

    // Fallback
    try {
      return new Date(value);
    } catch (error) {
      return null;
    }
  }

  // Override date parsing for form inputs
  parse(value, format) {
    if (!value) return null;

    // Prioritize Ethiopian date parsing
    if (typeof value === 'string') {
      const ethiopianDate = parseEthiopianDate(value);
      if (ethiopianDate && isValidEthiopianDate(ethiopianDate)) {
        const gregorianDate = ethiopianToGregorian(ethiopianDate.year, ethiopianDate.month, ethiopianDate.day);
        this._logConversion(gregorianDate, ethiopianDate, 'Parse Input');
        return gregorianDate;
      }
    }

    // Use the date method for consistent handling
    return this.date(value);
  }

  // Override date manipulation methods
  addDays(date, amount) {
    if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
      return null;
    }

    try {
      const ethiopianDate = gregorianToEthiopian(date);
      if (!ethiopianDate) {
        const newDate = new Date(date);
        newDate.setDate(newDate.getDate() + amount);
        return newDate;
      }

      const newEthiopianDate = addDaysToEthiopianDate(ethiopianDate, amount);
      return ethiopianToGregorian(newEthiopianDate.year, newEthiopianDate.month, newEthiopianDate.day);
    } catch (error) {
      const newDate = new Date(date);
      newDate.setDate(newDate.getDate() + amount);
      return newDate;
    }
  }

  addMonths(date, amount) {
    if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
      return null;
    }

    try {
      const ethiopianDate = gregorianToEthiopian(date);
      if (!ethiopianDate) {
        const newDate = new Date(date);
        newDate.setMonth(newDate.getMonth() + amount);
        return newDate;
      }

      const newEthiopianDate = addMonthsToEthiopianDate(ethiopianDate, amount);
      return ethiopianToGregorian(newEthiopianDate.year, newEthiopianDate.month, newEthiopianDate.day);
    } catch (error) {
      const newDate = new Date(date);
      newDate.setMonth(newDate.getMonth() + amount);
      return newDate;
    }
  }

  addYears(date, amount) {
    if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
      return null;
    }

    try {
      const ethiopianDate = gregorianToEthiopian(date);
      if (!ethiopianDate) {
        const newDate = new Date(date);
        newDate.setFullYear(newDate.getFullYear() + amount);
        return newDate;
      }

      const newEthiopianDate = addYearsToEthiopianDate(ethiopianDate, amount);
      return ethiopianToGregorian(newEthiopianDate.year, newEthiopianDate.month, newEthiopianDate.day);
    } catch (error) {
      const newDate = new Date(date);
      newDate.setFullYear(newDate.getFullYear() + amount);
      return newDate;
    }
  }

  // Override calendar methods
  getWeekdays() {
    const weekdays = this.ethiopianLocale === 'am'
      ? ['እሑድ', 'ሰኞ', 'ማክሰኞ', 'ረቡዕ', 'ሐሙስ', 'ዓርብ', 'ቅዳሜ']
      : ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    return weekdays;
  }

  // Override validation methods
  isValid(date) {
    if (!date || !(date instanceof Date)) return false;
    if (isNaN(date.getTime())) return false;
    const ethiopianDate = gregorianToEthiopian(date);
    return ethiopianDate ? isValidEthiopianDate(ethiopianDate) : false;
  }

  // Override utility methods
  getYear(date) {
    if (!date) return 0;
    const ethiopianDate = gregorianToEthiopian(date);
    return ethiopianDate ? ethiopianDate.year : 0;
  }

  getMonth(date) {
    if (!date) return 0;
    const ethiopianDate = gregorianToEthiopian(date);
    return ethiopianDate ? ethiopianDate.month - 1 : 0; // MUI expects 0-based months
  }

  getDate(date) {
    if (!date) return 0;
    const ethiopianDate = gregorianToEthiopian(date);
    return ethiopianDate ? ethiopianDate.day : 0;
  }

  getDaysInMonth(date) {
    if (!date) return 30;
    const ethiopianDate = gregorianToEthiopian(date);
    if (!ethiopianDate) return 30;
    return getDaysInEthiopianMonth(ethiopianDate.month, ethiopianDate.year);
  }

  // Override format helper methods
  getFormatHelperText(format) {
    return 'DD/MM/YYYY (Ethiopian Calendar)';
  }

  getDateSeparator() {
    return '/';
  }

  // Override any other methods that might cause issues
  expandFormat(format) {
    return [
      { type: 'day', value: 'DD' },
      { type: 'literal', value: '/' },
      { type: 'month', value: 'MM' },
      { type: 'literal', value: '/' },
      { type: 'year', value: 'YYYY' }
    ];
  }

  // Utility methods for dual-calendar support

  /**
   * Get both Ethiopian and Gregorian representations of a date
   * @param {Date} gregorianDate - Gregorian date
   * @returns {Object} - {gregorian: Date, ethiopian: Object, formatted: Object}
   */
  getDualCalendarInfo(gregorianDate) {
    if (!gregorianDate || !(gregorianDate instanceof Date) || isNaN(gregorianDate.getTime())) {
      return null;
    }

    const ethiopianDate = gregorianToEthiopian(gregorianDate);
    if (!ethiopianDate) return null;

    const language = this.ethiopianLocale === 'am' ? 'am' : 'en';

    return {
      gregorian: gregorianDate,
      ethiopian: ethiopianDate,
      formatted: {
        ethiopian: formatEthiopianDate(ethiopianDate, 'DD/MM/YYYY', language),
        ethiopianLong: formatEthiopianDate(ethiopianDate, 'DD MMM YYYY', language),
        gregorian: gregorianDate.toLocaleDateString('en-US'),
        gregorianLong: gregorianDate.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        })
      }
    };
  }

  /**
   * Create a Gregorian date from Ethiopian date components
   * @param {number} year - Ethiopian year
   * @param {number} month - Ethiopian month (1-13)
   * @param {number} day - Ethiopian day
   * @returns {Date} - Gregorian date
   */
  createFromEthiopian(year, month, day) {
    const gregorianDate = ethiopianToGregorian(year, month, day);
    if (gregorianDate) {
      this._logConversion(gregorianDate, { year, month, day }, 'Create From Ethiopian');
    }
    return gregorianDate;
  }

  /**
   * Get Ethiopian date components from Gregorian date
   * @param {Date} gregorianDate - Gregorian date
   * @returns {Object} - {year, month, day}
   */
  getEthiopianComponents(gregorianDate) {
    const ethiopianDate = gregorianToEthiopian(gregorianDate);
    if (ethiopianDate) {
      this._logConversion(gregorianDate, ethiopianDate, 'Get Ethiopian Components');
    }
    return ethiopianDate;
  }
}

export default FixedEthiopianDateAdapter;
