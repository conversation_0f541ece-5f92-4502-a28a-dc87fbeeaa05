from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from users.models import UserRole


class Command(BaseCommand):
    help = 'Create a superadmin user'

    def add_arguments(self, parser):
        parser.add_argument('--email', type=str, help='Email address', default='<EMAIL>')
        parser.add_argument('--username', type=str, help='Username', default='admin')
        parser.add_argument('--password', type=str, help='Password', default='admin123')

    def handle(self, *args, **options):
        User = get_user_model()
        
        email = options['email']
        username = options['username']
        password = options['password']
        
        # Check if user already exists
        if User.objects.filter(email=email).exists():
            self.stdout.write(
                self.style.WARNING(f'User with email {email} already exists')
            )
            return
        
        # Create superadmin user
        user = User.objects.create_user(
            email=email,
            username=username,
            password=password,
            role=UserRole.SUPERADMIN,
            is_superuser=True,
            is_staff=True,
            first_name='Super',
            last_name='Admin'
        )
        
        self.stdout.write(
            self.style.SUCCESS(f'Successfully created superadmin user: {email}')
        )
        self.stdout.write(f'Username: {username}')
        self.stdout.write(f'Password: {password}')
        self.stdout.write(f'Role: {user.role}')
