@echo off
echo ========================================
echo   TRANSPARENT Biometric Service Manager
echo ========================================
echo.
echo PRODUCTION-READY SOLUTION
echo.
echo This service manager provides:
echo - Automatic transparent restarts
echo - No user intervention required
echo - Continuous availability for clerks
echo - Professional service management
echo - Rate limiting to prevent restart loops
echo.
echo FEATURES:
echo - Service runs continuously in background
echo - Automatic restart after crashes
echo - No "Press any key" prompts
echo - Logging to service_manager.log
echo - Graceful shutdown with Ctrl+C
echo - Rate limiting (max 20 restarts/hour)
echo.
echo CLERK EXPERIENCE:
echo - Service always available at localhost:8001
echo - No technical intervention needed
echo - Seamless biometric capture
echo - Professional reliability
echo.

cd local-biometric-service

echo Starting Transparent Service Manager...
echo Service URL: http://localhost:8001
echo Automatic transparent restarts enabled
echo Press Ctrl+C to stop service manager
echo.
echo Service is now running transparently!
echo    Clerks can use biometric capture normally.
echo    Service will auto-restart after each capture.
echo.

python service_manager.py

echo.
echo Service manager stopped.
pause
