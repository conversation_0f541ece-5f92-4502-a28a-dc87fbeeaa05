"""
Test script for GoID API endpoints.
This script simulates API requests and responses without needing to run the server.
"""

import os
import sys
import json
import django
from django.test import Client
from django.contrib.auth import get_user_model
from django.db import connection

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

# Import models
from tenants.models import Tenant, Domain, TenantType
from django_tenants.utils import schema_context

User = get_user_model()

# Create a test client
client = Client()

def print_response(response):
    """Print the response in a readable format."""
    print(f"Status Code: {response.status_code}")
    try:
        data = json.loads(response.content)
        print(json.dumps(data, indent=2))
    except:
        print(response.content)
    print("-" * 80)

def test_authentication():
    """Test authentication endpoints."""
    print("\n=== Testing Authentication ===\n")
    
    # Create a superuser if it doesn't exist
    if not User.objects.filter(username='admin').exists():
        User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            role='superadmin'
        )
        print("Superuser created successfully.")
    else:
        print("Superuser already exists.")
    
    # Test login
    print("\n--- Login ---\n")
    response = client.post('/api/auth/token/', {
        'email': '<EMAIL>',
        'password': 'admin123'
    }, content_type='application/json')
    print_response(response)
    
    # Save the token for later use
    if response.status_code == 200:
        data = json.loads(response.content)
        token = data.get('access')
        client.defaults['HTTP_AUTHORIZATION'] = f'Bearer {token}'
        print("Token saved for future requests.")
    else:
        print("Failed to get token. Subsequent tests may fail.")

def test_tenants():
    """Test tenant endpoints."""
    print("\n=== Testing Tenants ===\n")
    
    # Create public tenant if it doesn't exist
    if not Tenant.objects.filter(schema_name='public').exists():
        tenant = Tenant(
            schema_name='public',
            name='Public',
            type=TenantType.CITY
        )
        tenant.save()
        
        domain = Domain()
        domain.domain = 'localhost'
        domain.tenant = tenant
        domain.is_primary = True
        domain.save()
        
        print("Public tenant created successfully.")
    else:
        print("Public tenant already exists.")
    
    # Test get all tenants
    print("\n--- Get All Tenants ---\n")
    response = client.get('/api/tenants/')
    print_response(response)
    
    # Test create tenant
    print("\n--- Create Tenant ---\n")
    response = client.post('/api/tenants/', {
        'name': 'Test City',
        'type': 'city'
    }, content_type='application/json')
    print_response(response)
    
    # Get the tenant ID for later use
    if response.status_code == 201:
        data = json.loads(response.content)
        tenant_id = data.get('id')
        print(f"Tenant ID: {tenant_id}")
        
        # Test get tenant by ID
        print("\n--- Get Tenant by ID ---\n")
        response = client.get(f'/api/tenants/{tenant_id}/')
        print_response(response)
        
        # Test create subcity
        print("\n--- Create Subcity ---\n")
        response = client.post('/api/tenants/', {
            'name': 'Test Subcity',
            'type': 'subcity',
            'parent': tenant_id
        }, content_type='application/json')
        print_response(response)
        
        # Get the subcity ID for later use
        if response.status_code == 201:
            data = json.loads(response.content)
            subcity_id = data.get('id')
            print(f"Subcity ID: {subcity_id}")
            
            # Test create kebele
            print("\n--- Create Kebele ---\n")
            response = client.post('/api/tenants/', {
                'name': 'Test Kebele',
                'type': 'kebele',
                'parent': subcity_id
            }, content_type='application/json')
            print_response(response)
            
            # Test tenant hierarchy
            print("\n--- Get Tenant Hierarchy ---\n")
            response = client.get('/api/tenants/hierarchy/')
            print_response(response)
            
            # Test switch tenant
            print("\n--- Switch Tenant ---\n")
            response = client.post('/api/tenants/switch/', {
                'tenant_id': tenant_id
            }, content_type='application/json')
            print_response(response)

def test_users():
    """Test user endpoints."""
    print("\n=== Testing Users ===\n")
    
    # Test get all users
    print("\n--- Get All Users ---\n")
    response = client.get('/api/auth/users/')
    print_response(response)
    
    # Test create user
    print("\n--- Create User ---\n")
    response = client.post('/api/auth/users/', {
        'email': '<EMAIL>',
        'username': 'testuser',
        'password': 'password123',
        'password2': 'password123',
        'first_name': 'Test',
        'last_name': 'User',
        'role': 'clerk'
    }, content_type='application/json')
    print_response(response)
    
    # Get the user ID for later use
    if response.status_code == 201:
        data = json.loads(response.content)
        user_id = data.get('id')
        print(f"User ID: {user_id}")
        
        # Test get user by ID
        print("\n--- Get User by ID ---\n")
        response = client.get(f'/api/auth/users/{user_id}/')
        print_response(response)
        
        # Test get current user
        print("\n--- Get Current User ---\n")
        response = client.get('/api/auth/users/me/')
        print_response(response)

def test_citizens():
    """Test citizen endpoints."""
    print("\n=== Testing Citizens ===\n")
    
    # Test get all citizens
    print("\n--- Get All Citizens ---\n")
    response = client.get('/api/citizens/')
    print_response(response)
    
    # Test create citizen
    print("\n--- Create Citizen ---\n")
    response = client.post('/api/citizens/', {
        'first_name': 'John',
        'middle_name': 'Doe',
        'last_name': 'Smith',
        'date_of_birth': '1990-01-01',
        'gender': 'male',
        'phone_number': '+251911234567',
        'email': '<EMAIL>',
        'nationality': 'Ethiopian',
        'marital_status': 'single'
    }, content_type='application/json')
    print_response(response)
    
    # Get the citizen ID for later use
    if response.status_code == 201:
        data = json.loads(response.content)
        citizen_id = data.get('id')
        print(f"Citizen ID: {citizen_id}")
        
        # Test get citizen by ID
        print("\n--- Get Citizen by ID ---\n")
        response = client.get(f'/api/citizens/{citizen_id}/')
        print_response(response)
        
        # Test citizen statistics
        print("\n--- Get Citizen Statistics ---\n")
        response = client.get('/api/citizens/statistics/')
        print_response(response)

def test_idcards():
    """Test ID card endpoints."""
    print("\n=== Testing ID Cards ===\n")
    
    # Get a citizen ID
    citizen = None
    try:
        citizen = User.objects.first()
    except:
        print("No citizens found. Create a citizen first.")
        return
    
    # Test get all ID cards
    print("\n--- Get All ID Cards ---\n")
    response = client.get('/api/idcards/')
    print_response(response)
    
    # Test create ID card
    print("\n--- Create ID Card ---\n")
    response = client.post('/api/idcards/', {
        'citizen': citizen.id,
        'status': 'draft'
    }, content_type='application/json')
    print_response(response)
    
    # Get the ID card ID for later use
    if response.status_code == 201:
        data = json.loads(response.content)
        idcard_id = data.get('id')
        print(f"ID Card ID: {idcard_id}")
        
        # Test get ID card by ID
        print("\n--- Get ID Card by ID ---\n")
        response = client.get(f'/api/idcards/{idcard_id}/')
        print_response(response)
        
        # Test update ID card status
        print("\n--- Update ID Card Status ---\n")
        response = client.post(f'/api/idcards/{idcard_id}/update_status/', {
            'status': 'pending_approval',
            'comment': 'Submitting for approval'
        }, content_type='application/json')
        print_response(response)
        
        # Test get workflow logs
        print("\n--- Get Workflow Logs ---\n")
        response = client.get(f'/api/idcards/{idcard_id}/workflow_logs/')
        print_response(response)
        
        # Test ID card statistics
        print("\n--- Get ID Card Statistics ---\n")
        response = client.get('/api/idcards/statistics/')
        print_response(response)

def test_workflows():
    """Test workflow endpoints."""
    print("\n=== Testing Workflows ===\n")
    
    # Test get all workflow logs
    print("\n--- Get All Workflow Logs ---\n")
    response = client.get('/api/workflows/')
    print_response(response)
    
    # Test workflow statistics
    print("\n--- Get Workflow Statistics ---\n")
    response = client.get('/api/workflows/statistics/')
    print_response(response)

def main():
    """Run all tests."""
    test_authentication()
    test_tenants()
    test_users()
    test_citizens()
    test_idcards()
    test_workflows()

if __name__ == '__main__':
    main()
