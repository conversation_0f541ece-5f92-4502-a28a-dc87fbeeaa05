#!/usr/bin/env python3
"""
Translation Validation System for GoID
Validates translation files for completeness, consistency, and quality
"""

import os
import re
import json
import argparse
from pathlib import Path
from collections import defaultdict

class TranslationValidator:
    def __init__(self, locale_path='/app/locale'):
        self.locale_path = Path(locale_path)
        self.supported_languages = ['en', 'am', 'om', 'ti', 'so', 'aa']
        self.validation_rules = {
            'required_keys': [
                'dashboard', 'login', 'logout', 'username', 'password',
                'save', 'cancel', 'delete', 'edit', 'add', 'search',
                'loading', 'success', 'error', 'yes', 'no'
            ],
            'max_length': 500,
            'min_length': 1,
            'forbidden_chars': ['<script>', '</script>', '<iframe>', '</iframe>'],
            'placeholder_pattern': r'\{[^}]+\}',
            'html_pattern': r'<[^>]+>'
        }
        
    def parse_po_file(self, po_file_path):
        """Parse a .po file and return translations with metadata"""
        translations = {}
        metadata = {'total': 0, 'translated': 0, 'fuzzy': 0, 'errors': []}
        
        if not os.path.exists(po_file_path):
            return translations, metadata
            
        with open(po_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse PO file entries with flags
        entries = re.findall(
            r'(?:#[^\n]*\n)*(?:#,\s*([^\n]*)\n)?msgid\s+"([^"]*)"\nmsgstr\s+"([^"]*)"',
            content,
            re.MULTILINE
        )
        
        for flags, msgid, msgstr in entries:
            if msgid:  # Skip empty msgid (header)
                metadata['total'] += 1
                
                # Unescape quotes and newlines
                msgid = msgid.replace('\\"', '"').replace('\\n', '\n')
                msgstr = msgstr.replace('\\"', '"').replace('\\n', '\n')
                
                is_fuzzy = flags and 'fuzzy' in flags
                is_translated = bool(msgstr.strip())
                
                translations[msgid] = {
                    'translation': msgstr,
                    'is_translated': is_translated,
                    'is_fuzzy': is_fuzzy,
                    'flags': flags or ''
                }
                
                if is_translated:
                    metadata['translated'] += 1
                if is_fuzzy:
                    metadata['fuzzy'] += 1
        
        return translations, metadata
    
    def validate_translation_content(self, key, translation, language):
        """Validate individual translation content"""
        issues = []
        
        if not translation:
            return issues
            
        # Length validation
        if len(translation) > self.validation_rules['max_length']:
            issues.append({
                'type': 'length_exceeded',
                'message': f'Translation too long ({len(translation)} chars, max {self.validation_rules["max_length"]})',
                'severity': 'warning'
            })
        
        if len(translation.strip()) < self.validation_rules['min_length']:
            issues.append({
                'type': 'too_short',
                'message': 'Translation is empty or too short',
                'severity': 'error'
            })
        
        # Security validation
        for forbidden in self.validation_rules['forbidden_chars']:
            if forbidden.lower() in translation.lower():
                issues.append({
                    'type': 'security_risk',
                    'message': f'Contains potentially dangerous content: {forbidden}',
                    'severity': 'error'
                })
        
        # HTML validation
        html_matches = re.findall(self.validation_rules['html_pattern'], translation)
        if html_matches:
            issues.append({
                'type': 'html_content',
                'message': f'Contains HTML tags: {", ".join(html_matches)}',
                'severity': 'warning'
            })
        
        # Placeholder validation
        placeholders = re.findall(self.validation_rules['placeholder_pattern'], translation)
        if placeholders:
            # Check if placeholders are properly formatted
            for placeholder in placeholders:
                if not re.match(r'^\{[a-zA-Z_][a-zA-Z0-9_]*\}$', placeholder):
                    issues.append({
                        'type': 'invalid_placeholder',
                        'message': f'Invalid placeholder format: {placeholder}',
                        'severity': 'error'
                    })
        
        # Language-specific validation
        if language == 'am':  # Amharic
            # Check for proper Amharic characters
            if not re.search(r'[\u1200-\u137F]', translation) and len(translation) > 5:
                issues.append({
                    'type': 'language_mismatch',
                    'message': 'Amharic translation should contain Ethiopic characters',
                    'severity': 'warning'
                })
        
        return issues
    
    def validate_translation_consistency(self, translations_by_lang):
        """Validate consistency across languages"""
        issues = []
        
        # Get all keys from all languages
        all_keys = set()
        for lang_translations in translations_by_lang.values():
            all_keys.update(lang_translations.keys())
        
        # Check for missing translations across languages
        for key in all_keys:
            languages_with_key = []
            languages_missing_key = []
            
            for lang, translations in translations_by_lang.items():
                if key in translations and translations[key]['is_translated']:
                    languages_with_key.append(lang)
                else:
                    languages_missing_key.append(lang)
            
            if languages_missing_key:
                issues.append({
                    'type': 'missing_translation',
                    'key': key,
                    'message': f'Missing in languages: {", ".join(languages_missing_key)}',
                    'present_in': languages_with_key,
                    'missing_in': languages_missing_key,
                    'severity': 'error' if len(languages_missing_key) > len(languages_with_key) else 'warning'
                })
        
        # Check for placeholder consistency
        for key in all_keys:
            placeholders_by_lang = {}
            
            for lang, translations in translations_by_lang.items():
                if key in translations and translations[key]['is_translated']:
                    translation = translations[key]['translation']
                    placeholders = set(re.findall(self.validation_rules['placeholder_pattern'], translation))
                    placeholders_by_lang[lang] = placeholders
            
            if len(placeholders_by_lang) > 1:
                # Check if all languages have the same placeholders
                placeholder_sets = list(placeholders_by_lang.values())
                if not all(pset == placeholder_sets[0] for pset in placeholder_sets):
                    issues.append({
                        'type': 'placeholder_inconsistency',
                        'key': key,
                        'message': 'Placeholder mismatch across languages',
                        'details': placeholders_by_lang,
                        'severity': 'error'
                    })
        
        return issues
    
    def validate_required_translations(self, translations, language):
        """Validate that required translations are present"""
        issues = []
        
        for required_key in self.validation_rules['required_keys']:
            if required_key not in translations:
                issues.append({
                    'type': 'missing_required',
                    'key': required_key,
                    'message': f'Required translation missing: {required_key}',
                    'severity': 'error'
                })
            elif not translations[required_key]['is_translated']:
                issues.append({
                    'type': 'untranslated_required',
                    'key': required_key,
                    'message': f'Required translation not translated: {required_key}',
                    'severity': 'error'
                })
        
        return issues
    
    def run_validation(self):
        """Run complete validation on all translation files"""
        print("🔍 Starting Translation Validation")
        print("=" * 60)
        
        all_translations = {}
        all_issues = []
        summary = {
            'languages_checked': 0,
            'total_keys': 0,
            'total_issues': 0,
            'errors': 0,
            'warnings': 0
        }
        
        # Load and validate each language
        for lang in self.supported_languages:
            if lang == 'en':  # Skip English as it's the source
                continue
                
            po_file = self.locale_path / lang / 'LC_MESSAGES' / 'django.po'
            
            if not po_file.exists():
                all_issues.append({
                    'language': lang,
                    'type': 'missing_file',
                    'message': f'Translation file not found: {po_file}',
                    'severity': 'error'
                })
                continue
            
            print(f"\n📋 Validating {lang.upper()} translations...")
            
            translations, metadata = self.parse_po_file(str(po_file))
            all_translations[lang] = translations
            
            summary['languages_checked'] += 1
            summary['total_keys'] += metadata['total']
            
            print(f"   Total strings: {metadata['total']}")
            print(f"   Translated: {metadata['translated']}")
            print(f"   Fuzzy: {metadata['fuzzy']}")
            
            # Validate individual translations
            for key, trans_data in translations.items():
                if trans_data['is_translated']:
                    content_issues = self.validate_translation_content(
                        key, trans_data['translation'], lang
                    )
                    for issue in content_issues:
                        issue['language'] = lang
                        issue['key'] = key
                        all_issues.append(issue)
            
            # Validate required translations
            required_issues = self.validate_required_translations(translations, lang)
            for issue in required_issues:
                issue['language'] = lang
                all_issues.append(issue)
        
        # Cross-language consistency validation
        if len(all_translations) > 1:
            print(f"\n🔄 Checking cross-language consistency...")
            consistency_issues = self.validate_translation_consistency(all_translations)
            all_issues.extend(consistency_issues)
        
        # Summarize issues
        for issue in all_issues:
            summary['total_issues'] += 1
            if issue['severity'] == 'error':
                summary['errors'] += 1
            else:
                summary['warnings'] += 1
        
        # Print results
        print(f"\n" + "=" * 60)
        print(f"📊 Validation Summary")
        print(f"   Languages checked: {summary['languages_checked']}")
        print(f"   Total translation keys: {summary['total_keys']}")
        print(f"   Total issues found: {summary['total_issues']}")
        print(f"   Errors: {summary['errors']}")
        print(f"   Warnings: {summary['warnings']}")
        
        if summary['errors'] > 0:
            print(f"\n❌ Validation FAILED - {summary['errors']} errors found")
        elif summary['warnings'] > 0:
            print(f"\n⚠️  Validation PASSED with warnings - {summary['warnings']} warnings")
        else:
            print(f"\n✅ Validation PASSED - No issues found")
        
        return all_issues, summary

def main():
    parser = argparse.ArgumentParser(description='GoID Translation Validation Tool')
    parser.add_argument('--output', '-o', help='Output file for validation report (JSON)')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    
    args = parser.parse_args()
    
    validator = TranslationValidator()
    issues, summary = validator.run_validation()
    
    if args.verbose and issues:
        print(f"\n📝 Detailed Issues:")
        for issue in issues[:20]:  # Show first 20 issues
            lang = issue.get('language', 'N/A')
            key = issue.get('key', 'N/A')
            print(f"   [{issue['severity'].upper()}] {lang}:{key} - {issue['message']}")
        
        if len(issues) > 20:
            print(f"   ... and {len(issues) - 20} more issues")
    
    if args.output:
        report = {
            'summary': summary,
            'issues': issues,
            'timestamp': '2025-06-26T16:30:00Z'
        }
        
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 Validation report saved to: {args.output}")
    
    # Exit with error code if validation failed
    return 1 if summary['errors'] > 0 else 0

if __name__ == '__main__':
    exit(main())
