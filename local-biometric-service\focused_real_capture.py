#!/usr/bin/env python3
"""
FOCUSED REAL CAPTURE
Based on official Futronic SDK documentation and working finger detection
"""

import ctypes
from ctypes import cdll, c_int, c_uint, c_ulong, c_void_p, c_ubyte, POINTER, byref
import time
import os

class FocusedRealCapture:
    """Focused on the most likely working signatures for real capture"""
    
    def __init__(self):
        self.scan_api = None
        self.device_handle = None
        self.is_initialized = False
        
        print("=== FOCUSED REAL CAPTURE ===")
        print("Testing only the most likely working signatures")
        
        self._initialize_device()
    
    def _initialize_device(self):
        """Initialize the Futronic device"""
        try:
            # Load DLL
            dll_path = os.path.abspath('./ftrScanAPI.dll')
            self.scan_api = cdll.LoadLibrary(dll_path)
            print("✅ DLL loaded successfully")
            
            # Initialize device
            self.scan_api.ftrScanOpenDevice.restype = c_void_p
            self.scan_api.ftrScanOpenDevice.argtypes = []
            self.device_handle = self.scan_api.ftrScanOpenDevice()
            
            if not self.device_handle:
                print("❌ Device initialization failed")
                return False
            
            print(f"✅ Device initialized: {self.device_handle}")
            self.is_initialized = True
            return True
            
        except Exception as e:
            print(f"❌ Initialization failed: {e}")
            return False
    
    def test_real_capture_signatures(self):
        """Test the most likely working signatures based on Futronic documentation"""
        
        if not self.is_initialized:
            print("❌ Device not initialized")
            return False
        
        print("\n🎯 TESTING MOST LIKELY WORKING SIGNATURES")
        print("Based on official Futronic SDK patterns...")
        
        # Most likely working signatures based on typical Futronic SDK
        likely_signatures = [
            # Standard Futronic patterns
            {
                'name': 'Standard Futronic GetFrame',
                'argtypes': [c_void_p, POINTER(c_ubyte), POINTER(c_ulong)],
                'description': 'ftrScanGetFrame(handle, buffer, &size)'
            },
            {
                'name': 'Alternative GetFrame',
                'argtypes': [c_void_p, POINTER(c_ubyte), c_ulong],
                'description': 'ftrScanGetFrame(handle, buffer, size)'
            },
            {
                'name': 'Simple GetFrame',
                'argtypes': [c_void_p, c_void_p, POINTER(c_ulong)],
                'description': 'ftrScanGetFrame(handle, void_buffer, &size)'
            },
            {
                'name': 'Direct GetFrame',
                'argtypes': [c_void_p, c_void_p, c_ulong],
                'description': 'ftrScanGetFrame(handle, void_buffer, size)'
            }
        ]
        
        for sig in likely_signatures:
            print(f"\n=== TESTING: {sig['name']} ===")
            print(f"Signature: {sig['description']}")
            
            if self._test_signature_for_real_data('ftrScanGetFrame', sig['argtypes'], sig['name']):
                print(f"🎉 SUCCESS! {sig['name']} captured real fingerprint data!")
                return True
            
            print(f"❌ {sig['name']} failed")
            time.sleep(1)  # Brief pause between tests
        
        # Try ftrScanGetImage if GetFrame fails
        print("\n🔄 ftrScanGetFrame failed, trying ftrScanGetImage...")
        
        for sig in likely_signatures:
            print(f"\n=== TESTING: {sig['name']} (GetImage) ===")
            
            if self._test_signature_for_real_data('ftrScanGetImage', sig['argtypes'], sig['name']):
                print(f"🎉 SUCCESS! {sig['name']} (GetImage) captured real fingerprint data!")
                return True
            
            print(f"❌ {sig['name']} (GetImage) failed")
            time.sleep(1)
        
        print("\n❌ All focused signatures failed")
        return False
    
    def _test_signature_for_real_data(self, function_name, argtypes, description):
        """Test a specific signature and check for real fingerprint data"""
        
        try:
            print(f"🔧 Configuring {function_name} with {description}...")
            
            # Get and configure the function
            capture_func = getattr(self.scan_api, function_name)
            capture_func.restype = c_int
            capture_func.argtypes = argtypes
            
            # Prepare buffers
            buffer_size = 320 * 480  # Standard fingerprint image size
            image_buffer = (c_ubyte * buffer_size)()
            frame_size = c_ulong(buffer_size)
            
            print("🖐️ PLACE YOUR FINGER ON THE SCANNER NOW!")
            print("⏰ You have 5 seconds...")
            
            for i in range(5):
                print(f"Place finger... {5-i} seconds")
                time.sleep(1)
            
            print("📸 ATTEMPTING REAL CAPTURE...")
            
            # Try the capture based on signature type
            result = -1
            
            if len(argtypes) == 3 and 'POINTER' in str(argtypes[2]):
                # Pointer to size
                print("Calling with size pointer...")
                size_param = c_ulong(buffer_size)
                result = capture_func(self.device_handle, image_buffer, byref(size_param))
                actual_size = size_param.value
                print(f"Result: {result}, Actual size: {actual_size}")
                
            elif len(argtypes) == 3:
                # Direct size
                print("Calling with direct size...")
                result = capture_func(self.device_handle, image_buffer, c_ulong(buffer_size))
                actual_size = buffer_size
                print(f"Result: {result}, Buffer size: {actual_size}")
                
            else:
                print(f"Unsupported signature with {len(argtypes)} parameters")
                return False
            
            # Check for success and real data
            if result == 0:  # Success
                print(f"✅ DLL call succeeded! Result: {result}")
                
                # Analyze the captured data
                if actual_size > 0:
                    image_data = bytes(image_buffer[:actual_size])
                    non_zero_bytes = sum(1 for b in image_data if b > 0)
                    unique_values = len(set(image_data[:min(1000, len(image_data))]))
                    
                    print(f"📊 Data analysis:")
                    print(f"   Size: {actual_size} bytes")
                    print(f"   Non-zero bytes: {non_zero_bytes}")
                    print(f"   Unique values: {unique_values}")
                    print(f"   Quality: {(non_zero_bytes/actual_size)*100:.1f}%")
                    
                    # Check if this looks like real fingerprint data
                    if non_zero_bytes > 1000 and unique_values > 10:
                        print("🎉 THIS LOOKS LIKE REAL FINGERPRINT DATA!")
                        
                        # Show first few bytes as hex
                        first_bytes = [f"{b:02x}" for b in image_data[:20]]
                        print(f"First 20 bytes: {' '.join(first_bytes)}")
                        
                        return True
                    else:
                        print("⚠️ Data doesn't look like real fingerprint (too uniform)")
                        return False
                else:
                    print("❌ No data captured (size = 0)")
                    return False
            else:
                print(f"❌ DLL call failed with result: {result}")
                return False
                
        except Exception as e:
            print(f"💥 Signature test crashed: {e}")
            return False
    
    def run_focused_test(self):
        """Run the focused real capture test"""
        
        if not self.is_initialized:
            print("❌ Cannot run test - device not initialized")
            return
        
        print("\n🚀 STARTING FOCUSED REAL CAPTURE TEST")
        print("Goal: Find signature that captures actual fingerprint ridges")
        
        success = self.test_real_capture_signatures()
        
        if success:
            print("\n🎉 SUCCESS! Found working real capture signature!")
        else:
            print("\n❌ FAILED! No working signature found for real capture")
            print("The device initializes but capture functions don't work properly")

if __name__ == '__main__':
    try:
        capture = FocusedRealCapture()
        capture.run_focused_test()
        
        print("\nTest completed. Press any key to exit...")
        input()
        
    except KeyboardInterrupt:
        print("\nTest interrupted")
    except Exception as e:
        print(f"Test failed: {e}")
    finally:
        print("Cleaning up...")
