#!/usr/bin/env python3
"""
Analyze the fingerprint template data from the database to understand
what biometric data is actually stored and how matching should work.
"""

import base64
import json
import sys

def analyze_fingerprint_template(template_b64):
    """Analyze a base64-encoded fingerprint template"""
    try:
        print("🔍 Analyzing Fingerprint Template")
        print("=" * 50)
        
        # Decode the base64 data
        decoded_bytes = base64.b64decode(template_b64)
        template_data = json.loads(decoded_bytes)
        
        print(f"📊 Template Structure:")
        print(f"   Version: {template_data.get('version', 'N/A')}")
        print(f"   Thumb Type: {template_data.get('thumb_type', 'N/A')}")
        print(f"   Extraction Method: {template_data.get('extraction_method', 'N/A')}")
        print(f"   Real Capture: {template_data.get('real_capture', 'N/A')}")
        print(f"   Quality Verified: {template_data.get('quality_verified', 'N/A')}")
        
        print(f"\n📏 Image Information:")
        image_dims = template_data.get('image_dimensions', {})
        print(f"   Width: {image_dims.get('width', 'N/A')} pixels")
        print(f"   Height: {image_dims.get('height', 'N/A')} pixels")
        print(f"   Total Pixels: {template_data.get('total_pixels', 'N/A')}")
        print(f"   Frame Size: {template_data.get('frame_size', 'N/A')} bytes")
        
        print(f"\n🎯 Quality Metrics:")
        print(f"   Data Quality: {template_data.get('data_quality', 'N/A')}%")
        print(f"   Minutiae Count: {template_data.get('minutiae_count', 'N/A')}")
        print(f"   Has Template: {template_data.get('has_template', 'N/A')}")
        print(f"   Has Image: {template_data.get('has_image', 'N/A')}")
        
        print(f"\n🔧 Device Information:")
        device_info = template_data.get('device_info', {})
        print(f"   Model: {device_info.get('model', 'N/A')}")
        print(f"   Interface: {device_info.get('interface', 'N/A')}")
        print(f"   Real Device: {device_info.get('real_device', 'N/A')}")
        
        print(f"\n⏰ Capture Information:")
        print(f"   Capture Time: {template_data.get('capture_time', 'N/A')}")
        print(f"   Template Hash: {template_data.get('template_hash', 'N/A')}")
        
        # Analyze minutiae points
        minutiae_points = template_data.get('minutiae_points', [])
        print(f"\n🔍 Minutiae Analysis:")
        print(f"   Total Minutiae Points: {len(minutiae_points)}")
        
        if minutiae_points:
            print(f"   Sample Minutiae Points (first 5):")
            for i, point in enumerate(minutiae_points[:5], 1):
                print(f"     {i}. X:{point.get('x', 'N/A')}, Y:{point.get('y', 'N/A')}, "
                      f"Angle:{point.get('angle', 'N/A')}°, "
                      f"Type:{point.get('type', 'N/A')}, "
                      f"Quality:{point.get('quality', 'N/A')}")
            
            # Analyze minutiae distribution
            ridge_endings = sum(1 for p in minutiae_points if p.get('type') == 'ridge_ending')
            bifurcations = sum(1 for p in minutiae_points if p.get('type') == 'bifurcation')
            
            print(f"\n   Minutiae Distribution:")
            print(f"     Ridge Endings: {ridge_endings}")
            print(f"     Bifurcations: {bifurcations}")
            
            # Quality analysis
            qualities = [p.get('quality', 0) for p in minutiae_points if 'quality' in p]
            if qualities:
                avg_quality = sum(qualities) / len(qualities)
                min_quality = min(qualities)
                max_quality = max(qualities)
                print(f"     Average Quality: {avg_quality:.3f}")
                print(f"     Quality Range: {min_quality:.3f} - {max_quality:.3f}")
        
        print(f"\n✅ ANALYSIS SUMMARY:")
        print(f"   This is REAL biometric data from a Futronic FS88H device")
        print(f"   Contains {len(minutiae_points)} minutiae points for matching")
        print(f"   High quality template (95% data quality)")
        print(f"   Suitable for duplicate detection and matching")
        
        return template_data
        
    except Exception as e:
        print(f"❌ Error analyzing template: {e}")
        return None

def test_matching_algorithm(template_data):
    """Test how the matching algorithm should work with this data"""
    print(f"\n🧪 MATCHING ALGORITHM ANALYSIS")
    print("=" * 50)
    
    minutiae_points = template_data.get('minutiae_points', [])
    
    print(f"For duplicate detection, the system should:")
    print(f"1. Compare minutiae point coordinates (X, Y positions)")
    print(f"2. Compare minutiae angles and types")
    print(f"3. Calculate spatial relationships between points")
    print(f"4. Use quality scores to weight comparisons")
    
    print(f"\nCurrent template has:")
    print(f"- {len(minutiae_points)} minutiae points to compare")
    print(f"- Coordinate range: X(0-141), Y(6-451)")
    print(f"- Both ridge endings and bifurcations")
    print(f"- Quality scores from 0.71 to 0.99")
    
    print(f"\nFor effective matching:")
    print(f"- Need at least 12-15 matching minutiae points")
    print(f"- Allow small coordinate tolerance (±3-5 pixels)")
    print(f"- Consider angle differences (±15-20 degrees)")
    print(f"- Weight by quality scores")
    print(f"- Use 85% similarity threshold for duplicates")

if __name__ == "__main__":
    # The template from the database
    template_b64 = "eyJ2ZXJzaW9uIjogIjIuMCIsICJ0aHVtYl90eXBlIjogInVua25vd24iLCAibWludXRpYWVfcG9pbnRzIjogW3sieCI6IDYyLCAieSI6IDIwOCwgImFuZ2xlIjogMTk1LCAidHlwZSI6ICJyaWRnZV9lbmRpbmciLCAicXVhbGl0eSI6IDAuOTR9LCB7IngiOiAyMywgInkiOiA0NTEsICJhbmdsZSI6IDMxNiwgInR5cGUiOiAiYmlmdXJjYXRpb24iLCAicXVhbGl0eSI6IDAuODR9LCB7IngiOiA1MCwgInkiOiAyMDQsICJhbmdsZSI6IDE4OCwgInR5cGUiOiAicmlkZ2VfZW5kaW5nIiwgInF1YWxpdHkiOiAwLjk3fSwgeyJ4IjogNzIsICJ5IjogMzY3LCAiYW5nbGUiOiAxNDQsICJ0eXBlIjogImJpZnVyY2F0aW9uIiwgInF1YWxpdHkiOiAwLjgzfSwgeyJ4IjogODksICJ5IjogMjQ3LCAiYW5nbGUiOiAxNTYsICJ0eXBlIjogInJpZGdlX2VuZGluZyIsICJxdWFsaXR5IjogMC43OTk5OTk5OTk5OTk5OTk5fSwgeyJ4IjogNzAsICJ5IjogMjAsICJhbmdsZSI6IDczLCAidHlwZSI6ICJiaWZ1cmNhdGlvbiIsICJxdWFsaXR5IjogMC44Nzk5OTk5OTk5OTk5OTk5fSwgeyJ4IjogMjAsICJ5IjogNzUsICJhbmdsZSI6IDEzLCAidHlwZSI6ICJyaWRnZV9lbmRpbmciLCAicXVhbGl0eSI6IDAuNzc5OTk5OTk5OTk5OTk5OX0sIHsieCI6IDEyMiwgInkiOiAyNjUsICJhbmdsZSI6IDMwLCAidHlwZSI6ICJiaWZ1cmNhdGlvbiIsICJxdWFsaXR5IjogMC45N30sIHsieCI6IDI3LCAieSI6IDYsICJhbmdsZSI6IDI2OSwgInR5cGUiOiAicmlkZ2VfZW5kaW5nIiwgInF1YWxpdHkiOiAwLjk5fSwgeyJ4IjogMTM5LCAieSI6IDM3NywgImFuZ2xlIjogMzQ3LCAidHlwZSI6ICJiaWZ1cmNhdGlvbiIsICJxdWFsaXR5IjogMC43NH0sIHsieCI6IDYzLCAieSI6IDM2MCwgImFuZ2xlIjogMzM3LCAidHlwZSI6ICJyaWRnZV9lbmRpbmciLCAicXVhbGl0eSI6IDAuODg5OTk5OTk5OTk5OTk5OX0sIHsieCI6IDEyMywgInkiOiAxMzgsICJhbmdsZSI6IDI1MywgInR5cGUiOiAiYmlmdXJjYXRpb24iLCAicXVhbGl0eSI6IDAuODJ9LCB7IngiOiA5MiwgInkiOiA0MSwgImFuZ2xlIjogMjkxLCAidHlwZSI6ICJyaWRnZV9lbmRpbmciLCAicXVhbGl0eSI6IDAuNzF9LCB7IngiOiAxMDcsICJ5IjogNDQxLCAiYW5nbGUiOiAyNDMsICJ0eXBlIjogImJpZnVyY2F0aW9uIiwgInF1YWxpdHkiOiAwLjkyOTk5OTk5OTk5OTk5OTl9LCB7IngiOiAxNiwgInkiOiA0NDcsICJhbmdsZSI6IDEwOSwgInR5cGUiOiAicmlkZ2VfZW5kaW5nIiwgInF1YWxpdHkiOiAwLjgwOTk5OTk5OTk5OTk5OTl9LCB7IngiOiA5OSwgInkiOiA0NDIsICJhbmdsZSI6IDEzOSwgInR5cGUiOiAiYmlmdXJjYXRpb24iLCAicXVhbGl0eSI6IDAuODc5OTk5OTk5OTk5OTk5OX0sIHsieCI6IDI4LCAieSI6IDMwOCwgImFuZ2xlIjogODQsICJ0eXBlIjogInJpZGdlX2VuZGluZyIsICJxdWFsaXR5IjogMC44NH0sIHsieCI6IDY3LCAieSI6IDEyNywgImFuZ2xlIjogMzE2LCAidHlwZSI6ICJiaWZ1cmNhdGlvbiIsICJxdWFsaXR5IjogMC45OH0sIHsieCI6IDg1LCAieSI6IDM5NywgImFuZ2xlIjogMjkxLCAidHlwZSI6ICJyaWRnZV9lbmRpbmciLCAicXVhbGl0eSI6IDAuOTA5OTk5OTk5OTk5OTk5OX0sIHsieCI6IDEwOSwgInkiOiA3LCAiYW5nbGUiOiAxLCAidHlwZSI6ICJiaWZ1cmNhdGlvbiIsICJxdWFsaXR5IjogMC43NH0sIHsieCI6IDEzLCAieSI6IDIzNiwgImFuZ2xlIjogMjA2LCAidHlwZSI6ICJyaWRnZV9lbmRpbmciLCAicXVhbGl0eSI6IDAuNzZ9LCB7IngiOiA3MSwgInkiOiA3MiwgImFuZ2xlIjogMjg5LCAidHlwZSI6ICJiaWZ1cmNhdGlvbiIsICJxdWFsaXR5IjogMC44MDk5OTk5OTk5OTk5OTk5fSwgeyJ4IjogMTI0LCAieSI6IDEyNywgImFuZ2xlIjogMzE0LCAidHlwZSI6ICJyaWRnZV9lbmRpbmciLCAicXVhbGl0eSI6IDAuNzZ9LCB7IngiOiAxMzksICJ5IjogMzgyLCAiYW5nbGUiOiA0OCwgInR5cGUiOiAiYmlmdXJjYXRpb24iLCAicXVhbGl0eSI6IDAuODc5OTk5OTk5OTk5OTk5OX0sIHsieCI6IDAsICJ5IjogNzYsICJhbmdsZSI6IDIyNywgInR5cGUiOiAicmlkZ2VfZW5kaW5nIiwgInF1YWxpdHkiOiAwLjk3fSwgeyJ4IjogMTQxLCAieSI6IDMyMSwgImFuZ2xlIjogMjQ3LCAidHlwZSI6ICJiaWZ1cmNhdGlvbiIsICJxdWFsaXR5IjogMC44Mn0sIHsieCI6IDQzLCAieSI6IDEzNywgImFuZ2xlIjogMSwgInR5cGUiOiAicmlkZ2VfZW5kaW5nIiwgInF1YWxpdHkiOiAwLjk5fSwgeyJ4IjogMTA5LCAieSI6IDE3MywgImFuZ2xlIjogMzU5LCAidHlwZSI6ICJiaWZ1cmNhdGlvbiIsICJxdWFsaXR5IjogMC44ODk5OTk5OTk5OTk5OTk5fSwgeyJ4IjogOTIsICJ5IjogMzY0LCAiYW5nbGUiOiA1MSwgInR5cGUiOiAicmlkZ2VfZW5kaW5nIiwgInF1YWxpdHkiOiAwLjg2fSwgeyJ4IjogMTIzLCAieSI6IDM1NywgImFuZ2xlIjogMjk5LCAidHlwZSI6ICJiaWZ1cmNhdGlvbiIsICJxdWFsaXR5IjogMC43NX1dLCAiaW1hZ2VfZGltZW5zaW9ucyI6IHsid2lkdGgiOiAxNjAsICJoZWlnaHQiOiA0ODB9LCAidG90YWxfcGl4ZWxzIjogNzY4MDAsICJkYXRhX3F1YWxpdHkiOiA5NSwgImV4dHJhY3Rpb25fbWV0aG9kIjogImZ1dHJvbmljX2phcl9yZWFsX2NhcHR1cmUiLCAidGVtcGxhdGVfaGFzaCI6ICI1NDM0ZTljYzQ3YjE0YmRjYTZiYmNhZDFlMGVjNGY1OSIsICJjYXB0dXJlX3RpbWUiOiAiMjAyNS0wNi0yNFQxNTowNzozMi42NDEyMTEiLCAiZnJhbWVfc2l6ZSI6IDY1NTQ0LCAibWludXRpYWVfY291bnQiOiAzMCwgImhhc190ZW1wbGF0ZSI6IHRydWUsICJoYXNfaW1hZ2UiOiB0cnVlLCAicmVhbF9jYXB0dXJlIjogdHJ1ZSwgInF1YWxpdHlfdmVyaWZpZWQiOiB0cnVlLCAiZGV2aWNlX2luZm8iOiB7Im1vZGVsIjogIkZ1dHJvbmljIEZTODhIIiwgImludGVyZmFjZSI6ICJqYXJfYnJpZGdlX3JlYWwiLCAicmVhbF9kZXZpY2UiOiB0cnVlfX0="
    
    template_data = analyze_fingerprint_template(template_b64)
    
    if template_data:
        test_matching_algorithm(template_data)
