# 🔧 Authentication 404 Error Solution

## Problem
Frontend login is failing with 404 errors on the authentication endpoint:
```
Failed to load resource: the server responded with a status of 404 (Not Found)
************:8000/api/auth/token/
```

## Root Cause Analysis
The issue is caused by:
1. **Tenant middleware interference** - The middleware was not properly handling public authentication endpoints
2. **Vite proxy configuration** - The proxy was pointing to Docker internal network instead of external IP
3. **Schema context issues** - Authentication endpoints need to run in public schema

## ✅ Complete Solution Applied

### 1. Fixed Tenant Middleware
Updated `backend/tenants/middleware.py` to properly handle public endpoints:

```python
# Skip tenant processing for public endpoints that should use public schema
public_paths = ['/admin/', '/swagger/', '/redoc/', '/static/', '/media/', '/api/auth/', '/api/shared/']
if any(request.path.startswith(path) for path in public_paths):
    # For public paths, ensure we're using the public schema
    connection = connections['default']
    connection.set_schema_to_public()
    return None
```

### 2. Updated Vite Proxy Configuration
Fixed `frontend/vite.config.js` to proxy to the correct backend URL:

```javascript
proxy: {
  '/api': {
    target: 'http://************:8000',
    changeOrigin: true,
    secure: false,
  },
},
```

### 3. Enhanced CORS Configuration
Ensured `backend/goid/settings.py` includes proper CORS settings for your IP.

## 🚀 Quick Fix Instructions

### Option 1: Use the Fix Script
```bash
chmod +x fix_auth_404.sh
./fix_auth_404.sh
```

### Option 2: Manual Steps
```bash
# 1. Restart backend to apply middleware changes
docker-compose restart backend

# 2. Restart frontend to apply Vite config changes
docker-compose restart frontend

# 3. Wait for services to restart
sleep 30

# 4. Test the authentication endpoint
python test_auth_endpoint.py
```

### Option 3: Full Rebuild (if above doesn't work)
```bash
# Stop all services
docker-compose down

# Rebuild with no cache
docker-compose build --no-cache

# Start services
docker-compose up -d

# Wait for startup
sleep 45

# Test
python test_auth_endpoint.py
```

## 🧪 Testing the Fix

### 1. Test Health Check
```bash
curl http://************:8000/
# Expected: "GoID Backend is running!"
```

### 2. Test Authentication Endpoint
```bash
curl -X POST http://************:8000/api/auth/token/ \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "admin123"}'
```

Expected response:
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "id": 1,
  "email": "<EMAIL>",
  "username": "admin",
  "role": "superadmin",
  ...
}
```

### 3. Test Frontend Login
1. Open `http://************:3000/`
2. Try logging in with:
   - **Superadmin**: `<EMAIL>` / `admin123`
   - **Tenant User**: `<EMAIL>` / `password123`

## 🔐 Login Credentials

### Superadmin (Email-based)
- **Email**: `<EMAIL>`
- **Password**: `admin123`

### Tenant Users (Domain-based)
- **Clerk**: `<EMAIL>` / `password123`
- **Kebele Admin**: `<EMAIL>` / `password123`
- **Subcity Admin**: `<EMAIL>` / `password123`

## 🔍 Verification Steps

1. **Backend Health**: `curl http://************:8000/` → Should return "GoID Backend is running!"
2. **Auth Endpoint**: Test with curl command above → Should return JWT tokens
3. **Shared API**: `curl http://************:8000/api/shared/countries/` → Should return JSON data
4. **Frontend**: Open `http://************:3000/` → Should load login page
5. **Login**: Try logging in → Should redirect to dashboard

## 🆘 Troubleshooting

### Issue: Still getting 404 on auth endpoint
**Solutions:**
1. Check Django logs: `docker-compose logs backend`
2. Verify middleware changes: Look for "public_paths" in logs
3. Restart services: `docker-compose restart backend frontend`

### Issue: CORS errors in browser
**Solutions:**
1. Check CORS settings include your IP
2. Clear browser cache
3. Try incognito/private browsing mode

### Issue: Frontend can't connect to backend
**Solutions:**
1. Verify backend is accessible: `curl http://************:8000/`
2. Check Docker network: `docker-compose ps`
3. Verify firewall settings on host machine

### Issue: Login works but redirects fail
**Solutions:**
1. Check JWT token contains proper tenant information
2. Verify user has correct role and tenant assignment
3. Check browser console for JavaScript errors

## 📋 Expected Results After Fix

- ✅ **Health check** returns "GoID Backend is running!"
- ✅ **Auth endpoint** returns JWT tokens (not 404)
- ✅ **Frontend login** works without 404 errors
- ✅ **Both email and domain-based** login work
- ✅ **Proper tenant context** is maintained after login
- ✅ **API calls** work with authentication headers

## 🔧 Manual Debugging

If issues persist, debug manually:

```bash
# Enter backend container
docker-compose exec backend bash

# Test Django shell
python manage.py shell

# In shell:
from django.urls import reverse
print(reverse('token_obtain_pair'))

from users.views import CustomTokenObtainPairView
print(CustomTokenObtainPairView)

# Test URL patterns
from django.core.management import call_command
call_command('show_urls')
```

## 📞 Support

If authentication is still not working after following this guide:
1. Provide output of `docker-compose logs backend`
2. Share results of `python test_auth_endpoint.py`
3. Include browser console errors (F12 → Console)
4. Confirm which specific step is failing

The solution addresses the core middleware and proxy configuration issues that were preventing the authentication endpoint from being accessible.
