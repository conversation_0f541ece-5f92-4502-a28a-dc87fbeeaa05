#!/usr/bin/env python
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from users.models import User, UserRole
from tenants.models import Tenant

def fix_user_role():
    print("=== FIXING USER ROLE ===")
    
    # Find the user <NAME_EMAIL>
    try:
        user = User.objects.get(email='<EMAIL>')
        print(f"Found user: {user.email}")
        print(f"Current role: {user.role}")
        print(f"Current tenant: {user.tenant}")
        
        # Update role to subcity_admin
        user.role = UserRole.SUBCITY_ADMIN
        user.save()
        
        print(f"✅ Updated role to: {user.role}")
        
        # Verify the change
        user.refresh_from_db()
        print(f"✅ Verified role in DB: {user.role}")
        
    except User.DoesNotExist:
        print("❌ User <EMAIL> not found!")
        
        # List all users
        print("\n=== ALL USERS ===")
        users = User.objects.all()
        for u in users:
            print(f"Email: {u.email}, Role: {u.role}, Tenant: {u.tenant}")

if __name__ == '__main__':
    fix_user_role()
