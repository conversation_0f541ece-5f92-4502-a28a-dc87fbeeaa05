#!/usr/bin/env python
"""
Test script to verify API endpoints are using group-based permissions correctly.
"""
import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from django_tenants.utils import schema_context
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()

def get_auth_token(user):
    """Get JWT token for user."""
    refresh = RefreshToken.for_user(user)
    return str(refresh.access_token)

def test_api_permissions():
    print('=== Testing API Endpoints with Group-Based Permissions ===')

    # <NAME_EMAIL> (kebele_leader)
    with schema_context('kebele_kebele15'):
        try:
            user = User.objects.get(email='<EMAIL>')
            token = get_auth_token(user)
            
            client = APIClient()
            client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
            
            print(f'\n🔍 Testing with {user.email} (kebele_leader)')
            
            # Test citizen endpoints
            print('\n--- Citizen Endpoints ---')
            
            # Test GET /api/citizens/ (should work - has view_citizen permission)
            response = client.get('/api/citizens/')
            print(f'GET /api/citizens/: {response.status_code} (expected: 200)')
            
            # Test POST /api/citizens/ (should work - has add_citizen permission)
            citizen_data = {
                'first_name': 'Test',
                'last_name': 'User',
                'date_of_birth': '1990-01-01',
                'sex': 'M',
                'nationality': 1,
                'city': 1,
                'subcity': 1,
                'kebele': 8
            }
            response = client.post('/api/citizens/', data=citizen_data)
            print(f'POST /api/citizens/: {response.status_code} (expected: 201 or 400)')
            
            # Test ID Card endpoints
            print('\n--- ID Card Endpoints ---')
            
            # Test GET /api/idcards/ (should work - has view_idcard permission)
            response = client.get('/api/idcards/')
            print(f'GET /api/idcards/: {response.status_code} (expected: 200)')
            
            # Test User endpoints
            print('\n--- User Endpoints ---')
            
            # Test GET /api/users/ (should fail - no manage_users permission)
            response = client.get('/api/users/')
            print(f'GET /api/users/: {response.status_code} (expected: 403)')
            
        except User.DoesNotExist:
            print('<EMAIL> not found')

    # <NAME_EMAIL> (clerk)
    with schema_context('kebele_kebele15'):
        try:
            user = User.objects.get(email='<EMAIL>')
            token = get_auth_token(user)
            
            client = APIClient()
            client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
            
            print(f'\n🔍 Testing with {user.email} (clerk)')
            
            # Test citizen endpoints
            print('\n--- Citizen Endpoints ---')
            
            # Test GET /api/citizens/ (should work - has view_citizen permission)
            response = client.get('/api/citizens/')
            print(f'GET /api/citizens/: {response.status_code} (expected: 200)')
            
            # Test ID Card endpoints
            print('\n--- ID Card Endpoints ---')
            
            # Test GET /api/idcards/ (should work - has view_idcard permission)
            response = client.get('/api/idcards/')
            print(f'GET /api/idcards/: {response.status_code} (expected: 200)')
            
            # Test User endpoints
            print('\n--- User Endpoints ---')
            
            # Test GET /api/users/ (should fail - no manage_users permission)
            response = client.get('/api/users/')
            print(f'GET /api/users/: {response.status_code} (expected: 403)')
            
        except User.DoesNotExist:
            print('<EMAIL> not found')

if __name__ == '__main__':
    test_api_permissions()
