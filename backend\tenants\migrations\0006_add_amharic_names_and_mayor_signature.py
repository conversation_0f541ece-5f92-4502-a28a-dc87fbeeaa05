# Generated migration for adding Amharic names and mayor signature

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tenants', '0005_merge_20250609_2004'),
    ]

    operations = [
        # Add Amharic name fields to CityAdministration
        migrations.AddField(
            model_name='cityadministration',
            name='city_name_am',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='City Name (Amharic)'),
        ),
        migrations.AddField(
            model_name='cityadministration',
            name='mayor_name_am',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Mayor Name (Amharic)'),
        ),
        migrations.AddField(
            model_name='cityadministration',
            name='mayor_signature',
            field=models.ImageField(blank=True, null=True, upload_to='signatures/', verbose_name='Mayor Signature'),
        ),
        migrations.AddField(
            model_name='cityadministration',
            name='deputy_mayor_am',
            field=models.Cha<PERSON><PERSON><PERSON>(blank=True, max_length=100, null=True, verbose_name='Deputy Mayor (Amharic)'),
        ),
        
        # Add Amharic name field to SubCity
        migrations.AddField(
            model_name='subcity',
            name='name_am',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Amharic)'),
        ),
        
        # Add Amharic name field to Kebele
        migrations.AddField(
            model_name='kebele',
            name='name_am',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Amharic)'),
        ),
        
        # Add Amharic name field to Ketena
        migrations.AddField(
            model_name='ketena',
            name='name_am',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Amharic)'),
        ),
    ]
