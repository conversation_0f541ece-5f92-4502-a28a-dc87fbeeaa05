from rest_framework import serializers
from .models import (
    Country, 
    Region, 
    Religion, 
    CitizenStatus, 
    MaritalStatus, 
    DocumentType, 
    EmploymentType, 
    Relationship, 
    CurrentStatus, 
    BiometricType,
    Ketena
)

class CountrySerializer(serializers.ModelSerializer):
    class Meta:
        model = Country
        fields = ['id', 'name', 'code', 'capital_city', 'flag_image', 'is_active']


class RegionSerializer(serializers.ModelSerializer):
    country_name = serializers.ReadOnlyField(source='country.name')
    
    class Meta:
        model = Region
        fields = ['id', 'name', 'code', 'country', 'country_name', 'is_active']


class ReligionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Religion
        fields = ['id', 'name', 'description', 'is_active']


class CitizenStatusSerializer(serializers.ModelSerializer):
    class Meta:
        model = CitizenStatus
        fields = ['id', 'name', 'description', 'is_active']


class MaritalStatusSerializer(serializers.ModelSerializer):
    class Meta:
        model = MaritalStatus
        fields = ['id', 'name', 'description', 'is_active']


class DocumentTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = DocumentType
        fields = ['id', 'name', 'description', 'is_active']


class EmploymentTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = EmploymentType
        fields = ['id', 'name', 'description', 'is_active']


class RelationshipSerializer(serializers.ModelSerializer):
    class Meta:
        model = Relationship
        fields = ['id', 'name', 'description', 'is_active']


class CurrentStatusSerializer(serializers.ModelSerializer):
    class Meta:
        model = CurrentStatus
        fields = ['id', 'name', 'description', 'is_active']


class BiometricTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = BiometricType
        fields = ['id', 'name', 'description', 'is_active']


class KetenaSerializer(serializers.ModelSerializer):
    class Meta:
        model = Ketena
        fields = ['id', 'name', 'code', 'description', 'is_active']
