#!/bin/bash

# Check Tenant Database and Permissions Script
# This script checks the database state and user permissions for tenant list

echo "🔍 Checking Tenant Database and Permissions"
echo "==========================================="

# Step 1: Check if tenants exist in database
echo "📍 Step 1: Checking tenants in database..."
docker-compose exec -T backend python manage.py shell << 'EOF'
from tenants.models import Tenant, Domain
from django.contrib.auth import get_user_model

print("=== TENANT DATABASE CHECK ===")

# Check tenants
tenants = Tenant.objects.all()
print(f"Total tenants in database: {tenants.count()}")

if tenants.exists():
    print("\nTenants found:")
    for tenant in tenants:
        domains = tenant.domains.all()
        domain_list = [d.domain for d in domains]
        print(f"- {tenant.name} ({tenant.type})")
        print(f"  ID: {tenant.id}")
        print(f"  Schema: {tenant.schema_name}")
        print(f"  Domains: {domain_list}")
        print(f"  Active: {tenant.is_active}")
        print(f"  Parent: {tenant.parent.name if tenant.parent else 'None'}")
        print()
else:
    print("❌ NO TENANTS FOUND IN DATABASE!")
    print("This explains why the tenant list is empty.")

print("=== USER PERMISSIONS CHECK ===")

# Check superuser
User = get_user_model()
try:
    admin_user = User.objects.get(email='<EMAIL>')
    print(f"\nAdmin user found:")
    print(f"- Email: {admin_user.email}")
    print(f"- Username: {admin_user.username}")
    print(f"- Is superuser: {admin_user.is_superuser}")
    print(f"- Is staff: {admin_user.is_staff}")
    print(f"- Is active: {admin_user.is_active}")
    print(f"- Role: {getattr(admin_user, 'role', 'No role attribute')}")
    print(f"- Tenant: {admin_user.tenant.name if admin_user.tenant else 'No tenant assigned'}")
    
    if not admin_user.is_superuser:
        print("❌ ISSUE: Admin user is not marked as superuser!")
        print("This could cause permission issues.")
        
except User.DoesNotExist:
    print("❌ ADMIN USER NOT FOUND!")
    print("User <EMAIL> does not exist.")

print("\n=== PERMISSION CLASS CHECK ===")

# Test the permission class directly
from tenants.permissions import CanManageTenants
from django.test import RequestFactory
from django.contrib.auth.models import AnonymousUser

factory = RequestFactory()
request = factory.get('/api/tenants/')

# Test with anonymous user
request.user = AnonymousUser()
permission = CanManageTenants()
anon_result = permission.has_permission(request, None)
print(f"Anonymous user permission: {anon_result}")

# Test with admin user
try:
    request.user = admin_user
    admin_result = permission.has_permission(request, None)
    print(f"Admin user permission: {admin_result}")
    
    if not admin_result:
        print("❌ ISSUE: Admin user fails CanManageTenants permission!")
        print("Check the permission class logic.")
        
except:
    print("❌ Could not test admin user permission")

print("\n=== VIEWSET QUERYSET CHECK ===")

# Test the viewset queryset
try:
    from tenants.views.tenant_views import TenantViewSet
    
    # Create a mock request with admin user
    request.user = admin_user
    
    viewset = TenantViewSet()
    viewset.request = request
    
    queryset = viewset.get_queryset()
    print(f"ViewSet queryset count: {queryset.count()}")
    
    if queryset.count() == 0:
        print("❌ ISSUE: ViewSet queryset returns no results!")
        print("Check the get_queryset method filtering logic.")
    else:
        print("✅ ViewSet queryset returns tenants")
        
except Exception as e:
    print(f"❌ Error testing ViewSet: {e}")

EOF

# Step 2: Check URL routing
echo ""
echo "📍 Step 2: Checking URL routing..."
docker-compose exec -T backend python manage.py shell << 'EOF'
from django.urls import resolve, reverse
from django.test import RequestFactory

print("=== URL ROUTING CHECK ===")

# Test URL resolution
try:
    match = resolve('/api/tenants/')
    print(f"✅ URL /api/tenants/ resolves to:")
    print(f"  View: {match.func}")
    print(f"  View name: {match.view_name}")
    print(f"  App name: {match.app_name}")
    print(f"  Namespace: {match.namespace}")
except Exception as e:
    print(f"❌ URL resolution failed: {e}")

# Test reverse URL
try:
    url = reverse('tenant-list')
    print(f"✅ Reverse URL for tenant-list: {url}")
except Exception as e:
    print(f"❌ Reverse URL failed: {e}")

EOF

# Step 3: Test the API endpoint directly in Django
echo ""
echo "📍 Step 3: Testing API endpoint directly in Django..."
docker-compose exec -T backend python manage.py shell << 'EOF'
from django.test import Client
from django.contrib.auth import get_user_model
import json

print("=== DIRECT API TEST ===")

client = Client()
User = get_user_model()

# Get admin user
try:
    admin_user = User.objects.get(email='<EMAIL>')
    
    # Login the user
    client.force_login(admin_user)
    
    # Test the tenant endpoint
    response = client.get('/api/tenants/')
    print(f"Direct API test status: {response.status_code}")
    
    if response.status_code == 200:
        try:
            data = json.loads(response.content)
            print(f"✅ API returns data: {type(data)}")
            
            if isinstance(data, list):
                print(f"Array with {len(data)} items")
            elif isinstance(data, dict) and 'results' in data:
                print(f"Paginated with {len(data.get('results', []))} items")
                print(f"Total count: {data.get('count', 'unknown')}")
            else:
                print(f"Unexpected data structure: {data}")
                
        except json.JSONDecodeError:
            print(f"Non-JSON response: {response.content}")
    else:
        print(f"❌ API failed: {response.content}")
        
except Exception as e:
    print(f"❌ Direct API test failed: {e}")

EOF

echo ""
echo "✅ Database and permissions check completed!"
echo ""
echo "🔍 Analysis:"
echo "1. If no tenants found: Need to create test tenants"
echo "2. If admin not superuser: Need to fix user permissions"
echo "3. If permission check fails: Issue with CanManageTenants"
echo "4. If queryset empty: Issue with ViewSet filtering"
echo "5. If URL routing fails: Issue with URL configuration"
echo ""
echo "💡 Quick fixes based on results:"
echo "- No tenants: python create_test_tenants.py"
echo "- User not superuser: Fix in Django admin or shell"
echo "- Permission issues: Check tenants/permissions.py"
echo "- Queryset issues: Check TenantViewSet.get_queryset()"
