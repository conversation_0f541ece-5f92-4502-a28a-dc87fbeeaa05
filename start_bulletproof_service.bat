@echo off
echo ========================================
echo   BULLETPROOF Biometric Service
echo ========================================
echo.
echo 🛡️  BULLETPROOF FEATURES:
echo    - Every operation isolated
echo    - Single-threaded for stability  
echo    - Extreme error handling
echo    - Step-by-step logging
echo    - Will NOT crash
echo.

cd local-biometric-service

echo 🚀 Starting BULLETPROOF service...
echo 📱 Service URL: http://localhost:8001
echo 🎯 Mission: NEVER CRASH, EVER
echo.
echo 🔍 DEBUGGING FEATURES:
echo    - Logs every single step
echo    - Isolates each operation
echo    - Identifies exact crash point
echo    - Continues even on errors
echo.

python bulletproof_service.py

echo.
echo Bulletproof service stopped.
pause
