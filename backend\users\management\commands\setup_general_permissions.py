"""
Management command to set up general, workflow-based permissions and groups.
This replaces the granular permission system with more intuitive, role-based permissions.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from users.models_groups import TenantGroup, GroupTemplate
from django.contrib.auth import get_user_model

User = get_user_model()


class Command(BaseCommand):
    help = 'Set up general workflow-based permissions and groups'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset existing groups and permissions',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🔧 Setting up general workflow-based permissions...')
        )

        if options['reset']:
            self.reset_permissions_and_groups()

        with transaction.atomic():
            # Create general permissions
            permissions = self.create_general_permissions()
            
            # Create system groups with general permissions
            groups = self.create_system_groups(permissions)
            
            # Create group templates
            self.create_group_templates(permissions)

        self.stdout.write(
            self.style.SUCCESS('✅ General permissions and groups setup completed!')
        )

    def reset_permissions_and_groups(self):
        """Reset existing permissions and groups"""
        self.stdout.write('🗑️ Resetting existing permissions and groups...')
        
        # Delete custom groups (keep system groups)
        TenantGroup.objects.filter(is_system_group=False).delete()
        
        # Delete custom permissions (keep Django's default ones)
        Permission.objects.filter(
            content_type__app_label__in=['users', 'tenants', 'biometrics']
        ).exclude(
            codename__in=['add_user', 'change_user', 'delete_user', 'view_user']
        ).delete()

    def create_general_permissions(self):
        """Create general workflow-based permissions"""
        self.stdout.write('📋 Creating general permissions...')
        
        # Get or create content types
        user_ct = ContentType.objects.get_for_model(User)
        
        # Define general permissions
        general_permissions = [
            # Citizen Management (Clerk Level)
            ('register_citizens', 'Can register citizens (full workflow)', user_ct),
            ('view_citizens_list', 'Can view citizens list', user_ct),
            ('view_citizen_details', 'Can view citizen details', user_ct),
            
            # ID Card Management
            ('generate_id_cards', 'Can generate ID cards', user_ct),
            ('view_id_cards_list', 'Can view ID cards list', user_ct),
            ('send_id_cards_for_approval', 'Can send ID cards for approval', user_ct),
            ('approve_id_cards', 'Can approve ID cards', user_ct),
            ('send_id_cards_to_higher_level', 'Can send ID cards to higher administrative level', user_ct),
            
            # Document Management
            ('verify_documents', 'Can verify citizen documents', user_ct),
            
            # User Management (Admin Levels)
            ('create_kebele_users', 'Can create kebele users and assign roles', user_ct),
            ('create_subcity_users', 'Can create subcity users and assign roles', user_ct),
            ('manage_all_users', 'Can manage all users across all tenants', user_ct),
            
            # Data Access (Hierarchical)
            ('view_own_kebele_data', 'Can view own kebele data', user_ct),
            ('view_child_kebeles_data', 'Can view data from child kebeles', user_ct),
            ('view_child_subcities_data', 'Can view data from child subcities', user_ct),
            ('view_all_tenants_data', 'Can view data from all tenants', user_ct),
            
            # Dashboard Access
            ('view_kebele_dashboard', 'Can view kebele dashboard', user_ct),
            ('view_subcity_dashboard', 'Can view subcity dashboard (aggregated)', user_ct),
            ('view_city_dashboard', 'Can view city dashboard (aggregated)', user_ct),
            ('view_system_dashboard', 'Can view system-wide dashboard', user_ct),
            
            # Reports
            ('view_kebele_reports', 'Can view reports for own kebele', user_ct),
            ('view_subcity_reports', 'Can view cumulative reports for subcity', user_ct),
            ('view_city_reports', 'Can view cumulative reports for city', user_ct),
            ('view_all_reports', 'Can view all reports across system', user_ct),
            
            # System Administration
            ('manage_tenants', 'Can manage tenants (create, edit, delete)', user_ct),
            ('manage_system_settings', 'Can manage system-wide settings', user_ct),
            ('full_system_access', 'Full access to all system features', user_ct),
        ]
        
        created_permissions = {}
        for codename, name, content_type in general_permissions:
            permission, created = Permission.objects.get_or_create(
                codename=codename,
                content_type=content_type,
                defaults={'name': name}
            )
            created_permissions[codename] = permission
            if created:
                self.stdout.write(f'  ✅ Created permission: {codename}')
            else:
                self.stdout.write(f'  ♻️ Updated permission: {codename}')
        
        return created_permissions

    def create_system_groups(self, permissions):
        """Create system groups with appropriate permissions"""
        self.stdout.write('👥 Creating system groups...')
        
        # Define groups and their permissions
        group_definitions = {
            'clerk': {
                'description': 'Kebele clerk with citizen registration and basic ID card management',
                'group_type': 'operational',
                'level': 10,
                'permissions': [
                    'register_citizens',
                    'view_citizens_list',
                    'view_citizen_details',
                    'generate_id_cards',
                    'view_id_cards_list',
                    'send_id_cards_for_approval',
                    'view_own_kebele_data',
                    'view_kebele_dashboard',
                ]
            },
            'kebele_leader': {
                'description': 'Kebele leader with approval and verification responsibilities',
                'group_type': 'administrative',
                'level': 30,
                'permissions': [
                    'view_citizens_list',
                    'view_citizen_details',
                    'view_id_cards_list',
                    'approve_id_cards',
                    'send_id_cards_to_higher_level',
                    'verify_documents',
                    'view_own_kebele_data',
                    'view_kebele_dashboard',
                    'view_kebele_reports',
                ]
            },
            'subcity_admin': {
                'description': 'Subcity administrator managing multiple kebeles',
                'group_type': 'administrative',
                'level': 50,
                'permissions': [
                    'create_kebele_users',
                    'view_citizens_list',
                    'view_citizen_details',
                    'view_id_cards_list',
                    'approve_id_cards',
                    'view_child_kebeles_data',
                    'view_subcity_dashboard',
                    'view_subcity_reports',
                ]
            },
            'city_admin': {
                'description': 'City administrator managing multiple subcities',
                'group_type': 'administrative',
                'level': 70,
                'permissions': [
                    'create_subcity_users',
                    'view_citizens_list',
                    'view_citizen_details',
                    'view_child_subcities_data',
                    'view_city_dashboard',
                    'view_city_reports',
                ]
            },
            'super_admin': {
                'description': 'System super administrator with full access',
                'group_type': 'administrative',
                'level': 100,
                'permissions': [
                    'full_system_access',
                    'manage_all_users',
                    'manage_tenants',
                    'manage_system_settings',
                    'view_all_tenants_data',
                    'view_system_dashboard',
                    'view_all_reports',
                    # Include all other permissions for completeness
                    'register_citizens',
                    'view_citizens_list',
                    'view_citizen_details',
                    'generate_id_cards',
                    'view_id_cards_list',
                    'approve_id_cards',
                    'verify_documents',
                ]
            }
        }
        
        created_groups = {}
        for group_name, group_config in group_definitions.items():
            # Create Django Group
            django_group, created = Group.objects.get_or_create(name=group_name)
            
            # Create or update TenantGroup
            tenant_group, created = TenantGroup.objects.get_or_create(
                group=django_group,
                defaults={
                    'description': group_config['description'],
                    'is_system_group': True,
                    'group_type': group_config['group_type'],
                    'level': group_config['level'],
                }
            )
            
            # Assign permissions
            group_permissions = []
            for perm_codename in group_config['permissions']:
                if perm_codename in permissions:
                    group_permissions.append(permissions[perm_codename])
            
            django_group.permissions.set(group_permissions)
            created_groups[group_name] = tenant_group
            
            self.stdout.write(
                f'  ✅ {"Created" if created else "Updated"} group: {group_name} '
                f'({len(group_permissions)} permissions)'
            )
        
        return created_groups

    def create_group_templates(self, permissions):
        """Create group templates for easy group creation"""
        self.stdout.write('📋 Creating group templates...')
        
        template_definitions = [
            {
                'name': 'Basic Clerk',
                'description': 'Basic clerk permissions for citizen registration',
                'group_type': 'operational',
                'level': 10,
                'permissions': ['register_citizens', 'view_citizens_list', 'view_kebele_dashboard'],
                'tenant_types': ['kebele']
            },
            {
                'name': 'Senior Clerk',
                'description': 'Senior clerk with ID card generation capabilities',
                'group_type': 'operational',
                'level': 15,
                'permissions': [
                    'register_citizens', 'view_citizens_list', 'view_citizen_details',
                    'generate_id_cards', 'view_id_cards_list', 'view_kebele_dashboard'
                ],
                'tenant_types': ['kebele']
            },
            {
                'name': 'Document Verifier',
                'description': 'Specialized role for document verification',
                'group_type': 'functional',
                'level': 20,
                'permissions': [
                    'view_citizens_list', 'view_citizen_details', 'verify_documents',
                    'view_kebele_dashboard'
                ],
                'tenant_types': ['kebele']
            },
            {
                'name': 'ID Card Processor',
                'description': 'Specialized role for ID card processing',
                'group_type': 'functional',
                'level': 20,
                'permissions': [
                    'view_citizens_list', 'generate_id_cards', 'view_id_cards_list',
                    'send_id_cards_for_approval', 'view_kebele_dashboard'
                ],
                'tenant_types': ['kebele']
            }
        ]
        
        for template_config in template_definitions:
            template, created = GroupTemplate.objects.get_or_create(
                name=template_config['name'],
                defaults={
                    'description': template_config['description'],
                    'group_type': template_config['group_type'],
                    'level': template_config['level'],
                    'tenant_types': template_config['tenant_types'],
                    'is_system_template': True
                }
            )
            
            # Assign permissions to template
            template_permissions = []
            for perm_codename in template_config['permissions']:
                if perm_codename in permissions:
                    template_permissions.append(permissions[perm_codename])
            
            template.permissions.set(template_permissions)
            
            self.stdout.write(
                f'  ✅ {"Created" if created else "Updated"} template: {template_config["name"]}'
            )

    def style_success(self, message):
        """Helper to style success messages"""
        return self.style.SUCCESS(message)

    def style_warning(self, message):
        """Helper to style warning messages"""
        return self.style.WARNING(message)
