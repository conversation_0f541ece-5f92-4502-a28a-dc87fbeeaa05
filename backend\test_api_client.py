"""
Test script for GoID API endpoints using Django test client.
"""

import os
import sys
import json
import django
from django.test import Client
from django.contrib.auth import get_user_model
from django.db import connection

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

# Import models
from tenants.models import Tenant, Domain, TenantType
from django_tenants.utils import schema_context

User = get_user_model()

# Create a test client
client = Client()

def print_response(response):
    """Print the response in a readable format."""
    print(f"Status Code: {response.status_code}")
    try:
        data = json.loads(response.content)
        print(json.dumps(data, indent=2))
    except:
        print(response.content)
    print("-" * 80)

def test_login():
    """Test login endpoint."""
    print("\n=== Testing Login ===\n")
    
    # Create a superuser if it doesn't exist
    if not User.objects.filter(username='admin').exists():
        User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            role='superadmin'
        )
        print("Superuser created successfully.")
    else:
        print("Superuser already exists.")
    
    # Test login
    response = client.post('/api/auth/token/', {
        'email': '<EMAIL>',
        'password': 'admin123'
    }, content_type='application/json')
    print_response(response)
    
    # Save the token for later use
    if response.status_code == 200:
        data = json.loads(response.content)
        token = data.get('access')
        client.defaults['HTTP_AUTHORIZATION'] = f'Bearer {token}'
        print("Token saved for future requests.")
    else:
        print("Failed to get token. Subsequent tests may fail.")

def test_get_users():
    """Test get users endpoint."""
    print("\n=== Testing Get Users ===\n")
    
    response = client.get('/api/auth/users/')
    print_response(response)

def test_get_tenants():
    """Test get tenants endpoint."""
    print("\n=== Testing Get Tenants ===\n")
    
    response = client.get('/api/tenants/')
    print_response(response)

def test_get_citizens():
    """Test get citizens endpoint."""
    print("\n=== Testing Get Citizens ===\n")
    
    response = client.get('/api/citizens/')
    print_response(response)

def test_get_idcards():
    """Test get ID cards endpoint."""
    print("\n=== Testing Get ID Cards ===\n")
    
    response = client.get('/api/idcards/')
    print_response(response)

def test_get_workflows():
    """Test get workflows endpoint."""
    print("\n=== Testing Get Workflows ===\n")
    
    response = client.get('/api/workflows/')
    print_response(response)

def main():
    """Run all tests."""
    test_login()
    test_get_users()
    test_get_tenants()
    test_get_citizens()
    test_get_idcards()
    test_get_workflows()

if __name__ == '__main__':
    main()
