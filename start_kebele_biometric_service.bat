@echo off
echo ========================================
echo     Kebele Biometric Service
echo ========================================
echo.
echo This service runs on kebele workstations
echo to provide fingerprint capture for the
echo GoID system accessed via web browser.
echo.

cd local-biometric-service

echo 🏢 Starting Kebele Biometric Service...
echo 📱 Service URL: http://localhost:8001
echo 🌐 Health Check: http://localhost:8001/api/health
echo.
echo ⚠️  KEBELE DEPLOYMENT:
echo    - This service runs on the kebele PC
echo    - Futronic FS88H device must be connected
echo    - GoID web system will connect to this service
echo    - Keep this window open during citizen registration
echo.
echo 📋 NETWORK ACCESS:
echo    - Service accessible from any browser on this PC
echo    - GoID system (running on central server) will detect this service
echo    - Automatic fallback to simulation if device not available
echo.
echo 🔧 TROUBLESHOOTING:
echo    - Ensure Futronic device is connected via USB
echo    - Check that DLL files are in this directory
echo    - Verify Windows Firewall allows Python connections
echo    - Make sure no other software is using the device
echo.
echo ❌ Close this window to stop the service
echo.

python kebele_biometric_service.py

echo.
echo Kebele biometric service stopped.
pause
