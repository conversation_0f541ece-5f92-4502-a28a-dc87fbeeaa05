# Generated by Django 4.2.7 on 2025-06-07 12:06

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('citizens', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='IDCardServiceRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('service_type', models.CharField(choices=[('renew', 'Renewal'), ('replacement', 'Replacement'), ('reprint', 'Reprint')], max_length=20)),
                ('application_method', models.CharField(choices=[('online', 'Online Application'), ('physical', 'Physical Visit')], max_length=20)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('kebele_approved', 'Approved by <PERSON><PERSON><PERSON>'), ('subcity_processing', 'Processing at SubCity'), ('printing', 'Printing'), ('ready', 'Ready for Collection'), ('completed', 'Completed'), ('rejected', 'Rejected')], default='pending', max_length=20)),
                ('reason', models.TextField()),
                ('supporting_documents', models.JSONField(blank=True, default=list)),
                ('requested_at', models.DateTimeField(auto_now_add=True)),
                ('kebele_reviewed_at', models.DateTimeField(blank=True, null=True)),
                ('subcity_forwarded_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('kebele_reviewer', models.CharField(blank=True, max_length=100, null=True)),
                ('subcity_processor', models.CharField(blank=True, max_length=100, null=True)),
                ('kebele_notes', models.TextField(blank=True, null=True)),
                ('subcity_notes', models.TextField(blank=True, null=True)),
                ('rejection_reason', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('citizen', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service_requests', to='citizens.citizen')),
            ],
            options={
                'verbose_name': 'ID Card Service Request',
                'verbose_name_plural': 'ID Card Service Requests',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='IDCardTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text="Template name (e.g., 'Default Template', 'Special Edition')", max_length=100)),
                ('description', models.TextField(blank=True, help_text='Template description')),
                ('background_color', models.CharField(default='#FFFFFF', help_text='Background color in hex format', max_length=7)),
                ('text_color', models.CharField(default='#000000', help_text='Text color in hex format', max_length=7)),
                ('accent_color', models.CharField(default='#1976D2', help_text='Accent color in hex format', max_length=7)),
                ('logo_position', models.CharField(choices=[('top_left', 'Top Left'), ('top_center', 'Top Center'), ('top_right', 'Top Right')], default='top_left', max_length=20)),
                ('photo_position', models.CharField(choices=[('left', 'Left Side'), ('right', 'Right Side'), ('center', 'Center')], default='left', max_length=20)),
                ('header_text', models.CharField(default='Federal Democratic Republic of Ethiopia', max_length=200)),
                ('subtitle_text', models.CharField(default='National ID Card', max_length=200)),
                ('footer_text', models.CharField(blank=True, help_text='Optional footer text', max_length=200)),
                ('is_active', models.BooleanField(default=True)),
                ('is_default', models.BooleanField(default=False, help_text='Default template for this tenant')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='IDCardRenewal',
            fields=[
                ('idcardservicerequest_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='idcards.idcardservicerequest')),
                ('new_expiry_date', models.DateField()),
            ],
            options={
                'verbose_name': 'ID Card Renewal',
                'verbose_name_plural': 'ID Card Renewals',
            },
            bases=('idcards.idcardservicerequest',),
        ),
        migrations.CreateModel(
            name='IDCardReplacement',
            fields=[
                ('idcardservicerequest_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='idcards.idcardservicerequest')),
                ('damage_description', models.TextField()),
                ('damage_photos', models.JSONField(blank=True, default=list)),
            ],
            options={
                'verbose_name': 'ID Card Replacement',
                'verbose_name_plural': 'ID Card Replacements',
            },
            bases=('idcards.idcardservicerequest',),
        ),
        migrations.CreateModel(
            name='IDCardReprint',
            fields=[
                ('idcardservicerequest_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='idcards.idcardservicerequest')),
                ('police_report', models.FileField(blank=True, null=True, upload_to='police_reports/')),
                ('police_report_number', models.CharField(max_length=100)),
                ('loss_date', models.DateField()),
                ('loss_location', models.CharField(max_length=200)),
            ],
            options={
                'verbose_name': 'ID Card Reprint',
                'verbose_name_plural': 'ID Card Reprints',
            },
            bases=('idcards.idcardservicerequest',),
        ),
        migrations.CreateModel(
            name='IDCard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('card_number', models.CharField(editable=False, max_length=50, unique=True)),
                ('issue_date', models.DateField(blank=True, null=True)),
                ('expiry_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('pending_approval', 'Pending Kebele Approval'), ('kebele_approved', 'Kebele Approved - Pending Subcity Approval'), ('approved', 'Fully Approved - Ready for Printing'), ('rejected', 'Rejected'), ('printed', 'Printed'), ('issued', 'Issued'), ('expired', 'Expired'), ('revoked', 'Revoked')], default='draft', max_length=20)),
                ('qr_code', models.ImageField(blank=True, null=True, upload_to='id_card_qr_codes/')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('kebele_approval_comment', models.TextField(blank=True, help_text='Comment from kebele leader during approval', null=True)),
                ('submitted_for_approval_at', models.DateTimeField(blank=True, help_text='When the ID card was submitted for kebele approval', null=True)),
                ('kebele_approved_at', models.DateTimeField(blank=True, help_text='When the ID card was approved by kebele leader', null=True)),
                ('subcity_approval_comment', models.TextField(blank=True, help_text='Comment from subcity admin during final approval', null=True)),
                ('submitted_to_subcity_at', models.DateTimeField(blank=True, help_text='When the ID card was submitted to subcity for final approval', null=True)),
                ('subcity_approved_at', models.DateTimeField(blank=True, help_text='When the ID card was finally approved by subcity admin', null=True)),
                ('has_kebele_pattern', models.BooleanField(default=False, help_text='Whether kebele approval pattern (left half) is applied')),
                ('has_subcity_pattern', models.BooleanField(default=False, help_text='Whether subcity approval pattern (right half) is applied')),
                ('printed_at', models.DateTimeField(blank=True, help_text='When the ID card was printed', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('pdf_file', models.FileField(blank=True, null=True, upload_to='id_card_pdfs/')),
                ('citizen', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='id_cards', to='citizens.citizen')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
