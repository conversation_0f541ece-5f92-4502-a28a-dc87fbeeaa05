@echo off
echo ========================================
echo    MINIMAL REAL SDK SERVICE
echo ========================================
echo.
echo 🎯 MINIMAL APPROACH - JUST TEST BASIC FUNCTIONALITY
echo.
echo ✅ WHAT THIS DOES:
echo   - Loads real SDK DLLs
echo   - Tests device connection briefly
echo   - Doesn't hold device handles
echo   - Starts Flask web service
echo   - Tests real finger detection
echo.
echo 🔧 TROUBLESHOOTING APPROACH:
echo   - Simplified initialization
echo   - Better error handling
echo   - No persistent device handles
echo   - Clear success/failure messages
echo.

python minimal_real_sdk.py

echo.
echo Minimal SDK service stopped.
pause
