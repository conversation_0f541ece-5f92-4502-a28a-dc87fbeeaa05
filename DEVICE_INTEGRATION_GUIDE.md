# Futronic FS88H Device Integration with Docker

This guide explains how to integrate the Futronic FS88H fingerprint scanner with the GoID system running in Docker containers.

## 🔄 **Current Status**

### **Development Mode (Current)**
- ✅ **Mock/Simulation Mode**: Fully functional without real hardware
- ✅ **API Integration**: Complete backend/frontend integration
- ✅ **Testing**: Comprehensive test suite available
- ✅ **Demo**: Working demo at `/demo/biometric`

### **Production Mode (Real Device)**
- 🔧 **Requires**: Additional setup steps below
- 🔧 **Hardware**: Futronic FS88H device
- 🔧 **Drivers**: Host system driver installation
- 🔧 **Docker Config**: USB device access configuration

## 🚀 **Quick Start (Development)**

**No additional setup needed!** The system works in simulation mode:

1. **Access Demo**: `http://localhost:3000/demo/biometric`
2. **Test Registration**: Complete citizen registration with mock fingerprints
3. **Run Tests**: `docker-compose exec backend python manage.py test_fingerprint_device --all`

## 🔧 **Production Setup (Real Device)**

### **Step 1: Host System Setup**

#### **Windows Host:**
```powershell
# 1. Download Futronic SDK from official website
# https://www.futronic-tech.com/downloads.html

# 2. Install Futronic SDK for Windows
# Follow the installation wizard

# 3. Connect FS88H device
# Windows should automatically install drivers

# 4. Verify device in Device Manager
# Look for "Futronic FS88H" under "Biometric Devices"
```

#### **Linux Host:**
```bash
# 1. Install system dependencies
sudo apt-get update
sudo apt-get install -y libusb-1.0-0-dev udev wget

# 2. Download and install Futronic Linux SDK
wget -O /tmp/futronic-sdk.tar.gz "https://www.futronic-tech.com/downloads/linux-sdk.tar.gz"
sudo tar -xzf /tmp/futronic-sdk.tar.gz -C /opt/
sudo chmod +x /opt/futronic-sdk/bin/*

# 3. Add udev rules for device access
sudo tee /etc/udev/rules.d/99-futronic.rules << EOF
SUBSYSTEM=="usb", ATTRS{idVendor}=="1491", ATTRS{idProduct}=="0020", MODE="0666", GROUP="plugdev"
EOF

# 4. Reload udev rules
sudo udevadm control --reload-rules
sudo udevadm trigger

# 5. Add user to plugdev group
sudo usermod -a -G plugdev $USER

# 6. Reboot or logout/login for group changes to take effect
```

### **Step 2: Docker Configuration**

#### **Enable USB Device Access:**

Uncomment the device access lines in `docker-compose.yml`:

```yaml
backend:
  build: ./backend
  command: ["bash", "/app/startup.sh"]
  volumes:
    - ./backend:/app
  ports:
    - "8000:8000"
  depends_on:
    - db
  # USB device access for Futronic FS88H
  devices:
    - /dev/bus/usb:/dev/bus/usb  # Linux
    # - //./pipe/docker_engine://./pipe/docker_desktop_data  # Windows (Docker Desktop)
  privileged: true  # Required for USB access
```

#### **Update Dockerfile:**

Uncomment the Futronic SDK installation in `backend/Dockerfile`:

```dockerfile
# Download and install Futronic SDK
RUN wget -O /tmp/futronic-sdk.tar.gz "https://www.futronic-tech.com/downloads/linux-sdk.tar.gz" \
    && tar -xzf /tmp/futronic-sdk.tar.gz -C /opt/ \
    && rm /tmp/futronic-sdk.tar.gz

# Set environment variables for SDK
ENV FUTRONIC_SDK_PATH=/opt/futronic-sdk
ENV LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/opt/futronic-sdk/lib
```

### **Step 3: Backend Code Updates**

#### **Replace Mock Implementation:**

Update `backend/biometrics/device_integration.py`:

```python
# Replace the mock implementation with real SDK calls
import ctypes
from ctypes import cdll, c_int, c_char_p, POINTER

class FutronicFS88H:
    def __init__(self):
        # Load Futronic SDK library
        try:
            self.sdk = cdll.LoadLibrary('/opt/futronic-sdk/lib/libftrScanAPI.so')
            self.device_info = DeviceInfo()
            self.is_initialized = False
        except OSError as e:
            print(f"Failed to load Futronic SDK: {e}")
            # Fallback to mock mode
            self.sdk = None
            self.device_info = DeviceInfo()
            self.is_initialized = False
    
    def initialize(self) -> bool:
        if self.sdk is None:
            # Fallback to mock mode
            return super().initialize()
        
        try:
            # Real SDK initialization
            result = self.sdk.ftrScanOpenDevice()
            if result == 0:  # Success
                self.is_initialized = True
                self.device_info.is_connected = True
                return True
            else:
                self.last_error = f"SDK initialization failed with code: {result}"
                return False
        except Exception as e:
            self.last_error = str(e)
            return False
    
    def capture_fingerprint(self, thumb_type: str) -> Optional[FingerprintTemplate]:
        if self.sdk is None:
            # Fallback to mock mode
            return super().capture_fingerprint(thumb_type)
        
        try:
            # Real SDK capture
            # Implementation depends on specific Futronic SDK API
            # This is a placeholder - actual implementation would use SDK functions
            pass
        except Exception as e:
            # Fallback to mock on error
            return super().capture_fingerprint(thumb_type)
```

### **Step 4: Rebuild and Test**

```bash
# 1. Rebuild Docker containers
docker-compose down
docker-compose build --no-cache
docker-compose up -d

# 2. Test device connection
docker-compose exec backend python manage.py test_fingerprint_device --all

# 3. Check device status
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:8000/api/biometrics/device-status/
```

## 🔍 **Troubleshooting**

### **Common Issues:**

#### **1. Device Not Detected**
```bash
# Check USB devices on host
lsusb | grep -i futronic

# Check Docker container can see USB
docker-compose exec backend ls -la /dev/bus/usb/

# Check permissions
docker-compose exec backend ls -la /dev/bus/usb/001/
```

#### **2. Permission Denied**
```bash
# Ensure user is in plugdev group
groups $USER

# Check udev rules
sudo udevadm info -a -p $(udevadm info -q path -n /dev/bus/usb/001/XXX)
```

#### **3. SDK Loading Issues**
```bash
# Check SDK files in container
docker-compose exec backend ls -la /opt/futronic-sdk/

# Check library dependencies
docker-compose exec backend ldd /opt/futronic-sdk/lib/libftrScanAPI.so
```

### **Fallback Mode:**

If device integration fails, the system automatically falls back to **simulation mode**:
- ✅ All functionality works with mock data
- ✅ Development and testing can continue
- ✅ Frontend integration remains functional
- ⚠️ No real fingerprint capture (uses generated templates)

## 📋 **Production Checklist**

### **Before Deployment:**
- [ ] Futronic FS88H device connected and recognized
- [ ] Host system drivers installed and working
- [ ] Docker USB access configured
- [ ] SDK integration tested
- [ ] Device permissions verified
- [ ] Backup/fallback mode tested

### **After Deployment:**
- [ ] Device status monitoring setup
- [ ] Staff training on device operation
- [ ] Error handling procedures documented
- [ ] Regular device maintenance scheduled

## 🔄 **Development vs Production**

| Feature | Development (Current) | Production (Real Device) |
|---------|----------------------|--------------------------|
| **Device** | Mock/Simulation | Real Futronic FS88H |
| **Setup** | No additional setup | Driver + Docker config |
| **Testing** | Full functionality | Full functionality |
| **Fingerprints** | Generated templates | Real biometric data |
| **Quality** | Simulated scores | Actual quality metrics |
| **Fraud Detection** | Mock matching | Real biometric matching |

## 📞 **Support**

### **For Device Issues:**
- **Futronic Support**: https://www.futronic-tech.com/support.html
- **SDK Documentation**: Included with SDK download
- **Community Forums**: Search for "Futronic FS88H Docker" issues

### **For Integration Issues:**
- Check the comprehensive test suite: `python manage.py test biometrics`
- Review logs: `docker-compose logs backend`
- Use the demo page: `http://localhost:3000/demo/biometric`

---

**Note**: The current implementation works perfectly in simulation mode for development and testing. Real device integration is optional and can be added when hardware is available.
