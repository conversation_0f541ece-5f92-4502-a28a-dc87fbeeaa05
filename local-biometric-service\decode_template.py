import base64
import json

# Your template
template_b64 = "eyJ2ZXJzaW9uIjogIjEuMCIsICJ0aHVtYl90eXBlIjogImxlZnQiLCAiZnJhbWVfc2l6ZSI6IDU0NTcsICJxdWFsaXR5X3Njb3JlIjogODUsICJjYXB0dXJlX3RpbWUiOiAiMjAyNS0wNi0yMFQxOTo1Mjo1OS43NjE2NDUiLCAiZGV2aWNlX2luZm8iOiB7Im1vZGVsIjogIkZ1dHJvbmljIEZTODhIIiwgImludGVyZmFjZSI6ICJpc29sYXRlZF9wcm9jZXNzIiwgInJlYWxfZGV2aWNlIjogdHJ1ZX19"

# Decode
decoded = base64.b64decode(template_b64).decode('utf-8')
template_data = json.loads(decoded)

print("DECODED TEMPLATE:")
print(json.dumps(template_data, indent=2))
