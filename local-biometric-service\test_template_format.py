#!/usr/bin/env python3
"""
Test Template Format Compatibility
"""

import urllib.request
import urllib.parse
import json
import base64

def test_template_format():
    """Test if the template format matches frontend expectations"""
    
    print("=== TESTING TEMPLATE FORMAT ===")
    print("Testing if JAR bridge returns GoID-compatible template format")
    print()
    
    # Test device status first
    print("1. Testing device status...")
    try:
        response = urllib.request.urlopen("http://localhost:8001/api/device/status", timeout=5)
        if response.getcode() == 200:
            data = json.loads(response.read().decode())
            print("✅ Device status OK")
        else:
            print("❌ Device status failed")
            return False
    except Exception as e:
        print(f"❌ Device status error: {e}")
        return False
    
    print()
    
    # Test capture and analyze template format
    print("2. Testing template format...")
    print("💡 This will open the JAR application")
    print("💡 Please capture your fingerprint and close the application")
    
    try:
        # Prepare capture request
        capture_data = json.dumps({"thumb_type": "left"}).encode()
        
        req = urllib.request.Request(
            "http://localhost:8001/api/capture/fingerprint",
            data=capture_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print("\n📡 Sending capture request...")
        
        response = urllib.request.urlopen(req, timeout=40)
        
        if response.getcode() == 200:
            result = json.loads(response.read().decode())
            
            print("✅ Capture response received")
            print(f"   Success: {result.get('success')}")
            
            if result.get('success'):
                data = result.get('data', {})
                
                print("\n🔍 Analyzing template format...")
                
                # Check required fields for frontend compatibility
                required_fields = [
                    'template', 'template_data', 'quality_score', 'quality_valid',
                    'minutiae_count', 'real_capture', 'thumb_type'
                ]
                
                missing_fields = []
                present_fields = []
                
                for field in required_fields:
                    if field in data:
                        present_fields.append(field)
                    else:
                        missing_fields.append(field)
                
                print(f"\n✅ Present fields: {present_fields}")
                if missing_fields:
                    print(f"❌ Missing fields: {missing_fields}")
                
                # Analyze template data
                template_b64 = data.get('template') or data.get('template_data')
                if template_b64:
                    try:
                        template_json = base64.b64decode(template_b64).decode()
                        template_obj = json.loads(template_json)
                        
                        print(f"\n📋 Template structure:")
                        print(f"   Version: {template_obj.get('version')}")
                        print(f"   Thumb type: {template_obj.get('thumb_type')}")
                        print(f"   Image size: {template_obj.get('image_width')}x{template_obj.get('image_height')}")
                        print(f"   Quality score: {template_obj.get('quality_score')}")
                        print(f"   Real capture: {template_obj.get('real_capture')}")
                        print(f"   Has template: {template_obj.get('has_template')}")
                        print(f"   Has image: {template_obj.get('has_image')}")
                        
                    except Exception as decode_error:
                        print(f"⚠️ Could not decode template: {decode_error}")
                
                # Check quality validation fields
                print(f"\n🎯 Quality validation fields:")
                print(f"   quality_valid: {data.get('quality_valid')}")
                print(f"   quality_message: {data.get('quality_message')}")
                print(f"   quality_score: {data.get('quality_score')}")
                print(f"   validation_passed: {data.get('validation_passed')}")
                
                # Frontend compatibility check
                print(f"\n🔍 Frontend compatibility check:")
                
                # This simulates what the frontend does
                template = data
                quality_valid = template.get('quality_valid', True)
                quality_message = template.get('quality_message', 'No message')
                
                print(f"   template.quality_valid: {quality_valid}")
                print(f"   template.quality_message: {quality_message}")
                
                if not quality_valid:
                    print(f"❌ Frontend would reject: {quality_message}")
                    return False
                else:
                    print(f"✅ Frontend should accept this template")
                    return True
                
            else:
                print(f"❌ Capture failed: {result.get('error')}")
                return False
        else:
            print(f"❌ Capture request failed: {response.getcode()}")
            return False
            
    except Exception as e:
        print(f"❌ Template format test error: {e}")
        return False

def main():
    print("Template Format Compatibility Test")
    print("=" * 40)
    
    print("This test will:")
    print("1. Capture a fingerprint using the JAR bridge")
    print("2. Analyze the returned template format")
    print("3. Check compatibility with GoID frontend expectations")
    print()
    
    input("Press Enter to start the test...")
    print()
    
    success = test_template_format()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 TEMPLATE FORMAT TEST PASSED!")
        print("✅ Template format is compatible with GoID frontend")
        print("✅ Quality validation should work correctly")
        
        print("\nNext steps:")
        print("1. The template format is now correct")
        print("2. Try fingerprint capture in GoID frontend")
        print("3. Quality validation errors should be resolved")
        
    else:
        print("❌ TEMPLATE FORMAT TEST FAILED!")
        print("❌ Template format may still have compatibility issues")
        
        print("\nTroubleshooting:")
        print("1. Check the template structure in the output above")
        print("2. Ensure all required fields are present")
        print("3. Verify quality_valid field is set correctly")
    
    print("\nTest completed.")
    input("Press Enter to exit...")

if __name__ == '__main__':
    main()
