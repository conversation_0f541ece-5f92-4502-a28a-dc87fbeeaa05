from rest_framework import permissions
from django.db import connection


class DynamicPermission(permissions.BasePermission):
    """
    Dynamic permission class that checks both role-based and granular permissions.
    
    Usage:
    class MyViewSet(viewsets.ModelViewSet):
        permission_classes = [DynamicPermission]
        required_permissions = {
            'list': ['view_citizens'],
            'retrieve': ['view_citizens'],
            'create': ['create_citizens'],
            'update': ['edit_citizens'],
            'destroy': ['delete_citizens'],
            'custom_action': ['approve_id_cards', 'print_id_cards']  # Multiple permissions (AND logic)
        }
    """
    
    def has_permission(self, request, view):
        user = request.user
        
        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False
        
        # Get required permissions for this action
        required_permissions = getattr(view, 'required_permissions', {})
        action = getattr(view, 'action', None)
        
        if not action or action not in required_permissions:
            # If no specific permissions defined, fall back to default behavior
            return True
        
        permissions_needed = required_permissions[action]
        if isinstance(permissions_needed, str):
            permissions_needed = [permissions_needed]
        
        # Check if user has all required permissions
        for permission_codename in permissions_needed:
            if not user.has_custom_permission(permission_codename):
                return False
        
        return True


class RequirePermissions(permissions.BasePermission):
    """
    Simple permission class that requires specific permissions.
    
    Usage:
    @action(detail=True, methods=['post'], permission_classes=[RequirePermissions])
    @require_permissions(['print_id_cards'])
    def print_card(self, request, pk=None):
        ...
    """
    
    def __init__(self, required_permissions=None):
        self.required_permissions = required_permissions or []
    
    def has_permission(self, request, view):
        user = request.user
        
        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False
        
        # Get required permissions from decorator or view attribute
        permissions_needed = getattr(view, '_required_permissions', self.required_permissions)
        
        if isinstance(permissions_needed, str):
            permissions_needed = [permissions_needed]
        
        # Check if user has all required permissions
        for permission_codename in permissions_needed:
            if not user.has_custom_permission(permission_codename):
                return False
        
        return True


def require_permissions(permissions):
    """
    Decorator to specify required permissions for a view method.
    
    Usage:
    @require_permissions(['print_id_cards'])
    def my_view_method(self, request, pk=None):
        ...
    """
    def decorator(func):
        func._required_permissions = permissions
        return func
    return decorator


class HybridPermission(permissions.BasePermission):
    """
    Hybrid permission class that combines legacy role-based permissions 
    with new dynamic permissions for backward compatibility.
    
    This allows gradual migration from hardcoded role permissions to dynamic permissions.
    """
    
    def __init__(self, legacy_permission_class=None, required_permissions=None):
        self.legacy_permission_class = legacy_permission_class
        self.required_permissions = required_permissions or []
    
    def has_permission(self, request, view):
        user = request.user
        
        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False
        
        # First check dynamic permissions if specified
        if self.required_permissions:
            permissions_needed = self.required_permissions
            if isinstance(permissions_needed, str):
                permissions_needed = [permissions_needed]
            
            # If user has dynamic permissions, grant access
            has_dynamic_permissions = all(
                user.has_custom_permission(perm) for perm in permissions_needed
            )
            if has_dynamic_permissions:
                return True
        
        # Fall back to legacy permission class if dynamic permissions not granted
        if self.legacy_permission_class:
            legacy_permission = self.legacy_permission_class()
            return legacy_permission.has_permission(request, view)
        
        return False
    
    def has_object_permission(self, request, view, obj):
        user = request.user
        
        # Check dynamic permissions first
        if self.required_permissions:
            permissions_needed = self.required_permissions
            if isinstance(permissions_needed, str):
                permissions_needed = [permissions_needed]
            
            has_dynamic_permissions = all(
                user.has_custom_permission(perm) for perm in permissions_needed
            )
            if has_dynamic_permissions:
                return True
        
        # Fall back to legacy permission class
        if self.legacy_permission_class:
            legacy_permission = self.legacy_permission_class()
            if hasattr(legacy_permission, 'has_object_permission'):
                return legacy_permission.has_object_permission(request, view, obj)
        
        return False


class TenantAwareDynamicPermission(DynamicPermission):
    """
    Dynamic permission class that also considers tenant context.
    Useful for multi-tenant scenarios where permissions might vary by tenant.
    """
    
    def has_permission(self, request, view):
        # First check basic dynamic permissions
        if not super().has_permission(request, view):
            return False
        
        user = request.user
        
        # Additional tenant-based checks can be added here
        # For example, checking if user's tenant matches the current tenant context
        if hasattr(connection, 'tenant') and connection.tenant:
            current_tenant = connection.tenant
            user_tenant = user.tenant
            
            # Allow if user belongs to current tenant or has hierarchy access
            if user_tenant == current_tenant:
                return True
            
            # Check hierarchy based on user's role (existing logic)
            if user.role == 'city_admin':
                if current_tenant.type == 'subcity' and current_tenant.parent == user_tenant:
                    return True
                if current_tenant.type == 'kebele' and current_tenant.parent and current_tenant.parent.parent == user_tenant:
                    return True
            
            if user.role == 'subcity_admin':
                if current_tenant.type == 'kebele' and current_tenant.parent == user_tenant:
                    return True
        
        return True  # Allow if no tenant context or user has appropriate access
