#!/bin/bash

# Fix Tenant ID Card 404 Error Sc<PERSON>t
# This script fixes the tenant-specific ID card endpoint 404 issues

echo "🔧 Fixing Tenant ID Card 404 Errors"
echo "===================================="

# Step 1: Test current state
echo "📍 Step 1: Testing current tenant ID card endpoints..."
python test_tenant_idcard_endpoints.py

# Step 2: Restart backend service to apply middleware changes
echo ""
echo "📍 Step 2: Restarting backend service to apply middleware changes..."
docker-compose restart backend

# Step 3: Wait for service to restart
echo "⏳ Waiting for backend to restart..."
sleep 25

# Step 4: Test again
echo ""
echo "📍 Step 3: Testing after restart..."
python test_tenant_idcard_endpoints.py

# Step 5: Check if we need to run migrations
echo ""
echo "📍 Step 4: Checking database and migrations..."
docker-compose exec backend python manage.py migrate_schemas --shared
docker-compose exec backend python manage.py migrate_schemas

# Step 6: Final test
echo ""
echo "📍 Step 5: Final test after migrations..."
python test_tenant_idcard_endpoints.py

echo ""
echo "✅ Tenant ID card endpoint fix completed!"
echo ""
echo "🔍 Manual testing:"
echo "1. Login to frontend: http://10.139.8.141:3000/"
echo "   Use: <EMAIL> / password123"
echo ""
echo "2. Navigate to ID Cards section"
echo "   Should load without 404 errors"
echo ""
echo "3. Test API directly:"
echo "   # First get auth token:"
echo "   curl -X POST http://10.139.8.141:8000/api/auth/token/ \\"
echo "     -H 'Content-Type: application/json' \\"
echo "     -d '{\"username\": \"<EMAIL>\", \"password\": \"password123\"}'"
echo ""
echo "   # Then test ID cards endpoint (replace TOKEN and TENANT_ID):"
echo "   curl -H 'Authorization: Bearer TOKEN' \\"
echo "     http://10.139.8.141:8000/api/tenants/TENANT_ID/idcards/"
echo ""
echo "💡 If still not working:"
echo "- Check Docker logs: docker-compose logs backend"
echo "- Verify tenant exists: Check admin panel"
echo "- Ensure user has correct tenant assignment"
echo "- Check middleware is processing tenant URLs correctly"
