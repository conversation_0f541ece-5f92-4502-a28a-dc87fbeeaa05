from django.urls import path
from .views.transfer_views import CitizenTransferViewSet

# Global transfer URLs that bypass tenant middleware
urlpatterns = [
    # Transfer endpoints - these bypass django-tenants middleware completely
    path('', CitizenTransferViewSet.as_view({
        'get': 'list',
        'post': 'create'
    }), name='global-transfers-list'),
    
    path('<int:pk>/', CitizenTransferViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'patch': 'partial_update',
        'delete': 'destroy'
    }), name='global-transfers-detail'),
    
    path('<int:pk>/upload-documents/', CitizenTransferViewSet.as_view({
        'post': 'upload_documents'
    }), name='global-transfers-upload-documents'),

    path('<int:pk>/debug-sync/', CitizenTransferViewSet.as_view({
        'get': 'debug_sync'
    }), name='global-transfers-debug-sync'),

    path('<int:pk>/force-sync-documents/', CitizenTransferViewSet.as_view({
        'post': 'force_sync_documents'
    }), name='global-transfers-force-sync-documents'),

    path('debug-transfer-data/', CitizenTransferViewSet.as_view({
        'get': 'debug_transfer_data'
    }), name='global-transfers-debug-transfer-data'),

    path('debug-by-transfer-id/', CitizenTransferViewSet.as_view({
        'get': 'debug_by_transfer_id'
    }), name='global-transfers-debug-by-transfer-id'),

    path('<int:pk>/review/', CitizenTransferViewSet.as_view({
        'post': 'review'
    }), name='global-transfers-review'),
    
    path('<int:pk>/complete/', CitizenTransferViewSet.as_view({
        'post': 'complete'
    }), name='global-transfers-complete'),
    
    path('<int:pk>/cancel/', CitizenTransferViewSet.as_view({
        'post': 'cancel'
    }), name='global-transfers-cancel'),
    
    path('stats/', CitizenTransferViewSet.as_view({
        'get': 'stats'
    }), name='global-transfers-stats'),
    
    path('available-kebeles/', CitizenTransferViewSet.as_view({
        'get': 'available_kebeles'
    }), name='global-transfers-available-kebeles'),
]
