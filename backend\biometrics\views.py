"""
Biometrics API views for fingerprint device integration.
"""

import logging
from datetime import datetime
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views.decorators.http import require_http_methods
from django.utils.translation import gettext_lazy as _

from .device_integration import get_device, FingerprintTemplate
from .fingerprint_processing import FingerprintProcessor
from .duplicate_detection_service import EnhancedDuplicateDetectionService
from .photo_duplicate_detection import photo_duplicate_detector
from tenants.permissions import CanManageTenants

logger = logging.getLogger(__name__)


@api_view(['GET'])
def health_check(request):
    """
    Health check endpoint for the biometric service.
    No authentication required - useful for monitoring.
    """
    try:
        device = get_device()
        status_info = device.get_device_status()
        
        return Response({
            'status': 'healthy',
            'service': 'GoID Biometric Service',
            'timestamp': datetime.now().isoformat(),
            'device_available': status_info.get('connected', False),
            'simulation_mode': not status_info.get('real_device', False)
        })
        
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return Response({
            'status': 'unhealthy',
            'service': 'GoID Biometric Service',
            'timestamp': datetime.now().isoformat(),
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def device_status(request):
    """
    Get fingerprint device status.
    
    Returns device connection status, model info, and last error.
    """
    try:
        device = get_device()
        status_info = device.get_device_status()
        
        return Response({
            'success': True,
            'data': status_info
        })
        
    except Exception as e:
        logger.error(f"Error getting device status: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def initialize_device(request):
    """
    Initialize the fingerprint device.
    
    Attempts to connect and initialize the Futronic FS88H device.
    """
    try:
        device = get_device()
        success = device.initialize()
        
        if success:
            return Response({
                'success': True,
                'message': 'Device initialized successfully',
                'data': device.get_device_status()
            })
        else:
            return Response({
                'success': False,
                'error': device.last_error or 'Device initialization failed'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
    except Exception as e:
        logger.error(f"Error initializing device: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def capture_fingerprint(request):
    """
    Capture a fingerprint from the device.
    
    Expected payload:
    {
        "thumb_type": "left" | "right"
    }
    """
    try:
        thumb_type = request.data.get('thumb_type')
        
        if not thumb_type or thumb_type not in ['left', 'right']:
            return Response({
                'success': False,
                'error': _('thumb_type must be "left" or "right"')
            }, status=status.HTTP_400_BAD_REQUEST)
        
        logger.info(f"🔍 GoID backend capturing {thumb_type} thumb fingerprint...")
        device = get_device()
        logger.info(f"📱 Using device: {device.__class__.__name__}")
        template = device.capture_fingerprint(thumb_type)
        
        if template:
            # Validate quality using processor
            processor = FingerprintProcessor()
            quality_result = processor.validate_template_quality(template.template_data)

            # Ensure all data is properly formatted for frontend
            template_data = template.template_data
            if not isinstance(template_data, str):
                template_data = str(template_data)

            return Response({
                'success': True,
                'data': {
                    'thumb_type': str(template.thumb_type),
                    'template_data': template_data,  # Ensure this is a string
                    'quality_score': int(template.quality_score),
                    'quality_valid': quality_result.get('is_valid', True),
                    'quality_message': quality_result.get('message', f'Quality score: {template.quality_score}%'),
                    'minutiae_count': int(template.minutiae_count),
                    'capture_time': str(template.capture_time),
                    'device_info': template.device_info
                }
            })
        else:
            return Response({
                'success': False,
                'error': _('Failed to capture fingerprint')
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
    except Exception as e:
        logger.error(f"Error capturing fingerprint: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated, CanManageTenants])
def process_fingerprint(request):
    """
    Process raw fingerprint data into a template.
    
    Expected payload:
    {
        "raw_data": "base64_encoded_data",
        "thumb_type": "left" | "right"
    }
    """
    try:
        raw_data = request.data.get('raw_data')
        thumb_type = request.data.get('thumb_type')
        
        if not raw_data:
            return Response({
                'success': False,
                'error': 'raw_data is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if not thumb_type or thumb_type not in ['left', 'right']:
            return Response({
                'success': False,
                'error': 'thumb_type must be "left" or "right"'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        device = get_device()
        template = device.process_template(raw_data, thumb_type)
        
        return Response({
            'success': True,
            'data': {
                'thumb_type': template.thumb_type,
                'template_data': template.template_data,
                'quality_score': template.quality_score,
                'minutiae_count': template.minutiae_count,
                'capture_time': template.capture_time,
                'device_info': template.device_info
            }
        })
        
    except Exception as e:
        logger.error(f"Error processing fingerprint: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def validate_quality(request):
    """
    Validate fingerprint template quality.
    
    Expected payload:
    {
        "fingerprint_data": "base64_encoded_template"
    }
    """
    try:
        fingerprint_data = request.data.get('fingerprint_data')
        
        if not fingerprint_data:
            return Response({
                'success': False,
                'error': 'fingerprint_data is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        processor = FingerprintProcessor()
        quality_result = processor.validate_template_quality(fingerprint_data)
        
        return Response({
            'success': True,
            'data': quality_result
        })
        
    except Exception as e:
        logger.error(f"Error validating fingerprint quality: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated, CanManageTenants])
def match_fingerprints(request):
    """
    Match two fingerprint templates.
    
    Expected payload:
    {
        "template1": "base64_encoded_template",
        "template2": "base64_encoded_template"
    }
    """
    try:
        template1 = request.data.get('template1')
        template2 = request.data.get('template2')
        
        if not template1 or not template2:
            return Response({
                'success': False,
                'error': 'Both template1 and template2 are required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        device = get_device()
        is_match, confidence = device.match_templates(template1, template2)
        
        return Response({
            'success': True,
            'data': {
                'is_match': is_match,
                'confidence_score': confidence,
                'match_threshold': 0.8
            }
        })
        
    except Exception as e:
        logger.error(f"Error matching fingerprints: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def check_duplicates(request):
    """
    Enhanced duplicate fingerprint checking across tenants.

    Expected payload:
    {
        "left_thumb_fingerprint": "base64_template",
        "right_thumb_fingerprint": "base64_template",
        "exclude_citizen_id": 123  // Optional: exclude specific citizen from check
    }
    """
    try:
        left_thumb = request.data.get('left_thumb_fingerprint')
        right_thumb = request.data.get('right_thumb_fingerprint')
        exclude_citizen_id = request.data.get('exclude_citizen_id')

        if not left_thumb and not right_thumb:
            return Response({
                'success': False,
                'error': 'At least one fingerprint template is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Use enhanced duplicate detection service
        detection_service = EnhancedDuplicateDetectionService()
        duplicate_result = detection_service.check_duplicate_registration({
            'left_thumb_fingerprint': left_thumb,
            'right_thumb_fingerprint': right_thumb
        }, exclude_citizen_id=exclude_citizen_id)

        return Response({
            'success': True,
            'data': duplicate_result
        })

    except Exception as e:
        logger.error(f"Error checking duplicates: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def real_time_duplicate_check(request):
    """
    Real-time duplicate checking for citizen registration.
    This endpoint is optimized for speed and should be called during registration.

    Expected payload:
    {
        "fingerprint_data": {
            "left_thumb_fingerprint": "base64_template",
            "right_thumb_fingerprint": "base64_template"
        },
        "citizen_data": {
            "first_name": "John",
            "last_name": "Doe",
            "date_of_birth": "1990-01-01"
        }
    }
    """
    try:
        fingerprint_data = request.data.get('fingerprint_data', {})
        citizen_data = request.data.get('citizen_data', {})

        if not fingerprint_data.get('left_thumb_fingerprint') and not fingerprint_data.get('right_thumb_fingerprint'):
            return Response({
                'success': False,
                'error': 'At least one fingerprint template is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Use enhanced duplicate detection service
        detection_service = EnhancedDuplicateDetectionService()
        duplicate_result = detection_service.check_duplicate_registration(fingerprint_data)

        # Add fraud prevention analysis
        fraud_analysis = {
            'risk_level': 'low',
            'block_registration': False,
            'recommendations': []
        }

        if duplicate_result['has_duplicates']:
            fraud_analysis['risk_level'] = 'critical'
            fraud_analysis['block_registration'] = True
            fraud_analysis['recommendations'].append(
                '🚨 BLOCK REGISTRATION: Duplicate fingerprints detected'
            )

            # Log security event
            logger.warning(f"🚨 FRAUD ALERT: Duplicate fingerprints detected for {citizen_data.get('first_name', 'Unknown')} {citizen_data.get('last_name', 'Unknown')}")
            for match in duplicate_result['matches'][:3]:  # Log first 3 matches
                logger.warning(f"   📍 Match: {match['citizen_name']} in {match['tenant_name']} (Score: {match['match_score']:.1f})")

        return Response({
            'success': True,
            'data': {
                'duplicate_check': duplicate_result,
                'fraud_analysis': fraud_analysis,
                'processing_time': duplicate_result.get('stats', {}).get('processing_time', 0)
            }
        })

    except Exception as e:
        logger.error(f"Error in real-time duplicate check: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated, CanManageTenants])
def bulk_duplicate_audit(request):
    """
    Perform bulk duplicate detection audit across all tenants.
    This is an administrative function for system auditing.
    """
    try:
        detection_service = EnhancedDuplicateDetectionService()
        audit_result = detection_service.bulk_duplicate_check()

        return Response({
            'success': True,
            'data': audit_result
        })

    except Exception as e:
        logger.error(f"Error in bulk duplicate audit: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated, CanManageTenants])
def clear_duplicate_cache(request):
    """
    Clear the duplicate detection cache.
    Use this after bulk data changes or system maintenance.
    """
    try:
        detection_service = EnhancedDuplicateDetectionService()
        detection_service.clear_cache()

        return Response({
            'success': True,
            'message': 'Duplicate detection cache cleared successfully'
        })

    except Exception as e:
        logger.error(f"Error clearing duplicate cache: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated, CanManageTenants])
def duplicate_detection_stats(request):
    """
    Get enhanced duplicate detection service statistics, FMatcher performance, and configuration.
    """
    try:
        detection_service = EnhancedDuplicateDetectionService()

        # Get both legacy and enhanced statistics
        try:
            legacy_stats = detection_service.get_statistics()
        except AttributeError:
            legacy_stats = detection_service.stats

        enhanced_stats = detection_service.get_enhanced_statistics()

        return Response({
            'success': True,
            'data': {
                'service_status': 'active',
                'enhanced_statistics': enhanced_stats,
                'legacy_stats': legacy_stats,
                'configuration': {
                    'cache_timeout': detection_service.cache_timeout,
                    'batch_size': detection_service.batch_size,
                    'max_concurrent_comparisons': getattr(detection_service, 'max_concurrent_comparisons', 'N/A')
                }
            }
        })

    except Exception as e:
        logger.error(f"Error getting duplicate detection stats: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def fraud_prevention_check(request):
    """
    Comprehensive fraud prevention check for citizen registration.

    Expected payload:
    {
        "citizen_data": {
            "first_name": "John",
            "last_name": "Doe",
            "date_of_birth": "1990-01-01",
            "phone": "+251911000000",
            "email": "<EMAIL>"
        },
        "biometric_data": {
            "left_thumb_fingerprint": "base64_template",
            "right_thumb_fingerprint": "base64_template"
        }
    }
    """
    try:
        citizen_data = request.data.get('citizen_data', {})
        biometric_data = request.data.get('biometric_data', {})

        if not citizen_data and not biometric_data:
            return Response({
                'success': False,
                'error': 'Either citizen_data or biometric_data is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        fraud_results = {
            'risk_level': 'low',
            'fraud_detected': False,
            'indicators': [],
            'matches': [],
            'biometric_duplicates': [],
            'personal_duplicates': [],
            'recommendations': []
        }

        # Check biometric duplicates if provided
        if biometric_data.get('left_thumb_fingerprint') or biometric_data.get('right_thumb_fingerprint'):
            processor = FingerprintProcessor()
            duplicate_result = processor.check_duplicates(biometric_data)

            if duplicate_result['has_duplicates']:
                fraud_results['fraud_detected'] = True
                fraud_results['risk_level'] = 'critical'
                fraud_results['indicators'].append('Duplicate fingerprints detected')
                fraud_results['biometric_duplicates'] = duplicate_result['matches']
                fraud_results['matches'].extend(duplicate_result['matches'])
                fraud_results['recommendations'].append(
                    'BLOCK REGISTRATION: Identical fingerprints found in system'
                )

        # Check personal information duplicates if provided
        if citizen_data:
            # Import here to avoid circular imports
            from idcards.fraud_detection import FraudDetectionService

            fraud_service = FraudDetectionService()
            personal_fraud = fraud_service.check_fraud_indicators(citizen_data, request.user.tenant_id)

            if personal_fraud['risk_level'] in ['high', 'critical']:
                if fraud_results['risk_level'] == 'low':
                    fraud_results['risk_level'] = personal_fraud['risk_level']
                fraud_results['indicators'].extend(personal_fraud['indicators'])
                fraud_results['personal_duplicates'] = personal_fraud.get('matches', [])
                fraud_results['matches'].extend(personal_fraud.get('matches', []))

                if personal_fraud['risk_level'] == 'critical':
                    fraud_results['fraud_detected'] = True
                    fraud_results['recommendations'].append(
                        'HIGH RISK: Similar personal information found in other kebeles'
                    )

        # Generate final recommendations
        if fraud_results['risk_level'] == 'critical':
            fraud_results['recommendations'].insert(0,
                '🚨 REGISTRATION BLOCKED: Critical fraud indicators detected'
            )
        elif fraud_results['risk_level'] == 'high':
            fraud_results['recommendations'].insert(0,
                '⚠️ HIGH RISK: Manual verification required before registration'
            )
        elif fraud_results['risk_level'] == 'medium':
            fraud_results['recommendations'].insert(0,
                '⚠️ MEDIUM RISK: Additional verification recommended'
            )
        else:
            fraud_results['recommendations'].append(
                '✅ LOW RISK: No significant fraud indicators detected'
            )

        return Response({
            'success': True,
            'data': fraud_results
        })

    except Exception as e:
        logger.error(f"Error in fraud prevention check: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated, CanManageTenants])
def biometric_stats(request):
    """
    Get biometric statistics for the current tenant.
    """
    try:
        processor = FingerprintProcessor()
        stats = processor.get_biometric_statistics()
        
        return Response({
            'success': True,
            'data': stats
        })
        
    except Exception as e:
        logger.error(f"Error getting biometric stats: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def test_duplicate_detection(request):
    """
    Test endpoint to verify duplicate detection is working.
    This will create test data and check if duplicates are detected.
    """
    try:
        # Create a test fingerprint template
        test_template = "test_fingerprint_template_12345"

        processor = FingerprintProcessor()

        # Test 1: Compare identical templates
        match1, score1 = processor._compare_templates(test_template, test_template)

        # Test 2: Compare different templates
        match2, score2 = processor._compare_templates(test_template, "different_template_67890")

        # Test 3: Check current biometric database
        from tenants.models.citizen import Biometric
        biometric_count = Biometric.objects.count()

        # Test 4: Run actual duplicate check with test data
        test_data = {
            'left_thumb_fingerprint': test_template,
            'right_thumb_fingerprint': test_template
        }
        duplicate_result = processor.check_duplicates(test_data)

        return Response({
            'success': True,
            'data': {
                'test_results': {
                    'identical_templates': {
                        'match': match1,
                        'score': score1,
                        'expected': 'Should be True with score 1.0'
                    },
                    'different_templates': {
                        'match': match2,
                        'score': score2,
                        'expected': 'Should be False with low score'
                    },
                    'database_check': {
                        'biometric_records_found': biometric_count,
                        'duplicate_check_result': duplicate_result
                    }
                },
                'processor_config': {
                    'match_threshold': processor.match_threshold,
                    'quality_threshold': processor.quality_threshold
                },
                'recommendations': [
                    'If identical_templates.match is False, the comparison algorithm needs fixing',
                    'If biometric_records_found > 0 but no duplicates detected, check real data comparison',
                    'Try registering the same citizen twice to test real duplicate detection'
                ]
            }
        })

    except Exception as e:
        logger.error(f"Error in test duplicate detection: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def debug_fingerprint_data(request):
    """
    Debug endpoint to examine what fingerprint data is actually stored in the database.
    """
    try:
        from tenants.models.citizen import Biometric, Citizen
        from tenants.models import Tenant
        from django_tenants.utils import schema_context
        import base64
        import json

        debug_info = {
            'current_tenant': request.user.tenant.name if hasattr(request.user, 'tenant') else 'Unknown',
            'tenants_checked': [],
            'biometric_records': [],
            'template_analysis': []
        }

        # Get all kebele tenants
        tenants = Tenant.objects.filter(type='kebele')

        for tenant in tenants:
            tenant_info = {
                'name': tenant.name,
                'schema': tenant.schema_name,
                'biometric_count': 0,
                'citizens': []
            }

            try:
                with schema_context(tenant.schema_name):
                    biometrics = Biometric.objects.select_related('citizen').all()[:5]  # Limit to first 5
                    tenant_info['biometric_count'] = Biometric.objects.count()

                    for bio in biometrics:
                        citizen_info = {
                            'citizen_name': f"{bio.citizen.first_name} {bio.citizen.last_name}",
                            'citizen_id': bio.citizen.digital_id,
                            'left_thumb_length': len(bio.left_thumb_fingerprint) if bio.left_thumb_fingerprint else 0,
                            'right_thumb_length': len(bio.right_thumb_fingerprint) if bio.right_thumb_fingerprint else 0,
                            'left_thumb_preview': bio.left_thumb_fingerprint[:100] + '...' if bio.left_thumb_fingerprint else None,
                            'right_thumb_preview': bio.right_thumb_fingerprint[:100] + '...' if bio.right_thumb_fingerprint else None
                        }

                        # Try to decode and analyze the template
                        if bio.left_thumb_fingerprint:
                            try:
                                decoded = json.loads(base64.b64decode(bio.left_thumb_fingerprint).decode())
                                citizen_info['left_template_analysis'] = {
                                    'version': decoded.get('version', 'Unknown'),
                                    'has_fingerprint_image': 'fingerprint_image' in decoded,
                                    'frame_size': decoded.get('frame_size', 0),
                                    'device_model': decoded.get('device_info', {}).get('model', 'Unknown'),
                                    'thumb_type': decoded.get('thumb_type', 'Unknown'),
                                    'capture_time': decoded.get('capture_time', 'Unknown')
                                }

                                if 'fingerprint_image' in decoded:
                                    image_data = base64.b64decode(decoded['fingerprint_image'])
                                    citizen_info['left_template_analysis']['fingerprint_image_size'] = len(image_data)
                                    citizen_info['left_template_analysis']['fingerprint_image_preview'] = image_data[:20].hex()

                            except Exception as e:
                                citizen_info['left_template_analysis'] = {'error': str(e)}

                        tenant_info['citizens'].append(citizen_info)

            except Exception as e:
                tenant_info['error'] = str(e)

            debug_info['tenants_checked'].append(tenant_info)

        return Response({
            'success': True,
            'data': debug_info
        })

    except Exception as e:
        logger.error(f"Error in debug fingerprint data: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def capture_and_check_duplicates(request):
    """
    Capture fingerprint from device on port 8002 and immediately check for duplicates.
    This endpoint integrates device capture with duplicate detection.

    Expected payload:
    {
        "thumb_type": "left",  // or "right"
        "citizen_id": 123      // optional: exclude this citizen from duplicate check
    }
    """
    try:
        thumb_type = request.data.get('thumb_type', 'left')
        citizen_id = request.data.get('citizen_id')

        if thumb_type not in ['left', 'right']:
            return Response({
                'success': False,
                'error': 'Invalid thumb_type. Must be "left" or "right"'
            }, status=status.HTTP_400_BAD_REQUEST)

        logger.info(f"🔍 Capture and duplicate check for {thumb_type} thumb")

        # Step 1: Capture fingerprint from device on port 8002
        import requests
        try:
            device_response = requests.post(
                'http://localhost:8002/api/capture/fingerprint',
                json={'thumb_type': thumb_type},
                timeout=30
            )

            if device_response.status_code != 200:
                return Response({
                    'success': False,
                    'error': f'Device capture failed: HTTP {device_response.status_code}'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            device_data = device_response.json()

            if not device_data.get('success'):
                return Response({
                    'success': False,
                    'error': f'Device capture failed: {device_data.get("error", "Unknown error")}'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except requests.exceptions.ConnectionError:
            return Response({
                'success': False,
                'error': 'Device service on port 8002 is not available. Please start the biometric service.'
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
        except Exception as device_error:
            return Response({
                'success': False,
                'error': f'Device communication error: {str(device_error)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Step 2: Extract template data
        capture_data = device_data.get('data', {})
        template_data_str = capture_data.get('template_data')

        if not template_data_str:
            return Response({
                'success': False,
                'error': 'No template data received from device'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Step 3: Check for duplicates using enhanced service
        detection_service = EnhancedDuplicateDetectionService()

        fingerprint_data = {}
        if thumb_type == 'left':
            fingerprint_data['left_thumb_fingerprint'] = template_data_str
        else:
            fingerprint_data['right_thumb_fingerprint'] = template_data_str

        duplicate_result = detection_service.check_duplicate_registration(
            fingerprint_data,
            exclude_citizen_id=citizen_id
        )

        # Step 4: Return comprehensive result
        return Response({
            'success': True,
            'data': {
                'capture_result': {
                    'thumb_type': thumb_type,
                    'quality_score': capture_data.get('quality_score', 0),
                    'template_size': capture_data.get('template_size', 0),
                    'has_ansi_template': capture_data.get('has_ansi_template', False),
                    'capture_time': capture_data.get('capture_time'),
                    'device_info': capture_data.get('device_info', {})
                },
                'duplicate_check': duplicate_result,
                'recommendation': {
                    'allow_registration': not duplicate_result['has_duplicates'],
                    'action': 'block_registration' if duplicate_result['has_duplicates'] else 'allow_registration',
                    'reason': f"Found {duplicate_result['total_matches']} duplicate(s)" if duplicate_result['has_duplicates'] else "No duplicates detected"
                }
            }
        })

    except Exception as e:
        logger.error(f"Error in capture and duplicate check: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def save_fingerprint_from_device(request):
    """
    Capture fingerprint from device on port 8002 and save to database.
    This endpoint handles the complete workflow: capture -> duplicate check -> save.

    Expected payload:
    {
        "citizen_id": 123,
        "thumb_type": "left"  // or "right"
    }
    """
    try:
        citizen_id = request.data.get('citizen_id')
        thumb_type = request.data.get('thumb_type', 'left')

        if not citizen_id:
            return Response({
                'success': False,
                'error': 'citizen_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        if thumb_type not in ['left', 'right']:
            return Response({
                'success': False,
                'error': 'Invalid thumb_type. Must be "left" or "right"'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get citizen
        from citizens.models import Citizen, Biometric
        try:
            citizen = Citizen.objects.get(id=citizen_id)
        except Citizen.DoesNotExist:
            return Response({
                'success': False,
                'error': f'Citizen with ID {citizen_id} not found'
            }, status=status.HTTP_404_NOT_FOUND)

        logger.info(f"💾 Saving {thumb_type} thumb for citizen {citizen_id}")

        # Step 1: Capture from device
        import requests
        try:
            device_response = requests.post(
                'http://localhost:8002/api/capture/fingerprint',
                json={'thumb_type': thumb_type},
                timeout=30
            )

            if device_response.status_code != 200:
                return Response({
                    'success': False,
                    'error': f'Device capture failed: HTTP {device_response.status_code}'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            device_data = device_response.json()

            if not device_data.get('success'):
                return Response({
                    'success': False,
                    'error': f'Device capture failed: {device_data.get("error", "Unknown error")}'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except requests.exceptions.ConnectionError:
            return Response({
                'success': False,
                'error': 'Device service on port 8002 is not available'
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)

        # Step 2: Check for duplicates (excluding current citizen)
        capture_data = device_data.get('data', {})
        template_data_str = capture_data.get('template_data')

        detection_service = EnhancedDuplicateDetectionService()
        fingerprint_data = {}
        if thumb_type == 'left':
            fingerprint_data['left_thumb_fingerprint'] = template_data_str
        else:
            fingerprint_data['right_thumb_fingerprint'] = template_data_str

        duplicate_result = detection_service.check_duplicate_registration(
            fingerprint_data,
            exclude_citizen_id=citizen_id
        )

        # Step 3: Handle duplicates
        if duplicate_result['has_duplicates']:
            logger.warning(f"🚨 Duplicate fingerprint detected for citizen {citizen_id}")
            return Response({
                'success': False,
                'error': 'Duplicate fingerprint detected',
                'error_code': 'DUPLICATE_BIOMETRIC',
                'duplicate_data': duplicate_result
            }, status=status.HTTP_409_CONFLICT)

        # Step 4: Save to database
        biometric, created = Biometric.objects.get_or_create(
            citizen=citizen,
            defaults={}
        )

        # Store the template data (base64 encoded JSON format)
        if thumb_type == 'left':
            biometric.left_thumb_fingerprint = template_data_str
        else:
            biometric.right_thumb_fingerprint = template_data_str

        biometric.save()

        # Step 5: Clear cache to include new template
        detection_service.clear_cache()

        logger.info(f"✅ Fingerprint saved for citizen {citizen_id} ({thumb_type} thumb)")

        return Response({
            'success': True,
            'message': f'Fingerprint captured and saved successfully',
            'data': {
                'citizen_id': citizen_id,
                'thumb_type': thumb_type,
                'quality_score': capture_data.get('quality_score', 0),
                'template_size': capture_data.get('template_size', 0),
                'biometric_record_created': created,
                'duplicate_check_passed': True
            }
        })

    except Exception as e:
        logger.error(f"Error saving fingerprint from device: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def check_photo_duplicates(request):
    """
    Check for duplicate photos across tenants.

    Expected payload:
    {
        "photo_data": "base64_encoded_photo_data",
        "exclude_citizen_id": 123  // Optional: exclude specific citizen from check
    }
    """
    try:
        photo_data = request.data.get('photo_data')
        exclude_citizen_id = request.data.get('exclude_citizen_id')

        if not photo_data:
            return Response({
                'success': False,
                'error': 'Photo data is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Use photo duplicate detection service
        duplicate_result = photo_duplicate_detector.check_photo_duplicates(
            photo_data,
            exclude_citizen_id=exclude_citizen_id
        )

        return Response({
            'success': True,
            'data': duplicate_result
        })

    except Exception as e:
        logger.error(f"Error checking photo duplicates: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def comprehensive_duplicate_check(request):
    """
    Comprehensive duplicate check including both fingerprints and photos.

    Expected payload:
    {
        "fingerprint_data": {
            "left_thumb_fingerprint": "base64_template",
            "right_thumb_fingerprint": "base64_template"
        },
        "photo_data": "base64_encoded_photo_data",
        "citizen_data": {
            "first_name": "John",
            "last_name": "Doe"
        },
        "exclude_citizen_id": 123  // Optional
    }
    """
    try:
        fingerprint_data = request.data.get('fingerprint_data', {})
        photo_data = request.data.get('photo_data')
        citizen_data = request.data.get('citizen_data', {})
        exclude_citizen_id = request.data.get('exclude_citizen_id')

        results = {
            'fingerprint_check': None,
            'photo_check': None,
            'overall_assessment': None
        }

        # Check fingerprint duplicates
        if fingerprint_data.get('left_thumb_fingerprint') or fingerprint_data.get('right_thumb_fingerprint'):
            detection_service = EnhancedDuplicateDetectionService()
            results['fingerprint_check'] = detection_service.check_duplicate_registration(
                fingerprint_data,
                exclude_citizen_id=exclude_citizen_id
            )

        # Check photo duplicates
        if photo_data:
            results['photo_check'] = photo_duplicate_detector.check_photo_duplicates(
                photo_data,
                exclude_citizen_id=exclude_citizen_id
            )

        # Overall assessment
        fingerprint_duplicates = results['fingerprint_check'] and results['fingerprint_check']['has_duplicates']
        photo_duplicates = results['photo_check'] and results['photo_check']['has_duplicates']

        has_any_duplicates = fingerprint_duplicates or photo_duplicates

        # Determine risk level
        risk_level = 'low'
        recommendations = []

        if fingerprint_duplicates and photo_duplicates:
            risk_level = 'critical'
            recommendations.append('🚨 CRITICAL: Both fingerprint and photo duplicates detected')
            recommendations.append('🚫 BLOCK REGISTRATION: High probability of duplicate citizen')
        elif fingerprint_duplicates:
            risk_level = 'high'
            recommendations.append('🚨 HIGH RISK: Fingerprint duplicate detected')
            recommendations.append('⚠️ INVESTIGATE: Verify citizen identity before proceeding')
        elif photo_duplicates:
            risk_level = 'medium'
            recommendations.append('⚠️ MEDIUM RISK: Photo duplicate detected')
            recommendations.append('🔍 REVIEW: Check if same person with updated photo')
        else:
            risk_level = 'low'
            recommendations.append('✅ LOW RISK: No duplicates detected')
            recommendations.append('✅ ALLOW: Registration can proceed')

        results['overall_assessment'] = {
            'has_duplicates': has_any_duplicates,
            'risk_level': risk_level,
            'block_registration': risk_level in ['critical', 'high'],
            'recommendations': recommendations,
            'duplicate_types': {
                'fingerprint': fingerprint_duplicates,
                'photo': photo_duplicates
            }
        }

        # Log security event if duplicates found
        if has_any_duplicates:
            citizen_name = f"{citizen_data.get('first_name', 'Unknown')} {citizen_data.get('last_name', 'Unknown')}"
            logger.warning(f"🚨 COMPREHENSIVE DUPLICATE ALERT: {citizen_name}")
            logger.warning(f"   Fingerprint duplicates: {fingerprint_duplicates}")
            logger.warning(f"   Photo duplicates: {photo_duplicates}")
            logger.warning(f"   Risk level: {risk_level}")

        return Response({
            'success': True,
            'data': results
        })

    except Exception as e:
        logger.error(f"Error in comprehensive duplicate check: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
