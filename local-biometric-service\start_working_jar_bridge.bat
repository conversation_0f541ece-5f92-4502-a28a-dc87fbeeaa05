@echo off
echo ========================================
echo   WORKING JAR BRIDGE SERVICE
echo   Using Proven GonderFingerPrint.jar
echo ========================================
echo.

echo MISSION: Use the working JAR file for real fingerprint capture
echo.
echo ADVANTAGES:
echo   - Uses proven working GonderFingerPrint.jar
echo   - No need to reverse-engineer DLL signatures
echo   - Leverages existing working solution
echo   - Provides same API for GoID integration
echo.

echo Checking Python installation...
python --version
if errorlevel 1 (
    echo ERROR: Python not found. Please install Python 3.7+
    pause
    exit /b 1
)

echo.
echo Checking JAR file and dependencies...
if not exist "fingerPrint\GonderFingerPrint.jar" (
    echo ERROR: GonderFingerPrint.jar not found
    echo Please ensure the JAR file is in the fingerPrint directory
    pause
    exit /b 1
)

echo SUCCESS: GonderFingerPrint.jar found

if not exist "fingerPrint\ftrScanAPI.dll" (
    echo ERROR: ftrScanAPI.dll not found in fingerPrint directory
    pause
    exit /b 1
)

echo SUCCESS: Required DLL files found

echo.
echo Starting Working JAR Bridge Service...
echo Service will be available at: http://localhost:8001
echo.
echo This service will use your proven working JAR file!
echo.
echo Press Ctrl+C to stop the service
echo.

python working_jar_bridge.py

echo.
echo Service stopped.
pause
