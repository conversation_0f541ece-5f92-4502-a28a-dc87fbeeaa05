import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Grid,
  Divider,
  <PERSON><PERSON>,
  <PERSON>ir<PERSON><PERSON><PERSON>ress,
  <PERSON><PERSON>,
  <PERSON>,
  StepLabel,
  Paper,
  TextField,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import {
  ArrowBack as BackIcon,
  Save as SaveIcon,
  Check as CheckIcon,
  Print as PrintIcon,
} from '@mui/icons-material';
import axios from '../../utils/axios';

// Mock data for citizens
const mockCitizens = [
  {
    id: 1,
    first_name: '<PERSON><PERSON>',
    last_name: '<PERSON><PERSON><PERSON>',
    gender: 'male',
    date_of_birth: '1985-05-15',
    phone_number: '+251911234567',
    address: '<PERSON>le, Addis Ababa',
    nationality: 'Ethiopian',
    occupation: 'Teacher',
    emergency_contact_name: '<PERSON><PERSON>',
    emergency_contact_phone: '+251922345678',
    has_id_card: false,
    created_at: '2023-11-10T12:00:00Z',
  },
  {
    id: 2,
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    gender: 'female',
    date_of_birth: '1990-08-22',
    phone_number: '+251922345678',
    address: '<PERSON><PERSON>, Addis Ababa',
    nationality: 'Ethiopian',
    occupation: 'Doctor',
    emergency_contact_name: '<PERSON> Mohammed',
    emergency_contact_phone: '+251933456789',
    has_id_card: false,
    created_at: '2023-11-11T12:00:00Z',
  },
  {
    id: 3,
    first_name: 'Daniel',
    last_name: 'Tesfaye',
    gender: 'male',
    date_of_birth: '1978-12-10',
    phone_number: '+251933456789',
    address: 'Bole, Addis Ababa',
    nationality: 'Ethiopian',
    occupation: 'Engineer',
    emergency_contact_name: 'Tigist Tesfaye',
    emergency_contact_phone: '+251944567890',
    has_id_card: false,
    created_at: '2023-11-12T12:00:00Z',
  },
];

const steps = ['Verify Information', 'Review & Submit', 'Approval', 'Print ID Card'];

const IDCardCreate = () => {
  const { citizenId } = useParams();
  const navigate = useNavigate();
  const [citizen, setCitizen] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeStep, setActiveStep] = useState(0);
  const [idCardData, setIdCardData] = useState({
    card_number: '',
    issue_date: new Date().toISOString().split('T')[0],
    expiry_date: new Date(new Date().setFullYear(new Date().getFullYear() + 5)).toISOString().split('T')[0],
    status: 'draft',
  });
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [notes, setNotes] = useState('');
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    // In a real implementation, this would fetch data from the API
    // For now, we'll just use mock data
    const timer = setTimeout(() => {
      const foundCitizen = mockCitizens.find(c => c.id === parseInt(citizenId));
      if (foundCitizen) {
        setCitizen(foundCitizen);
        // Generate a card number
        const randomDigits = Math.floor(100000 + Math.random() * 900000);
        setIdCardData(prev => ({
          ...prev,
          card_number: `ETH-KBOL-${randomDigits}`,
        }));
      } else {
        setError('Citizen not found');
      }
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [citizenId]);

  const handleNext = () => {
    if (activeStep === 0 && !termsAccepted) {
      setError('You must verify that the information is correct');
      return;
    }

    if (activeStep === 1) {
      // Submit the ID card for approval
      setLoading(true);
      // In a real implementation, this would call an API endpoint
      setTimeout(() => {
        setIdCardData(prev => ({
          ...prev,
          status: 'pending_approval',
        }));
        setLoading(false);
        setActiveStep(prev => prev + 1);
      }, 1000);
      return;
    }

    if (activeStep === 2) {
      // Approve the ID card
      setLoading(true);
      // In a real implementation, this would call an API endpoint
      setTimeout(() => {
        setIdCardData(prev => ({
          ...prev,
          status: 'approved',
        }));
        setLoading(false);
        setActiveStep(prev => prev + 1);
      }, 1000);
      return;
    }

    if (activeStep === 3) {
      // Print the ID card
      navigate(`/idcards/${1}/print`); // In a real app, this would be the actual ID card ID
      return;
    }

    setActiveStep(prev => prev + 1);
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  const handleSubmit = () => {
    setLoading(true);
    // In a real implementation, this would call an API endpoint
    setTimeout(() => {
      setSuccess(true);
      setLoading(false);
      // Navigate to the ID card list after a delay
      setTimeout(() => {
        navigate('/idcards');
      }, 1500);
    }, 1000);
  };

  if (loading && !citizen) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error && !citizen) {
    return (
      <Box sx={{ textAlign: 'center', py: 5 }}>
        <Typography variant="h5" color="error" gutterBottom>
          Error Loading Citizen
        </Typography>
        <Typography variant="body1">{error}</Typography>
        <Button
          variant="contained"
          startIcon={<BackIcon />}
          onClick={() => navigate('/citizens')}
          sx={{ mt: 3 }}
        >
          Back to Citizens
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Button
            variant="outlined"
            startIcon={<BackIcon />}
            onClick={() => navigate('/citizens')}
            sx={{ mr: 2 }}
          >
            Back
          </Button>
          <Typography variant="h4" component="h1">
            Create ID Card
          </Typography>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          ID Card created successfully!
        </Alert>
      )}

      <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
        {steps.map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>

      <Paper sx={{ p: 3, mb: 3 }}>
        {activeStep === 0 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              Citizen Information
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Full Name
                </Typography>
                <Typography variant="body1">
                  {citizen.first_name} {citizen.last_name}
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Gender
                </Typography>
                <Typography variant="body1">
                  {citizen.gender === 'male' ? 'Male' : 'Female'}
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Date of Birth
                </Typography>
                <Typography variant="body1">
                  {new Date(citizen.date_of_birth).toLocaleDateString()}
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Nationality
                </Typography>
                <Typography variant="body1">
                  {citizen.nationality}
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Phone Number
                </Typography>
                <Typography variant="body1">
                  {citizen.phone_number}
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Address
                </Typography>
                <Typography variant="body1">
                  {citizen.address}
                </Typography>
              </Grid>
            </Grid>
            <Divider sx={{ my: 2 }} />
            <FormControlLabel
              control={
                <Checkbox
                  checked={termsAccepted}
                  onChange={(e) => setTermsAccepted(e.target.checked)}
                />
              }
              label="I verify that the above information is correct and up-to-date"
            />
          </Box>
        )}

        {activeStep === 1 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              ID Card Details
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Card Number
                </Typography>
                <Typography variant="body1">
                  {idCardData.card_number}
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Issue Date
                </Typography>
                <Typography variant="body1">
                  {new Date(idCardData.issue_date).toLocaleDateString()}
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Expiry Date
                </Typography>
                <Typography variant="body1">
                  {new Date(idCardData.expiry_date).toLocaleDateString()}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  label="Notes"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="Add any additional notes or comments about this ID card"
                />
              </Grid>
            </Grid>
          </Box>
        )}

        {activeStep === 2 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              Approval
            </Typography>
            <Alert severity="info" sx={{ mb: 2 }}>
              The ID card has been submitted for approval. In a real application, this would require approval from an authorized user.
            </Alert>
            <Typography variant="body1" paragraph>
              For demonstration purposes, you can proceed to the next step to simulate approval.
            </Typography>
          </Box>
        )}

        {activeStep === 3 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              Print ID Card
            </Typography>
            <Alert severity="success" sx={{ mb: 2 }}>
              The ID card has been approved and is ready to be printed.
            </Alert>
            <Typography variant="body1" paragraph>
              Click the "Print ID Card" button to proceed to the printing page.
            </Typography>
          </Box>
        )}
      </Paper>

      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
        <Button
          variant="outlined"
          onClick={handleBack}
          disabled={activeStep === 0 || loading}
        >
          Back
        </Button>
        <Button
          variant="contained"
          onClick={handleNext}
          disabled={loading}
          startIcon={activeStep === 3 ? <PrintIcon /> : activeStep === 2 ? <CheckIcon /> : <SaveIcon />}
        >
          {loading ? <CircularProgress size={24} /> : 
            activeStep === 3 ? 'Print ID Card' : 
            activeStep === 2 ? 'Approve' : 
            activeStep === 1 ? 'Submit for Approval' : 
            'Next'}
        </Button>
      </Box>
    </Box>
  );
};

export default IDCardCreate;
