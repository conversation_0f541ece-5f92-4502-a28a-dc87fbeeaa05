#!/usr/bin/env python3
"""
GoID Biometric Service - Specifically designed for GoID integration
"""

import logging
import ctypes
from ctypes import cdll, c_int, c_uint, c_void_p, POINTER, byref
import base64
import json
import os
from datetime import datetime
from flask import Flask, jsonify, request
from flask_cors import CORS

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

class GoIDFingerprintDevice:
    def __init__(self):
        self.scan_api = None
        self.device_handle = None
        self.connected = False
    
    def initialize(self):
        """Initialize device for GoID"""
        try:
            logger.info("🔧 Initializing Futronic device for GoID...")
            self.scan_api = cdll.LoadLibrary('./ftrScanAPI.dll')
            
            # Setup function signatures
            self.scan_api.ftrScanOpenDevice.restype = c_void_p
            self.scan_api.ftrScanCloseDevice.argtypes = [c_void_p]
            self.scan_api.ftrScanCloseDevice.restype = c_int
            self.scan_api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(ctypes.c_ubyte), POINTER(c_uint)]
            self.scan_api.ftrScanGetFrame.restype = c_int
            
            logger.info("📱 Opening device...")
            self.device_handle = self.scan_api.ftrScanOpenDevice()
            
            if self.device_handle:
                logger.info(f"✅ Device opened for GoID! Handle: {self.device_handle}")
                self.connected = True
                return True
            else:
                logger.warning("❌ Failed to open device - using simulation mode")
                self.connected = False
                return False
                
        except Exception as e:
            logger.error(f"❌ Device initialization error: {e}")
            self.connected = False
            return False
    
    def capture_for_goid(self, thumb_type='left'):
        """Capture fingerprint specifically formatted for GoID"""
        try:
            logger.info(f"📸 GoID requesting {thumb_type} thumb capture...")
            
            if not self.connected:
                # Return simulation data for GoID
                return self._create_simulation_response(thumb_type)
            
            # Real device capture
            buffer_size = 320 * 480
            image_buffer = (ctypes.c_ubyte * buffer_size)()
            frame_size = c_uint(buffer_size)
            
            result = self.scan_api.ftrScanGetFrame(self.device_handle, image_buffer, byref(frame_size))
            actual_size = frame_size.value
            
            logger.info(f"📊 Capture result: code={result}, size={actual_size}")
            
            if actual_size > 0:
                # Process real fingerprint data
                image_bytes = bytes(image_buffer[:actual_size])
                
                # Quick quality analysis
                sample_size = min(500, actual_size)
                sample_bytes = image_bytes[:sample_size]
                non_zero_count = sum(1 for b in sample_bytes if b > 0)
                data_percentage = (non_zero_count / sample_size) * 100
                quality_score = min(100, max(30, int(data_percentage * 2.5)))
                
                # Create GoID-compatible template
                template_data = {
                    'version': '1.0',
                    'thumb_type': thumb_type,
                    'frame_size': actual_size,
                    'quality_score': quality_score,
                    'capture_time': datetime.now().isoformat(),
                    'device_info': {
                        'model': 'Futronic FS88H',
                        'real_device': True,
                        'interface': 'goid_service'
                    }
                }
                
                # Ensure template_data is a proper string for GoID
                template_string = base64.b64encode(json.dumps(template_data).encode()).decode()
                
                logger.info(f"✅ Real fingerprint captured for GoID: Quality {quality_score}%")
                
                return {
                    'success': True,
                    'data': {
                        'thumb_type': thumb_type,
                        'template_data': template_string,  # Ensure this is a string
                        'quality_score': quality_score,
                        'quality_valid': quality_score >= 30,
                        'quality_message': f'Quality score: {quality_score}%',
                        'minutiae_count': max(20, min(60, quality_score // 2)),
                        'capture_time': template_data['capture_time'],
                        'device_info': template_data['device_info']
                    }
                }
            else:
                logger.warning("⚠️ No fingerprint data captured")
                return self._create_simulation_response(thumb_type)
                
        except Exception as e:
            logger.error(f"❌ GoID capture error: {e}")
            return self._create_simulation_response(thumb_type)
    
    def _create_simulation_response(self, thumb_type):
        """Create simulation response for GoID when real device not available"""
        logger.info(f"🎭 Creating simulation response for {thumb_type} thumb")
        
        # Create simulation template
        sim_template = {
            'version': '1.0',
            'thumb_type': thumb_type,
            'simulation': True,
            'quality_score': 75,
            'capture_time': datetime.now().isoformat(),
            'device_info': {
                'model': 'Futronic FS88H (Simulation)',
                'real_device': False,
                'interface': 'goid_simulation'
            }
        }
        
        template_string = base64.b64encode(json.dumps(sim_template).encode()).decode()
        
        return {
            'success': True,
            'data': {
                'thumb_type': thumb_type,
                'template_data': template_string,
                'quality_score': 75,
                'quality_valid': True,
                'quality_message': 'Simulation mode - Quality score: 75%',
                'minutiae_count': 35,
                'capture_time': sim_template['capture_time'],
                'device_info': sim_template['device_info']
            }
        }
    
    def get_status_for_goid(self):
        """Get device status formatted for GoID"""
        return {
            'connected': self.connected,
            'device_connected': self.connected,
            'status': 'connected' if self.connected else 'simulation',
            'model': 'Futronic FS88H',
            'interface': 'goid_service',
            'real_device': self.connected,
            'simulation_mode': not self.connected
        }

# Global device
device = GoIDFingerprintDevice()

@app.before_request
def log_request():
    """Log all requests from GoID"""
    logger.info(f"🌐 GoID Request: {request.method} {request.path}")

@app.route('/api/device/status', methods=['GET'])
def device_status():
    """Device status endpoint for GoID"""
    status = device.get_status_for_goid()
    logger.info(f"📊 Sending status to GoID: {status}")
    
    return jsonify({
        'success': True,
        'connected': status['connected'],
        'device_connected': status['device_connected'],
        'data': status
    })

@app.route('/api/capture/fingerprint', methods=['GET', 'POST'])
def capture_fingerprint():
    """Fingerprint capture endpoint for GoID"""
    if request.method == 'GET':
        thumb_type = request.args.get('thumb_type', 'left')
    else:
        data = request.get_json() or {}
        thumb_type = data.get('thumb_type', 'left')
    
    logger.info(f"👆 GoID requesting {thumb_type} thumb capture")
    
    result = device.capture_for_goid(thumb_type)
    
    if result['success']:
        logger.info(f"✅ Sending {thumb_type} thumb data to GoID")
    else:
        logger.error(f"❌ Failed to capture {thumb_type} thumb for GoID")
    
    return jsonify(result)

@app.route('/api/health', methods=['GET'])
def health():
    """Health check for GoID"""
    return jsonify({
        'status': 'healthy',
        'service': 'GoID Biometric Service',
        'device_connected': device.connected,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/', methods=['GET'])
def index():
    """Simple status page"""
    status = device.get_status_for_goid()
    
    return f"""
    <!DOCTYPE html>
    <html>
    <head><title>GoID Biometric Service</title></head>
    <body style="font-family: Arial; margin: 40px;">
        <h1>🔒 GoID Biometric Service</h1>
        <h3>Device Status:</h3>
        <p><strong>Connected:</strong> {'Yes' if status['connected'] else 'No (Simulation Mode)'}</p>
        <p><strong>Model:</strong> {status['model']}</p>
        <p><strong>Interface:</strong> {status['interface']}</p>
        <p><strong>Real Device:</strong> {'Yes' if status['real_device'] else 'No'}</p>
        
        <h3>API Endpoints:</h3>
        <ul>
            <li><a href="/api/device/status">/api/device/status</a></li>
            <li><a href="/api/health">/api/health</a></li>
        </ul>
        
        <p><em>Service running on port 8001 for GoID integration</em></p>
    </body>
    </html>
    """

if __name__ == '__main__':
    try:
        logger.info("🚀 Starting GoID Biometric Service")
        
        # Initialize device
        device.initialize()
        
        # Start service
        logger.info("🌐 GoID Biometric Service running on port 8001")
        logger.info("🔗 Designed specifically for GoID integration")
        
        app.run(host='0.0.0.0', port=8001, debug=False, threaded=True)
        
    except KeyboardInterrupt:
        logger.info("🛑 GoID Biometric Service stopped")
    except Exception as e:
        logger.error(f"❌ Service error: {e}")
    finally:
        if device.device_handle and device.scan_api:
            try:
                device.scan_api.ftrScanCloseDevice(device.device_handle)
                logger.info("🔒 Device closed")
            except:
                pass
