import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useLocalization } from '../../contexts/LocalizationContext';
import {
  Box,
  Button,
  Card,
  CardContent,
  Typography,
  Grid,
  Divider,
  Chip,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar,
} from '@mui/material';
import {
  ArrowBack as BackIcon,
  Edit as EditIcon,
  CreditCard as CardIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
  Home as HomeIcon,
  Work as WorkIcon,
  Flag as FlagIcon,
  ContactPhone as ContactPhoneIcon,
} from '@mui/icons-material';
import axios from '../../utils/axios';

// Mock data for citizens
const mockCitizens = [
  {
    id: 1,
    first_name: '<PERSON><PERSON>',
    last_name: '<PERSON><PERSON><PERSON>',
    gender: 'male',
    date_of_birth: '1985-05-15',
    phone_number: '+251911234567',
    address: '<PERSON><PERSON>, Addis Ababa',
    nationality: 'Ethiopian',
    occupation: 'Teacher',
    emergency_contact_name: '<PERSON><PERSON>',
    emergency_contact_phone: '+251922345678',
    has_id_card: true,
    created_at: '2023-11-10T12:00:00<PERSON>',
  },
  {
    id: 2,
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    gender: 'female',
    date_of_birth: '1990-08-22',
    phone_number: '+251922345678',
    address: 'Bole, Addis Ababa',
    nationality: 'Ethiopian',
    occupation: 'Doctor',
    emergency_contact_name: '<PERSON> Mohammed',
    emergency_contact_phone: '+251933456789',
    has_id_card: true,
    created_at: '2023-11-11T12:00:00Z',
  },
  {
    id: 3,
    first_name: 'Daniel',
    last_name: 'Tesfaye',
    gender: 'male',
    date_of_birth: '1978-12-10',
    phone_number: '+251933456789',
    address: 'Bole, Addis Ababa',
    nationality: 'Ethiopian',
    occupation: 'Engineer',
    emergency_contact_name: 'Tigist Tesfaye',
    emergency_contact_phone: '+251944567890',
    has_id_card: false,
    created_at: '2023-11-12T12:00:00Z',
  },
];

// Mock data for ID cards
const mockIdCards = [
  {
    id: 1,
    card_number: 'ETH-KBOL-123456',
    citizen_id: 1,
    issue_date: '2023-11-10',
    expiry_date: '2028-11-10',
    status: 'issued',
    created_at: '2023-11-10T12:00:00Z',
  },
  {
    id: 2,
    card_number: 'ETH-KBOL-123457',
    citizen_id: 2,
    issue_date: '2023-11-12',
    expiry_date: '2028-11-12',
    status: 'issued',
    created_at: '2023-11-12T12:00:00Z',
  },
];

const CitizenView = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { t } = useLocalization();
  const [citizen, setCitizen] = useState(null);
  const [idCards, setIdCards] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    // In a real implementation, this would fetch data from the API
    // For now, we'll just use mock data
    const timer = setTimeout(() => {
      const foundCitizen = mockCitizens.find(c => c.id === parseInt(id));
      if (foundCitizen) {
        setCitizen(foundCitizen);
        setIdCards(mockIdCards.filter(card => card.citizen_id === parseInt(id)));
      } else {
        setError('Citizen not found');
      }
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [id]);

  const calculateAge = (dateOfBirth) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  const getStatusChip = (status) => {
    switch (status) {
      case 'draft':
        return <Chip label="Draft" color="default" size="small" />;
      case 'pending_approval':
        return <Chip label="Pending Approval" color="warning" size="small" />;
      case 'approved':
        return <Chip label="Approved" color="info" size="small" />;
      case 'rejected':
        return <Chip label="Rejected" color="error" size="small" />;
      case 'printed':
        return <Chip label="Printed" color="secondary" size="small" />;
      case 'issued':
        return <Chip label="Issued" color="success" size="small" />;
      case 'expired':
        return <Chip label="Expired" color="error" size="small" />;
      case 'revoked':
        return <Chip label="Revoked" color="error" size="small" />;
      default:
        return <Chip label={status} size="small" />;
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ textAlign: 'center', py: 5 }}>
        <Typography variant="h5" color="error" gutterBottom>
          Error Loading Citizen
        </Typography>
        <Typography variant="body1">{error}</Typography>
        <Button
          variant="contained"
          startIcon={<BackIcon />}
          onClick={() => navigate('/citizens')}
          sx={{ mt: 3 }}
        >
          Back to Citizens
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Button
            variant="outlined"
            startIcon={<BackIcon />}
            onClick={() => navigate('/citizens')}
            sx={{ mr: 2 }}
          >
            {t('back', 'Back')}
          </Button>
          <Typography variant="h4" component="h1">
            {t('citizen_details', 'Citizen Details')}
          </Typography>
        </Box>
        <Box>
          <Button
            variant="outlined"
            startIcon={<EditIcon />}
            onClick={() => navigate(`/citizens/${id}`)}
            sx={{ mr: 1 }}
          >
            {t('edit', 'Edit')}
          </Button>
          {!citizen.has_id_card && (
            <Button
              variant="contained"
              startIcon={<CardIcon />}
              onClick={() => navigate(`/citizens/${id}/idcards/create`)}
            >
              {t('create_id_card', 'Create ID Card')}
            </Button>
          )}
        </Box>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar
                sx={{
                  width: 100,
                  height: 100,
                  mx: 'auto',
                  mb: 2,
                  bgcolor: citizen.gender === 'male' ? 'primary.main' : 'secondary.main',
                  fontSize: '2rem',
                }}
              >
                {citizen.first_name.charAt(0)}
              </Avatar>
              <Typography variant="h5" gutterBottom>
                {citizen.first_name} {citizen.last_name}
              </Typography>
              <Typography variant="body1" color="text.secondary" gutterBottom>
                {citizen.gender === 'male' ? 'Male' : 'Female'}, {calculateAge(citizen.date_of_birth)} years old
              </Typography>
              <Divider sx={{ my: 2 }} />
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <PhoneIcon sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2">{citizen.phone_number}</Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <HomeIcon sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2">{citizen.address}</Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <FlagIcon sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2">{citizen.nationality}</Typography>
              </Box>
              {citizen.occupation && (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <WorkIcon sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography variant="body2">{citizen.occupation}</Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={8}>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Personal Information
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Full Name
                  </Typography>
                  <Typography variant="body1">
                    {citizen.first_name} {citizen.last_name}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Gender
                  </Typography>
                  <Typography variant="body1">
                    {citizen.gender === 'male' ? 'Male' : 'Female'}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Date of Birth
                  </Typography>
                  <Typography variant="body1">
                    {new Date(citizen.date_of_birth).toLocaleDateString()} ({calculateAge(citizen.date_of_birth)} years)
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Nationality
                  </Typography>
                  <Typography variant="body1">
                    {citizen.nationality}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Phone Number
                  </Typography>
                  <Typography variant="body1">
                    {citizen.phone_number}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Address
                  </Typography>
                  <Typography variant="body1">
                    {citizen.address}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Occupation
                  </Typography>
                  <Typography variant="body1">
                    {citizen.occupation || '-'}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Registration Date
                  </Typography>
                  <Typography variant="body1">
                    {new Date(citizen.created_at).toLocaleDateString()}
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Emergency Contact
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Contact Name
                  </Typography>
                  <Typography variant="body1">
                    {citizen.emergency_contact_name || '-'}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Contact Phone
                  </Typography>
                  <Typography variant="body1">
                    {citizen.emergency_contact_phone || '-'}
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                ID Cards
              </Typography>
              {idCards.length > 0 ? (
                <TableContainer component={Paper} variant="outlined">
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Card Number</TableCell>
                        <TableCell>Issue Date</TableCell>
                        <TableCell>Expiry Date</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell align="right">Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {idCards.map((card) => (
                        <TableRow key={card.id}>
                          <TableCell>{card.card_number}</TableCell>
                          <TableCell>{card.issue_date ? new Date(card.issue_date).toLocaleDateString() : '-'}</TableCell>
                          <TableCell>{card.expiry_date ? new Date(card.expiry_date).toLocaleDateString() : '-'}</TableCell>
                          <TableCell>{getStatusChip(card.status)}</TableCell>
                          <TableCell align="right">
                            <Button
                              size="small"
                              onClick={() => navigate(`/idcards/${card.id}`)}
                            >
                              View
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Box sx={{ textAlign: 'center', py: 3 }}>
                  <CardIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 1 }} />
                  <Typography variant="body1" gutterBottom>
                    No ID cards found for this citizen
                  </Typography>
                  {!citizen.has_id_card && (
                    <Button
                      variant="contained"
                      startIcon={<CardIcon />}
                      onClick={() => navigate(`/citizens/${id}/idcards/create`)}
                      sx={{ mt: 1 }}
                    >
                      Create ID Card
                    </Button>
                  )}
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default CitizenView;
