#!/usr/bin/env python3
"""
Simple Windows Service Installer for GoID JAR Bridge
Uses NSSM (Non-Sucking Service Manager) for easier service creation
"""

import sys
import os
import subprocess
import urllib.request
import zipfile
from pathlib import Path

def download_nssm():
    """Download NSSM if not present"""
    nssm_dir = Path(__file__).parent / "nssm"
    nssm_exe = nssm_dir / "nssm.exe"
    
    if nssm_exe.exists():
        print("✅ NSSM already available")
        return str(nssm_exe)
    
    print("📥 Downloading NSSM (Non-Sucking Service Manager)...")
    
    try:
        nssm_dir.mkdir(exist_ok=True)
        
        # Download NSSM
        nssm_url = "https://nssm.cc/release/nssm-2.24.zip"
        zip_file = nssm_dir / "nssm.zip"
        
        print("⏬ Downloading NSSM...")
        urllib.request.urlretrieve(nssm_url, zip_file)
        
        print("📦 Extracting NSSM...")
        with zipfile.ZipFile(zip_file, 'r') as zip_ref:
            zip_ref.extractall(nssm_dir)
        
        # Find the correct nssm.exe file (64-bit for 64-bit Python)
        import platform
        is_64bit = platform.machine().endswith('64')

        nssm_arch = "win64" if is_64bit else "win32"
        print(f"🔍 Looking for {nssm_arch} version of NSSM...")

        for root, dirs, files in os.walk(nssm_dir):
            if "nssm.exe" in files and nssm_arch in root:
                nssm_source = Path(root) / "nssm.exe"
                nssm_source.rename(nssm_exe)
                print(f"✅ Using {nssm_arch} NSSM for 64-bit Python")
                break
        
        # Clean up
        zip_file.unlink()
        
        if nssm_exe.exists():
            print("✅ NSSM downloaded successfully")
            return str(nssm_exe)
        else:
            print("❌ Failed to extract NSSM")
            return None
            
    except Exception as e:
        print(f"❌ Failed to download NSSM: {e}")
        return None

def install_service():
    """Install the JAR bridge as a Windows service using NSSM"""
    
    print("🔧 Installing GoID JAR Bridge Service...")
    
    # Get NSSM
    nssm_exe = download_nssm()
    if not nssm_exe:
        print("❌ Cannot install service without NSSM")
        return False
    
    # Service configuration
    service_name = "GoIDJarBridge"
    service_display_name = "GoID JAR Bridge Service"
    service_description = "GoID Real Fingerprint Capture Service"
    
    # Paths
    service_dir = Path(__file__).parent
    python_exe = sys.executable
    jar_bridge_script = service_dir / "working_jar_bridge.py"
    
    if not jar_bridge_script.exists():
        print(f"❌ JAR bridge script not found: {jar_bridge_script}")
        return False
    
    try:
        # Install service
        print(f"📦 Installing service '{service_name}'...")
        
        cmd = [
            nssm_exe, "install", service_name,
            python_exe, str(jar_bridge_script)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"❌ Service installation failed: {result.stderr}")
            return False
        
        # Configure service
        print("⚙️ Configuring service...")
        
        # Set display name
        subprocess.run([nssm_exe, "set", service_name, "DisplayName", service_display_name])
        
        # Set description
        subprocess.run([nssm_exe, "set", service_name, "Description", service_description])
        
        # Set working directory
        subprocess.run([nssm_exe, "set", service_name, "AppDirectory", str(service_dir)])
        
        # Set startup type to automatic
        subprocess.run([nssm_exe, "set", service_name, "Start", "SERVICE_AUTO_START"])
        
        # Set restart behavior
        subprocess.run([nssm_exe, "set", service_name, "AppRestartDelay", "30000"])  # 30 seconds
        subprocess.run([nssm_exe, "set", service_name, "AppThrottle", "10000"])      # 10 seconds
        
        # Set log files
        log_dir = service_dir / "logs"
        log_dir.mkdir(exist_ok=True)
        
        subprocess.run([nssm_exe, "set", service_name, "AppStdout", str(log_dir / "service_output.log")])
        subprocess.run([nssm_exe, "set", service_name, "AppStderr", str(log_dir / "service_error.log")])
        
        print("✅ Service installed successfully!")
        print(f"   Service Name: {service_name}")
        print(f"   Display Name: {service_display_name}")
        print(f"   Script: {jar_bridge_script}")
        print(f"   Logs: {log_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ Service installation failed: {e}")
        return False

def uninstall_service():
    """Uninstall the service"""
    
    nssm_exe = Path(__file__).parent / "nssm" / "nssm.exe"
    if not nssm_exe.exists():
        print("❌ NSSM not found - cannot uninstall service")
        return False
    
    service_name = "GoIDJarBridge"
    
    try:
        print(f"🗑️ Uninstalling service '{service_name}'...")
        
        # Stop service first
        subprocess.run([nssm_exe, "stop", service_name], capture_output=True)
        
        # Remove service
        result = subprocess.run([nssm_exe, "remove", service_name, "confirm"], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Service uninstalled successfully!")
            return True
        else:
            print(f"❌ Service uninstallation failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Service uninstallation failed: {e}")
        return False

def start_service():
    """Start the service"""
    
    try:
        print("▶️ Starting GoID JAR Bridge Service...")
        
        result = subprocess.run(["sc", "start", "GoIDJarBridge"], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Service started successfully!")
            return True
        else:
            print(f"❌ Service start failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Service start failed: {e}")
        return False

def stop_service():
    """Stop the service"""
    
    try:
        print("⏹️ Stopping GoID JAR Bridge Service...")
        
        result = subprocess.run(["sc", "stop", "GoIDJarBridge"], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Service stopped successfully!")
            return True
        else:
            print(f"❌ Service stop failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Service stop failed: {e}")
        return False

def check_service_status():
    """Check service status"""
    
    try:
        result = subprocess.run(["sc", "query", "GoIDJarBridge"], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("📊 Service Status:")
            print(result.stdout)
            return True
        else:
            print("❌ Service not found or not installed")
            return False
            
    except Exception as e:
        print(f"❌ Status check failed: {e}")
        return False

def test_service():
    """Test if the service is responding"""
    
    try:
        print("🧪 Testing service...")
        
        import urllib.request
        response = urllib.request.urlopen("http://localhost:8001/api/device/status", timeout=10)
        
        if response.getcode() == 200:
            print("✅ Service is responding correctly!")
            print("🌐 API endpoint: http://localhost:8001/api/device/status")
            return True
        else:
            print(f"⚠️ Service responded with status: {response.getcode()}")
            return False
            
    except Exception as e:
        print(f"❌ Service test failed: {e}")
        print("💡 Make sure the service is running")
        return False

def main():
    """Main entry point"""
    
    if len(sys.argv) < 2:
        print("GoID JAR Bridge Simple Service Installer")
        print("=" * 45)
        print("Usage:")
        print("  python simple_service_installer.py install    - Install service")
        print("  python simple_service_installer.py uninstall  - Uninstall service")
        print("  python simple_service_installer.py start      - Start service")
        print("  python simple_service_installer.py stop       - Stop service")
        print("  python simple_service_installer.py status     - Check status")
        print("  python simple_service_installer.py test       - Test service")
        print("  python simple_service_installer.py restart    - Restart service")
        return
    
    command = sys.argv[1].lower()
    
    if command == 'install':
        install_service()
    elif command == 'uninstall':
        uninstall_service()
    elif command == 'start':
        start_service()
    elif command == 'stop':
        stop_service()
    elif command == 'status':
        check_service_status()
    elif command == 'test':
        test_service()
    elif command == 'restart':
        stop_service()
        import time
        time.sleep(3)
        start_service()
    else:
        print(f"❌ Unknown command: {command}")

if __name__ == '__main__':
    main()
