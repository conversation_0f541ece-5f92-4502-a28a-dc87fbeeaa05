from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.db import connection
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from ..models.kebele import Kebele
from ..models.subcity import SubCity
from ..models.city import CityAdministration
import json


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def get_tenant_data(request):
    """
    Get tenant data directly from database tables.
    This endpoint bypasses permission restrictions to fetch tenant logos, signatures, and Amharic names
    for ID card generation.
    
    Expected POST data:
    {
        "kebele_tenant_id": 7,
        "subcity_tenant_id": 6,
        "city_tenant_id": 5
    }
    """
    try:
        # Parse request data
        data = request.data
        kebele_tenant_id = data.get('kebele_tenant_id')
        subcity_tenant_id = data.get('subcity_tenant_id')
        city_tenant_id = data.get('city_tenant_id')
        
        print(f"🔍 get_tenant_data called with:")
        print(f"🔍 kebele_tenant_id: {kebele_tenant_id}")
        print(f"🔍 subcity_tenant_id: {subcity_tenant_id}")
        print(f"🔍 city_tenant_id: {city_tenant_id}")
        
        result = {
            'kebele_data': None,
            'subcity_data': None,
            'city_data': None
        }

        # Use Django ORM instead of raw SQL for better reliability
        # Fetch kebele data
        if kebele_tenant_id:
            print(f"🔍 Querying Kebele model for tenant_id: {kebele_tenant_id}")
            try:
                kebele = Kebele.objects.get(tenant_id=kebele_tenant_id)
                result['kebele_data'] = {
                    'id': kebele.id,
                    'name': kebele.name,
                    'name_am': kebele.name_am,
                    'logo': kebele.logo.url if kebele.logo else None,
                    'mayor_signature': kebele.mayor_signature.url if kebele.mayor_signature else None,
                    'pattern_image': kebele.pattern_image.url if kebele.pattern_image else None,
                    'tenant_id': kebele.tenant_id
                }
                print(f"🔍 Found kebele data: {result['kebele_data']}")
            except Kebele.DoesNotExist:
                print(f"⚠️ No kebele found with tenant_id: {kebele_tenant_id}")

        # Fetch subcity data
        if subcity_tenant_id:
            print(f"🔍 Querying SubCity model for tenant_id: {subcity_tenant_id}")
            try:
                subcity = SubCity.objects.get(tenant_id=subcity_tenant_id)
                result['subcity_data'] = {
                    'id': subcity.id,
                    'name': subcity.name,
                    'name_am': subcity.name_am,
                    'logo': subcity.logo.url if subcity.logo else None,
                    'mayor_signature': subcity.mayor_signature.url if subcity.mayor_signature else None,
                    'pattern_image': subcity.pattern_image.url if subcity.pattern_image else None,
                    'tenant_id': subcity.tenant_id
                }
                print(f"🔍 Found subcity data: {result['subcity_data']}")
            except SubCity.DoesNotExist:
                print(f"⚠️ No subcity found with tenant_id: {subcity_tenant_id}")

        # Fetch city data
        if city_tenant_id:
            print(f"🔍 Querying CityAdministration model for tenant_id: {city_tenant_id}")
            try:
                city = CityAdministration.objects.get(tenant_id=city_tenant_id)
                result['city_data'] = {
                    'id': city.id,
                    'city_name': city.city_name,
                    'city_name_am': city.city_name_am,
                    'logo': city.logo.url if city.logo else None,
                    'mayor_signature': city.mayor_signature.url if city.mayor_signature else None,
                    'tenant_id': city.tenant_id
                }
                print(f"🔍 Found city data: {result['city_data']}")
            except CityAdministration.DoesNotExist:
                print(f"⚠️ No city found with tenant_id: {city_tenant_id}")
        
        print(f"🔍 Returning result: {result}")
        return Response(result)

    except Exception as e:
        print(f"❌ Error in get_tenant_data: {str(e)}")
        import traceback
        print(f"❌ Traceback: {traceback.format_exc()}")
        return Response({
            'error': str(e),
            'kebele_data': None,
            'subcity_data': None,
            'city_data': None
        }, status=500)
