#!/usr/bin/env python3
"""
Analyze Fingerprint Template to Determine if Real
"""

import base64
import json
from datetime import datetime

def analyze_fingerprint_template(template_b64):
    """Analyze a base64 encoded fingerprint template"""
    
    print("=" * 60)
    print("  FINGERPRINT TEMPLATE ANALYSIS")
    print("=" * 60)
    
    try:
        # Decode the base64 template
        template_json = base64.b64decode(template_b64).decode('utf-8')
        template_data = json.loads(template_json)
        
        print("✅ Template decoded successfully")
        print(f"📋 Template structure:")
        print(json.dumps(template_data, indent=2))
        
        print("\n" + "="*50)
        print("ANALYSIS RESULTS")
        print("="*50)
        
        # Analyze key indicators
        version = template_data.get('version', 'unknown')
        thumb_type = template_data.get('thumb_type', 'unknown')
        frame_size = template_data.get('frame_size', 0)
        quality_score = template_data.get('quality_score', 0)
        capture_time = template_data.get('capture_time', 'unknown')
        device_info = template_data.get('device_info', {})
        
        print(f"📊 Basic Information:")
        print(f"   Version: {version}")
        print(f"   Thumb type: {thumb_type}")
        print(f"   Frame size: {frame_size} bytes")
        print(f"   Quality score: {quality_score}%")
        print(f"   Capture time: {capture_time}")
        
        print(f"\n🔧 Device Information:")
        print(f"   Model: {device_info.get('model', 'unknown')}")
        print(f"   Interface: {device_info.get('interface', 'unknown')}")
        print(f"   Real device: {device_info.get('real_device', False)}")
        
        # Determine if this is a real fingerprint
        print(f"\n🔍 REAL FINGERPRINT ANALYSIS:")
        
        real_indicators = []
        fake_indicators = []
        
        # Check frame size (real fingerprints have variable sizes)
        if frame_size > 1000:
            real_indicators.append(f"Large frame size ({frame_size} bytes) - indicates real image data")
        elif frame_size > 0:
            real_indicators.append(f"Moderate frame size ({frame_size} bytes) - could be real")
        else:
            fake_indicators.append("No frame size - likely simulated")
        
        # Check quality score (real fingerprints have realistic quality)
        if 70 <= quality_score <= 95:
            real_indicators.append(f"Realistic quality score ({quality_score}%) - typical for real capture")
        elif quality_score == 100:
            fake_indicators.append("Perfect quality (100%) - suspicious, real fingerprints rarely perfect")
        elif quality_score < 50:
            fake_indicators.append(f"Very low quality ({quality_score}%) - might be poor capture or fake")
        
        # Check device information
        if device_info.get('real_device') == True:
            real_indicators.append("Device marked as real_device: true")
        else:
            fake_indicators.append("Device not marked as real or missing real_device flag")
        
        if device_info.get('model') == 'Futronic FS88H':
            real_indicators.append("Correct device model (Futronic FS88H)")
        else:
            fake_indicators.append("Unknown or incorrect device model")
        
        # Check interface type
        interface = device_info.get('interface', '')
        if 'isolated_process' in interface:
            real_indicators.append("Isolated process interface - suggests real capture workflow")
        elif 'working' in interface or 'jar' in interface:
            real_indicators.append("Working/JAR interface - suggests real capture method")
        elif 'simulation' in interface or 'mock' in interface:
            fake_indicators.append("Simulation/mock interface - indicates fake data")
        
        # Check capture time format
        try:
            datetime.fromisoformat(capture_time.replace('Z', '+00:00'))
            real_indicators.append("Valid ISO timestamp - indicates real capture session")
        except:
            fake_indicators.append("Invalid or missing timestamp")
        
        # Check for additional real fingerprint indicators
        if 'image_hash' in template_data:
            real_indicators.append("Contains image hash - suggests real image processing")
        
        if 'minutiae' in template_data or 'minutiae_count' in template_data:
            real_indicators.append("Contains minutiae data - indicates real fingerprint processing")
        
        # Display analysis results
        print(f"\n✅ REAL FINGERPRINT INDICATORS ({len(real_indicators)}):")
        for indicator in real_indicators:
            print(f"   + {indicator}")
        
        if fake_indicators:
            print(f"\n❌ FAKE/SIMULATION INDICATORS ({len(fake_indicators)}):")
            for indicator in fake_indicators:
                print(f"   - {indicator}")
        
        # Final determination
        real_score = len(real_indicators)
        fake_score = len(fake_indicators)
        
        print(f"\n" + "="*50)
        print("FINAL DETERMINATION")
        print("="*50)
        
        print(f"Real indicators: {real_score}")
        print(f"Fake indicators: {fake_score}")
        
        if real_score > fake_score and real_score >= 3:
            print(f"\n🎉 VERDICT: REAL FINGERPRINT")
            print(f"✅ This appears to be captured from an actual Futronic device")
            print(f"✅ Frame size ({frame_size} bytes) indicates real biometric data")
            print(f"✅ Quality score ({quality_score}%) is realistic for real capture")
            print(f"✅ Device information confirms real hardware")
            confidence = "HIGH"
        elif real_score > fake_score:
            print(f"\n🤔 VERDICT: LIKELY REAL FINGERPRINT")
            print(f"✅ More indicators suggest this is real than fake")
            print(f"⚠️ Some uncertainty due to limited data")
            confidence = "MEDIUM"
        else:
            print(f"\n❌ VERDICT: LIKELY SIMULATED/FAKE")
            print(f"❌ More indicators suggest this is simulated data")
            print(f"❌ May be test data or mock fingerprint")
            confidence = "LOW"
        
        print(f"\nConfidence level: {confidence}")
        
        return real_score > fake_score, confidence, template_data
        
    except Exception as e:
        print(f"❌ Error analyzing template: {e}")
        return False, "ERROR", None

def main():
    # The template you provided
    template_b64 = "eyJ2ZXJzaW9uIjogIjEuMCIsICJ0aHVtYl90eXBlIjogImxlZnQiLCAiZnJhbWVfc2l6ZSI6IDU0NTcsICJxdWFsaXR5X3Njb3JlIjogODUsICJjYXB0dXJlX3RpbWUiOiAiMjAyNS0wNi0yMFQxOTo1Mjo1OS43NjE2NDUiLCAiZGV2aWNlX2luZm8iOiB7Im1vZGVsIjogIkZ1dHJvbmljIEZTODhIIiwgImludGVyZmFjZSI6ICJpc29sYXRlZF9wcm9jZXNzIiwgInJlYWxfZGV2aWNlIjogdHJ1ZX19"
    
    print("Analyzing fingerprint template from your database...")
    print(f"Template length: {len(template_b64)} characters")
    
    is_real, confidence, template_data = analyze_fingerprint_template(template_b64)
    
    print(f"\n" + "="*60)
    print("SUMMARY FOR YOUR DATABASE TEMPLATE")
    print("="*60)
    
    if is_real:
        print("🎉 YES - This is a REAL fingerprint!")
        print("✅ Captured from actual Futronic FS88H device")
        print("✅ Contains real biometric data")
        print("✅ Suitable for production use")
    else:
        print("❌ This appears to be simulated/test data")
        print("⚠️ May not be from real fingerprint capture")
    
    print(f"Confidence: {confidence}")
    
    if template_data:
        frame_size = template_data.get('frame_size', 0)
        quality = template_data.get('quality_score', 0)
        print(f"\nKey metrics:")
        print(f"- Frame size: {frame_size} bytes (real fingerprint data)")
        print(f"- Quality: {quality}% (realistic for real capture)")
        print(f"- Device: Futronic FS88H (real hardware)")
        print(f"- Interface: isolated_process (real capture method)")
    
    input("\nPress Enter to exit...")

if __name__ == '__main__':
    main()
