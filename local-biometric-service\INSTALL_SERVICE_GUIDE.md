# GoID JAR Bridge Windows Service Installation Guide

## 🚀 Quick Installation

### Step 1: Open Command Prompt as Administrator
1. Press `Win + X`
2. Select "Command Prompt (Admin)" or "PowerShell (Admin)"
3. Navigate to the service directory:
   ```cmd
   cd "C:\Users\<USER>\Desktop\GoID\local-biometric-service"
   ```

### Step 2: Install Dependencies
```cmd
pip install pywin32
```

### Step 3: Install the Service
```cmd
python install_windows_service.py check
python install_windows_service.py install
python install_windows_service.py start
```

### Step 4: Verify Installation
```cmd
sc query GoIDJarBridge
```

### Step 5: Test the Service
Open browser and go to: http://localhost:8001/api/device/status

## 🛠️ Service Management Commands

### Start Service
```cmd
python install_windows_service.py start
```

### Stop Service
```cmd
python install_windows_service.py stop
```

### Restart Service
```cmd
python install_windows_service.py restart
```

### Check Service Status
```cmd
sc query GoIDJarBridge
```

### Uninstall Service
```cmd
python install_windows_service.py uninstall
```

## 📋 Troubleshooting

### If Installation Fails:
1. **Check Admin Rights:** Make sure you're running as Administrator
2. **Check Python Path:** Ensure Python is in your PATH
3. **Check Dependencies:** Run `pip install pywin32`
4. **Check File Paths:** Make sure you're in the correct directory

### If Service Won't Start:
1. **Check Logs:** Look in `logs/jar_bridge_service.log`
2. **Check JAR Bridge:** Test `python working_jar_bridge.py` manually
3. **Check Port:** Ensure port 8001 is not in use
4. **Check Java:** Ensure Java is installed and accessible

### If Service Crashes:
1. **Check Event Viewer:** Windows Logs > Application
2. **Check Service Logs:** `logs/jar_bridge_service.log`
3. **Restart Service:** `python install_windows_service.py restart`

## 🎯 Service Features

- ✅ **Auto-Start:** Starts automatically with Windows
- ✅ **Auto-Restart:** Restarts automatically if it crashes
- ✅ **Health Monitoring:** Checks service health every 30 seconds
- ✅ **Logging:** Detailed logs in `logs/` directory
- ✅ **Windows Integration:** Appears in Services.msc

## 📁 File Structure

```
local-biometric-service/
├── working_jar_bridge.py          # Main JAR bridge service
├── install_windows_service.py     # Service installer
├── install_service.bat            # Batch installer (fixed)
├── manage_service.bat              # Service manager
├── logs/                           # Service logs
│   └── jar_bridge_service.log
└── fingerPrint/                    # JAR application
    └── GonderFingerPrint.jar
```

## 🔧 Manual Installation (Alternative)

If the automated installer doesn't work, you can install manually:

1. **Open Services.msc**
2. **Create New Service** (requires sc.exe or other tools)
3. **Point to Python script**
4. **Configure auto-start**

## 🎉 Success Indicators

When properly installed, you should see:
- ✅ Service appears in Services.msc as "GoID JAR Bridge Service"
- ✅ Service status shows "Running"
- ✅ http://localhost:8001/api/device/status returns JSON response
- ✅ JAR application can capture fingerprints
- ✅ Service logs show successful startup

## 📞 Support

If you encounter issues:
1. Check the logs in `logs/jar_bridge_service.log`
2. Test the JAR bridge manually: `python working_jar_bridge.py`
3. Verify Java and Python installations
4. Check Windows Event Viewer for service errors
