#!/usr/bin/env python3
"""
Translation Management Tool for GoID System
Provides utilities for managing translations across all supported languages
"""

import os
import re
import json
import argparse
from pathlib import Path
from collections import defaultdict

class TranslationManager:
    def __init__(self, locale_path='/app/locale'):
        self.locale_path = Path(locale_path)
        self.supported_languages = ['en', 'am', 'om', 'ti', 'so', 'aa']
        
    def extract_strings_from_code(self, search_paths=None):
        """Extract translatable strings from Python code"""
        if search_paths is None:
            search_paths = ['/app']
        
        translatable_strings = set()
        
        # Patterns to match translatable strings
        patterns = [
            r'_\([\'"]([^\'"]+)[\'"]\)',  # _('string') or _("string")
            r'gettext\([\'"]([^\'"]+)[\'"]\)',  # gettext('string')
            r'ngettext\([\'"]([^\'"]+)[\'"]',  # ngettext('string', ...)
        ]
        
        for search_path in search_paths:
            for root, dirs, files in os.walk(search_path):
                # Skip certain directories
                if any(skip in root for skip in ['__pycache__', '.git', 'node_modules', 'staticfiles']):
                    continue
                    
                for file in files:
                    if file.endswith('.py'):
                        file_path = os.path.join(root, file)
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                                
                            for pattern in patterns:
                                matches = re.findall(pattern, content)
                                translatable_strings.update(matches)
                        except Exception as e:
                            print(f"Warning: Could not read {file_path}: {e}")
        
        return sorted(translatable_strings)
    
    def parse_po_file(self, po_file_path):
        """Parse a .po file and return translations dictionary"""
        translations = {}
        
        if not os.path.exists(po_file_path):
            return translations
            
        with open(po_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse PO file entries
        po_entries = re.findall(r'msgid\s+"([^"]*)"(?:\n[^m]*)*msgstr\s+"([^"]*)"', content, re.MULTILINE)
        
        for msgid, msgstr in po_entries:
            if msgid:  # Skip empty msgid
                # Unescape quotes
                msgid = msgid.replace('\\"', '"').replace('\\n', '\n')
                msgstr = msgstr.replace('\\"', '"').replace('\\n', '\n')
                translations[msgid] = msgstr
        
        return translations
    
    def get_translation_status(self):
        """Get translation status for all languages"""
        status = {}
        
        # Get all translatable strings from Amharic (most complete)
        am_po_file = self.locale_path / 'am' / 'LC_MESSAGES' / 'django.po'
        all_strings = set()
        
        if am_po_file.exists():
            am_translations = self.parse_po_file(str(am_po_file))
            all_strings = set(am_translations.keys())
        
        # Check each language
        for lang in self.supported_languages:
            if lang == 'en':  # English is the source language
                continue
                
            po_file = self.locale_path / lang / 'LC_MESSAGES' / 'django.po'
            translations = self.parse_po_file(str(po_file))
            
            # Count translated vs untranslated
            translated = sum(1 for v in translations.values() if v.strip())
            total = len(all_strings) if all_strings else len(translations)
            untranslated = total - translated
            
            status[lang] = {
                'total': total,
                'translated': translated,
                'untranslated': untranslated,
                'percentage': (translated / total * 100) if total > 0 else 0,
                'file_exists': po_file.exists()
            }
        
        return status
    
    def find_missing_translations(self, language):
        """Find missing translations for a specific language"""
        # Get all strings from Amharic (reference)
        am_po_file = self.locale_path / 'am' / 'LC_MESSAGES' / 'django.po'
        am_translations = self.parse_po_file(str(am_po_file))
        all_strings = set(am_translations.keys())
        
        # Get translations for target language
        lang_po_file = self.locale_path / language / 'LC_MESSAGES' / 'django.po'
        lang_translations = self.parse_po_file(str(lang_po_file))
        
        # Find missing and empty translations
        missing = []
        for string in all_strings:
            if string not in lang_translations or not lang_translations[string].strip():
                missing.append(string)
        
        return missing
    
    def generate_translation_report(self):
        """Generate a comprehensive translation report"""
        print("🌍 GoID Translation Status Report")
        print("=" * 60)
        
        status = self.get_translation_status()
        
        for lang, info in status.items():
            lang_names = {
                'am': 'Amharic (አማርኛ)',
                'om': 'Oromo (Afaan Oromoo)',
                'ti': 'Tigrinya (ትግርኛ)',
                'so': 'Somali (Soomaali)',
                'aa': 'Afar (Qafar)'
            }
            
            print(f"\n📊 {lang_names.get(lang, lang.upper())}")
            print(f"   File exists: {'✅' if info['file_exists'] else '❌'}")
            print(f"   Total strings: {info['total']}")
            print(f"   Translated: {info['translated']}")
            print(f"   Untranslated: {info['untranslated']}")
            print(f"   Completion: {info['percentage']:.1f}%")
            
            # Progress bar
            bar_length = 30
            filled = int(bar_length * info['percentage'] / 100)
            bar = '█' * filled + '░' * (bar_length - filled)
            print(f"   Progress: [{bar}] {info['percentage']:.1f}%")
        
        print("\n" + "=" * 60)
        
        # Overall statistics
        total_languages = len(status)
        avg_completion = sum(info['percentage'] for info in status.values()) / total_languages if total_languages > 0 else 0
        
        print(f"📈 Overall Statistics:")
        print(f"   Languages: {total_languages}")
        print(f"   Average completion: {avg_completion:.1f}%")
        
        # Recommendations
        print(f"\n💡 Recommendations:")
        for lang, info in status.items():
            if info['percentage'] < 50:
                print(f"   - {lang}: Needs significant translation work ({info['untranslated']} missing)")
            elif info['percentage'] < 90:
                print(f"   - {lang}: Nearly complete, {info['untranslated']} strings remaining")

def main():
    parser = argparse.ArgumentParser(description='GoID Translation Management Tool')
    parser.add_argument('command', choices=['status', 'extract', 'missing'], 
                       help='Command to execute')
    parser.add_argument('--language', '-l', help='Language code for language-specific commands')
    parser.add_argument('--output', '-o', help='Output file for extracted strings')
    
    args = parser.parse_args()
    
    manager = TranslationManager()
    
    if args.command == 'status':
        manager.generate_translation_report()
    
    elif args.command == 'extract':
        print("🔍 Extracting translatable strings from code...")
        strings = manager.extract_strings_from_code()
        print(f"Found {len(strings)} translatable strings")
        
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(strings, f, indent=2, ensure_ascii=False)
            print(f"Saved to {args.output}")
        else:
            for string in strings[:20]:  # Show first 20
                print(f"  - {string}")
            if len(strings) > 20:
                print(f"  ... and {len(strings) - 20} more")
    
    elif args.command == 'missing':
        if not args.language:
            print("❌ Language code required for 'missing' command")
            return
        
        print(f"🔍 Finding missing translations for {args.language}...")
        missing = manager.find_missing_translations(args.language)
        
        if missing:
            print(f"Found {len(missing)} missing translations:")
            for string in missing[:20]:  # Show first 20
                print(f"  - {string}")
            if len(missing) > 20:
                print(f"  ... and {len(missing) - 20} more")
        else:
            print("✅ No missing translations found!")

if __name__ == '__main__':
    main()
