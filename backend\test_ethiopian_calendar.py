#!/usr/bin/env python3
"""
Test script for Ethiopian calendar conversion
Run this to verify the Ethiopian calendar utilities work correctly
"""

import os
import sys
import django
from datetime import date

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from common.ethiopian_calendar import (
    gregorian_to_ethiopian,
    ethiopian_to_gregorian,
    format_ethiopian_date,
    parse_ethiopian_date,
    calculate_ethiopian_age,
    get_current_ethiopian_date,
    is_valid_ethiopian_date
)

def test_date_conversion():
    """Test basic date conversion"""
    print("=== Testing Date Conversion ===")
    
    # Test known dates
    test_dates = [
        date(2024, 1, 1),   # New Year's Day 2024
        date(2024, 9, 11),  # Ethiopian New Year 2017
        date(1988, 5, 1),   # Sample birth date
        date(2000, 1, 1),   # Y2K
    ]
    
    for gregorian_date in test_dates:
        ethiopian_date = gregorian_to_ethiopian(gregorian_date)
        if ethiopian_date:
            formatted = format_ethiopian_date(ethiopian_date, 'DD MMM YYYY', 'en')
            formatted_am = format_ethiopian_date(ethiopian_date, 'DD MMM YYYY', 'am')
            
            # Convert back to verify
            converted_back = ethiopian_to_gregorian(
                ethiopian_date['year'], 
                ethiopian_date['month'], 
                ethiopian_date['day']
            )
            
            print(f"Gregorian: {gregorian_date}")
            print(f"Ethiopian: {formatted} ({formatted_am})")
            print(f"Back to Gregorian: {converted_back}")
            print(f"Match: {gregorian_date == converted_back}")
            print("-" * 50)

def test_age_calculation():
    """Test age calculation"""
    print("=== Testing Age Calculation ===")
    
    # Test birth date: May 1, 1988
    birth_date_gregorian = date(1988, 5, 1)
    birth_date_ethiopian = gregorian_to_ethiopian(birth_date_gregorian)
    
    if birth_date_ethiopian:
        age = calculate_ethiopian_age(birth_date_ethiopian)
        print(f"Birth date (Gregorian): {birth_date_gregorian}")
        print(f"Birth date (Ethiopian): {format_ethiopian_date(birth_date_ethiopian)}")
        print(f"Age in Ethiopian calendar: {age} years")
        print("-" * 50)

def test_current_date():
    """Test current date"""
    print("=== Testing Current Date ===")
    
    current_ethiopian = get_current_ethiopian_date()
    current_gregorian = date.today()
    
    print(f"Current Gregorian date: {current_gregorian}")
    print(f"Current Ethiopian date: {format_ethiopian_date(current_ethiopian, 'DD MMM YYYY', 'en')}")
    print(f"Current Ethiopian date (Amharic): {format_ethiopian_date(current_ethiopian, 'DD MMM YYYY', 'am')}")
    print("-" * 50)

def test_date_parsing():
    """Test date string parsing"""
    print("=== Testing Date Parsing ===")
    
    test_strings = [
        "15/03/2016",  # Ethiopian date
        "01-05-1980",  # Another format
        "25/13/2015",  # Pagume date
    ]
    
    for date_string in test_strings:
        parsed = parse_ethiopian_date(date_string)
        if parsed:
            is_valid = is_valid_ethiopian_date(parsed)
            gregorian_equivalent = None
            if is_valid:
                gregorian_equivalent = ethiopian_to_gregorian(
                    parsed['year'], parsed['month'], parsed['day']
                )
            
            print(f"Date string: {date_string}")
            print(f"Parsed: {parsed}")
            print(f"Valid: {is_valid}")
            print(f"Gregorian equivalent: {gregorian_equivalent}")
            print("-" * 30)

if __name__ == "__main__":
    print("Ethiopian Calendar Test Suite")
    print("=" * 60)
    
    try:
        test_current_date()
        test_date_conversion()
        test_age_calculation()
        test_date_parsing()
        
        print("✅ All tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
