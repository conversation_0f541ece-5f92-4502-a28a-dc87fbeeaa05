@echo off
echo ========================================
echo GoID Database Reset and Migration
echo ========================================

echo.
echo Step 1: Stopping all containers and removing volumes...
docker-compose down -v

echo.
echo Step 2: Removing any orphaned containers...
docker container prune -f

echo.
echo Step 3: Removing unused volumes...
docker volume prune -f

echo.
echo Step 4: Rebuilding backend image (no cache)...
docker-compose build --no-cache backend

echo.
echo Step 5: Starting database first...
docker-compose up -d db

echo.
echo Step 6: Waiting for database to be ready (10 seconds)...
timeout /t 10 /nobreak

echo.
echo Step 7: Starting all services...
docker-compose up -d

echo.
echo Step 8: Waiting for backend to initialize (30 seconds)...
timeout /t 30 /nobreak

echo.
echo Step 9: Checking container status...
docker-compose ps

echo.
echo Step 10: Showing backend logs...
docker-compose logs backend

echo.
echo ========================================
echo Reset and migration complete!
echo ========================================
echo.
echo You can now:
echo 1. Check if backend is running: docker-compose ps
echo 2. View logs: docker-compose logs backend
echo 3. Test API: curl http://localhost:8000
echo 4. Open frontend: http://localhost:3000
echo.
pause
