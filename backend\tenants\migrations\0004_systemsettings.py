# Generated by Django 4.2.7 on 2025-06-07 17:09

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('tenants', '0003_remove_tenant_public_service_portal_enabled_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('primary_color', models.CharField(default='#ff8f00', help_text='Primary color for the entire system (hex format, e.g., #ff8f00)', max_length=7)),
                ('secondary_color', models.CharField(default='#ef6c00', help_text='Secondary color for the entire system (hex format, e.g., #ef6c00)', max_length=7)),
                ('public_services_enabled', models.<PERSON><PERSON><PERSON><PERSON>ield(default=False, help_text='Enable public access to ID card application services system-wide')),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('updated_by', models.ForeignKey(blank=True, help_text='User who last updated these settings', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'System Settings',
                'verbose_name_plural': 'System Settings',
            },
        ),
    ]
