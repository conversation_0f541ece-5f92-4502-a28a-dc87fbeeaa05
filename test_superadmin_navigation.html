<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Superadmin Navigation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .nav-item {
            padding: 8px;
            margin: 4px 0;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 GoID Superadmin Navigation Test</h1>
        
        <div class="test-section info">
            <h3>📋 Test Instructions</h3>
            <p>This page helps you test the superadmin navigation setup:</p>
            <ol>
                <li>Click "Test Login" to authenticate as superadmin</li>
                <li>Check the navigation items that should appear</li>
                <li>Verify permissions in the JWT token</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🔐 Authentication Test</h3>
            <button onclick="testLogin()">Test Superadmin Login</button>
            <button onclick="clearStorage()">Clear Storage</button>
            <div id="auth-result"></div>
        </div>

        <div class="test-section">
            <h3>🧭 Expected Navigation for Superadmin</h3>
            <div class="nav-item">📊 Dashboard</div>
            <div class="nav-item">🏢 Tenants (with Tenants List, Register Tenant)</div>
            <div class="nav-item">👥 System Users (with Users List, Create User)</div>
            <div class="nav-item">⚙️ System Settings</div>
            <div class="nav-item">🎨 Theme Manager</div>
            
            <p><strong>Should NOT see:</strong> Citizens, ID Cards, Clearances, Transfers, Reports</p>
        </div>

        <div class="test-section">
            <h3>🔍 JWT Token Analysis</h3>
            <button onclick="analyzeToken()">Analyze Current Token</button>
            <div id="token-result"></div>
        </div>

        <div class="test-section">
            <h3>🌐 Quick Links</h3>
            <button onclick="window.open('http://localhost:3000', '_blank')">Open Frontend</button>
            <button onclick="window.open('http://localhost:3000/login', '_blank')">Open Login Page</button>
            <button onclick="window.open('http://localhost:8000/admin/', '_blank')">Open Admin Panel</button>
        </div>
    </div>

    <script>
        async function testLogin() {
            const resultDiv = document.getElementById('auth-result');
            resultDiv.innerHTML = '<p>🔄 Testing login...</p>';

            try {
                const response = await fetch('http://localhost:8000/api/auth/token/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    localStorage.setItem('accessToken', data.access);
                    localStorage.setItem('refreshToken', data.refresh);
                    
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Login Successful!</h4>
                            <p>Tokens saved to localStorage</p>
                            <p>You can now test the frontend navigation</p>
                        </div>
                    `;
                } else {
                    const error = await response.text();
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Login Failed</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${error}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Network Error</h4>
                        <p>${error.message}</p>
                        <p>Make sure the backend is running on http://localhost:8000</p>
                    </div>
                `;
            }
        }

        function analyzeToken() {
            const resultDiv = document.getElementById('token-result');
            const token = localStorage.getItem('accessToken');

            if (!token) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ No Token Found</h4>
                        <p>Please login first</p>
                    </div>
                `;
                return;
            }

            try {
                // Decode JWT token (without verification)
                const payload = JSON.parse(atob(token.split('.')[1]));
                
                const keyPermissions = [
                    'manage_tenants',
                    'manage_system_settings', 
                    'manage_all_users',
                    'full_system_access',
                    'view_system_dashboard'
                ];

                const permissionStatus = keyPermissions.map(perm => {
                    const hasPermission = payload.permissions && payload.permissions.includes(perm);
                    return `<li>${hasPermission ? '✅' : '❌'} ${perm}</li>`;
                }).join('');

                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>🔍 Token Analysis</h4>
                        <p><strong>User:</strong> ${payload.email}</p>
                        <p><strong>Role:</strong> ${payload.role}</p>
                        <p><strong>Is Superuser:</strong> ${payload.is_superuser}</p>
                        <p><strong>Total Permissions:</strong> ${payload.permissions ? payload.permissions.length : 0}</p>
                        
                        <h5>Key Navigation Permissions:</h5>
                        <ul>${permissionStatus}</ul>
                        
                        <details>
                            <summary>All Permissions</summary>
                            <pre>${JSON.stringify(payload.permissions, null, 2)}</pre>
                        </details>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Token Decode Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        function clearStorage() {
            localStorage.clear();
            document.getElementById('auth-result').innerHTML = '<p>🧹 Storage cleared</p>';
            document.getElementById('token-result').innerHTML = '';
        }

        // Auto-analyze token if it exists
        window.onload = function() {
            if (localStorage.getItem('accessToken')) {
                analyzeToken();
            }
        };
    </script>
</body>
</html>
