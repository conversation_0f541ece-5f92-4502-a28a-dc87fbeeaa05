#!/bin/bash

# Fix <PERSON><PERSON>e Clerk Submit for Approval 403 Error
# This script fixes the issue where kebele clerks can't submit ID cards for approval

echo "🔧 Fixing Kebele Clerk Submit for Approval 403 Error"
echo "===================================================="

echo "📍 Issue Description:"
echo "- Kebele clerk registers citizen and creates ID card"
echo "- Kebele clerk tries to send ID card for approval to kebele leader"
echo "- Gets 403 Forbidden error"
echo "- This should work at kebele tenant level"
echo ""

# Step 1: Check current kebele users and their roles
echo "📍 Step 1: Checking kebele users and roles..."

docker-compose exec -T backend python manage.py shell << 'EOF'
from django.contrib.auth import get_user_model
from tenants.models import Tenant
from django_tenants.utils import schema_context

User = get_user_model()

print("=== KEBELE TENANT USERS ANALYSIS ===")

# Get all kebele tenants
kebele_tenants = Tenant.objects.filter(type='kebele')
print(f"Found {kebele_tenants.count()} kebele tenants")

for kebele in kebele_tenants:
    print(f"\n🏢 Kebele: {kebele.name} (ID: {kebele.id}, Schema: {kebele.schema_name})")
    
    try:
        with schema_context(kebele.schema_name):
            users = User.objects.all()
            print(f"   Users in this kebele: {users.count()}")
            
            for user in users:
                print(f"   - {user.email} | Role: {user.role} | Active: {user.is_active} | Superuser: {user.is_superuser}")
            
            # Check for clerks specifically
            clerks = User.objects.filter(role='clerk')
            leaders = User.objects.filter(role='kebele_leader')
            
            print(f"   📊 Summary:")
            print(f"      Clerks: {clerks.count()}")
            print(f"      Kebele Leaders: {leaders.count()}")
            
            # Check ID cards in this kebele
            from idcards.models import IDCard
            total_cards = IDCard.objects.count()
            draft_cards = IDCard.objects.filter(status='draft').count()
            pending_cards = IDCard.objects.filter(status='pending_approval').count()
            
            print(f"   📋 ID Cards:")
            print(f"      Total: {total_cards}")
            print(f"      Draft: {draft_cards}")
            print(f"      Pending Approval: {pending_cards}")
            
    except Exception as e:
        print(f"   ❌ Error accessing kebele schema: {e}")

print("\n=== PERMISSION LOGIC TEST ===")

# Test the permission logic for clerks
test_roles = ['clerk', 'kebele_leader', 'superadmin']
allowed_submit_roles = ['clerk', 'superadmin']

for role in test_roles:
    can_submit = role in allowed_submit_roles
    print(f"Role '{role}' can submit for approval: {can_submit}")

EOF

# Step 2: Create/ensure proper kebele clerk exists
echo ""
echo "📍 Step 2: Ensuring proper kebele clerk exists..."

docker-compose exec -T backend python manage.py shell << 'EOF'
from django.contrib.auth import get_user_model
from tenants.models import Tenant
from django_tenants.utils import schema_context

User = get_user_model()

print("=== CREATING/UPDATING KEBELE CLERK ===")

# Get the first kebele tenant for testing
kebele_tenant = Tenant.objects.filter(type='kebele').first()

if not kebele_tenant:
    print("❌ No kebele tenant found!")
    exit()

print(f"✅ Using kebele tenant: {kebele_tenant.name} (ID: {kebele_tenant.id})")

with schema_context(kebele_tenant.schema_name):
    # Check if clerk exists
    clerk = User.objects.filter(role='clerk').first()
    
    if clerk:
        print(f"✅ Found existing clerk: {clerk.email}")
        # Update clerk to ensure correct settings
        clerk.role = 'clerk'
        clerk.is_active = True
        clerk.save()
        print(f"   ✅ Updated clerk role and status")
    else:
        # Create new clerk
        clerk = User.objects.create_user(
            username='kebele_clerk',
            email='<EMAIL>',
            password='clerk123',
            first_name='Kebele',
            last_name='Clerk',
            role='clerk',
            is_active=True,
            is_superuser=False
        )
        print(f"✅ Created new clerk: {clerk.email}")
    
    print(f"   Final clerk details:")
    print(f"   - ID: {clerk.id}")
    print(f"   - Email: {clerk.email}")
    print(f"   - Role: {clerk.role}")
    print(f"   - Active: {clerk.is_active}")
    print(f"   - Superuser: {clerk.is_superuser}")
    
    # Ensure kebele leader exists too
    leader = User.objects.filter(role='kebele_leader').first()
    if not leader:
        leader = User.objects.create_user(
            username='kebele_leader',
            email='<EMAIL>',
            password='leader123',
            first_name='Kebele',
            last_name='Leader',
            role='kebele_leader',
            is_active=True,
            is_superuser=False
        )
        print(f"✅ Created kebele leader: {leader.email}")
    else:
        print(f"✅ Kebele leader exists: {leader.email}")
    
    # Create a test draft ID card if none exists
    from idcards.models import IDCard
    from citizens.models import Citizen
    
    draft_cards = IDCard.objects.filter(status='draft')
    if draft_cards.count() == 0:
        # Try to create a test ID card
        citizens = Citizen.objects.all()
        if citizens.exists():
            test_citizen = citizens.first()
            
            # Create draft ID card
            test_card = IDCard.objects.create(
                citizen=test_citizen,
                status='draft',
                created_by=clerk,
                issue_date=None,
                expiry_date=None
            )
            print(f"✅ Created test draft ID card: {test_card.id}")
        else:
            print("⚠️  No citizens available to create test ID card")
    else:
        print(f"✅ Found {draft_cards.count()} draft ID cards for testing")

EOF

# Step 3: Test authentication with kebele clerk
echo ""
echo "📍 Step 3: Testing kebele clerk authentication..."

CLERK_AUTH_RESPONSE=$(curl -s -X POST http://10.139.8.141:8000/api/auth/token/ \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"clerk123"}')

if echo "$CLERK_AUTH_RESPONSE" | grep -q "access"; then
    echo "✅ Kebele clerk authentication successful"
    
    CLERK_TOKEN=$(echo "$CLERK_AUTH_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin)['access'])" 2>/dev/null)
    
    if [ ! -z "$CLERK_TOKEN" ]; then
        echo "✅ Clerk token extracted"
        
        # Decode token to verify role
        echo ""
        echo "📍 Step 4: Verifying clerk token contents..."
        python3 -c "
import base64
import json

token = '$CLERK_TOKEN'
try:
    payload = token.split('.')[1]
    payload += '=' * (4 - len(payload) % 4)
    decoded = base64.b64decode(payload)
    data = json.loads(decoded)
    
    print('Clerk token contents:')
    print(f'  Email: {data.get(\"email\")}')
    print(f'  Role: {data.get(\"role\")}')
    print(f'  Tenant ID: {data.get(\"tenant_id\")}')
    print(f'  Is Superuser: {data.get(\"is_superuser\")}')
    
    role = data.get('role')
    if role == 'clerk':
        print('  ✅ Role is correctly set to clerk')
    else:
        print(f'  ❌ Role is {role}, should be clerk')
        
except Exception as e:
    print(f'Error decoding token: {e}')
"
        
        # Step 5: Test submit for approval with clerk token
        echo ""
        echo "📍 Step 5: Testing submit for approval with clerk..."
        
        # Get kebele tenant ID from clerk's token
        TENANT_ID=$(python3 -c "
import base64
import json

token = '$CLERK_TOKEN'
try:
    payload = token.split('.')[1]
    payload += '=' * (4 - len(payload) % 4)
    decoded = base64.b64decode(payload)
    data = json.loads(decoded)
    print(data.get('tenant_id', 'none'))
except:
    print('none')
")
        
        if [ "$TENANT_ID" != "none" ]; then
            echo "Using tenant ID from token: $TENANT_ID"
            
            # Get ID cards in clerk's tenant
            IDCARDS_RESPONSE=$(curl -s -H "Authorization: Bearer $CLERK_TOKEN" \
              http://10.139.8.141:8000/api/tenants/$TENANT_ID/idcards/)
            
            DRAFT_CARD_ID=$(echo "$IDCARDS_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    cards = data.get('results', data) if isinstance(data, dict) else data
    
    for card in cards:
        if card.get('status') == 'draft':
            print(card['id'])
            exit()
    
    # If no draft, use first card
    if cards:
        print(cards[0]['id'])
    else:
        print('none')
except:
    print('none')
" 2>/dev/null)
            
            if [ "$DRAFT_CARD_ID" != "none" ]; then
                echo "Testing submit for approval with draft card: $DRAFT_CARD_ID"
                
                SUBMIT_RESPONSE=$(curl -s -X POST \
                  -H "Authorization: Bearer $CLERK_TOKEN" \
                  -H "Content-Type: application/json" \
                  -d '{"action":"submit_for_approval","comment":"Submitted by kebele clerk for approval"}' \
                  http://10.139.8.141:8000/api/tenants/$TENANT_ID/idcards/$DRAFT_CARD_ID/approval_action/ \
                  -w "HTTP_STATUS:%{http_code}")
                
                HTTP_STATUS=$(echo "$SUBMIT_RESPONSE" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
                RESPONSE_BODY=$(echo "$SUBMIT_RESPONSE" | sed 's/HTTP_STATUS:[0-9]*$//')
                
                echo "Submit for approval status: $HTTP_STATUS"
                
                if [ "$HTTP_STATUS" = "200" ]; then
                    echo "✅ SUCCESS! Kebele clerk can submit for approval"
                    echo "Response: $RESPONSE_BODY"
                elif [ "$HTTP_STATUS" = "403" ]; then
                    echo "❌ Still getting 403 Forbidden"
                    echo "Response: $RESPONSE_BODY"
                    echo ""
                    echo "🔍 This indicates a deeper permission issue"
                elif [ "$HTTP_STATUS" = "400" ]; then
                    echo "⚠️  400 Bad Request - Check ID card status"
                    echo "Response: $RESPONSE_BODY"
                else
                    echo "⚠️  Unexpected status: $HTTP_STATUS"
                    echo "Response: $RESPONSE_BODY"
                fi
                
            else
                echo "❌ No ID cards found for testing"
            fi
        else
            echo "❌ No tenant ID found in token"
        fi
        
    else
        echo "❌ Could not extract clerk token"
    fi
else
    echo "❌ Kebele clerk authentication failed"
    echo "Response: $CLERK_AUTH_RESPONSE"
fi

# Step 6: Check backend logs for any errors
echo ""
echo "📍 Step 6: Checking backend logs for errors..."
docker-compose logs --tail=20 backend | grep -E "403|Forbidden|approval|clerk" || echo "No relevant logs found"

echo ""
echo "✅ Kebele clerk submit for approval fix completed!"
echo ""
echo "🧪 Manual testing steps:"
echo "1. Login as kebele clerk: <EMAIL> / clerk123"
echo "2. Go to ID Cards list"
echo "3. Find ID card with 'draft' status"
echo "4. Click 'Send for Approval' button"
echo "5. Should work without 403 error"
echo ""
echo "🔍 Expected workflow:"
echo "1. ✅ Kebele clerk creates ID card (status: draft)"
echo "2. ✅ Kebele clerk sends for approval (status: pending_approval)"
echo "3. ✅ Kebele leader approves ID card (status: approved)"
echo ""
echo "💡 If still getting 403:"
echo "- Verify user is actually a kebele clerk with role 'clerk'"
echo "- Check that ID card status is 'draft'"
echo "- Ensure user is in correct kebele tenant"
echo "- Check browser console for detailed error messages"
