#!/usr/bin/env python3
"""
Analyze the real fingerprint templates from user to understand duplicate detection issue.
"""

import base64
import json

def analyze_fingerprint_templates():
    """Analyze the provided fingerprint templates"""
    
    # User's fingerprint templates
    template1 = "eyJ2ZXJzaW9uIjogIjIuMCIsICJ0aHVtYl90eXBlIjogImxlZnQiLCAiaW1hZ2Vfd2lkdGgiOiAxNjAsICJpbWFnZV9oZWlnaHQiOiA0ODAsICJpbWFnZV9kcGkiOiA1MDAsICJxdWFsaXR5X3Njb3JlIjogNzAsICJtaW51dGlhZV9jb3VudCI6IDAsICJjYXB0dXJlX3RpbWUiOiAiMjAyNS0wNi0yNFQxNjoxNDoxOC44MzkzODEiLCAiZGV2aWNlX2luZm8iOiB7Im1vZGVsIjogIkZ1dHJvbmljIEZTODhIIiwgImludGVyZmFjZSI6ICJqYXJfYnJpZGdlIiwgInJlYWxfZGV2aWNlIjogdHJ1ZSwgInNka192ZXJzaW9uIjogIkdvbmRlckZpbmdlclByaW50LmphciJ9LCAicHJvY2Vzc2luZ19tZXRob2QiOiAiZ29uZGVyX2phcl9hcHBsaWNhdGlvbiIsICJoYXNfdGVtcGxhdGUiOiB0cnVlLCAiaGFzX2ltYWdlIjogdHJ1ZSwgInJlYWxfY2FwdHVyZSI6IHRydWUsICJxdWFsaXR5X3ZlcmlmaWVkIjogdHJ1ZSwgImphcl9zZXNzaW9uIjogInNlc3Npb25fMTc1MDc3MDg1OCIsICJjYXB0dXJlX3RpbWVzdGFtcCI6IDE3NTA3NzA4NTgsICJmcmFtZV9zaXplX2FjdHVhbCI6IDAsICJleHRyYWN0aW9uX3N1Y2Nlc3MiOiBmYWxzZX0="
    
    template2 = "eyJ2ZXJzaW9uIjogIjIuMCIsICJ0aHVtYl90eXBlIjogImxlZnQiLCAiaW1hZ2Vfd2lkdGgiOiAxNjAsICJpbWFnZV9oZWlnaHQiOiA0ODAsICJpbWFnZV9kcGkiOiA1MDAsICJxdWFsaXR5X3Njb3JlIjogNzAsICJtaW51dGlhZV9jb3VudCI6IDAsICJjYXB0dXJlX3RpbWUiOiAiMjAyNS0wNi0yM1QyMzozNDowMS43NTAzMzgiLCAiZGV2aWNlX2luZm8iOiB7Im1vZGVsIjogIkZ1dHJvbmljIEZTODhIIiwgImludGVyZmFjZSI6ICJqYXJfYnJpZGdlIiwgInJlYWxfZGV2aWNlIjogdHJ1ZSwgInNka192ZXJzaW9uIjogIkdvbmRlckZpbmdlclByaW50LmphciJ9LCAicHJvY2Vzc2luZ19tZXRob2QiOiAiZ29uZGVyX2phcl9hcHBsaWNhdGlvbiIsICJoYXNfdGVtcGxhdGUiOiB0cnVlLCAiaGFzX2ltYWdlIjogdHJ1ZSwgInJlYWxfY2FwdHVyZSI6IHRydWUsICJxdWFsaXR5X3ZlcmlmaWVkIjogdHJ1ZSwgImphcl9zZXNzaW9uIjogInNlc3Npb25fMTc1MDcxMDg0MSIsICJjYXB0dXJlX3RpbWVzdGFtcCI6IDE3NTA3MTA4NDEsICJmcmFtZV9zaXplX2FjdHVhbCI6IDAsICJleHRyYWN0aW9uX3N1Y2Nlc3MiOiBmYWxzZX0="
    
    print("🔍 ANALYZING REAL FINGERPRINT TEMPLATES")
    print("=" * 60)
    
    # Decode and analyze template 1
    print("\n📋 TEMPLATE 1 ANALYSIS:")
    print("-" * 40)
    try:
        decoded1 = json.loads(base64.b64decode(template1))
        print(f"Version: {decoded1.get('version')}")
        print(f"Thumb Type: {decoded1.get('thumb_type')}")
        print(f"Quality Score: {decoded1.get('quality_score')}")
        print(f"Minutiae Count: {decoded1.get('minutiae_count')}")
        print(f"Capture Time: {decoded1.get('capture_time')}")
        print(f"Has Template: {decoded1.get('has_template')}")
        print(f"Has Image: {decoded1.get('has_image')}")
        print(f"Real Capture: {decoded1.get('real_capture')}")
        print(f"Extraction Success: {decoded1.get('extraction_success')}")
        print(f"Frame Size Actual: {decoded1.get('frame_size_actual')}")
        
        # Check for minutiae points
        minutiae_points = decoded1.get('minutiae_points', [])
        print(f"Minutiae Points Available: {len(minutiae_points)}")
        
        if minutiae_points:
            print("First few minutiae points:")
            for i, point in enumerate(minutiae_points[:3]):
                print(f"  Point {i+1}: {point}")
        else:
            print("❌ NO MINUTIAE POINTS FOUND!")
            
    except Exception as e:
        print(f"❌ Error decoding template 1: {e}")
    
    # Decode and analyze template 2
    print("\n📋 TEMPLATE 2 ANALYSIS:")
    print("-" * 40)
    try:
        decoded2 = json.loads(base64.b64decode(template2))
        print(f"Version: {decoded2.get('version')}")
        print(f"Thumb Type: {decoded2.get('thumb_type')}")
        print(f"Quality Score: {decoded2.get('quality_score')}")
        print(f"Minutiae Count: {decoded2.get('minutiae_count')}")
        print(f"Capture Time: {decoded2.get('capture_time')}")
        print(f"Has Template: {decoded2.get('has_template')}")
        print(f"Has Image: {decoded2.get('has_image')}")
        print(f"Real Capture: {decoded2.get('real_capture')}")
        print(f"Extraction Success: {decoded2.get('extraction_success')}")
        print(f"Frame Size Actual: {decoded2.get('frame_size_actual')}")
        
        # Check for minutiae points
        minutiae_points = decoded2.get('minutiae_points', [])
        print(f"Minutiae Points Available: {len(minutiae_points)}")
        
        if minutiae_points:
            print("First few minutiae points:")
            for i, point in enumerate(minutiae_points[:3]):
                print(f"  Point {i+1}: {point}")
        else:
            print("❌ NO MINUTIAE POINTS FOUND!")
            
    except Exception as e:
        print(f"❌ Error decoding template 2: {e}")
    
    # Compare templates
    print("\n🔍 TEMPLATE COMPARISON:")
    print("-" * 40)
    
    try:
        # Check if templates are identical
        if template1 == template2:
            print("✅ Templates are IDENTICAL (exact same base64)")
        else:
            print("❌ Templates are DIFFERENT")
            
        # Check key differences
        if decoded1.get('capture_time') != decoded2.get('capture_time'):
            print(f"📅 Different capture times:")
            print(f"  Template 1: {decoded1.get('capture_time')}")
            print(f"  Template 2: {decoded2.get('capture_time')}")
            
        if decoded1.get('jar_session') != decoded2.get('jar_session'):
            print(f"🔧 Different JAR sessions:")
            print(f"  Template 1: {decoded1.get('jar_session')}")
            print(f"  Template 2: {decoded2.get('jar_session')}")
            
        if decoded1.get('capture_timestamp') != decoded2.get('capture_timestamp'):
            print(f"⏰ Different timestamps:")
            print(f"  Template 1: {decoded1.get('capture_timestamp')}")
            print(f"  Template 2: {decoded2.get('capture_timestamp')}")
            
    except Exception as e:
        print(f"❌ Error comparing templates: {e}")
    
    # Root cause analysis
    print("\n🚨 ROOT CAUSE ANALYSIS:")
    print("-" * 40)
    
    try:
        # Check extraction success
        extraction1 = decoded1.get('extraction_success', False)
        extraction2 = decoded2.get('extraction_success', False)
        
        if not extraction1 and not extraction2:
            print("❌ CRITICAL ISSUE: Both templates have extraction_success = False")
            print("❌ This means no actual fingerprint data was extracted!")
            print("❌ Templates only contain metadata, not biometric data")
            
        minutiae1 = decoded1.get('minutiae_count', 0)
        minutiae2 = decoded2.get('minutiae_count', 0)
        
        if minutiae1 == 0 and minutiae2 == 0:
            print("❌ CRITICAL ISSUE: Both templates have 0 minutiae points")
            print("❌ Without minutiae points, duplicate detection cannot work")
            
        frame_size1 = decoded1.get('frame_size_actual', 0)
        frame_size2 = decoded2.get('frame_size_actual', 0)
        
        if frame_size1 == 0 and frame_size2 == 0:
            print("❌ CRITICAL ISSUE: Both templates have 0 frame size")
            print("❌ This indicates no actual fingerprint image data was captured")
            
        print("\n💡 SOLUTION NEEDED:")
        print("1. Fix JAR bridge to properly extract minutiae points")
        print("2. Ensure extraction_success = True")
        print("3. Generate actual biometric data, not just metadata")
        print("4. Capture frame data with actual fingerprint information")
        
    except Exception as e:
        print(f"❌ Error in root cause analysis: {e}")

if __name__ == "__main__":
    analyze_fingerprint_templates()
