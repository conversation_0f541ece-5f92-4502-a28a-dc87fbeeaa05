#!/usr/bin/env python3
"""
Test script to verify the transfer endpoint is working correctly.
"""

import requests
import json
import base64

def decode_jwt_token(token):
    """Decode JWT token without verification for debugging."""
    try:
        # Split the token
        header, payload, signature = token.split('.')
        
        # Add padding if needed
        payload += '=' * (4 - len(payload) % 4)
        
        # Decode the payload
        decoded_bytes = base64.urlsafe_b64decode(payload)
        decoded_payload = json.loads(decoded_bytes)
        
        return decoded_payload
    except Exception as e:
        print(f"Error decoding token: {e}")
        return None

def test_transfer_endpoint():
    """Test the transfer available-kebeles endpoint."""
    
    base_url = "http://************:8000"
    
    print("🧪 Testing Transfer Endpoint")
    print("=" * 40)
    
    # Test with kebele user login
    print("\n📍 Testing with kebele user login")
    print("-" * 30)
    
    # Login as kebele user
    login_data = {
        "username": "<EMAIL>",
        "password": "password123"
    }
    
    try:
        response = requests.post(f"{base_url}/api/auth/token/", json=login_data)
        print(f"Login response status: {response.status_code}")
        
        if response.status_code == 200:
            auth_data = response.json()
            access_token = auth_data.get('access')
            print("✅ Authentication successful")
            
            # Decode token to see user info
            token_data = decode_jwt_token(access_token)
            if token_data:
                print(f"Token user info:")
                print(f"  User ID: {token_data.get('user_id')}")
                print(f"  Email: {token_data.get('email')}")
                print(f"  Role: {token_data.get('role')}")
                print(f"  Tenant ID: {token_data.get('tenant_id')}")
                print(f"  Tenant Name: {token_data.get('tenant_name')}")
                print(f"  Tenant Type: {token_data.get('tenant_type')}")
            
            # Test the transfer endpoint
            print(f"\n🔍 Testing transfer available-kebeles endpoint...")
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            
            transfer_response = requests.get(
                f"{base_url}/api/tenants/transfers/available-kebeles/",
                headers=headers
            )
            
            print(f"Transfer endpoint status: {transfer_response.status_code}")
            print(f"Transfer endpoint response: {transfer_response.text}")
            
            if transfer_response.status_code == 200:
                transfer_data = transfer_response.json()
                print("✅ Transfer endpoint working!")
                print(f"Current kebele: {transfer_data.get('current_kebele', {}).get('name')}")
                print(f"Available kebeles: {len(transfer_data.get('available_kebeles', []))}")
                
                for kebele in transfer_data.get('available_kebeles', [])[:3]:  # Show first 3
                    print(f"  - {kebele.get('name')} (ID: {kebele.get('id')})")
            else:
                print("❌ Transfer endpoint failed")
                
        else:
            print(f"❌ Authentication failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_transfer_endpoint()
