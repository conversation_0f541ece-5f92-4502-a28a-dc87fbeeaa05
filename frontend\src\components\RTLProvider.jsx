/**
 * RTL Provider Component
 * Provides RTL (Right-to-Left) language support and utilities
 */

import React, { createContext, useContext, useEffect, useMemo } from 'react';
import { useLocalization } from '../contexts/LocalizationContext';

const RTLContext = createContext();

export const useRTL = () => {
  const context = useContext(RTLContext);
  if (!context) {
    throw new Error('useRTL must be used within an RTLProvider');
  }
  return context;
};

export const RTLProvider = ({ children }) => {
  const { currentLanguage, getDirection, isRTL, getDirectionHelpers } = useLocalization();

  // Get direction helpers for current language
  const directionHelpers = useMemo(() => {
    return getDirectionHelpers ? getDirectionHelpers() : {
      isRTL: false,
      direction: 'ltr',
      textAlign: 'left',
      marginStart: 'marginLeft',
      marginEnd: 'marginRight',
      paddingStart: 'paddingLeft',
      paddingEnd: 'paddingRight',
      borderStart: 'borderLeft',
      borderEnd: 'borderRight',
      start: 'left',
      end: 'right'
    };
  }, [currentLanguage, getDirectionHelpers]);

  // Update document direction when language changes
  useEffect(() => {
    const direction = getDirection ? getDirection() : 'ltr';
    document.documentElement.dir = direction;
    document.documentElement.setAttribute('data-direction', direction);
    
    // Add RTL class to body for CSS targeting
    if (directionHelpers.isRTL) {
      document.body.classList.add('rtl');
      document.body.classList.remove('ltr');
    } else {
      document.body.classList.add('ltr');
      document.body.classList.remove('rtl');
    }
  }, [currentLanguage, getDirection, directionHelpers.isRTL]);

  // RTL-aware style helpers
  const getStyle = (styles) => {
    if (!directionHelpers.isRTL) {
      return styles;
    }

    const rtlStyles = { ...styles };

    // Convert logical properties to physical properties for RTL
    const conversions = {
      marginLeft: 'marginRight',
      marginRight: 'marginLeft',
      paddingLeft: 'paddingRight',
      paddingRight: 'paddingLeft',
      borderLeft: 'borderRight',
      borderRight: 'borderLeft',
      borderLeftWidth: 'borderRightWidth',
      borderRightWidth: 'borderLeftWidth',
      borderLeftColor: 'borderRightColor',
      borderRightColor: 'borderLeftColor',
      borderLeftStyle: 'borderRightStyle',
      borderRightStyle: 'borderLeftStyle',
      left: 'right',
      right: 'left',
      textAlign: styles.textAlign === 'left' ? 'right' : styles.textAlign === 'right' ? 'left' : styles.textAlign
    };

    Object.entries(conversions).forEach(([ltr, rtl]) => {
      if (styles[ltr] !== undefined) {
        rtlStyles[rtl] = styles[ltr];
        delete rtlStyles[ltr];
      }
    });

    return rtlStyles;
  };

  // RTL-aware className helper
  const getClassName = (baseClass, rtlClass = null) => {
    if (!directionHelpers.isRTL) {
      return baseClass;
    }
    return rtlClass ? `${baseClass} ${rtlClass}` : `${baseClass} ${baseClass}-rtl`;
  };

  // Direction-aware positioning
  const getPosition = (position) => {
    if (!directionHelpers.isRTL) {
      return position;
    }

    const rtlPosition = { ...position };
    
    if (position.left !== undefined) {
      rtlPosition.right = position.left;
      delete rtlPosition.left;
    }
    if (position.right !== undefined) {
      rtlPosition.left = position.right;
      delete rtlPosition.right;
    }

    return rtlPosition;
  };

  // Transform values for RTL (useful for animations, transforms)
  const getTransform = (transform) => {
    if (!directionHelpers.isRTL || !transform) {
      return transform;
    }

    // Flip translateX values
    return transform.replace(/translateX\(([^)]+)\)/g, (match, value) => {
      if (value.includes('-')) {
        return `translateX(${value.replace('-', '')})`;
      } else {
        return `translateX(-${value})`;
      }
    });
  };

  // Icon direction helper (for arrows, etc.)
  const getIconDirection = (iconName) => {
    if (!directionHelpers.isRTL) {
      return iconName;
    }

    const iconMappings = {
      'arrow-left': 'arrow-right',
      'arrow-right': 'arrow-left',
      'chevron-left': 'chevron-right',
      'chevron-right': 'chevron-left',
      'angle-left': 'angle-right',
      'angle-right': 'angle-left',
      'caret-left': 'caret-right',
      'caret-right': 'caret-left'
    };

    return iconMappings[iconName] || iconName;
  };

  const value = {
    // Direction info
    ...directionHelpers,
    
    // Helper functions
    getStyle,
    getClassName,
    getPosition,
    getTransform,
    getIconDirection,
    
    // Convenience properties
    directionClass: directionHelpers.isRTL ? 'rtl' : 'ltr',
    oppositeDirection: directionHelpers.isRTL ? 'ltr' : 'rtl'
  };

  return (
    <RTLContext.Provider value={value}>
      {children}
    </RTLContext.Provider>
  );
};

/**
 * Higher-order component to add RTL support to any component
 */
export const withRTL = (WrappedComponent) => {
  const WithRTL = (props) => (
    <RTLProvider>
      <WrappedComponent {...props} />
    </RTLProvider>
  );

  WithRTL.displayName = `withRTL(${WrappedComponent.displayName || WrappedComponent.name})`;
  return WithRTL;
};

/**
 * RTL-aware Box component
 */
export const RTLBox = ({ children, style = {}, className = '', rtlClassName = '', ...props }) => {
  const { getStyle, getClassName } = useRTL();

  return (
    <div
      className={getClassName(className, rtlClassName)}
      style={getStyle(style)}
      {...props}
    >
      {children}
    </div>
  );
};

/**
 * RTL-aware Text component
 */
export const RTLText = ({ children, align = 'start', style = {}, ...props }) => {
  const { textAlign, getStyle } = useRTL();

  const textStyle = {
    ...style,
    textAlign: align === 'start' ? textAlign : align === 'end' ? (textAlign === 'left' ? 'right' : 'left') : align
  };

  return (
    <span style={getStyle(textStyle)} {...props}>
      {children}
    </span>
  );
};

export default RTLProvider;
