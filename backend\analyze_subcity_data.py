#!/usr/bin/env python3
"""
Analyze and fix SubCity data inconsistency.
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
sys.path.append('/app')
django.setup()

from django.db import connection

def analyze_and_fix():
    """Analyze SubCity data and fix sequence."""
    cursor = connection.cursor()
    
    print('=== Complete SubCity Analysis ===')
    
    # Check all SubCity records directly from table
    cursor.execute('SELECT id, name, tenant_id, city_id FROM tenants_subcity ORDER BY id;')
    all_rows = cursor.fetchall()
    print(f'All SubCity records in table: {len(all_rows)}')
    for row in all_rows:
        print(f'  ID: {row[0]}, Name: {row[1]}, Tenant: {row[2]}, City: {row[3]}')
    
    # Check sequence
    cursor.execute('SELECT last_value, is_called FROM tenants_subcity_id_seq;')
    seq_info = cursor.fetchone()
    print(f'Current sequence: last_value={seq_info[0]}, is_called={seq_info[1]}')
    
    # Find the actual max ID
    cursor.execute('SELECT MAX(id) FROM tenants_subcity;')
    max_id = cursor.fetchone()[0]
    print(f'Actual max ID: {max_id}')
    
    # Fix sequence to max_id + 1
    if max_id:
        next_id = max_id + 1
        cursor.execute(f"SELECT setval('tenants_subcity_id_seq', {next_id});")
        print(f'✅ Fixed sequence to: {next_id}')
        
        # Verify
        cursor.execute('SELECT last_value, is_called FROM tenants_subcity_id_seq;')
        new_seq = cursor.fetchone()
        print(f'New sequence: last_value={new_seq[0]}, is_called={new_seq[1]}')
    else:
        print('No records found, resetting sequence to 1')
        cursor.execute("SELECT setval('tenants_subcity_id_seq', 1, false);")
    
    print('\n🎉 Analysis and fix complete!')
    return True

if __name__ == "__main__":
    analyze_and_fix()
