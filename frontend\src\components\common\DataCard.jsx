import { useState } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  useTheme, 
  alpha,
  CircularProgress,
  LinearProgress
} from '@mui/material';
import { TrendingUp, TrendingDown, TrendingFlat } from '@mui/icons-material';

/**
 * A data card component for displaying statistics with trend indicators
 * 
 * @param {Object} props
 * @param {string} props.title - Card title
 * @param {string|number} props.value - Main value to display
 * @param {string} props.subtitle - Subtitle or description
 * @param {React.ReactNode} props.icon - Icon to display
 * @param {number} props.trend - Trend value (positive, negative, or zero)
 * @param {string} props.trendLabel - Label for the trend
 * @param {string} props.color - Primary color for the card (primary, secondary, success, error, warning, info)
 * @param {boolean} props.loading - Whether the card is in loading state
 * @param {number} props.progress - Progress value (0-100) for displaying a progress indicator
 * @param {Object} props.sx - Additional styles to apply to the card
 */
const DataCard = ({
  title,
  value,
  subtitle,
  icon,
  trend,
  trendLabel,
  color = 'primary',
  loading = false,
  progress,
  sx = {},
  ...rest
}) => {
  const theme = useTheme();
  const [hovered, setHovered] = useState(false);
  
  // Get the color from theme
  const getColor = () => {
    if (!color) return theme.palette.primary.main;
    return theme.palette[color]?.main || theme.palette.primary.main;
  };
  
  const cardColor = getColor();
  
  // Determine trend icon and color
  const getTrendIcon = () => {
    if (trend > 0) return <TrendingUp fontSize="small" />;
    if (trend < 0) return <TrendingDown fontSize="small" />;
    return <TrendingFlat fontSize="small" />;
  };
  
  const getTrendColor = () => {
    if (trend > 0) return theme.palette.success.main;
    if (trend < 0) return theme.palette.error.main;
    return theme.palette.text.secondary;
  };
  
  return (
    <Paper
      elevation={hovered ? 8 : 2}
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
      sx={{
        position: 'relative',
        p: 3,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        borderRadius: 3,
        overflow: 'hidden',
        transition: 'all 0.3s ease',
        transform: hovered ? 'translateY(-4px)' : 'none',
        border: `1px solid ${theme.palette.mode === 'light' 
          ? 'rgba(0, 0, 0, 0.05)' 
          : 'rgba(255, 255, 255, 0.05)'
        }`,
        '&:hover': {
          borderColor: alpha(cardColor, 0.2),
        },
        ...sx
      }}
      {...rest}
    >
      {/* Background decoration */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          right: 0,
          width: 100,
          height: 100,
          background: `radial-gradient(circle at top right, ${alpha(cardColor, 0.15)}, transparent 70%)`,
          zIndex: 0,
        }}
      />
      
      {/* Card content */}
      <Box sx={{ position: 'relative', zIndex: 1, display: 'flex', flexDirection: 'column', height: '100%' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Typography variant="subtitle1" color="text.secondary" fontWeight={500}>
            {title}
          </Typography>
          
          {icon && (
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: 40,
                height: 40,
                borderRadius: '50%',
                backgroundColor: alpha(cardColor, 0.1),
                color: cardColor,
              }}
            >
              {icon}
            </Box>
          )}
        </Box>
        
        <Box sx={{ my: 1, flexGrow: 1 }}>
          {loading ? (
            <Box sx={{ display: 'flex', alignItems: 'center', height: '100%' }}>
              <CircularProgress size={24} color={color} />
            </Box>
          ) : (
            <Typography variant="h4" component="div" fontWeight="bold">
              {value}
            </Typography>
          )}
          
          {subtitle && (
            <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
              {subtitle}
            </Typography>
          )}
        </Box>
        
        {progress !== undefined && (
          <Box sx={{ mt: 2, mb: 1 }}>
            <LinearProgress 
              variant="determinate" 
              value={progress} 
              color={color}
              sx={{ 
                height: 6, 
                borderRadius: 3,
                backgroundColor: alpha(cardColor, 0.1),
              }} 
            />
          </Box>
        )}
        
        {trend !== undefined && (
          <Box 
            sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              mt: 2,
              color: getTrendColor(),
              fontSize: '0.875rem',
            }}
          >
            {getTrendIcon()}
            <Typography 
              variant="body2" 
              component="span" 
              sx={{ 
                ml: 0.5, 
                fontWeight: 500,
                color: getTrendColor(),
              }}
            >
              {trend > 0 ? '+' : ''}{trend}%
            </Typography>
            {trendLabel && (
              <Typography 
                variant="body2" 
                component="span" 
                color="text.secondary" 
                sx={{ ml: 0.5 }}
              >
                {trendLabel}
              </Typography>
            )}
          </Box>
        )}
      </Box>
    </Paper>
  );
};

export default DataCard;
