#!/usr/bin/env python3
"""
Detailed debugging for kebele user management issues.
"""

import requests
import json

def debug_kebele_issues():
    """Debug both the filtering and users endpoint issues."""
    
    base_url = "http://10.139.8.141:8000"
    
    print("🔍 Detailed Kebele Issues Debugging")
    print("=" * 50)
    
    # Step 1: Get authentication token
    print("📍 Step 1: Getting authentication token...")
    
    try:
        auth_response = requests.post(
            f"{base_url}/api/auth/token/",
            json={"email": "<EMAIL>", "password": "admin123"},
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if auth_response.status_code == 200:
            auth_data = auth_response.json()
            access_token = auth_data.get('access')
            print("✅ Authentication successful")
            
            # Decode token to see user info
            import base64
            try:
                header, payload, signature = access_token.split('.')
                payload += '=' * (4 - len(payload) % 4)
                decoded_payload = base64.urlsafe_b64decode(payload)
                token_data = json.loads(decoded_payload)
                
                print(f"Token user info:")
                print(f"  User ID: {token_data.get('user_id')}")
                print(f"  Email: {token_data.get('email')}")
                print(f"  Role: {token_data.get('role')}")
                print(f"  Is Superuser: {token_data.get('is_superuser')}")
                
            except Exception as e:
                print(f"Could not decode token: {e}")
                
        else:
            print(f"❌ Authentication failed: {auth_response.text}")
            return
            
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    # Step 2: Test tenant filtering in detail
    print(f"\n📍 Step 2: Testing tenant filtering in detail...")
    
    # Test all tenants
    print("2a. Testing all tenants: /api/tenants/")
    try:
        response = requests.get(f"{base_url}/api/tenants/", headers=headers, timeout=10)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            all_tenants = data if isinstance(data, list) else data.get('results', [])
            print(f"Total tenants: {len(all_tenants)}")
            
            # Show tenant types
            tenant_types = {}
            for tenant in all_tenants:
                t_type = tenant.get('type', 'unknown')
                tenant_types[t_type] = tenant_types.get(t_type, 0) + 1
            print(f"Tenant types: {tenant_types}")
            
        else:
            print(f"Failed: {response.text}")
            
    except Exception as e:
        print(f"Error: {e}")
    
    # Test kebele tenants only
    print("\n2b. Testing kebele tenants: /api/tenants/?type=kebele")
    try:
        response = requests.get(f"{base_url}/api/tenants/?type=kebele", headers=headers, timeout=10)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            kebele_tenants = data if isinstance(data, list) else data.get('results', [])
            print(f"Kebele tenants: {len(kebele_tenants)}")
            
            if kebele_tenants:
                print("Kebele details:")
                for tenant in kebele_tenants[:3]:  # Show first 3
                    print(f"  - ID: {tenant.get('id')}, Name: {tenant.get('name')}, Parent: {tenant.get('parent')}")
                    
                # Store first kebele for testing
                first_kebele_id = kebele_tenants[0]['id']
                print(f"Will use kebele ID {first_kebele_id} for user testing")
            else:
                print("No kebele tenants found")
                first_kebele_id = None
                
        else:
            print(f"Failed: {response.text}")
            first_kebele_id = None
            
    except Exception as e:
        print(f"Error: {e}")
        first_kebele_id = None
    
    # Test with parent filter
    print("\n2c. Testing parent filtering: /api/tenants/?type=kebele&parent=1")
    try:
        response = requests.get(f"{base_url}/api/tenants/?type=kebele&parent=1", headers=headers, timeout=10)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            filtered_tenants = data if isinstance(data, list) else data.get('results', [])
            print(f"Parent-filtered kebeles: {len(filtered_tenants)}")
            
            if filtered_tenants:
                print("Filtered kebele details:")
                for tenant in filtered_tenants:
                    print(f"  - ID: {tenant.get('id')}, Name: {tenant.get('name')}, Parent: {tenant.get('parent')}")
            else:
                print("No kebeles found with parent=1")
                
        else:
            print(f"Failed: {response.text}")
            
    except Exception as e:
        print(f"Error: {e}")
    
    # Step 3: Test users endpoint in detail
    print(f"\n📍 Step 3: Testing users endpoint in detail...")
    
    if first_kebele_id:
        # Test different URL patterns
        user_urls = [
            f"/api/tenants/{first_kebele_id}/users/",
            f"/api/tenants/{first_kebele_id}/users",
            f"/api/tenants/users/{first_kebele_id}/",
            f"/api/users/?tenant={first_kebele_id}",
        ]
        
        for url in user_urls:
            print(f"\n3a. Testing: {url}")
            try:
                response = requests.get(f"{base_url}{url}", headers=headers, timeout=10)
                print(f"Status: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        users = data if isinstance(data, list) else data.get('results', [])
                        print(f"✅ SUCCESS: Found {len(users)} users")
                        if users:
                            print(f"First user: {users[0]}")
                        break  # Found working URL
                    except:
                        print(f"✅ SUCCESS: Non-JSON response (might be HTML)")
                        break
                elif response.status_code == 404:
                    print(f"❌ 404 Not Found")
                elif response.status_code == 500:
                    print(f"❌ 500 Server Error: {response.text[:200]}")
                else:
                    print(f"❌ Status {response.status_code}: {response.text[:200]}")
                    
            except Exception as e:
                print(f"❌ Error: {e}")
    else:
        print("No kebele ID available for testing users endpoint")
    
    # Step 4: Test URL resolution in Django
    print(f"\n📍 Step 4: Testing URL resolution...")
    
    test_urls = [
        "/api/tenants/",
        "/api/tenants/1/",
        "/api/tenants/1/users/",
        "/api/tenants/1/create_user/",
    ]
    
    for url in test_urls:
        try:
            response = requests.get(f"{base_url}{url}", headers=headers, timeout=5)
            print(f"{url}: {response.status_code}")
        except:
            print(f"{url}: Connection failed")

def check_django_urls():
    """Check Django URL configuration directly."""
    
    print(f"\n📍 Step 5: Checking Django URL configuration...")
    
    import subprocess
    
    django_script = '''
from django.urls import resolve, reverse
from django.conf import settings

print("=== URL RESOLUTION TEST ===")

test_urls = [
    "/api/tenants/",
    "/api/tenants/1/",
    "/api/tenants/1/users/",
    "/api/tenants/1/create_user/",
    "/api/tenants/1/create_admin/",
]

for url in test_urls:
    try:
        match = resolve(url)
        print(f"✅ {url} -> {match.func.__name__} ({match.view_name})")
    except Exception as e:
        print(f"❌ {url} -> {e}")

print("\\n=== REVERSE URL TEST ===")

try:
    tenant_list_url = reverse('tenant-list')
    print(f"tenant-list: {tenant_list_url}")
except Exception as e:
    print(f"tenant-list: {e}")

try:
    tenant_users_url = reverse('tenant-users', kwargs={'pk': 1})
    print(f"tenant-users: {tenant_users_url}")
except Exception as e:
    print(f"tenant-users: {e}")

print("\\n=== AVAILABLE URLS ===")
from django.urls import get_resolver
resolver = get_resolver()
url_patterns = []

def extract_patterns(patterns, prefix=""):
    for pattern in patterns:
        if hasattr(pattern, 'url_patterns'):
            extract_patterns(pattern.url_patterns, prefix + str(pattern.pattern))
        else:
            url_patterns.append(prefix + str(pattern.pattern))

extract_patterns(resolver.url_patterns)

tenant_urls = [url for url in url_patterns if 'tenant' in url.lower()]
print("Tenant-related URLs:")
for url in tenant_urls[:10]:  # Show first 10
    print(f"  {url}")
'''
    
    try:
        result = subprocess.run(
            ["docker-compose", "exec", "-T", "backend", "python", "manage.py", "shell", "-c", django_script],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        print("Django URL check output:")
        print(result.stdout)
        
        if result.stderr:
            print("Errors:")
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ Django URL check failed: {e}")

if __name__ == "__main__":
    debug_kebele_issues()
    check_django_urls()
    
    print("\n" + "=" * 50)
    print("🔧 Diagnosis Summary:")
    print("")
    print("Issue 1: Filtering Problem")
    print("- Check if parent filtering is working in step 2c")
    print("- If all kebeles shown: TenantViewSet.get_queryset() issue")
    print("")
    print("Issue 2: Users Endpoint 404")
    print("- Check which URL pattern works in step 3a")
    print("- Check URL resolution in step 4")
    print("- Check Django URL configuration in step 5")
    print("")
    print("💡 Next steps based on results:")
    print("1. If no URL works: URL pattern registration issue")
    print("2. If URL resolves but 404: ViewSet action issue")
    print("3. If 500 error: Schema or permission issue")
    print("4. If filtering not working: get_queryset logic issue")
