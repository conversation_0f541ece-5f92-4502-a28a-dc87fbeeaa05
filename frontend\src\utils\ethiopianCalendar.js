/**
 * Ethiopian Calendar Utilities
 * 
 * The Ethiopian calendar has 13 months:
 * - 12 months of 30 days each
 * - 1 month (Pagume) of 5 days (6 in leap years)
 * - Ethiopian year starts on September 11 (or 12 in leap years) of Gregorian calendar
 * - Ethiopian calendar is approximately 7-8 years behind Gregorian calendar
 */

// Ethiopian month names
export const ETHIOPIAN_MONTHS = [
  'መስከረም', // Meskerem (September)
  'ጥቅምት',   // Tikimt (October)
  'ኅዳር',    // Hidar (November)
  'ታኅሳስ',   // <PERSON><PERSON><PERSON> (December)
  'ጥር',     // Tir (January)
  'የካቲት',   // Yekatit (February)
  'መጋቢት',   // Megabit (March)
  'ሚያዝያ',   // Miazia (April)
  'ግንቦት',   // G<PERSON><PERSON> (May)
  'ሰኔ',     // <PERSON><PERSON> (June)
  'ሐምሌ',    // <PERSON><PERSON> (July)
  'ነሐሴ',    // Nehase (August)
  'ጳጉሜ'     // Pagume (September - 5/6 days)
];

// Ethiopian month names in English
export const ETHIOPIAN_MONTHS_EN = [
  'Meskerem', 'Tikimt', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Tir', '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>gum<PERSON>'
];

/**
 * Check if an Ethiopian year is a leap year
 * @param {number} ethiopianYear - Ethiopian year
 * @returns {boolean} - True if leap year
 */
export function isEthiopianLeapYear(ethiopianYear) {
  return (ethiopianYear % 4) === 3;
}

/**
 * Get the number of days in an Ethiopian month
 * @param {number} month - Month (1-13)
 * @param {number} year - Ethiopian year
 * @returns {number} - Number of days in the month
 */
export function getDaysInEthiopianMonth(month, year) {
  if (month >= 1 && month <= 12) {
    return 30;
  } else if (month === 13) {
    return isEthiopianLeapYear(year) ? 6 : 5;
  }
  throw new Error('Invalid Ethiopian month');
}

/**
 * Convert Gregorian date to Ethiopian date using accurate algorithm
 * @param {Date} gregorianDate - Gregorian date
 * @returns {Object} - {year, month, day}
 */
export function gregorianToEthiopian(gregorianDate) {
  if (!gregorianDate || !(gregorianDate instanceof Date)) {
    return null;
  }

  const year = gregorianDate.getFullYear();
  let month = gregorianDate.getMonth() + 1; // JavaScript months are 0-based
  const day = gregorianDate.getDate();

  // Convert to Julian Day Number first for accuracy
  let adjustedYear = year;
  let adjustedMonth = month;

  if (month <= 2) {
    adjustedYear -= 1;
    adjustedMonth += 12;
  }

  const a = Math.floor(adjustedYear / 100);
  const b = 2 - a + Math.floor(a / 4);

  const jdn = Math.floor(365.25 * (adjustedYear + 4716)) +
              Math.floor(30.6001 * (adjustedMonth + 1)) +
              day + b - 1524;

  // Convert Julian Day Number to Ethiopian date
  // Ethiopian epoch JDN is 1724221 (September 11, 8 AD Gregorian)
  const ethiopianJdn = jdn - 1724220;

  if (ethiopianJdn < 1) {
    return null;
  }

  // Calculate Ethiopian year
  let ethiopianYear = Math.floor((ethiopianJdn - 1) / 365) + 1;

  // Adjust for leap years
  let leapAdjustments = Math.floor((ethiopianYear - 1) / 4);
  ethiopianYear = Math.floor((ethiopianJdn - 1 - leapAdjustments) / 365) + 1;

  // Calculate day of Ethiopian year
  leapAdjustments = Math.floor((ethiopianYear - 1) / 4);
  const dayOfYear = ethiopianJdn - ((ethiopianYear - 1) * 365 + leapAdjustments);

  // Calculate Ethiopian month and day
  let ethiopianMonth, ethiopianDay;

  if (dayOfYear <= 360) { // First 12 months (30 days each)
    ethiopianMonth = Math.floor((dayOfYear - 1) / 30) + 1;
    ethiopianDay = ((dayOfYear - 1) % 30) + 1;
  } else { // Pagume (13th month)
    ethiopianMonth = 13;
    ethiopianDay = dayOfYear - 360;
  }

  return {
    year: ethiopianYear,
    month: ethiopianMonth,
    day: ethiopianDay
  };
}

/**
 * Convert Ethiopian date to Gregorian date using accurate algorithm
 * @param {number} year - Ethiopian year
 * @param {number} month - Ethiopian month (1-13)
 * @param {number} day - Ethiopian day
 * @returns {Date} - Gregorian date
 */
export function ethiopianToGregorian(year, month, day) {
  if (!year || !month || !day) {
    return null;
  }

  // Validate Ethiopian date
  if (month < 1 || month > 13 || day < 1) {
    throw new Error('Invalid Ethiopian date');
  }

  if (month <= 12 && day > 30) {
    throw new Error('Invalid day for Ethiopian month');
  }

  if (month === 13 && day > getDaysInEthiopianMonth(13, year)) {
    throw new Error('Invalid day for Pagume');
  }

  // Calculate day of Ethiopian year
  let dayOfYear;
  if (month <= 12) {
    dayOfYear = (month - 1) * 30 + day;
  } else {
    dayOfYear = 360 + day;
  }

  // Calculate leap year adjustments
  const leapAdjustments = Math.floor((year - 1) / 4);

  // Calculate Ethiopian Julian Day Number
  const ethiopianJdn = (year - 1) * 365 + leapAdjustments + dayOfYear + 1724220;

  // Convert Julian Day Number to Gregorian date
  const a = ethiopianJdn + 32044;
  const b = Math.floor((4 * a + 3) / 146097);
  const c = a - Math.floor((146097 * b) / 4);
  const d = Math.floor((4 * c + 3) / 1461);
  const e = c - Math.floor((1461 * d) / 4);
  const m = Math.floor((5 * e + 2) / 153);

  const gregorianDay = e - Math.floor((153 * m + 2) / 5) + 1;
  const gregorianMonth = m + 3 - 12 * Math.floor(m / 10);
  const gregorianYear = 100 * b + d - 4800 + Math.floor(m / 10);

  try {
    return new Date(gregorianYear, gregorianMonth - 1, gregorianDay); // JavaScript months are 0-based
  } catch (error) {
    return null;
  }
}

/**
 * Format Ethiopian date as string
 * @param {Object} ethiopianDate - {year, month, day}
 * @param {string} format - Format string ('DD/MM/YYYY', 'DD MMM YYYY', etc.)
 * @param {string} language - 'am' for Amharic, 'en' for English
 * @returns {string} - Formatted date string
 */
export function formatEthiopianDate(ethiopianDate, format = 'DD/MM/YYYY', language = 'am') {
  if (!ethiopianDate || !ethiopianDate.year || !ethiopianDate.month || !ethiopianDate.day) {
    return '';
  }

  const { year, month, day } = ethiopianDate;
  const monthNames = language === 'am' ? ETHIOPIAN_MONTHS : ETHIOPIAN_MONTHS_EN;
  
  const paddedDay = day.toString().padStart(2, '0');
  const paddedMonth = month.toString().padStart(2, '0');
  
  switch (format) {
    case 'DD/MM/YYYY':
      return `${paddedDay}/${paddedMonth}/${year}`;
    case 'DD-MM-YYYY':
      return `${paddedDay}-${paddedMonth}-${year}`;
    case 'DD MMM YYYY':
      return `${paddedDay} ${monthNames[month - 1]} ${year}`;
    case 'MMM DD, YYYY':
      return `${monthNames[month - 1]} ${paddedDay}, ${year}`;
    case 'YYYY-MM-DD':
      return `${year}-${paddedMonth}-${paddedDay}`;
    default:
      return `${paddedDay}/${paddedMonth}/${year}`;
  }
}

/**
 * Parse Ethiopian date string
 * @param {string} dateString - Date string in format 'DD/MM/YYYY' or 'DD-MM-YYYY'
 * @returns {Object} - {year, month, day}
 */
export function parseEthiopianDate(dateString) {
  if (!dateString) {
    return null;
  }

  const separators = ['/', '-', '.'];
  let parts = null;
  
  for (const separator of separators) {
    if (dateString.includes(separator)) {
      parts = dateString.split(separator);
      break;
    }
  }
  
  if (!parts || parts.length !== 3) {
    return null;
  }
  
  const day = parseInt(parts[0], 10);
  const month = parseInt(parts[1], 10);
  const year = parseInt(parts[2], 10);
  
  if (isNaN(day) || isNaN(month) || isNaN(year)) {
    return null;
  }
  
  return { year, month, day };
}

/**
 * Get current Ethiopian date
 * @returns {Object} - {year, month, day}
 */
export function getCurrentEthiopianDate() {
  return gregorianToEthiopian(new Date());
}

/**
 * Calculate age in Ethiopian calendar
 * @param {Object} birthDate - Ethiopian birth date {year, month, day}
 * @param {Object} currentDate - Current Ethiopian date (optional)
 * @returns {number} - Age in years
 */
export function calculateEthiopianAge(birthDate, currentDate = null) {
  if (!birthDate) {
    return null;
  }
  
  const current = currentDate || getCurrentEthiopianDate();
  
  let age = current.year - birthDate.year;
  
  // Adjust if birthday hasn't occurred this year
  if (current.month < birthDate.month || 
      (current.month === birthDate.month && current.day < birthDate.day)) {
    age--;
  }
  
  return age;
}

/**
 * Add days to Ethiopian date
 * @param {Object} ethiopianDate - {year, month, day}
 * @param {number} days - Number of days to add
 * @returns {Object} - New Ethiopian date
 */
export function addDaysToEthiopianDate(ethiopianDate, days) {
  if (!ethiopianDate || days === 0) {
    return ethiopianDate;
  }

  // Convert to Gregorian, add days, convert back
  const gregorianDate = ethiopianToGregorian(ethiopianDate.year, ethiopianDate.month, ethiopianDate.day);
  gregorianDate.setDate(gregorianDate.getDate() + days);
  return gregorianToEthiopian(gregorianDate);
}

/**
 * Add months to Ethiopian date
 * @param {Object} ethiopianDate - {year, month, day}
 * @param {number} months - Number of months to add
 * @returns {Object} - New Ethiopian date
 */
export function addMonthsToEthiopianDate(ethiopianDate, months) {
  if (!ethiopianDate || months === 0) {
    return ethiopianDate;
  }

  let { year, month, day } = ethiopianDate;

  month += months;

  // Handle year overflow
  while (month > 13) {
    month -= 13;
    year++;
  }

  while (month < 1) {
    month += 13;
    year--;
  }

  // Adjust day if it exceeds the month's days
  const maxDays = getDaysInEthiopianMonth(month, year);
  if (day > maxDays) {
    day = maxDays;
  }

  return { year, month, day };
}

/**
 * Add years to Ethiopian date
 * @param {Object} ethiopianDate - {year, month, day}
 * @param {number} years - Number of years to add
 * @returns {Object} - New Ethiopian date
 */
export function addYearsToEthiopianDate(ethiopianDate, years) {
  if (!ethiopianDate || years === 0) {
    return ethiopianDate;
  }

  let { year, month, day } = ethiopianDate;
  year += years;

  // Adjust day if it's February 29th and new year is not leap
  if (month === 13 && day === 6 && !isEthiopianLeapYear(year)) {
    day = 5;
  }

  return { year, month, day };
}

/**
 * Check if Ethiopian date is valid
 * @param {Object} ethiopianDate - {year, month, day}
 * @returns {boolean} - True if valid
 */
export function isValidEthiopianDate(ethiopianDate) {
  if (!ethiopianDate || !ethiopianDate.year || !ethiopianDate.month || !ethiopianDate.day) {
    return false;
  }

  const { year, month, day } = ethiopianDate;

  if (year < 1 || month < 1 || month > 13 || day < 1) {
    return false;
  }

  const maxDays = getDaysInEthiopianMonth(month, year);
  return day <= maxDays;
}

export default {
  ETHIOPIAN_MONTHS,
  ETHIOPIAN_MONTHS_EN,
  isEthiopianLeapYear,
  getDaysInEthiopianMonth,
  gregorianToEthiopian,
  ethiopianToGregorian,
  formatEthiopianDate,
  parseEthiopianDate,
  getCurrentEthiopianDate,
  calculateEthiopianAge,
  addDaysToEthiopianDate,
  addMonthsToEthiopianDate,
  addYearsToEthiopianDate,
  isValidEthiopianDate
};
