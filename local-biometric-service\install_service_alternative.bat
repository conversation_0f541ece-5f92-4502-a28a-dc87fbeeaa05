@echo off
echo ========================================
echo   GoID JAR Bridge Service (Alternative)
echo ========================================
echo.

REM Change to script directory
cd /d "%~dp0"

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Running as Administrator
) else (
    echo ❌ Administrator privileges required
    echo 💡 Right-click and "Run as Administrator"
    pause
    exit /b 1
)

echo Current directory: %CD%
echo Python executable: %~dp0python_path.txt will be created

REM Find Python executable
where python > python_path.txt 2>nul
if %errorlevel% == 0 (
    set /p PYTHON_PATH=<python_path.txt
    echo ✅ Found Python: !PYTHON_PATH!
) else (
    echo ❌ Python not found in PATH
    pause
    exit /b 1
)

REM Test JAR bridge manually first
echo.
echo 🧪 Testing JAR bridge manually...
echo Press Ctrl+C to stop the test after a few seconds
timeout /t 3 /nobreak >nul
start /wait cmd /c "!PYTHON_PATH! working_jar_bridge.py & timeout /t 10 & taskkill /f /im python.exe 2>nul"

echo.
echo 📦 Creating Windows service using SC command...

REM Create service using sc.exe
sc create GoIDJarBridge ^
    binPath= "\"!PYTHON_PATH!\" \"%~dp0working_jar_bridge.py\"" ^
    DisplayName= "GoID JAR Bridge Service" ^
    start= auto ^
    depend= ""

if %errorlevel% == 0 (
    echo ✅ Service created successfully
) else (
    echo ❌ Service creation failed
    echo 💡 Service might already exist
)

REM Set service description
sc description GoIDJarBridge "GoID Real Fingerprint Capture Service using Futronic JAR Bridge"

REM Configure service recovery
sc failure GoIDJarBridge reset= 86400 actions= restart/30000/restart/60000/restart/120000

echo.
echo ▶️ Starting service...
sc start GoIDJarBridge

echo.
echo 📊 Service status:
sc query GoIDJarBridge

echo.
echo 🧪 Testing service...
timeout /t 5 /nobreak >nul
curl -s http://localhost:8001/api/device/status
if %errorlevel% == 0 (
    echo.
    echo ✅ Service is responding!
) else (
    echo.
    echo ❌ Service is not responding
    echo 💡 Check Windows Event Viewer for errors
)

echo.
echo ========================================
echo   Installation Complete!
echo ========================================
echo.
echo Service Management:
echo   Start:   sc start GoIDJarBridge
echo   Stop:    sc stop GoIDJarBridge
echo   Status:  sc query GoIDJarBridge
echo   Delete:  sc delete GoIDJarBridge
echo.
echo Test URL: http://localhost:8001/api/device/status
echo.

REM Clean up
del python_path.txt 2>nul

pause
