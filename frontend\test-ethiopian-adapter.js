// Simple test to verify EthiopianDateAdapter works
import { EthiopianDateAdapter } from './src/utils/EthiopianDateAdapter.js';

// Test the adapter
const adapter = new EthiopianDateAdapter();
const testDate = new Date('2024-01-15');

console.log('Testing EthiopianDateAdapter...');
console.log('Test date:', testDate);

try {
  console.log('Format test:', adapter.format(testDate, 'shortDate'));
  console.log('FormatByString test:', adapter.formatByString(testDate, 'DD/MM/YYYY'));
  console.log('AddDays test:', adapter.addDays(testDate, 5));
  console.log('AddMonths test:', adapter.addMonths(testDate, 1));
  console.log('AddYears test:', adapter.addYears(testDate, 1));
  console.log('All tests passed!');
} catch (error) {
  console.error('Test failed:', error);
}
