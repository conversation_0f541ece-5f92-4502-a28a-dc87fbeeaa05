#!/usr/bin/env python
"""
Simple script to set up general workflow-based permissions and groups.
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from users.models_groups import TenantGroup, GroupTemplate, GroupMembership
from users.models import UserRole
from django.contrib.auth import get_user_model
from django_tenants.utils import schema_context, get_public_schema_name

User = get_user_model()

def create_general_permissions():
    """Create general workflow-based permissions"""
    print('📋 Creating general permissions...')
    
    # Get or create content types
    user_ct = ContentType.objects.get_for_model(User)
    
    # Define general permissions
    general_permissions = [
        # Citizen Management (Clerk Level)
        ('register_citizens', 'Can register citizens (full workflow)', user_ct),
        ('view_citizens_list', 'Can view citizens list', user_ct),
        ('view_citizen_details', 'Can view citizen details', user_ct),
        
        # ID Card Management
        ('generate_id_cards', 'Can generate ID cards', user_ct),
        ('view_id_cards_list', 'Can view ID cards list', user_ct),
        ('send_id_cards_for_approval', 'Can send ID cards for approval', user_ct),
        ('approve_id_cards', 'Can approve ID cards', user_ct),
        ('print_id_cards', 'Can print ID cards locally', user_ct),
        ('send_id_cards_to_higher_level', 'Can send ID cards to higher administrative level', user_ct),
        
        # Document Management
        ('verify_documents', 'Can verify citizen documents', user_ct),
        
        # User Management (Admin Levels)
        ('create_kebele_users', 'Can create kebele users and assign roles', user_ct),
        ('create_subcity_users', 'Can create subcity users and assign roles', user_ct),
        ('manage_all_users', 'Can manage all users across all tenants', user_ct),
        
        # Data Access (Hierarchical)
        ('view_own_kebele_data', 'Can view own kebele data', user_ct),
        ('view_child_kebeles_data', 'Can view data from child kebeles', user_ct),
        ('view_child_subcities_data', 'Can view data from child subcities', user_ct),
        ('view_all_tenants_data', 'Can view data from all tenants', user_ct),
        
        # Dashboard Access
        ('view_kebele_dashboard', 'Can view kebele dashboard', user_ct),
        ('view_subcity_dashboard', 'Can view subcity dashboard (aggregated)', user_ct),
        ('view_city_dashboard', 'Can view city dashboard (aggregated)', user_ct),
        ('view_system_dashboard', 'Can view system-wide dashboard', user_ct),
        
        # Reports
        ('view_kebele_reports', 'Can view reports for own kebele', user_ct),
        ('view_subcity_reports', 'Can view cumulative reports for subcity', user_ct),
        ('view_city_reports', 'Can view cumulative reports for city', user_ct),
        ('view_all_reports', 'Can view all reports across system', user_ct),
        
        # System Administration
        ('manage_tenants', 'Can manage tenants (create, edit, delete)', user_ct),
        ('manage_system_settings', 'Can manage system-wide settings', user_ct),
        ('full_system_access', 'Full access to all system features', user_ct),
    ]
    
    created_permissions = {}
    for codename, name, content_type in general_permissions:
        permission, created = Permission.objects.get_or_create(
            codename=codename,
            content_type=content_type,
            defaults={'name': name}
        )
        created_permissions[codename] = permission
        if created:
            print(f'  ✅ Created permission: {codename}')
        else:
            print(f'  ♻️ Updated permission: {codename}')
    
    return created_permissions

def create_system_groups(permissions):
    """Create system groups with appropriate permissions"""
    print('👥 Creating system groups...')
    
    # Define groups and their permissions
    group_definitions = {
        'clerk': {
            'description': 'Kebele clerk with citizen registration and basic ID card management',
            'group_type': 'operational',
            'level': 10,
            'permissions': [
                'register_citizens',
                'view_citizens_list',
                'view_citizen_details',
                'generate_id_cards',
                'view_id_cards_list',
                'send_id_cards_for_approval',
                'view_own_kebele_data',
                'view_kebele_dashboard',
            ]
        },
        'kebele_leader': {
            'description': 'Kebele leader with approval and verification responsibilities',
            'group_type': 'administrative',
            'level': 30,
            'permissions': [
                'view_citizens_list',
                'view_citizen_details',
                'view_id_cards_list',
                'approve_id_cards',
                'send_id_cards_to_higher_level',
                'verify_documents',
                'view_own_kebele_data',
                'view_kebele_dashboard',
                'view_kebele_reports',
            ]
        },
        'kebele_admin': {
            'description': 'Kebele administrator with full kebele management capabilities',
            'group_type': 'administrative',
            'level': 40,
            'permissions': [
                'register_citizens',
                'view_citizens_list',
                'view_citizen_details',
                'generate_id_cards',
                'view_id_cards_list',
                'approve_id_cards',
                'print_id_cards',
                'verify_documents',
                'create_kebele_users',
                'view_own_kebele_data',
                'view_kebele_dashboard',
                'view_kebele_reports',
            ]
        },
        'subcity_admin': {
            'description': 'Subcity administrator managing multiple kebeles',
            'group_type': 'administrative',
            'level': 50,
            'permissions': [
                'create_kebele_users',
                'view_citizens_list',
                'view_citizen_details',
                'view_id_cards_list',
                'approve_id_cards',
                'view_child_kebeles_data',
                'view_subcity_dashboard',
                'view_subcity_reports',
            ]
        },
        'city_admin': {
            'description': 'City administrator managing multiple subcities',
            'group_type': 'administrative',
            'level': 70,
            'permissions': [
                'create_subcity_users',
                'view_citizens_list',
                'view_citizen_details',
                'view_child_subcities_data',
                'view_city_dashboard',
                'view_city_reports',
            ]
        },
        'super_admin': {
            'description': 'System super administrator with full access',
            'group_type': 'administrative',
            'level': 100,
            'permissions': [
                'full_system_access',
                'manage_all_users',
                'manage_tenants',
                'manage_system_settings',
                'view_all_tenants_data',
                'view_system_dashboard',
                'view_all_reports',
                # Include all other permissions for completeness
                'register_citizens',
                'view_citizens_list',
                'view_citizen_details',
                'generate_id_cards',
                'view_id_cards_list',
                'approve_id_cards',
                'verify_documents',
            ]
        }
    }
    
    created_groups = {}
    for group_name, group_config in group_definitions.items():
        # Create Django Group
        django_group, created = Group.objects.get_or_create(name=group_name)
        
        # Create or update TenantGroup
        tenant_group, created = TenantGroup.objects.get_or_create(
            group=django_group,
            defaults={
                'description': group_config['description'],
                'is_system_group': True,
                'group_type': group_config['group_type'],
                'level': group_config['level'],
            }
        )
        
        # Assign permissions
        group_permissions = []
        for perm_codename in group_config['permissions']:
            if perm_codename in permissions:
                group_permissions.append(permissions[perm_codename])
        
        django_group.permissions.set(group_permissions)
        created_groups[group_name] = tenant_group
        
        print(f'  ✅ {"Created" if created else "Updated"} group: {group_name} ({len(group_permissions)} permissions)')
    
    return created_groups

def create_superadmin_user(groups):
    """Create or update superadmin user and assign to super_admin group"""
    print('👤 Setting up superadmin user...')

    # Create or get superadmin user
    superadmin_email = '<EMAIL>'
    superadmin_username = 'admin'
    superadmin_password = 'admin123'

    try:
        user = User.objects.get(email=superadmin_email)
        print(f'  ♻️ Found existing superadmin user: {superadmin_email}')

        # Update user properties to ensure they're correct
        user.username = superadmin_username
        user.role = UserRole.SUPERADMIN
        user.is_superuser = True
        user.is_staff = True
        user.is_active = True
        user.first_name = 'Super'
        user.last_name = 'Admin'
        user.save()
        print(f'  ✅ Updated superadmin user properties')

    except User.DoesNotExist:
        user = User.objects.create_user(
            email=superadmin_email,
            username=superadmin_username,
            password=superadmin_password,
            role=UserRole.SUPERADMIN,
            is_superuser=True,
            is_staff=True,
            first_name='Super',
            last_name='Admin'
        )
        print(f'  ✅ Created superadmin user: {superadmin_email}')

    # Assign user to super_admin group
    with schema_context(get_public_schema_name()):
        if 'super_admin' in groups:
            super_admin_group = groups['super_admin']

            # Check if user is already in group
            membership = GroupMembership.objects.filter(
                user_email=user.email,
                group=super_admin_group,
                is_active=True
            ).first()

            if not membership:
                # Add user to super_admin group
                membership = GroupMembership.objects.create(
                    user_email=user.email,
                    user_tenant_id=None,  # Superadmin has no specific tenant
                    user_tenant_schema=None,  # Nullable field
                    group=super_admin_group,
                    is_primary=True,
                    is_active=True,
                    reason='Initial superadmin setup'
                )
                print(f'  ✅ Added superadmin to super_admin group')
            else:
                print(f'  ♻️ Superadmin already in super_admin group')
        else:
            print(f'  ❌ super_admin group not found!')

    return user

def main():
    print('🔧 Setting up complete GoID system...')

    with transaction.atomic():
        # Create general permissions
        permissions = create_general_permissions()

        # Create system groups with general permissions
        groups = create_system_groups(permissions)

        # Create superadmin user and assign to group
        superadmin_user = create_superadmin_user(groups)

    print('✅ Complete system setup completed!')
    print(f'📊 Created {len(permissions)} permissions and {len(groups)} groups')
    print(f'👤 Superadmin user: {superadmin_user.email} (password: admin123)')
    print('🌐 You can now login to the frontend with superadmin credentials')

if __name__ == '__main__':
    main()
