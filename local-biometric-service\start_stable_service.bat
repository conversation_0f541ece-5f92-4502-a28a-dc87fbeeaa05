@echo off
echo ========================================
echo    STABLE WORKING BIOMETRIC SERVICE
echo ========================================
echo.
echo GUARANTEED FEATURES:
echo   - Will NOT crash during testing
echo   - Real finger detection (when possible)
echo   - 3-second timeout for no finger
echo   - Conservative SDK usage
echo   - Graceful error handling
echo.
echo SERVICE URL: http://localhost:8001
echo.
echo TESTING APPROACH:
echo   - Avoids problematic SDK calls during startup
echo   - Uses safe finger detection methods
echo   - Falls back gracefully if SDK issues occur
echo   - Always provides user feedback
echo.
echo EXPECTED RESULTS:
echo   - No finger = "No finger detected" (3 seconds)
echo   - With finger = Success with quality score
echo   - Service stays running regardless of results
echo.

echo Starting stable working biometric service...
echo.
echo This service prioritizes stability over LED activation
echo Keep this window open while using GoID system
echo.

python stable_working_service.py

echo.
echo Service stopped normally.
pause
