#!/usr/bin/env python3
"""
Minimal test service to debug routing issues
"""

from flask import Flask, jsonify
from flask_cors import CORS

app = Flask(__name__)
CORS(app, origins=['*'])

@app.route('/')
def index():
    return '<h1>Test Service Running!</h1>'

@app.route('/api/health')
def health():
    return jsonify({'status': 'ok', 'message': 'Test service is working'})

@app.route('/api/device/status')
def device_status():
    return jsonify({
        'success': True,
        'data': {
            'connected': True,
            'device_connected': True,
            'initialized': True,
            'model': 'Test Device'
        },
        'connected': True,
        'device_connected': True
    })

if __name__ == '__main__':
    print("Starting Test Service on http://localhost:8001")
    
    try:
        from waitress import serve
        serve(app, host='0.0.0.0', port=8001)
    except ImportError:
        app.run(host='0.0.0.0', port=8001, debug=True)
