import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { format, parse, isValid, addDays, addMonths, addYears } from 'date-fns';

// Create a custom adapter that uses date-fns instead of dayjs
export class CustomDateAdapter extends AdapterDateFns {
  constructor({ locale } = {}) {
    super({ locale });
  }

  // Override any methods that might cause issues
  format(date, formatString) {
    return format(date, formatString);
  }

  parse(value, formatString) {
    if (!value) return null;
    const parsedDate = parse(value, formatString, new Date());
    return isValid(parsedDate) ? parsedDate : null;
  }

  addDays(date, amount) {
    return addDays(date, amount);
  }

  addMonths(date, amount) {
    return addMonths(date, amount);
  }

  addYears(date, amount) {
    return addYears(date, amount);
  }

  // Add any other methods that might be needed
}

export default CustomDateAdapter;
