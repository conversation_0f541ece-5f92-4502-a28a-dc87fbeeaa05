/* Dashboard-specific Tailwind CSS utilities */

/* Enhanced animations for dashboard with orange theme */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes rotateIn {
  from {
    transform: rotate(-180deg) scale(0.5);
    opacity: 0;
  }
  to {
    transform: rotate(0deg) scale(1);
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px #5fc25f, 0 0 10px #61d433, 0 0 15px #c59e6b;
  }
  50% {
    box-shadow: 0 0 10px #a3c457, 0 0 20px #2e9ccf, 0 0 30px #5379ca;
  }
}

/* Custom gradient backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-success {
  background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.gradient-warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-error {
  background: linear-gradient(135deg, #a7e98e 0%, #fecfef 100%);
}

/* Custom shadows */
.shadow-soft {
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
}

.shadow-medium {
  box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.shadow-large {
  box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Chart container improvements */
.chart-container {
  @apply bg-white rounded-xl shadow-soft hover:shadow-medium transition-all duration-300;
}

.chart-container:hover {
  transform: translateY(-2px);
}

/* Dashboard card hover effects */
.dashboard-card {
  @apply transform transition-all duration-300 ease-in-out;
}

.dashboard-card:hover {
  @apply -translate-y-1 shadow-lg;
}

/* Responsive text utilities */
.text-responsive-sm {
  @apply text-sm md:text-base;
}

.text-responsive-lg {
  @apply text-lg md:text-xl lg:text-2xl;
}

.text-responsive-xl {
  @apply text-xl md:text-2xl lg:text-3xl;
}

/* Custom grid patterns */
.grid-pattern {
  background-image: radial-gradient(circle at 50% 50%, rgba(255,255,255,0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Loading spinner */
.spinner {
  @apply animate-spin rounded-full border-b-2 border-blue-600;
}

/* Glassmorphism effect */
.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Tooltip styles */
.tooltip {
  @apply bg-white border border-gray-200 rounded-lg shadow-lg p-2 text-sm;
}

/* Enhanced Animation Classes */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-slide-up {
  animation: slideUp 0.8s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.5s ease-out forwards;
}

.animate-bounce-in {
  animation: bounceIn 0.8s ease-out forwards;
}

.animate-slide-in-left {
  animation: slideInLeft 0.7s ease-out forwards;
}

.animate-slide-in-right {
  animation: slideInRight 0.7s ease-out forwards;
}

.animate-rotate-in {
  animation: rotateIn 0.8s ease-out forwards;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 143, 0, 0.2), transparent);
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

/* Chart-specific animations */
.chart-enter {
  animation: scaleIn 0.8s ease-out forwards;
}

.chart-hover {
  transition: all 0.3s ease;
}

.chart-hover:hover {
  transform: scale(1.02);
  filter: brightness(1.1);
}

/* Animation delays */
.delay-100 {
  animation-delay: 0.1s;
}

.delay-200 {
  animation-delay: 0.2s;
}

.delay-300 {
  animation-delay: 0.3s;
}

.delay-400 {
  animation-delay: 0.4s;
}

.delay-500 {
  animation-delay: 0.5s;
}

.delay-700 {
  animation-delay: 0.7s;
}

.delay-1000 {
  animation-delay: 1s;
}
