import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { enUS } from 'date-fns/locale';
import {
  gregorianToEthiopian,
  ethiopianToGregorian,
  formatEthiopianDate,
  parseEthiopianDate,
  addDaysToEthiopianDate,
  addMonthsToEthiopianDate,
  addYearsToEthiopianDate,
  isValidEthiopianDate,
  getCurrentEthiopianDate,
  ETHIOPIAN_MONTHS,
  ETHIOPIAN_MONTHS_EN,
  getDaysInEthiopianMonth
} from './ethiopianCalendar';

// Create a minimal locale object that satisfies date-fns requirements
const createEthiopianLocale = (language = 'en') => {
  const baseLocale = enUS; // Use English US as base

  return {
    ...baseLocale,
    code: language === 'am' ? 'am-ET' : 'en-ET',
    localize: {
      ...baseLocale.localize,
      month: (monthIndex) => {
        const monthNames = language === 'am' ? ETHIOPIAN_MONTHS : ETHIOPIAN_MONTHS_EN;
        return monthNames[monthIndex] || monthNames[0];
      },
      day: baseLocale.localize.day,
      dayPeriod: baseLocale.localize.dayPeriod,
      era: baseLocale.localize.era,
      quarter: baseLocale.localize.quarter,
    },
    formatLong: baseLocale.formatLong,
    formatRelative: baseLocale.formatRelative,
    match: baseLocale.match,
  };
};

/**
 * Ethiopian Date Adapter for MUI X Date Pickers
 * This adapter allows MUI date pickers to work with Ethiopian calendar
 */
export class EthiopianDateAdapter extends AdapterDateFns {
  constructor({ locale, formats } = {}) {
    // Create a proper date-fns locale object
    const language = locale || 'en';
    const dateFnsLocale = createEthiopianLocale(language);

    // Call super constructor first
    super({ locale: dateFnsLocale, formats });

    // Override properties after super() call to avoid conflicts
    // Use Object.defineProperty to avoid setter conflicts
    Object.defineProperty(this, 'lib', {
      value: 'ethiopian-calendar',
      writable: true,
      configurable: true
    });

    Object.defineProperty(this, 'locale', {
      value: language,
      writable: true,
      configurable: true
    });

    Object.defineProperty(this, 'dateFnsLocale', {
      value: dateFnsLocale,
      writable: true,
      configurable: true
    });

    // Set formatTokenMap as a property
    Object.defineProperty(this, 'formatTokenMap', {
      value: {
        // Day tokens
        d: 'day',
        dd: 'day',
        D: 'day',
        DD: 'day',
        // Month tokens
        M: 'month',
        MM: 'month',
        MMM: 'month',
        MMMM: 'month',
        // Year tokens
        y: 'year',
        yy: 'year',
        yyyy: 'year',
        YYYY: 'year'
      },
      writable: true,
      configurable: true
    });

    // Completely replace the format methods to ensure they're never called from parent
    this.format = this._ethiopianFormat.bind(this);
    this.formatByString = this._ethiopianFormatByString.bind(this);

    // Also override any potential internal method names
    this._format = this._ethiopianFormat.bind(this);
    this._formatByString = this._ethiopianFormatByString.bind(this);
  }

  // Core date creation and parsing methods
  date(value) {
    if (value === null) {
      return null;
    }

    if (value === undefined) {
      return new Date();
    }

    if (typeof value === 'string') {
      const ethiopianDate = parseEthiopianDate(value);
      if (ethiopianDate) {
        return ethiopianToGregorian(ethiopianDate.year, ethiopianDate.month, ethiopianDate.day);
      }
      return new Date(value);
    }

    if (value instanceof Date) {
      return value;
    }

    // If it's an Ethiopian date object
    if (value && typeof value === 'object' && value.year && value.month && value.day) {
      return ethiopianToGregorian(value.year, value.month, value.day);
    }

    return new Date(value);
  }

  parse(value, format) {
    if (!value) return null;

    if (typeof value === 'string') {
      const ethiopianDate = parseEthiopianDate(value);
      if (ethiopianDate && isValidEthiopianDate(ethiopianDate)) {
        return ethiopianToGregorian(ethiopianDate.year, ethiopianDate.month, ethiopianDate.day);
      }
    }

    // Try to parse as a regular date string instead of calling super
    try {
      const parsedDate = new Date(value);
      return isNaN(parsedDate.getTime()) ? null : parsedDate;
    } catch (error) {
      console.warn('Ethiopian parse error:', error);
      return null;
    }
  }



  // Ethiopian-specific format method that never calls parent
  _ethiopianFormat(date, formatKey) {
    // Always handle formatting internally, never call parent
    if (!date) return '';

    // Handle invalid dates gracefully
    if (!(date instanceof Date) || isNaN(date.getTime())) {
      return '';
    }

    // Always use Ethiopian calendar formatting
    try {
      const ethiopianDate = gregorianToEthiopian(date);
      if (!ethiopianDate) {
        // Return a safe fallback format
        return date.toLocaleDateString('en-US');
      }

      const language = this.locale === 'am' ? 'am' : 'en';

      // Handle all possible format keys
      switch (formatKey) {
        case 'fullDate':
        case 'fullDateWithWeekday':
          return formatEthiopianDate(ethiopianDate, 'DD MMM YYYY', language);
        case 'keyboardDate':
        case 'normalDate':
        case 'shortDate':
        case 'inputDate':
          return formatEthiopianDate(ethiopianDate, 'DD/MM/YYYY', language);
        case 'year':
          return ethiopianDate.year.toString();
        case 'month':
          const monthNames = language === 'am' ? ETHIOPIAN_MONTHS : ETHIOPIAN_MONTHS_EN;
          return monthNames[ethiopianDate.month - 1] || '';
        case 'monthShort':
          const monthNamesShort = language === 'am' ? ETHIOPIAN_MONTHS : ETHIOPIAN_MONTHS_EN;
          return (monthNamesShort[ethiopianDate.month - 1] || '').substring(0, 3);
        case 'monthAndYear':
          const monthNamesForYear = language === 'am' ? ETHIOPIAN_MONTHS : ETHIOPIAN_MONTHS_EN;
          return `${monthNamesForYear[ethiopianDate.month - 1]} ${ethiopianDate.year}`;
        case 'dayOfMonth':
          return ethiopianDate.day.toString();
        case 'weekday':
          const weekdays = this.getWeekdays();
          const dayOfWeek = date.getDay();
          return weekdays[dayOfWeek] || '';
        case 'weekdayShort':
          const weekdaysShort = this.getWeekdays();
          const dayOfWeekShort = date.getDay();
          return (weekdaysShort[dayOfWeekShort] || '').substring(0, 3);
        default:
          // For any unknown format, return Ethiopian date
          return formatEthiopianDate(ethiopianDate, 'DD/MM/YYYY', language);
      }
    } catch (error) {
      console.warn('Ethiopian date formatting error:', error);
      // Return a safe fallback
      return date.toLocaleDateString('en-US');
    }
  }

  // Ethiopian-specific formatByString method that never calls parent
  _ethiopianFormatByString(date, formatString) {
    // Always handle formatting internally, never call parent
    if (!date) return '';

    // Handle invalid dates gracefully
    if (!(date instanceof Date) || isNaN(date.getTime())) {
      return '';
    }

    // Always use Ethiopian calendar formatting
    try {
      const ethiopianDate = gregorianToEthiopian(date);
      if (!ethiopianDate) {
        // Return a safe fallback format
        return date.toLocaleDateString('en-US');
      }

      const language = this.locale === 'am' ? 'am' : 'en';

      // Use our Ethiopian date formatter
      if (formatString) {
        return formatEthiopianDate(ethiopianDate, formatString, language);
      } else {
        // Default format if no format string provided
        return formatEthiopianDate(ethiopianDate, 'DD/MM/YYYY', language);
      }
    } catch (error) {
      console.warn('Ethiopian date formatByString error:', error);
      // Return a safe fallback
      return date.toLocaleDateString('en-US');
    }
  }

  // Public format methods that delegate to Ethiopian implementations
  format(date, formatKey) {
    return this._ethiopianFormat(date, formatKey);
  }

  formatByString(date, formatString) {
    return this._ethiopianFormatByString(date, formatString);
  }

  // Override expandFormat to handle Ethiopian calendar formats
  expandFormat(format) {
    // Always return a safe, simple format for Ethiopian calendar
    // Never call parent methods
    return [
      { type: 'day', value: 'DD' },
      { type: 'literal', value: '/' },
      { type: 'month', value: 'MM' },
      { type: 'literal', value: '/' },
      { type: 'year', value: 'YYYY' }
    ];
  }

  // Override any methods that might internally call format
  getFormatByString(formatString) {
    // Return the format string as-is or a safe default
    return formatString || 'DD/MM/YYYY';
  }

  // Override to prevent any locale-related calls
  getLocaleFormats() {
    return {
      keyboardDate: 'DD/MM/YYYY',
      fullDate: 'DD MMM YYYY',
      shortDate: 'DD/MM/YYYY',
      normalDate: 'DD/MM/YYYY',
      monthAndYear: 'MMM YYYY',
      dayOfMonth: 'DD',
      month: 'MMM',
      monthShort: 'MMM',
      year: 'YYYY'
    };
  }

  // Override getFormatHelperText to provide Ethiopian format help
  getFormatHelperText(format) {
    return 'DD/MM/YYYY (Ethiopian Calendar)';
  }



  // Date manipulation methods
  addDays(date, amount) {
    if (!date) return null;

    // Handle invalid dates gracefully
    if (!(date instanceof Date) || isNaN(date.getTime())) {
      return null;
    }

    try {
      const ethiopianDate = gregorianToEthiopian(date);
      if (!ethiopianDate) {
        // Use simple JavaScript date arithmetic as fallback
        const newDate = new Date(date);
        newDate.setDate(newDate.getDate() + amount);
        return newDate;
      }

      const newEthiopianDate = addDaysToEthiopianDate(ethiopianDate, amount);
      return ethiopianToGregorian(newEthiopianDate.year, newEthiopianDate.month, newEthiopianDate.day);
    } catch (error) {
      console.warn('Ethiopian addDays error:', error);
      // Use simple JavaScript date arithmetic as fallback
      const newDate = new Date(date);
      newDate.setDate(newDate.getDate() + amount);
      return newDate;
    }
  }

  addWeeks(date, amount) {
    return this.addDays(date, amount * 7);
  }

  addMonths(date, amount) {
    if (!date) return null;

    // Handle invalid dates gracefully
    if (!(date instanceof Date) || isNaN(date.getTime())) {
      return null;
    }

    try {
      const ethiopianDate = gregorianToEthiopian(date);
      if (!ethiopianDate) {
        // Use simple JavaScript date arithmetic as fallback
        const newDate = new Date(date);
        newDate.setMonth(newDate.getMonth() + amount);
        return newDate;
      }

      const newEthiopianDate = addMonthsToEthiopianDate(ethiopianDate, amount);
      return ethiopianToGregorian(newEthiopianDate.year, newEthiopianDate.month, newEthiopianDate.day);
    } catch (error) {
      console.warn('Ethiopian addMonths error:', error);
      // Use simple JavaScript date arithmetic as fallback
      const newDate = new Date(date);
      newDate.setMonth(newDate.getMonth() + amount);
      return newDate;
    }
  }

  addYears(date, amount) {
    if (!date) return null;

    // Handle invalid dates gracefully
    if (!(date instanceof Date) || isNaN(date.getTime())) {
      return null;
    }

    try {
      const ethiopianDate = gregorianToEthiopian(date);
      if (!ethiopianDate) {
        // Use simple JavaScript date arithmetic as fallback
        const newDate = new Date(date);
        newDate.setFullYear(newDate.getFullYear() + amount);
        return newDate;
      }

      const newEthiopianDate = addYearsToEthiopianDate(ethiopianDate, amount);
      return ethiopianToGregorian(newEthiopianDate.year, newEthiopianDate.month, newEthiopianDate.day);
    } catch (error) {
      console.warn('Ethiopian addYears error:', error);
      // Use simple JavaScript date arithmetic as fallback
      const newDate = new Date(date);
      newDate.setFullYear(newDate.getFullYear() + amount);
      return newDate;
    }
  }

  // Date comparison methods
  isEqual(date1, date2) {
    if (!date1 || !date2) return false;
    
    const eth1 = gregorianToEthiopian(date1);
    const eth2 = gregorianToEthiopian(date2);
    
    if (!eth1 || !eth2) return false;
    
    return eth1.year === eth2.year && eth1.month === eth2.month && eth1.day === eth2.day;
  }

  isSameDay(date1, date2) {
    return this.isEqual(date1, date2);
  }

  isSameMonth(date1, date2) {
    if (!date1 || !date2) return false;
    
    const eth1 = gregorianToEthiopian(date1);
    const eth2 = gregorianToEthiopian(date2);
    
    if (!eth1 || !eth2) return false;
    
    return eth1.year === eth2.year && eth1.month === eth2.month;
  }

  isSameYear(date1, date2) {
    if (!date1 || !date2) return false;
    
    const eth1 = gregorianToEthiopian(date1);
    const eth2 = gregorianToEthiopian(date2);
    
    if (!eth1 || !eth2) return false;
    
    return eth1.year === eth2.year;
  }

  isAfter(date1, date2) {
    if (!date1 || !date2) return false;
    return date1.getTime() > date2.getTime();
  }

  isBefore(date1, date2) {
    if (!date1 || !date2) return false;
    return date1.getTime() < date2.getTime();
  }

  // Date extraction methods
  getYear(date) {
    if (!date) return 0;
    const ethiopianDate = gregorianToEthiopian(date);
    return ethiopianDate ? ethiopianDate.year : 0;
  }

  getMonth(date) {
    if (!date) return 0;
    const ethiopianDate = gregorianToEthiopian(date);
    return ethiopianDate ? ethiopianDate.month - 1 : 0; // MUI expects 0-based months
  }

  getDate(date) {
    if (!date) return 0;
    const ethiopianDate = gregorianToEthiopian(date);
    return ethiopianDate ? ethiopianDate.day : 0;
  }

  getDaysInMonth(date) {
    if (!date) return 30;
    const ethiopianDate = gregorianToEthiopian(date);
    if (!ethiopianDate) return 30;
    return getDaysInEthiopianMonth(ethiopianDate.month, ethiopianDate.year);
  }

  // Validation methods
  isValid(date) {
    if (!date || !(date instanceof Date)) return false;
    if (isNaN(date.getTime())) return false;
    
    const ethiopianDate = gregorianToEthiopian(date);
    return ethiopianDate ? isValidEthiopianDate(ethiopianDate) : false;
  }

  isNull(date) {
    return date === null;
  }

  // Calendar navigation methods
  getWeekdays() {
    // Ethiopian week starts on Sunday
    const weekdays = this.locale === 'am'
      ? ['እሑድ', 'ሰኞ', 'ማክሰኞ', 'ረቡዕ', 'ሐሙስ', 'ዓርብ', 'ቅዳሜ']
      : ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

    return weekdays;
  }

  getWeekArray(date) {
    // This is a simplified implementation
    // For now, use a safe fallback instead of calling super
    if (!date) return [];

    try {
      // Create a simple week array with the current date
      const startOfWeek = this.startOfDay(date);
      const week = [];
      for (let i = 0; i < 7; i++) {
        week.push(this.addDays(startOfWeek, i));
      }
      return [week];
    } catch (error) {
      console.warn('Ethiopian getWeekArray error:', error);
      return [];
    }
  }

  // Override to prevent format parsing issues
  getDateSeparator() {
    return '/';
  }

  // Override methods that might be called by MUI internally
  getMeridiemText(meridiem) {
    return meridiem === 'am' ? 'AM' : 'PM';
  }

  getMonthArray(date) {
    if (!date) return [];

    try {
      const ethiopianDate = gregorianToEthiopian(date);
      if (!ethiopianDate) return [];

      const months = [];
      for (let i = 1; i <= 13; i++) {
        months.push(ethiopianToGregorian(ethiopianDate.year, i, 1));
      }
      return months;
    } catch (error) {
      console.warn('Ethiopian getMonthArray error:', error);
      return [];
    }
  }

  getYearRange(start, end) {
    const years = [];
    for (let year = start; year <= end; year++) {
      years.push(ethiopianToGregorian(year, 1, 1));
    }
    return years;
  }

  // Override any other methods that might call parent format
  formatNumber(numberToFormat) {
    return String(numberToFormat);
  }

  // Override methods that might be called internally by MUI
  getCurrentLocaleCode() {
    return this.locale === 'am' ? 'am-ET' : 'en-ET';
  }

  // Override to prevent any internal format calls
  getDisplayName() {
    return 'Ethiopian Calendar';
  }

  // Override to ensure no parent format calls
  formatTokens(date, formatString) {
    return this.formatByString(date, formatString);
  }

  // Override to prevent locale-based formatting
  formatWithLocale(date, formatString) {
    return this.formatByString(date, formatString);
  }

  // Override any potential internal format calls
  _format(date, formatString) {
    return this.formatByString(date, formatString);
  }

  // Override to prevent parent adapter calls
  _formatByString(date, formatString) {
    return this.formatByString(date, formatString);
  }

  // Utility methods
  startOfDay(date) {
    if (!date) return null;
    const newDate = new Date(date);
    newDate.setHours(0, 0, 0, 0);
    return newDate;
  }

  endOfDay(date) {
    if (!date) return null;
    const newDate = new Date(date);
    newDate.setHours(23, 59, 59, 999);
    return newDate;
  }

  startOfMonth(date) {
    if (!date) return null;
    const ethiopianDate = gregorianToEthiopian(date);
    if (!ethiopianDate) return null;
    return ethiopianToGregorian(ethiopianDate.year, ethiopianDate.month, 1);
  }

  endOfMonth(date) {
    if (!date) return null;
    const ethiopianDate = gregorianToEthiopian(date);
    if (!ethiopianDate) return null;
    const daysInMonth = getDaysInEthiopianMonth(ethiopianDate.month, ethiopianDate.year);
    return ethiopianToGregorian(ethiopianDate.year, ethiopianDate.month, daysInMonth);
  }

  startOfYear(date) {
    if (!date) return null;
    const ethiopianDate = gregorianToEthiopian(date);
    if (!ethiopianDate) return null;
    return ethiopianToGregorian(ethiopianDate.year, 1, 1);
  }

  endOfYear(date) {
    if (!date) return null;
    const ethiopianDate = gregorianToEthiopian(date);
    if (!ethiopianDate) return null;
    const daysInPagume = getDaysInEthiopianMonth(13, ethiopianDate.year);
    return ethiopianToGregorian(ethiopianDate.year, 13, daysInPagume);
  }
}

export default EthiopianDateAdapter;
