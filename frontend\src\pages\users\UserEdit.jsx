import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CardContent,
  Typography,
  TextField,
  Grid,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  Alert,
  CircularProgress,
  Divider,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material';
import {
  ArrowBack as BackIcon,
} from '@mui/icons-material';
import { useFormik } from 'formik';
import * as yup from 'yup';
import axios from '../../utils/axios';
import { getStandardMenuProps, createSelectChangeHandler } from '../../utils/dropdownUtils';

// Mock data for users
const mockUsers = [
  {
    id: 1,
    email: '<EMAIL>',
    username: 'admin',
    first_name: 'Admin',
    last_name: 'User',
    role: 'superadmin',
    tenant: null,
    tenant_name: null,
    is_active: true,
    date_joined: '2023-11-15T12:00:00Z'
  },
  {
    id: 2,
    email: '<EMAIL>',
    username: 'city_admin',
    first_name: 'City',
    last_name: 'Admin',
    role: 'city_admin',
    tenant: 1,
    tenant_name: 'Addis Ababa',
    is_active: true,
    date_joined: '2023-11-15T12:00:00Z'
  },
  {
    id: 3,
    email: '<EMAIL>',
    username: 'subcity_admin',
    first_name: 'Subcity',
    last_name: 'Admin',
    role: 'subcity_admin',
    tenant: 2,
    tenant_name: 'Bole',
    is_active: true,
    date_joined: '2023-11-15T12:00:00Z'
  },
  {
    id: 4,
    email: '<EMAIL>',
    username: 'kebele_admin',
    first_name: 'Kebele',
    last_name: 'Admin',
    role: 'kebele_admin',
    tenant: 3,
    tenant_name: 'Bole 01',
    is_active: true,
    date_joined: '2023-11-15T12:00:00Z'
  },
  {
    id: 5,
    email: '<EMAIL>',
    username: 'clerk',
    first_name: 'Clerk',
    last_name: 'User',
    role: 'clerk',
    tenant: 3,
    tenant_name: 'Bole 01',
    is_active: true,
    date_joined: '2023-11-15T12:00:00Z'
  }
];

// Mock data for tenants
const mockTenants = [
  {
    id: 1,
    name: 'Addis Ababa',
    type: 'city',
  },
  {
    id: 2,
    name: 'Bole',
    type: 'subcity',
    parent: 1,
  },
  {
    id: 3,
    name: 'Bole 01',
    type: 'kebele',
    parent: 2,
  },
];

const validationSchema = yup.object({
  email: yup.string().email('Enter a valid email').required('Email is required'),
  first_name: yup.string().required('First name is required'),
  last_name: yup.string().required('Last name is required'),
  role: yup.string().required('Role is required'),
  tenant: yup.number().when('role', {
    is: (role) => role !== 'superadmin',
    then: () => yup.number().required('Tenant is required'),
    otherwise: () => yup.number().nullable(),
  }),
});

const passwordValidationSchema = yup.object({
  current_password: yup.string().required('Current password is required'),
  new_password: yup.string()
    .min(8, 'Password should be of minimum 8 characters length')
    .required('New password is required'),
  confirm_password: yup.string()
    .oneOf([yup.ref('new_password'), null], 'Passwords must match')
    .required('Confirm password is required'),
});

const UserEdit = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [tenants, setTenants] = useState([]);
  const [availableTenants, setAvailableTenants] = useState([]);
  const [passwordDialogOpen, setPasswordDialogOpen] = useState(false);

  useEffect(() => {
    // In a real implementation, this would fetch data from the API
    // For now, we'll just use mock data
    const timer = setTimeout(() => {
      const foundUser = mockUsers.find(u => u.id === parseInt(id));
      if (foundUser) {
        setUser(foundUser);
        setTenants(mockTenants);
      } else {
        setError('User not found');
      }
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [id]);

  const formik = useFormik({
    initialValues: {
      email: '',
      username: '',
      first_name: '',
      last_name: '',
      role: '',
      tenant: '',
      is_active: true,
    },
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      try {
        setLoading(true);
        setError('');

        // In a real implementation, this would call an API endpoint
        // For now, we'll just simulate a successful request
        await new Promise(resolve => setTimeout(resolve, 1000));

        setSuccess(true);
        setTimeout(() => {
          navigate('/users');
        }, 1500);
      } catch (err) {
        setError(err.response?.data?.detail || 'Failed to update user. Please try again.');
      } finally {
        setLoading(false);
      }
    },
    enableReinitialize: true,
  });

  const passwordFormik = useFormik({
    initialValues: {
      current_password: '',
      new_password: '',
      confirm_password: '',
    },
    validationSchema: passwordValidationSchema,
    onSubmit: async (values) => {
      try {
        // In a real implementation, this would call an API endpoint
        // For now, we'll just simulate a successful request
        await new Promise(resolve => setTimeout(resolve, 500));

        // Close the dialog and reset the form
        setPasswordDialogOpen(false);
        passwordFormik.resetForm();
        setSuccess(true);
      } catch (err) {
        setError(err.response?.data?.detail || 'Failed to change password. Please try again.');
      }
    },
  });

  // Set initial values when user data is loaded
  useEffect(() => {
    if (user) {
      formik.setValues({
        email: user.email || '',
        username: user.username || '',
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        role: user.role || '',
        tenant: user.tenant || '',
        is_active: user.is_active,
      });
    }
  }, [user]);

  // Update available tenants based on selected role
  useEffect(() => {
    if (formik.values.role === 'city_admin') {
      // City admins can only be assigned to city tenants
      setAvailableTenants(tenants.filter(tenant => tenant.type === 'city'));
    } else if (formik.values.role === 'subcity_admin') {
      // Subcity admins can only be assigned to subcity tenants
      setAvailableTenants(tenants.filter(tenant => tenant.type === 'subcity'));
    } else if (formik.values.role === 'kebele_admin' || formik.values.role === 'clerk') {
      // Kebele admins and clerks can only be assigned to kebele tenants
      setAvailableTenants(tenants.filter(tenant => tenant.type === 'kebele'));
    } else {
      setAvailableTenants([]);
      formik.setFieldValue('tenant', '');
    }
  }, [formik.values.role, tenants]);

  if (loading && !user) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error && !user) {
    return (
      <Box sx={{ textAlign: 'center', py: 5 }}>
        <Typography variant="h5" color="error" gutterBottom>
          Error Loading User
        </Typography>
        <Typography variant="body1">{error}</Typography>
        <Button
          variant="contained"
          startIcon={<BackIcon />}
          onClick={() => navigate('/users')}
          sx={{ mt: 3 }}
        >
          Back to Users
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Button
            variant="outlined"
            startIcon={<BackIcon />}
            onClick={() => navigate('/users')}
            sx={{ mr: 2 }}
          >
            Back
          </Button>
          <Typography variant="h4" component="h1">
            Edit User
          </Typography>
        </Box>
        <Button
          variant="outlined"
          color="secondary"
          onClick={() => setPasswordDialogOpen(true)}
        >
          Change Password
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          User updated successfully!
        </Alert>
      )}

      <form onSubmit={formik.handleSubmit}>
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              User Information
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="first_name"
                  name="first_name"
                  label="First Name"
                  value={formik.values.first_name}
                  onChange={formik.handleChange}
                  error={formik.touched.first_name && Boolean(formik.errors.first_name)}
                  helperText={formik.touched.first_name && formik.errors.first_name}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="last_name"
                  name="last_name"
                  label="Last Name"
                  value={formik.values.last_name}
                  onChange={formik.handleChange}
                  error={formik.touched.last_name && Boolean(formik.errors.last_name)}
                  helperText={formik.touched.last_name && formik.errors.last_name}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="email"
                  name="email"
                  label="Email Address"
                  value={formik.values.email}
                  onChange={formik.handleChange}
                  error={formik.touched.email && Boolean(formik.errors.email)}
                  helperText={formik.touched.email && formik.errors.email}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="username"
                  name="username"
                  label="Username"
                  value={formik.values.username}
                  InputProps={{
                    readOnly: true,
                  }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth error={formik.touched.role && Boolean(formik.errors.role)}>
                  <InputLabel id="role-label">Role</InputLabel>
                  <Select
                    labelId="role-label"
                    id="role"
                    name="role"
                    value={formik.values.role}
                    label="Role"
                    onChange={createSelectChangeHandler('role', formik.handleChange)}
                    disabled={formik.values.role === 'superadmin'} // Prevent changing superadmin role
                    MenuProps={getStandardMenuProps()}
                  >
                    <MenuItem value="superadmin">Super Admin</MenuItem>
                    <MenuItem value="city_admin">City Admin</MenuItem>
                    <MenuItem value="subcity_admin">Subcity Admin</MenuItem>
                    <MenuItem value="kebele_admin">Kebele Admin</MenuItem>
                    <MenuItem value="clerk">Clerk</MenuItem>
                  </Select>
                  {formik.touched.role && formik.errors.role && (
                    <FormHelperText>{formik.errors.role}</FormHelperText>
                  )}
                </FormControl>
              </Grid>
              {formik.values.role && formik.values.role !== 'superadmin' && (
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth error={formik.touched.tenant && Boolean(formik.errors.tenant)}>
                    <InputLabel id="tenant-label">Tenant</InputLabel>
                    <Select
                      labelId="tenant-label"
                      id="tenant"
                      name="tenant"
                      value={formik.values.tenant}
                      label="Tenant"
                      onChange={formik.handleChange}
                    >
                      {availableTenants.map((tenant) => (
                        <MenuItem key={tenant.id} value={tenant.id}>
                          {tenant.name}
                        </MenuItem>
                      ))}
                    </Select>
                    {formik.touched.tenant && formik.errors.tenant && (
                      <FormHelperText>{formik.errors.tenant}</FormHelperText>
                    )}
                  </FormControl>
                </Grid>
              )}
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel id="is-active-label">Status</InputLabel>
                  <Select
                    labelId="is-active-label"
                    id="is_active"
                    name="is_active"
                    value={formik.values.is_active}
                    label="Status"
                    onChange={formik.handleChange}
                    disabled={formik.values.role === 'superadmin'} // Prevent disabling superadmin
                  >
                    <MenuItem value={true}>Active</MenuItem>
                    <MenuItem value={false}>Inactive</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
          <Button
            type="submit"
            variant="contained"
            size="large"
            disabled={loading}
            sx={{ minWidth: 150 }}
          >
            {loading ? <CircularProgress size={24} /> : 'Update User'}
          </Button>
        </Box>
      </form>

      {/* Change Password Dialog */}
      <Dialog open={passwordDialogOpen} onClose={() => setPasswordDialogOpen(false)}>
        <form onSubmit={passwordFormik.handleSubmit}>
          <DialogTitle>Change Password</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Enter your current password and a new password to change your password.
            </DialogContentText>
            <TextField
              autoFocus
              margin="dense"
              id="current_password"
              name="current_password"
              label="Current Password"
              type="password"
              fullWidth
              value={passwordFormik.values.current_password}
              onChange={passwordFormik.handleChange}
              error={passwordFormik.touched.current_password && Boolean(passwordFormik.errors.current_password)}
              helperText={passwordFormik.touched.current_password && passwordFormik.errors.current_password}
            />
            <TextField
              margin="dense"
              id="new_password"
              name="new_password"
              label="New Password"
              type="password"
              fullWidth
              value={passwordFormik.values.new_password}
              onChange={passwordFormik.handleChange}
              error={passwordFormik.touched.new_password && Boolean(passwordFormik.errors.new_password)}
              helperText={passwordFormik.touched.new_password && passwordFormik.errors.new_password}
            />
            <TextField
              margin="dense"
              id="confirm_password"
              name="confirm_password"
              label="Confirm New Password"
              type="password"
              fullWidth
              value={passwordFormik.values.confirm_password}
              onChange={passwordFormik.handleChange}
              error={passwordFormik.touched.confirm_password && Boolean(passwordFormik.errors.confirm_password)}
              helperText={passwordFormik.touched.confirm_password && passwordFormik.errors.confirm_password}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setPasswordDialogOpen(false)}>Cancel</Button>
            <Button type="submit" variant="contained">Change Password</Button>
          </DialogActions>
        </form>
      </Dialog>
    </Box>
  );
};

export default UserEdit;
