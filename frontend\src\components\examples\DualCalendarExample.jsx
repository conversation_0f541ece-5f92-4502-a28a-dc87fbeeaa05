import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON>po<PERSON>,
  <PERSON>rid,
  <PERSON><PERSON>,
  Al<PERSON>,
  Divider,
  Chip
} from '@mui/material';
import {
  Today as TodayIcon,
  Event as EventIcon,
  SwapHoriz as SwapIcon
} from '@mui/icons-material';
import DualCalendarDatePicker from '../common/DualCalendarDatePicker';
import FixedEthiopianDateAdapter from '../../utils/FixedEthiopianDateAdapter';
import {
  gregorianToEthiopian,
  formatEthiopianDate,
  getCurrentEthiopianDate
} from '../../utils/ethiopianCalendar';

/**
 * Example component demonstrating the dual calendar system
 * 
 * This shows how:
 * 1. User sees and selects Ethiopian dates
 * 2. System stores both Ethiopian and Gregorian dates
 * 3. Both calendars are used appropriately
 */
const DualCalendarExample = () => {
  const [selectedDate, setSelectedDate] = useState(null);
  const [adapter] = useState(() => new FixedEthiopianDateAdapter({ locale: 'en' }));

  const handleDateChange = (newDate) => {
    setSelectedDate(newDate);
    
    // Example of how to handle the date in your application
    if (newDate) {
      const dualInfo = adapter.getDualCalendarInfo(newDate);
      console.log('Date selected by user:', dualInfo);
      
      // You would typically save both dates to your backend:
      // const dataToSave = {
      //   date_gregorian: newDate.toISOString(),
      //   date_ethiopian: dualInfo.formatted.ethiopian,
      //   date_ethiopian_components: dualInfo.ethiopian
      // };
    }
  };

  const setToday = () => {
    setSelectedDate(new Date());
  };

  const getCurrentEthiopianInfo = () => {
    const today = new Date();
    const ethiopianToday = getCurrentEthiopianDate();
    return {
      gregorian: today,
      ethiopian: ethiopianToday,
      formatted: {
        gregorian: today.toLocaleDateString('en-US'),
        ethiopian: formatEthiopianDate(ethiopianToday, 'DD/MM/YYYY', 'en'),
        ethiopianLong: formatEthiopianDate(ethiopianToday, 'DD MMM YYYY', 'en')
      }
    };
  };

  const todayInfo = getCurrentEthiopianInfo();
  const selectedInfo = selectedDate ? adapter.getDualCalendarInfo(selectedDate) : null;

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Ethiopian Calendar System Demo
      </Typography>
      
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>User Experience:</strong> Users see and select Ethiopian calendar dates. 
          The system automatically converts and stores both Ethiopian and Gregorian dates.
        </Typography>
      </Alert>

      <Grid container spacing={3}>
        {/* Date Picker Section */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <EventIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Select Date (Ethiopian Calendar)
              </Typography>
              
              <DualCalendarDatePicker
                label="Birth Date"
                value={selectedDate}
                onChange={handleDateChange}
                language="en"
                showBothCalendars={true}
                helperText="Select a date using Ethiopian calendar"
              />

              <Box sx={{ mt: 2 }}>
                <Button
                  variant="outlined"
                  startIcon={<TodayIcon />}
                  onClick={setToday}
                  size="small"
                >
                  Set to Today
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Information Display Section */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Calendar Information
              </Typography>

              {/* Today's Date */}
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="primary">
                  Today's Date:
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, mt: 1, flexWrap: 'wrap' }}>
                  <Chip
                    size="small"
                    label={`Ethiopian: ${todayInfo.formatted.ethiopianLong}`}
                    color="primary"
                  />
                  <SwapIcon sx={{ fontSize: 16, alignSelf: 'center' }} />
                  <Chip
                    size="small"
                    label={`Gregorian: ${todayInfo.formatted.gregorian}`}
                    color="secondary"
                  />
                </Box>
              </Box>

              <Divider sx={{ my: 2 }} />

              {/* Selected Date */}
              {selectedInfo ? (
                <Box>
                  <Typography variant="subtitle2" color="primary">
                    Selected Date:
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1, mt: 1, flexWrap: 'wrap' }}>
                    <Chip
                      size="small"
                      label={`Ethiopian: ${selectedInfo.formatted.ethiopianLong}`}
                      color="primary"
                    />
                    <SwapIcon sx={{ fontSize: 16, alignSelf: 'center' }} />
                    <Chip
                      size="small"
                      label={`Gregorian: ${selectedInfo.formatted.gregorian}`}
                      color="secondary"
                    />
                  </Box>

                  {/* Technical Details */}
                  <Box sx={{ mt: 2, p: 1, bgcolor: 'grey.50', borderRadius: 1 }}>
                    <Typography variant="caption" color="text.secondary">
                      <strong>System Storage:</strong><br />
                      Ethiopian: {selectedInfo.ethiopian.year}/{selectedInfo.ethiopian.month}/{selectedInfo.ethiopian.day}<br />
                      Gregorian: {selectedInfo.gregorian.toISOString()}<br />
                      <strong>User sees:</strong> {selectedInfo.formatted.ethiopian} (Ethiopian)<br />
                      <strong>System uses:</strong> Both calendars as needed
                    </Typography>
                  </Box>
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  Select a date to see the dual calendar information
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Usage Instructions */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                How It Works
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <EventIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="subtitle2">1. User Selects</Typography>
                    <Typography variant="body2" color="text.secondary">
                      User sees and selects Ethiopian calendar dates in the UI
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={4}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <SwapIcon color="secondary" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="subtitle2">2. System Converts</Typography>
                    <Typography variant="body2" color="text.secondary">
                      System automatically converts Ethiopian date to Gregorian equivalent
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={4}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <TodayIcon color="success" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="subtitle2">3. System Stores</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Both Ethiopian and Gregorian dates are stored and used as needed
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DualCalendarExample;
