#!/usr/bin/env python3
"""
Test GoID Integration with JAR Bridge
"""

import requests
import json
import time

def test_biometric_service():
    """Test the biometric service endpoints"""
    
    base_url = "http://localhost:8001"
    
    print("=== TESTING GOID INTEGRATION ===")
    print(f"Testing service at: {base_url}")
    print()
    
    # Test 1: Device Status
    print("1. Testing device status...")
    try:
        response = requests.get(f"{base_url}/api/device/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ Device status OK")
            print(f"   Service type: {data.get('service_type')}")
            print(f"   Message: {data.get('message')}")
        else:
            print(f"❌ Device status failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Device status error: {e}")
        return False
    
    print()
    
    # Test 2: Device Initialize (optional)
    print("2. Testing device initialization...")
    try:
        response = requests.post(f"{base_url}/api/device/initialize", timeout=5)
        if response.status_code == 200:
            print("✅ Device initialization OK")
        else:
            print(f"⚠️ Device initialization returned: {response.status_code}")
    except Exception as e:
        print(f"⚠️ Device initialization error: {e}")
    
    print()
    
    # Test 3: Fingerprint Capture Simulation
    print("3. Testing fingerprint capture API...")
    try:
        capture_data = {"thumb_type": "left"}
        
        print("📡 Sending capture request...")
        print("💡 This will launch the JAR application")
        print("💡 Use the JAR window to capture your fingerprint")
        print("💡 Close the JAR when done")
        
        response = requests.post(
            f"{base_url}/api/capture/fingerprint", 
            json=capture_data,
            timeout=70  # Give time for user interaction
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Fingerprint capture API OK")
            print(f"   Success: {data.get('success')}")
            print(f"   Method: {data.get('data', {}).get('method')}")
            print(f"   Quality: {data.get('data', {}).get('quality_score')}")
            print(f"   Real capture: {data.get('data', {}).get('real_capture')}")
        else:
            print(f"❌ Fingerprint capture failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Fingerprint capture error: {e}")
        return False
    
    print()
    print("🎉 ALL TESTS PASSED!")
    print("✅ JAR Bridge is ready for GoID integration")
    return True

def test_frontend_compatibility():
    """Test compatibility with GoID frontend expectations"""
    
    print("\n=== TESTING FRONTEND COMPATIBILITY ===")
    
    # Test the exact endpoints the frontend uses
    base_url = "http://localhost:8001"
    
    # Simulate frontend biometric service detection
    print("Testing frontend biometric service detection...")
    
    try:
        # This is what the frontend does to detect biometric services
        response = requests.get(f"{base_url}/api/device/status", timeout=3)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('device_connected'):
                print("✅ Frontend will detect this service as available")
                print(f"   Service type: {data.get('service_type')}")
                return True
            else:
                print("❌ Frontend will not detect this service")
                return False
        else:
            print("❌ Frontend detection will fail")
            return False
            
    except Exception as e:
        print(f"❌ Frontend compatibility test failed: {e}")
        return False

if __name__ == '__main__':
    print("GoID JAR Bridge Integration Test")
    print("=" * 40)
    
    # Test basic service
    service_ok = test_biometric_service()
    
    if service_ok:
        # Test frontend compatibility
        frontend_ok = test_frontend_compatibility()
        
        if frontend_ok:
            print("\n🎉 INTEGRATION TEST SUCCESSFUL!")
            print("✅ JAR Bridge is fully compatible with GoID")
            print("✅ Ready for production use")
        else:
            print("\n⚠️ Service works but may have frontend compatibility issues")
    else:
        print("\n❌ INTEGRATION TEST FAILED!")
        print("❌ Service is not working correctly")
    
    print("\nTest completed.")
    input("Press Enter to exit...")
