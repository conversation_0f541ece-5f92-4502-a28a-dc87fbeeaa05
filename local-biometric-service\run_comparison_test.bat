@echo off
echo ========================================
echo   DIRECT COMPARISON TEST
echo   Find the Exact Difference
echo ========================================
echo.

echo 🔍 MYSTERY: Debug script works, but ALL services crash
echo.
echo ✅ WHAT WORKS:
echo    - Standalone debug script
echo    - ftrScanGetFrame returns 1, gets 4782 bytes
echo.
echo ❌ WHAT CRASHES:
echo    - Flask services
echo    - Basic HTTP server services  
echo    - Pure standalone services
echo    - ALL crash at ftrScanGetFrame() call
echo.
echo 🎯 THIS TEST WILL:
echo    - Run EXACT debug code vs service code
echo    - Test tiny variations to isolate the issue
echo    - Find the subtle difference causing crashes
echo    - Determine if it's timing, variables, or context
echo.

echo Checking Python installation...
python --version
if errorlevel 1 (
    echo ERROR: Python not found. Please install Python 3.7+
    pause
    exit /b 1
)

echo.
echo Checking required DLL files...
if not exist "ftrScanAPI.dll" (
    echo ERROR: ftrScanAPI.dll not found
    echo Please ensure Futronic SDK files are in the current directory
    pause
    exit /b 1
)

echo SUCCESS: ftrScanAPI.dll found

echo.
echo ========================================
echo   COMPARISON STRATEGY
echo ========================================
echo.
echo This test will run:
echo.
echo 1. EXACT debug approach (that worked)
echo 2. Service approach (that crashes)
echo 3. Minimal variations (different variables, timing)
echo 4. Timing tests (delays between calls)
echo.
echo Expected outcomes:
echo   - If debug works but service fails: Find the difference
echo   - If both work: Issue is with service frameworks
echo   - If both fail: Fundamental SDK problem
echo.

echo Starting DIRECT COMPARISON TEST...
echo.

python direct_comparison_test.py

echo.
echo ========================================
echo   COMPARISON COMPLETE
echo ========================================
echo.
echo Review the results above to identify:
echo   - Which approaches work vs fail
echo   - What the exact difference is
echo   - Whether it's timing, variables, or context
echo.
echo This will tell us the ROOT CAUSE of the crashes!
echo.
pause
