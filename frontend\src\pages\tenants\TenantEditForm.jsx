import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import axios from '../../utils/axios';
import {
  Box,
  Button,
  Card,
  CardContent,
  Typography,
  TextField,
  Grid,
  CircularProgress,
  Alert,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Snackbar,
} from '@mui/material';
import {
  ArrowBack as BackIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';

const TenantEditForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [tenant, setTenant] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    type: '',
    parent: '',
  });
  const [parentOptions, setParentOptions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  useEffect(() => {
    fetchTenantDetails();
    fetchParentOptions();
  }, [id]);

  const fetchTenantDetails = async () => {
    try {
      setLoading(true);
      setError('');
      const response = await axios.get(`/api/tenants/${id}/`);
      const tenantData = response.data;
      setTenant(tenantData);
      setFormData({
        name: tenantData.name,
        type: tenantData.type,
        parent: tenantData.parent || '',
      });
    } catch (error) {
      console.error('Failed to fetch tenant details:', error);
      setError('Failed to load tenant details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchParentOptions = async () => {
    try {
      const response = await axios.get('/api/tenants/');
      const tenants = response.data.results || response.data;
      setParentOptions(Array.isArray(tenants) ? tenants : []);
    } catch (error) {
      console.error('Failed to fetch parent options:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      setSnackbar({
        open: true,
        message: 'Tenant name is required.',
        severity: 'error'
      });
      return;
    }

    try {
      setSaving(true);
      await axios.patch(`/api/tenants/${id}/`, {
        name: formData.name.trim(),
        // Note: type and parent are typically not editable after creation
      });

      setSnackbar({
        open: true,
        message: 'Tenant updated successfully!',
        severity: 'success'
      });

      // Navigate back to details page after a short delay
      setTimeout(() => {
        navigate(`/tenants/${id}/details`);
      }, 1500);

    } catch (error) {
      console.error('Failed to update tenant:', error);
      setSnackbar({
        open: true,
        message: error.response?.data?.detail || 'Failed to update tenant. Please try again.',
        severity: 'error'
      });
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    navigate(`/tenants/${id}/details`);
  };

  const handleSnackbarClose = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const getParentOptions = () => {
    if (!tenant) return [];

    // Filter parent options based on tenant type
    return parentOptions.filter(option => {
      if (tenant.type === 'subcity') {
        return option.type === 'city' && option.id !== tenant.id;
      } else if (tenant.type === 'kebele') {
        return option.type === 'subcity' && option.id !== tenant.id;
      }
      return false;
    });
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
        <Button
          variant="outlined"
          startIcon={<BackIcon />}
          onClick={() => navigate('/tenants')}
        >
          Back to Tenants
        </Button>
      </Box>
    );
  }

  if (!tenant) {
    return (
      <Box>
        <Alert severity="warning" sx={{ mb: 3 }}>
          Tenant not found.
        </Alert>
        <Button
          variant="outlined"
          startIcon={<BackIcon />}
          onClick={() => navigate('/tenants')}
        >
          Back to Tenants
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<BackIcon />}
            onClick={() => navigate(`/tenants/${id}/details`)}
          >
            Back
          </Button>
          <Typography variant="h4" component="h1">
            Edit Tenant: {tenant.name}
          </Typography>
        </Box>
      </Box>

      {/* Edit Form */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Tenant Information
          </Typography>
          <Divider sx={{ mb: 3 }} />

          <Box component="form" onSubmit={handleSubmit}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Tenant Name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                  disabled={saving}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Type"
                  value={tenant.type.charAt(0).toUpperCase() + tenant.type.slice(1)}
                  disabled
                  helperText="Tenant type cannot be changed after creation"
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Schema Name"
                  value={tenant.schema_name}
                  disabled
                  helperText="Schema name cannot be changed after creation"
                />
              </Grid>

              {tenant.type !== 'city' && (
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth disabled>
                    <InputLabel>Parent Tenant</InputLabel>
                    <Select
                      value={formData.parent}
                      label="Parent Tenant"
                      name="parent"
                    >
                      {getParentOptions().map((option) => (
                        <MenuItem key={option.id} value={option.id}>
                          {option.name} ({option.type})
                        </MenuItem>
                      ))}
                    </Select>
                    <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
                      Parent tenant cannot be changed after creation
                    </Typography>
                  </FormControl>
                </Grid>
              )}

              <Grid item xs={12}>
                <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                  <Button
                    variant="outlined"
                    startIcon={<CancelIcon />}
                    onClick={handleCancel}
                    disabled={saving}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    variant="contained"
                    startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
                    disabled={saving}
                  >
                    {saving ? 'Saving...' : 'Save Changes'}
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </CardContent>
      </Card>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default TenantEditForm;
