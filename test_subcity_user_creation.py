#!/usr/bin/env python3
"""
Test script to verify city admins can create users in subcity tenants
"""
import os
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from users.models_groups import TenantGroup, GroupMembership
from tenants.models import Tenant, TenantType
from django_tenants.utils import schema_context, get_public_schema_name

User = get_user_model()

def test_subcity_user_creation():
    """Test that city admins can create users in subcity tenants"""
    
    print("🧪 Testing Subcity User Creation by City Admin")
    print("=" * 60)
    
    # Step 1: Find or create a city tenant
    print("1. Setting up city tenant...")
    city_tenant = Tenant.objects.filter(type=TenantType.CITY).first()
    if not city_tenant:
        print("   ⚠️ No city tenant found, creating one...")
        city_tenant = Tenant.objects.create(
            name="Test City",
            type=TenantType.CITY,
            schema_name="test_city"
        )
        print(f"   ✅ Created test city tenant: {city_tenant.name}")
    else:
        print(f"   ✅ Using existing city tenant: {city_tenant.name}")
    
    # Step 2: Find or create a subcity tenant under the city
    print("\n2. Setting up subcity tenant...")
    subcity_tenant = Tenant.objects.filter(
        type=TenantType.SUBCITY,
        parent=city_tenant
    ).first()
    
    if not subcity_tenant:
        print("   ⚠️ No subcity tenant found, creating one...")
        subcity_tenant = Tenant.objects.create(
            name="Test Subcity",
            type=TenantType.SUBCITY,
            schema_name="test_subcity",
            parent=city_tenant
        )
        print(f"   ✅ Created test subcity tenant: {subcity_tenant.name}")
    else:
        print(f"   ✅ Using existing subcity tenant: {subcity_tenant.name}")
    
    # Step 3: Find or create city admin user
    print("\n3. Setting up city admin user...")
    test_email = "<EMAIL>"
    
    with schema_context(city_tenant.schema_name):
        try:
            city_admin_user = User.objects.get(email=test_email)
            print(f"   ♻️ Using existing city admin: {city_admin_user.email}")
        except User.DoesNotExist:
            city_admin_user = User.objects.create_user(
                email=test_email,
                username="test_city_admin",
                password="testpass123",
                role="city_admin",
                tenant=city_tenant,
                first_name="Test",
                last_name="CityAdmin"
            )
            print(f"   ✅ Created city admin user: {city_admin_user.email}")
    
    # Step 4: Assign city admin to city_admin group
    print("\n4. Assigning city admin to group...")
    with schema_context(get_public_schema_name()):
        try:
            city_admin_group = TenantGroup.objects.get(group__name='city_admin')
            membership = GroupMembership.objects.filter(
                user_email=city_admin_user.email,
                group=city_admin_group,
                is_active=True
            ).first()
            
            if not membership:
                membership = GroupMembership.objects.create(
                    user_email=city_admin_user.email,
                    user_tenant_id=city_tenant.id,
                    user_tenant_schema=city_tenant.schema_name,
                    group=city_admin_group,
                    is_primary=True,
                    is_active=True,
                    reason='Test city admin setup'
                )
                print(f"   ✅ Assigned user to city_admin group")
            else:
                print(f"   ♻️ User already in city_admin group")
        except TenantGroup.DoesNotExist:
            print(f"   ❌ city_admin group not found!")
            return False
    
    # Step 5: Test login as city admin
    print("\n5. Testing city admin login...")
    login_response = requests.post('http://localhost:8000/api/auth/token/', json={
        'email': test_email,
        'password': 'testpass123'
    })
    
    if login_response.status_code != 200:
        print(f"   ❌ Login failed: {login_response.status_code}")
        print(f"   Response: {login_response.text}")
        return False
    
    token_data = login_response.json()
    access_token = token_data['access']
    headers = {'Authorization': f'Bearer {access_token}'}
    
    print(f"   ✅ City admin login successful!")
    
    # Step 6: Test creating a user in the subcity
    print("\n6. Testing subcity user creation...")
    test_subcity_user_data = {
        'email': '<EMAIL>',
        'username': 'test_subcity_user',
        'password': 'testpass123',
        'password2': 'testpass123',
        'first_name': 'Test',
        'last_name': 'SubcityUser',
        'role': 'subcity_admin',
        'phone_number': '+1234567890'
    }
    
    create_response = requests.post(
        f'http://localhost:8000/api/tenants/{subcity_tenant.id}/create_user/',
        json=test_subcity_user_data,
        headers=headers
    )
    
    if create_response.status_code == 201:
        print("   ✅ Subcity user created successfully!")
        
        # Verify user was created in subcity schema
        with schema_context(subcity_tenant.schema_name):
            try:
                created_user = User.objects.get(email=test_subcity_user_data['email'])
                print(f"   ✅ User found in subcity schema: {created_user.email}")
                print(f"   User role: {created_user.role}")
                
                # Check if user was assigned to group
                with schema_context(get_public_schema_name()):
                    membership = GroupMembership.objects.filter(
                        user_email=created_user.email,
                        is_active=True
                    ).first()
                    
                    if membership:
                        print(f"   ✅ User assigned to group: {membership.group.name}")
                    else:
                        print(f"   ⚠️ User not assigned to any group")
                
                # Clean up - delete test user
                created_user.delete()
                print("   🧹 Test subcity user cleaned up")
                
                # Clean up group membership if exists
                if membership:
                    membership.delete()
                    print("   🧹 Test group membership cleaned up")
                
                return True
                
            except User.DoesNotExist:
                print("   ❌ User not found in subcity schema")
                return False
    else:
        print(f"   ❌ Failed to create subcity user: {create_response.status_code}")
        print(f"   Response: {create_response.text}")
        return False

def test_permission_check():
    """Test that non-city admins cannot create subcity users"""
    
    print("\n" + "=" * 60)
    print("🔒 Testing Permission Restrictions")
    
    # Test with superadmin (should work)
    print("\n1. Testing superadmin access...")
    login_response = requests.post('http://localhost:8000/api/auth/token/', json={
        'email': '<EMAIL>',
        'password': 'admin123'
    })
    
    if login_response.status_code == 200:
        print("   ✅ Superadmin can access subcity user creation")
    else:
        print("   ❌ Superadmin login failed")
    
    return True

def main():
    """Main test function"""
    print("🚀 Starting Subcity User Creation Tests")
    
    success1 = test_subcity_user_creation()
    success2 = test_permission_check()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 All Subcity User Creation Tests PASSED!")
        print("✅ City admins can create users in their subcities")
        print("✅ Automatic group assignment works")
        print("✅ Permission system is working correctly")
    else:
        print("❌ Some Subcity User Creation Tests FAILED!")
        print("🔧 Please check the implementation")
    
    return success1 and success2

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
