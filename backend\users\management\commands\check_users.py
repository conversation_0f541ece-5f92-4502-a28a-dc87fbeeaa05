from django.core.management.base import BaseCommand
from users.models import User
from tenants.models import Tenant


class Command(BaseCommand):
    help = 'Check existing users and their roles'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('=== EXISTING USERS ==='))
        
        users = User.objects.all()
        for user in users:
            self.stdout.write(f"Email: {user.email}")
            self.stdout.write(f"  Role: {user.role}")
            self.stdout.write(f"  Tenant: {user.tenant}")
            self.stdout.write(f"  Is Superuser: {user.is_superuser}")
            self.stdout.write(f"  Is Active: {user.is_active}")
            self.stdout.write("---")
        
        self.stdout.write(self.style.SUCCESS('=== EXISTING TENANTS ==='))
        
        tenants = Tenant.objects.all()
        for tenant in tenants:
            self.stdout.write(f"Name: {tenant.name}")
            self.stdout.write(f"  Type: {tenant.type}")
            self.stdout.write(f"  Schema: {tenant.schema_name}")
            self.stdout.write(f"  Parent: {tenant.parent}")
            self.stdout.write("---")
