
#!/bin/bash

# Fix IP-Related Tenant List Issue Script
# This script fixes the specific issue where tenant list works in Django admin but not in frontend after IP change

echo "🔧 Fixing IP-Related Tenant List Issue"
echo "======================================"

echo "📍 Issue Summary:"
echo "- ✅ Tenants exist in database (visible in Django admin)"
echo "- ✅ Network connectivity works"
echo "- ✅ Authentication works"
echo "- ❌ Frontend tenant list not fetching after IP change"
echo ""

# Step 1: Test the specific CORS issue
echo "📍 Step 1: Testing IP-related CORS issue..."
python fix_ip_cors_tenant_issue.py

# Step 2: Clear browser cache and restart services
echo ""
echo "📍 Step 2: Clearing cache and restarting services..."

echo "Restarting backend to ensure CORS settings are applied..."
docker-compose restart backend
sleep 20

echo "Restarting frontend to clear any cached requests..."
docker-compose restart frontend
sleep 20

# Step 3: Test the API endpoint directly
echo ""
echo "📍 Step 3: Testing API endpoint directly..."

echo "Getting authentication token..."
AUTH_RESPONSE=$(curl -s -X POST http://************:8000/api/auth/token/ \
  -H "Content-Type: application/json" \
  -H "Origin: http://************:3000" \
  -d '{"email":"<EMAIL>","password":"admin123"}')

if echo "$AUTH_RESPONSE" | grep -q "access"; then
    echo "✅ Authentication successful with CORS headers"
    
    # Extract token
    TOKEN=$(echo "$AUTH_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin)['access'])" 2>/dev/null)
    
    if [ ! -z "$TOKEN" ]; then
        echo "Testing tenant endpoint with CORS headers..."
        
        # Test with exact headers the frontend would send
        TENANT_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
          -H "Content-Type: application/json" \
          -H "Origin: http://************:3000" \
          -H "Referer: http://************:3000/tenants" \
          http://************:8000/api/tenants/)
        
        echo "Tenant endpoint response:"
        if echo "$TENANT_RESPONSE" | python3 -m json.tool 2>/dev/null; then
            echo "✅ Tenant endpoint returning valid JSON data"
            
            # Count tenants
            TENANT_COUNT=$(echo "$TENANT_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, list):
        print(len(data))
    elif isinstance(data, dict) and 'results' in data:
        print(len(data.get('results', [])))
    else:
        print('0')
except:
    print('0')
" 2>/dev/null)
            
            echo "Found $TENANT_COUNT tenants in API response"
            
            if [ "$TENANT_COUNT" -gt "0" ]; then
                echo "✅ API is returning tenant data correctly"
            else
                echo "❌ API returning empty data despite tenants existing in database"
            fi
        else
            echo "❌ Invalid response from tenant endpoint:"
            echo "$TENANT_RESPONSE"
        fi
    else
        echo "❌ Could not extract token"
    fi
else
    echo "❌ Authentication failed"
    echo "Response: $AUTH_RESPONSE"
fi

# Step 4: Test CORS preflight specifically
echo ""
echo "📍 Step 4: Testing CORS preflight specifically..."

echo "Testing CORS preflight for tenant endpoint..."
PREFLIGHT_RESPONSE=$(curl -s -X OPTIONS http://************:8000/api/tenants/ \
  -H "Origin: http://************:3000" \
  -H "Access-Control-Request-Method: GET" \
  -H "Access-Control-Request-Headers: authorization,content-type" \
  -w "HTTP_STATUS:%{http_code}" \
  -o /tmp/preflight_response.txt)

PREFLIGHT_STATUS=$(echo "$PREFLIGHT_RESPONSE" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
echo "CORS preflight status: $PREFLIGHT_STATUS"

if [ "$PREFLIGHT_STATUS" = "200" ]; then
    echo "✅ CORS preflight successful"
    echo "CORS headers in response:"
    cat /tmp/preflight_response.txt
else
    echo "❌ CORS preflight failed"
    echo "Response:"
    cat /tmp/preflight_response.txt
fi

# Step 5: Check frontend environment variables
echo ""
echo "📍 Step 5: Checking frontend configuration..."

echo "Checking frontend environment variables..."
docker-compose exec frontend printenv | grep -E "VITE_|NODE_" || echo "No VITE_ or NODE_ variables found"

echo ""
echo "Expected frontend configuration:"
echo "- VITE_API_URL should be: http://************:8000"
echo "- Frontend should be accessible at: http://************:3000"

# Step 6: Browser cache clearing instructions
echo ""
echo "📍 Step 6: Browser cache clearing (CRITICAL)..."

echo "🚨 IMPORTANT: Clear browser cache completely!"
echo ""
echo "Method 1 - Hard Refresh:"
echo "1. Open http://************:3000/"
echo "2. Press Ctrl+Shift+R (or Cmd+Shift+R on Mac)"
echo "3. This bypasses cache for current page"
echo ""
echo "Method 2 - Clear All Cache:"
echo "1. Open browser settings"
echo "2. Go to Privacy/Security"
echo "3. Clear browsing data"
echo "4. Select 'All time' and check all boxes"
echo "5. Clear data"
echo ""
echo "Method 3 - Incognito Mode:"
echo "1. Open incognito/private window"
echo "2. Navigate to http://************:3000/"
echo "3. Test if tenant list loads"
echo ""

# Step 7: Final verification
echo "📍 Step 7: Final verification steps..."

echo ""
echo "✅ IP-related tenant list fix completed!"
echo ""
echo "🧪 Manual testing checklist:"
echo "1. ✅ Clear browser cache (CRITICAL)"
echo "2. ✅ Open: http://************:3000/"
echo "3. ✅ Login with: <EMAIL> / admin123"
echo "4. ✅ Navigate to: Tenants section"
echo "5. ✅ Check: Should see list of tenants"
echo ""
echo "🔍 Expected results:"
echo "- ✅ Login works without errors"
echo "- ✅ Tenant list page loads"
echo "- ✅ Shows tenants from database"
echo "- ✅ No ERR_EMPTY_RESPONSE errors"
echo "- ✅ No CORS errors in console"
echo ""
echo "💡 If still not working:"
echo "1. Check browser console (F12) for specific errors"
echo "2. Check Network tab for failed requests"
echo "3. Try incognito mode to bypass cache"
echo "4. Verify CORS headers in network responses"
echo ""
echo "🚨 Most likely remaining issue:"
echo "- Browser cached the failed requests from before"
echo "- Solution: Clear cache completely or use incognito mode"
echo ""
echo "🔧 Debug commands:"
echo "- Test API: curl -H \"Authorization: Bearer TOKEN\" http://************:8000/api/tenants/"
echo "- Check logs: docker-compose logs backend | grep tenant"
echo "- Verify CORS: Check Access-Control-Allow-Origin header in response"
