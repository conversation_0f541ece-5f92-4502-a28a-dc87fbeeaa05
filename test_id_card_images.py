#!/usr/bin/env python3
"""
Test ID Card Image Display

This script tests the ID card image display issues and provides solutions.
"""

import os
import requests
from datetime import datetime


def test_logo_file():
    """Test if the logo file exists and is accessible"""
    print("🖼️ TESTING LOGO FILE")
    print("-" * 50)
    
    logo_path = "frontend/public/images/gondar-logo.svg"
    
    if os.path.exists(logo_path):
        print(f"   ✅ Logo file exists: {logo_path}")
        
        # Check file size
        file_size = os.path.getsize(logo_path)
        print(f"   📏 File size: {file_size} bytes")
        
        # Check if it's a valid SVG
        with open(logo_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if content.startswith('<svg'):
                print("   ✅ Valid SVG format")
            else:
                print("   ❌ Invalid SVG format")
                
        return True
    else:
        print(f"   ❌ Logo file missing: {logo_path}")
        return False


def test_frontend_server():
    """Test if frontend server can serve the logo"""
    print("\n🌐 TESTING FRONTEND SERVER")
    print("-" * 50)
    
    try:
        # Test if frontend server is running
        response = requests.get('http://localhost:3000', timeout=5)
        
        if response.status_code == 200:
            print("   ✅ Frontend server is running")
            
            # Test logo accessibility
            logo_response = requests.get('http://localhost:3000/images/gondar-logo.svg', timeout=5)
            
            if logo_response.status_code == 200:
                print("   ✅ Logo is accessible via frontend server")
                print(f"   📏 Logo response size: {len(logo_response.content)} bytes")
                return True
            else:
                print(f"   ❌ Logo not accessible: HTTP {logo_response.status_code}")
                return False
        else:
            print(f"   ❌ Frontend server error: HTTP {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ❌ Frontend server not running on port 3000")
        print("   💡 Start with: cd frontend && npm start")
        return False
    except Exception as e:
        print(f"   ❌ Frontend server test error: {e}")
        return False


def test_backend_media_files():
    """Test backend media files for signature and pattern"""
    print("\n📁 TESTING BACKEND MEDIA FILES")
    print("-" * 50)
    
    try:
        # Test if backend is running
        response = requests.get('http://localhost:8000/api/health/', timeout=5)
        
        if response.status_code in [200, 404]:  # 404 is ok, means server is running
            print("   ✅ Backend server is running")
            
            # Test media file access
            test_urls = [
                'http://localhost:8000/media/',
                'http://localhost:8000/static/',
            ]
            
            for url in test_urls:
                try:
                    media_response = requests.get(url, timeout=5)
                    if media_response.status_code in [200, 403, 404]:  # These are all ok
                        print(f"   ✅ Media endpoint accessible: {url}")
                    else:
                        print(f"   ⚠️ Media endpoint: {url} - HTTP {media_response.status_code}")
                except Exception as e:
                    print(f"   ❌ Media endpoint error: {url} - {e}")
            
            return True
        else:
            print(f"   ❌ Backend server error: HTTP {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ❌ Backend server not running on port 8000")
        print("   💡 Start with: cd backend && python manage.py runserver")
        return False
    except Exception as e:
        print(f"   ❌ Backend server test error: {e}")
        return False


def check_id_card_template():
    """Check ID card template configuration"""
    print("\n📋 CHECKING ID CARD TEMPLATE")
    print("-" * 50)
    
    template_path = "frontend/src/components/idcards/IDCardTemplate.jsx"
    
    if os.path.exists(template_path):
        print(f"   ✅ Template file exists: {template_path}")
        
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # Check for key elements
            checks = [
                ('Logo path', 'gondar-logo.svg' in content),
                ('Signature display', 'mayorSignature' in content),
                ('Pattern image', 'subcityPatternImage' in content),
                ('Amharic header', 'በ{cityNameAm' in content),
                ('CORS support', 'crossOrigin="anonymous"' in content),
            ]
            
            for check_name, check_result in checks:
                if check_result:
                    print(f"   ✅ {check_name}: Found")
                else:
                    print(f"   ❌ {check_name}: Missing")
        
        return True
    else:
        print(f"   ❌ Template file missing: {template_path}")
        return False


def provide_solutions():
    """Provide solutions for common issues"""
    print("\n🔧 SOLUTIONS FOR COMMON ISSUES")
    print("-" * 50)
    
    print("1. **Logo not displaying:**")
    print("   - Ensure frontend server is running: cd frontend && npm start")
    print("   - Check browser console for 404 errors")
    print("   - Verify logo file exists: frontend/public/images/gondar-logo.svg")
    print()
    
    print("2. **Signature not displaying:**")
    print("   - Ensure backend server is running: cd backend && python manage.py runserver")
    print("   - Check if tenant has mayor_signature uploaded")
    print("   - Verify CORS settings in Django")
    print("   - Check browser network tab for failed requests")
    print()
    
    print("3. **Pattern image not displaying:**")
    print("   - Ensure subcity admin has approved the ID card")
    print("   - Check if tenant has pattern_image uploaded")
    print("   - Verify user has subcity role or ID is approved")
    print()
    
    print("4. **Amharic names not showing:**")
    print("   - Check if tenant has Amharic names configured")
    print("   - Verify API calls are successful")
    print("   - Check browser console for JavaScript errors")
    print()
    
    print("5. **CORS Issues:**")
    print("   - Add crossOrigin='anonymous' to img tags")
    print("   - Configure Django CORS settings")
    print("   - Use full URLs for media files")
    print()


def show_debugging_steps():
    """Show debugging steps"""
    print("\n🔍 DEBUGGING STEPS")
    print("-" * 50)
    
    print("1. **Open Browser Developer Tools:**")
    print("   - Press F12 or right-click → Inspect")
    print("   - Go to Console tab")
    print("   - Look for error messages")
    print()
    
    print("2. **Check Network Tab:**")
    print("   - Go to Network tab in developer tools")
    print("   - Reload the ID card page")
    print("   - Look for failed requests (red entries)")
    print("   - Check if images are loading (200 status)")
    print()
    
    print("3. **Check Console Logs:**")
    print("   - Look for messages starting with:")
    print("     - ✅ Logo loaded successfully")
    print("     - ✅ Mayor signature loaded successfully")
    print("     - 🔍 Subcity pattern image set")
    print("     - 🔍 Amharic names updated successfully")
    print()
    
    print("4. **Test Individual Components:**")
    print("   - Test logo: http://localhost:3000/images/gondar-logo.svg")
    print("   - Test backend media: http://localhost:8000/media/")
    print("   - Check ID card preview vs print mode")
    print()


def main():
    """Main test function"""
    print("🧪 ID CARD IMAGE DISPLAY TEST")
    print("=" * 80)
    print(f"Test started at: {datetime.now().isoformat()}")
    print()
    
    # Run tests
    logo_ok = test_logo_file()
    frontend_ok = test_frontend_server()
    backend_ok = test_backend_media_files()
    template_ok = check_id_card_template()
    
    # Summary
    print("\n📊 TEST SUMMARY")
    print("=" * 50)
    print(f"✅ Logo File: {logo_ok}")
    print(f"✅ Frontend Server: {frontend_ok}")
    print(f"✅ Backend Server: {backend_ok}")
    print(f"✅ Template Configuration: {template_ok}")
    
    if all([logo_ok, frontend_ok, backend_ok, template_ok]):
        print("\n🎉 ALL TESTS PASSED!")
        print("Images should be displaying correctly on ID cards.")
        print()
        print("🔍 If images still not showing:")
        print("1. Check browser console for errors")
        print("2. Verify tenant has uploaded signature/pattern images")
        print("3. Ensure ID card is approved by subcity admin")
    else:
        print("\n⚠️ SOME TESTS FAILED")
        print("Please fix the issues above and try again.")
    
    # Show solutions and debugging steps
    provide_solutions()
    show_debugging_steps()
    
    print("\n🎯 EXPECTED BEHAVIOR")
    print("=" * 50)
    print("✅ Logo: Gondar city logo should appear in top-left")
    print("✅ Signature: Mayor signature should appear in bottom-right (when approved)")
    print("✅ Pattern: Subcity pattern should overlay the card (when approved)")
    print("✅ Header: Should show Amharic text: በጎንደር ከተማ አስተዳደር በዞብል ክ/ከተማ የቀበሌ14 ቀበሌ")
    print("✅ Subheader: Should show: የነዋሪዎች መታወቂያ ካርድ")


if __name__ == '__main__':
    main()
