#!/usr/bin/env python3
"""
Test script to verify automatic admin group assignment functionality
"""
import os
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from users.models_groups import TenantGroup, GroupMembership
from tenants.models import Tenant, TenantType
from django_tenants.utils import schema_context, get_public_schema_name

User = get_user_model()

def test_admin_group_assignment():
    """Test that admin users are automatically assigned to appropriate groups"""
    
    print("🧪 Testing Admin Group Assignment")
    print("=" * 50)
    
    # Step 1: Check if groups exist
    print("1. Checking if admin groups exist...")
    with schema_context(get_public_schema_name()):
        admin_groups = ['city_admin', 'subcity_admin', 'kebele_admin', 'kebele_leader', 'clerk']
        existing_groups = []
        
        for group_name in admin_groups:
            try:
                group = TenantGroup.objects.get(group__name=group_name)
                existing_groups.append(group_name)
                print(f"   ✅ {group_name} group exists")
            except TenantGroup.DoesNotExist:
                print(f"   ❌ {group_name} group missing")
        
        if len(existing_groups) != len(admin_groups):
            print("   ⚠️ Some groups are missing. Run setup_general_permissions.py first")
            return False
    
    # Step 2: Check existing tenants and their admin users
    print("\n2. Checking existing tenants and admin users...")
    
    tenants = Tenant.objects.all()
    print(f"   Found {tenants.count()} tenants")
    
    for tenant in tenants:
        print(f"\n   📂 Tenant: {tenant.name} ({tenant.type})")
        
        try:
            with schema_context(tenant.schema_name):
                # Find admin users in this tenant
                admin_users = User.objects.filter(
                    role__in=['city_admin', 'subcity_admin', 'kebele_admin', 'kebele_leader', 'clerk']
                )
                
                print(f"      Admin users: {admin_users.count()}")
                
                for user in admin_users:
                    print(f"      👤 {user.email} (role: {user.role})")
                    
                    # Check group membership
                    with schema_context(get_public_schema_name()):
                        memberships = GroupMembership.objects.filter(
                            user_email=user.email,
                            is_active=True
                        )
                        
                        if memberships.exists():
                            for membership in memberships:
                                print(f"         ✅ Member of: {membership.group.name}")
                        else:
                            print(f"         ❌ No group memberships found")
                            
        except Exception as e:
            print(f"      ❌ Error accessing tenant schema: {e}")
    
    # Step 3: Test creating a new admin user via API
    print("\n3. Testing new admin user creation...")
    
    # First, login as superadmin to get token
    login_response = requests.post('http://localhost:8000/api/auth/token/', json={
        'email': '<EMAIL>',
        'password': 'admin123'
    })
    
    if login_response.status_code != 200:
        print("   ❌ Failed to login as superadmin")
        return False
    
    token = login_response.json()['access']
    headers = {'Authorization': f'Bearer {token}'}
    
    # Find a city tenant to test with
    city_tenant = Tenant.objects.filter(type=TenantType.CITY).first()
    if not city_tenant:
        print("   ⚠️ No city tenant found to test with")
        return True
    
    print(f"   Testing with city tenant: {city_tenant.name}")
    
    # Create a test admin user
    test_admin_data = {
        'email': '<EMAIL>',
        'username': 'test_city_admin',
        'password': 'testpass123',
        'password2': 'testpass123',
        'first_name': 'Test',
        'last_name': 'Admin',
        'role': 'city_admin',
        'phone_number': '+1234567890'
    }
    
    create_response = requests.post(
        f'http://localhost:8000/api/tenants/{city_tenant.id}/create_admin/',
        json=test_admin_data,
        headers=headers
    )
    
    if create_response.status_code == 201:
        print("   ✅ Test admin user created successfully")
        
        # Check if user was assigned to group
        with schema_context(get_public_schema_name()):
            membership = GroupMembership.objects.filter(
                user_email=test_admin_data['email'],
                group__group__name='city_admin',
                is_active=True
            ).first()
            
            if membership:
                print("   ✅ User automatically assigned to city_admin group")
                
                # Clean up - delete test user
                with schema_context(city_tenant.schema_name):
                    try:
                        test_user = User.objects.get(email=test_admin_data['email'])
                        test_user.delete()
                        print("   🧹 Test user cleaned up")
                    except User.DoesNotExist:
                        pass
                
                # Clean up group membership
                membership.delete()
                print("   🧹 Test group membership cleaned up")
                
                return True
            else:
                print("   ❌ User was NOT assigned to city_admin group")
                return False
    else:
        print(f"   ❌ Failed to create test admin user: {create_response.status_code}")
        print(f"   Response: {create_response.text}")
        return False

def main():
    """Main test function"""
    success = test_admin_group_assignment()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Admin Group Assignment Test PASSED!")
        print("✅ New admin users will be automatically assigned to appropriate groups")
    else:
        print("❌ Admin Group Assignment Test FAILED!")
        print("🔧 Please check the setup and try again")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
