from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework_simplejwt.views import TokenRefreshView
from .views import UserViewSet, CustomTokenObtainPairView
from .views_permissions import (
    PermissionViewSet, RolePermissionViewSet, UserPermissionViewSet,
    UserPermissionSummaryViewSet
)
from .views_roles import RoleViewSet, UserRoleManagementViewSet, TenantRoleManagementViewSet
from .views_groups import GroupManagementViewSet

router = DefaultRouter()
router.register(r'users', UserViewSet)
router.register(r'permissions', PermissionViewSet)
router.register(r'role-permissions', RolePermissionViewSet)
router.register(r'user-permissions', UserPermissionViewSet)
router.register(r'user-permission-summary', UserPermissionSummaryViewSet)
router.register(r'roles', RoleViewSet)
router.register(r'role-management', UserRoleManagementViewSet, basename='role-management')
router.register(r'tenant-role-management', TenantRoleManagementViewSet, basename='tenant-role-management')
router.register(r'group-management', GroupManagementViewSet, basename='group-management')

urlpatterns = [
    path('', include(router.urls)),
    path('token/', CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
]
