@echo off
echo ========================================
echo   AUTO-START JAR BRIDGE SERVICE
echo   Production Ready Biometric Service
echo ========================================
echo.

echo Starting JAR Bridge Service for GoID...
echo.

REM Change to the correct directory
cd /d "%~dp0"

REM Start the service in the background
echo Starting biometric service...
start /min python working_jar_bridge.py

echo.
echo ✅ JAR Bridge Service started successfully!
echo.
echo Service Details:
echo   - URL: http://localhost:8001
echo   - Status: Running in background
echo   - Using: GonderFingerPrint.jar
echo.
echo The service is now ready for GoID integration.
echo.

REM Wait a moment for service to start
timeout /t 3 /nobreak >nul

REM Test if service is responding
echo Testing service connectivity...
curl -s http://localhost:8001/api/device/status >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Service is responding correctly
) else (
    echo ⚠️ Service may still be starting...
)

echo.
echo Service is ready for use with GoID!
pause
