from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from tenants.models import SystemSettings
from tenants.serializers.system_settings_serializer import SystemSettingsSerializer


class SystemSettingsViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing global system settings.
    Only public tenant superadmins can access these settings.
    """
    queryset = SystemSettings.objects.all()
    serializer_class = SystemSettingsSerializer
    permission_classes = [permissions.AllowAny]  # Allow public access to read settings
    
    def get_queryset(self):
        """Return the singleton SystemSettings instance."""
        return SystemSettings.objects.filter(id=1)
    
    def list(self, request):
        """Get the current system settings."""
        settings = SystemSettings.get_settings()
        serializer = self.get_serializer(settings)
        return Response(serializer.data)
    
    def retrieve(self, request, pk=None):
        """Get the current system settings (same as list)."""
        settings = SystemSettings.get_settings()
        serializer = self.get_serializer(settings)
        return Response(serializer.data)
    
    def create(self, request):
        """Not allowed - settings are singleton."""
        return Response(
            {"detail": "Cannot create new system settings. Use update instead."},
            status=status.HTTP_405_METHOD_NOT_ALLOWED
        )
    
    def destroy(self, request, pk=None):
        """Not allowed - settings cannot be deleted."""
        return Response(
            {"detail": "Cannot delete system settings."},
            status=status.HTTP_405_METHOD_NOT_ALLOWED
        )
    
    def update(self, request, pk=None):
        """Update system settings."""
        # Check permissions
        if not self._has_permission(request.user):
            return Response(
                {"detail": "Only public tenant superadmins can update system settings."},
                status=status.HTTP_403_FORBIDDEN
            )
        
        settings = SystemSettings.get_settings()
        serializer = self.get_serializer(settings, data=request.data, partial=True)
        
        if serializer.is_valid():
            serializer.save(updated_by=request.user)
            return Response(serializer.data)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def partial_update(self, request, pk=None):
        """Partial update system settings."""
        return self.update(request, pk)
    
    @action(detail=False, methods=['patch'])
    def update_theme(self, request):
        """Update theme colors."""
        # Check permissions
        if not self._has_permission(request.user):
            return Response(
                {"detail": "Only public tenant superadmins can update system theme."},
                status=status.HTTP_403_FORBIDDEN
            )
        
        settings = SystemSettings.get_settings()
        
        # Validate color format
        primary_color = request.data.get('primary_color')
        secondary_color = request.data.get('secondary_color')
        
        if primary_color and not self._is_valid_hex_color(primary_color):
            return Response(
                {"detail": "Primary color must be a valid hex color (e.g., #ff8f00)."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if secondary_color and not self._is_valid_hex_color(secondary_color):
            return Response(
                {"detail": "Secondary color must be a valid hex color (e.g., #ef6c00)."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update colors
        if primary_color:
            settings.primary_color = primary_color
        if secondary_color:
            settings.secondary_color = secondary_color
        
        settings.updated_by = request.user
        settings.save()
        
        serializer = self.get_serializer(settings)
        return Response(serializer.data)
    
    @action(detail=False, methods=['patch'])
    def update_public_access(self, request):
        """Update public services access."""
        # Check permissions
        if not self._has_permission(request.user):
            return Response(
                {"detail": "Only public tenant superadmins can update public access settings."},
                status=status.HTTP_403_FORBIDDEN
            )
        
        settings = SystemSettings.get_settings()
        
        # Update public services setting
        if 'public_services_enabled' in request.data:
            settings.public_services_enabled = request.data['public_services_enabled']
        
        settings.updated_by = request.user
        settings.save()
        
        serializer = self.get_serializer(settings)
        return Response(serializer.data)
    
    def _has_permission(self, user):
        """
        Check if user has permission to update system settings.
        Only public tenant superadmins are allowed.
        """
        print(f"🔍 SYSTEM_SETTINGS Permission Check:")
        print(f"  User: {user}")
        print(f"  User email: {user.email}")
        print(f"  User is_superuser: {user.is_superuser}")
        print(f"  User role: {getattr(user, 'role', 'NO_ROLE')}")
        print(f"  User tenant: {getattr(user, 'tenant', 'NO_TENANT')}")
        print(f"  User tenant type: {getattr(getattr(user, 'tenant', None), 'type', 'NO_TYPE')}")
        
        # Must be superuser or superadmin role
        if not (user.is_superuser or user.role == 'superadmin'):
            print(f"❌ Not superuser/superadmin")
            return False
        
        # Must be from public tenant
        if hasattr(user, 'tenant') and user.tenant:
            if user.tenant.type != 'public':
                print(f"❌ Not from public tenant (tenant type: {user.tenant.type})")
                return False
        else:
            # If no tenant, assume it's a public tenant superadmin
            print(f"✅ No tenant - assuming public tenant superadmin")
            pass
        
        print(f"✅ Permission granted")
        return True
    
    def _is_valid_hex_color(self, color):
        """Validate hex color format."""
        import re
        pattern = r'^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$'
        return bool(re.match(pattern, color))
