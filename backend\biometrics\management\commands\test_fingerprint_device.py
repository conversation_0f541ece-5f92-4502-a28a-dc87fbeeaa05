"""
Management command to test fingerprint device functionality.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from biometrics.device_integration import get_device
from biometrics.fingerprint_processing import FingerprintProcessor


class Command(BaseCommand):
    help = 'Test fingerprint device functionality'

    def add_arguments(self, parser):
        parser.add_argument(
            '--capture',
            action='store_true',
            help='Test fingerprint capture functionality',
        )
        parser.add_argument(
            '--validate',
            action='store_true',
            help='Test fingerprint validation functionality',
        )
        parser.add_argument(
            '--match',
            action='store_true',
            help='Test fingerprint matching functionality',
        )
        parser.add_argument(
            '--all',
            action='store_true',
            help='Run all tests',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🔍 Testing Futronic FS88H Fingerprint Device Integration')
        )
        self.stdout.write('=' * 60)

        if options['all'] or not any([options['capture'], options['validate'], options['match']]):
            self.test_device_status()
            self.test_device_initialization()
            self.test_fingerprint_capture()
            self.test_fingerprint_validation()
            self.test_fingerprint_matching()
            self.test_processor_functionality()
        else:
            if options['capture']:
                self.test_fingerprint_capture()
            if options['validate']:
                self.test_fingerprint_validation()
            if options['match']:
                self.test_fingerprint_matching()

        self.stdout.write(
            self.style.SUCCESS('\n✅ All tests completed successfully!')
        )

    def test_device_status(self):
        """Test device status functionality."""
        self.stdout.write('\n📋 Testing Device Status...')
        
        try:
            device = get_device()
            status = device.get_device_status()
            
            self.stdout.write(f"  Device Model: {status['model']}")
            self.stdout.write(f"  Serial Number: {status['serial_number']}")
            self.stdout.write(f"  Firmware Version: {status['firmware_version']}")
            self.stdout.write(f"  Connected: {status['connected']}")
            self.stdout.write(f"  Initialized: {status['initialized']}")
            
            self.stdout.write(self.style.SUCCESS('  ✅ Device status test passed'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ Device status test failed: {e}'))

    def test_device_initialization(self):
        """Test device initialization."""
        self.stdout.write('\n🔧 Testing Device Initialization...')
        
        try:
            device = get_device()
            success = device.initialize()
            
            if success:
                self.stdout.write('  Device initialized successfully')
                self.stdout.write(f"  Connection Status: {device.device_info.is_connected}")
                self.stdout.write(self.style.SUCCESS('  ✅ Device initialization test passed'))
            else:
                self.stdout.write(self.style.ERROR('  ❌ Device initialization failed'))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ Device initialization test failed: {e}'))

    def test_fingerprint_capture(self):
        """Test fingerprint capture functionality."""
        self.stdout.write('\n👆 Testing Fingerprint Capture...')
        
        try:
            device = get_device()
            device.initialize()
            
            # Test left thumb capture
            self.stdout.write('  Capturing left thumb fingerprint...')
            left_template = device.capture_fingerprint('left')
            
            self.stdout.write(f"    Thumb Type: {left_template.thumb_type}")
            self.stdout.write(f"    Quality Score: {left_template.quality_score}%")
            self.stdout.write(f"    Minutiae Count: {left_template.minutiae_count}")
            self.stdout.write(f"    Capture Time: {left_template.capture_time}")
            
            # Test right thumb capture
            self.stdout.write('  Capturing right thumb fingerprint...')
            right_template = device.capture_fingerprint('right')
            
            self.stdout.write(f"    Thumb Type: {right_template.thumb_type}")
            self.stdout.write(f"    Quality Score: {right_template.quality_score}%")
            self.stdout.write(f"    Minutiae Count: {right_template.minutiae_count}")
            
            self.stdout.write(self.style.SUCCESS('  ✅ Fingerprint capture test passed'))
            
            return left_template, right_template
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ Fingerprint capture test failed: {e}'))
            return None, None

    def test_fingerprint_validation(self):
        """Test fingerprint quality validation."""
        self.stdout.write('\n🔍 Testing Fingerprint Validation...')
        
        try:
            device = get_device()
            device.initialize()
            
            # Capture a fingerprint for validation
            template = device.capture_fingerprint('left')
            
            # Test quality validation
            is_valid, message = device.validate_quality(template)
            
            self.stdout.write(f"  Quality Valid: {is_valid}")
            self.stdout.write(f"  Validation Message: {message}")
            
            # Test with processor
            processor = FingerprintProcessor()
            validation_result = processor.validate_template_quality(template.template_data)
            
            self.stdout.write(f"  Processor Validation: {validation_result['is_valid']}")
            self.stdout.write(f"  Quality Score: {validation_result['quality_score']}%")
            self.stdout.write(f"  Minutiae Count: {validation_result['minutiae_count']}")
            
            if validation_result['issues']:
                self.stdout.write(f"  Issues: {', '.join(validation_result['issues'])}")
            
            self.stdout.write(self.style.SUCCESS('  ✅ Fingerprint validation test passed'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ Fingerprint validation test failed: {e}'))

    def test_fingerprint_matching(self):
        """Test fingerprint matching functionality."""
        self.stdout.write('\n🔗 Testing Fingerprint Matching...')
        
        try:
            device = get_device()
            device.initialize()
            
            # Capture two fingerprints
            template1 = device.capture_fingerprint('left')
            template2 = device.capture_fingerprint('right')
            
            # Test matching same template (should match)
            is_match, confidence = device.match_templates(
                template1.template_data, 
                template1.template_data
            )
            
            self.stdout.write(f"  Same Template Match: {is_match} (confidence: {confidence:.2f})")
            
            # Test matching different templates (may or may not match)
            is_match, confidence = device.match_templates(
                template1.template_data, 
                template2.template_data
            )
            
            self.stdout.write(f"  Different Template Match: {is_match} (confidence: {confidence:.2f})")
            
            self.stdout.write(self.style.SUCCESS('  ✅ Fingerprint matching test passed'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ Fingerprint matching test failed: {e}'))

    def test_processor_functionality(self):
        """Test fingerprint processor functionality."""
        self.stdout.write('\n⚙️ Testing Fingerprint Processor...')
        
        try:
            processor = FingerprintProcessor()
            
            # Test statistics
            stats = processor.get_biometric_statistics()
            
            self.stdout.write(f"  Total Citizens: {stats['total_citizens']}")
            self.stdout.write(f"  Total Biometric Records: {stats['total_biometric_records']}")
            self.stdout.write(f"  Biometric Coverage: {stats['biometric_coverage_percent']}%")
            
            fingerprint_stats = stats['fingerprint_stats']
            self.stdout.write(f"  Left Thumb Coverage: {fingerprint_stats['left_thumb_coverage_percent']}%")
            self.stdout.write(f"  Right Thumb Coverage: {fingerprint_stats['right_thumb_coverage_percent']}%")
            self.stdout.write(f"  Complete Coverage: {fingerprint_stats['complete_coverage_percent']}%")
            
            self.stdout.write(self.style.SUCCESS('  ✅ Fingerprint processor test passed'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ Fingerprint processor test failed: {e}'))
