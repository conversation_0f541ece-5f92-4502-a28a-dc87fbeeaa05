"""
<PERSON><PERSON><PERSON> to set up the database and run tests.
"""

import os
import sys
import django
import time

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.core.management import call_command


def setup_database():
    """Set up the database."""
    print("\n=== Setting up database ===\n")
    
    # Run migrations
    print("Running migrations...")
    call_command('migrate_schemas', verbosity=1)
    
    # Initialize data
    print("\nInitializing data...")
    from init_data import create_superuser, create_public_tenant, create_sample_tenants, create_sample_citizens
    create_superuser()
    create_public_tenant()
    create_sample_tenants()
    create_sample_citizens()
    
    print("\nDatabase setup completed.")


def run_tests():
    """Run API tests."""
    print("\n=== Running API tests ===\n")
    
    # Run the test client
    print("Running test client...")
    from test_api_client import main as test_client_main
    test_client_main()
    
    print("\nAPI tests completed.")


def main():
    """Set up database and run tests."""
    setup_database()
    time.sleep(1)  # Give the database a moment to settle
    run_tests()


if __name__ == '__main__':
    main()
