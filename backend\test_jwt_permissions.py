#!/usr/bin/env python
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append('/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from django_tenants.utils import schema_context
from users.serializers import CustomTokenObtainPairSerializer
from rest_framework_simplejwt.tokens import RefreshToken
import jwt
from django.conf import settings

User = get_user_model()

def test_jwt_permissions():
    print('=== Testing JWT Token Permissions ===')
    
    # Test with clerk user in kebele15
    with schema_context('kebele_kebele15'):
        try:
            user = User.objects.get(email='<EMAIL>')
            print(f'\nTesting JWT for: {user.email} (Role: {user.role})')
            
            # Test custom token serializer
            print('\n--- Custom Token Serializer Test ---')
            serializer = CustomTokenObtainPairSerializer()
            token = serializer.get_token(user)
            
            print(f'Token type: {type(token)}')
            print(f'Token payload keys: {list(token.payload.keys())}')
            
            # Check if permissions are in the token
            if 'permissions' in token.payload:
                permissions = token.payload['permissions']
                print(f'✅ Permissions found in JWT: {len(permissions)} permissions')
                print(f'First 10 permissions: {permissions[:10]}')
            else:
                print('❌ No permissions found in JWT token')
                print(f'Available keys: {list(token.payload.keys())}')
            
            # Test access token
            print('\n--- Access Token Test ---')
            access_token = str(token.access_token)
            print(f'Access token length: {len(access_token)}')
            
            # Decode the access token
            try:
                decoded = jwt.decode(access_token, settings.SECRET_KEY, algorithms=['HS256'])
                print(f'Decoded token keys: {list(decoded.keys())}')
                
                if 'permissions' in decoded:
                    permissions = decoded['permissions']
                    print(f'✅ Permissions in access token: {len(permissions)} permissions')
                    print(f'Sample permissions: {permissions[:5]}')
                else:
                    print('❌ No permissions in access token')
                    
                # Check other important fields
                print(f'User ID: {decoded.get("user_id")}')
                print(f'Email: {decoded.get("email")}')
                print(f'Role: {decoded.get("role")}')
                print(f'Tenant ID: {decoded.get("tenant_id")}')
                print(f'Tenant Name: {decoded.get("tenant_name")}')
                
            except Exception as e:
                print(f'❌ Error decoding access token: {e}')
            
        except User.DoesNotExist:
            print('<EMAIL> not found')
        except Exception as e:
            print(f'Error: {e}')
            import traceback
            traceback.print_exc()

    # Test with kebele leader user
    with schema_context('kebele_kebele15'):
        try:
            user = User.objects.get(email='<EMAIL>')
            print(f'\n\nTesting JWT for: {user.email} (Role: {user.role})')
            
            # Test custom token serializer
            serializer = CustomTokenObtainPairSerializer()
            token = serializer.get_token(user)
            
            # Check if permissions are in the token
            if 'permissions' in token.payload:
                permissions = token.payload['permissions']
                print(f'✅ Permissions found in JWT: {len(permissions)} permissions')
                
                # Check for specific permissions
                expected_permissions = ['view_citizen', 'add_citizen', 'change_citizen', 'delete_citizen', 'view_idcard', 'add_idcard']
                found_permissions = [p for p in expected_permissions if p in permissions]
                print(f'Expected permissions found: {found_permissions}')
                
                if len(found_permissions) >= 4:
                    print('✅ JWT permissions look correct for kebele leader')
                else:
                    print('❌ JWT permissions seem incomplete for kebele leader')
            else:
                print('❌ No permissions found in JWT token')
            
        except User.DoesNotExist:
            print('<EMAIL> not found')
        except Exception as e:
            print(f'Error: {e}')

if __name__ == '__main__':
    test_jwt_permissions()
