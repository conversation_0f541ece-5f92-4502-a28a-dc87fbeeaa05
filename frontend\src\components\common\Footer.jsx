import { Box, Container, Grid, Typography, Link, IconButton, Divider, useTheme, alpha } from '@mui/material';
import {
  Facebook as FacebookIcon,
  Twitter as TwitterIcon,
  LinkedIn as LinkedInIcon,
  Instagram as InstagramIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
} from '@mui/icons-material';
import { Link as RouterLink } from 'react-router-dom';

const Footer = () => {
  const theme = useTheme();
  const currentYear = new Date().getFullYear();

  const footerLinks = [
    {
      title: 'About',
      links: [
        { name: 'About Us', url: '/about' },
        { name: 'Our Mission', url: '/mission' },
        { name: 'Leadership', url: '/leadership' },
        { name: 'News', url: '/news' },
      ],
    },
    {
      title: 'Services',
      links: [
        { name: 'ID Card Issuance', url: '/services/id-cards' },
        { name: 'Citizen Registration', url: '/services/registration' },
        { name: 'Verification', url: '/services/verification' },
        { name: 'Data Management', url: '/services/data-management' },
      ],
    },
    {
      title: 'Support',
      links: [
        { name: 'Help Center', url: '/help' },
        { name: 'FAQs', url: '/faqs' },
        { name: 'Contact Us', url: '/contact' },
        { name: 'Report Issue', url: '/report-issue' },
      ],
    },
    {
      title: 'Legal',
      links: [
        { name: 'Terms of Service', url: '/terms' },
        { name: 'Privacy Policy', url: '/privacy' },
        { name: 'Data Protection', url: '/data-protection' },
        { name: 'Compliance', url: '/compliance' },
      ],
    },
  ];

  return (
    <Box
      component="footer"
      sx={{
        py: 6,
        px: 2,
        mt: 'auto',
        backgroundColor: theme.palette.mode === 'light'
          ? alpha(theme.palette.primary.main, 0.03)
          : alpha(theme.palette.background.paper, 0.2),
        borderTop: `1px solid ${theme.palette.mode === 'light'
          ? alpha(theme.palette.primary.main, 0.1)
          : alpha(theme.palette.divider, 0.2)}`,
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Background decoration */}
      <Box
        sx={{
          position: 'absolute',
          top: -100,
          right: -100,
          width: 300,
          height: 300,
          borderRadius: '50%',
          background: `radial-gradient(circle, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.primary.main, 0)} 70%)`,
          filter: 'blur(50px)',
          zIndex: 0,
        }}
      />
      <Box
        sx={{
          position: 'absolute',
          bottom: -150,
          left: -150,
          width: 400,
          height: 400,
          borderRadius: '50%',
          background: `radial-gradient(circle, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.primary.main, 0)} 70%)`,
          filter: 'blur(50px)',
          zIndex: 0,
        }}
      />

      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 1 }}>
        <Grid container spacing={4}>
          {/* Logo and description */}
          <Grid item xs={12} md={4}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Box
                component="span"
                sx={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mr: 1,
                  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                  color: 'white',
                  width: 40,
                  height: 40,
                  borderRadius: '10px',
                  fontSize: '1.5rem',
                  fontWeight: 'bold',
                }}
              >
                G
              </Box>
              <Typography
                variant="h5"
                component={RouterLink}
                to="/"
                sx={{
                  fontWeight: 'bold',
                  color: theme.palette.text.primary,
                  textDecoration: 'none',
                  letterSpacing: '-0.02em',
                }}
              >
                GoID
              </Typography>
            </Box>
            <Typography variant="body2" color="text.secondary" paragraph>
              GoID is a comprehensive digital ID management system designed to streamline citizen identification and verification processes for government agencies at all levels.
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, mb: 3 }}>
              <IconButton
                size="small"
                aria-label="facebook"
                sx={{
                  color: theme.palette.text.secondary,
                  '&:hover': {
                    color: '#1877F2',
                    backgroundColor: alpha('#1877F2', 0.1),
                  },
                }}
              >
                <FacebookIcon fontSize="small" />
              </IconButton>
              <IconButton
                size="small"
                aria-label="twitter"
                sx={{
                  color: theme.palette.text.secondary,
                  '&:hover': {
                    color: '#1DA1F2',
                    backgroundColor: alpha('#1DA1F2', 0.1),
                  },
                }}
              >
                <TwitterIcon fontSize="small" />
              </IconButton>
              <IconButton
                size="small"
                aria-label="linkedin"
                sx={{
                  color: theme.palette.text.secondary,
                  '&:hover': {
                    color: '#0A66C2',
                    backgroundColor: alpha('#0A66C2', 0.1),
                  },
                }}
              >
                <LinkedInIcon fontSize="small" />
              </IconButton>
              <IconButton
                size="small"
                aria-label="instagram"
                sx={{
                  color: theme.palette.text.secondary,
                  '&:hover': {
                    color: '#E4405F',
                    backgroundColor: alpha('#E4405F', 0.1),
                  },
                }}
              >
                <InstagramIcon fontSize="small" />
              </IconButton>
            </Box>
          </Grid>

          {/* Links */}
          {footerLinks.map((section) => (
            <Grid item xs={6} sm={3} md={2} key={section.title}>
              <Typography
                variant="subtitle2"
                color="text.primary"
                fontWeight="bold"
                gutterBottom
              >
                {section.title}
              </Typography>
              <Box component="ul" sx={{ p: 0, m: 0, listStyle: 'none' }}>
                {section.links.map((link) => (
                  <Box component="li" key={link.name} sx={{ mb: 1 }}>
                    <Link
                      component={RouterLink}
                      to={link.url}
                      underline="hover"
                      color="text.secondary"
                      sx={{
                        fontSize: '0.875rem',
                        transition: 'all 0.2s',
                        '&:hover': {
                          color: theme.palette.primary.main,
                        },
                      }}
                    >
                      {link.name}
                    </Link>
                  </Box>
                ))}
              </Box>
            </Grid>
          ))}
        </Grid>

        <Divider sx={{ my: 4, opacity: 0.2 }} />

        {/* Bottom section */}
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <Typography variant="body2" color="text.secondary">
              © {currentYear} GoID. All rights reserved.
            </Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', justifyContent: { xs: 'flex-start', md: 'flex-end' }, flexWrap: 'wrap', gap: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <EmailIcon fontSize="small" sx={{ mr: 1, color: theme.palette.text.secondary }} />
                <Typography variant="body2" color="text.secondary">
                  <EMAIL>
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <PhoneIcon fontSize="small" sx={{ mr: 1, color: theme.palette.text.secondary }} />
                <Typography variant="body2" color="text.secondary">
                  +251 11 123 4567
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <LocationIcon fontSize="small" sx={{ mr: 1, color: theme.palette.text.secondary }} />
                <Typography variant="body2" color="text.secondary">
                  Addis Ababa, Ethiopia
                </Typography>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default Footer;
