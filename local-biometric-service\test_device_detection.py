#!/usr/bin/env python3
"""
Test script to check Futronic FS88H device detection
"""

import sys
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_usb_detection():
    """Test USB device detection using pyusb"""
    try:
        import usb.core
        import usb.util
        
        # Futronic FS88H VID:PID
        VENDOR_ID = 0x1491
        PRODUCT_ID = 0x0020
        
        logger.info("🔍 Scanning for USB devices...")
        
        # Find all USB devices
        devices = usb.core.find(find_all=True)
        device_count = len(list(devices))
        logger.info(f"Found {device_count} USB devices total")
        
        # Look specifically for Futronic FS88H
        device = usb.core.find(idVendor=VENDOR_ID, idProduct=PRODUCT_ID)
        
        if device is None:
            logger.warning("❌ Futronic FS88H device not found")
            logger.info("Checking for any Futronic devices...")
            
            # Check for any Futronic devices
            futronic_devices = usb.core.find(find_all=True, idVendor=VENDOR_ID)
            futronic_list = list(futronic_devices)
            
            if futronic_list:
                logger.info(f"Found {len(futronic_list)} Futronic device(s):")
                for dev in futronic_list:
                    logger.info(f"  - VID: 0x{dev.idVendor:04x}, PID: 0x{dev.idProduct:04x}")
            else:
                logger.warning("No Futronic devices found")
            
            return False
        else:
            logger.info("✅ Futronic FS88H device detected!")
            logger.info(f"Device: {device}")
            logger.info(f"Vendor ID: 0x{device.idVendor:04x}")
            logger.info(f"Product ID: 0x{device.idProduct:04x}")
            
            try:
                # Try to get device information
                logger.info(f"Bus: {device.bus}")
                logger.info(f"Address: {device.address}")
                logger.info(f"Speed: {device.speed}")
                
                # Try to get string descriptors
                try:
                    manufacturer = usb.util.get_string(device, device.iManufacturer)
                    logger.info(f"Manufacturer: {manufacturer}")
                except:
                    logger.info("Manufacturer: Unable to read")
                
                try:
                    product = usb.util.get_string(device, device.iProduct)
                    logger.info(f"Product: {product}")
                except:
                    logger.info("Product: Unable to read")
                
            except Exception as e:
                logger.warning(f"Could not read device details: {e}")
            
            return True
            
    except ImportError:
        logger.error("❌ pyusb not installed. Run: pip install pyusb")
        return False
    except Exception as e:
        logger.error(f"❌ USB detection error: {e}")
        return False

def test_libusb_integration():
    """Test LibUSB integration"""
    try:
        logger.info("🔧 Testing LibUSB integration...")
        
        from libusb_futronic import LibUSBFutronicFS88H
        
        device = LibUSBFutronicFS88H()
        
        if device.initialize():
            status = device.get_device_status()
            
            logger.info("📊 Device Status:")
            for key, value in status.items():
                logger.info(f"  {key}: {value}")
            
            if status['real_device']:
                logger.info("✅ Real device detected and working!")
                
                # Test fingerprint capture
                logger.info("🧪 Testing fingerprint capture...")
                try:
                    template = device.capture_fingerprint('left')
                    if template:
                        logger.info(f"✅ Fingerprint capture successful!")
                        logger.info(f"Quality: {template.quality_score}%")
                        logger.info(f"Minutiae: {template.minutiae_count}")
                    else:
                        logger.warning("⚠️ Fingerprint capture returned None")
                except Exception as e:
                    logger.error(f"❌ Fingerprint capture failed: {e}")
                
            else:
                logger.warning("⚠️ Running in simulation mode")
                logger.warning("Real device not detected or driver issue")
            
            return status['real_device']
        else:
            logger.error("❌ Device initialization failed")
            return False
            
    except ImportError as e:
        logger.error(f"❌ LibUSB module import failed: {e}")
        logger.info("Make sure libusb_futronic.py is in the same directory")
        return False
    except Exception as e:
        logger.error(f"❌ LibUSB integration error: {e}")
        return False

def main():
    """Main test function"""
    logger.info("🚀 Futronic FS88H Device Detection Test")
    logger.info("=" * 50)
    
    # Test 1: USB detection
    logger.info("\n📋 Test 1: USB Device Detection")
    usb_detected = test_usb_detection()
    
    # Test 2: LibUSB integration
    logger.info("\n📋 Test 2: LibUSB Integration")
    libusb_working = test_libusb_integration()
    
    # Summary
    logger.info("\n📊 Test Summary")
    logger.info("=" * 30)
    logger.info(f"USB Detection: {'✅ PASS' if usb_detected else '❌ FAIL'}")
    logger.info(f"LibUSB Integration: {'✅ PASS' if libusb_working else '❌ FAIL'}")
    
    if usb_detected and libusb_working:
        logger.info("\n🎉 All tests passed! Device is ready for use.")
    elif usb_detected and not libusb_working:
        logger.info("\n⚠️ Device detected but LibUSB integration failed.")
        logger.info("This might be a driver issue. Try installing LibUSB driver with Zadig.")
    elif not usb_detected:
        logger.info("\n❌ Device not detected. Please check:")
        logger.info("1. Device is connected via USB")
        logger.info("2. Device drivers are installed")
        logger.info("3. Device is not being used by another application")
    
    return usb_detected and libusb_working

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
