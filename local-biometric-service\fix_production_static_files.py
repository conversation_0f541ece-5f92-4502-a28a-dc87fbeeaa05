#!/usr/bin/env python3
"""
Fix Production Static Files Issues
"""

import subprocess
import urllib.request
import time

def run_docker_command(cmd_list, description):
    """Run a docker-compose command with production config"""
    
    full_cmd = ["docker-compose", "-f", "docker-compose.production.yaml"] + cmd_list
    
    print(f"🔄 {description}...")
    print(f"   Command: {' '.join(full_cmd[3:])}")
    
    try:
        result = subprocess.run(
            full_cmd,
            capture_output=True,
            text=True,
            cwd="C:\\Users\\<USER>\\Desktop\\GoID",
            timeout=120
        )
        
        if result.returncode == 0:
            print(f"   ✅ Success")
            if result.stdout.strip():
                # Show last 300 characters of output
                output = result.stdout.strip()
                if len(output) > 300:
                    print(f"   Output: ...{output[-300:]}")
                else:
                    print(f"   Output: {output}")
            return True, result.stdout
        else:
            print(f"   ❌ Error: {result.stderr}")
            return False, result.stderr
            
    except subprocess.TimeoutExpired:
        print(f"   ⏰ Command timed out")
        return False, "Timeout"
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return False, str(e)

def check_container_status():
    """Check production container status"""
    
    print("🐳 Checking Production Container Status...")
    print("=" * 45)
    
    success, output = run_docker_command(["ps"], "Checking container status")
    
    if success and "backend" in output and "Up" in output:
        print("✅ Backend container is running")
        return True
    else:
        print("❌ Backend container not running properly")
        print("💡 Try: docker-compose -f docker-compose.production.yaml up -d")
        return False

def fix_static_files_comprehensive():
    """Comprehensive static files fix"""
    
    print(f"\n🔧 Comprehensive Static Files Fix...")
    print("=" * 40)
    
    # Step 1: Clear existing static files
    print("\n📁 Step 1: Clearing existing static files...")
    run_docker_command(
        ["exec", "backend", "rm", "-rf", "/app/staticfiles/*"],
        "Clearing static files"
    )
    
    # Step 2: Collect static files
    print("\n📦 Step 2: Collecting static files...")
    success, output = run_docker_command(
        ["exec", "backend", "python", "manage.py", "collectstatic", "--noinput", "--clear"],
        "Collecting static files"
    )
    
    if not success:
        print("❌ Static files collection failed!")
        return False
    
    # Step 3: Check if admin static files exist
    print("\n🔍 Step 3: Verifying admin static files...")
    success, output = run_docker_command(
        ["exec", "backend", "ls", "-la", "/app/staticfiles/admin/css/"],
        "Checking admin CSS files"
    )
    
    if success and "base.css" in output:
        print("✅ Admin CSS files found")
    else:
        print("❌ Admin CSS files missing!")
        return False
    
    # Step 4: Check Django settings
    print("\n⚙️ Step 4: Checking Django settings...")
    success, output = run_docker_command(
        ["exec", "backend", "python", "manage.py", "shell", "-c",
         "from django.conf import settings; print(f'STATIC_URL: {settings.STATIC_URL}'); print(f'STATIC_ROOT: {settings.STATIC_ROOT}'); print(f'DEBUG: {settings.DEBUG}')"],
        "Checking Django settings"
    )
    
    if success:
        print(f"   Settings: {output}")
    
    # Step 5: Check file permissions
    print("\n🔐 Step 5: Checking file permissions...")
    run_docker_command(
        ["exec", "backend", "chmod", "-R", "755", "/app/staticfiles/"],
        "Setting file permissions"
    )
    
    return True

def check_nginx_configuration():
    """Check if nginx is serving static files"""
    
    print(f"\n🌐 Checking Nginx Configuration...")
    print("=" * 35)
    
    # Check if nginx container exists
    success, output = run_docker_command(
        ["ps"], "Checking for nginx container"
    )
    
    if "nginx" in output:
        print("✅ Nginx container found")
        
        # Check nginx configuration
        success, output = run_docker_command(
            ["exec", "nginx", "nginx", "-t"],
            "Testing nginx configuration"
        )
        
        if success:
            print("✅ Nginx configuration is valid")
        else:
            print("❌ Nginx configuration has errors")
            
        # Restart nginx
        run_docker_command(
            ["restart", "nginx"],
            "Restarting nginx"
        )
        
        return True
    else:
        print("⚠️ No nginx container found - Django serving static files directly")
        return False

def test_static_file_serving():
    """Test if static files are being served correctly"""
    
    print(f"\n🧪 Testing Static File Serving...")
    print("=" * 35)
    
    # Test URLs
    test_urls = [
        ("Admin CSS", "http://localhost:8000/static/admin/css/base.css"),
        ("Admin JS", "http://localhost:8000/static/admin/js/core.js"),
        ("Admin Interface", "http://localhost:8000/admin/"),
    ]
    
    for name, url in test_urls:
        try:
            response = urllib.request.urlopen(url, timeout=10)
            content_type = response.headers.get('Content-Type', 'unknown')
            content_length = response.headers.get('Content-Length', 'unknown')
            
            print(f"✅ {name}:")
            print(f"   Status: {response.getcode()}")
            print(f"   Content-Type: {content_type}")
            print(f"   Content-Length: {content_length}")
            
            # Check if CSS is actually CSS
            if "css" in url and "text/css" not in content_type:
                print(f"   ⚠️ WARNING: CSS file has wrong MIME type!")
                
        except urllib.error.HTTPError as e:
            print(f"❌ {name}: HTTP {e.code}")
        except urllib.error.URLError as e:
            print(f"❌ {name}: Connection failed - {e}")
        except Exception as e:
            print(f"❌ {name}: Error - {e}")

def restart_production_services():
    """Restart production services"""
    
    print(f"\n🔄 Restarting Production Services...")
    print("=" * 40)
    
    services = ["backend", "nginx", "db"]
    
    for service in services:
        success, output = run_docker_command(
            ["restart", service],
            f"Restarting {service}"
        )
        
        if success:
            print(f"✅ {service} restarted")
        else:
            print(f"⚠️ {service} restart failed (may not exist)")
    
    # Wait for services to start
    print("⏰ Waiting for services to start...")
    time.sleep(15)

def create_django_static_fix():
    """Create Django settings fix for static files"""
    
    print(f"\n📝 Django Settings Fix...")
    print("=" * 25)
    
    settings_fix = '''
# Add these to your Django settings.py for production:

import os

# Static files settings
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')

# For production with nginx
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'

# If using whitenoise (alternative to nginx)
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',  # Add this
    # ... other middleware
]

# Whitenoise settings (if using)
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'
'''
    
    print("💡 Django settings that may need to be added:")
    print(settings_fix)

def main():
    """Main function"""
    
    print("GoID Production Static Files Fixer")
    print("=" * 40)
    
    print("This will fix static file serving issues in production Docker.")
    print()
    
    # Check container status
    if not check_container_status():
        return
    
    # Fix static files comprehensively
    if fix_static_files_comprehensive():
        print("✅ Static files fix completed")
    else:
        print("❌ Static files fix failed")
        return
    
    # Check nginx configuration
    has_nginx = check_nginx_configuration()
    
    # Restart services
    restart_production_services()
    
    # Test static file serving
    test_static_file_serving()
    
    # Show Django settings fix
    create_django_static_fix()
    
    print(f"\n" + "="*60)
    print("PRODUCTION STATIC FILES FIX SUMMARY")
    print("="*60)
    
    print(f"🔧 Actions Taken:")
    print(f"   ✅ Cleared and recollected static files")
    print(f"   ✅ Set proper file permissions")
    print(f"   ✅ Restarted services")
    print(f"   ✅ Tested static file serving")
    
    if has_nginx:
        print(f"   ✅ Nginx configuration checked")
    else:
        print(f"   ⚠️ No nginx found - using Django static serving")
    
    print(f"\n💡 If issues persist:")
    print(f"1. Check docker-compose.production.yaml for static volume mounts")
    print(f"2. Ensure STATIC_ROOT is correctly set in Django settings")
    print(f"3. Consider using whitenoise for static file serving")
    print(f"4. Check nginx configuration if using nginx")
    
    print(f"\n🧪 Test these URLs:")
    print(f"   http://localhost:8000/admin/ (should load with CSS)")
    print(f"   http://localhost:8000/static/admin/css/base.css (should return CSS)")
    print(f"   http://localhost:8000/api/ (should return API response)")
    
    input("\nPress Enter to exit...")

if __name__ == '__main__':
    main()
