import { useState, useEffect, useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CardContent,
  Typography,
  Grid,
  Divider,
  Alert,
  CircularProgress,
  Paper,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material';
import {
  ArrowBack as BackIcon,
  Print as PrintIcon,
  CheckCircle as CheckCircleIcon,
  QrCode as QrCodeIcon,
} from '@mui/icons-material';
import axios from '../../utils/axios';

// Mock data for ID cards
const mockIdCards = [
  {
    id: 1,
    card_number: 'ETH-KBOL-123456',
    citizen: {
      id: 1,
      first_name: '<PERSON><PERSON>',
      last_name: '<PERSON><PERSON><PERSON>',
      gender: 'male',
      date_of_birth: '1985-05-15',
      phone_number: '+251911234567',
      address: '<PERSON><PERSON>, Addis Ababa',
      nationality: 'Ethiopian',
      occupation: 'Teacher',
    },
    issue_date: '2023-11-10',
    expiry_date: '2028-11-10',
    status: 'approved',
    qr_code: 'https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=ETH-KBOL-123456',
    created_at: '2023-11-10T12:00:00Z',
  },
  {
    id: 2,
    card_number: 'ETH-KBOL-123457',
    citizen: {
      id: 2,
      first_name: 'Sara',
      last_name: 'Mohammed',
      gender: 'female',
      date_of_birth: '1990-08-22',
      phone_number: '+251922345678',
      address: 'Bole, Addis Ababa',
      nationality: 'Ethiopian',
      occupation: 'Doctor',
    },
    issue_date: '2023-11-12',
    expiry_date: '2028-11-12',
    status: 'approved',
    qr_code: 'https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=ETH-KBOL-123457',
    created_at: '2023-11-12T12:00:00Z',
  },
];

const IDCardPrint = () => {
  const { id, tenantId } = useParams(); // Support both /idcards/:id/print and /tenants/:tenantId/idcards/:id/print
  const navigate = useNavigate();
  const [idCard, setIdCard] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [printSuccess, setPrintSuccess] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const idCardRef = useRef(null);

  useEffect(() => {
    // In a real implementation, this would fetch data from the API
    // For now, we'll just use mock data
    const timer = setTimeout(() => {
      const foundIdCard = mockIdCards.find(card => card.id === parseInt(id));
      if (foundIdCard) {
        setIdCard(foundIdCard);
      } else {
        setError('ID Card not found');
      }
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [id]);

  const handlePrint = () => {
    const printContent = idCardRef.current;
    const originalContents = document.body.innerHTML;

    // In a real implementation, this would use a proper print library
    // For now, we'll just simulate printing
    document.body.innerHTML = printContent.innerHTML;
    window.print();
    document.body.innerHTML = originalContents;

    // Show confirmation dialog
    setConfirmDialogOpen(true);
  };

  const handleConfirmPrint = () => {
    setLoading(true);
    // In a real implementation, this would call an API endpoint to update the status
    setTimeout(() => {
      setIdCard(prev => ({
        ...prev,
        status: 'printed',
      }));
      setLoading(false);
      setConfirmDialogOpen(false);
      setPrintSuccess(true);
    }, 1000);
  };

  if (loading && !idCard) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ textAlign: 'center', py: 5 }}>
        <Typography variant="h5" color="error" gutterBottom>
          Error Loading ID Card
        </Typography>
        <Typography variant="body1">{error}</Typography>
        <Button
          variant="contained"
          startIcon={<BackIcon />}
          onClick={() => navigate('/idcards')}
          sx={{ mt: 3 }}
        >
          Back to ID Cards
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Button
            variant="outlined"
            startIcon={<BackIcon />}
            onClick={() => {
              // Preserve tenant context when going back
              if (tenantId) {
                navigate(`/tenants/${tenantId}/idcards/${id}`);
              } else {
                navigate(`/idcards/${id}`);
              }
            }}
            sx={{ mr: 2 }}
          >
            Back
          </Button>
          <Typography variant="h4" component="h1">
            Print ID Card
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<PrintIcon />}
          onClick={handlePrint}
          disabled={loading}
        >
          Print
        </Button>
      </Box>

      {printSuccess && (
        <Alert severity="success" sx={{ mb: 3 }}>
          ID Card has been marked as printed successfully!
        </Alert>
      )}

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          ID Card Preview
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          Review the ID card below before printing. Click the "Print" button to print the ID card.
        </Typography>

        {/* ID Card Preview */}
        <Box ref={idCardRef} sx={{ mt: 3 }}>
          <Card sx={{ maxWidth: 500, mx: 'auto', border: '1px solid #ccc' }}>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" component="div">
                  Ethiopian Digital ID
                </Typography>
                <img
                  src="https://upload.wikimedia.org/wikipedia/commons/thumb/7/71/Emblem_of_Ethiopia.svg/800px-Emblem_of_Ethiopia.svg.png"
                  alt="Ethiopian Emblem"
                  style={{ height: 40 }}
                />
              </Box>
              <Divider sx={{ mb: 2 }} />

              <Grid container spacing={2}>
                <Grid item xs={8}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Full Name
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {idCard.citizen.first_name} {idCard.citizen.last_name}
                  </Typography>

                  <Typography variant="subtitle2" color="text.secondary">
                    Date of Birth
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {new Date(idCard.citizen.date_of_birth).toLocaleDateString()}
                  </Typography>

                  <Typography variant="subtitle2" color="text.secondary">
                    Gender
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {idCard.citizen.gender === 'male' ? 'Male' : 'Female'}
                  </Typography>

                  <Typography variant="subtitle2" color="text.secondary">
                    Nationality
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {idCard.citizen.nationality}
                  </Typography>
                </Grid>

                <Grid item xs={4} sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                  <Box
                    sx={{
                      width: 100,
                      height: 120,
                      bgcolor: 'grey.300',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      mb: 1
                    }}
                  >
                    <Typography variant="body2" color="text.secondary">
                      Photo
                    </Typography>
                  </Box>
                  <img
                    src={idCard.qr_code}
                    alt="QR Code"
                    style={{ width: 80, height: 80 }}
                  />
                </Grid>
              </Grid>

              <Divider sx={{ my: 2 }} />

              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    ID Number
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {idCard.card_number}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Issue Date
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {new Date(idCard.issue_date).toLocaleDateString()}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Expiry Date
                  </Typography>
                  <Typography variant="body1">
                    {new Date(idCard.expiry_date).toLocaleDateString()}
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Box>
      </Paper>

      {/* Print Confirmation Dialog */}
      <Dialog
        open={confirmDialogOpen}
        onClose={() => setConfirmDialogOpen(false)}
      >
        <DialogTitle>Confirm Print</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Has the ID card been printed successfully?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDialogOpen(false)}>No</Button>
          <Button onClick={handleConfirmPrint} variant="contained" disabled={loading}>
            {loading ? <CircularProgress size={24} /> : 'Yes, Mark as Printed'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default IDCardPrint;
