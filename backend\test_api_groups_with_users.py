#!/usr/bin/env python
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append('/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from django_tenants.utils import schema_context
from django.test import Client
from rest_framework_simplejwt.tokens import RefreshToken
import json

User = get_user_model()

def test_groups_with_users_api():
    print('=== Testing Groups with Users API ===')
    
    # Test with subcity admin user (barbara) who can manage kebele15
    with schema_context('subcity_zoble'):
        try:
            user = User.objects.get(email='<EMAIL>')
            print(f'\nTesting API with user: {user.email} (ID: {user.id})')
            
            # Create JWT token for authentication
            refresh = RefreshToken.for_user(user)
            access_token = str(refresh.access_token)
            
            # Test with Django test client
            client = Client()
            
            # Test groups_with_users endpoint for kebele15 (ID: 8)
            print(f'\n--- Testing groups_with_users for tenant_id=8 (Kebele15) ---')
            response = client.get(
                '/api/auth/group-management/groups_with_users/',
                {'tenant_id': '8'},
                HTTP_AUTHORIZATION=f'Bearer {access_token}',
                HTTP_HOST='zoble.localhost:8000'
            )
            
            print(f'Response status: {response.status_code}')
            
            if response.status_code == 200:
                try:
                    data = json.loads(response.content)
                    print(f'✅ API working!')
                    print(f'Total groups: {data.get("total_groups", 0)}')
                    print(f'Total users: {data.get("total_users", 0)}')
                    
                    groups = data.get('groups', [])
                    for group in groups:
                        print(f'\nGroup: {group["name"]} (ID: {group["id"]})')
                        print(f'  Type: {group["group_type"]}')
                        print(f'  Level: {group["level"]}')
                        print(f'  Users: {group["users_count"]}')
                        
                        for user_data in group.get('users', []):
                            print(f'    - {user_data["email"]} (Primary: {user_data.get("is_primary", False)})')
                    
                except json.JSONDecodeError as e:
                    print(f'❌ JSON decode error: {e}')
                    print(f'Response content: {response.content}')
            else:
                print(f'❌ API failed')
                print(f'Response content: {response.content}')
                
            # Test without tenant_id filter (should show all manageable groups)
            print(f'\n--- Testing groups_with_users without tenant filter ---')
            response = client.get(
                '/api/auth/group-management/groups_with_users/',
                HTTP_AUTHORIZATION=f'Bearer {access_token}',
                HTTP_HOST='zoble.localhost:8000'
            )
            
            print(f'Response status: {response.status_code}')
            
            if response.status_code == 200:
                try:
                    data = json.loads(response.content)
                    print(f'✅ API working!')
                    print(f'Total groups: {data.get("total_groups", 0)}')
                    print(f'Total users: {data.get("total_users", 0)}')
                    
                    groups = data.get('groups', [])
                    for group in groups:
                        if group["users_count"] > 0:  # Only show groups with users
                            print(f'\nGroup: {group["name"]} (ID: {group["id"]})')
                            print(f'  Users: {group["users_count"]}')
                            
                            for user_data in group.get('users', []):
                                tenant_info = user_data.get('tenant', {})
                                tenant_name = tenant_info.get('name', 'No tenant') if tenant_info else 'No tenant'
                                print(f'    - {user_data["email"]} from {tenant_name}')
                    
                except json.JSONDecodeError as e:
                    print(f'❌ JSON decode error: {e}')
                
        except User.DoesNotExist:
            print('<EMAIL> not found')
        except Exception as e:
            print(f'Error: {e}')
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_groups_with_users_api()
