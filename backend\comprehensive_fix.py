#!/usr/bin/env python3
"""
Comprehensive fix for SubCity IntegrityError issues.
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
sys.path.append('/app')
django.setup()

from django.db import connection

def comprehensive_fix():
    """Comprehensive fix for SubCity sequence issues."""
    cursor = connection.cursor()
    
    print('=== Comprehensive SubCity Fix ===')
    
    # 1. Check ALL SubCity records (including any in different schemas)
    cursor.execute("""
        SELECT schemaname, tablename 
        FROM pg_tables 
        WHERE tablename = 'tenants_subcity'
        ORDER BY schemaname;
    """)
    schemas_with_subcity = cursor.fetchall()
    print(f'Schemas with tenants_subcity table: {len(schemas_with_subcity)}')
    for schema in schemas_with_subcity:
        print(f'  Schema: {schema[0]}')
    
    # 2. Check records in public schema (main one)
    cursor.execute('SELECT id, name, tenant_id FROM tenants_subcity ORDER BY id;')
    public_records = cursor.fetchall()
    print(f'\nPublic schema SubCity records: {len(public_records)}')
    for record in public_records:
        print(f'  ID: {record[0]}, Name: {record[1]}, Tenant: {record[2]}')
    
    # 3. Get the absolute maximum ID across all possible records
    cursor.execute("""
        SELECT COALESCE(MAX(id), 0) as max_id 
        FROM tenants_subcity;
    """)
    max_id = cursor.fetchone()[0]
    print(f'\nMax SubCity ID found: {max_id}')
    
    # 4. Check current sequence
    cursor.execute('SELECT last_value, is_called FROM tenants_subcity_id_seq;')
    seq_info = cursor.fetchone()
    print(f'Current sequence: last_value={seq_info[0]}, is_called={seq_info[1]}')
    
    # 5. Set sequence to a safe value (max_id + 10 to be extra safe)
    safe_next_id = max_id + 10
    cursor.execute(f"SELECT setval('tenants_subcity_id_seq', {safe_next_id});")
    print(f'✅ Set sequence to safe value: {safe_next_id}')
    
    # 6. Verify the fix
    cursor.execute('SELECT last_value, is_called FROM tenants_subcity_id_seq;')
    new_seq = cursor.fetchone()
    print(f'New sequence: last_value={new_seq[0]}, is_called={new_seq[1]}')
    
    # 7. Also fix Kebele sequence while we're at it
    cursor.execute('SELECT COALESCE(MAX(id), 0) FROM tenants_kebele;')
    max_kebele_id = cursor.fetchone()[0]
    safe_kebele_next = max_kebele_id + 10
    cursor.execute(f"SELECT setval('tenants_kebele_id_seq', {safe_kebele_next});")
    print(f'✅ Set Kebele sequence to safe value: {safe_kebele_next}')
    
    print('\n🎉 Comprehensive fix complete! Sequences set to safe values.')
    print('🚀 SubCity and Kebele registration should now work without IntegrityError!')
    
    return True

if __name__ == "__main__":
    comprehensive_fix()
