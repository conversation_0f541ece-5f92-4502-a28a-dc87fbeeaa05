Write-Host "========================================" -ForegroundColor Green
Write-Host "GoID Database Reset and Migration" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Write-Host ""
Write-Host "Step 1: Stopping all containers and removing volumes..." -ForegroundColor Yellow
docker-compose down -v

Write-Host ""
Write-Host "Step 2: Removing any orphaned containers..." -ForegroundColor Yellow
docker container prune -f

Write-Host ""
Write-Host "Step 3: Removing unused volumes..." -ForegroundColor Yellow
docker volume prune -f

Write-Host ""
Write-Host "Step 4: Rebuilding backend image (no cache)..." -ForegroundColor Yellow
docker-compose build --no-cache backend

Write-Host ""
Write-Host "Step 5: Starting database first..." -ForegroundColor Yellow
docker-compose up -d db

Write-Host ""
Write-Host "Step 6: Waiting for database to be ready (10 seconds)..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

Write-Host ""
Write-Host "Step 7: Starting all services..." -ForegroundColor Yellow
docker-compose up -d

Write-Host ""
Write-Host "Step 8: Waiting for backend to initialize (30 seconds)..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

Write-Host ""
Write-Host "Step 9: Checking container status..." -ForegroundColor Yellow
docker-compose ps

Write-Host ""
Write-Host "Step 10: Showing recent backend logs..." -ForegroundColor Yellow
docker-compose logs backend | Select-Object -Last 50

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Reset and migration complete!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Write-Host ""
Write-Host "You can now:" -ForegroundColor Cyan
Write-Host "1. Check if backend is running: docker-compose ps" -ForegroundColor White
Write-Host "2. View logs: docker-compose logs backend" -ForegroundColor White
Write-Host "3. Test API: curl http://localhost:8000" -ForegroundColor White
Write-Host "4. Open frontend: http://localhost:3000" -ForegroundColor White

Write-Host ""
Write-Host "Testing citizen clearance table..." -ForegroundColor Yellow
try {
    $testResult = docker exec goid-backend-1 python manage.py shell -c "
from workflows.models import CitizenClearanceRequest
from django_tenants.utils import schema_context
from tenants.models import Tenant

try:
    print('✅ CitizenClearanceRequest model imported successfully')
    
    tenants = Tenant.objects.filter(schema_name__in=['kebele_bole_01'])
    for tenant in tenants:
        with schema_context(tenant.schema_name):
            count = CitizenClearanceRequest.objects.count()
            print(f'✅ citizen_clearance_requests table exists in {tenant.schema_name} with {count} records')
            
    print('🎉 Citizen clearance system is working!')
except Exception as e:
    print(f'❌ Error: {e}')
"
    Write-Host $testResult -ForegroundColor Green
} catch {
    Write-Host "❌ Could not test citizen clearance table (backend may still be starting)" -ForegroundColor Red
}

Write-Host ""
Read-Host "Press Enter to continue..."
