#!/usr/bin/env python3
"""
Test script to verify that the Django admin interface CSS loading issue has been fixed.
This script tests both the admin page and static file serving.
"""

import requests
import sys
from urllib.parse import urljoin

def test_admin_interface():
    """Test the Django admin interface and static file serving."""
    base_url = "http://localhost:8000"
    
    print("🧪 Testing Django Admin Interface CSS Fix")
    print("=" * 50)
    
    # Test 1: Admin page accessibility
    print("1. Testing admin page accessibility...")
    try:
        admin_response = requests.get(f"{base_url}/admin/", timeout=10)
        if admin_response.status_code == 200:
            print("   ✅ Admin page is accessible (Status: 200)")
            
            # Check if the response contains CSS link tags
            if 'admin/css/base.css' in admin_response.text:
                print("   ✅ Admin page contains CSS references")
            else:
                print("   ⚠️  Admin page missing CSS references")
        else:
            print(f"   ❌ Admin page not accessible (Status: {admin_response.status_code})")
            return False
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Failed to access admin page: {e}")
        return False
    
    # Test 2: CSS file serving
    print("\n2. Testing CSS file serving...")
    css_files = [
        "/static/admin/css/base.css",
        "/static/admin/css/login.css",
        "/static/admin/css/dashboard.css"
    ]
    
    css_success = 0
    for css_file in css_files:
        try:
            css_response = requests.get(f"{base_url}{css_file}", timeout=10)
            content_type = css_response.headers.get('Content-Type', '')
            
            if css_response.status_code == 200:
                if 'text/css' in content_type:
                    print(f"   ✅ {css_file} - Status: 200, Content-Type: {content_type}")
                    css_success += 1
                else:
                    print(f"   ⚠️  {css_file} - Status: 200, but wrong Content-Type: {content_type}")
            else:
                print(f"   ❌ {css_file} - Status: {css_response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"   ❌ {css_file} - Error: {e}")
    
    # Test 3: JavaScript file serving
    print("\n3. Testing JavaScript file serving...")
    js_files = [
        "/static/admin/js/core.js",
        "/static/admin/js/admin/RelatedObjectLookups.js"
    ]
    
    js_success = 0
    for js_file in js_files:
        try:
            js_response = requests.get(f"{base_url}{js_file}", timeout=10)
            content_type = js_response.headers.get('Content-Type', '')
            
            if js_response.status_code == 200:
                if 'javascript' in content_type or 'text/plain' in content_type:
                    print(f"   ✅ {js_file} - Status: 200, Content-Type: {content_type}")
                    js_success += 1
                else:
                    print(f"   ⚠️  {js_file} - Status: 200, but unexpected Content-Type: {content_type}")
            else:
                print(f"   ❌ {js_file} - Status: {js_response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"   ❌ {js_file} - Error: {e}")
    
    # Test 4: Check for HTML being served instead of static files (the original problem)
    print("\n4. Testing for HTML content in static files (original issue)...")
    try:
        css_response = requests.get(f"{base_url}/static/admin/css/base.css", timeout=10)
        if css_response.status_code == 200:
            content = css_response.text[:200]  # First 200 characters
            if content.strip().startswith('<!DOCTYPE html>') or '<html' in content:
                print("   ❌ CSS file contains HTML content - ISSUE NOT FIXED!")
                return False
            else:
                print("   ✅ CSS file contains actual CSS content - ISSUE FIXED!")
        else:
            print(f"   ❌ Could not retrieve CSS file (Status: {css_response.status_code})")
            return False
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Error testing CSS content: {e}")
        return False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY:")
    print(f"   CSS Files Working: {css_success}/{len(css_files)}")
    print(f"   JS Files Working: {js_success}/{len(js_files)}")
    
    if css_success == len(css_files) and js_success == len(js_files):
        print("   🎉 ALL TESTS PASSED - Django Admin CSS Issue is FIXED!")
        return True
    else:
        print("   ⚠️  Some tests failed - Issue may not be fully resolved")
        return False

if __name__ == "__main__":
    success = test_admin_interface()
    sys.exit(0 if success else 1)
