import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  MenuItem,
  Box,
  Typography,
  Alert,
  Autocomplete,
  FormControl,
  InputLabel,
  Select,
  Divider,
  Chip
} from '@mui/material';
import {
  Send as SendIcon,
  Cancel as CancelIcon,
  Person as PersonIcon,
  LocationOn as LocationIcon,
  AttachFile as AttachFileIcon,
  CloudUpload as CloudUploadIcon,
  Description as DescriptionIcon,
  Badge as BadgeIcon
} from '@mui/icons-material';
import axios from '../../utils/axios';
import { useAuth } from '../../contexts/AuthContext';

const TransferRequestForm = ({ open, onClose, citizen, onSuccess }) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [kebeles, setKebeles] = useState([]);
  const [formData, setFormData] = useState({
    destination_kebele: '',
    transfer_reason: 'relocation',
    reason_description: ''
  });

  // Document upload state
  const [documents, setDocuments] = useState({
    application_letter: null,
    current_kebele_id: null
  });

  const [documentPreviews, setDocumentPreviews] = useState({
    application_letter: null,
    current_kebele_id: null
  });

  const transferReasons = [
    { value: 'relocation', label: 'Relocation/Moving' },
    { value: 'marriage', label: 'Marriage' },
    { value: 'work', label: 'Work/Employment' },
    { value: 'education', label: 'Education' },
    { value: 'family', label: 'Family Reasons' },
    { value: 'other', label: 'Other' }
  ];

  useEffect(() => {
    if (open) {
      fetchKebeles();
      resetForm();
    }
  }, [open]);

  const fetchKebeles = async () => {
    try {
      console.log('🔍 Fetching available kebeles for transfer...');
      console.log('🔍 Current user:', user);

      // Use the correct endpoint path for available kebeles
      const response = await axios.get('/api/tenants/transfers/available-kebeles/');
      const data = response.data;

      console.log('✅ Available kebeles response:', data);

      if (data.available_kebeles) {
        setKebeles(data.available_kebeles);
        console.log('✅ Available kebeles for transfer:', data.available_kebeles);
        console.log('🏙️ Current kebele context:', data.current_kebele);

        if (data.available_kebeles.length === 0) {
          setError(`No other kebeles available for transfer in ${data.current_kebele?.city_name || 'your city'}`);
        }
      } else {
        setError('Invalid response format from server');
      }
    } catch (error) {
      console.error('❌ Error fetching available kebeles:', error);

      // Show specific error message
      let errorMessage = 'Failed to load available kebeles';
      if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.response?.status === 500) {
        errorMessage = 'Server error - transfer system may not be properly configured';
      } else if (error.response?.status === 404) {
        errorMessage = 'Transfer endpoint not found - please contact administrator';
      }

      setError(errorMessage);

      // Don't use hardcoded data anymore - show the actual error
      console.log('❌ Not using fallback data - showing actual error to user');
    }
  };

  const resetForm = () => {
    setFormData({
      destination_kebele: '',
      transfer_reason: 'relocation',
      reason_description: ''
    });
    setDocuments({
      application_letter: null,
      current_kebele_id: null
    });
    setDocumentPreviews({
      application_letter: null,
      current_kebele_id: null
    });
    setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.destination_kebele) {
      setError('Please select a destination kebele');
      return;
    }

    if (!formData.reason_description.trim()) {
      setError('Please provide a reason for the transfer');
      return;
    }

    if (!documents.application_letter) {
      setError('Please upload the application letter');
      return;
    }

    if (!documents.current_kebele_id) {
      setError('Please upload the current kebele ID card');
      return;
    }

    try {
      setLoading(true);
      setError('');

      // Step 1: Create the transfer request first
      const transferData = {
        citizen_id: citizen.id,
        citizen_name: citizen.full_name || `${citizen.first_name} ${citizen.last_name}`,
        citizen_digital_id: citizen.digital_id,
        destination_kebele: formData.destination_kebele,
        transfer_reason: formData.transfer_reason,
        reason_description: formData.reason_description.trim()
      };

      console.log('🔍 Creating transfer request:', transferData);

      const response = await axios.post('/api/emergency-transfer/', transferData);
      console.log('✅ Transfer request created:', response.data);

      // Step 2: Upload documents if any are provided
      if (documents.application_letter || documents.current_kebele_id) {
        console.log('📎 Uploading documents...');

        const uploadFormData = new FormData();
        if (documents.application_letter) {
          console.log('📎 Adding application_letter:', documents.application_letter.name, documents.application_letter.size);
          uploadFormData.append('application_letter', documents.application_letter);
        }
        if (documents.current_kebele_id) {
          console.log('📎 Adding current_kebele_id:', documents.current_kebele_id.name, documents.current_kebele_id.size);
          uploadFormData.append('current_kebele_id', documents.current_kebele_id);
        }

        console.log('📎 FormData entries:');
        for (let [key, value] of uploadFormData.entries()) {
          console.log(`  ${key}:`, value);
        }

        try {
          // Use the source transfer ID for document upload
          const transferId = response.data.source_transfer_id;
          console.log('📎 Uploading to transfer ID:', transferId);
          console.log('📎 Upload URL:', `/api/tenants/transfers/${transferId}/upload-documents/`);

          const uploadResponse = await axios.post(
            `/api/tenants/transfers/${transferId}/upload-documents/`,
            uploadFormData,
            {
              headers: {
                'Content-Type': 'multipart/form-data',
              },
            }
          );
          console.log('✅ Documents uploaded successfully:', uploadResponse.data);
        } catch (uploadError) {
          console.warn('⚠️ Document upload failed, but transfer request was created:', uploadError);
          // Don't fail the entire process if document upload fails
        }
      }

      if (onSuccess) {
        onSuccess(response.data);
      }

      onClose();
    } catch (error) {
      console.error('❌ Error creating transfer request:', error);

      let errorMessage = 'Failed to create transfer request';
      if (error.response?.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data;
        } else if (error.response.data.detail) {
          errorMessage = error.response.data.detail;
        } else if (error.response.data.error) {
          errorMessage = error.response.data.error;
        } else if (error.response.data.non_field_errors) {
          errorMessage = error.response.data.non_field_errors[0];
        }
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleDocumentUpload = (documentType, file) => {
    if (file) {
      // Validate file type
      const allowedTypes = {
        application_letter: ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg'],
        current_kebele_id: ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf']
      };

      if (!allowedTypes[documentType].includes(file.type)) {
        setError(`Invalid file type for ${documentType.replace('_', ' ')}. Please upload PDF, JPG, or PNG files.`);
        return;
      }

      // Validate file size (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        setError(`File size too large. Please upload files smaller than 5MB.`);
        return;
      }

      setDocuments(prev => ({
        ...prev,
        [documentType]: file
      }));

      // Create preview URL for images
      if (file.type.startsWith('image/')) {
        const previewUrl = URL.createObjectURL(file);
        setDocumentPreviews(prev => ({
          ...prev,
          [documentType]: previewUrl
        }));
      } else {
        setDocumentPreviews(prev => ({
          ...prev,
          [documentType]: file.name
        }));
      }

      setError(''); // Clear any previous errors
    }
  };

  const removeDocument = (documentType) => {
    setDocuments(prev => ({
      ...prev,
      [documentType]: null
    }));

    // Clean up preview URL
    if (documentPreviews[documentType] && typeof documentPreviews[documentType] === 'string' && documentPreviews[documentType].startsWith('blob:')) {
      URL.revokeObjectURL(documentPreviews[documentType]);
    }

    setDocumentPreviews(prev => ({
      ...prev,
      [documentType]: null
    }));
  };

  if (!citizen) return null;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2 }
      }}
    >
      <DialogTitle sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 2,
        pb: 2,
        backgroundColor: 'primary.main',
        color: 'white'
      }}>
        <SendIcon />
        Create Transfer Request
      </DialogTitle>

      <form onSubmit={handleSubmit}>
        <DialogContent sx={{ pt: 3 }}>
          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {/* Citizen Information */}
          <Box sx={{
            p: 2,
            backgroundColor: 'grey.50',
            borderRadius: 1,
            mb: 3,
            border: '1px solid',
            borderColor: 'grey.200'
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
              <PersonIcon color="primary" />
              <Typography variant="h6" fontWeight="bold">
                Citizen Information
              </Typography>
            </Box>

            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Full Name:
                </Typography>
                <Typography variant="body1" fontWeight="medium">
                  {citizen.full_name || `${citizen.first_name} ${citizen.last_name}`}
                </Typography>
              </Box>

              <Box>
                <Typography variant="body2" color="text.secondary">
                  Digital ID:
                </Typography>
                <Typography variant="body1" fontWeight="medium">
                  {citizen.digital_id}
                </Typography>
              </Box>

              <Box>
                <Typography variant="body2" color="text.secondary">
                  Current Kebele:
                </Typography>
                <Chip
                  label={user?.tenant?.name || 'Current Kebele'}
                  color="primary"
                  size="small"
                />
              </Box>

              <Box>
                <Typography variant="body2" color="text.secondary">
                  Phone:
                </Typography>
                <Typography variant="body1" fontWeight="medium">
                  {citizen.phone || 'N/A'}
                </Typography>
              </Box>
            </Box>
          </Box>

          <Divider sx={{ my: 3 }} />

          {/* Transfer Details */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
            <LocationIcon color="primary" />
            <Typography variant="h6" fontWeight="bold">
              Transfer Details
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            {/* Destination Kebele */}
            <Autocomplete
              options={kebeles}
              getOptionLabel={(option) => {
                // Show kebele name with subcity context if available
                if (option.subcity_name) {
                  return `${option.name} (${option.subcity_name})`;
                }
                return option.name;
              }}
              value={kebeles.find(k => k.id === formData.destination_kebele) || null}
              onChange={(event, newValue) => {
                handleChange('destination_kebele', newValue?.id || '');
              }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Destination Kebele"
                  required
                  helperText="Select the kebele where the citizen wants to transfer (within same subcity)"
                />
              )}
              renderOption={(props, option) => {
                const { key, ...otherProps } = props;
                return (
                  <Box component="li" key={key} {...otherProps}>
                    <Box>
                      <Typography variant="body1" fontWeight="medium">
                        {option.name}
                      </Typography>
                      {option.subcity_name && (
                        <Typography variant="body2" color="text.secondary">
                          {option.subcity_name} Subcity
                        </Typography>
                      )}
                    </Box>
                  </Box>
                );
              }}
              disabled={loading}
            />

            {/* Transfer Reason */}
            <FormControl required>
              <InputLabel>Transfer Reason</InputLabel>
              <Select
                value={formData.transfer_reason}
                onChange={(e) => handleChange('transfer_reason', e.target.value)}
                label="Transfer Reason"
                disabled={loading}
              >
                {transferReasons.map((reason) => (
                  <MenuItem key={reason.value} value={reason.value}>
                    {reason.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* Reason Description */}
            <TextField
              label="Detailed Reason"
              multiline
              rows={4}
              value={formData.reason_description}
              onChange={(e) => handleChange('reason_description', e.target.value)}
              placeholder="Please provide a detailed explanation for the transfer request..."
              required
              disabled={loading}
              helperText="Provide specific details about why this citizen needs to transfer"
            />
          </Box>

          <Divider sx={{ my: 3 }} />

          {/* Required Documents */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
            <AttachFileIcon color="primary" />
            <Typography variant="h6" fontWeight="bold">
              Required Documents
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            {/* Application Letter Upload */}
            <Box sx={{
              p: 2,
              border: '2px dashed',
              borderColor: documents.application_letter ? 'success.main' : 'grey.300',
              borderRadius: 1,
              backgroundColor: documents.application_letter ? 'success.50' : 'grey.50'
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <DescriptionIcon color={documents.application_letter ? 'success' : 'action'} />
                <Typography variant="subtitle1" fontWeight="medium">
                  Application Letter
                </Typography>
                <Chip
                  label="Required"
                  size="small"
                  color="error"
                  variant="outlined"
                />
              </Box>

              {!documents.application_letter ? (
                <Box>
                  <input
                    accept=".pdf,.jpg,.jpeg,.png"
                    style={{ display: 'none' }}
                    id="application-letter-upload"
                    type="file"
                    onChange={(e) => handleDocumentUpload('application_letter', e.target.files[0])}
                    disabled={loading}
                  />
                  <label htmlFor="application-letter-upload">
                    <Button
                      variant="outlined"
                      component="span"
                      startIcon={<CloudUploadIcon />}
                      disabled={loading}
                      sx={{ mb: 1 }}
                    >
                      Upload Application Letter
                    </Button>
                  </label>
                  <Typography variant="body2" color="text.secondary">
                    Upload the citizen's application letter requesting the transfer (PDF, JPG, PNG - Max 5MB)
                  </Typography>
                </Box>
              ) : (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  {documentPreviews.application_letter && documentPreviews.application_letter.startsWith('blob:') ? (
                    <img
                      src={documentPreviews.application_letter}
                      alt="Application Letter Preview"
                      style={{ width: 60, height: 60, objectFit: 'cover', borderRadius: 4 }}
                    />
                  ) : (
                    <DescriptionIcon sx={{ fontSize: 40, color: 'success.main' }} />
                  )}
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body1" fontWeight="medium">
                      {documents.application_letter.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {(documents.application_letter.size / 1024 / 1024).toFixed(2)} MB
                    </Typography>
                  </Box>
                  <Button
                    variant="outlined"
                    color="error"
                    size="small"
                    onClick={() => removeDocument('application_letter')}
                    disabled={loading}
                  >
                    Remove
                  </Button>
                </Box>
              )}
            </Box>

            {/* Current Kebele ID Upload */}
            <Box sx={{
              p: 2,
              border: '2px dashed',
              borderColor: documents.current_kebele_id ? 'success.main' : 'grey.300',
              borderRadius: 1,
              backgroundColor: documents.current_kebele_id ? 'success.50' : 'grey.50'
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <BadgeIcon color={documents.current_kebele_id ? 'success' : 'action'} />
                <Typography variant="subtitle1" fontWeight="medium">
                  Current Kebele ID Card
                </Typography>
                <Chip
                  label="Required"
                  size="small"
                  color="error"
                  variant="outlined"
                />
              </Box>

              {!documents.current_kebele_id ? (
                <Box>
                  <input
                    accept=".pdf,.jpg,.jpeg,.png"
                    style={{ display: 'none' }}
                    id="kebele-id-upload"
                    type="file"
                    onChange={(e) => handleDocumentUpload('current_kebele_id', e.target.files[0])}
                    disabled={loading}
                  />
                  <label htmlFor="kebele-id-upload">
                    <Button
                      variant="outlined"
                      component="span"
                      startIcon={<CloudUploadIcon />}
                      disabled={loading}
                      sx={{ mb: 1 }}
                    >
                      Upload Kebele ID Card
                    </Button>
                  </label>
                  <Typography variant="body2" color="text.secondary">
                    Upload the citizen's current kebele ID card (PDF, JPG, PNG - Max 5MB)
                  </Typography>
                </Box>
              ) : (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  {documentPreviews.current_kebele_id && documentPreviews.current_kebele_id.startsWith('blob:') ? (
                    <img
                      src={documentPreviews.current_kebele_id}
                      alt="Kebele ID Preview"
                      style={{ width: 60, height: 60, objectFit: 'cover', borderRadius: 4 }}
                    />
                  ) : (
                    <BadgeIcon sx={{ fontSize: 40, color: 'success.main' }} />
                  )}
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body1" fontWeight="medium">
                      {documents.current_kebele_id.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {(documents.current_kebele_id.size / 1024 / 1024).toFixed(2)} MB
                    </Typography>
                  </Box>
                  <Button
                    variant="outlined"
                    color="error"
                    size="small"
                    onClick={() => removeDocument('current_kebele_id')}
                    disabled={loading}
                  >
                    Remove
                  </Button>
                </Box>
              )}
            </Box>
          </Box>
        </DialogContent>

        <DialogActions sx={{ p: 3, gap: 1 }}>
          <Button
            onClick={onClose}
            variant="outlined"
            startIcon={<CancelIcon />}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            startIcon={<SendIcon />}
            disabled={loading}
          >
            {loading ? 'Creating Request...' : 'Create Transfer Request'}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default TransferRequestForm;
