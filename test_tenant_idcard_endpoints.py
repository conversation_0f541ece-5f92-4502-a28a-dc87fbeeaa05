#!/usr/bin/env python3
"""
Test script to verify tenant-specific ID card endpoints are working.
"""

import requests
import json

def test_tenant_idcard_endpoints():
    """Test the tenant-specific ID card endpoints."""
    
    base_url = "http://************:8000"
    
    print("🧪 Testing Tenant-Specific ID Card Endpoints")
    print("=" * 50)
    
    # Step 1: Login to get authentication token
    print("📍 Step 1: Logging in to get authentication token...")
    try:
        login_response = requests.post(
            f"{base_url}/api/auth/token/",
            json={
                "username": "<EMAIL>",
                "password": "password123"
            },
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if login_response.status_code == 200:
            login_data = login_response.json()
            access_token = login_data.get('access')
            tenant_id = login_data.get('tenant_id')
            print(f"✅ Login successful! Tenant ID: {tenant_id}")
            
            if not access_token:
                print("❌ No access token in response")
                return
                
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
            
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            print(f"Response: {login_response.text}")
            return
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return
    
    # Step 2: Test tenant-specific ID card list endpoint
    print(f"\n📍 Step 2: Testing ID card list for tenant {tenant_id}...")
    try:
        idcard_url = f"{base_url}/api/tenants/{tenant_id}/idcards/"
        response = requests.get(idcard_url, headers=headers, timeout=10)
        
        print(f"URL: {idcard_url}")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ ID card list endpoint working!")
            print(f"   Found {len(data.get('results', data))} ID cards")
        elif response.status_code == 404:
            print("❌ ID card endpoint not found (404)")
        else:
            print(f"❌ ID card endpoint failed: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ ID card list error: {e}")
    
    # Step 3: Test with different tenant ID to verify URL parsing
    print(f"\n📍 Step 3: Testing with different tenant ID (42)...")
    try:
        test_url = f"{base_url}/api/tenants/42/idcards/"
        response = requests.get(test_url, headers=headers, timeout=10)
        
        print(f"URL: {test_url}")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 404:
            print("✅ Correctly returns 404 for non-existent tenant")
        elif response.status_code == 400:
            print("✅ Correctly returns 400 for invalid tenant")
        else:
            print(f"⚠️  Unexpected response: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Test tenant error: {e}")
    
    # Step 4: Test citizens endpoint for comparison
    print(f"\n📍 Step 4: Testing citizens endpoint for tenant {tenant_id}...")
    try:
        citizens_url = f"{base_url}/api/tenants/{tenant_id}/citizens/"
        response = requests.get(citizens_url, headers=headers, timeout=10)
        
        print(f"URL: {citizens_url}")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Citizens endpoint working!")
        else:
            print(f"❌ Citizens endpoint failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Citizens endpoint error: {e}")
    
    # Step 5: Test shared endpoints (should still work)
    print(f"\n📍 Step 5: Testing shared endpoints...")
    try:
        shared_url = f"{base_url}/api/shared/countries/"
        response = requests.get(shared_url, timeout=10)
        
        print(f"URL: {shared_url}")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Shared endpoints still working!")
        else:
            print(f"❌ Shared endpoints failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Shared endpoints error: {e}")

if __name__ == "__main__":
    test_tenant_idcard_endpoints()
    
    print("\n" + "=" * 50)
    print("🔧 If tenant ID card endpoints are still not working:")
    print("1. Restart Django server: docker-compose restart backend")
    print("2. Check Django logs: docker-compose logs backend")
    print("3. Verify middleware changes are applied")
    print("4. Check if tenant exists in database")
    print("\n🌐 Expected working URLs:")
    print("   Auth: http://************:8000/api/auth/token/")
    print("   Tenant ID Cards: http://************:8000/api/tenants/{tenant_id}/idcards/")
    print("   Tenant Citizens: http://************:8000/api/tenants/{tenant_id}/citizens/")
    print("   Shared: http://************:8000/api/shared/countries/")
