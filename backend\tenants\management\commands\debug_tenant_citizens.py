from django.core.management.base import BaseCommand
from django_tenants.utils import schema_context
from tenants.models import Tenant
from citizens.models import Citizen


class Command(BaseCommand):
    help = 'Debug citizens in tenant 42 to see the ID mismatch'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🔍 Debugging citizens in tenant 42...'))
        
        tenant_id = 42
        
        try:
            # Get tenant 42
            tenant = Tenant.objects.get(id=tenant_id)
            self.stdout.write(f'📋 Tenant: {tenant.name} ({tenant.schema_name})')
            
            with schema_context(tenant.schema_name):
                self.stdout.write(f'🔍 Inside schema context: {tenant.schema_name}')
                
                # List all citizens with their IDs
                citizens = Citizen.objects.all().order_by('id')
                self.stdout.write(f'📋 Total citizens in {tenant.schema_name}: {citizens.count()}')
                
                for citizen in citizens:
                    self.stdout.write(f'   ID {citizen.id}: {citizen.get_full_name()} (Digital: {citizen.digital_id})')
                    
                    # Check if this is the citizen the frontend expects
                    if citizen.id == 4:
                        self.stdout.write(f'     ⭐ This is citizen ID 4 that frontend is sending!')
                        self.stdout.write(f'     ⭐ Frontend expects: Ima Brianna Vance (C6C47BD1)')
                        self.stdout.write(f'     ⭐ Backend has: {citizen.get_full_name()} ({citizen.digital_id})')
                        
                        if citizen.digital_id == 'C6C47BD1':
                            self.stdout.write(f'     ✅ Digital IDs match - this is correct!')
                        else:
                            self.stdout.write(f'     ❌ Digital IDs do NOT match!')
                            self.stdout.write(f'        Expected: C6C47BD1')
                            self.stdout.write(f'        Found: {citizen.digital_id}')
                    
                    # Check if the expected citizen exists with a different ID
                    if citizen.digital_id == 'C6C47BD1':
                        self.stdout.write(f'     🎯 Found expected citizen "Ima Brianna Vance" at ID {citizen.id}!')
                        if citizen.id != 4:
                            self.stdout.write(f'     ⚠️  But frontend thinks this citizen is at ID 4!')
                
                # Check if there's a citizen with digital ID C6C47BD1
                try:
                    expected_citizen = Citizen.objects.get(digital_id='C6C47BD1')
                    self.stdout.write(f'\n🎯 Expected citizen found:')
                    self.stdout.write(f'   - Name: {expected_citizen.get_full_name()}')
                    self.stdout.write(f'   - Database ID: {expected_citizen.id}')
                    self.stdout.write(f'   - Digital ID: {expected_citizen.digital_id}')
                    
                    if expected_citizen.id != 4:
                        self.stdout.write(f'   ❌ MISMATCH: Frontend sends ID 4, but this citizen is at ID {expected_citizen.id}')
                        self.stdout.write(f'   🔧 Frontend should send ID {expected_citizen.id} instead of 4')
                    else:
                        self.stdout.write(f'   ✅ IDs match correctly')
                        
                except Citizen.DoesNotExist:
                    self.stdout.write(f'\n❌ Expected citizen with digital ID C6C47BD1 not found!')
                
        except Tenant.DoesNotExist:
            self.stdout.write(f'❌ Tenant {tenant_id} not found')
        except Exception as e:
            self.stdout.write(f'❌ Error: {str(e)}')
            import traceback
            self.stdout.write(traceback.format_exc())
        
        self.stdout.write(self.style.SUCCESS('\n🎉 Debug completed!'))
