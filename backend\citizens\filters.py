import django_filters
from .models import Citizen


class CitizenFilter(django_filters.FilterSet):
    """Filter for Citizen model."""
    first_name = django_filters.CharFilter(lookup_expr='icontains')
    middle_name = django_filters.CharFilter(lookup_expr='icontains')
    last_name = django_filters.CharFilter(lookup_expr='icontains')
    phone = django_filters.CharFilter(lookup_expr='icontains')
    email = django_filters.CharFilter(lookup_expr='icontains')
    gender = django_filters.ChoiceFilter(choices=[('male', 'Male'), ('female', 'Female')])
    digital_id = django_filters.Char<PERSON>ilter(lookup_expr='icontains')
    has_id_card = django_filters.BooleanFilter(method='filter_has_id_card')
    min_age = django_filters.NumberFilter(method='filter_min_age')
    max_age = django_filters.NumberFilter(method='filter_max_age')

    class Meta:
        model = Citizen
        fields = [
            'first_name', 'middle_name', 'last_name', 'gender', 'phone',
            'email', 'digital_id', 'has_id_card',
            'min_age', 'max_age'
        ]

    def filter_has_id_card(self, queryset, name, value):
        """Filter citizens based on whether they have an ID card."""
        if value:
            return queryset.filter(id_cards__isnull=False).distinct()
        else:
            return queryset.filter(id_cards__isnull=True)

    def filter_min_age(self, queryset, name, value):
        """Filter citizens with minimum age."""
        from django.db.models import F, ExpressionWrapper, fields
        from django.utils import timezone
        import datetime

        today = timezone.now().date()
        min_birth_date = today - datetime.timedelta(days=value*365.25)

        return queryset.filter(date_of_birth__lte=min_birth_date)

    def filter_max_age(self, queryset, name, value):
        """Filter citizens with maximum age."""
        from django.db.models import F, ExpressionWrapper, fields
        from django.utils import timezone
        import datetime

        today = timezone.now().date()
        max_birth_date = today - datetime.timedelta(days=value*365.25)

        return queryset.filter(date_of_birth__gte=max_birth_date)
