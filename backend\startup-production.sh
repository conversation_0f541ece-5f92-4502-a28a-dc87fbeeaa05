#!/bin/bash

# Production startup script for GoID Backend
echo "🚀 Starting GoID Backend (Production Mode)"
echo "=========================================="

# Wait for database to be ready
echo "⏳ Waiting for database connection..."
python wait-for-db.py

# Clean up migration cache
echo "🧹 Cleaning migration cache..."
find . -path "*/migrations/__pycache__" -exec rm -rf {} + 2>/dev/null || true

# Generate fresh migrations
echo "📝 Generating fresh migrations..."
python manage.py makemigrations users tenants shared citizens idcards workflows

# Run shared schema migrations first
echo "🔧 Running shared schema migrations..."
python manage.py migrate_schemas --shared

# Populate shared data for APIs
echo "📊 Populating shared data for APIs..."
python manage.py populate_shared_data

# Create public tenant if it doesn't exist
echo "🏢 Ensuring public tenant exists..."
python manage.py shell -c "
from tenants.models import Tenant, Domain, TenantType
try:
    if not Tenant.objects.filter(schema_name='public').exists():
        tenant = Tenant(schema_name='public', name='Public', type=TenantType.CITY)
        tenant.save()
        domain = Domain(domain='localhost', tenant=tenant, is_primary=True)
        domain.save()
        print('✅ Public tenant created')
    else:
        print('✅ Public tenant already exists')
except Exception as e:
    print(f'⚠️  Public tenant creation issue (may be normal): {e}')
"

# Run tenant schema migrations
echo "🔧 Running tenant schema migrations..."
python manage.py migrate_schemas --tenant

# Initialize sample data (tenants, users, citizens)
echo "📊 Initializing sample data..."
python manage.py init_data

# Initialize system permissions, groups, and superadmin
echo "🔧 Initializing system configuration..."
python init_system.py

# Collect static files
echo "📁 Collecting static files..."
python manage.py collectstatic --noinput

# Start the Django development server
echo "🌐 Starting Django server..."
echo "✅ Backend ready at http://localhost:8000"
python manage.py runserver 0.0.0.0:8000
