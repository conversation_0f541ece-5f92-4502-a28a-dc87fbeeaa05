import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  FormControl,
  FormHelperText,
  Paper,
  Chip,
  Alert,
  Tooltip,
  IconButton
} from '@mui/material';
import {
  Info as InfoIcon,
  CalendarToday as CalendarIcon,
  Today as TodayIcon
} from '@mui/icons-material';
import EthiopianCalendarWidget from './EthiopianCalendarWidget';
import {
  gregorianToEthiopian,
  formatEthiopianDate,
  getCurrentEthiopianDate
} from '../../utils/ethiopianCalendar';

/**
 * Ethiopian Date Picker Component using self-contained widget
 *
 * This component provides a true Ethiopian calendar interface where:
 * - Users see and interact with Ethiopian calendar (Year 2017, Meskerem, etc.)
 * - System receives Gregorian Date objects for storage
 * - No external package dependencies (Docker compatible)
 */
const EthiopianDatePickerComponent = ({
  label = "Select Ethiopian Date",
  value, // Gregorian Date object
  onChange, // Callback with Gregorian Date object
  error = false,
  helperText = "",
  required = false,
  disabled = false,
  fullWidth = true,
  language = 'en', // 'en' or 'am'
  showConversion = true,
  minYear = 2000,
  maxYear = 2030,
  disableFuture = false,
  placeholder = "Select date from Ethiopian calendar",
  ...otherProps
}) => {
  // Simply use the EthiopianCalendarWidget with the same props
  return (
    <EthiopianCalendarWidget
      label={label}
      value={value}
      onChange={onChange}
      error={error}
      helperText={helperText}
      required={required}
      disabled={disabled}
      language={language}
      showConversion={showConversion}
      minYear={minYear}
      maxYear={maxYear}
      disableFuture={disableFuture}
      placeholder={placeholder}
      {...otherProps}
    />
  );
};

export default EthiopianDatePickerComponent;
