/**
 * Security Pattern Utilities for ID Card Approval System
 *
 * This module handles the application of security patterns to ID cards
 * based on approval levels:
 * - Kebele Approval: Left half of castle pattern
 * - Subcity Approval: Right half of castle pattern
 */

// SVG pattern for the left half of the castle (kebele approval)
export const KEBELE_PATTERN_SVG = `
<svg width="200" height="150" viewBox="0 0 200 150" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="kebelePattern" patternUnits="userSpaceOnUse" width="200" height="150" opacity="0.15">
      <!-- Left half of the castle -->
      <g fill="#FFD700" opacity="0.3">
        <!-- Left tower -->
        <rect x="10" y="40" width="30" height="80" rx="15"/>
        <rect x="15" y="35" width="20" height="10"/>
        <rect x="12" y="32" width="6" height="8"/>
        <rect x="22" y="32" width="6" height="8"/>
        <rect x="32" y="32" width="6" height="8"/>

        <!-- Left wall -->
        <rect x="40" y="60" width="60" height="60"/>
        <rect x="45" y="55" width="8" height="10"/>
        <rect x="57" y="55" width="8" height="10"/>
        <rect x="69" y="55" width="8" height="10"/>
        <rect x="81" y="55" width="8" height="10"/>
        <rect x="93" y="55" width="8" height="10"/>

        <!-- Windows on left side -->
        <ellipse cx="55" cy="80" rx="6" ry="12" fill="white"/>
        <ellipse cx="75" cy="80" rx="6" ry="12" fill="white"/>
        <ellipse cx="85" cy="100" rx="6" ry="12" fill="white"/>

        <!-- Gate (partial) -->
        <path d="M 95 120 Q 95 110 100 110" fill="white" stroke="white" stroke-width="2"/>
      </g>
    </pattern>
  </defs>
  <rect width="200" height="150" fill="url(#kebelePattern)"/>
</svg>
`;

// SVG pattern for the right half of the castle (subcity approval)
export const SUBCITY_PATTERN_SVG = `
<svg width="200" height="150" viewBox="0 0 200 150" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="subcityPattern" patternUnits="userSpaceOnUse" width="200" height="150" opacity="0.15">
      <!-- Right half of the castle -->
      <g fill="#FFD700" opacity="0.3">
        <!-- Gate (partial) -->
        <path d="M 100 110 Q 105 110 105 120 L 105 120 L 100 120" fill="white" stroke="white" stroke-width="2"/>

        <!-- Right wall -->
        <rect x="100" y="60" width="60" height="60"/>
        <rect x="105" y="55" width="8" height="10"/>
        <rect x="117" y="55" width="8" height="10"/>
        <rect x="129" y="55" width="8" height="10"/>
        <rect x="141" y="55" width="8" height="10"/>
        <rect x="153" y="55" width="8" height="10"/>

        <!-- Right tower -->
        <rect x="160" y="40" width="30" height="80" rx="15"/>
        <rect x="165" y="35" width="20" height="10"/>
        <rect x="162" y="32" width="6" height="8"/>
        <rect x="172" y="32" width="6" height="8"/>
        <rect x="182" y="32" width="6" height="8"/>

        <!-- Windows on right side -->
        <ellipse cx="125" cy="80" rx="6" ry="12" fill="white"/>
        <ellipse cx="145" cy="80" rx="6" ry="12" fill="white"/>
        <ellipse cx="115" cy="100" rx="6" ry="12" fill="white"/>

        <!-- Additional architectural details -->
        <rect x="170" y="50" width="10" height="15" fill="white"/>
        <polygon points="165,65 175,60 185,65 175,70" fill="white"/>
      </g>
    </pattern>
  </defs>
  <rect width="200" height="150" fill="url(#subcityPattern)"/>
</svg>
`;

// Complete castle pattern (when both approvals are done)
export const COMPLETE_PATTERN_SVG = `
<svg width="200" height="150" viewBox="0 0 200 150" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="completePattern" patternUnits="userSpaceOnUse" width="200" height="150" opacity="0.15">
      <!-- Complete castle -->
      <g fill="#FFD700" opacity="0.3">
        <!-- Left tower -->
        <rect x="10" y="40" width="30" height="80" rx="15"/>
        <rect x="15" y="35" width="20" height="10"/>
        <rect x="12" y="32" width="6" height="8"/>
        <rect x="22" y="32" width="6" height="8"/>
        <rect x="32" y="32" width="6" height="8"/>

        <!-- Main wall -->
        <rect x="40" y="60" width="120" height="60"/>
        <rect x="45" y="55" width="8" height="10"/>
        <rect x="57" y="55" width="8" height="10"/>
        <rect x="69" y="55" width="8" height="10"/>
        <rect x="81" y="55" width="8" height="10"/>
        <rect x="93" y="55" width="8" height="10"/>
        <rect x="105" y="55" width="8" height="10"/>
        <rect x="117" y="55" width="8" height="10"/>
        <rect x="129" y="55" width="8" height="10"/>
        <rect x="141" y="55" width="8" height="10"/>
        <rect x="153" y="55" width="8" height="10"/>

        <!-- Right tower -->
        <rect x="160" y="40" width="30" height="80" rx="15"/>
        <rect x="165" y="35" width="20" height="10"/>
        <rect x="162" y="32" width="6" height="8"/>
        <rect x="172" y="32" width="6" height="8"/>
        <rect x="182" y="32" width="6" height="8"/>

        <!-- Windows -->
        <ellipse cx="55" cy="80" rx="6" ry="12" fill="white"/>
        <ellipse cx="75" cy="80" rx="6" ry="12" fill="white"/>
        <ellipse cx="125" cy="80" rx="6" ry="12" fill="white"/>
        <ellipse cx="145" cy="80" rx="6" ry="12" fill="white"/>
        <ellipse cx="85" cy="100" rx="6" ry="12" fill="white"/>
        <ellipse cx="115" cy="100" rx="6" ry="12" fill="white"/>

        <!-- Main gate -->
        <path d="M 95 120 Q 100 110 105 120 L 105 120 L 95 120" fill="white" stroke="white" stroke-width="2"/>

        <!-- Additional details -->
        <rect x="170" y="50" width="10" height="15" fill="white"/>
        <polygon points="165,65 175,60 185,65 175,70" fill="white"/>
      </g>
    </pattern>
  </defs>
  <rect width="200" height="150" fill="url(#completePattern)"/>
</svg>
`;

/**
 * Apply security pattern to ID card based on approval status and user role
 * CRITICAL SECURITY: Pattern visibility is restricted by user role
 * @param {Object} idCard - ID card object with approval status
 * @param {Object} user - Current user object with role information
 * @returns {string} - CSS class for the security pattern
 */
export const getSecurityPatternClass = (idCard, user = null) => {
  // Get user role from localStorage if not provided
  let userRole = user?.role;

  if (!userRole) {
    try {
      const accessToken = localStorage.getItem('accessToken');
      if (accessToken) {
        const base64Url = accessToken.split('.')[1];
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        }).join(''));
        const tokenData = JSON.parse(jsonPayload);
        userRole = tokenData.role;
      }
    } catch (e) {
      console.warn('Could not decode JWT token for role');
    }
  }

  // CRITICAL SECURITY: Clerks should NEVER see security patterns
  if (userRole === 'clerk') {
    return '';
  }

  // Kebele leaders only see kebele patterns (half pattern only)
  if (userRole === 'kebele_leader') {
    if (idCard.has_kebele_pattern) {
      return 'security-pattern-kebele';
    }
    return '';
  }

  // Subcity admins and above can see full patterns
  if (userRole === 'subcity_admin' || userRole === 'city_admin' || userRole === 'superadmin') {
    if (idCard.has_kebele_pattern && idCard.has_subcity_pattern) {
      return 'security-pattern-complete';
    } else if (idCard.has_kebele_pattern) {
      return 'security-pattern-kebele';
    } else if (idCard.has_subcity_pattern) {
      return 'security-pattern-subcity';
    }
  }

  return '';
};

/**
 * Get security pattern SVG based on approval status
 * @param {Object} idCard - ID card object with approval status
 * @returns {string} - SVG pattern string
 */
export const getSecurityPatternSVG = (idCard) => {
  if (idCard.has_kebele_pattern && idCard.has_subcity_pattern) {
    return COMPLETE_PATTERN_SVG;
  } else if (idCard.has_kebele_pattern) {
    return KEBELE_PATTERN_SVG;
  } else if (idCard.has_subcity_pattern) {
    return SUBCITY_PATTERN_SVG;
  }
  return '';
};

/**
 * Get pattern description for UI display with role-based security
 * CRITICAL SECURITY: Pattern descriptions are restricted by user role
 * @param {Object} idCard - ID card object with approval status
 * @param {Object} user - Current user object with role information
 * @returns {string} - Human readable pattern description
 */
export const getPatternDescription = (idCard, user = null) => {
  // Get user role from localStorage if not provided
  let userRole = user?.role;

  if (!userRole) {
    try {
      const accessToken = localStorage.getItem('accessToken');
      if (accessToken) {
        const base64Url = accessToken.split('.')[1];
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        }).join(''));
        const tokenData = JSON.parse(jsonPayload);
        userRole = tokenData.role;
      }
    } catch (e) {
      console.warn('Could not decode JWT token for role');
    }
  }

  // CRITICAL SECURITY: Clerks should not see security pattern information
  if (userRole === 'clerk') {
    return 'Standard ID Card';
  }

  // Kebele leaders only see kebele pattern information
  if (userRole === 'kebele_leader') {
    if (idCard.has_kebele_pattern) {
      return 'Kebele Approved';
    }
    return 'Pending Approval';
  }

  // Subcity admins and above can see full pattern information
  if (userRole === 'subcity_admin' || userRole === 'city_admin' || userRole === 'superadmin') {
    if (idCard.has_kebele_pattern && idCard.has_subcity_pattern) {
      return 'Fully Approved - Complete Security Pattern';
    } else if (idCard.has_kebele_pattern) {
      return 'Kebele Approved - Partial Security Pattern';
    } else if (idCard.has_subcity_pattern) {
      return 'Subcity Approved - Partial Security Pattern';
    }
  }

  return 'Standard ID Card';
};
