from django.db import models
from django.contrib.auth import get_user_model
from idcards.models import IDCardStatus

User = get_user_model()


class ApprovalAction(models.TextChoices):
    SUBMIT = 'submit', 'Submit'
    APPROVE = 'approve', 'Approve'
    REJECT = 'reject', 'Reject'
    PRINT = 'print', 'Print'
    ISSUE = 'issue', 'Issue'
    REVOKE = 'revoke', 'Revoke'


class WorkflowLog(models.Model):
    id_card = models.ForeignKey('idcards.IDCard', on_delete=models.CASCADE, related_name='workflow_logs')
    action = models.CharField(max_length=20, choices=ApprovalAction.choices)
    from_status = models.CharField(max_length=20, choices=IDCardStatus.choices)
    to_status = models.CharField(max_length=20, choices=IDCardStatus.choices)
    comment = models.TextField(blank=True, null=True)
    performed_by = models.ForeignKey('users.User', on_delete=models.SET_NULL, null=True)
    performed_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-performed_at']

    def __str__(self):
        return f"{self.action} by {self.performed_by} at {self.performed_at}"


class CitizenClearanceRequest(models.Model):
    """
    Model for managing citizen clearance requests for citizens leaving the city.

    This is for citizens moving to areas outside the city that don't use our system.
    Unlike transfers, this generates a clearance letter instead of moving data.

    Workflow:
    1. Kebele leader creates clearance request on behalf of citizen
    2. Subcity admin reviews and approves/rejects the clearance
    3. If approved, system generates official clearance letter
    4. Citizen gets clearance letter and can leave with official documentation
    """

    STATUS_CHOICES = [
        ('pending', 'Pending Review'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('issued', 'Clearance Letter Issued'),
        ('cancelled', 'Cancelled'),
    ]

    CLEARANCE_REASON_CHOICES = [
        ('relocation_outside_city', 'Relocation Outside City'),
        ('marriage_outside_city', 'Marriage Outside City'),
        ('work_outside_city', 'Work/Employment Outside City'),
        ('education_outside_city', 'Education Outside City'),
        ('family_outside_city', 'Family Reasons Outside City'),
        ('emigration', 'Emigration/Moving Abroad'),
        ('other', 'Other'),
    ]

    # Clearance identification
    clearance_id = models.CharField(max_length=20, unique=True, editable=False)

    # Citizen requesting clearance
    citizen_id = models.IntegerField(help_text="ID of citizen in kebele")
    citizen_name = models.CharField(max_length=255, help_text="Full name of citizen")
    citizen_digital_id = models.CharField(max_length=50, blank=True, null=True)

    # Source kebele (where citizen currently resides)
    source_kebele = models.ForeignKey(
        'tenants.Tenant',
        on_delete=models.CASCADE,
        related_name='clearance_requests',
        help_text="Kebele where citizen currently resides"
    )

    # Destination information (free text since it's outside our system)
    destination_location = models.CharField(
        max_length=255,
        help_text="Where the citizen is moving to (city, region, country)"
    )

    # Clearance details
    clearance_reason = models.CharField(
        max_length=30,
        choices=CLEARANCE_REASON_CHOICES,
        default='relocation_outside_city'
    )
    reason_description = models.TextField(
        help_text="Detailed reason for requesting clearance"
    )

    # Status and workflow
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending'
    )

    # Users involved in the process
    requested_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='clearance_requests_made',
        help_text="Kebele leader who initiated the request"
    )

    reviewed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='clearance_requests_reviewed',
        help_text="Subcity admin who reviewed the request"
    )

    issued_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='clearance_requests_issued',
        help_text="User who issued the clearance letter"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    reviewed_at = models.DateTimeField(null=True, blank=True)
    issued_at = models.DateTimeField(null=True, blank=True)

    # Required documents for clearance request
    application_letter = models.FileField(
        upload_to='clearance_documents/application_letters/',
        null=True,
        blank=True,
        help_text="Application letter from citizen requesting clearance"
    )

    current_kebele_id = models.FileField(
        upload_to='clearance_documents/kebele_ids/',
        null=True,
        blank=True,
        help_text="Current kebele ID card of the citizen"
    )

    # Additional supporting documents
    supporting_documents = models.FileField(
        upload_to='clearance_documents/supporting/',
        null=True,
        blank=True,
        help_text="Additional supporting documents (job offer, marriage certificate, etc.)"
    )

    # Document upload timestamps
    documents_uploaded_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When documents were uploaded"
    )

    # Review notes
    review_notes = models.TextField(
        blank=True,
        null=True,
        help_text="Notes from subcity admin"
    )

    # Clearance letter details
    clearance_letter_path = models.CharField(
        max_length=500,
        blank=True,
        null=True,
        help_text="Path to generated clearance letter PDF"
    )

    class Meta:
        db_table = 'citizen_clearance_requests'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['source_kebele', 'status']),
            models.Index(fields=['created_at']),
            models.Index(fields=['citizen_id']),
        ]

    def __str__(self):
        return f"Clearance {self.clearance_id}: {self.citizen_name} → {self.destination_location}"

    def save(self, *args, **kwargs):
        if not self.clearance_id:
            # Generate unique clearance ID: CL + year + month + sequential number
            from django.utils import timezone
            now = timezone.now()
            year_month = now.strftime('%Y%m')

            # Get the last clearance request for this year-month
            last_clearance = CitizenClearanceRequest.objects.filter(
                clearance_id__startswith=f'CL{year_month}'
            ).order_by('-clearance_id').first()

            if last_clearance:
                # Extract the sequential number and increment
                last_seq = int(last_clearance.clearance_id[-4:])
                new_seq = last_seq + 1
            else:
                new_seq = 1

            self.clearance_id = f'CL{year_month}{new_seq:04d}'

        super().save(*args, **kwargs)

    @property
    def can_be_approved(self):
        """Check if clearance request can be approved."""
        return self.status == 'pending'

    @property
    def can_be_rejected(self):
        """Check if clearance request can be rejected."""
        return self.status == 'pending'

    @property
    def can_be_issued(self):
        """Check if clearance letter can be issued."""
        return self.status == 'approved'

    @property
    def can_be_cancelled(self):
        """Check if clearance can be cancelled."""
        return self.status in ['pending', 'approved']


class CitizenTransferRequest(models.Model):
    """
    Model for managing citizen transfer requests between kebeles.

    Workflow:
    1. Kebele A leader creates transfer request on behalf of citizen
    2. Kebele B leader receives request and can accept/reject
    3. If accepted, Kebele A leader completes the transfer
    4. Citizen is disabled in Kebele A and copied to Kebele B
    """

    STATUS_CHOICES = [
        ('pending', 'Pending Review'),
        ('accepted', 'Accepted'),
        ('rejected', 'Rejected'),
        ('completed', 'Transfer Completed'),
        ('cancelled', 'Cancelled'),
    ]

    TRANSFER_REASON_CHOICES = [
        ('relocation', 'Relocation/Moving'),
        ('marriage', 'Marriage'),
        ('work', 'Work/Employment'),
        ('education', 'Education'),
        ('family', 'Family Reasons'),
        ('other', 'Other'),
    ]

    # Transfer identification
    transfer_id = models.CharField(max_length=20, unique=True, editable=False)

    # Citizen being transferred
    citizen_id = models.IntegerField(null=True, blank=True, help_text="ID of citizen in source kebele (null for destination schema records)")
    citizen_name = models.CharField(max_length=255, help_text="Full name of citizen")
    citizen_digital_id = models.CharField(max_length=50, blank=True, null=True)

    # Source kebele (where citizen currently resides)
    source_kebele = models.ForeignKey(
        'tenants.Tenant',
        on_delete=models.CASCADE,
        related_name='outgoing_transfers',
        help_text="Kebele where citizen currently resides"
    )

    # Destination kebele (where citizen wants to transfer)
    destination_kebele = models.ForeignKey(
        'tenants.Tenant',
        on_delete=models.CASCADE,
        related_name='incoming_transfers',
        help_text="Kebele where citizen wants to transfer"
    )

    # Transfer details
    transfer_reason = models.CharField(
        max_length=20,
        choices=TRANSFER_REASON_CHOICES,
        default='relocation'
    )
    reason_description = models.TextField(
        blank=True,
        null=True,
        help_text="Detailed reason for transfer"
    )

    # Status and workflow
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending'
    )

    # Users involved in the process
    requested_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='transfer_requests_made',
        help_text="Kebele A leader who initiated the request"
    )

    reviewed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='transfer_requests_reviewed',
        help_text="Kebele B leader who reviewed the request"
    )

    completed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='transfer_requests_completed',
        help_text="Kebele A leader who completed the transfer"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    reviewed_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    # Required documents for transfer request
    application_letter = models.FileField(
        upload_to='transfer_documents/application_letters/',
        null=True,
        blank=True,
        help_text="Application letter from citizen requesting transfer"
    )

    current_kebele_id = models.FileField(
        upload_to='transfer_documents/kebele_ids/',
        null=True,
        blank=True,
        help_text="Current kebele ID card of the citizen"
    )

    # Document upload timestamps
    documents_uploaded_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When documents were uploaded"
    )

    # Review notes
    review_notes = models.TextField(
        blank=True,
        null=True,
        help_text="Notes from destination kebele leader"
    )

    # Transfer completion details
    new_citizen_id = models.IntegerField(
        null=True,
        blank=True,
        help_text="ID of citizen in destination kebele after transfer"
    )

    class Meta:
        db_table = 'citizen_transfer_requests'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['source_kebele', 'status']),
            models.Index(fields=['destination_kebele', 'status']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"Transfer {self.transfer_id}: {self.citizen_name} ({self.source_kebele.name} → {self.destination_kebele.name})"

    def save(self, *args, **kwargs):
        if not self.transfer_id:
            # Generate unique transfer ID: TR + year + month + sequential number
            from django.utils import timezone
            now = timezone.now()
            year_month = now.strftime('%Y%m')

            # Get the last transfer request for this year-month
            last_transfer = CitizenTransferRequest.objects.filter(
                transfer_id__startswith=f'TR{year_month}'
            ).order_by('-transfer_id').first()

            if last_transfer:
                # Extract the sequential number and increment
                last_seq = int(last_transfer.transfer_id[-4:])
                new_seq = last_seq + 1
            else:
                new_seq = 1

            self.transfer_id = f'TR{year_month}{new_seq:04d}'

        super().save(*args, **kwargs)

    @property
    def can_be_accepted(self):
        """Check if transfer request can be accepted."""
        return self.status == 'pending'

    @property
    def can_be_rejected(self):
        """Check if transfer request can be rejected."""
        return self.status == 'pending'

    @property
    def can_be_completed(self):
        """Check if transfer can be completed."""
        return self.status == 'accepted'

    @property
    def can_be_cancelled(self):
        """Check if transfer can be cancelled."""
        return self.status in ['pending', 'accepted']


class CitizenTransferHistory(models.Model):
    """
    Model to track the history of citizen transfers for reporting purposes.
    This maintains a record even after the transfer is completed.
    """

    transfer_request = models.OneToOneField(
        CitizenTransferRequest,
        on_delete=models.CASCADE,
        related_name='history'
    )

    # Original citizen data (snapshot)
    original_citizen_data = models.JSONField(
        help_text="Complete citizen data from source kebele"
    )

    # Transfer metadata
    transfer_date = models.DateTimeField(auto_now_add=True)
    source_kebele_name = models.CharField(max_length=255)
    destination_kebele_name = models.CharField(max_length=255)

    # For reporting and analytics
    transfer_year = models.IntegerField()
    transfer_month = models.IntegerField()

    class Meta:
        db_table = 'citizen_transfer_history'
        ordering = ['-transfer_date']
        indexes = [
            models.Index(fields=['transfer_year', 'transfer_month']),
            models.Index(fields=['source_kebele_name']),
            models.Index(fields=['destination_kebele_name']),
        ]

    def __str__(self):
        return f"Transfer History: {self.transfer_request.citizen_name} ({self.transfer_date.strftime('%Y-%m-%d')})"

    def save(self, *args, **kwargs):
        if not self.transfer_year:
            self.transfer_year = self.transfer_date.year
        if not self.transfer_month:
            self.transfer_month = self.transfer_date.month
        super().save(*args, **kwargs)
