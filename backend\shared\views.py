from rest_framework import viewsets, filters
from rest_framework.permissions import <PERSON><PERSON><PERSON><PERSON><PERSON>ted, AllowAny
from django_filters.rest_framework import DjangoFilterBackend
from .models import (
    Country,
    Region,
    Religion,
    CitizenStatus,
    MaritalStatus,
    DocumentType,
    EmploymentType,
    Relationship,
    CurrentStatus,
    BiometricType,
    Ketena
)
from .serializers import (
    CountrySerializer,
    RegionSerializer,
    ReligionSerializer,
    CitizenStatusSerializer,
    MaritalStatusSerializer,
    DocumentTypeSerializer,
    EmploymentTypeSerializer,
    RelationshipSerializer,
    CurrentStatusSerializer,
    BiometricTypeSerializer,
    KetenaSerializer
)

class CountryViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows countries to be viewed.
    """
    queryset = Country.objects.filter(is_active=True)
    serializer_class = CountrySerializer
    permission_classes = [AllowAny]  # Allow public access to reference data
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['name', 'code']
    search_fields = ['name', 'code', 'capital_city']
    ordering_fields = ['name', 'code']
    ordering = ['name']
    pagination_class = None


class RegionViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows regions to be viewed.
    """
    queryset = Region.objects.filter(is_active=True)
    serializer_class = RegionSerializer
    permission_classes = [AllowAny]  # Allow public access to reference data
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['name', 'code', 'country']
    search_fields = ['name', 'code', 'country__name']
    ordering_fields = ['name', 'code', 'country__name']
    ordering = ['country__name', 'name']
    pagination_class = None


class ReligionViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows religions to be viewed.
    """
    queryset = Religion.objects.filter(is_active=True)
    serializer_class = ReligionSerializer
    permission_classes = [AllowAny]  # Allow public access to reference data
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['name']
    search_fields = ['name', 'description']
    ordering_fields = ['name']
    ordering = ['name']
    pagination_class = None


class CitizenStatusViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows citizen statuses to be viewed.
    """
    queryset = CitizenStatus.objects.filter(is_active=True)
    serializer_class = CitizenStatusSerializer
    permission_classes = [AllowAny]  # Allow public access to reference data
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['name']
    search_fields = ['name', 'description']
    ordering_fields = ['name']
    ordering = ['name']
    pagination_class = None


class MaritalStatusViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows marital statuses to be viewed.
    """
    queryset = MaritalStatus.objects.filter(is_active=True)
    serializer_class = MaritalStatusSerializer
    permission_classes = [AllowAny]  # Allow public access to reference data
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['name']
    search_fields = ['name', 'description']
    ordering_fields = ['name']
    ordering = ['name']
    pagination_class = None


class DocumentTypeViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows document types to be viewed.
    """
    queryset = DocumentType.objects.filter(is_active=True)
    serializer_class = DocumentTypeSerializer
    permission_classes = [AllowAny]  # Allow public access to reference data
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['name']
    search_fields = ['name', 'description']
    ordering_fields = ['name']
    ordering = ['name']
    pagination_class = None


class EmploymentTypeViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows employment types to be viewed.
    """
    queryset = EmploymentType.objects.filter(is_active=True)
    serializer_class = EmploymentTypeSerializer
    permission_classes = [AllowAny]  # Allow public access to reference data
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['name']
    search_fields = ['name', 'description']
    ordering_fields = ['name']
    ordering = ['name']
    pagination_class = None


class RelationshipViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows relationships to be viewed.
    """
    queryset = Relationship.objects.filter(is_active=True)
    serializer_class = RelationshipSerializer
    permission_classes = [AllowAny]  # Allow public access to reference data
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['name']
    search_fields = ['name', 'description']
    ordering_fields = ['name']
    ordering = ['name']
    pagination_class = None


class CurrentStatusViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows current statuses to be viewed.
    """
    queryset = CurrentStatus.objects.filter(is_active=True)
    serializer_class = CurrentStatusSerializer
    permission_classes = [AllowAny]  # Allow public access to reference data
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['name']
    search_fields = ['name', 'description']
    ordering_fields = ['name']
    ordering = ['name']
    pagination_class = None


class BiometricTypeViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows biometric types to be viewed.
    """
    queryset = BiometricType.objects.filter(is_active=True)
    serializer_class = BiometricTypeSerializer
    permission_classes = [AllowAny]  # Allow public access to reference data
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['name']
    search_fields = ['name', 'description']
    ordering_fields = ['name']
    ordering = ['name']
    pagination_class = None


class KetenaViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows ketenas to be viewed.
    """
    queryset = Ketena.objects.filter(is_active=True)
    serializer_class = KetenaSerializer
    permission_classes = [AllowAny]  # Allow public access to reference data
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['name', 'code']
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['name', 'code']
    ordering = ['name']
    pagination_class = None
