@echo off
echo 🔍 GoID Database Debug
echo =====================
echo.

echo 📊 Container Status:
echo ====================
docker-compose -f docker-compose.production.yml ps

echo.
echo 🗄️ Database Container Logs:
echo ===========================
docker-compose -f docker-compose.production.yml logs db

echo.
echo 🔧 Backend Container Logs:
echo ==========================
docker-compose -f docker-compose.production.yml logs backend

echo.
echo 🌐 Network Information:
echo =======================
docker network ls
docker network inspect goid_goid_network

echo.
echo 🔗 Testing Database Connection:
echo ===============================
echo Testing direct connection to database...
docker-compose -f docker-compose.production.yml exec db psql -U goid_user -d goid_db -c "SELECT version();"

echo.
echo 📋 Environment Variables in Backend:
echo ====================================
docker-compose -f docker-compose.production.yml exec backend env | grep -E "(DATABASE|DB_|POSTGRES)"

echo.
echo ✅ Debug complete!
pause
