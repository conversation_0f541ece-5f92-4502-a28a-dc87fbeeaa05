/**
 * Security Pattern Styles for ID Cards
 *
 * These styles apply security patterns to ID cards based on approval levels:
 * - Kebele approval: Left half castle pattern
 * - Subcity approval: Right half castle pattern
 * - Complete approval: Full castle pattern
 */

/* Base security pattern container */
.security-pattern-container {
  position: relative;
  overflow: hidden;
}

/* Static decorative pattern overlay - Applied to all ID cards */
.security-pattern-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 5; /* Higher than security patterns */
  opacity: 0.4;
  background:
    /* Large decorative circles - Blue tones like reference image */
    radial-gradient(circle at 15% 25%, rgba(52, 152, 219, 0.6) 0%, rgba(52, 152, 219, 0.2) 30%, transparent 50%),
    radial-gradient(circle at 85% 15%, rgba(41, 128, 185, 0.5) 0%, rgba(41, 128, 185, 0.15) 25%, transparent 40%),
    radial-gradient(circle at 75% 75%, rgba(52, 152, 219, 0.7) 0%, rgba(52, 152, 219, 0.25) 35%, transparent 55%),
    radial-gradient(circle at 25% 85%, rgba(41, 128, 185, 0.5) 0%, rgba(41, 128, 185, 0.18) 28%, transparent 45%),

    /* Medium circles with castle-like patterns */
    radial-gradient(circle at 60% 40%, rgba(30, 139, 195, 0.4) 0%, rgba(30, 139, 195, 0.15) 15%, transparent 20%),
    radial-gradient(circle at 40% 70%, rgba(52, 152, 219, 0.35) 0%, rgba(52, 152, 219, 0.12) 12%, transparent 18%),
    radial-gradient(circle at 80% 60%, rgba(41, 128, 185, 0.45) 0%, rgba(41, 128, 185, 0.18) 18%, transparent 22%),

    /* Castle-like geometric patterns - More visible */
    conic-gradient(from 0deg at 60% 40%, transparent 0deg, rgba(30, 139, 195, 0.3) 45deg, transparent 90deg, rgba(30, 139, 195, 0.2) 135deg, transparent 180deg, rgba(30, 139, 195, 0.3) 225deg, transparent 270deg, rgba(30, 139, 195, 0.2) 315deg, transparent 360deg),
    conic-gradient(from 45deg at 40% 70%, transparent 0deg, rgba(52, 152, 219, 0.25) 60deg, transparent 120deg, rgba(52, 152, 219, 0.15) 180deg, transparent 240deg, rgba(52, 152, 219, 0.25) 300deg, transparent 360deg),
    conic-gradient(from 90deg at 80% 60%, transparent 0deg, rgba(41, 128, 185, 0.35) 30deg, transparent 90deg, rgba(41, 128, 185, 0.25) 150deg, transparent 210deg, rgba(41, 128, 185, 0.35) 270deg, transparent 330deg),

    /* Small decorative dots - More visible */
    radial-gradient(circle at 30% 30%, rgba(52, 152, 219, 0.8) 0%, rgba(52, 152, 219, 0.3) 8%, transparent 12%),
    radial-gradient(circle at 70% 20%, rgba(41, 128, 185, 0.7) 0%, rgba(41, 128, 185, 0.25) 6%, transparent 10%),
    radial-gradient(circle at 20% 60%, rgba(30, 139, 195, 0.75) 0%, rgba(30, 139, 195, 0.28) 7%, transparent 11%),
    radial-gradient(circle at 90% 80%, rgba(52, 152, 219, 0.7) 0%, rgba(52, 152, 219, 0.22) 6%, transparent 10%),
    radial-gradient(circle at 50% 90%, rgba(41, 128, 185, 0.6) 0%, rgba(41, 128, 185, 0.18) 5%, transparent 9%),

    /* Subtle geometric patterns */
    linear-gradient(45deg, transparent 48%, rgba(52, 152, 219, 0.15) 49%, rgba(52, 152, 219, 0.15) 51%, transparent 52%),
    linear-gradient(-45deg, transparent 48%, rgba(41, 128, 185, 0.12) 49%, rgba(41, 128, 185, 0.12) 51%, transparent 52%);

  background-size:
    120px 120px, 80px 80px, 140px 140px, 90px 90px,
    40px 40px, 35px 35px, 45px 45px,
    25px 25px, 20px 20px, 30px 30px,
    15px 15px, 12px 12px, 14px 14px, 13px 13px, 11px 11px,
    60px 60px, 60px 60px;
}

/* New SVG Security Pattern Background for all security patterns */
.security-pattern-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1; /* Below shiny pattern but above content */
  opacity: 0.6;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='800' height='500' viewBox='0 0 800 500' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3ClinearGradient id='base' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' stop-color='%23fefcf5'/%3E%3Cstop offset='50%25' stop-color='%23f2e9b3'/%3E%3Cstop offset='100%25' stop-color='%23bfa945'/%3E%3C/linearGradient%3E%3ClinearGradient id='headerWhite' x1='0%25' y1='0%25' x2='0%25' y2='100%25'%3E%3Cstop offset='0%25' stop-color='%23fffefb' stop-opacity='0.97'/%3E%3Cstop offset='100%25' stop-color='%23e9e7df' stop-opacity='0.2'/%3E%3C/linearGradient%3E%3Cpattern id='guilloche' width='120' height='120' patternUnits='userSpaceOnUse'%3E%3Cpath d='M60,0 C80,30 40,90 60,120 M0,60 C30,80 90,40 120,60 M30,0 C45,30 75,30 90,0 M30,120 C45,90 75,90 90,120' stroke='%23c9a034' fill='none' stroke-width='1.4' opacity='0.7'/%3E%3Ccircle cx='60' cy='60' r='20' stroke='%23d9b94b' fill='none' stroke-width='1.1' opacity='0.55'/%3E%3C/pattern%3E%3Cpattern id='microtext' width='400' height='40' patternUnits='userSpaceOnUse'%3E%3Ctext x='0' y='15' font-size='8' fill='%23f9f7f1' opacity='0.85' font-family='Courier New, monospace' letter-spacing='2px' font-weight='700'%3E• SECURE DOCUMENT • FEDERAL IDENTITY • REPUBLIC OF ETHIOPIA • OFFICIAL USE ONLY •%3C/text%3E%3Ctext x='0' y='32' font-size='8' fill='%23f9f7f1' opacity='0.85' font-family='Courier New, monospace' letter-spacing='2px' font-weight='700'%3E• ID-ETH-2024 • ANTI-COUNTERFEIT • UV-SECURITY • HOLOGRAM •%3C/text%3E%3C/pattern%3E%3Cpattern id='hexgrid' width='50' height='43.3' patternUnits='userSpaceOnUse'%3E%3Cpolygon points='25,0 50,21.65 50,65 25,86.6 0,65 0,21.65' stroke='%23a87f17' fill='none' stroke-width='1' opacity='0.3'/%3E%3Ccircle cx='25' cy='21.65' r='3' fill='%23e9c85b' opacity='0.8'/%3E%3Ccircle cx='25' cy='65' r='3' fill='%23e9c85b' opacity='0.8'/%3E%3C/pattern%3E%3Cpattern id='waveCross' width='300' height='300' patternUnits='userSpaceOnUse'%3E%3Cpath d='M0 150 Q75 0 150 150 T300 150' fill='none' stroke='%239a8c42' stroke-width='1.6' opacity='0.55'/%3E%3Cpath d='M150 0 Q0 75 150 150 T150 300' fill='none' stroke='%23d1a924' stroke-width='1.7' opacity='0.6'/%3E%3Cpath d='M0 75 Q150 225 300 75' fill='none' stroke='%23ffd54c' stroke-width='1.3' opacity='0.5'/%3E%3C/pattern%3E%3Cpattern id='securityDots' width='12' height='12' patternUnits='userSpaceOnUse'%3E%3Ccircle cx='6' cy='6' r='2.4' fill='%23fadb5f' opacity='0.9'/%3E%3Ccircle cx='6' cy='6' r='1.0' fill='%23fffbee' opacity='0.95'/%3E%3C/pattern%3E%3Cpattern id='goldSecurity' width='20' height='20' patternUnits='userSpaceOnUse'%3E%3Crect width='20' height='20' fill='%23b99218' opacity='0.15'/%3E%3Cpath d='M0,0 L20,20 M20,0 L0,20' stroke='%23f7df48' stroke-width='1.6' opacity='0.8'/%3E%3C/pattern%3E%3CradialGradient id='burst' cx='50%25' cy='50%25' r='50%25'%3E%3Cstop offset='0%25' stop-color='%23fffde7' stop-opacity='0.92'/%3E%3Cstop offset='60%25' stop-color='%23fbd13e' stop-opacity='0.45'/%3E%3Cstop offset='90%25' stop-color='%23ab8635' stop-opacity='0.1'/%3E%3Cstop offset='100%25' stop-color='%236f5c26' stop-opacity='0'/%3E%3C/radialGradient%3E%3Cpattern id='holoPattern' width='500' height='500' patternUnits='userSpaceOnUse'%3E%3ClinearGradient id='holoGradient' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' stop-color='%23d9cb8a'/%3E%3Cstop offset='25%25' stop-color='%23eee2a8'/%3E%3Cstop offset='50%25' stop-color='%23f4e285'/%3E%3Cstop offset='75%25' stop-color='%23e6c55c'/%3E%3Cstop offset='100%25' stop-color='%23f9f4d1'/%3E%3C/linearGradient%3E%3Cg stroke='url(%23holoGradient)' stroke-width='2' fill='none' opacity='0.37'%3E%3Cpath d='M0 100 Q125 0 250 100 T500 100'/%3E%3Cpath d='M0 300 Q125 200 250 300 T500 300'/%3E%3Cpath d='M100 0 Q0 125 100 250 T100 500'/%3E%3Cpath d='M300 0 Q200 125 300 250 T300 500'/%3E%3Ccircle cx='250' cy='250' r='200' stroke-dasharray='12,12'/%3E%3C/g%3E%3C/pattern%3E%3Cpattern id='uvPattern' width='80' height='80' patternUnits='userSpaceOnUse'%3E%3Crect width='80' height='80' fill='%23000' opacity='0'/%3E%3Cpath d='M0 40 L80 40 M40 0 L40 80' stroke='%237fffd4' stroke-width='1.3' opacity='0.15'/%3E%3Ctext x='40' y='45' font-size='16' fill='%237fffd4' opacity='0.12' font-family='Arial' text-anchor='middle' font-weight='800'%3EUV%3C/text%3E%3C/pattern%3E%3Cfilter id='emboss'%3E%3CfeGaussianBlur in='SourceAlpha' stdDeviation='1.5' result='blur'/%3E%3CfeSpecularLighting in='blur' surfaceScale='6' specularConstant='0.7' specularExponent='15' lighting-color='%23ffffff' result='spec'%3E%3CfePointLight x='-5000' y='-10000' z='20000'/%3E%3C/feSpecularLighting%3E%3CfeComposite in='spec' in2='SourceGraphic' operator='in' result='specOut'/%3E%3CfeComposite in='SourceGraphic' in2='specOut' operator='arithmetic' k1='0' k2='1' k3='1' k4='0'/%3E%3C/filter%3E%3Cpattern id='borderGuilloche' width='30' height='30' patternUnits='userSpaceOnUse'%3E%3Cpath d='M15,0 C20,7.5 10,22.5 15,30 M0,15 C7.5,20 22.5,10 30,15' stroke='%23a88510' fill='none' stroke-width='1.6' opacity='0.65'/%3E%3C/pattern%3E%3CradialGradient id='sparkleGlow' cx='50%25' cy='50%25' r='50%25'%3E%3Cstop offset='0%25' stop-color='%23fff9cc' stop-opacity='1'/%3E%3Cstop offset='80%25' stop-color='%23fff9cc' stop-opacity='0'/%3E%3C/radialGradient%3E%3Ccircle id='sparkle' cx='0' cy='0' r='6' fill='url(%23sparkleGlow)'/%3E%3C/defs%3E%3Crect width='100%25' height='100%25' fill='url(%23base)'/%3E%3Crect width='100%25' height='120' fill='url(%23headerWhite)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23holoPattern)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23guilloche)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23hexgrid)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23microtext)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23waveCross)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23uvPattern)'/%3E%3Ccircle cx='700' cy='420' r='110' fill='url(%23burst)' filter='url(%23emboss)' opacity='0.9'/%3E%3Cuse href='%23sparkle' x='670' y='370' opacity='0.6'/%3E%3Cuse href='%23sparkle' x='720' y='450' opacity='0.5'/%3E%3Cuse href='%23sparkle' x='685' y='410' opacity='0.45'/%3E%3Cuse href='%23sparkle' x='730' y='390' opacity='0.4'/%3E%3Cpath d='M0 450 C150 350 650 500 800 450' fill='none' stroke='%23a88510' stroke-width='4' opacity='0.45' stroke-dasharray='14 8'/%3E%3Cpath d='M0 100 C200 200 600 0 800 100' fill='none' stroke='%23927a2e' stroke-width='3' opacity='0.55'/%3E%3Crect x='650' y='350' width='150' height='150' fill='url(%23securityDots)' rx='20' ry='20'/%3E%3Cuse href='%23sparkle' x='680' y='380' opacity='0.6'/%3E%3Cuse href='%23sparkle' x='740' y='420' opacity='0.55'/%3E%3Cuse href='%23sparkle' x='720' y='390' opacity='0.5'/%3E%3Cuse href='%23sparkle' x='700' y='430' opacity='0.45'/%3E%3Crect x='5' y='5' width='790' height='490' fill='none' stroke='url(%23borderGuilloche)' stroke-width='7' rx='20' ry='20'/%3E%3Ccircle cx='20' cy='20' r='18' stroke='%23d8b924' fill='none' stroke-width='3' opacity='0.8'/%3E%3Ccircle cx='780' cy='20' r='18' stroke='%23d8b924' fill='none' stroke-width='3' opacity='0.8'/%3E%3Ccircle cx='20' cy='480' r='18' stroke='%23d8b924' fill='none' stroke-width='3' opacity='0.8'/%3E%3Ccircle cx='780' cy='480' r='18' stroke='%23d8b924' fill='none' stroke-width='3' opacity='0.8'/%3E%3C/svg%3E");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

/* Security pattern base layer for castle patterns */
.security-pattern-kebele,
.security-pattern-subcity,
.security-pattern-complete {
  position: relative;
}

.security-pattern-kebele::after,
.security-pattern-subcity::after,
.security-pattern-complete::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 2; /* Between background pattern and shiny pattern */
  opacity: 0.6;
}

/* Kebele approval pattern (left half) - Enhanced Fasil Castle Watermark */
.security-pattern-kebele::after {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='400' height='300' viewBox='0 0 400 300' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='fasilCastlePattern' patternUnits='userSpaceOnUse' width='400' height='300'%3E%3Cg fill='%23F4D03F' opacity='0.4'%3E%3C!-- Main castle base wall with curved connection --%3E%3Cpath d='M 30 180 Q 50 170 80 175 L 320 175 Q 350 170 370 180 L 370 270 L 30 270 Z' fill='%23F4D03F'/%3E%3C!-- Left rounded tower --%3E%3Ccircle cx='60' cy='140' r='35' fill='%23F4D03F'/%3E%3Crect x='25' y='140' width='70' height='130' fill='%23F4D03F'/%3E%3C!-- Left tower battlements --%3E%3Crect x='30' y='125' width='8' height='20'/%3E%3Crect x='42' y='125' width='8' height='20'/%3E%3Crect x='54' y='125' width='8' height='20'/%3E%3Crect x='66' y='125' width='8' height='20'/%3E%3Crect x='78' y='125' width='8' height='20'/%3E%3Crect x='90' y='125' width='8' height='20'/%3E%3C!-- Left tower arched windows --%3E%3Cpath d='M 45 160 Q 45 150 60 150 Q 75 150 75 160 L 75 190 L 45 190 Z' fill='white' opacity='0.9'/%3E%3Cpath d='M 45 200 Q 45 195 52.5 195 Q 60 195 60 200 L 60 215 L 45 215 Z' fill='white' opacity='0.8'/%3E%3Cpath d='M 65 200 Q 65 195 72.5 195 Q 80 195 80 200 L 80 215 L 65 215 Z' fill='white' opacity='0.8'/%3E%3C!-- Central rounded tower (larger) --%3E%3Ccircle cx='200' cy='120' r='45' fill='%23F4D03F'/%3E%3Crect x='155' y='120' width='90' height='150' fill='%23F4D03F'/%3E%3C!-- Central tower battlements --%3E%3Crect x='160' y='100' width='10' height='25'/%3E%3Crect x='175' y='100' width='10' height='25'/%3E%3Crect x='190' y='100' width='10' height='25'/%3E%3Crect x='205' y='100' width='10' height='25'/%3E%3Crect x='220' y='100' width='10' height='25'/%3E%3Crect x='235' y='100' width='10' height='25'/%3E%3C!-- Central tower large arched windows --%3E%3Cpath d='M 170 150 Q 170 135 200 135 Q 230 135 230 150 L 230 190 L 170 190 Z' fill='white' opacity='0.9'/%3E%3Cpath d='M 175 200 Q 175 195 187.5 195 Q 200 195 200 200 L 200 220 L 175 220 Z' fill='white' opacity='0.8'/%3E%3Cpath d='M 205 200 Q 205 195 217.5 195 Q 230 195 230 200 L 230 220 L 205 220 Z' fill='white' opacity='0.8'/%3E%3C!-- Right rounded tower with multi-level structure --%3E%3Ccircle cx='340' cy='130' r='40' fill='%23F4D03F'/%3E%3Crect x='300' y='130' width='80' height='140' fill='%23F4D03F'/%3E%3C!-- Right tower upper level --%3E%3Crect x='310' y='110' width='60' height='60' fill='%23F4D03F'/%3E%3C!-- Right tower battlements --%3E%3Crect x='305' y='105' width='8' height='20'/%3E%3Crect x='318' y='105' width='8' height='20'/%3E%3Crect x='331' y='105' width='8' height='20'/%3E%3Crect x='344' y='105' width='8' height='20'/%3E%3Crect x='357' y='105' width='8' height='20'/%3E%3Crect x='370' y='105' width='8' height='20'/%3E%3C!-- Right tower arched windows --%3E%3Cpath d='M 315 150 Q 315 140 340 140 Q 365 140 365 150 L 365 180 L 315 180 Z' fill='white' opacity='0.9'/%3E%3Cpath d='M 320 190 Q 320 185 332.5 185 Q 345 185 345 190 L 345 210 L 320 210 Z' fill='white' opacity='0.8'/%3E%3Cpath d='M 350 190 Q 350 185 362.5 185 Q 375 185 375 190 L 375 210 L 350 210 Z' fill='white' opacity='0.8'/%3E%3C!-- Connecting walls with curved tops --%3E%3Cpath d='M 95 175 Q 125 165 155 175 L 155 270 L 95 270 Z' fill='%23F4D03F'/%3E%3Cpath d='M 245 175 Q 275 165 300 175 L 300 270 L 245 270 Z' fill='%23F4D03F'/%3E%3C!-- Wall battlements --%3E%3Crect x='105' y='160' width='6' height='15'/%3E%3Crect x='115' y='160' width='6' height='15'/%3E%3Crect x='125' y='160' width='6' height='15'/%3E%3Crect x='135' y='160' width='6' height='15'/%3E%3Crect x='145' y='160' width='6' height='15'/%3E%3Crect x='255' y='160' width='6' height='15'/%3E%3Crect x='265' y='160' width='6' height='15'/%3E%3Crect x='275' y='160' width='6' height='15'/%3E%3Crect x='285' y='160' width='6' height='15'/%3E%3Crect x='295' y='160' width='6' height='15'/%3E%3C!-- Decorative architectural details --%3E%3Cpath d='M 180 240 Q 180 230 200 230 Q 220 230 220 240 L 220 270 L 180 270 Z' fill='white' opacity='0.9'/%3E%3C/g%3E%3C/pattern%3E%3C/defs%3E%3Crect width='400' height='300' fill='url(%23fasilCastlePattern)'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  clip-path: polygon(0 0, 50% 0, 50% 100%, 0 100%);
}

/* Add visual border for kebele pattern */
.security-pattern-kebele {
  border-left: 5px solid #dfcd68 !important;
  box-shadow: inset 5px 0 10px rgba(204, 188, 95, 0.3) !important;
}

/* Subcity approval pattern (right half) - Enhanced Fasil Castle Watermark */
.security-pattern-subcity::after {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='400' height='300' viewBox='0 0 400 300' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='fasilCastlePatternRight' patternUnits='userSpaceOnUse' width='400' height='300'%3E%3Cg fill='%2358D68D' opacity='0.4'%3E%3C!-- Main castle base wall with curved connection --%3E%3Cpath d='M 30 180 Q 50 170 80 175 L 320 175 Q 350 170 370 180 L 370 270 L 30 270 Z' fill='%2358D68D'/%3E%3C!-- Left rounded tower --%3E%3Ccircle cx='60' cy='140' r='35' fill='%2358D68D'/%3E%3Crect x='25' y='140' width='70' height='130' fill='%2358D68D'/%3E%3C!-- Left tower battlements --%3E%3Crect x='30' y='125' width='8' height='20'/%3E%3Crect x='42' y='125' width='8' height='20'/%3E%3Crect x='54' y='125' width='8' height='20'/%3E%3Crect x='66' y='125' width='8' height='20'/%3E%3Crect x='78' y='125' width='8' height='20'/%3E%3Crect x='90' y='125' width='8' height='20'/%3E%3C!-- Left tower arched windows --%3E%3Cpath d='M 45 160 Q 45 150 60 150 Q 75 150 75 160 L 75 190 L 45 190 Z' fill='white' opacity='0.9'/%3E%3Cpath d='M 45 200 Q 45 195 52.5 195 Q 60 195 60 200 L 60 215 L 45 215 Z' fill='white' opacity='0.8'/%3E%3Cpath d='M 65 200 Q 65 195 72.5 195 Q 80 195 80 200 L 80 215 L 65 215 Z' fill='white' opacity='0.8'/%3E%3C!-- Central rounded tower (larger) --%3E%3Ccircle cx='200' cy='120' r='45' fill='%2358D68D'/%3E%3Crect x='155' y='120' width='90' height='150' fill='%2358D68D'/%3E%3C!-- Central tower battlements --%3E%3Crect x='160' y='100' width='10' height='25'/%3E%3Crect x='175' y='100' width='10' height='25'/%3E%3Crect x='190' y='100' width='10' height='25'/%3E%3Crect x='205' y='100' width='10' height='25'/%3E%3Crect x='220' y='100' width='10' height='25'/%3E%3Crect x='235' y='100' width='10' height='25'/%3E%3C!-- Central tower large arched windows --%3E%3Cpath d='M 170 150 Q 170 135 200 135 Q 230 135 230 150 L 230 190 L 170 190 Z' fill='white' opacity='0.9'/%3E%3Cpath d='M 175 200 Q 175 195 187.5 195 Q 200 195 200 200 L 200 220 L 175 220 Z' fill='white' opacity='0.8'/%3E%3Cpath d='M 205 200 Q 205 195 217.5 195 Q 230 195 230 200 L 230 220 L 205 220 Z' fill='white' opacity='0.8'/%3E%3C!-- Right rounded tower with multi-level structure and balcony --%3E%3Ccircle cx='340' cy='130' r='40' fill='%2358D68D'/%3E%3Crect x='300' y='130' width='80' height='140' fill='%2358D68D'/%3E%3C!-- Right tower upper level --%3E%3Crect x='310' y='110' width='60' height='60' fill='%2358D68D'/%3E%3C!-- Right tower balcony structure --%3E%3Crect x='350' y='160' width='25' height='40' fill='%2358D68D'/%3E%3Cpath d='M 350 160 L 375 160 L 375 200 L 365 200 L 365 170 L 350 170 Z' fill='white' opacity='0.7'/%3E%3C!-- Balcony stairs --%3E%3Crect x='355' y='175' width='3' height='2' fill='white' opacity='0.8'/%3E%3Crect x='355' y='178' width='6' height='2' fill='white' opacity='0.8'/%3E%3Crect x='355' y='181' width='9' height='2' fill='white' opacity='0.8'/%3E%3Crect x='355' y='184' width='12' height='2' fill='white' opacity='0.8'/%3E%3Crect x='355' y='187' width='15' height='2' fill='white' opacity='0.8'/%3E%3C!-- Right tower battlements --%3E%3Crect x='305' y='105' width='8' height='20'/%3E%3Crect x='318' y='105' width='8' height='20'/%3E%3Crect x='331' y='105' width='8' height='20'/%3E%3Crect x='344' y='105' width='8' height='20'/%3E%3Crect x='357' y='105' width='8' height='20'/%3E%3Crect x='370' y='105' width='8' height='20'/%3E%3C!-- Right tower arched windows --%3E%3Cpath d='M 315 150 Q 315 140 340 140 Q 365 140 365 150 L 365 180 L 315 180 Z' fill='white' opacity='0.9'/%3E%3Cpath d='M 320 190 Q 320 185 332.5 185 Q 345 185 345 190 L 345 210 L 320 210 Z' fill='white' opacity='0.8'/%3E%3Cpath d='M 350 190 Q 350 185 362.5 185 Q 375 185 375 190 L 375 210 L 350 210 Z' fill='white' opacity='0.8'/%3E%3C!-- Connecting walls with curved tops --%3E%3Cpath d='M 95 175 Q 125 165 155 175 L 155 270 L 95 270 Z' fill='%2358D68D'/%3E%3Cpath d='M 245 175 Q 275 165 300 175 L 300 270 L 245 270 Z' fill='%2358D68D'/%3E%3C!-- Wall battlements --%3E%3Crect x='105' y='160' width='6' height='15'/%3E%3Crect x='115' y='160' width='6' height='15'/%3E%3Crect x='125' y='160' width='6' height='15'/%3E%3Crect x='135' y='160' width='6' height='15'/%3E%3Crect x='145' y='160' width='6' height='15'/%3E%3Crect x='255' y='160' width='6' height='15'/%3E%3Crect x='265' y='160' width='6' height='15'/%3E%3Crect x='275' y='160' width='6' height='15'/%3E%3Crect x='285' y='160' width='6' height='15'/%3E%3Crect x='295' y='160' width='6' height='15'/%3E%3C!-- Decorative architectural details --%3E%3Cpath d='M 180 240 Q 180 230 200 230 Q 220 230 220 240 L 220 270 L 180 270 Z' fill='white' opacity='0.9'/%3E%3C/g%3E%3C/pattern%3E%3C/defs%3E%3Crect width='400' height='300' fill='url(%23fasilCastlePatternRight)'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  clip-path: polygon(50% 0, 100% 0, 100% 100%, 50% 100%);
}

/* Subcity pattern styling - no additional borders needed */

/* Complete approval pattern (full) - Enhanced Fasil Castle Watermark */
.security-pattern-complete::after {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='400' height='300' viewBox='0 0 400 300' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='fasilCastlePatternComplete' patternUnits='userSpaceOnUse' width='400' height='300'%3E%3Cg fill='%23F39C12' opacity='0.45'%3E%3C!-- Main castle base wall with curved connection --%3E%3Cpath d='M 30 180 Q 50 170 80 175 L 320 175 Q 350 170 370 180 L 370 270 L 30 270 Z' fill='%23F39C12'/%3E%3C!-- Left rounded tower (golden) --%3E%3Ccircle cx='60' cy='140' r='35' fill='%23F4D03F'/%3E%3Crect x='25' y='140' width='70' height='130' fill='%23F4D03F'/%3E%3C!-- Left tower battlements --%3E%3Crect x='30' y='125' width='8' height='20'/%3E%3Crect x='42' y='125' width='8' height='20'/%3E%3Crect x='54' y='125' width='8' height='20'/%3E%3Crect x='66' y='125' width='8' height='20'/%3E%3Crect x='78' y='125' width='8' height='20'/%3E%3Crect x='90' y='125' width='8' height='20'/%3E%3C!-- Left tower arched windows --%3E%3Cpath d='M 45 160 Q 45 150 60 150 Q 75 150 75 160 L 75 190 L 45 190 Z' fill='white' opacity='0.9'/%3E%3Cpath d='M 45 200 Q 45 195 52.5 195 Q 60 195 60 200 L 60 215 L 45 215 Z' fill='white' opacity='0.8'/%3E%3Cpath d='M 65 200 Q 65 195 72.5 195 Q 80 195 80 200 L 80 215 L 65 215 Z' fill='white' opacity='0.8'/%3E%3C!-- Central rounded tower (larger) --%3E%3Ccircle cx='200' cy='120' r='45' fill='%23F39C12'/%3E%3Crect x='155' y='120' width='90' height='150' fill='%23F39C12'/%3E%3C!-- Central tower battlements --%3E%3Crect x='160' y='100' width='10' height='25'/%3E%3Crect x='175' y='100' width='10' height='25'/%3E%3Crect x='190' y='100' width='10' height='25'/%3E%3Crect x='205' y='100' width='10' height='25'/%3E%3Crect x='220' y='100' width='10' height='25'/%3E%3Crect x='235' y='100' width='10' height='25'/%3E%3C!-- Central tower large arched windows --%3E%3Cpath d='M 170 150 Q 170 135 200 135 Q 230 135 230 150 L 230 190 L 170 190 Z' fill='white' opacity='0.9'/%3E%3Cpath d='M 175 200 Q 175 195 187.5 195 Q 200 195 200 200 L 200 220 L 175 220 Z' fill='white' opacity='0.8'/%3E%3Cpath d='M 205 200 Q 205 195 217.5 195 Q 230 195 230 200 L 230 220 L 205 220 Z' fill='white' opacity='0.8'/%3E%3C!-- Right rounded tower with multi-level structure and balcony (green) --%3E%3Ccircle cx='340' cy='130' r='40' fill='%2358D68D'/%3E%3Crect x='300' y='130' width='80' height='140' fill='%2358D68D'/%3E%3C!-- Right tower upper level --%3E%3Crect x='310' y='110' width='60' height='60' fill='%2358D68D'/%3E%3C!-- Right tower balcony structure --%3E%3Crect x='350' y='160' width='25' height='40' fill='%2358D68D'/%3E%3Cpath d='M 350 160 L 375 160 L 375 200 L 365 200 L 365 170 L 350 170 Z' fill='white' opacity='0.7'/%3E%3C!-- Balcony stairs --%3E%3Crect x='355' y='175' width='3' height='2' fill='white' opacity='0.8'/%3E%3Crect x='355' y='178' width='6' height='2' fill='white' opacity='0.8'/%3E%3Crect x='355' y='181' width='9' height='2' fill='white' opacity='0.8'/%3E%3Crect x='355' y='184' width='12' height='2' fill='white' opacity='0.8'/%3E%3Crect x='355' y='187' width='15' height='2' fill='white' opacity='0.8'/%3E%3C!-- Right tower battlements --%3E%3Crect x='305' y='105' width='8' height='20'/%3E%3Crect x='318' y='105' width='8' height='20'/%3E%3Crect x='331' y='105' width='8' height='20'/%3E%3Crect x='344' y='105' width='8' height='20'/%3E%3Crect x='357' y='105' width='8' height='20'/%3E%3Crect x='370' y='105' width='8' height='20'/%3E%3C!-- Right tower arched windows --%3E%3Cpath d='M 315 150 Q 315 140 340 140 Q 365 140 365 150 L 365 180 L 315 180 Z' fill='white' opacity='0.9'/%3E%3Cpath d='M 320 190 Q 320 185 332.5 185 Q 345 185 345 190 L 345 210 L 320 210 Z' fill='white' opacity='0.8'/%3E%3Cpath d='M 350 190 Q 350 185 362.5 185 Q 375 185 375 190 L 375 210 L 350 210 Z' fill='white' opacity='0.8'/%3E%3C!-- Connecting walls with curved tops --%3E%3Cpath d='M 95 175 Q 125 165 155 175 L 155 270 L 95 270 Z' fill='%23F4D03F'/%3E%3Cpath d='M 245 175 Q 275 165 300 175 L 300 270 L 245 270 Z' fill='%2358D68D'/%3E%3C!-- Wall battlements --%3E%3Crect x='105' y='160' width='6' height='15'/%3E%3Crect x='115' y='160' width='6' height='15'/%3E%3Crect x='125' y='160' width='6' height='15'/%3E%3Crect x='135' y='160' width='6' height='15'/%3E%3Crect x='145' y='160' width='6' height='15'/%3E%3Crect x='255' y='160' width='6' height='15'/%3E%3Crect x='265' y='160' width='6' height='15'/%3E%3Crect x='275' y='160' width='6' height='15'/%3E%3Crect x='285' y='160' width='6' height='15'/%3E%3Crect x='295' y='160' width='6' height='15'/%3E%3C!-- Decorative architectural details --%3E%3Cpath d='M 180 240 Q 180 230 200 230 Q 220 230 220 240 L 220 270 L 180 270 Z' fill='white' opacity='0.9'/%3E%3C!-- Crown for complete approval --%3E%3Ccircle cx='200' cy='50' r='15' fill='%23F39C12' opacity='1.0'/%3E%3Cpolygon points='200,35 205,45 215,45 207,52 210,62 200,57 190,62 193,52 185,45 195,45' fill='white' opacity='1.0'/%3E%3C/g%3E%3C/pattern%3E%3C/defs%3E%3Crect width='400' height='300' fill='url(%23fasilCastlePatternComplete)'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

/* Complete pattern styling - no additional borders needed */

/* Pattern overlay for better visibility */
.security-pattern-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 40%, rgba(255, 215, 0, 0.1) 50%, transparent 60%);
  pointer-events: none;
  z-index: 2;
}

/* Status indicators - Much more prominent */
.pattern-status-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
  z-index: 3;
  border: 2px solid;
  box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

.pattern-status-kebele {
  background: #FFD700;
  color: #333;
  border-color: #FFA000;
}

.pattern-status-subcity {
  background: #4CAF50;
  color: white;
  border-color: #2E7D32;
}

.pattern-status-complete {
  background: linear-gradient(45deg, #FFD700, #4CAF50);
  color: white;
  border-color: #2E7D32;
  animation: pulse 2s infinite;
}

/* Pulse animation for complete pattern */
@keyframes pulse {
  0% { box-shadow: 0 2px 8px rgba(0,0,0,0.3); }
  50% { box-shadow: 0 4px 16px rgba(76, 175, 80, 0.6); }
  100% { box-shadow: 0 2px 8px rgba(0,0,0,0.3); }
}



/* Animation for pattern application */
@keyframes patternApply {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.1);
  }
  100% {
    opacity: 0.15;
    transform: scale(1);
  }
}

.security-pattern-container.pattern-applying::before {
  animation: patternApply 1s ease-in-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .security-pattern-container::before {
    background-size: 150px 112px;
  }

  .pattern-status-indicator {
    font-size: 8px;
    padding: 2px 6px;
  }
}

/* Status text overlays using data attributes */
.security-pattern-kebele[data-status="kebele"]::before {
  content: '🏛️ KEBELE APPROVED';
  position: absolute;
  top: 50%;
  left: 25%;
  transform: translate(-50%, -50%) rotate(-15deg);
  font-size: 24px;
  font-weight: bold;
  color: rgba(255, 215, 0, 0.8);
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  z-index: 6; /* Above shiny pattern */
  pointer-events: none;
}

.security-pattern-subcity[data-status="subcity"]::before {
  content: '🏢 SUBCITY APPROVED';
  position: absolute;
  top: 50%;
  right: 25%;
  transform: translate(50%, -50%) rotate(15deg);
  font-size: 24px;
  font-weight: bold;
  color: rgba(76, 175, 80, 0.8);
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  z-index: 6; /* Above shiny pattern */
  pointer-events: none;
}

/* Removed "FULLY SECURED" text overlay for cleaner design */

/* Glow animation for complete pattern */
@keyframes glow {
  from { text-shadow: 2px 2px 4px rgba(0,0,0,0.5), 0 0 10px rgba(76, 175, 80, 0.5); }
  to { text-shadow: 2px 2px 4px rgba(0,0,0,0.5), 0 0 20px rgba(76, 175, 80, 0.8); }
}

/* Print styles */
@media print {
  /* Decorative pattern - visible in print */
  .security-pattern-container::before {
    opacity: 0.25; /* Visible blue patterns for print */
  }

  /* Security patterns */
  .security-pattern-kebele::after,
  .security-pattern-subcity::after,
  .security-pattern-complete::after {
    opacity: 0.4;
  }

  /* New SVG Background pattern - optimized for print */
  .security-pattern-container::after {
    opacity: 0.4; /* Increased opacity for better print visibility */
  }

  .pattern-status-indicator {
    display: none;
  }

  /* Text overlays removed for cleaner design */
}
