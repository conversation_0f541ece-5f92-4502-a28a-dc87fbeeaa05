#!/usr/bin/env python
"""
Create .mo file from .po file for Amharic translations
"""

import struct
import os
from pathlib import Path

def create_mo_file():
    """Create a .mo file from the .po file"""
    
    print("🌍 Creating Amharic .mo file...")
    
    # Define translations (key-value pairs from our .po file)
    translations = {
        "Dashboard": "ዳሽቦርድ",
        "Citizens": "ዜጎች",
        "ID Cards": "መታወቂያ ካርዶች",
        "Clearance": "ፍቃድ",
        "Transfer": "ዝውውር",
        "Print Queue": "የህትመት ወረፋ",
        "Service Requests": "የአገልግሎት ጥያቄዎች",
        "Kebele Users": "የቀበሌ ተጠቃሚዎች",
        "Reports": "ሪፖርቶች",
        "Citizen Directory": "የዜጎች ማውጫ",
        "Subcity Users": "የንዑስ ከተማ ተጠቃሚዎች",
        "Tenants": "ተከራዮች",
        "System Users": "የስርዓት ተጠቃሚዎች",
        "System Settings": "የስርዓት ቅንብሮች",
        "Login": "ግባ",
        "Logout": "ውጣ",
        "Username": "የተጠቃሚ ስም",
        "Password": "የይለፍ ቃል",
        "Email": "ኢሜይል",
        "First Name": "ስም",
        "Last Name": "የአባት ስም",
        "Add Citizen": "ዜጋ ጨምር",
        "Edit Citizen": "ዜጋን አርም",
        "Delete Citizen": "ዜጋን ሰርዝ",
        "Citizen Registration": "የዜጋ ምዝገባ",
        "Full Name": "ሙሉ ስም",
        "Date of Birth": "የተወለደበት ቀን",
        "Gender": "ጾታ",
        "Male": "ወንድ",
        "Female": "ሴት",
        "Phone Number": "ስልክ ቁጥር",
        "Address": "አድራሻ",
        "Nationality": "ዜግነት",
        "Ethiopian": "ኢትዮጵያዊ",
        "Generate ID Card": "መታወቂያ ካርድ ፍጠር",
        "Print ID Card": "መታወቂያ ካርድ አትም",
        "ID Card Status": "የመታወቂያ ካርድ ሁኔታ",
        "Pending": "በመጠባበቅ ላይ",
        "Approved": "ጸድቋል",
        "Rejected": "ተቀባይነት አላገኘም",
        "Printed": "ታትሟል",
        "Capture Fingerprint": "የጣት አሻራ ያንሱ",
        "Left Thumb": "የግራ አውራ ጣት",
        "Right Thumb": "የቀኝ አውራ ጣት",
        "Fingerprint Quality": "የጣት አሻራ ጥራት",
        "High Quality": "ከፍተኛ ጥራት",
        "Medium Quality": "መካከለኛ ጥራት",
        "Low Quality": "ዝቅተኛ ጥራት",
        "Biometric Data": "ባዮሜትሪክ መረጃ",
        "Duplicate Detection": "ተደጋጋሚ ፍለጋ",
        "No Duplicates Found": "ተደጋጋሚ አልተገኘም",
        "Duplicate Found": "ተደጋጋሚ ተገኝቷል",
        "Save": "አስቀምጥ",
        "Cancel": "ሰርዝ",
        "Submit": "አስገባ",
        "Edit": "አርም",
        "Delete": "ሰርዝ",
        "View": "ይመልከቱ",
        "Search": "ፈልግ",
        "Filter": "ማጣሪያ",
        "Export": "ወደ ውጭ ላክ",
        "Import": "ከውጭ አምጣ",
        "Print": "አትም",
        "Success": "ተሳክቷል",
        "Error": "ስህተት",
        "Warning": "ማስጠንቀቂያ",
        "Information": "መረጃ",
        "Loading...": "በመጫን ላይ...",
        "Please wait...": "እባክዎ ይጠብቁ...",
        "This field is required.": "ይህ መስክ አስፈላጊ ነው።",
        "Please enter a valid email address.": "እባክዎ ትክክለኛ ኢሜይል አድራሻ ያስገቡ።",
        "Please enter a valid phone number.": "እባክዎ ትክክለኛ ስልክ ቁጥር ያስገቡ።",
        "Password must be at least 8 characters long.": "የይለፍ ቃል ቢያንስ 8 ቁምፊዎች ሊኖሩት ይገባል።",
        "Today": "ዛሬ",
        "Yesterday": "ትናንት",
        "Tomorrow": "ነገ",
        "Date": "ቀን",
        "Time": "ሰዓት",
        "Total": "ጠቅላላ",
        "Count": "ቁጥር",
        "Page": "ገጽ",
        "of": "ከ",
        "items per page": "በገጽ ያሉ ንጥሎች",
        "System is ready": "ስርዓቱ ዝግጁ ነው",
        "Connection established": "ግንኙነት ተመስርቷል",
        "Connection failed": "ግንኙነት አልተሳካም",
        "Device connected": "መሳሪያ ተገናኝቷል",
        "Device not found": "መሳሪያ አልተገኘም",
        "Draft": "ረቂቅ",
        "Under Review": "በግምገማ ላይ",
        "Completed": "ተጠናቋል",
        "Cancelled": "ተሰርዟል",
        "City": "ከተማ",
        "Subcity": "ንዑስ ከተማ",
        "Kebele": "ቀበሌ",
        "Woreda": "ወረዳ",
        "Zone": "ዞን",
        "Region": "ክልል",
        "Upload": "ስቀል",
        "Download": "አውርድ",
        "File": "ፋይል",
        "Image": "ምስል",
        "Document": "ሰነድ",
        "Photo": "ፎቶ",
        "Administrator": "አስተዳዳሪ",
        "User": "ተጠቃሚ",
        "Clerk": "ፀሐፊ",
        "Leader": "መሪ",
        "Manager": "ሥራ አስኪያጅ",
        "Supervisor": "ተቆጣጣሪ",
        "Settings": "ቅንብሮች",
        "Configuration": "ውቅር",
        "Preferences": "ምርጫዎች",
        "Language": "ቋንቋ",
        "Theme": "ገጽታ",
        "Notifications": "ማሳወቂያዎች",

        # Additional UI translations
        "profile": "መገለጫ",
        "dashboard_welcome": "ወደ የመታወቂያ አስተዳደር ዳሽቦርድዎ እንኳን በደህና መጡ",
        "citizens_subtitle": "የዜጎች መረጃ ይመዝግቡ እና ያስተዳድሩ",
        "citizen_directory_subtitle": "ከቀበሌ ተከራዮች የዜጎች መረጃ ይመልከቱ",
        "id_cards_subtitle": "ዲጂታል መታወቂያ ካርዶችን ይፍጠሩ እና ያስተዳድሩ",
        "print_queue_subtitle": "የጸደቁ መታወቂያ ካርዶችን ያትሙ",
        "reports_subtitle": "የስርዓት ሪፖርቶችን ይመልከቱ እና ይፍጠሩ",
        "id_card_services": "የመታወቂያ ካርድ አገልግሎቶች",
        "id_card_services_subtitle": "ለመታወቂያ ካርድ እድሳት፣ ምትክ እና እንደገና ህትመት አገልግሎቶች ያመልክቱ"
    }
    
    # Create .mo file path
    mo_file_path = Path(__file__).parent / 'locale' / 'am' / 'LC_MESSAGES' / 'django.mo'
    mo_file_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Create .mo file content
    keys = list(translations.keys())
    values = list(translations.values())
    
    # Sort by keys for consistent ordering
    sorted_items = sorted(translations.items())
    keys = [item[0] for item in sorted_items]
    values = [item[1] for item in sorted_items]
    
    # Encode strings
    encoded_keys = [key.encode('utf-8') for key in keys]
    encoded_values = [value.encode('utf-8') for value in values]
    
    # Calculate offsets
    key_offsets = []
    value_offsets = []
    
    # Start after header (28 bytes) + key table + value table
    key_start = 28 + len(keys) * 8 + len(values) * 8
    value_start = key_start + sum(len(key) + 1 for key in encoded_keys)  # +1 for null terminator
    
    current_key_offset = key_start
    current_value_offset = value_start
    
    for key, value in zip(encoded_keys, encoded_values):
        key_offsets.append((len(key), current_key_offset))
        current_key_offset += len(key) + 1
        
        value_offsets.append((len(value), current_value_offset))
        current_value_offset += len(value) + 1
    
    # Write .mo file
    with open(mo_file_path, 'wb') as f:
        # Magic number (little endian)
        f.write(struct.pack('<I', 0x950412de))
        
        # Version
        f.write(struct.pack('<I', 0))
        
        # Number of strings
        f.write(struct.pack('<I', len(keys)))
        
        # Offset of key table
        f.write(struct.pack('<I', 28))
        
        # Offset of value table
        f.write(struct.pack('<I', 28 + len(keys) * 8))
        
        # Hash table size (0 = no hash table)
        f.write(struct.pack('<I', 0))
        
        # Offset of hash table (0 = no hash table)
        f.write(struct.pack('<I', 0))
        
        # Write key table
        for length, offset in key_offsets:
            f.write(struct.pack('<I', length))
            f.write(struct.pack('<I', offset))
        
        # Write value table
        for length, offset in value_offsets:
            f.write(struct.pack('<I', length))
            f.write(struct.pack('<I', offset))
        
        # Write keys
        for key in encoded_keys:
            f.write(key)
            f.write(b'\x00')  # Null terminator
        
        # Write values
        for value in encoded_values:
            f.write(value)
            f.write(b'\x00')  # Null terminator
    
    print(f"✅ Created .mo file: {mo_file_path}")
    print(f"📊 Translations: {len(translations)} entries")
    print(f"📁 File size: {mo_file_path.stat().st_size} bytes")
    
    return True

if __name__ == '__main__':
    print("🌍 GoID Amharic Translation Compiler")
    print("=" * 40)
    create_mo_file()
    print("🎉 Amharic translations ready!")
