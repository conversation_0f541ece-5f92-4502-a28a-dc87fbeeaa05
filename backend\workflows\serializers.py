from rest_framework import serializers
from .models import WorkflowLog, CitizenTransferRequest


class WorkflowLogSerializer(serializers.ModelSerializer):
    performed_by_username = serializers.CharField(source='performed_by.username', read_only=True)
    
    class Meta:
        model = WorkflowLog
        fields = (
            'id', 'id_card', 'action', 'from_status', 'to_status',
            'comment', 'performed_by', 'performed_by_username', 'performed_at'
        )
        read_only_fields = ('performed_at',)


class CitizenTransferRequestSerializer(serializers.ModelSerializer):
    """Serializer for citizen transfer requests with document upload support"""

    # Read-only fields for display
    source_kebele_name = serializers.CharField(source='source_kebele.name', read_only=True)
    destination_kebele_name = serializers.CharField(source='destination_kebele.name', read_only=True)
    requested_by_username = serializers.CharField(source='requested_by.username', read_only=True)
    reviewed_by_username = serializers.Char<PERSON><PERSON>(source='reviewed_by.username', read_only=True)

    # Additional fields for frontend compatibility
    source_kebele_info = serializers.SerializerMethodField()
    destination_kebele_info = serializers.SerializerMethodField()
    transfer_reason_display = serializers.SerializerMethodField()
    status_display = serializers.SerializerMethodField()

    # Document fields with proper file handling
    application_letter = serializers.FileField(required=False, allow_null=True)
    current_kebele_id = serializers.FileField(required=False, allow_null=True)

    # Document URLs for frontend display
    application_letter_url = serializers.SerializerMethodField()
    current_kebele_id_url = serializers.SerializerMethodField()

    class Meta:
        model = CitizenTransferRequest
        fields = (
            'id', 'transfer_id', 'citizen_id', 'citizen_name', 'citizen_digital_id',
            'source_kebele', 'source_kebele_name', 'source_kebele_info',
            'destination_kebele', 'destination_kebele_name', 'destination_kebele_info',
            'transfer_reason', 'transfer_reason_display', 'reason_description',
            'status', 'status_display',
            'requested_by', 'requested_by_username', 'reviewed_by', 'reviewed_by_username',
            'application_letter', 'current_kebele_id', 'application_letter_url', 'current_kebele_id_url',
            'documents_uploaded_at', 'review_notes',
            'created_at', 'reviewed_at', 'completed_at'
        )
        read_only_fields = (
            'id', 'transfer_id', 'created_at', 'reviewed_at', 'completed_at',
            'source_kebele_name', 'destination_kebele_name', 'requested_by_username', 'reviewed_by_username'
        )

    def get_application_letter_url(self, obj):
        """Get URL for application letter document"""
        if obj.application_letter:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.application_letter.url)
        return None

    def get_current_kebele_id_url(self, obj):
        """Get URL for current kebele ID document"""
        if obj.current_kebele_id:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.current_kebele_id.url)
        return None

    def update(self, instance, validated_data):
        """Custom update to handle document uploads"""
        # Update documents_uploaded_at when documents are uploaded
        if 'application_letter' in validated_data or 'current_kebele_id' in validated_data:
            from django.utils import timezone
            validated_data['documents_uploaded_at'] = timezone.now()

        return super().update(instance, validated_data)

    def get_source_kebele_info(self, obj):
        """Get source kebele information"""
        if obj.source_kebele:
            return {
                'id': obj.source_kebele.id,
                'name': obj.source_kebele.name
            }
        return None

    def get_destination_kebele_info(self, obj):
        """Get destination kebele information"""
        if obj.destination_kebele:
            return {
                'id': obj.destination_kebele.id,
                'name': obj.destination_kebele.name
            }
        return None

    def get_transfer_reason_display(self, obj):
        """Get human-readable transfer reason"""
        reason_map = {
            'relocation': 'Relocation/Moving',
            'marriage': 'Marriage',
            'work': 'Work/Employment',
            'education': 'Education',
            'family': 'Family Reasons',
            'other': 'Other'
        }
        return reason_map.get(obj.transfer_reason, obj.transfer_reason.title())

    def get_status_display(self, obj):
        """Get human-readable status"""
        status_map = {
            'pending': 'Pending Review',
            'accepted': 'Accepted',
            'rejected': 'Rejected',
            'completed': 'Completed',
            'cancelled': 'Cancelled'
        }
        return status_map.get(obj.status, obj.status.title())


class CitizenTransferRequestReviewSerializer(serializers.Serializer):
    """Serializer for reviewing transfer requests (accept/reject)"""

    action = serializers.ChoiceField(
        choices=['accept', 'reject'],
        required=True,
        help_text="Action to take on the transfer request"
    )

    review_notes = serializers.CharField(
        max_length=1000,
        required=False,
        allow_blank=True,
        help_text="Optional notes for the review decision"
    )
