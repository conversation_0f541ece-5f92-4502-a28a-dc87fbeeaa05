#!/usr/bin/env python3
"""
Test script for GoID biometric service functionality
"""

import requests
import json
import sys
import time
from datetime import datetime

def test_biometric_endpoints():
    """Test all biometric endpoints without authentication"""
    
    base_url = "http://localhost:8000/api/biometrics"
    
    print("🔍 Testing GoID Biometric Service")
    print("=" * 50)
    
    # Test endpoints that might work without auth
    endpoints_to_test = [
        ("GET", "/device-status/", "Device Status"),
        ("POST", "/initialize-device/", "Initialize Device"),
        ("POST", "/capture/", "Capture Fingerprint"),
    ]
    
    results = []
    
    for method, endpoint, description in endpoints_to_test:
        url = f"{base_url}{endpoint}"
        print(f"\n📱 Testing {description}")
        print(f"🌐 {method} {url}")
        
        try:
            if method == "GET":
                response = requests.get(url, timeout=5)
            elif method == "POST":
                test_data = {}
                if "capture" in endpoint:
                    test_data = {"thumb_type": "left"}
                response = requests.post(url, json=test_data, timeout=5)
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Success: {json.dumps(data, indent=2)}")
                results.append((description, "SUCCESS", data))
            elif response.status_code == 401:
                print(f"🔐 Authentication required (expected)")
                results.append((description, "AUTH_REQUIRED", None))
            else:
                print(f"❌ Error {response.status_code}: {response.text}")
                results.append((description, "ERROR", response.text))
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Connection error: {e}")
            results.append((description, "CONNECTION_ERROR", str(e)))
    
    # Summary
    print(f"\n📊 Test Results Summary")
    print("=" * 50)
    for desc, status, data in results:
        print(f"{desc}: {status}")
    
    return results

def test_external_biometric_service():
    """Test external biometric service on port 8001"""
    
    print(f"\n🌐 Testing External Biometric Service")
    print("=" * 50)
    
    base_url = "http://localhost:8001"
    
    endpoints = [
        ("GET", "/api/health", "Health Check"),
        ("GET", "/api/device/status", "Device Status"),
        ("POST", "/api/device/reconnect", "Device Reconnect"),
    ]
    
    for method, endpoint, description in endpoints:
        url = f"{base_url}{endpoint}"
        print(f"\n📱 Testing {description}")
        print(f"🌐 {method} {url}")
        
        try:
            if method == "GET":
                response = requests.get(url, timeout=3)
            else:
                response = requests.post(url, timeout=3)
            
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                print(f"✅ Response: {response.json()}")
            else:
                print(f"❌ Error: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Service not available: {e}")
            return False
    
    return True

def test_device_simulation():
    """Test if the backend biometric simulation is working"""
    
    print(f"\n🎭 Testing Device Simulation Mode")
    print("=" * 50)
    
    try:
        # Import and test the device directly
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
        
        from biometrics.device_integration import get_device
        
        print("📱 Getting device instance...")
        device = get_device()
        
        print("🔧 Initializing device...")
        initialized = device.initialize()
        print(f"Initialization: {'✅ Success' if initialized else '❌ Failed'}")
        
        if initialized:
            print("📊 Getting device status...")
            status = device.get_device_status()
            print(f"Device Status: {json.dumps(status, indent=2)}")
            
            print("🔍 Testing fingerprint capture...")
            template = device.capture_fingerprint("left")
            if template:
                print(f"✅ Capture successful!")
                print(f"   Quality: {template.quality_score}%")
                print(f"   Minutiae: {template.minutiae_count}")
                print(f"   Thumb: {template.thumb_type}")
            else:
                print("❌ Capture failed")
        
        print("🔒 Disconnecting device...")
        device.disconnect()
        
    except Exception as e:
        print(f"❌ Simulation test error: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main test function"""
    
    print("🚀 GoID Biometric Service Test Suite")
    print(f"⏰ Started at: {datetime.now()}")
    print("=" * 60)
    
    # Test 1: Backend biometric endpoints
    test_biometric_endpoints()
    
    # Test 2: External biometric service
    external_available = test_external_biometric_service()
    
    # Test 3: Device simulation
    test_device_simulation()
    
    print(f"\n✅ Test suite completed at: {datetime.now()}")
    print("\n🔧 Recommendations:")
    
    if not external_available:
        print("📱 Start external biometric service: start_external_biometric_service.bat")
        print("🔗 External service should run on http://localhost:8001")
    
    print("🔐 Use valid JWT token for authenticated endpoints")
    print("📋 Check backend logs for detailed error messages")
    print("🎭 Simulation mode is working for development/testing")

if __name__ == "__main__":
    main()
