#!/usr/bin/env python3
"""
Create user tables directly using SQL.
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
sys.path.append('/app')
django.setup()

from django.db import connection

def create_user_tables_sql():
    """Create user tables directly using SQL."""
    cursor = connection.cursor()
    
    print('=== Creating User Tables with SQL ===')
    
    # Get all tenant schemas
    cursor.execute("SELECT schema_name FROM tenants_tenant WHERE schema_name != 'public';")
    schemas = cursor.fetchall()
    
    print(f'Found {len(schemas)} tenant schemas')
    
    for schema in schemas:
        schema_name = schema[0]
        print(f'\n--- Creating tables in schema: {schema_name} ---')
        
        try:
            # Create auth_user table
            cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {schema_name}.auth_user (
                    id SERIAL PRIMARY KEY,
                    password VARCHAR(128) NOT NULL,
                    last_login TIMESTAMP WITH TIME ZONE,
                    is_superuser BOOLEAN NOT NULL DEFAULT FALSE,
                    username VARCHAR(150) NOT NULL,
                    first_name VARCHAR(150) NOT NULL DEFAULT '',
                    last_name VARCHAR(150) NOT NULL DEFAULT '',
                    email VARCHAR(254) NOT NULL,
                    is_staff BOOLEAN NOT NULL DEFAULT FALSE,
                    is_active BOOLEAN NOT NULL DEFAULT TRUE,
                    date_joined TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                    role VARCHAR(20) NOT NULL DEFAULT 'clerk',
                    tenant_id BIGINT,
                    phone_number VARCHAR(15),
                    profile_picture VARCHAR(100)
                );
            """)
            
            # Create unique constraints
            cursor.execute(f"""
                CREATE UNIQUE INDEX IF NOT EXISTS {schema_name}_auth_user_username_uniq 
                ON {schema_name}.auth_user (username);
            """)
            
            cursor.execute(f"""
                CREATE UNIQUE INDEX IF NOT EXISTS {schema_name}_auth_user_email_uniq 
                ON {schema_name}.auth_user (email);
            """)
            
            # Create auth_group table
            cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {schema_name}.auth_group (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(150) NOT NULL UNIQUE
                );
            """)
            
            # Create auth_permission table
            cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {schema_name}.auth_permission (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    content_type_id INTEGER NOT NULL,
                    codename VARCHAR(100) NOT NULL,
                    UNIQUE(content_type_id, codename)
                );
            """)
            
            # Create auth_user_groups table
            cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {schema_name}.auth_user_groups (
                    id SERIAL PRIMARY KEY,
                    user_id INTEGER NOT NULL,
                    group_id INTEGER NOT NULL,
                    UNIQUE(user_id, group_id)
                );
            """)
            
            # Create auth_user_user_permissions table
            cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {schema_name}.auth_user_user_permissions (
                    id SERIAL PRIMARY KEY,
                    user_id INTEGER NOT NULL,
                    permission_id INTEGER NOT NULL,
                    UNIQUE(user_id, permission_id)
                );
            """)
            
            print(f'✅ Created auth tables in {schema_name}')
            
        except Exception as e:
            print(f'❌ Error creating tables in {schema_name}: {e}')
    
    print('\n=== Verification ===')
    for schema in schemas:
        schema_name = schema[0]
        cursor.execute(f"SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = '{schema_name}' AND table_name = 'auth_user');")
        has_user_table = cursor.fetchone()[0]
        print(f'{schema_name}: auth_user table = {has_user_table}')
    
    return True

if __name__ == "__main__":
    create_user_tables_sql()
