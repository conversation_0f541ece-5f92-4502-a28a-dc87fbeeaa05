#!/usr/bin/env python3
"""
Diagnostic Futronic SDK Service
Detailed logging and step-by-step analysis of SDK operations
"""

import logging
import ctypes
from ctypes import cdll, c_int, c_uint, c_void_p, c_ubyte, POINTER, byref
import time
import base64
import json
import os
import sys
import threading
from datetime import datetime
from typing import Optional, Dict, Any
from flask import Flask, jsonify, request
from flask_cors import CORS

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('diagnostic_sdk_service.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app, origins="*")

# Futronic SDK Constants
FTR_OK = 0
FTR_ERROR_EMPTY_FRAME = 1
FTR_ERROR_FAKE_FINGER = 2
FTR_ERROR_NO_FRAME = 3

# Image specifications
FTR_IMAGE_WIDTH = 320
FTR_IMAGE_HEIGHT = 480
FTR_IMAGE_SIZE = FTR_IMAGE_WIDTH * FTR_IMAGE_HEIGHT

class DiagnosticSDKManager:
    """Diagnostic SDK manager with detailed logging"""
    
    def __init__(self):
        self.scan_api = None
        self.device_handle = None
        self.is_initialized = False
        self.last_error = ""
        
        logger.info("=== DIAGNOSTIC SDK MANAGER INITIALIZATION ===")
        self._load_sdk()
    
    def _load_sdk(self):
        """Load SDK with detailed diagnostics"""
        try:
            # Check DLL existence
            dll_path = os.path.abspath('./ftrScanAPI.dll')
            logger.info(f"Checking for SDK DLL at: {dll_path}")
            
            if not os.path.exists(dll_path):
                self.last_error = f"ftrScanAPI.dll not found at: {dll_path}"
                logger.error(f"ERROR: {self.last_error}")
                return False
            
            dll_size = os.path.getsize(dll_path)
            logger.info(f"DLL file size: {dll_size} bytes")
            
            # Load the DLL
            logger.info("Loading ftrScanAPI.dll...")
            self.scan_api = cdll.LoadLibrary(dll_path)
            logger.info("SUCCESS: DLL loaded successfully")
            
            # Configure function signatures
            self._configure_api()
            
            logger.info("SUCCESS: SDK loaded and configured")
            return True
            
        except Exception as e:
            self.last_error = f"SDK loading failed: {e}"
            logger.error(f"ERROR: {self.last_error}")
            return False
    
    def _configure_api(self):
        """Configure API with detailed logging"""
        try:
            logger.info("Configuring API function signatures...")
            
            # Device management
            logger.debug("Configuring ftrScanOpenDevice...")
            self.scan_api.ftrScanOpenDevice.restype = c_void_p
            self.scan_api.ftrScanOpenDevice.argtypes = []
            
            logger.debug("Configuring ftrScanCloseDevice...")
            self.scan_api.ftrScanCloseDevice.restype = c_int
            self.scan_api.ftrScanCloseDevice.argtypes = [c_void_p]
            
            # Image capture
            logger.debug("Configuring ftrScanGetFrame...")
            self.scan_api.ftrScanGetFrame.restype = c_int
            self.scan_api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(c_ubyte), POINTER(c_uint)]
            
            # Finger detection (optional)
            try:
                logger.debug("Configuring ftrScanIsFingerPresent...")
                self.scan_api.ftrScanIsFingerPresent.restype = c_int
                self.scan_api.ftrScanIsFingerPresent.argtypes = [c_void_p, POINTER(c_int)]
                logger.info("SUCCESS: Finger detection function available")
            except AttributeError:
                logger.warning("WARNING: Finger detection function not available")
            
            logger.info("SUCCESS: API configured successfully")
            
        except Exception as e:
            logger.error(f"ERROR: API configuration failed: {e}")
            raise
    
    def initialize_device(self) -> bool:
        """Initialize device with detailed diagnostics"""
        try:
            logger.info("=== DEVICE INITIALIZATION ===")
            
            if not self.scan_api:
                logger.error("ERROR: SDK not loaded")
                return False
            
            # Close any existing connection
            if self.device_handle:
                logger.info("Closing existing device connection...")
                try:
                    self.scan_api.ftrScanCloseDevice(self.device_handle)
                    logger.info("Previous connection closed")
                except Exception as e:
                    logger.warning(f"Error closing previous connection: {e}")
                self.device_handle = None
            
            # Open new connection
            logger.info("Opening device connection...")
            self.device_handle = self.scan_api.ftrScanOpenDevice()
            
            logger.info(f"Device handle returned: {self.device_handle}")
            
            if self.device_handle:
                self.is_initialized = True
                logger.info(f"SUCCESS: Device initialized - Handle: {self.device_handle}")
                return True
            else:
                self.last_error = "Failed to open device connection - device may not be connected"
                logger.error(f"ERROR: {self.last_error}")
                return False
                
        except Exception as e:
            self.last_error = f"Device initialization error: {e}"
            logger.error(f"ERROR: {self.last_error}")
            return False
    
    def test_finger_detection(self) -> Dict[str, Any]:
        """Test finger detection with detailed diagnostics"""
        logger.info("=== FINGER DETECTION TEST ===")
        
        if not self.is_initialized:
            return {'success': False, 'error': 'Device not initialized'}
        
        try:
            # Check if finger detection function is available
            if not hasattr(self.scan_api, 'ftrScanIsFingerPresent'):
                return {'success': False, 'error': 'Finger detection function not available'}
            
            finger_status = c_int(0)
            
            logger.info("Calling ftrScanIsFingerPresent...")
            start_time = time.time()
            
            # Use thread with timeout
            result_container = {'result': None, 'exception': None}
            
            def finger_detection_thread():
                try:
                    result_container['result'] = self.scan_api.ftrScanIsFingerPresent(
                        self.device_handle, 
                        byref(finger_status)
                    )
                except Exception as e:
                    result_container['exception'] = e
            
            thread = threading.Thread(target=finger_detection_thread)
            thread.daemon = True
            thread.start()
            thread.join(timeout=2.0)
            
            elapsed_time = time.time() - start_time
            logger.info(f"Finger detection call completed in {elapsed_time:.3f} seconds")
            
            if thread.is_alive():
                logger.warning("Finger detection timed out")
                return {'success': False, 'error': 'Finger detection timed out'}
            
            if result_container['exception']:
                logger.error(f"Finger detection exception: {result_container['exception']}")
                return {'success': False, 'error': str(result_container['exception'])}
            
            result = result_container['result']
            finger_present = finger_status.value > 0
            
            logger.info(f"Finger detection result: {result}, Finger present: {finger_present}")
            
            return {
                'success': True,
                'result_code': result,
                'finger_present': finger_present,
                'elapsed_time': elapsed_time
            }
            
        except Exception as e:
            logger.error(f"Finger detection test failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def test_frame_capture(self) -> Dict[str, Any]:
        """Test frame capture with detailed diagnostics"""
        logger.info("=== FRAME CAPTURE TEST ===")
        
        if not self.is_initialized:
            return {'success': False, 'error': 'Device not initialized'}
        
        try:
            # Create buffer
            buffer_size = FTR_IMAGE_SIZE * 2
            logger.info(f"Creating image buffer of size: {buffer_size} bytes")
            
            image_buffer = (c_ubyte * buffer_size)()
            frame_size = c_uint(buffer_size)
            
            logger.info(f"Initial frame size: {frame_size.value}")
            
            # Test frame capture with timeout
            result_container = {'result': None, 'exception': None, 'frame_size': 0}
            
            def frame_capture_thread():
                try:
                    logger.debug("Thread: Calling ftrScanGetFrame...")
                    result_container['result'] = self.scan_api.ftrScanGetFrame(
                        self.device_handle, 
                        image_buffer, 
                        byref(frame_size)
                    )
                    result_container['frame_size'] = frame_size.value
                    logger.debug(f"Thread: Frame capture completed with result {result_container['result']}")
                except Exception as e:
                    logger.error(f"Thread: Frame capture exception: {e}")
                    result_container['exception'] = e
            
            logger.info("Starting frame capture thread...")
            start_time = time.time()
            
            capture_thread = threading.Thread(target=frame_capture_thread)
            capture_thread.daemon = True
            capture_thread.start()
            capture_thread.join(timeout=5.0)
            
            elapsed_time = time.time() - start_time
            logger.info(f"Frame capture completed in {elapsed_time:.3f} seconds")
            
            if capture_thread.is_alive():
                logger.warning("Frame capture timed out")
                return {'success': False, 'error': 'Frame capture timed out', 'elapsed_time': elapsed_time}
            
            if result_container['exception']:
                logger.error(f"Frame capture exception: {result_container['exception']}")
                return {'success': False, 'error': str(result_container['exception']), 'elapsed_time': elapsed_time}
            
            result = result_container['result']
            actual_frame_size = result_container['frame_size']
            
            logger.info(f"Frame capture result: {result}")
            logger.info(f"Frame size returned: {actual_frame_size}")
            
            # Analyze result
            if result == FTR_OK:
                if actual_frame_size > 0:
                    logger.info(f"SUCCESS: Frame captured - {actual_frame_size} bytes")
                    
                    # Calculate basic statistics
                    image_data = bytes(image_buffer[:actual_frame_size])
                    non_zero_bytes = sum(1 for b in image_data if b > 0)
                    avg_value = sum(image_data) / len(image_data) if image_data else 0
                    
                    return {
                        'success': True,
                        'result_code': result,
                        'frame_size': actual_frame_size,
                        'non_zero_bytes': non_zero_bytes,
                        'average_value': avg_value,
                        'elapsed_time': elapsed_time
                    }
                else:
                    logger.warning("Frame captured but size is 0")
                    return {'success': False, 'error': 'Empty frame captured', 'result_code': result}
            
            elif result == FTR_ERROR_EMPTY_FRAME:
                logger.info("No finger detected on scanner")
                return {'success': False, 'error': 'No finger detected', 'result_code': result}
            
            elif result == FTR_ERROR_NO_FRAME:
                logger.info("No frame available")
                return {'success': False, 'error': 'No frame available', 'result_code': result}
            
            else:
                logger.warning(f"Frame capture failed with code: {result}")
                return {'success': False, 'error': f'Capture failed with code {result}', 'result_code': result}
            
        except Exception as e:
            logger.error(f"Frame capture test failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_device_status(self) -> Dict[str, Any]:
        """Get detailed device status"""
        return {
            'connected': self.is_initialized,
            'initialized': self.is_initialized,
            'device_available': self.is_initialized,
            'handle': self.device_handle,
            'model': 'Futronic FS88H',
            'serial_number': f'Handle_{self.device_handle}' if self.device_handle else '',
            'sdk_version': 'Diagnostic',
            'interface': 'USB',
            'status': 'Connected' if self.is_initialized else 'Disconnected',
            'last_error': self.last_error,
            'capabilities': {
                'image_capture': True,
                'finger_detection': hasattr(self.scan_api, 'ftrScanIsFingerPresent') if self.scan_api else False,
                'detailed_diagnostics': True
            }
        }

# Global SDK manager
sdk_manager = DiagnosticSDKManager()

# Flask API Endpoints

@app.route('/api/device/status', methods=['GET'])
def get_device_status():
    """Get device status"""
    try:
        status = sdk_manager.get_device_status()
        logger.info(f"Device status requested - Connected: {status['connected']}")
        
        return jsonify({
            'success': True,
            'data': status
        })
        
    except Exception as e:
        logger.error(f"ERROR: Status check error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/test/finger-detection', methods=['POST'])
def test_finger_detection():
    """Test finger detection"""
    try:
        logger.info("Finger detection test requested")
        result = sdk_manager.test_finger_detection()
        
        return jsonify({
            'success': result['success'],
            'data': result
        })
        
    except Exception as e:
        logger.error(f"ERROR: Finger detection test error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/test/frame-capture', methods=['POST'])
def test_frame_capture():
    """Test frame capture"""
    try:
        logger.info("Frame capture test requested")
        result = sdk_manager.test_frame_capture()
        
        return jsonify({
            'success': result['success'],
            'data': result
        })
        
    except Exception as e:
        logger.error(f"ERROR: Frame capture test error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/device/initialize', methods=['POST'])
def initialize_device():
    """Initialize device"""
    try:
        logger.info("Device initialization requested")
        
        success = sdk_manager.initialize_device()
        
        if success:
            return jsonify({
                'success': True,
                'message': 'Device initialized successfully',
                'data': sdk_manager.get_device_status()
            })
        else:
            return jsonify({
                'success': False,
                'error': sdk_manager.last_error
            }), 500
            
    except Exception as e:
        logger.error(f"ERROR: Device initialization error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Service health check"""
    return jsonify({
        'status': 'healthy',
        'service': 'Diagnostic Futronic SDK Service',
        'version': '1.0.0',
        'timestamp': datetime.now().isoformat(),
        'sdk_status': {
            'sdk_loaded': sdk_manager.scan_api is not None,
            'device_initialized': sdk_manager.is_initialized,
            'diagnostic_mode': True
        }
    })

@app.route('/', methods=['GET'])
def index():
    """Diagnostic service interface"""
    device_status = sdk_manager.get_device_status()

    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Diagnostic Futronic SDK Service</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }}
            .container {{ max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }}
            .status-card {{ background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 5px; border-left: 4px solid #007bff; }}
            .success {{ border-left-color: #28a745; }}
            .error {{ border-left-color: #dc3545; }}
            .warning {{ border-left-color: #ffc107; }}
            .btn {{ padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }}
            .btn-primary {{ background: #007bff; color: white; }}
            .btn-success {{ background: #28a745; color: white; }}
            .btn-warning {{ background: #ffc107; color: black; }}
            .btn-info {{ background: #17a2b8; color: white; }}
            .test-results {{ background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; font-family: monospace; }}
            .log-output {{ background: #000; color: #0f0; padding: 15px; margin: 10px 0; border-radius: 5px; font-family: monospace; max-height: 300px; overflow-y: auto; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🔍 Diagnostic Futronic SDK Service</h1>
            <p>Detailed analysis and testing of SDK operations</p>

            <div class="status-card {'success' if device_status['connected'] else 'error'}">
                <h3>Device Status</h3>
                <p><strong>Connection:</strong> {'✅ Connected' if device_status['connected'] else '❌ Disconnected'}</p>
                <p><strong>Model:</strong> {device_status['model']}</p>
                <p><strong>Handle:</strong> {device_status['handle'] or 'N/A'}</p>
                <p><strong>SDK Version:</strong> {device_status['sdk_version']}</p>
                <p><strong>Last Error:</strong> {device_status['last_error'] or 'None'}</p>
            </div>

            <div class="status-card">
                <h3>Capabilities</h3>
                <p><strong>Image Capture:</strong> {'✅ Available' if device_status['capabilities']['image_capture'] else '❌ Not Available'}</p>
                <p><strong>Finger Detection:</strong> {'✅ Available' if device_status['capabilities']['finger_detection'] else '❌ Not Available'}</p>
                <p><strong>Diagnostic Mode:</strong> ✅ Active</p>
            </div>

            <div class="status-card">
                <h3>🧪 Diagnostic Tests</h3>
                <p>Run individual tests to diagnose SDK functionality:</p>

                <button class="btn btn-info" onclick="initializeDevice()">🔌 Initialize Device</button>
                <button class="btn btn-warning" onclick="testFingerDetection()">👆 Test Finger Detection</button>
                <button class="btn btn-primary" onclick="testFrameCapture()">📸 Test Frame Capture</button>
                <button class="btn btn-success" onclick="runFullDiagnostic()">🔍 Full Diagnostic</button>

                <div id="testResults" class="test-results" style="display: none;">
                    <h4>Test Results:</h4>
                    <pre id="resultsContent"></pre>
                </div>
            </div>

            <div class="status-card">
                <h3>📋 Instructions</h3>
                <ol>
                    <li><strong>Initialize Device:</strong> Test basic device connection</li>
                    <li><strong>Test Finger Detection:</strong> Check if finger presence detection works</li>
                    <li><strong>Test Frame Capture:</strong> Attempt to capture a fingerprint frame</li>
                    <li><strong>Full Diagnostic:</strong> Run all tests in sequence</li>
                </ol>
                <p><strong>Note:</strong> Place your finger on the scanner when testing frame capture.</p>
            </div>
        </div>

        <script>
            function showResults(title, data) {{
                const resultsDiv = document.getElementById('testResults');
                const resultsContent = document.getElementById('resultsContent');

                resultsContent.textContent = title + '\\n' + JSON.stringify(data, null, 2);
                resultsDiv.style.display = 'block';
                resultsDiv.scrollIntoView({{ behavior: 'smooth' }});
            }}

            async function initializeDevice() {{
                const button = event.target;
                const originalText = button.textContent;

                button.textContent = '🔄 Initializing...';
                button.disabled = true;

                try {{
                    const response = await fetch('/api/device/initialize', {{
                        method: 'POST',
                        headers: {{ 'Content-Type': 'application/json' }}
                    }});

                    const result = await response.json();
                    showResults('🔌 Device Initialization Test', result);

                    if (result.success) {{
                        alert('✅ Device initialized successfully!');
                        location.reload();
                    }} else {{
                        alert('❌ Device initialization failed: ' + result.error);
                    }}
                }} catch (error) {{
                    showResults('🔌 Device Initialization Test - ERROR', {{ error: error.message }});
                    alert('❌ Network error: ' + error.message);
                }} finally {{
                    button.textContent = originalText;
                    button.disabled = false;
                }}
            }}

            async function testFingerDetection() {{
                const button = event.target;
                const originalText = button.textContent;

                button.textContent = '🔄 Testing...';
                button.disabled = true;

                try {{
                    const response = await fetch('/api/test/finger-detection', {{
                        method: 'POST',
                        headers: {{ 'Content-Type': 'application/json' }}
                    }});

                    const result = await response.json();
                    showResults('👆 Finger Detection Test', result);

                    if (result.success) {{
                        const data = result.data;
                        alert('✅ Finger detection test completed!\\n\\nResult Code: ' + data.result_code + '\\nFinger Present: ' + data.finger_present + '\\nTime: ' + data.elapsed_time.toFixed(3) + 's');
                    }} else {{
                        alert('❌ Finger detection test failed: ' + result.data.error);
                    }}
                }} catch (error) {{
                    showResults('👆 Finger Detection Test - ERROR', {{ error: error.message }});
                    alert('❌ Network error: ' + error.message);
                }} finally {{
                    button.textContent = originalText;
                    button.disabled = false;
                }}
            }}

            async function testFrameCapture() {{
                const button = event.target;
                const originalText = button.textContent;

                button.textContent = '🔄 Capturing...';
                button.disabled = true;

                alert('📸 Place your finger on the scanner and click OK to start capture test');

                try {{
                    const response = await fetch('/api/test/frame-capture', {{
                        method: 'POST',
                        headers: {{ 'Content-Type': 'application/json' }}
                    }});

                    const result = await response.json();
                    showResults('📸 Frame Capture Test', result);

                    if (result.success) {{
                        const data = result.data;
                        alert('✅ Frame capture test successful!\\n\\nFrame Size: ' + data.frame_size + ' bytes\\nNon-zero bytes: ' + data.non_zero_bytes + '\\nAverage value: ' + data.average_value.toFixed(2) + '\\nTime: ' + data.elapsed_time.toFixed(3) + 's');
                    }} else {{
                        alert('❌ Frame capture test failed: ' + result.data.error);
                    }}
                }} catch (error) {{
                    showResults('📸 Frame Capture Test - ERROR', {{ error: error.message }});
                    alert('❌ Network error: ' + error.message);
                }} finally {{
                    button.textContent = originalText;
                    button.disabled = false;
                }}
            }}

            async function runFullDiagnostic() {{
                const button = event.target;
                const originalText = button.textContent;

                button.textContent = '🔄 Running Full Diagnostic...';
                button.disabled = true;

                try {{
                    showResults('🔍 Full Diagnostic Started', {{ message: 'Running comprehensive diagnostic tests...' }});

                    // Test 1: Device initialization
                    console.log('Running device initialization test...');
                    const initResponse = await fetch('/api/device/initialize', {{ method: 'POST' }});
                    const initResult = await initResponse.json();

                    // Test 2: Finger detection
                    console.log('Running finger detection test...');
                    const fingerResponse = await fetch('/api/test/finger-detection', {{ method: 'POST' }});
                    const fingerResult = await fingerResponse.json();

                    // Test 3: Frame capture
                    console.log('Running frame capture test...');
                    alert('📸 Place your finger on the scanner for frame capture test');
                    const frameResponse = await fetch('/api/test/frame-capture', {{ method: 'POST' }});
                    const frameResult = await frameResponse.json();

                    const fullResults = {{
                        device_initialization: initResult,
                        finger_detection: fingerResult,
                        frame_capture: frameResult,
                        summary: {{
                            device_init_success: initResult.success,
                            finger_detection_success: fingerResult.success,
                            frame_capture_success: frameResult.success,
                            overall_success: initResult.success && frameResult.success
                        }}
                    }};

                    showResults('🔍 Full Diagnostic Results', fullResults);

                    const summary = fullResults.summary;
                    let message = '🔍 Full Diagnostic Complete!\\n\\n';
                    message += '🔌 Device Init: ' + (summary.device_init_success ? '✅ PASS' : '❌ FAIL') + '\\n';
                    message += '👆 Finger Detection: ' + (summary.finger_detection_success ? '✅ PASS' : '❌ FAIL') + '\\n';
                    message += '📸 Frame Capture: ' + (summary.frame_capture_success ? '✅ PASS' : '❌ FAIL') + '\\n\\n';
                    message += 'Overall: ' + (summary.overall_success ? '✅ SYSTEM WORKING' : '❌ ISSUES DETECTED');

                    alert(message);

                }} catch (error) {{
                    showResults('🔍 Full Diagnostic - ERROR', {{ error: error.message }});
                    alert('❌ Diagnostic error: ' + error.message);
                }} finally {{
                    button.textContent = originalText;
                    button.disabled = false;
                }}
            }}
        </script>
    </body>
    </html>
    """

    return html_content

if __name__ == '__main__':
    try:
        logger.info("=== STARTING DIAGNOSTIC FUTRONIC SDK SERVICE ===")
        logger.info("Detailed logging and step-by-step analysis enabled")

        # Initialize device on startup
        if sdk_manager.initialize_device():
            logger.info("SUCCESS: Device ready for diagnostic testing")
        else:
            logger.warning("WARNING: Device not available - diagnostic tests may fail")

        logger.info("Diagnostic service available at http://localhost:8001")
        logger.info("=== DIAGNOSTIC SERVICE READY ===")

        # Start Flask service
        app.run(host='0.0.0.0', port=8001, debug=False, threaded=True, use_reloader=False)

    except KeyboardInterrupt:
        logger.info("Diagnostic service shutdown requested")
    except Exception as e:
        logger.error(f"ERROR: Diagnostic service error: {e}")
    finally:
        logger.info("Diagnostic Futronic SDK Service stopped")
