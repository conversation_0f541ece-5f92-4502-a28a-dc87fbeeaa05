from django.core.management.base import BaseCommand
from django.db import transaction
from django_tenants.utils import schema_context
from tenants.models import Tenant
from citizens.models import Citizen
from idcards.models import IDCard, IDCardStatus
from users.models import User
from datetime import datetime, timedelta
import random


class Command(BaseCommand):
    help = 'Populate test data for dashboard testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant-id',
            type=int,
            help='Tenant ID to populate data for',
        )

    def handle(self, *args, **options):
        tenant_id = options.get('tenant_id')
        
        if tenant_id:
            try:
                tenant = Tenant.objects.get(id=tenant_id)
                self.populate_tenant_data(tenant)
            except Tenant.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'Tenant with ID {tenant_id} does not exist')
                )
        else:
            # Populate data for all tenants
            tenants = Tenant.objects.filter(tenant_type__in=['kebele', 'subcity'])
            for tenant in tenants:
                self.populate_tenant_data(tenant)

    def populate_tenant_data(self, tenant):
        self.stdout.write(f'Populating data for tenant: {tenant.name} (ID: {tenant.id})')
        
        with schema_context(tenant.schema_name):
            with transaction.atomic():
                # Create sample citizens
                citizens_data = [
                    {
                        'first_name': 'Abebe',
                        'middle_name': 'Kebede',
                        'last_name': 'Tesfaye',
                        'gender': 'M',
                        'date_of_birth': datetime(1990, 5, 15).date(),
                        'phone_number': '+251911123456',
                        'email': '<EMAIL>',
                    },
                    {
                        'first_name': 'Sara',
                        'middle_name': 'Mohammed',
                        'last_name': 'Ali',
                        'gender': 'F',
                        'date_of_birth': datetime(1985, 8, 22).date(),
                        'phone_number': '+251922234567',
                        'email': '<EMAIL>',
                    },
                    {
                        'first_name': 'Daniel',
                        'middle_name': 'Tesfaye',
                        'last_name': 'Girma',
                        'gender': 'M',
                        'date_of_birth': datetime(1992, 12, 3).date(),
                        'phone_number': '+251933345678',
                        'email': '<EMAIL>',
                    },
                    {
                        'first_name': 'Hiwot',
                        'middle_name': 'Girma',
                        'last_name': 'Bekele',
                        'gender': 'F',
                        'date_of_birth': datetime(1988, 3, 18).date(),
                        'phone_number': '+251944456789',
                        'email': '<EMAIL>',
                    },
                    {
                        'first_name': 'Yohannes',
                        'middle_name': 'Haile',
                        'last_name': 'Mariam',
                        'gender': 'M',
                        'date_of_birth': datetime(1995, 7, 9).date(),
                        'phone_number': '+251955567890',
                        'email': '<EMAIL>',
                    },
                ]

                created_citizens = []
                for citizen_data in citizens_data:
                    citizen, created = Citizen.objects.get_or_create(
                        first_name=citizen_data['first_name'],
                        middle_name=citizen_data['middle_name'],
                        last_name=citizen_data['last_name'],
                        defaults=citizen_data
                    )
                    if created:
                        # Set creation date to simulate different registration times
                        days_ago = random.randint(1, 30)
                        citizen.created_at = datetime.now() - timedelta(days=days_ago)
                        citizen.save()
                        self.stdout.write(f'  Created citizen: {citizen.first_name} {citizen.last_name}')
                    created_citizens.append(citizen)

                # Create sample ID cards
                statuses = [
                    IDCardStatus.DRAFT,
                    IDCardStatus.PENDING_APPROVAL,
                    IDCardStatus.APPROVED,
                    IDCardStatus.REJECTED,
                    IDCardStatus.PRINTING,
                ]

                for i, citizen in enumerate(created_citizens):
                    # Create ID card for each citizen
                    status = random.choice(statuses)
                    
                    id_card, created = IDCard.objects.get_or_create(
                        citizen=citizen,
                        defaults={
                            'card_number': f'AA0101{tenant.id:02d}{i+1:010d}',
                            'status': status,
                            'issue_date': datetime.now().date(),
                            'expiry_date': (datetime.now() + timedelta(days=365*5)).date(),
                        }
                    )
                    
                    if created:
                        # Set creation date to simulate different registration times
                        days_ago = random.randint(1, 25)
                        id_card.created_at = datetime.now() - timedelta(days=days_ago)
                        id_card.save()
                        self.stdout.write(f'  Created ID card for {citizen.first_name} {citizen.last_name} - Status: {status}')

                self.stdout.write(
                    self.style.SUCCESS(f'Successfully populated data for tenant: {tenant.name}')
                )
