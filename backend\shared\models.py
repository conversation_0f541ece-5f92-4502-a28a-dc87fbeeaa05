from django.db import models
from django.utils import timezone
from tenants.utils import Timestamp

class Country(Timestamp):
    """
    Country model for storing country information.
    This is a shared model accessible by all tenants.
    """
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=3, unique=True)  # 3-character unique code for the country
    capital_city = models.CharField(max_length=100, blank=True, null=True)
    flag_image = models.ImageField(upload_to='country_flags/', blank=True, null=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.name} ({self.code})"

    class Meta:
        verbose_name = "Country"
        verbose_name_plural = "Countries"
        ordering = ['name', 'code']


class Region(Timestamp):
    """
    Region model for storing region/state information.
    This is a shared model accessible by all tenants.
    """
    country = models.ForeignKey(Country, on_delete=models.CASCADE, related_name='regions')
    name = models.Char<PERSON>ield(max_length=100)
    code = models.Cha<PERSON><PERSON><PERSON>(max_length=5, unique=True)  # 5-character unique code for the region
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.name} ({self.code})"

    class Meta:
        verbose_name = "Region"
        verbose_name_plural = "Regions"
        ordering = ['name', 'code']


class Religion(Timestamp):
    """
    Religion model for storing religion information.
    This is a shared model accessible by all tenants.
    """
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Religion"
        verbose_name_plural = "Religions"
        ordering = ['name']


class CitizenStatus(Timestamp):
    """
    CitizenStatus model for storing citizen status information.
    This is a shared model accessible by all tenants.
    """
    name = models.CharField(max_length=100)  # e.g., Citizen, Resident, Visitor, Foreign National
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Citizen Status"
        verbose_name_plural = "Citizen Statuses"
        ordering = ['name']


class MaritalStatus(Timestamp):
    """
    MaritalStatus model for storing marital status information.
    This is a shared model accessible by all tenants.
    """
    name = models.CharField(max_length=50)  # e.g., Single, Married, etc.
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Marital Status"
        verbose_name_plural = "Marital Statuses"
        ordering = ['name']


class DocumentType(Timestamp):
    """
    DocumentType model for storing document type information.
    This is a shared model accessible by all tenants.
    """
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Document Type"
        verbose_name_plural = "Document Types"
        ordering = ['name']


class EmploymentType(Timestamp):
    """
    EmploymentType model for storing employment type information.
    This is a shared model accessible by all tenants.
    """
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Employment Type"
        verbose_name_plural = "Employment Types"
        ordering = ['name']


class Relationship(Timestamp):
    """
    Relationship model to store various types of relationships.
    This is a shared model accessible by all tenants.
    """
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Relationship"
        verbose_name_plural = "Relationships"
        ordering = ['name']


class CurrentStatus(Timestamp):
    """
    CurrentStatus model for storing current status information.
    This is a shared model accessible by all tenants.
    """
    name = models.CharField(max_length=50, unique=True, help_text="Current status of the citizen")
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Current Status"
        verbose_name_plural = "Current Statuses"
        ordering = ['name']


class BiometricType(Timestamp):
    """
    BiometricType model for storing biometric type information.
    This is a shared model accessible by all tenants.
    """
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Biometric Type"
        verbose_name_plural = "Biometric Types"
        ordering = ['name']


class Ketena(Timestamp):
    """
    Ketena model for storing ketena information.
    This is a shared model accessible by all tenants.
    """
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=10, unique=True, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)

    def save(self, *args, **kwargs):
        if not self.code:
            # Save first to get an ID if this is a new instance
            if not self.id:
                super().save(*args, **kwargs)
                self.code = f"GD-KT{self.id:02d}"  # Generates code like GD-KT01, GD-KT02, etc.
                return super().save(*args, **kwargs)
            else:
                self.code = f"GD-KT{self.id:02d}"
        return super().save(*args, **kwargs)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Ketena"
        verbose_name_plural = "Ketenes"
        ordering = ['name']
