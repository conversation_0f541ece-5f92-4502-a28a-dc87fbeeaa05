#!/usr/bin/env python3
"""
Test script to verify city_admin navigation works correctly
"""
import os
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from users.models_groups import TenantGroup, GroupMembership
from tenants.models import Tenant, TenantType
from django_tenants.utils import schema_context, get_public_schema_name

User = get_user_model()

def test_city_admin_navigation():
    """Test that city_admin user gets proper navigation"""
    
    print("🧪 Testing City Admin Navigation")
    print("=" * 50)
    
    # Step 1: Ensure groups exist
    print("1. Ensuring groups exist...")
    with schema_context(get_public_schema_name()):
        try:
            city_admin_group = TenantGroup.objects.get(group__name='city_admin')
            print(f"   ✅ city_admin group exists: {city_admin_group.name}")
        except TenantGroup.DoesNotExist:
            print("   ❌ city_admin group missing!")
            return False
    
    # Step 2: Find or create a city tenant
    print("\n2. Finding city tenant...")
    city_tenant = Tenant.objects.filter(type=TenantType.CITY).first()
    if not city_tenant:
        print("   ⚠️ No city tenant found, creating one...")
        city_tenant = Tenant.objects.create(
            name="Test City",
            type=TenantType.CITY,
            schema_name="test_city"
        )
        print(f"   ✅ Created test city tenant: {city_tenant.name}")
    else:
        print(f"   ✅ Using existing city tenant: {city_tenant.name}")
    
    # Step 3: Find or create city admin user
    print("\n3. Setting up city admin user...")
    test_email = "<EMAIL>"
    
    with schema_context(city_tenant.schema_name):
        try:
            city_admin_user = User.objects.get(email=test_email)
            print(f"   ♻️ Using existing city admin: {city_admin_user.email}")
        except User.DoesNotExist:
            city_admin_user = User.objects.create_user(
                email=test_email,
                username="test_city_admin",
                password="testpass123",
                role="city_admin",
                tenant=city_tenant,
                first_name="Test",
                last_name="CityAdmin"
            )
            print(f"   ✅ Created city admin user: {city_admin_user.email}")
    
    # Step 4: Assign user to city_admin group
    print("\n4. Assigning user to city_admin group...")
    with schema_context(get_public_schema_name()):
        membership = GroupMembership.objects.filter(
            user_email=city_admin_user.email,
            group=city_admin_group,
            is_active=True
        ).first()
        
        if not membership:
            membership = GroupMembership.objects.create(
                user_email=city_admin_user.email,
                user_tenant_id=city_tenant.id,
                user_tenant_schema=city_tenant.schema_name,
                group=city_admin_group,
                is_primary=True,
                is_active=True,
                reason='Test city admin setup'
            )
            print(f"   ✅ Assigned user to city_admin group")
        else:
            print(f"   ♻️ User already in city_admin group")
    
    # Step 5: Test login and check JWT token
    print("\n5. Testing login and JWT token...")
    login_response = requests.post('http://localhost:8000/api/auth/token/', json={
        'email': test_email,
        'password': 'testpass123'
    })
    
    if login_response.status_code != 200:
        print(f"   ❌ Login failed: {login_response.status_code}")
        print(f"   Response: {login_response.text}")
        return False
    
    token_data = login_response.json()
    access_token = token_data['access']
    
    # Decode JWT token to check permissions
    import jwt
    decoded = jwt.decode(access_token, options={"verify_signature": False})
    
    print(f"   ✅ Login successful!")
    print(f"   User: {decoded.get('email')}")
    print(f"   Role: {decoded.get('role')}")
    print(f"   Is superuser: {decoded.get('is_superuser')}")
    
    permissions = decoded.get('permissions', [])
    print(f"   Permissions count: {len(permissions)}")
    
    # Check for key city_admin permissions
    expected_permissions = [
        'create_subcity_users',
        'view_citizens_list',
        'view_citizen_details',
        'view_id_cards_list',
        'approve_id_cards',
        'view_child_subcities_data',
        'view_city_dashboard',
        'view_city_reports'
    ]
    
    print(f"\n   Expected city_admin permissions:")
    missing_permissions = []
    for perm in expected_permissions:
        has_perm = perm in permissions
        status = "✅" if has_perm else "❌"
        print(f"     {status} {perm}")
        if not has_perm:
            missing_permissions.append(perm)
    
    if missing_permissions:
        print(f"\n   ⚠️ Missing permissions: {missing_permissions}")
        print(f"   All permissions: {permissions}")
    
    # Step 6: Clean up test user
    print("\n6. Cleaning up...")
    try:
        with schema_context(city_tenant.schema_name):
            city_admin_user.delete()
        
        with schema_context(get_public_schema_name()):
            if membership:
                membership.delete()
        
        print("   🧹 Test user and membership cleaned up")
    except Exception as e:
        print(f"   ⚠️ Cleanup warning: {e}")
    
    return len(missing_permissions) == 0

def main():
    """Main test function"""
    success = test_city_admin_navigation()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 City Admin Navigation Test PASSED!")
        print("✅ City admin users should see proper navigation menus")
        print("✅ Frontend navigation should work correctly")
    else:
        print("❌ City Admin Navigation Test FAILED!")
        print("🔧 Please check the group permissions setup")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
