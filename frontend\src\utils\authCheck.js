/**
 * Authentication check utilities
 */

/**
 * Check if the current JWT token is valid and not expired
 * @returns {Object} - { isValid: boolean, isExpired: boolean, expiresAt: Date|null, role: string|null }
 */
export const checkTokenStatus = () => {
  try {
    const accessToken = localStorage.getItem('accessToken');
    
    if (!accessToken) {
      return { isValid: false, isExpired: false, expiresAt: null, role: null };
    }

    // Decode the JWT token
    const base64Url = accessToken.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
    
    const tokenData = JSON.parse(jsonPayload);
    const currentTime = Math.floor(Date.now() / 1000);
    const isExpired = tokenData.exp < currentTime;
    const expiresAt = new Date(tokenData.exp * 1000);
    
    return {
      isValid: !isExpired,
      isExpired: isExpired,
      expiresAt: expiresAt,
      role: tokenData.role || null,
      tenantId: tokenData.tenant_id || null,
      userId: tokenData.user_id || null
    };
  } catch (error) {
    console.error('Error checking token status:', error);
    return { isValid: false, isExpired: false, expiresAt: null, role: null };
  }
};

/**
 * Refresh the JWT token using the refresh token
 * @returns {Promise<boolean>} - True if refresh was successful
 */
export const refreshAuthToken = async () => {
  try {
    const refreshToken = localStorage.getItem('refreshToken');
    if (!refreshToken) {
      console.error('No refresh token available');
      return false;
    }

    const response = await fetch('/api/auth/token/refresh/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ refresh: refreshToken })
    });

    if (response.ok) {
      const data = await response.json();
      if (data.access) {
        localStorage.setItem('accessToken', data.access);
        console.log('✅ Token refreshed successfully');
        return true;
      }
    }
    
    console.error('❌ Failed to refresh token');
    return false;
  } catch (error) {
    console.error('❌ Error refreshing token:', error);
    return false;
  }
};

/**
 * Check if user has permission to perform an action based on role
 * @param {string} action - The action to check (print, approve, etc.)
 * @param {string} userRole - The user's role
 * @returns {boolean} - True if user has permission
 */
export const hasPermissionForAction = (action, userRole) => {
  const permissions = {
    'print': ['subcity_admin', 'city_admin', 'superadmin'],
    'approve_kebele': ['kebele_leader', 'kebele_admin', 'subcity_admin', 'city_admin', 'superadmin'],
    'approve_subcity': ['subcity_admin', 'city_admin', 'superadmin'],
    'create_citizen': ['clerk'],
    'edit_citizen': ['clerk'],
    'create_idcard': ['clerk'],
    'submit_for_approval': ['clerk']
  };

  return permissions[action]?.includes(userRole) || false;
};
