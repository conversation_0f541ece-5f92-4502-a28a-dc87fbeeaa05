#!/usr/bin/env python3
"""
Debug script to check URL patterns and endpoint availability.
"""

import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')

try:
    django.setup()
    
    from django.urls import get_resolver
    from django.conf import settings
    
    print("🧪 Debugging URL Patterns")
    print("=" * 50)
    
    # Get the root URL resolver
    resolver = get_resolver()
    
    def print_url_patterns(patterns, prefix=''):
        """Recursively print URL patterns"""
        for pattern in patterns:
            if hasattr(pattern, 'url_patterns'):
                # This is an include() pattern
                print(f"{prefix}📁 {pattern.pattern} -> {pattern.namespace or 'No namespace'}")
                print_url_patterns(pattern.url_patterns, prefix + "  ")
            else:
                # This is a regular pattern
                view_name = getattr(pattern.callback, '__name__', 'Unknown')
                if hasattr(pattern.callback, 'view_class'):
                    view_name = f"{pattern.callback.view_class.__name__}.{pattern.callback.actions}"
                print(f"{prefix}🔗 {pattern.pattern} -> {view_name}")
    
    print("📋 All URL patterns:")
    print_url_patterns(resolver.url_patterns)
    
    print("\n" + "=" * 50)
    print("🔍 Looking for approval_action patterns...")
    
    # Search for approval_action patterns specifically
    def find_approval_patterns(patterns, prefix=''):
        """Find patterns containing 'approval_action'"""
        found_patterns = []
        for pattern in patterns:
            pattern_str = str(pattern.pattern)
            if hasattr(pattern, 'url_patterns'):
                found_patterns.extend(find_approval_patterns(pattern.url_patterns, prefix + str(pattern.pattern)))
            else:
                if 'approval_action' in pattern_str:
                    found_patterns.append(prefix + pattern_str)
        return found_patterns
    
    approval_patterns = find_approval_patterns(resolver.url_patterns)
    
    if approval_patterns:
        print("✅ Found approval_action patterns:")
        for pattern in approval_patterns:
            print(f"  🎯 {pattern}")
    else:
        print("❌ No approval_action patterns found!")
    
    print("\n" + "=" * 50)
    print("🔍 Checking tenant-specific patterns...")
    
    # Look for tenant patterns
    def find_tenant_patterns(patterns, prefix=''):
        """Find patterns containing tenant IDs"""
        found_patterns = []
        for pattern in patterns:
            pattern_str = str(pattern.pattern)
            if hasattr(pattern, 'url_patterns'):
                found_patterns.extend(find_tenant_patterns(pattern.url_patterns, prefix + str(pattern.pattern)))
            else:
                if 'tenant' in pattern_str.lower() or '<int:tenant_id>' in pattern_str:
                    found_patterns.append(prefix + pattern_str)
        return found_patterns
    
    tenant_patterns = find_tenant_patterns(resolver.url_patterns)
    
    if tenant_patterns:
        print("✅ Found tenant patterns:")
        for pattern in tenant_patterns:
            print(f"  🏢 {pattern}")
    else:
        print("❌ No tenant patterns found!")
        
except Exception as e:
    print(f"❌ Error setting up Django or checking URLs: {e}")
    import traceback
    traceback.print_exc()

print("\n🏁 URL debugging completed!")
