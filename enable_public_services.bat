@echo off
echo 🔧 Enabling Public Services via API calls...

REM First, let's check the current status
echo.
echo 1️⃣ Checking current system settings...
curl -X GET "http://localhost:8000/api/tenants/system-settings/" ^
     -H "Content-Type: application/json"

echo.
echo.
echo 2️⃣ Enabling system-wide public services...
curl -X PUT "http://localhost:8000/api/tenants/system-settings/" ^
     -H "Content-Type: application/json" ^
     -d "{\"public_services_enabled\": true, \"primary_color\": \"#1976d2\", \"secondary_color\": \"#ef6c00\"}"

echo.
echo.
echo 3️⃣ Getting list of tenants...
curl -X GET "http://localhost:8000/api/tenants/" ^
     -H "Content-Type: application/json"

echo.
echo.
echo 🎉 Public services should now be enabled!
echo 📝 You may need to enable individual tenant settings through the admin interface.
echo 🔗 Visit the Public Services Portal to verify: http://localhost:3000/public-services

pause
