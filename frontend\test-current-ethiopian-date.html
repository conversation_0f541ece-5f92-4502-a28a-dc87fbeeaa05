<!DOCTYPE html>
<html>
<head>
    <title>Current Ethiopian Date Test</title>
</head>
<body>
    <h1>Current Ethiopian Date</h1>
    <div id="result"></div>
    
    <script type="module">
        // Mock Ethiopian calendar functions for testing
        function gregorianToEthiopian(gregorianDate) {
            const EPOCH_OFFSET = 1723856; // Days between Ethiopian and Gregorian epochs
            const DAYS_IN_YEAR = 365;
            const DAYS_IN_LEAP_YEAR = 366;
            
            const gregorianYear = gregorianDate.getFullYear();
            const gregorianMonth = gregorianDate.getMonth() + 1; // JavaScript months are 0-based
            const gregorianDay = gregorianDate.getDate();
            
            // Calculate days since Gregorian epoch (January 1, 1 AD)
            let daysSinceGregorianEpoch = 0;
            
            // Add days for complete years
            for (let year = 1; year < gregorianYear; year++) {
                if (isGregorianLeapYear(year)) {
                    daysSinceGregorianEpoch += DAYS_IN_LEAP_YEAR;
                } else {
                    daysSinceGregorianEpoch += DAYS_IN_YEAR;
                }
            }
            
            // Add days for complete months in current year
            const daysInGregorianMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
            if (isGregorianLeapYear(gregorianYear)) {
                daysInGregorianMonth[1] = 29; // February in leap year
            }
            
            for (let month = 1; month < gregorianMonth; month++) {
                daysSinceGregorianEpoch += daysInGregorianMonth[month - 1];
            }
            
            // Add days in current month
            daysSinceGregorianEpoch += gregorianDay;
            
            // Convert to Ethiopian calendar
            const daysSinceEthiopianEpoch = daysSinceGregorianEpoch - EPOCH_OFFSET;
            
            if (daysSinceEthiopianEpoch < 1) {
                return null; // Date is before Ethiopian epoch
            }
            
            // Calculate Ethiopian year
            let ethiopianYear = 1;
            let remainingDays = daysSinceEthiopianEpoch;
            
            while (remainingDays > getDaysInEthiopianYear(ethiopianYear)) {
                remainingDays -= getDaysInEthiopianYear(ethiopianYear);
                ethiopianYear++;
            }
            
            // Calculate Ethiopian month and day
            let ethiopianMonth = 1;
            while (remainingDays > getDaysInEthiopianMonth(ethiopianMonth, ethiopianYear)) {
                remainingDays -= getDaysInEthiopianMonth(ethiopianMonth, ethiopianYear);
                ethiopianMonth++;
            }
            
            const ethiopianDay = remainingDays;
            
            return {
                year: ethiopianYear,
                month: ethiopianMonth,
                day: ethiopianDay
            };
        }
        
        function isGregorianLeapYear(year) {
            return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
        }
        
        function getDaysInEthiopianYear(year) {
            // Ethiopian leap year occurs every 4 years, but the 4th year of each 4-year cycle has 366 days
            return (year % 4 === 3) ? 366 : 365;
        }
        
        function getDaysInEthiopianMonth(month, year) {
            if (month <= 12) {
                return 30; // First 12 months have 30 days each
            } else if (month === 13) {
                // 13th month (Pagume) has 5 or 6 days depending on leap year
                return (year % 4 === 3) ? 6 : 5;
            }
            return 0;
        }
        
        function formatEthiopianDate(ethiopianDate, format = 'DD/MM/YYYY', language = 'en') {
            const ETHIOPIAN_MONTHS_EN = [
                'Meskerem', 'Tikimt', 'Hidar', 'Tahsas', 'Tir', 'Yekatit',
                'Megabit', 'Miazia', 'Ginbot', 'Sene', 'Hamle', 'Nehase', 'Pagume'
            ];
            
            const ETHIOPIAN_MONTHS_AM = [
                'መስከረም', 'ጥቅምት', 'ሕዳር', 'ታሕሳስ', 'ጥር', 'የካቲት',
                'መጋቢት', 'ሚያዝያ', 'ግንቦት', 'ሰኔ', 'ሐምሌ', 'ነሐሴ', 'ጳጉሜ'
            ];
            
            const monthNames = language === 'am' ? ETHIOPIAN_MONTHS_AM : ETHIOPIAN_MONTHS_EN;
            const monthName = monthNames[ethiopianDate.month - 1] || '';
            
            const day = ethiopianDate.day.toString().padStart(2, '0');
            const month = ethiopianDate.month.toString().padStart(2, '0');
            const year = ethiopianDate.year.toString();
            
            return format
                .replace('DD', day)
                .replace('MM', month)
                .replace('MMM', monthName)
                .replace('YYYY', year);
        }
        
        // Test current Ethiopian date
        const today = new Date();
        const ethiopianToday = gregorianToEthiopian(today);
        
        const resultDiv = document.getElementById('result');
        resultDiv.innerHTML = `
            <h2>Today's Date</h2>
            <p><strong>Gregorian:</strong> ${today.toLocaleDateString()}</p>
            <p><strong>Ethiopian:</strong> ${formatEthiopianDate(ethiopianToday, 'DD/MM/YYYY', 'en')}</p>
            <p><strong>Ethiopian (Long):</strong> ${formatEthiopianDate(ethiopianToday, 'DD MMM YYYY', 'en')}</p>
            <p><strong>Ethiopian (Amharic):</strong> ${formatEthiopianDate(ethiopianToday, 'DD MMM YYYY', 'am')}</p>
            <h3>Ethiopian Date Components:</h3>
            <p>Year: ${ethiopianToday.year}</p>
            <p>Month: ${ethiopianToday.month}</p>
            <p>Day: ${ethiopianToday.day}</p>
        `;
    </script>
</body>
</html>
