#!/usr/bin/env python3
"""
Test script to check user permissions for citizen creation
"""

import os
import sys
import django

# Set up Django first
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

# Now import Django modules
from django.test import RequestFactory

from users.models import User
from tenants.models import Tenant
from common.permissions import CanManageCitizens
from tenants.views.tenant_citizen_views import TenantSpecificCitizenViewSet

def test_user_permissions():
    """Test user permissions for citizen creation"""
    print("=== Testing User Permissions ===")
    
    # Get the user
    try:
        user = User.objects.get(username="<EMAIL>")
        print(f"✅ Found user: {user.username}")
        print(f"   Role: {user.role}")
        print(f"   Tenant: {user.tenant}")
        print(f"   Is authenticated: {user.is_authenticated}")
        print(f"   Is superuser: {user.is_superuser}")
    except User.DoesNotExist:
        print("❌ User not found")
        return
    
    # Get the tenant
    try:
        tenant = Tenant.objects.get(id=7)
        print(f"✅ Found tenant: {tenant.name} (ID: {tenant.id})")
        print(f"   Schema: {tenant.schema_name}")
        print(f"   Type: {tenant.type}")
    except Tenant.DoesNotExist:
        print("❌ Tenant not found")
        return
    
    # Test permission class directly
    print("\n=== Testing CanManageCitizens Permission ===")
    
    # Create a mock request
    factory = RequestFactory()
    
    # Test POST request (citizen creation)
    request = factory.post('/api/tenants/7/citizens/')
    request.user = user
    
    # Create mock view
    view = TenantSpecificCitizenViewSet()
    view.action = 'create'
    view.kwargs = {'tenant_id': 7}
    
    # Test permission
    permission = CanManageCitizens()
    has_permission = permission.has_permission(request, view)
    
    print(f"Permission result for POST /api/tenants/7/citizens/: {has_permission}")
    
    # Test fraud check endpoint
    print("\n=== Testing Fraud Check Permission ===")
    request = factory.post('/api/tenants/7/citizens/check-registration-fraud/')
    request.user = user
    
    view.action = 'check_registration_fraud'
    has_permission = permission.has_permission(request, view)
    
    print(f"Permission result for POST check-registration-fraud: {has_permission}")
    
    # Test with different roles
    print("\n=== Testing Different Roles ===")
    original_role = user.role
    
    test_roles = ['clerk', 'kebele_leader', 'kebele_admin', 'subcity_admin', 'city_admin', 'superadmin']
    
    for role in test_roles:
        user.role = role
        has_permission = permission.has_permission(request, view)
        print(f"Role '{role}': {has_permission}")
    
    # Restore original role
    user.role = original_role
    
    print("\n=== Test Complete ===")

if __name__ == "__main__":
    test_user_permissions()
