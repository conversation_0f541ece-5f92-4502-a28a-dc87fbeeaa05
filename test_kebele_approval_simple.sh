#!/bin/bash

# Simple test for kebele leader approval 403 issue

echo "🔧 Testing Kebele Leader Approval Issue"
echo "======================================="

# Step 1: Restart backend to apply fixes
echo "📍 Step 1: Restarting backend..."
docker-compose restart backend
sleep 30

# Step 2: Test authentication
echo ""
echo "📍 Step 2: Testing authentication..."
AUTH_RESPONSE=$(curl -s -X POST http://10.139.8.141:8000/api/auth/token/ \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}')

if echo "$AUTH_RESPONSE" | grep -q "access"; then
    echo "✅ Authentication successful"
    
    TOKEN=$(echo "$AUTH_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin)['access'])" 2>/dev/null)
    
    if [ ! -z "$TOKEN" ]; then
        echo ""
        echo "📍 Step 3: Testing ID card approval..."
        
        # Get a kebele tenant
        KEBELE_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
          "http://10.139.8.141:8000/api/tenants/?type=kebele")
        
        KEBELE_ID=$(echo "$KEBELE_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, dict) and 'results' in data and len(data['results']) > 0:
        print(data['results'][0]['id'])
    else:
        print('none')
except:
    print('none')
" 2>/dev/null)
        
        if [ "$KEBELE_ID" != "none" ]; then
            echo "Using kebele ID: $KEBELE_ID"
            
            # Get ID cards
            IDCARDS_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
              http://10.139.8.141:8000/api/tenants/$KEBELE_ID/idcards/)
            
            echo "ID cards response status: $(echo "$IDCARDS_RESPONSE" | head -c 100)"
            
            # Test approval action (this will likely fail but we'll see the error)
            echo ""
            echo "Testing approval action..."
            APPROVAL_RESPONSE=$(curl -s -X POST \
              -H "Authorization: Bearer $TOKEN" \
              -H "Content-Type: application/json" \
              -d '{"action":"approve","comment":"Test approval"}' \
              http://10.139.8.141:8000/api/tenants/$KEBELE_ID/idcards/1/approval_action/ \
              -w "HTTP_STATUS:%{http_code}")
            
            HTTP_STATUS=$(echo "$APPROVAL_RESPONSE" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
            RESPONSE_BODY=$(echo "$APPROVAL_RESPONSE" | sed 's/HTTP_STATUS:[0-9]*$//')
            
            echo "Approval status: $HTTP_STATUS"
            echo "Response: $RESPONSE_BODY"
            
        else
            echo "No kebele found"
        fi
    else
        echo "Could not extract token"
    fi
else
    echo "Authentication failed"
fi

echo ""
echo "📍 Step 4: Checking backend logs..."
docker-compose logs --tail=10 backend | grep -E "403|approval|error" || echo "No relevant logs"

echo ""
echo "✅ Test completed!"
echo ""
echo "🧪 Manual test steps:"
echo "1. Login as kebele leader"
echo "2. Go to ID Cards"
echo "3. Find pending approval ID card"
echo "4. Click Approve"
echo "5. Check browser console for detailed error"
