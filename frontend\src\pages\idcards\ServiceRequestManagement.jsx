import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  <PERSON>ton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Chip,
  Grid,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Visibility as ViewIcon,
  Send as ForwardIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import axios from '../../utils/axios';

const ServiceRequestManagement = () => {
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [actionType, setActionType] = useState(''); // 'approve' or 'reject'
  const [notes, setNotes] = useState('');
  const [rejectionReason, setRejectionReason] = useState('');
  const [alert, setAlert] = useState({ show: false, type: 'info', message: '' });

  useEffect(() => {
    fetchServiceRequests();
  }, []);

  const showAlert = (type, message) => {
    setAlert({ show: true, type, message });
    setTimeout(() => setAlert({ show: false, type: 'info', message: '' }), 5000);
  };

  const fetchServiceRequests = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/idcards/services/requests/');
      setRequests(response.data.requests || []);
    } catch (error) {
      console.error('Error fetching service requests:', error);
      showAlert('error', 'Failed to fetch service requests');
    } finally {
      setLoading(false);
    }
  };

  const handleAction = async (request, action) => {
    setSelectedRequest(request);
    setActionType(action);
    setDialogOpen(true);
    setNotes('');
    setRejectionReason('');
  };

  const submitAction = async () => {
    try {
      setLoading(true);
      
      const endpoint = actionType === 'approve' 
        ? `/api/idcards/services/requests/${selectedRequest.id}/approve/`
        : `/api/idcards/services/requests/${selectedRequest.id}/reject/`;
      
      const payload = actionType === 'approve' 
        ? { notes }
        : { reason: rejectionReason };
      
      const response = await axios.post(endpoint, payload);
      
      if (response.data.success) {
        showAlert('success', response.data.message);
        setDialogOpen(false);
        fetchServiceRequests(); // Refresh the list
      } else {
        showAlert('error', response.data.error || 'Action failed');
      }
    } catch (error) {
      console.error('Error submitting action:', error);
      showAlert('error', error.response?.data?.error || 'Action failed');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Pending': return 'warning';
      case 'Approved by Kebele': return 'info';
      case 'Processing at SubCity': return 'primary';
      case 'Completed': return 'success';
      case 'Rejected': return 'error';
      default: return 'default';
    }
  };

  const getServiceTypeColor = (serviceType) => {
    switch (serviceType) {
      case 'Renewal': return '#4CAF50';
      case 'Replacement': return '#FF9800';
      case 'Reprint': return '#2196F3';
      default: return '#757575';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
          ID Card Service Requests
        </Typography>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={fetchServiceRequests}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>

      {alert.show && (
        <Alert severity={alert.type} sx={{ mb: 3 }}>
          {alert.message}
        </Alert>
      )}

      {loading && requests.length === 0 ? (
        <Box display="flex" justifyContent="center" py={4}>
          <CircularProgress />
        </Box>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell><strong>Citizen</strong></TableCell>
                <TableCell><strong>Digital ID</strong></TableCell>
                <TableCell><strong>Service Type</strong></TableCell>
                <TableCell><strong>Method</strong></TableCell>
                <TableCell><strong>Status</strong></TableCell>
                <TableCell><strong>Requested</strong></TableCell>
                <TableCell><strong>Actions</strong></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {requests.map((request) => (
                <TableRow key={request.id} hover>
                  <TableCell>{request.citizen_name}</TableCell>
                  <TableCell>
                    <Typography variant="body2" fontFamily="monospace">
                      {request.citizen_digital_id}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={request.service_type}
                      size="small"
                      sx={{
                        backgroundColor: getServiceTypeColor(request.service_type),
                        color: 'white',
                        fontWeight: 'bold'
                      }}
                    />
                  </TableCell>
                  <TableCell>{request.application_method}</TableCell>
                  <TableCell>
                    <Chip
                      label={request.status}
                      size="small"
                      color={getStatusColor(request.status)}
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {new Date(request.requested_at).toLocaleDateString()}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box display="flex" gap={1}>
                      <Tooltip title="View Details">
                        <IconButton
                          size="small"
                          onClick={() => handleAction(request, 'view')}
                        >
                          <ViewIcon />
                        </IconButton>
                      </Tooltip>
                      
                      {request.status === 'Pending' && (
                        <>
                          <Tooltip title="Approve & Forward">
                            <IconButton
                              size="small"
                              color="success"
                              onClick={() => handleAction(request, 'approve')}
                            >
                              <ApproveIcon />
                            </IconButton>
                          </Tooltip>
                          
                          <Tooltip title="Reject">
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleAction(request, 'reject')}
                            >
                              <RejectIcon />
                            </IconButton>
                          </Tooltip>
                        </>
                      )}
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
              
              {requests.length === 0 && !loading && (
                <TableRow>
                  <TableCell colSpan={7} align="center" sx={{ py: 4 }}>
                    <Typography variant="body1" color="textSecondary">
                      No service requests found
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Action Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {actionType === 'approve' && 'Approve & Forward Request'}
          {actionType === 'reject' && 'Reject Request'}
          {actionType === 'view' && 'Request Details'}
        </DialogTitle>
        
        <DialogContent>
          {selectedRequest && (
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    Citizen Name
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {selectedRequest.citizen_name}
                  </Typography>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    Digital ID
                  </Typography>
                  <Typography variant="body1" gutterBottom fontFamily="monospace">
                    {selectedRequest.citizen_digital_id}
                  </Typography>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    Service Type
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {selectedRequest.service_type}
                  </Typography>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    Application Method
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {selectedRequest.application_method}
                  </Typography>
                </Grid>
                
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="textSecondary">
                    Reason
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {selectedRequest.reason}
                  </Typography>
                </Grid>
              </Grid>

              {actionType === 'approve' && (
                <TextField
                  fullWidth
                  label="Notes (Optional)"
                  multiline
                  rows={3}
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  sx={{ mt: 3 }}
                  placeholder="Add any notes for the subcity admin..."
                />
              )}

              {actionType === 'reject' && (
                <TextField
                  fullWidth
                  label="Rejection Reason"
                  multiline
                  rows={3}
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  sx={{ mt: 3 }}
                  placeholder="Explain why this request is being rejected..."
                  required
                />
              )}
            </Box>
          )}
        </DialogContent>
        
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>
            {actionType === 'view' ? 'Close' : 'Cancel'}
          </Button>
          
          {actionType === 'approve' && (
            <Button
              onClick={submitAction}
              variant="contained"
              color="success"
              disabled={loading}
              startIcon={loading ? <CircularProgress size={16} /> : <ForwardIcon />}
            >
              Approve & Forward to SubCity
            </Button>
          )}
          
          {actionType === 'reject' && (
            <Button
              onClick={submitAction}
              variant="contained"
              color="error"
              disabled={loading || !rejectionReason.trim()}
              startIcon={loading ? <CircularProgress size={16} /> : <RejectIcon />}
            >
              Reject Request
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ServiceRequestManagement;
