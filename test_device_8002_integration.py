#!/usr/bin/env python3
"""
Test Device 8002 Integration with Duplicate Detection

This script tests the integration between your biometric device service
on port 8002 and the duplicate detection system.
"""

import requests
import json
import time
from datetime import datetime


def test_device_service():
    """Test if the device service on port 8002 is working"""
    print("🔌 Testing Device Service on Port 8002")
    print("-" * 50)
    
    try:
        # Test health endpoint
        response = requests.get('http://localhost:8002/api/health', timeout=5)
        
        if response.status_code == 200:
            health_data = response.json()
            print(f"   ✅ Service Status: {health_data.get('status', 'Unknown')}")
            print(f"   Device Ready: {health_data.get('device_ready', False)}")
            print(f"   Service Type: {health_data.get('function', 'Unknown')}")
            return True
        else:
            print(f"   ❌ Health check failed: HTTP {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ❌ Device service not running on port 8002")
        print("   💡 Start with: cd local-biometric-service && python minimal_capture_service.py")
        return False
    except Exception as e:
        print(f"   ❌ Health check error: {e}")
        return False


def test_device_status():
    """Test device status endpoint"""
    print("\n📱 Testing Device Status")
    print("-" * 50)
    
    try:
        response = requests.get('http://localhost:8002/api/device/status', timeout=5)
        
        if response.status_code == 200:
            status_data = response.json()
            print(f"   Device Connected: {status_data.get('connected', False)}")
            print(f"   Device Model: {status_data.get('model', 'Unknown')}")
            print(f"   Real Device: {status_data.get('real_device', False)}")
            print(f"   Interface: {status_data.get('data', {}).get('interface', 'Unknown')}")
            return status_data.get('connected', False)
        else:
            print(f"   ❌ Status check failed: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Status check error: {e}")
        return False


def test_fingerprint_capture():
    """Test fingerprint capture from device"""
    print("\n🖐️ Testing Fingerprint Capture")
    print("-" * 50)
    
    try:
        print("   📋 Requesting left thumb capture...")
        print("   👆 Please place your LEFT THUMB on the scanner when prompted")
        
        response = requests.post(
            'http://localhost:8002/api/capture/fingerprint',
            json={'thumb_type': 'left'},
            timeout=45  # Give time for user to place finger
        )
        
        if response.status_code == 200:
            capture_data = response.json()
            
            if capture_data.get('success'):
                data = capture_data.get('data', {})
                print(f"   ✅ Capture Successful!")
                print(f"   Quality Score: {data.get('quality_score', 0)}%")
                print(f"   Template Size: {data.get('template_size', 0)} bytes")
                print(f"   Has ANSI Template: {data.get('has_ansi_template', False)}")
                print(f"   Minutiae Count: {data.get('minutiae_count', 0)}")
                
                return capture_data
            else:
                print(f"   ❌ Capture Failed: {capture_data.get('error', 'Unknown error')}")
                return None
        else:
            print(f"   ❌ Capture HTTP Error: {response.status_code}")
            return None
            
    except requests.exceptions.Timeout:
        print("   ⏰ Capture timeout - no finger detected or device not responding")
        return None
    except Exception as e:
        print(f"   ❌ Capture error: {e}")
        return None


def test_ansi_template_extraction():
    """Test ANSI template extraction"""
    print("\n🧬 Testing ANSI Template Extraction")
    print("-" * 50)
    
    try:
        print("   📋 Requesting ANSI template extraction...")
        print("   👆 Please place your finger on the scanner")
        
        response = requests.post(
            'http://localhost:8002/api/get-ansi-template',
            json={'thumb_type': 'left'},
            timeout=45
        )
        
        if response.status_code == 200:
            ansi_data = response.json()
            
            if ansi_data.get('success'):
                data = ansi_data.get('data', {})
                ansi_template = data.get('ansi_iso_template', '')
                
                print(f"   ✅ ANSI Template Extracted!")
                print(f"   Template Size: {len(ansi_template)} characters")
                print(f"   Quality Score: {data.get('quality_score', 0)}%")
                print(f"   Template Preview: {ansi_template[:50]}..." if ansi_template else "   No template data")
                
                return ansi_template
            else:
                print(f"   ❌ ANSI Extraction Failed: {ansi_data.get('error', 'Unknown error')}")
                return None
        else:
            print(f"   ❌ ANSI HTTP Error: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"   ❌ ANSI extraction error: {e}")
        return None


def test_django_integration():
    """Test Django backend integration"""
    print("\n🔗 Testing Django Backend Integration")
    print("-" * 50)
    
    try:
        # Test if Django backend is running
        response = requests.get('http://localhost:8000/api/biometrics/duplicate-detection-stats/', timeout=5)
        
        if response.status_code == 401:
            print("   🔐 Django backend is running (requires authentication)")
            return True
        elif response.status_code == 200:
            print("   ✅ Django backend is running and accessible")
            return True
        else:
            print(f"   ❌ Django backend error: HTTP {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ❌ Django backend not running on port 8000")
        print("   💡 Start with: cd backend && python manage.py runserver")
        return False
    except Exception as e:
        print(f"   ❌ Django test error: {e}")
        return False


def test_complete_workflow():
    """Test the complete workflow: capture -> duplicate check"""
    print("\n🔄 Testing Complete Workflow")
    print("-" * 50)
    
    print("This test will:")
    print("1. Capture a fingerprint from your device")
    print("2. Send it to Django for duplicate detection")
    print("3. Show the complete integration results")
    print()
    
    # Note: This would require authentication for the Django endpoint
    print("   ⚠️ Complete workflow test requires:")
    print("   - Django backend running with authentication")
    print("   - Valid user credentials")
    print("   - Existing fingerprint data in database")
    print()
    print("   💡 To test manually:")
    print("   1. Start both services")
    print("   2. Register citizens with fingerprints via frontend")
    print("   3. Try to register the same person again")
    print("   4. System should detect duplicate and block registration")


def main():
    """Main test function"""
    print("🧪 DEVICE 8002 INTEGRATION TEST")
    print("=" * 60)
    print(f"Test started at: {datetime.now().isoformat()}")
    print()
    
    # Test 1: Device service availability
    device_available = test_device_service()
    
    if not device_available:
        print("\n🚨 DEVICE SERVICE NOT AVAILABLE")
        print("Please start the device service first:")
        print("cd local-biometric-service")
        print("python minimal_capture_service.py")
        return 1
    
    # Test 2: Device status
    device_ready = test_device_status()
    
    # Test 3: Django backend
    django_available = test_django_integration()
    
    # Test 4: Fingerprint capture (if device is ready)
    capture_result = None
    if device_ready:
        print("\n⚠️ FINGERPRINT CAPTURE TEST")
        print("This will require you to place your finger on the scanner.")
        user_input = input("Do you want to test fingerprint capture? (y/n): ")
        
        if user_input.lower() == 'y':
            capture_result = test_fingerprint_capture()
            
            if capture_result:
                # Test ANSI template extraction
                ansi_template = test_ansi_template_extraction()
    
    # Test 5: Complete workflow explanation
    test_complete_workflow()
    
    # Summary
    print("\n📊 TEST SUMMARY")
    print("=" * 60)
    print(f"✅ Device Service (Port 8002): {device_available}")
    print(f"✅ Device Ready: {device_ready}")
    print(f"✅ Django Backend (Port 8000): {django_available}")
    print(f"✅ Fingerprint Capture: {capture_result is not None}")
    
    if device_available and django_available:
        print("\n🎉 INTEGRATION READY!")
        print("Both services are running and can communicate.")
        print()
        print("🔧 Next Steps:")
        print("1. Register citizens with fingerprints via frontend")
        print("2. The system will automatically check for duplicates")
        print("3. Duplicate registrations will be blocked")
        print()
        print("🌐 Access Points:")
        print("- Device Service: http://localhost:8002")
        print("- Django Backend: http://localhost:8000")
        print("- Frontend: http://localhost:3000")
    else:
        print("\n⚠️ SETUP REQUIRED")
        print("Please start the missing services and try again.")
    
    return 0


if __name__ == '__main__':
    exit_code = main()
    exit(exit_code)
