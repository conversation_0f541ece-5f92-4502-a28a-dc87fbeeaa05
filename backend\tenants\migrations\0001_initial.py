# Generated by Django 4.2.7 on 2025-06-07 12:06

from django.db import migrations, models
import django.db.models.deletion
import django_tenants.postgresql_backend.base
import tenants.models.citizen


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Citizen',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('digital_id', models.Char<PERSON>ield(max_length=50, unique=True)),
                ('first_name', models.Char<PERSON>ield(max_length=100)),
                ('middle_name', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name', models.CharField(max_length=100)),
                ('first_name_am', models.Char<PERSON><PERSON>(blank=True, max_length=100, null=True)),
                ('middle_name_am', models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ('last_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('date_of_birth', models.DateField(blank=True, null=True)),
                ('gender', models.CharField(blank=True, max_length=10, null=True)),
                ('photo', models.TextField(blank=True, null=True)),
                ('religion', models.IntegerField(blank=True, null=True)),
                ('subcity', models.IntegerField(blank=True, null=True)),
                ('kebele', models.IntegerField(blank=True, null=True)),
                ('ketena', models.IntegerField(blank=True, null=True)),
                ('status', models.IntegerField(blank=True, null=True)),
                ('house_number', models.CharField(blank=True, max_length=20, null=True)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('nationality', models.IntegerField(blank=True, null=True)),
                ('region', models.IntegerField(blank=True, null=True)),
                ('id_issue_date', models.DateField(blank=True, null=True)),
                ('id_expiry_date', models.DateField(blank=True, null=True)),
                ('employment', models.CharField(blank=True, max_length=100, null=True)),
                ('employee_type', models.CharField(blank=True, max_length=100, null=True)),
                ('organization_name', models.CharField(blank=True, max_length=200, null=True)),
                ('marital_status', models.IntegerField(blank=True, null=True)),
                ('is_resident', models.BooleanField(default=False, help_text='Indicates whether the citizen is a resident of the city.')),
                ('is_active', models.BooleanField(default=True)),
                ('transfer_reason', models.CharField(blank=True, help_text='Reason for transfer if citizen was transferred', max_length=255, null=True)),
                ('transfer_date', models.DateTimeField(blank=True, help_text='Date when citizen was transferred', null=True)),
            ],
            options={
                'verbose_name': 'Citizen',
                'verbose_name_plural': 'Citizens',
            },
        ),
        migrations.CreateModel(
            name='CitizenStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Citizen Status',
                'verbose_name_plural': 'Citizen Statuses',
            },
        ),
        migrations.CreateModel(
            name='CityAdministration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('city_code', models.CharField(max_length=50, unique=True)),
                ('city_name', models.CharField(max_length=100)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='logos/')),
                ('motto_slogan', models.TextField(blank=True, null=True)),
                ('city_intro', models.TextField(blank=True, null=True)),
                ('mayor_name', models.CharField(blank=True, max_length=100, null=True)),
                ('deputy_mayor', models.CharField(blank=True, max_length=100, null=True)),
                ('contact_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('contact_phone', models.CharField(blank=True, max_length=20, null=True)),
                ('google_maps_url', models.URLField(blank=True, null=True)),
                ('area_sq_km', models.FloatField(blank=True, null=True)),
                ('elevation_meters', models.IntegerField(blank=True, null=True)),
                ('timezone', models.CharField(default='EAT', max_length=50)),
                ('website', models.URLField(blank=True, null=True)),
                ('headquarter_address', models.TextField(blank=True, null=True)),
                ('postal_code', models.CharField(blank=True, max_length=20, null=True)),
                ('established_date', models.DateField(blank=True, null=True)),
                ('is_resident', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'City Administration',
                'verbose_name_plural': 'City Administrations',
                'ordering': ['city_name', 'city_code'],
            },
        ),
        migrations.CreateModel(
            name='Country',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=3, unique=True)),
                ('capital_city', models.CharField(blank=True, max_length=100, null=True)),
            ],
            options={
                'verbose_name': 'Country',
                'verbose_name_plural': 'Countries',
                'ordering': ['name', 'code'],
            },
        ),
        migrations.CreateModel(
            name='CurrentStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Current status of the citizen', max_length=50, unique=True)),
            ],
            options={
                'verbose_name': 'Current Status',
                'verbose_name_plural': 'Current Statuses',
            },
        ),
        migrations.CreateModel(
            name='DocumentType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Document Type',
                'verbose_name_plural': 'Document Types',
            },
        ),
        migrations.CreateModel(
            name='EmploymentType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Employment Type',
                'verbose_name_plural': 'Employment Types',
            },
        ),
        migrations.CreateModel(
            name='Kebele',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(default='Kebele', max_length=100)),
                ('code', models.CharField(blank=True, max_length=10, null=True, unique=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Kebele',
                'verbose_name_plural': 'Kebeles',
            },
        ),
        migrations.CreateModel(
            name='MaritalStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=50)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Marital Status',
                'verbose_name_plural': 'Marital Statuses',
            },
        ),
        migrations.CreateModel(
            name='Relationship',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Relationship',
                'verbose_name_plural': 'Relationships',
            },
        ),
        migrations.CreateModel(
            name='Religion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Religion',
                'verbose_name_plural': 'Religions',
            },
        ),
        migrations.CreateModel(
            name='Tenant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('schema_name', models.CharField(db_index=True, max_length=63, unique=True, validators=[django_tenants.postgresql_backend.base._check_schema_name])),
                ('name', models.CharField(max_length=100)),
                ('type', models.CharField(choices=[('city', 'City'), ('subcity', 'Sub City'), ('kebele', 'Kebele')], max_length=10)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='tenants.tenant')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='SubCity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(default='Subcity', max_length=100, unique=True)),
                ('code', models.CharField(blank=True, max_length=10, null=True, unique=True)),
                ('is_active', models.BooleanField(default=True)),
                ('city', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='subcities', to='tenants.cityadministration')),
                ('tenant', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='subcity_profile', to='tenants.tenant')),
            ],
            options={
                'verbose_name': 'SubCity',
                'verbose_name_plural': 'SubCities',
            },
        ),
        migrations.CreateModel(
            name='Region',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=5, unique=True)),
                ('country', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='regions', to='tenants.country')),
            ],
            options={
                'verbose_name': 'Region',
                'verbose_name_plural': 'Regions',
                'ordering': ['name', 'code'],
            },
        ),
        migrations.CreateModel(
            name='Photo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('photo', models.ImageField(upload_to=tenants.models.citizen.citizen_photo_path)),
                ('upload_date', models.DateTimeField(auto_now_add=True)),
                ('citizen', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='photo_record', to='tenants.citizen')),
            ],
            options={
                'verbose_name': 'Photo',
                'verbose_name_plural': 'Photos',
            },
        ),
        migrations.CreateModel(
            name='Parent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('first_name', models.CharField(max_length=100)),
                ('middle_name', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name', models.CharField(max_length=100)),
                ('first_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('middle_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('gender', models.CharField(blank=True, choices=[('male', 'Male'), ('female', 'Female')], max_length=10, null=True)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('is_resident', models.BooleanField(default=False, help_text='Indicates whether the parent is a resident of the city.')),
                ('nationality', models.IntegerField(blank=True, null=True)),
                ('citizen', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='parents', to='tenants.citizen')),
                ('linked_citizen', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='as_parent', to='tenants.citizen')),
            ],
            options={
                'verbose_name': 'Parent',
                'verbose_name_plural': 'Parents',
            },
        ),
        migrations.CreateModel(
            name='Ketena',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(blank=True, max_length=10, null=True, unique=True)),
                ('is_active', models.BooleanField(default=True)),
                ('kebele', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ketenes', to='tenants.kebele')),
            ],
            options={
                'verbose_name': 'Ketena',
                'verbose_name_plural': 'Ketenes',
            },
        ),
        migrations.AddField(
            model_name='kebele',
            name='sub_city',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='kebeles', to='tenants.subcity'),
        ),
        migrations.AddField(
            model_name='kebele',
            name='tenant',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='kebele_profile', to='tenants.tenant'),
        ),
        migrations.CreateModel(
            name='Domain',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('domain', models.CharField(db_index=True, max_length=253, unique=True)),
                ('is_primary', models.BooleanField(db_index=True, default=True)),
                ('tenant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='domains', to='tenants.tenant')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Document',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('document_file', models.FileField(upload_to=tenants.models.citizen.citizen_document_path)),
                ('issue_date', models.DateField(blank=True, null=True)),
                ('expiry_date', models.DateField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('citizen', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='tenants.citizen')),
                ('document_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='tenants.documenttype')),
            ],
            options={
                'verbose_name': 'Document',
                'verbose_name_plural': 'Documents',
            },
        ),
        migrations.AddField(
            model_name='cityadministration',
            name='country',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='tenants.country'),
        ),
        migrations.AddField(
            model_name='cityadministration',
            name='region',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='tenants.region'),
        ),
        migrations.AddField(
            model_name='cityadministration',
            name='tenant',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='city_profile', to='tenants.tenant'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='current_status',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='tenants.currentstatus'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='father',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='children_as_father', to='tenants.parent'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='mother',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='children_as_mother', to='tenants.parent'),
        ),
        migrations.CreateModel(
            name='Child',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('first_name', models.CharField(max_length=100)),
                ('middle_name', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name', models.CharField(max_length=100)),
                ('first_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('middle_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('date_of_birth', models.DateField()),
                ('is_active', models.BooleanField(default=True)),
                ('is_resident', models.BooleanField(default=False, help_text='Indicates whether the child is a resident of the city.')),
                ('nationality', models.IntegerField(blank=True, null=True)),
                ('citizen', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='children', to='tenants.citizen')),
                ('linked_citizen', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='as_child', to='tenants.citizen')),
            ],
            options={
                'verbose_name': 'Child',
                'verbose_name_plural': 'Children',
            },
        ),
        migrations.CreateModel(
            name='Biometric',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('left_hand_fingerprint', models.BinaryField(blank=True, null=True)),
                ('right_hand_fingerprint', models.BinaryField(blank=True, null=True)),
                ('left_thumb_fingerprint', models.BinaryField(blank=True, null=True)),
                ('right_thumb_fingerprint', models.BinaryField(blank=True, null=True)),
                ('left_eye_iris_scan', models.BinaryField(blank=True, null=True)),
                ('right_eye_iris_scan', models.BinaryField(blank=True, null=True)),
                ('citizen', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='biometric_record', to='tenants.citizen')),
            ],
            options={
                'verbose_name': 'Biometric',
                'verbose_name_plural': 'Biometrics',
            },
        ),
        migrations.CreateModel(
            name='Spouse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('first_name', models.CharField(max_length=100)),
                ('middle_name', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name', models.CharField(max_length=100)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('nationality', models.IntegerField(blank=True, null=True)),
                ('primary_contact', models.BooleanField(default=False, help_text='Indicates whether this is the primary emergency contact.')),
                ('is_active', models.BooleanField(default=True)),
                ('is_resident', models.BooleanField(default=False, help_text='Indicates whether the spouse is a resident of the city.')),
                ('citizen', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='spouse_records', to='tenants.citizen')),
                ('linked_citizen', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='as_spouse', to='tenants.citizen')),
            ],
            options={
                'verbose_name': 'Spouse',
                'verbose_name_plural': 'Spouses',
                'unique_together': {('citizen', 'phone')},
            },
        ),
        migrations.CreateModel(
            name='EmergencyContact',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('first_name', models.CharField(max_length=100)),
                ('last_name', models.CharField(max_length=100)),
                ('middle_name', models.CharField(blank=True, max_length=100, null=True)),
                ('relationship', models.CharField(blank=True, max_length=100, null=True)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('nationality', models.IntegerField(blank=True, null=True)),
                ('primary_contact', models.BooleanField(default=False, help_text='Indicates whether this is the primary emergency contact.')),
                ('is_active', models.BooleanField(default=True)),
                ('is_resident', models.BooleanField(default=False, help_text='Indicates whether the emergency contact is a resident of the city.')),
                ('citizen', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='emergency_contacts', to='tenants.citizen')),
                ('linked_citizen', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='as_emergency_contact', to='tenants.citizen')),
            ],
            options={
                'verbose_name': 'Emergency Contact',
                'verbose_name_plural': 'Emergency Contacts',
                'unique_together': {('citizen', 'phone')},
            },
        ),
    ]
