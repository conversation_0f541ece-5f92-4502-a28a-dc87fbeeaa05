#!/usr/bin/env python3
"""
Quick CSS Fix for Production Django
"""

import subprocess
import urllib.request

def run_production_command(cmd_list):
    """Run production docker command"""
    full_cmd = ["docker-compose", "-f", "docker-compose.production.yaml"] + cmd_list
    
    try:
        result = subprocess.run(
            full_cmd,
            capture_output=True,
            text=True,
            cwd="C:\\Users\\<USER>\\Desktop\\GoID",
            timeout=60
        )
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def fix_static_files():
    """Fix static files collection"""
    
    print("🔧 Fixing Static Files...")
    print("=" * 25)
    
    # Step 1: Collect static files
    print("📦 Collecting static files...")
    success, stdout, stderr = run_production_command([
        "exec", "backend", "python", "manage.py", "collectstatic", "--noinput", "--clear"
    ])
    
    if success:
        print("✅ Static files collected")
    else:
        print(f"❌ Collection failed: {stderr}")
        return False
    
    # Step 2: Check if admin CSS exists
    print("🔍 Checking admin CSS files...")
    success, stdout, stderr = run_production_command([
        "exec", "backend", "find", "/app", "-name", "base.css", "-type", "f"
    ])
    
    if success and "base.css" in stdout:
        print(f"✅ Found admin CSS: {stdout.strip()}")
    else:
        print("❌ Admin CSS not found!")
        return False
    
    # Step 3: Set permissions
    print("🔐 Setting file permissions...")
    run_production_command([
        "exec", "backend", "chmod", "-R", "755", "/app/staticfiles/"
    ])
    
    return True

def check_static_serving():
    """Check what's being served for static files"""
    
    print(f"\n🧪 Testing Static File Serving...")
    print("=" * 35)
    
    test_url = "http://localhost:8000/static/admin/css/base.css"
    
    try:
        response = urllib.request.urlopen(test_url, timeout=10)
        content_type = response.headers.get('Content-Type', 'unknown')
        content = response.read().decode()[:200]
        
        print(f"📊 Response Analysis:")
        print(f"   Status: {response.getcode()}")
        print(f"   Content-Type: {content_type}")
        print(f"   Content preview: {content[:100]}...")
        
        if "text/css" in content_type:
            print("✅ CSS is being served correctly!")
            return True
        elif "text/html" in content_type:
            print("❌ HTML being served instead of CSS (404 page)")
            return False
        else:
            print(f"⚠️ Unexpected content type: {content_type}")
            return False
            
    except urllib.error.HTTPError as e:
        print(f"❌ HTTP Error {e.code}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def enable_static_serving():
    """Enable Django static file serving"""
    
    print(f"\n⚙️ Enabling Static File Serving...")
    print("=" * 35)
    
    # Check current settings
    print("🔍 Checking Django settings...")
    success, stdout, stderr = run_production_command([
        "exec", "backend", "python", "manage.py", "shell", "-c",
        "from django.conf import settings; print(f'DEBUG: {settings.DEBUG}'); print(f'STATIC_URL: {settings.STATIC_URL}'); print(f'STATIC_ROOT: {settings.STATIC_ROOT}')"
    ])
    
    if success:
        print(f"   Current settings: {stdout.strip()}")
    
    # Create a temporary fix for static serving
    print("🔧 Creating static serving fix...")
    
    static_fix_script = '''
import os
from django.conf import settings
from django.core.management import execute_from_command_line

# Temporary fix to enable static serving
if not settings.DEBUG:
    # Add static files serving for production
    from django.conf.urls.static import static
    from django.urls import include, path
    
    # This should be added to your main urls.py
    print("Add this to your main urls.py:")
    print("from django.conf.urls.static import static")
    print("from django.conf import settings")
    print("")
    print("urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)")
'''
    
    print("💡 Static serving fix needed in urls.py:")
    print(static_fix_script)

def restart_backend():
    """Restart backend service"""
    
    print(f"\n🔄 Restarting Backend...")
    print("=" * 20)
    
    success, stdout, stderr = run_production_command(["restart", "backend"])
    
    if success:
        print("✅ Backend restarted")
        
        # Wait for restart
        import time
        print("⏰ Waiting for service to start...")
        time.sleep(10)
        
        return True
    else:
        print(f"❌ Restart failed: {stderr}")
        return False

def test_admin_interface():
    """Test admin interface"""
    
    print(f"\n🧪 Testing Admin Interface...")
    print("=" * 30)
    
    try:
        response = urllib.request.urlopen("http://localhost:8000/admin/", timeout=10)
        content = response.read().decode()
        
        print(f"📊 Admin Interface:")
        print(f"   Status: {response.getcode()}")
        
        # Check if CSS is loading
        if "base.css" in content:
            print("✅ CSS references found in HTML")
        else:
            print("⚠️ No CSS references found")
        
        # Check for styling
        if "django-admin" in content:
            print("✅ Django admin structure present")
        else:
            print("⚠️ Django admin structure missing")
            
        return True
        
    except Exception as e:
        print(f"❌ Admin test failed: {e}")
        return False

def main():
    """Main function"""
    
    print("Quick CSS Fix for Django Admin")
    print("=" * 35)
    
    print("This will fix the missing CSS in Django admin interface.")
    print()
    
    # Fix static files
    if not fix_static_files():
        print("❌ Static files fix failed")
        return
    
    # Check static serving
    css_working = check_static_serving()
    
    if not css_working:
        # Enable static serving
        enable_static_serving()
        
        # Restart backend
        restart_backend()
        
        # Test again
        css_working = check_static_serving()
    
    # Test admin interface
    test_admin_interface()
    
    print(f"\n" + "="*50)
    print("QUICK CSS FIX SUMMARY")
    print("="*50)
    
    if css_working:
        print("🎉 SUCCESS!")
        print("✅ CSS files are now being served correctly")
        print("✅ Admin interface should display properly")
    else:
        print("⚠️ PARTIAL SUCCESS")
        print("✅ Static files collected")
        print("❌ CSS still not serving correctly")
        
        print(f"\n💡 Manual fix needed:")
        print(f"1. Add static serving to urls.py:")
        print(f"   from django.conf.urls.static import static")
        print(f"   from django.conf import settings")
        print(f"   urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)")
        print(f"")
        print(f"2. Or install whitenoise:")
        print(f"   pip install whitenoise")
        print(f"   Add 'whitenoise.middleware.WhiteNoiseMiddleware' to MIDDLEWARE")
    
    print(f"\n🧪 Test URLs:")
    print(f"   http://localhost:8000/admin/ (should have CSS now)")
    print(f"   http://localhost:8000/static/admin/css/base.css (should return CSS)")
    
    input("\nPress Enter to exit...")

if __name__ == '__main__':
    main()
