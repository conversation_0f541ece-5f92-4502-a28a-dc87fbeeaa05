@echo off
echo ========================================
echo   Windows Compatible Futronic SDK Service
echo   No Unicode Issues - Production Ready
echo ========================================
echo.

echo Checking Python installation...
python --version
if errorlevel 1 (
    echo ERROR: Python not found. Please install Python 3.7+
    pause
    exit /b 1
)

echo.
echo Checking required DLL files...
if not exist "ftrScanAPI.dll" (
    echo ERROR: ftrScanAPI.dll not found
    echo Please ensure Futronic SDK files are in the current directory
    pause
    exit /b 1
)

if exist "ftrMathAPI.dll" (
    echo SUCCESS: ftrMathAPI.dll found - Template matching enabled
) else (
    echo WARNING: ftrMathAPI.dll not found - Template matching disabled
)

echo.
echo Installing required Python packages...
pip install flask flask-cors

echo.
echo Starting Windows Compatible Futronic SDK Service...
echo Service will be available at: http://localhost:8001
echo.
echo Press Ctrl+C to stop the service
echo.

python windows_compatible_sdk_service.py

echo.
echo Service stopped.
pause
