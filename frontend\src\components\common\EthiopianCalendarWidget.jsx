import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  IconButton,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Alert,
  Tooltip,
  ToggleButton,
  ToggleButtonGroup,
  Autocomplete,
  TextField
} from '@mui/material';
import {
  ChevronLeft,
  ChevronRight,
  Today as TodayIcon,
  CalendarToday as CalendarIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import {
  gregorianToEthiopian,
  ethiopianToGregorian,
  formatEthiopianDate,
  getCurrentEthiopianDate,
  getDaysInEthiopianMonth,
  ETHIOPIAN_MONTHS,
  ETHIOPIAN_MONTHS_EN
} from '../../utils/ethiopianCalendar';

/**
 * Self-contained Ethiopian Calendar Widget
 * 
 * This component provides a true Ethiopian calendar interface without external dependencies:
 * - Users see Ethiopian years (2017 = current)
 * - Users see Ethiopian months (Meskerem, Tikimt, etc.)
 * - Users see Ethiopian days (1-30 for regular months)
 * - System receives Gregorian Date objects
 * - No external package dependencies
 */
const EthiopianCalendarWidgetV2 = ({
  label = "Select Ethiopian Date",
  value, // Gregorian Date object
  onChange, // Callback with Gregorian Date object
  error = false,
  helperText = "",
  required = false,
  disabled = false,
  language = 'en', // 'en' or 'am'
  showConversion = true,
  minYear = 2000,
  maxYear = 2030,
  disableFuture = false,
  placeholder = "Click to select Ethiopian date",
  showToggle = true, // Show GC/EC toggle like in the image
  ...otherProps
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [calendarType, setCalendarType] = useState('EC'); // 'EC' for Ethiopian, 'GC' for Gregorian
  const [viewDate, setViewDate] = useState(() => {
    if (value && value instanceof Date && !isNaN(value.getTime())) {
      return gregorianToEthiopian(value);
    }
    // Default to current Ethiopian date (current year)
    const currentEth = getCurrentEthiopianDate();
    return currentEth;
  });
  const [selectedEthiopian, setSelectedEthiopian] = useState(null);
  const [currentEthiopian] = useState(() => getCurrentEthiopianDate());

  // Debug log to verify enhanced component is loading
  console.log('🔥 EthiopianCalendarWidgetV2 ENHANCED LOADED 🔥 - showToggle:', showToggle, 'calendarType:', calendarType);
  console.log('🎯 Component props:', { label, showToggle, showConversion, calendarType });

  // Update selected Ethiopian date when value changes
  useEffect(() => {
    if (value && value instanceof Date && !isNaN(value.getTime())) {
      const ethiopianDate = gregorianToEthiopian(value);
      setSelectedEthiopian(ethiopianDate);
    } else {
      setSelectedEthiopian(null);
    }
  }, [value]);

  const monthNames = language === 'am' ? ETHIOPIAN_MONTHS : ETHIOPIAN_MONTHS_EN;
  const gregorianMonths = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const daysInMonth = calendarType === 'EC'
    ? getDaysInEthiopianMonth(viewDate.month, viewDate.year)
    : new Date(viewDate.year, viewDate.month, 0).getDate();

  const handleCalendarTypeChange = (event, newType) => {
    if (newType !== null) {
      setCalendarType(newType);
      // Update view date based on calendar type - always default to current year
      if (newType === 'EC') {
        const currentEth = getCurrentEthiopianDate();
        setViewDate(value ? gregorianToEthiopian(value) : {
          year: currentEth.year, // Always use current Ethiopian year
          month: currentEth.month,
          day: currentEth.day
        });
      } else {
        const now = new Date();
        setViewDate(value ? {
          year: value.getFullYear(),
          month: value.getMonth() + 1,
          day: value.getDate()
        } : {
          year: now.getFullYear(), // Always use current Gregorian year
          month: now.getMonth() + 1,
          day: now.getDate()
        });
      }
    }
  };

  const handleDateSelect = (day) => {
    let gregorianDate;

    if (calendarType === 'EC') {
      const ethiopianDate = { year: viewDate.year, month: viewDate.month, day };
      gregorianDate = ethiopianToGregorian(ethiopianDate.year, ethiopianDate.month, ethiopianDate.day);
      setSelectedEthiopian(ethiopianDate);
    } else {
      gregorianDate = new Date(viewDate.year, viewDate.month - 1, day);
      const ethiopianEquivalent = gregorianToEthiopian(gregorianDate);
      setSelectedEthiopian(ethiopianEquivalent);
    }

    if (gregorianDate) {
      // Check if future dates are disabled
      if (disableFuture && gregorianDate > new Date()) {
        return; // Don't select future dates if disabled
      }

      setIsOpen(false);

      if (onChange) {
        onChange(gregorianDate);
      }

      console.log('Calendar Selection:', {
        calendarType,
        userSelected: calendarType === 'EC' ? { year: viewDate.year, month: viewDate.month, day } : `${viewDate.year}-${viewDate.month}-${day}`,
        systemReceives: gregorianDate.toISOString(),
        currentEthiopianYear: currentEthiopian.year
      });
    }
  };

  const navigateMonth = (direction) => {
    let newMonth = viewDate.month + direction;
    let newYear = viewDate.year;

    if (calendarType === 'EC') {
      if (newMonth > 13) {
        newMonth = 1;
        newYear++;
      } else if (newMonth < 1) {
        newMonth = 13;
        newYear--;
      }
    } else {
      if (newMonth > 12) {
        newMonth = 1;
        newYear++;
      } else if (newMonth < 1) {
        newMonth = 12;
        newYear--;
      }
    }

    setViewDate({ ...viewDate, month: newMonth, year: newYear });
  };

  const navigateYear = (direction) => {
    const newYear = viewDate.year + direction;
    if (newYear >= minYear && newYear <= maxYear) {
      setViewDate({ ...viewDate, year: newYear });
    }
  };

  // Handle year selection
  const handleYearChange = (event, newValue) => {
    if (newValue) {
      const newYear = typeof newValue === 'object' ? newValue.value : newValue;
      setViewDate({ ...viewDate, year: newYear });
    }
  };

  // Handle month selection
  const handleMonthChange = (event, newValue) => {
    if (newValue) {
      const newMonth = typeof newValue === 'object' ? newValue.value : newValue;
      setViewDate({ ...viewDate, month: newMonth });
    }
  };

  // Generate year options with unlimited past years
  const getYearOptions = () => {
    const currentYear = calendarType === 'EC' ? getCurrentEthiopianDate().year : new Date().getFullYear();
    const years = [];

    // Generate years from 1900 to current year (unlimited past years)
    const startYear = calendarType === 'EC' ? 1900 : 1900; // Ethiopian year 1900 = Gregorian ~1907

    for (let year = startYear; year <= currentYear; year++) {
      years.push({
        value: year,
        label: year.toString()
      });
    }

    // Reverse so current year appears first/at top
    return years.reverse();
  };

  // Generate month options
  const getMonthOptions = () => {
    if (calendarType === 'EC') {
      return monthNames.map((name, index) => ({
        value: index + 1,
        label: name
      }));
    } else {
      return gregorianMonths.map((name, index) => ({
        value: index + 1,
        label: name
      }));
    }
  };

  // Get current year for default selection
  const getCurrentYear = () => {
    return calendarType === 'EC' ? getCurrentEthiopianDate().year : new Date().getFullYear();
  };

  const setToday = () => {
    const today = new Date();
    const ethiopianToday = getCurrentEthiopianDate();
    setSelectedEthiopian(ethiopianToday);

    if (calendarType === 'EC') {
      setViewDate(ethiopianToday);
    } else {
      setViewDate({
        year: today.getFullYear(),
        month: today.getMonth() + 1,
        day: today.getDate()
      });
    }

    setIsOpen(false);

    if (onChange) {
      onChange(today);
    }
  };

  const isToday = (day) => {
    if (calendarType === 'EC') {
      return currentEthiopian.year === viewDate.year &&
             currentEthiopian.month === viewDate.month &&
             currentEthiopian.day === day;
    } else {
      const today = new Date();
      return today.getFullYear() === viewDate.year &&
             today.getMonth() + 1 === viewDate.month &&
             today.getDate() === day;
    }
  };

  const isSelected = (day) => {
    if (calendarType === 'EC') {
      return selectedEthiopian &&
             selectedEthiopian.year === viewDate.year &&
             selectedEthiopian.month === viewDate.month &&
             selectedEthiopian.day === day;
    } else {
      if (!value) return false;
      return value.getFullYear() === viewDate.year &&
             value.getMonth() + 1 === viewDate.month &&
             value.getDate() === day;
    }
  };

  const isFutureDate = (day) => {
    if (!disableFuture) return false;

    let testDate;
    if (calendarType === 'EC') {
      testDate = ethiopianToGregorian(viewDate.year, viewDate.month, day);
    } else {
      testDate = new Date(viewDate.year, viewDate.month - 1, day);
    }

    return testDate && testDate > new Date();
  };

  const getDisplayValue = () => {
    if (value && value instanceof Date && !isNaN(value.getTime())) {
      if (calendarType === 'EC') {
        const ethiopianDate = gregorianToEthiopian(value);
        return formatEthiopianDate(ethiopianDate, 'DD MMM YYYY', language);
      } else {
        return value.toLocaleDateString('en-US');
      }
    }
    return placeholder;
  };

  const getConversionInfo = () => {
    if (!selectedEthiopian) return null;
    
    const gregorianDate = ethiopianToGregorian(selectedEthiopian.year, selectedEthiopian.month, selectedEthiopian.day);
    if (!gregorianDate) return null;

    return {
      ethiopian: formatEthiopianDate(selectedEthiopian, 'DD MMM YYYY', language),
      gregorian: gregorianDate.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    };
  };

  const conversionInfo = getConversionInfo();

  return (
    <Box>
      {/* Label */}
      <Typography variant="subtitle2" gutterBottom>
        {label}
        {required && <span style={{ color: 'red' }}> *</span>}
        <Tooltip title="Ethiopian Calendar - Select dates using Ethiopian calendar system (Year 2017 = Current)">
          <IconButton size="small" sx={{ ml: 1 }}>
            <InfoIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Typography>

      {/* Current Ethiopian Date Info - Compact */}
      {!label && (
        <Alert severity="info" sx={{ mb: 1, py: 0.5 }}>
          <Typography variant="caption">
            Today: <strong>{formatEthiopianDate(currentEthiopian, 'DD MMM YYYY', language)}</strong>
            (Year {currentEthiopian.year})
          </Typography>
        </Alert>
      )}

      {/* Date Input Display - Only show if has label */}
      {label && (
        <Paper
          variant="outlined"
          sx={{
            p: 2,
            cursor: disabled ? 'default' : 'pointer',
            bgcolor: error ? 'error.light' : 'background.paper',
            border: error ? '1px solid red' : '1px solid #ddd',
            '&:hover': disabled ? {} : { bgcolor: 'action.hover' }
          }}
          onClick={() => !disabled && setIsOpen(!isOpen)}
        >
          <Typography variant="body1" color={selectedEthiopian ? 'text.primary' : 'text.secondary'}>
            {getDisplayValue()}
          </Typography>
        </Paper>
      )}

      {/* Calendar Popup */}
      {(isOpen || !label) && !disabled && (
        <Paper sx={{ mt: label ? 1 : 0, p: 1.5, border: label ? '1px solid #ddd' : 'none', minWidth: 300, maxWidth: 350 }}>
          {/* Calendar Type Toggle - Like in your image */}
          {showToggle && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
              <ToggleButtonGroup
                value={calendarType}
                exclusive
                onChange={handleCalendarTypeChange}
                size="small"
              >
                <ToggleButton value="EC" sx={{ px: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <Box
                      sx={{
                        width: 8,
                        height: 8,
                        borderRadius: '50%',
                        bgcolor: calendarType === 'EC' ? 'success.main' : 'grey.400'
                      }}
                    />
                    EC
                  </Box>
                </ToggleButton>
                <ToggleButton value="GC" sx={{ px: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <Box
                      sx={{
                        width: 8,
                        height: 8,
                        borderRadius: '50%',
                        bgcolor: calendarType === 'GC' ? 'success.main' : 'grey.400'
                      }}
                    />
                    GC
                  </Box>
                </ToggleButton>
              </ToggleButtonGroup>
            </Box>
          )}

          {/* Calendar Header with Searchable Autocomplete Dropdowns */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, gap: 1 }}>
            {/* Searchable Year Selector */}
            <Autocomplete
              size="small"
              sx={{ minWidth: 100 }}
              options={getYearOptions()}
              value={getYearOptions().find(option => option.value === viewDate.year) || null}
              onChange={handleYearChange}
              getOptionLabel={(option) => option.label}
              isOptionEqualToValue={(option, value) => option.value === value.value}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Year"
                  placeholder="Search year..."
                />
              )}
              ListboxProps={{
                style: {
                  maxHeight: 200,
                },
              }}
              filterOptions={(options, { inputValue }) => {
                // Custom filter to search years
                return options.filter(option =>
                  option.label.includes(inputValue)
                );
              }}
            />

            {/* Searchable Month Selector */}
            <Autocomplete
              size="small"
              sx={{ minWidth: 140 }}
              options={getMonthOptions()}
              value={getMonthOptions().find(option => option.value === viewDate.month) || null}
              onChange={handleMonthChange}
              getOptionLabel={(option) => option.label}
              isOptionEqualToValue={(option, value) => option.value === value.value}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Month"
                  placeholder="Search month..."
                />
              )}
              ListboxProps={{
                style: {
                  maxHeight: 200,
                },
              }}
              filterOptions={(options, { inputValue }) => {
                // Custom filter to search months
                return options.filter(option =>
                  option.label.toLowerCase().includes(inputValue.toLowerCase())
                );
              }}
            />

            {/* Navigation Arrows */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <IconButton size="small" onClick={() => navigateMonth(-1)}>
                <ChevronLeft />
              </IconButton>
              <IconButton size="small" onClick={() => navigateMonth(1)}>
                <ChevronRight />
              </IconButton>
            </Box>
          </Box>

          {/* Weekday Headers for Gregorian Calendar */}
          {calendarType === 'GC' && (
            <Grid container sx={{ mb: 1 }}>
              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                <Grid item xs={12/7} key={day}>
                  <Typography
                    variant="caption"
                    sx={{
                      display: 'block',
                      textAlign: 'center',
                      fontWeight: 'bold',
                      color: 'text.secondary',
                      py: 0.5
                    }}
                  >
                    {day}
                  </Typography>
                </Grid>
              ))}
            </Grid>
          )}

          {/* Calendar Grid */}
          <Grid container spacing={0.5} sx={{ mb: 1 }}>
            {Array.from({ length: daysInMonth }, (_, i) => i + 1).map(day => (
              <Grid item xs={12/7} key={day}>
                <Box
                  onClick={() => !isFutureDate(day) && handleDateSelect(day)}
                  sx={{
                    p: 0.5,
                    textAlign: 'center',
                    cursor: isFutureDate(day) ? 'not-allowed' : 'pointer',
                    borderRadius: 1,
                    minHeight: 32,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    bgcolor: isSelected(day) ? 'primary.main' : 
                            isToday(day) ? 'primary.light' : 'transparent',
                    color: isSelected(day) ? 'primary.contrastText' :
                           isToday(day) ? 'primary.contrastText' : 
                           isFutureDate(day) ? 'text.disabled' : 'text.primary',
                    opacity: isFutureDate(day) ? 0.5 : 1,
                    '&:hover': isFutureDate(day) ? {} : {
                      bgcolor: isSelected(day) ? 'primary.dark' : 'action.hover'
                    }
                  }}
                >
                  {day}
                </Box>
              </Grid>
            ))}
          </Grid>

          {/* Calendar Footer */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Button
              size="small"
              startIcon={<TodayIcon />}
              onClick={setToday}
              variant="outlined"
            >
              Today
            </Button>
            
            <Button
              size="small"
              onClick={() => setIsOpen(false)}
              variant="text"
            >
              Close
            </Button>
          </Box>
        </Paper>
      )}

      {/* Conversion Display */}
      {showConversion && conversionInfo && (
        <Box sx={{ mt: 2 }}>
          <Typography variant="caption" color="text.secondary" gutterBottom>
            Selected Date Conversion:
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, mt: 1, flexWrap: 'wrap' }}>
            <Chip
              icon={<CalendarIcon />}
              label={`Ethiopian: ${conversionInfo.ethiopian}`}
              color="primary"
              size="small"
            />
            <Chip
              icon={<CalendarIcon />}
              label={`Gregorian: ${conversionInfo.gregorian}`}
              color="secondary"
              size="small"
            />
          </Box>
        </Box>
      )}

      {/* Helper Text */}
      {(helperText || error) && (
        <Typography 
          variant="caption" 
          color={error ? 'error' : 'text.secondary'}
          sx={{ mt: 1, display: 'block' }}
        >
          {helperText}
        </Typography>
      )}

      {/* Usage Instructions - Only show if has label */}
      {label && (
        <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
          💡 {showToggle ? 'Toggle between Ethiopian (EC) and Gregorian (GC) calendars.' : 'Ethiopian calendar interface.'}
          {calendarType === 'EC' && ` Year ${currentEthiopian.year} = Current year.`}
          System automatically converts to Gregorian for storage.
        </Typography>
      )}
    </Box>
  );
};

export default EthiopianCalendarWidgetV2;
export { EthiopianCalendarWidgetV2 as EthiopianCalendarWidget };
