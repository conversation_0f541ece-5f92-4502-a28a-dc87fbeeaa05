import { useNavigate } from 'react-router-dom';
import {
  AppBar,
  Box,
  Toolbar,
  IconButton,
  Typography,
  Container,
  Button,
} from '@mui/material';
import {
  Brightness4 as DarkModeIcon,
  Brightness7 as LightModeIcon,
} from '@mui/icons-material';
import { useTheme as useAppTheme } from '../../contexts/ThemeContext';

const LandingNavbar = () => {
  const navigate = useNavigate();
  const { mode, toggleTheme } = useAppTheme();

  return (
    <>
      <AppBar position="fixed" sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }}>
        <Container>
          <Toolbar disableGutters>
            <Typography
              variant="h6"
              component="div"
              sx={{
                mr: 2,
                fontWeight: 'bold',
                color: 'inherit',
                cursor: 'pointer',
              }}
              onClick={() => navigate('/')}
            >
              GoID
            </Typography>

            {/* Spacer to push items to the right */}
            <Box sx={{ flexGrow: 1 }} />

            {/* Theme toggle */}
            <IconButton
              color="inherit"
              onClick={toggleTheme}
              sx={{ mr: 1 }}
              aria-label="toggle theme"
            >
              {mode === 'light' ? <DarkModeIcon /> : <LightModeIcon />}
            </IconButton>

            {/* Sign In button */}
            <Button
              variant="outlined"
              color="inherit"
              sx={{
                borderColor: 'white',
                '&:hover': {
                  borderColor: 'white',
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                },
              }}
              onClick={() => navigate('/login')}
            >
              Sign In
            </Button>
          </Toolbar>
        </Container>
      </AppBar>


    </>
  );
};

export default LandingNavbar;
