# Flexible Workflow System Guide

## 🎯 **Overview**

The GoID system now supports **flexible workflow configurations** that allow different tenants to operate according to their specific organizational needs and capabilities. This addresses the key scenarios you mentioned:

1. **Autonomous Kebeles**: Self-sufficient kebeles that handle everything from registration to printing
2. **Custom Permission Assignment**: Create new users/groups with specific permissions
3. **Flexible Tenant Creation**: Use groups instead of hardcoded roles
4. **Adaptable Workflows**: System adapts to different organizational structures

## 🔧 **Workflow Types**

### **1. 🏠 Autonomous Workflow**
**Perfect for**: Self-sufficient kebeles with local printing capabilities

**Characteristics**:
- ✅ **Local ID Card Printing**: Kebele can print ID cards locally
- ✅ **Minimal External Approval**: Only kebele leader approval required
- ✅ **Complete Local Control**: Full workflow handled within kebele
- ✅ **Fast Processing**: No waiting for higher-level approvals

**Use Case**: Rural kebeles or well-equipped urban kebeles that want complete control

### **2. 🏛️ Centralized Workflow**
**Perfect for**: Standard hierarchical organizations

**Characteristics**:
- ❌ **No Local Printing**: ID cards printed at subcity level
- ✅ **Hierarchical Approval**: Multiple approval levels required
- ✅ **Quality Control**: Centralized oversight and standards
- ✅ **Resource Sharing**: Shared printing infrastructure

**Use Case**: Standard government hierarchy with centralized resources

### **3. 🔄 Hybrid Workflow**
**Perfect for**: Balanced approach with local capabilities and oversight

**Characteristics**:
- ✅ **Local Printing**: Kebele can print after approval
- ✅ **Quality Oversight**: Higher-level monitoring
- ✅ **Flexible Approval**: Configurable approval levels
- ✅ **Best of Both**: Local efficiency with quality control

**Use Case**: Progressive kebeles with good infrastructure but under oversight

### **4. ⚙️ Custom Workflow**
**Perfect for**: Special cases and unique requirements

**Characteristics**:
- 🔧 **Fully Configurable**: All aspects customizable
- 🔧 **Special Permissions**: Custom permission combinations
- 🔧 **Unique Approval Flows**: Non-standard approval processes
- 🔧 **Organizational Specific**: Tailored to specific needs

**Use Case**: Special administrative units, pilot programs, emergency situations

## 👥 **Custom Group Creation**

### **Creating Groups with Specific Permissions**

#### **Example 1: Autonomous Kebele Clerk with Printing**
```bash
python manage.py create_custom_group \
    --group-name "autonomous_clerk" \
    --description "Clerk with local ID card printing capabilities" \
    --base-group "clerk" \
    --permissions "print_id_cards" \
    --group-type "operational" \
    --level 15
```

#### **Example 2: Document Specialist**
```bash
python manage.py create_custom_group \
    --group-name "document_specialist" \
    --description "Specialized role for document verification" \
    --permissions "verify_documents" "view_citizens_list" "view_citizen_details" \
    --group-type "functional" \
    --level 20
```

#### **Example 3: Printing Officer**
```bash
python manage.py create_custom_group \
    --group-name "printing_officer" \
    --description "Officer responsible for ID card printing" \
    --permissions "print_id_cards" "view_id_cards_list" "approve_id_cards" \
    --tenant "kebele15" \
    --group-type "functional" \
    --level 30
```

### **Available Permissions for Custom Groups**

#### **Citizen Management**:
- `register_citizens` - Complete citizen registration workflow
- `view_citizens_list` - View list of citizens
- `view_citizen_details` - View detailed citizen information

#### **ID Card Management**:
- `generate_id_cards` - Generate ID cards
- `view_id_cards_list` - View ID card lists
- `approve_id_cards` - Approve ID cards
- `print_id_cards` - **Print ID cards locally** 🎯
- `send_id_cards_for_approval` - Send for approval
- `send_id_cards_to_higher_level` - Send to higher level

#### **Administrative**:
- `verify_documents` - Verify citizen documents
- `create_kebele_users` - Create kebele users
- `view_kebele_reports` - View reports
- `view_kebele_dashboard` - View dashboard

## 🏗️ **Tenant Creation with Groups**

### **Updated Tenant Creation Process**

The system now creates tenants with **group-based permissions** instead of hardcoded roles:

```python
# Old way (hardcoded roles)
user = User.objects.create_user(
    username='clerk',
    role='clerk',  # Hardcoded role
    tenant=kebele
)

# New way (group-based)
user = User.objects.create_user(
    username='clerk',
    role='clerk',  # Keep for backward compatibility
    tenant=kebele
)
# Assign to appropriate group
user.groups.add(Group.objects.get(name='clerk'))
```

### **Default Groups Created for New Tenants**

#### **For Kebele Tenants**:
- **clerk** - Basic operational permissions
- **kebele_leader** - Approval and verification permissions
- **autonomous_clerk** - (Optional) Clerk with printing permissions
- **autonomous_leader** - (Optional) Leader with full local authority

#### **For Subcity Tenants**:
- **subcity_admin** - Cross-kebele management permissions
- **printing_officer** - (Optional) Specialized printing role

#### **For City Tenants**:
- **city_admin** - Cross-subcity management permissions

## 🔧 **Configuring Tenant Workflows**

### **Configure Autonomous Kebele**
```bash
python manage.py configure_tenant_workflow \
    --tenant "Bole 01" \
    --workflow-type autonomous \
    --template autonomous_kebele \
    --enable-local-printing \
    --printing-authority kebele \
    --approval-levels kebele_leader \
    --create-custom-groups
```

**Result**: Kebele can handle complete workflow including printing

### **Configure Centralized Workflow**
```bash
python manage.py configure_tenant_workflow \
    --tenant "kebele_bole_02" \
    --workflow-type centralized \
    --template centralized_hierarchy \
    --printing-authority subcity \
    --approval-levels kebele_leader subcity_admin
```

**Result**: Standard hierarchy with subcity printing

### **Configure Hybrid Workflow**
```bash
python manage.py configure_tenant_workflow \
    --tenant "kebele_addis_ketema_01" \
    --workflow-type hybrid \
    --template hybrid_workflow \
    --enable-local-printing \
    --printing-authority kebele \
    --approval-levels kebele_leader \
    --create-custom-groups
```

**Result**: Local printing with oversight

## 📊 **Workflow Configuration Examples**

### **Scenario 1: Rural Kebele (Autonomous)**
```json
{
  "workflow_type": "autonomous",
  "id_card_processing": {
    "can_print_locally": true,
    "requires_higher_approval": false,
    "approval_levels": ["kebele_leader"],
    "printing_authority": "kebele",
    "quality_control": "local"
  },
  "permissions": {
    "clerk": ["register_citizens", "generate_id_cards", "print_id_cards"],
    "kebele_leader": ["approve_id_cards", "print_id_cards", "verify_documents"]
  }
}
```

### **Scenario 2: Urban Kebele (Centralized)**
```json
{
  "workflow_type": "centralized",
  "id_card_processing": {
    "can_print_locally": false,
    "requires_higher_approval": true,
    "approval_levels": ["kebele_leader", "subcity_admin"],
    "printing_authority": "subcity",
    "quality_control": "centralized"
  },
  "permissions": {
    "clerk": ["register_citizens", "generate_id_cards"],
    "kebele_leader": ["approve_id_cards", "send_id_cards_to_higher_level"],
    "subcity_admin": ["approve_id_cards", "print_id_cards"]
  }
}
```

### **Scenario 3: Progressive Kebele (Hybrid)**
```json
{
  "workflow_type": "hybrid",
  "id_card_processing": {
    "can_print_locally": true,
    "requires_higher_approval": true,
    "approval_levels": ["kebele_leader"],
    "printing_authority": "kebele",
    "quality_control": "hybrid"
  },
  "permissions": {
    "clerk": ["register_citizens", "generate_id_cards"],
    "kebele_leader": ["approve_id_cards", "print_id_cards", "verify_documents"]
  }
}
```

## 🎯 **Addressing Your Specific Questions**

### **Q1: Kebele wants to handle everything including printing?**
**Solution**: Configure as **Autonomous Workflow**
```bash
python manage.py configure_tenant_workflow \
    --tenant "your_kebele" \
    --workflow-type autonomous \
    --enable-local-printing \
    --create-custom-groups
```

### **Q2: Want to create new user/group with printing privilege?**
**Solution**: Create **Custom Group** with printing permissions
```bash
python manage.py create_custom_group \
    --group-name "printing_specialist" \
    --permissions "print_id_cards" "view_id_cards_list" "approve_id_cards" \
    --tenant "your_tenant"
```

### **Q3: Tenant creation now uses groups instead of roles?**
**Solution**: **Updated tenant creation** automatically assigns users to appropriate groups while maintaining backward compatibility with roles.

## 🚀 **Implementation Steps**

### **Step 1: Set Up General Permissions**
```bash
python manage.py setup_general_permissions
```

### **Step 2: Configure Tenant Workflow**
```bash
python manage.py configure_tenant_workflow \
    --tenant "your_tenant" \
    --workflow-type autonomous \
    --enable-local-printing \
    --create-custom-groups
```

### **Step 3: Create Custom Groups (if needed)**
```bash
python manage.py create_custom_group \
    --group-name "custom_role" \
    --permissions "permission1" "permission2" \
    --tenant "your_tenant"
```

### **Step 4: Assign Users to Groups**
```python
# In Django shell or management command
user.groups.add(Group.objects.get(name='autonomous_clerk'))
```

## 📈 **Benefits**

### **✅ Flexibility**
- **Adaptable Workflows**: Each tenant can operate according to their capabilities
- **Custom Permissions**: Create exactly the permissions you need
- **Scalable System**: Grows with organizational changes

### **✅ Efficiency**
- **Local Processing**: Autonomous kebeles process faster
- **Reduced Bottlenecks**: No waiting for higher-level approvals when not needed
- **Resource Optimization**: Use local resources effectively

### **✅ Control**
- **Quality Assurance**: Maintain standards through configurable oversight
- **Audit Trail**: Track all workflow configurations and changes
- **Security**: Granular permission control

---

**The flexible workflow system provides the perfect solution for organizations with diverse operational needs while maintaining security and quality standards!** 🎉
