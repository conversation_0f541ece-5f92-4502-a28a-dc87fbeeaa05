/**
 * PhotoProcessor Component
 * Provides photo upload, background removal, and enhancement functionality
 */

import React, { useState, useRef } from 'react';
import {
  Box,
  Button,
  Typography,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Switch,
  FormGroup,
  Divider,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  CloudUpload,
  PhotoCamera,
  AutoFixHigh,
  Download,
  Refresh,
  Visibility,
  VisibilityOff
} from '@mui/icons-material';
import photoProcessingService from '../../services/photoProcessingService';

const PhotoProcessor = ({
  onPhotoProcessed,
  initialPhoto = null,
  style = 'professional',
  showStyleOptions = true,
  showProcessingOptions = true,
  maxWidth = 400,
  maxHeight = 500
}) => {
  const [originalPhoto, setOriginalPhoto] = useState(initialPhoto);
  const [processedPhoto, setProcessedPhoto] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [showOriginal, setShowOriginal] = useState(false);
  
  // Processing options
  const [selectedStyle, setSelectedStyle] = useState(style);
  const [removeBackground, setRemoveBackground] = useState(true);
  const [enhance, setEnhance] = useState(true);
  
  const fileInputRef = useRef(null);

  const handleFileSelect = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    setError(null);
    setSuccess(null);

    try {
      // Validate file
      const validation = photoProcessingService.validateImageFile(file);
      if (!validation.valid) {
        setError(validation.error);
        return;
      }

      // Create preview
      const preview = await photoProcessingService.createPreview(file);
      setOriginalPhoto(preview);
      setProcessedPhoto(null);
      
      // Auto-process if enabled
      if (removeBackground || enhance) {
        await processPhoto(preview);
      }
    } catch (err) {
      setError(err.message);
    }
  };

  const processPhoto = async (photoData = originalPhoto) => {
    if (!photoData) {
      setError('Please select a photo first');
      return;
    }

    setIsProcessing(true);
    setError(null);
    setSuccess(null);

    try {
      const result = await photoProcessingService.processPhoto(photoData, {
        removeBackground,
        enhance,
        style: selectedStyle
      });

      if (result.success) {
        setProcessedPhoto(result.processed_image);
        setSuccess('Photo processed successfully!');
        
        // Notify parent component
        if (onPhotoProcessed) {
          onPhotoProcessed({
            original: photoData,
            processed: result.processed_image,
            metadata: result.metadata
          });
        }
      } else {
        setError(result.error || 'Processing failed');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRemoveBackgroundOnly = async () => {
    if (!originalPhoto) {
      setError('Please select a photo first');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      const result = await photoProcessingService.removeBackground(originalPhoto);
      
      if (result.success) {
        setProcessedPhoto(result.processed_image);
        setSuccess('Background removed successfully!');
        
        if (onPhotoProcessed) {
          onPhotoProcessed({
            original: originalPhoto,
            processed: result.processed_image,
            metadata: { background_removed: true, enhanced: false }
          });
        }
      } else {
        setError('Background removal failed');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setIsProcessing(false);
    }
  };

  const downloadProcessedPhoto = () => {
    if (!processedPhoto) return;

    const link = document.createElement('a');
    link.href = processedPhoto;
    link.download = `processed-photo-${Date.now()}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const resetPhoto = () => {
    setOriginalPhoto(null);
    setProcessedPhoto(null);
    setError(null);
    setSuccess(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <Card sx={{ maxWidth: 600, mx: 'auto' }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Photo Processor
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Upload a photo and enhance it for ID card use with background removal and professional styling.
        </Typography>

        {/* File Upload */}
        <Box sx={{ mb: 3 }}>
          <input
            type="file"
            accept="image/*"
            onChange={handleFileSelect}
            ref={fileInputRef}
            style={{ display: 'none' }}
          />
          <Button
            variant="outlined"
            startIcon={<CloudUpload />}
            onClick={() => fileInputRef.current?.click()}
            fullWidth
            sx={{ mb: 1 }}
          >
            Select Photo
          </Button>
          <Typography variant="caption" color="text.secondary">
            Supported formats: JPEG, PNG, WebP (max 10MB)
          </Typography>
        </Box>

        {/* Processing Options */}
        {showProcessingOptions && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom>
              Processing Options
            </Typography>
            <FormGroup>
              <FormControlLabel
                control={
                  <Switch
                    checked={removeBackground}
                    onChange={(e) => setRemoveBackground(e.target.checked)}
                  />
                }
                label="Remove Background"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={enhance}
                    onChange={(e) => setEnhance(e.target.checked)}
                  />
                }
                label="Enhance Quality"
              />
            </FormGroup>
          </Box>
        )}

        {/* Style Options */}
        {showStyleOptions && (
          <Box sx={{ mb: 3 }}>
            <FormControl component="fieldset">
              <FormLabel component="legend">Photo Style</FormLabel>
              <RadioGroup
                value={selectedStyle}
                onChange={(e) => setSelectedStyle(e.target.value)}
                row
              >
                <FormControlLabel
                  value="professional"
                  control={<Radio />}
                  label="Professional"
                />
                <FormControlLabel
                  value="passport"
                  control={<Radio />}
                  label="Passport"
                />
                <FormControlLabel
                  value="license"
                  control={<Radio />}
                  label="License"
                />
              </RadioGroup>
            </FormControl>
          </Box>
        )}

        {/* Action Buttons */}
        <Box sx={{ mb: 3, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          <Button
            variant="contained"
            startIcon={<AutoFixHigh />}
            onClick={() => processPhoto()}
            disabled={!originalPhoto || isProcessing}
          >
            Process Photo
          </Button>
          <Button
            variant="outlined"
            onClick={handleRemoveBackgroundOnly}
            disabled={!originalPhoto || isProcessing}
          >
            Remove Background Only
          </Button>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={resetPhoto}
            disabled={isProcessing}
          >
            Reset
          </Button>
        </Box>

        {/* Status Messages */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {success}
          </Alert>
        )}

        {/* Loading */}
        {isProcessing && (
          <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
            <CircularProgress />
            <Typography sx={{ ml: 2 }}>Processing photo...</Typography>
          </Box>
        )}

        {/* Photo Preview */}
        {(originalPhoto || processedPhoto) && (
          <Box>
            <Divider sx={{ my: 2 }} />
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="subtitle2">
                Preview
              </Typography>
              <Box>
                {originalPhoto && processedPhoto && (
                  <Tooltip title={showOriginal ? "Show Processed" : "Show Original"}>
                    <IconButton
                      onClick={() => setShowOriginal(!showOriginal)}
                      size="small"
                    >
                      {showOriginal ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </Tooltip>
                )}
                {processedPhoto && (
                  <Tooltip title="Download Processed Photo">
                    <IconButton
                      onClick={downloadProcessedPhoto}
                      size="small"
                    >
                      <Download />
                    </IconButton>
                  </Tooltip>
                )}
              </Box>
            </Box>
            
            <Box sx={{ textAlign: 'center' }}>
              <img
                src={showOriginal ? originalPhoto : (processedPhoto || originalPhoto)}
                alt={showOriginal ? "Original" : "Processed"}
                style={{
                  maxWidth: '100%',
                  maxHeight: '400px',
                  objectFit: 'contain',
                  border: '1px solid #ddd',
                  borderRadius: '8px'
                }}
              />
              <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                {showOriginal ? "Original Photo" : processedPhoto ? "Processed Photo" : "Original Photo"}
              </Typography>
            </Box>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default PhotoProcessor;
