@echo off
echo ========================================
echo   FUNCTION FIX DIAGNOSTIC TOOLS
echo   Analyze and Fix Specific SDK Functions
echo ========================================
echo.

echo Checking Python installation...
python --version
if errorlevel 1 (
    echo ERROR: Python not found. Please install Python 3.7+
    pause
    exit /b 1
)

echo.
echo Checking required DLL files...
if not exist "ftrScanAPI.dll" (
    echo ERROR: ftrScanAPI.dll not found
    echo Please ensure Futronic SDK files are in the current directory
    pause
    exit /b 1
)

echo SUCCESS: ftrScanAPI.dll found
echo.

echo ========================================
echo   STEP 1: Fix Finger Detection Function
echo ========================================
echo.
echo This will test different function signatures for ftrScanIsFingerPresent
echo to find the correct one that doesn't crash.
echo.
pause

python fix_finger_detection.py

echo.
echo ========================================
echo   STEP 2: Fix Frame Capture Function  
echo ========================================
echo.
echo This will test different approaches for ftrScanGetFrame
echo to find the most reliable capture method.
echo.
echo IMPORTANT: Place your finger on the scanner when prompted
echo for frame capture tests.
echo.
pause

python fix_frame_capture.py

echo.
echo ========================================
echo   ANALYSIS COMPLETE
echo ========================================
echo.
echo Review the output above to identify:
echo 1. Which finger detection signature works
echo 2. Which frame capture approach is most reliable
echo 3. Any alternative functions that can be used
echo.
echo This information will be used to create a fixed service
echo that addresses the root causes instead of avoiding them.
echo.
pause
