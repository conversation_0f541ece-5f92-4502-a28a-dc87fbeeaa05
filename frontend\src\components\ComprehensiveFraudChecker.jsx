import React, { useState, useEffect } from 'react';
import {
  Box,
  Alert,
  AlertTitle,
  Typography,
  Chip,
  Button,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Paper,
  Grid
} from '@mui/material';
import {
  Security as SecurityIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  CheckCircle as CheckIcon,
  ExpandMore as ExpandMoreIcon,
  Person as PersonIcon,
  People as FamilyIcon,
  Photo as PhotoIcon,
  Refresh as RefreshIcon,
  Block as BlockIcon
} from '@mui/icons-material';
import axios from '../utils/axios';

const ComprehensiveFraudChecker = ({ 
  citizenData, 
  parentData = [],
  onFraudDetected, 
  onFraudCleared,
  tenantId,
  autoCheck = true,
  showDetails = true 
}) => {
  const [checking, setChecking] = useState(false);
  const [fraudResults, setFraudResults] = useState(null);
  const [lastCheckedData, setLastCheckedData] = useState(null);

  // Prepare comprehensive citizen data including parent information
  const prepareComprehensiveData = () => {
    if (!citizenData) return null;

    // Extract parent names
    let motherFullName = '';
    let fatherFullName = '';

    if (parentData && parentData.length > 0) {
      parentData.forEach(parent => {
        const fullName = `${parent.first_name || ''} ${parent.middle_name || ''} ${parent.last_name || ''}`.trim();
        if (parent.relationship === 'mother') {
          motherFullName = fullName;
        } else if (parent.relationship === 'father') {
          fatherFullName = fullName;
        }
      });
    }

    return {
      first_name: citizenData.first_name,
      middle_name: citizenData.middle_name,
      last_name: citizenData.last_name,
      date_of_birth: citizenData.date_of_birth,
      mother_full_name: motherFullName,
      father_full_name: fatherFullName,
      photo: citizenData.photo
    };
  };

  // Auto-check when data changes
  useEffect(() => {
    if (!autoCheck || !tenantId) return;

    const comprehensiveData = prepareComprehensiveData();
    if (!comprehensiveData) return;

    // Check if we have enough reliable data
    const hasBasicInfo = comprehensiveData.first_name && comprehensiveData.last_name;
    const hasAdditionalInfo = comprehensiveData.date_of_birth || 
                             comprehensiveData.mother_full_name || 
                             comprehensiveData.father_full_name ||
                             comprehensiveData.photo;
    
    const hasEnoughData = hasBasicInfo && hasAdditionalInfo;
    const dataChanged = JSON.stringify(comprehensiveData) !== JSON.stringify(lastCheckedData);

    if (hasEnoughData && dataChanged) {
      const timer = setTimeout(() => {
        checkFraud(comprehensiveData);
      }, 2000); // 2 second debounce for comprehensive check

      return () => clearTimeout(timer);
    }
  }, [citizenData, parentData, autoCheck, tenantId]);

  // Notify parent component when fraud status changes
  useEffect(() => {
    if (fraudResults) {
      if (fraudResults.fraud_detected) {
        onFraudDetected?.(fraudResults);
      } else {
        onFraudCleared?.(fraudResults);
      }
    }
  }, [fraudResults, onFraudDetected, onFraudCleared]);

  const checkFraud = async (dataToCheck = null) => {
    const comprehensiveData = dataToCheck || prepareComprehensiveData();
    if (!comprehensiveData || !tenantId) return;

    try {
      setChecking(true);
      setLastCheckedData({ ...comprehensiveData });

      const response = await axios.post(
        `/api/tenants/${tenantId}/citizens/check-registration-fraud/`,
        comprehensiveData
      );

      setFraudResults(response.data);
    } catch (error) {
      console.error('Comprehensive fraud check failed:', error);
      setFraudResults({
        fraud_check_completed: false,
        can_proceed: true,
        risk_level: 'unknown',
        error: 'Fraud detection temporarily unavailable'
      });
    } finally {
      setChecking(false);
    }
  };

  const getSeverityColor = (riskLevel) => {
    switch (riskLevel) {
      case 'critical':
        return 'error';
      case 'high':
        return 'warning';
      case 'medium':
        return 'info';
      case 'low':
        return 'success';
      default:
        return 'info';
    }
  };

  const getRiskIcon = (riskLevel) => {
    switch (riskLevel) {
      case 'critical':
        return <ErrorIcon />;
      case 'high':
        return <WarningIcon />;
      case 'medium':
        return <WarningIcon />;
      case 'low':
        return <CheckIcon />;
      default:
        return <SecurityIcon />;
    }
  };

  const getDataQualityIndicators = () => {
    const data = prepareComprehensiveData();
    if (!data) return [];

    const indicators = [];
    
    if (data.first_name && data.last_name) {
      indicators.push({ label: 'Full Name', icon: <PersonIcon />, available: true });
    }
    
    if (data.date_of_birth) {
      indicators.push({ label: 'Date of Birth', icon: <PersonIcon />, available: true });
    }
    
    if (data.mother_full_name) {
      indicators.push({ label: 'Mother\'s Name', icon: <FamilyIcon />, available: true });
    }
    
    if (data.father_full_name) {
      indicators.push({ label: 'Father\'s Name', icon: <FamilyIcon />, available: true });
    }
    
    if (data.photo) {
      indicators.push({ label: 'Photo', icon: <PhotoIcon />, available: true });
    }

    return indicators;
  };

  const comprehensiveData = prepareComprehensiveData();
  const dataQuality = getDataQualityIndicators();

  // Don't show if no basic data
  if (!comprehensiveData || !comprehensiveData.first_name) {
    return null;
  }

  // Show checking indicator
  if (checking) {
    return (
      <Paper sx={{ p: 3, mb: 2, border: '1px solid', borderColor: 'info.main' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
          <CircularProgress size={24} />
          <Typography variant="h6">
            🔍 Comprehensive Fraud Detection in Progress
          </Typography>
        </Box>
        <Typography variant="body2" color="text.secondary">
          Checking for duplicate registrations across all kebeles using name, family information, and photo data...
        </Typography>
        
        {/* Show data quality indicators */}
        <Box sx={{ mt: 2 }}>
          <Typography variant="subtitle2" sx={{ mb: 1 }}>Data Available for Verification:</Typography>
          <Grid container spacing={1}>
            {dataQuality.map((indicator, index) => (
              <Grid item key={index}>
                <Chip
                  icon={indicator.icon}
                  label={indicator.label}
                  size="small"
                  color="primary"
                  variant="outlined"
                />
              </Grid>
            ))}
          </Grid>
        </Box>
      </Paper>
    );
  }

  // Don't show anything if no results yet
  if (!fraudResults) {
    return (
      <Paper sx={{ p: 2, mb: 2, border: '1px dashed', borderColor: 'grey.300' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <SecurityIcon color="action" />
          <Box>
            <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
              Fraud Detection Ready
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {dataQuality.length > 2 ? 
                'Sufficient data available for comprehensive fraud detection' : 
                'Add parent information for more accurate fraud detection'
              }
            </Typography>
          </Box>
          <Button
            variant="outlined"
            size="small"
            startIcon={<RefreshIcon />}
            onClick={() => checkFraud()}
            sx={{ ml: 'auto' }}
          >
            Check Now
          </Button>
        </Box>
      </Paper>
    );
  }

  // Show comprehensive fraud detection results
  return (
    <Paper sx={{ 
      mb: 2, 
      border: '2px solid', 
      borderColor: `${getSeverityColor(fraudResults.risk_level)}.main`,
      backgroundColor: fraudResults.risk_level === 'critical' ? 'error.light' : 'background.paper'
    }}>
      <Alert 
        severity={getSeverityColor(fraudResults.risk_level)}
        icon={getRiskIcon(fraudResults.risk_level)}
        sx={{ 
          '& .MuiAlert-message': { width: '100%' },
          borderRadius: 0
        }}
      >
        <AlertTitle sx={{ fontWeight: 'bold', fontSize: '1.2rem' }}>
          🔍 Comprehensive Fraud Detection Results
        </AlertTitle>

        {/* Data quality summary */}
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" sx={{ mb: 1 }}>
            <strong>Verification Data Used:</strong> {dataQuality.map(d => d.label).join(', ')}
          </Typography>
        </Box>

        {/* Main warning messages */}
        {fraudResults.warnings && fraudResults.warnings.map((warning, index) => (
          <Box key={index} sx={{ mb: 2 }}>
            <Typography variant="body1" sx={{ fontWeight: 'bold', mb: 1 }}>
              {warning.message}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              <strong>Action Required:</strong> {warning.action_required}
            </Typography>
          </Box>
        ))}

        {/* Risk level and status */}
        <Box sx={{ mb: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          <Chip
            label={`Risk Level: ${fraudResults.risk_level?.toUpperCase() || 'UNKNOWN'}`}
            color={getSeverityColor(fraudResults.risk_level)}
            variant="filled"
            size="small"
            sx={{ fontWeight: 'bold' }}
          />
          {!fraudResults.can_proceed && (
            <Chip
              icon={<BlockIcon />}
              label="REGISTRATION BLOCKED"
              color="error"
              variant="filled"
              size="small"
              sx={{ fontWeight: 'bold' }}
            />
          )}
          <Chip
            label={`${dataQuality.length} Data Points Verified`}
            color="info"
            variant="outlined"
            size="small"
          />
        </Box>

        {/* Manual check button */}
        <Box sx={{ mb: 2 }}>
          <Button
            variant="outlined"
            size="small"
            startIcon={<RefreshIcon />}
            onClick={() => checkFraud()}
            disabled={checking}
          >
            Re-run Comprehensive Check
          </Button>
        </Box>

        {/* Detailed match information */}
        {showDetails && fraudResults.matches && fraudResults.matches.length > 0 && (
          <Accordion sx={{ mt: 2 }}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                🚨 Found {fraudResults.matches.length} Potential Match(es) in Other Kebeles
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List>
                {fraudResults.matches.map((match, index) => (
                  <ListItem key={index} divider>
                    <ListItemIcon>
                      <PersonIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box>
                          <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                            {match.citizen_name} - {match.kebele_name}
                          </Typography>
                          <Chip
                            label={`${match.confidence} Match`}
                            size="small"
                            color={match.confidence.includes('100') ? 'error' : 'warning'}
                            sx={{ mt: 0.5 }}
                          />
                        </Box>
                      }
                      secondary={
                        <Box sx={{ mt: 1 }}>
                          <Typography variant="body2" color="text.secondary">
                            <strong>Match Type:</strong> {match.match_type.replace('_', ' ').toUpperCase()}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            <strong>Details:</strong> {match.details}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>
        )}

        {/* Critical blocking message */}
        {fraudResults.risk_level === 'critical' && (
          <Box sx={{ 
            mt: 2, 
            p: 2, 
            backgroundColor: 'error.dark', 
            borderRadius: 1,
            color: 'error.contrastText'
          }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
              ⛔ REGISTRATION CANNOT PROCEED: This citizen appears to already be registered in another kebele. 
              The combination of name, family information, and/or photo matches an existing registration. 
              Please contact the other kebele to verify the citizen's status.
            </Typography>
          </Box>
        )}

        {/* Success message */}
        {fraudResults.risk_level === 'low' && (
          <Typography variant="body2" color="success.main" sx={{ fontWeight: 'bold' }}>
            ✅ No duplicate registrations found using comprehensive verification. Safe to proceed with registration.
          </Typography>
        )}
      </Alert>
    </Paper>
  );
};

export default ComprehensiveFraudChecker;
