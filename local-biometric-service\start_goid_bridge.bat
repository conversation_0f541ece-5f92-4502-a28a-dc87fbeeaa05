@echo off
echo ========================================
echo    GOID OFFICIAL BRIDGE
echo ========================================
echo.
echo COMPLETE SOLUTION: REAL FINGERPRINTS + GOID DATABASE
echo.
echo WHAT THIS DOES:
echo   1. Captures REAL fingerprints with official Futronic software
echo   2. Saves captured fingerprints directly to GoID database
echo   3. Integrates with your existing GoID citizen management
echo   4. Provides web interface for biometric capture
echo.
echo GOID DATABASE INTEGRATION:
echo   - Connects to GoID backend API (localhost:8000)
echo   - Saves to Biometric table (left_thumb_fingerprint, right_thumb_fingerprint)
echo   - Links fingerprints to specific citizens by ID
echo   - Uses GoID-compatible template format
echo.
echo PROCESS:
echo   1. Enter citizen ID in web interface
echo   2. Click capture button (left or right thumb)
echo   3. Official Futronic software opens
echo   4. Capture fingerprint with official software
echo   5. SAVE the fingerprint image (File -> Save or Ctrl+S)
echo   6. Close the software
echo   7. Real fingerprint is saved to GoID database
echo.
echo REQUIREMENTS:
echo   - Official Futronic software in service directory
echo   - GoID backend running on localhost:8000
echo   - Valid citizen IDs in GoID database
echo.
echo WEB INTERFACE: http://localhost:8001
echo.

python goid_official_bridge.py

echo.
echo GoID official bridge stopped.
pause
