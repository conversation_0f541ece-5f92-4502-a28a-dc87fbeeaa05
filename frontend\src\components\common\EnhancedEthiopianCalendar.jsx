import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Button,
  Grid,
  Chip,
  ToggleButton,
  ToggleButtonGroup,
  TextField,
  Popover,
  FormControl,
  InputLabel
} from '@mui/material';
import {
  ChevronLeft,
  ChevronRight,
  Today as TodayIcon,
  CalendarToday as CalendarIcon
} from '@mui/icons-material';
import {
  gregorianToEthiopian,
  ethiopianToGregorian,
  formatEthiopianDate,
  getCurrentEthiopianDate,
  getDaysInEthiopianMonth,
  ETHIOPIAN_MONTHS,
  ETHIOPIAN_MONTHS_EN
} from '../../utils/ethiopianCalendar';

/**
 * Enhanced Ethiopian Calendar Component
 * 
 * Features:
 * - GC/EC toggle like in the image
 * - Proper calendar grid layout
 * - Month/year navigation
 * - Ethiopian calendar as default
 * - Gregorian calendar as option
 */
const EnhancedEthiopianCalendar = ({
  label = "Select Date",
  value, // Gregorian Date object
  onChange, // Callback with Gregorian Date object
  error = false,
  helperText = "",
  required = false,
  disabled = false,
  language = 'en',
  minYear = 1950,
  maxYear = 2050,
  disableFuture = false,
  placeholder = "Select date",
  ...otherProps
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [calendarType, setCalendarType] = useState('EC'); // 'EC' for Ethiopian, 'GC' for Gregorian
  const [viewDate, setViewDate] = useState(() => {
    const currentEth = getCurrentEthiopianDate();
    return {
      year: currentEth.year,
      month: currentEth.month,
      day: currentEth.day
    };
  });
  const [selectedDate, setSelectedDate] = useState(null);

  const currentEthiopian = getCurrentEthiopianDate();
  const currentGregorian = new Date();

  // Update selected date when value changes
  useEffect(() => {
    if (value && value instanceof Date && !isNaN(value.getTime())) {
      setSelectedDate(value);
    } else {
      setSelectedDate(null);
    }
  }, [value]);

  const handleClick = (event) => {
    if (!disabled) {
      setAnchorEl(event.currentTarget);
    }
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleCalendarTypeChange = (event, newType) => {
    if (newType !== null) {
      setCalendarType(newType);
      // Update view date based on calendar type
      if (newType === 'EC') {
        const currentEth = getCurrentEthiopianDate();
        setViewDate(selectedDate ? gregorianToEthiopian(selectedDate) : currentEth);
      } else {
        const now = new Date();
        setViewDate(selectedDate ? {
          year: selectedDate.getFullYear(),
          month: selectedDate.getMonth() + 1,
          day: selectedDate.getDate()
        } : {
          year: now.getFullYear(),
          month: now.getMonth() + 1,
          day: now.getDate()
        });
      }
    }
  };

  const navigateMonth = (direction) => {
    if (calendarType === 'EC') {
      let newMonth = viewDate.month + direction;
      let newYear = viewDate.year;

      if (newMonth > 13) {
        newMonth = 1;
        newYear++;
      } else if (newMonth < 1) {
        newMonth = 13;
        newYear--;
      }

      setViewDate({ ...viewDate, month: newMonth, year: newYear });
    } else {
      let newMonth = viewDate.month + direction;
      let newYear = viewDate.year;

      if (newMonth > 12) {
        newMonth = 1;
        newYear++;
      } else if (newMonth < 1) {
        newMonth = 12;
        newYear--;
      }

      setViewDate({ ...viewDate, month: newMonth, year: newYear });
    }
  };

  const handleDateSelect = (day) => {
    let gregorianDate;
    
    if (calendarType === 'EC') {
      gregorianDate = ethiopianToGregorian(viewDate.year, viewDate.month, day);
    } else {
      gregorianDate = new Date(viewDate.year, viewDate.month - 1, day);
    }

    if (gregorianDate) {
      // Check if future dates are disabled
      if (disableFuture && gregorianDate > new Date()) {
        return;
      }

      setSelectedDate(gregorianDate);
      handleClose();

      if (onChange) {
        onChange(gregorianDate);
      }

      console.log('Enhanced Ethiopian Calendar Selection:', {
        calendarType,
        userSelected: calendarType === 'EC' ? { year: viewDate.year, month: viewDate.month, day } : `${viewDate.year}-${viewDate.month}-${day}`,
        systemReceives: gregorianDate.toISOString(),
        ethiopianEquivalent: gregorianToEthiopian(gregorianDate)
      });
    }
  };

  const setToday = () => {
    const today = new Date();
    setSelectedDate(today);
    handleClose();

    if (onChange) {
      onChange(today);
    }

    // Update view date to today
    if (calendarType === 'EC') {
      const currentEth = getCurrentEthiopianDate();
      setViewDate(currentEth);
    } else {
      setViewDate({
        year: today.getFullYear(),
        month: today.getMonth() + 1,
        day: today.getDate()
      });
    }
  };

  const getDisplayValue = () => {
    if (selectedDate) {
      if (calendarType === 'EC') {
        const ethiopianDate = gregorianToEthiopian(selectedDate);
        return formatEthiopianDate(ethiopianDate, 'DD/MM/YYYY', language);
      } else {
        return selectedDate.toLocaleDateString('en-US');
      }
    }
    return placeholder;
  };

  const getMonthNames = () => {
    if (calendarType === 'EC') {
      return language === 'am' ? ETHIOPIAN_MONTHS : ETHIOPIAN_MONTHS_EN;
    } else {
      return [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ];
    }
  };

  const getDaysInMonth = () => {
    if (calendarType === 'EC') {
      return getDaysInEthiopianMonth(viewDate.month, viewDate.year);
    } else {
      return new Date(viewDate.year, viewDate.month, 0).getDate();
    }
  };

  const isToday = (day) => {
    if (calendarType === 'EC') {
      return currentEthiopian.year === viewDate.year &&
             currentEthiopian.month === viewDate.month &&
             currentEthiopian.day === day;
    } else {
      return currentGregorian.getFullYear() === viewDate.year &&
             currentGregorian.getMonth() + 1 === viewDate.month &&
             currentGregorian.getDate() === day;
    }
  };

  const isSelected = (day) => {
    if (!selectedDate) return false;

    if (calendarType === 'EC') {
      const selectedEthiopian = gregorianToEthiopian(selectedDate);
      return selectedEthiopian &&
             selectedEthiopian.year === viewDate.year &&
             selectedEthiopian.month === viewDate.month &&
             selectedEthiopian.day === day;
    } else {
      return selectedDate.getFullYear() === viewDate.year &&
             selectedDate.getMonth() + 1 === viewDate.month &&
             selectedDate.getDate() === day;
    }
  };

  const isFutureDate = (day) => {
    if (!disableFuture) return false;

    let testDate;
    if (calendarType === 'EC') {
      testDate = ethiopianToGregorian(viewDate.year, viewDate.month, day);
    } else {
      testDate = new Date(viewDate.year, viewDate.month - 1, day);
    }

    return testDate && testDate > new Date();
  };

  const monthNames = getMonthNames();
  const daysInMonth = getDaysInMonth();
  const open = Boolean(anchorEl);

  return (
    <FormControl fullWidth error={error}>
      {/* Label */}
      <InputLabel shrink sx={{ position: 'relative', transform: 'none', mb: 1 }}>
        {label}
        {required && <span style={{ color: 'red' }}> *</span>}
      </InputLabel>

      {/* Date Input Display */}
      <TextField
        fullWidth
        value={getDisplayValue()}
        onClick={handleClick}
        placeholder={placeholder}
        InputProps={{
          readOnly: true,
          endAdornment: (
            <IconButton onClick={handleClick} disabled={disabled}>
              <CalendarIcon />
            </IconButton>
          )
        }}
        error={error}
        helperText={helperText}
        disabled={disabled}
        sx={{ cursor: disabled ? 'default' : 'pointer' }}
      />

      {/* Calendar Popover */}
      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
      >
        <Paper sx={{ p: 2, minWidth: 320 }}>
          {/* Calendar Type Toggle */}
          <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
            <ToggleButtonGroup
              value={calendarType}
              exclusive
              onChange={handleCalendarTypeChange}
              size="small"
            >
              <ToggleButton value="EC" sx={{ px: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <Box
                    sx={{
                      width: 8,
                      height: 8,
                      borderRadius: '50%',
                      bgcolor: calendarType === 'EC' ? 'success.main' : 'grey.400'
                    }}
                  />
                  EC
                </Box>
              </ToggleButton>
              <ToggleButton value="GC" sx={{ px: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <Box
                    sx={{
                      width: 8,
                      height: 8,
                      borderRadius: '50%',
                      bgcolor: calendarType === 'GC' ? 'success.main' : 'grey.400'
                    }}
                  />
                  GC
                </Box>
              </ToggleButton>
            </ToggleButtonGroup>
          </Box>

          {/* Month/Year Header */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <IconButton size="small" onClick={() => navigateMonth(-1)}>
              <ChevronLeft />
            </IconButton>
            <Typography variant="h6" sx={{ minWidth: 180, textAlign: 'center' }}>
              {monthNames[viewDate.month - 1]} {viewDate.year}
            </Typography>
            <IconButton size="small" onClick={() => navigateMonth(1)}>
              <ChevronRight />
            </IconButton>
          </Box>

          {/* Weekday Headers */}
          <Grid container sx={{ mb: 1 }}>
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
              <Grid item xs={12/7} key={day}>
                <Typography
                  variant="caption"
                  sx={{
                    display: 'block',
                    textAlign: 'center',
                    fontWeight: 'bold',
                    color: 'text.secondary',
                    py: 0.5
                  }}
                >
                  {day}
                </Typography>
              </Grid>
            ))}
          </Grid>

          {/* Calendar Grid */}
          <Grid container spacing={0.5} sx={{ mb: 2 }}>
            {Array.from({ length: daysInMonth }, (_, i) => i + 1).map(day => (
              <Grid item xs={12/7} key={day}>
                <Box
                  onClick={() => !isFutureDate(day) && handleDateSelect(day)}
                  sx={{
                    p: 1,
                    textAlign: 'center',
                    cursor: isFutureDate(day) ? 'not-allowed' : 'pointer',
                    borderRadius: 1,
                    minHeight: 32,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    bgcolor: isSelected(day) ? 'primary.main' : 
                            isToday(day) ? 'success.light' : 'transparent',
                    color: isSelected(day) ? 'primary.contrastText' :
                           isToday(day) ? 'success.contrastText' : 
                           isFutureDate(day) ? 'text.disabled' : 'text.primary',
                    opacity: isFutureDate(day) ? 0.5 : 1,
                    '&:hover': isFutureDate(day) ? {} : {
                      bgcolor: isSelected(day) ? 'primary.dark' : 'action.hover'
                    }
                  }}
                >
                  <Typography variant="body2">
                    {day}
                  </Typography>
                </Box>
              </Grid>
            ))}
          </Grid>

          {/* Footer */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Button
              size="small"
              startIcon={<TodayIcon />}
              onClick={setToday}
              variant="outlined"
            >
              Today
            </Button>
            
            <Button
              size="small"
              onClick={handleClose}
              variant="text"
            >
              Close
            </Button>
          </Box>

          {/* Calendar Type Info */}
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block', textAlign: 'center' }}>
            {calendarType === 'EC' ? 'Ethiopian Calendar' : 'Gregorian Calendar'}
            {calendarType === 'EC' && ` (Year ${currentEthiopian.year} = Current)`}
          </Typography>
        </Paper>
      </Popover>
    </FormControl>
  );
};

export default EnhancedEthiopianCalendar;
