#!/usr/bin/env python3
"""
SDK Worker Process
Isolated process for handling Futronic SDK operations
This prevents crashes from affecting the main service
"""

import sys
import json
import ctypes
from ctypes import cdll, c_int, c_uint, c_void_p, c_ubyte, POINTER, byref
import base64
import time
from datetime import datetime

# Futronic SDK Constants
FTR_OK = 0
FTR_ERROR_EMPTY_FRAME = 1
FTR_ERROR_FAKE_FINGER = 2
FTR_ERROR_NO_FRAME = 3

# Image specifications
FTR_IMAGE_WIDTH = 320
FTR_IMAGE_HEIGHT = 480
FTR_IMAGE_SIZE = FTR_IMAGE_WIDTH * FTR_IMAGE_HEIGHT

class SDKWorker:
    """Isolated SDK worker for safe operations"""
    
    def __init__(self):
        self.scan_api = None
        self.device_handle = None
        self.is_initialized = False

        try:
            # Load SDK with error details
            import os
            dll_path = os.path.abspath('./ftrScanAPI.dll')
            if not os.path.exists(dll_path):
                self._output_error(f"DLL not found at: {dll_path}")
                sys.exit(1)

            self.scan_api = cdll.LoadLibrary(dll_path)
            self._configure_api()
        except Exception as e:
            self._output_error(f"Failed to load SDK: {e}")
            sys.exit(1)
    
    def _configure_api(self):
        """Configure API function signatures"""
        try:
            self.scan_api.ftrScanOpenDevice.restype = c_void_p
            self.scan_api.ftrScanOpenDevice.argtypes = []
            
            self.scan_api.ftrScanCloseDevice.restype = c_int
            self.scan_api.ftrScanCloseDevice.argtypes = [c_void_p]
            
            self.scan_api.ftrScanGetFrame.restype = c_int
            self.scan_api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(c_ubyte), POINTER(c_uint)]
        except Exception as e:
            self._output_error(f"API configuration failed: {e}")
            sys.exit(1)
    
    def _output_success(self, data):
        """Output success result"""
        result = {'success': True, 'data': data}
        print(json.dumps(result))
        sys.exit(0)
    
    def _output_error(self, error_msg):
        """Output error result"""
        result = {'success': False, 'error': error_msg}
        print(json.dumps(result))
        sys.exit(1)
    
    def check_device(self):
        """Check if device is available"""
        try:
            handle = self.scan_api.ftrScanOpenDevice()
            if handle:
                self.scan_api.ftrScanCloseDevice(handle)
                self._output_success({'available': True, 'handle': handle})
            else:
                self._output_error("Device not found")
        except Exception as e:
            self._output_error(f"Device check failed: {e}")
    
    def initialize_device(self):
        """Initialize device"""
        try:
            self.device_handle = self.scan_api.ftrScanOpenDevice()
            if self.device_handle:
                self.is_initialized = True
                self._output_success({'handle': self.device_handle})
            else:
                self._output_error("Failed to open device")
        except Exception as e:
            self._output_error(f"Device initialization failed: {e}")
    
    def capture_fingerprint(self, thumb_type):
        """Capture fingerprint"""
        try:
            if not self.is_initialized:
                # Try to initialize
                self.device_handle = self.scan_api.ftrScanOpenDevice()
                if not self.device_handle:
                    self._output_error("Device not available")
                self.is_initialized = True
            
            # Multiple capture attempts
            max_attempts = 5
            for attempt in range(max_attempts):
                try:
                    # Create buffer
                    buffer_size = FTR_IMAGE_SIZE * 2
                    image_buffer = (c_ubyte * buffer_size)()
                    frame_size = c_uint(buffer_size)
                    
                    # Attempt capture
                    result = self.scan_api.ftrScanGetFrame(self.device_handle, image_buffer, byref(frame_size))
                    
                    if result == FTR_OK:
                        actual_size = frame_size.value
                        if actual_size > 0:
                            # Extract image data
                            image_data = bytes(image_buffer[:actual_size])
                            
                            # Calculate quality
                            quality_score = self._calculate_quality(image_data)
                            
                            # Estimate minutiae
                            minutiae_count = self._estimate_minutiae(image_data)
                            
                            # Encode image data
                            image_b64 = base64.b64encode(image_data).decode()
                            
                            capture_data = {
                                'image_data': image_b64,
                                'quality_score': quality_score,
                                'minutiae_count': minutiae_count,
                                'capture_time': datetime.now().isoformat(),
                                'frame_size': actual_size
                            }
                            
                            self._output_success(capture_data)
                        else:
                            if attempt < max_attempts - 1:
                                time.sleep(0.5)
                                continue
                    
                    elif result == FTR_ERROR_EMPTY_FRAME:
                        if attempt < max_attempts - 1:
                            time.sleep(1)
                            continue
                    
                    elif result == FTR_ERROR_NO_FRAME:
                        if attempt < max_attempts - 1:
                            time.sleep(0.5)
                            continue
                    
                    else:
                        if attempt < max_attempts - 1:
                            time.sleep(0.5)
                            continue
                
                except Exception as capture_error:
                    if attempt < max_attempts - 1:
                        time.sleep(1)
                        continue
                    else:
                        self._output_error(f"Capture attempt failed: {capture_error}")
            
            self._output_error("All capture attempts failed - please place finger on scanner")
            
        except Exception as e:
            self._output_error(f"Fingerprint capture failed: {e}")
        finally:
            # Clean up
            if self.device_handle and self.scan_api:
                try:
                    self.scan_api.ftrScanCloseDevice(self.device_handle)
                except:
                    pass
    
    def _calculate_quality(self, image_data):
        """Calculate basic image quality"""
        try:
            if not image_data:
                return 0
            
            # Basic quality metrics
            non_zero_pixels = sum(1 for b in image_data if b > 10)
            total_pixels = len(image_data)
            
            if total_pixels == 0:
                return 0
            
            # Calculate percentage of meaningful pixels
            meaningful_ratio = non_zero_pixels / total_pixels
            
            # Calculate average intensity
            avg_intensity = sum(image_data) / total_pixels
            
            # Simple quality score
            quality_score = int((meaningful_ratio * 50) + (avg_intensity / 255 * 50))
            
            return max(0, min(100, quality_score))
            
        except Exception:
            return 50
    
    def _estimate_minutiae(self, image_data):
        """Estimate minutiae count"""
        try:
            if not image_data:
                return 0
            
            # Simple estimation based on image complexity
            unique_values = len(set(image_data))
            complexity_ratio = unique_values / 256
            
            # Estimate minutiae count
            estimated_count = int(complexity_ratio * 60) + 20
            
            return min(80, max(20, estimated_count))
            
        except Exception:
            return 40

def main():
    """Main worker function"""
    if len(sys.argv) < 2:
        print(json.dumps({'success': False, 'error': 'No operation specified'}))
        sys.exit(1)
    
    operation = sys.argv[1]
    kwargs = {}
    
    if len(sys.argv) > 2:
        try:
            kwargs = json.loads(sys.argv[2])
        except json.JSONDecodeError:
            print(json.dumps({'success': False, 'error': 'Invalid parameters'}))
            sys.exit(1)
    
    worker = SDKWorker()
    
    if operation == 'check_device':
        worker.check_device()
    elif operation == 'initialize_device':
        worker.initialize_device()
    elif operation == 'capture_fingerprint':
        thumb_type = kwargs.get('thumb_type', 'left')
        worker.capture_fingerprint(thumb_type)
    else:
        print(json.dumps({'success': False, 'error': f'Unknown operation: {operation}'}))
        sys.exit(1)

if __name__ == '__main__':
    main()
