#!/bin/bash

# Fix Network Access Issue Script
# This script fixes CORS and network binding issues for multi-computer access

echo "🔧 Fixing Network Access Issue"
echo "=============================="

# Step 1: Check current network configuration
echo "📍 Step 1: Checking current network configuration..."

echo "Current IP configuration:"
ip addr show | grep "inet " | grep -v "127.0.0.1"

echo ""
echo "Docker container status:"
docker-compose ps

echo ""
echo "Testing current connectivity:"
echo "Testing localhost access..."
curl -I http://localhost:8000/admin/ 2>/dev/null | head -1 || echo "Localhost: FAILED"

echo "Testing 127.0.0.1 access..."
curl -I http://127.0.0.1:8000/admin/ 2>/dev/null | head -1 || echo "127.0.0.1: FAILED"

echo "Testing ************ access..."
curl -I http://************:8000/admin/ 2>/dev/null | head -1 || echo "************: FAILED"

# Step 2: Check if ports are properly bound
echo ""
echo "📍 Step 2: Checking port binding..."
echo "Ports listening on 8000:"
netstat -tlnp | grep :8000 || echo "No process listening on port 8000"

echo ""
echo "Docker port mapping:"
docker-compose port backend 8000 2>/dev/null || echo "Backend port mapping failed"

# Step 3: Test CORS configuration
echo ""
echo "📍 Step 3: Testing CORS configuration..."

# Test CORS preflight request
echo "Testing CORS preflight request..."
curl -X OPTIONS http://************:8000/api/tenants/ \
  -H "Origin: http://************:3000" \
  -H "Access-Control-Request-Method: GET" \
  -H "Access-Control-Request-Headers: authorization,content-type" \
  -v 2>&1 | grep -E "HTTP|Access-Control"

# Step 4: Restart services with proper configuration
echo ""
echo "📍 Step 4: Restarting services with proper configuration..."

echo "Stopping all services..."
docker-compose down

echo "Starting database..."
docker-compose up -d db
sleep 15

echo "Starting backend with network binding..."
docker-compose up -d backend
sleep 30

echo "Starting frontend..."
docker-compose up -d frontend
sleep 20

# Step 5: Test connectivity after restart
echo ""
echo "📍 Step 5: Testing connectivity after restart..."

echo "Testing backend admin interface..."
ADMIN_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://************:8000/admin/ 2>/dev/null || echo "000")
echo "Admin status: $ADMIN_STATUS"

if [ "$ADMIN_STATUS" = "200" ] || [ "$ADMIN_STATUS" = "302" ]; then
    echo "✅ Backend is accessible"
    
    # Test authentication
    echo "Testing authentication..."
    AUTH_RESPONSE=$(curl -s -X POST http://************:8000/api/auth/token/ \
      -H "Content-Type: application/json" \
      -H "Origin: http://************:3000" \
      -d '{"email":"<EMAIL>","password":"admin123"}' 2>/dev/null)
    
    if echo "$AUTH_RESPONSE" | grep -q "access"; then
        echo "✅ Authentication working"
        
        # Extract token and test tenant endpoint
        TOKEN=$(echo "$AUTH_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin)['access'])" 2>/dev/null)
        
        if [ ! -z "$TOKEN" ]; then
            echo "Testing tenant endpoint with CORS headers..."
            TENANT_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
              -H "Origin: http://************:3000" \
              http://************:8000/api/tenants/ 2>/dev/null)
            
            if echo "$TENANT_RESPONSE" | grep -q -E '\[|\{'; then
                echo "✅ Tenant endpoint working with CORS"
            else
                echo "❌ Tenant endpoint failed"
                echo "Response: $TENANT_RESPONSE"
            fi
        else
            echo "❌ Could not extract token"
        fi
    else
        echo "❌ Authentication failed"
        echo "Response: $AUTH_RESPONSE"
    fi
else
    echo "❌ Backend not accessible"
fi

# Step 6: Test from different network locations
echo ""
echo "📍 Step 6: Network accessibility test..."

echo "Testing from different origins:"
ORIGINS=(
    "http://localhost:3000"
    "http://127.0.0.1:3000" 
    "http://************:3000"
)

for origin in "${ORIGINS[@]}"; do
    echo "Testing origin: $origin"
    CORS_TEST=$(curl -s -X OPTIONS http://************:8000/api/tenants/ \
      -H "Origin: $origin" \
      -H "Access-Control-Request-Method: GET" \
      -w "%{http_code}" -o /dev/null 2>/dev/null)
    
    if [ "$CORS_TEST" = "200" ]; then
        echo "  ✅ CORS allowed for $origin"
    else
        echo "  ❌ CORS blocked for $origin (status: $CORS_TEST)"
    fi
done

# Step 7: Show final status
echo ""
echo "📍 Step 7: Final status..."
docker-compose ps

echo ""
echo "✅ Network access fix completed!"
echo ""
echo "🧪 Manual testing steps:"
echo "1. From your computer:"
echo "   - Frontend: http://************:3000/"
echo "   - Backend: http://************:8000/admin/"
echo ""
echo "2. From other computers on the network:"
echo "   - Frontend: http://************:3000/"
echo "   - Should be able to login and see tenant list"
echo ""
echo "🔍 Expected results:"
echo "- ✅ Backend accessible from ************:8000"
echo "- ✅ Frontend accessible from ************:3000"
echo "- ✅ CORS allows requests from frontend to backend"
echo "- ✅ No ERR_EMPTY_RESPONSE errors"
echo "- ✅ Tenant list loads properly"
echo ""
echo "💡 If still not working:"
echo "1. Check firewall settings on host machine"
echo "2. Verify network connectivity between computers"
echo "3. Check if other computers can ping ************"
echo "4. Test with: curl -I http://************:8000/admin/"
echo ""
echo "🚨 Common network issues:"
echo "- Firewall blocking ports 3000 and 8000"
echo "- Docker not binding to external interface"
echo "- CORS configuration blocking cross-origin requests"
echo "- Network routing issues"
