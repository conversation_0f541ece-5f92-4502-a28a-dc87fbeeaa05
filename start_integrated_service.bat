@echo off
echo ========================================
echo   INTEGRATED Biometric Service
echo ========================================
echo.
echo PRODUCTION-READY INTEGRATED SOLUTION
echo.
echo This service provides:
echo - Multi-capture resistant architecture
echo - Full frontend integration
echo - Professional web interface
echo - Real device biometric capture
echo - No crashes between captures
echo.
echo INTEGRATION FEATURES:
echo - Compatible with GoID frontend
echo - Proper device status reporting
echo - Expected API response format
echo - Professional localhost:8001 interface
echo - Process isolation for stability
echo.
echo EXPECTED BEHAVIOR:
echo - Frontend shows "Connected" status
echo - Left thumb capture: Works seamlessly
echo - Service stays alive: Guaranteed
echo - Right thumb capture: Works immediately
echo - Database submission: Success
echo.

cd local-biometric-service

echo Starting Integrated Biometric Service...
echo Service URL: http://localhost:8001
echo Frontend Integration: Enabled
echo Professional Interface: Available
echo.
echo Ready for seamless biometric capture!
echo.

python improved_biometric_service.py

echo.
echo Integrated service stopped.
pause
