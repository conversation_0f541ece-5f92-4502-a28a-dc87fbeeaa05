#!/usr/bin/env python3
"""
Check citizen and ID card data structure
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
sys.path.append('/app')
django.setup()

from django_tenants.utils import schema_context
from tenants.models import Tenant

def check_citizen_data():
    # Check Kebele14 citizen data structure
    tenant = Tenant.objects.get(name='Kebele14')
    print(f'Checking tenant: {tenant.name} (ID: {tenant.id})')
    
    with schema_context(tenant.schema_name):
        from citizens.models import Citizen
        from idcards.models import IDCard
        
        citizen = Citizen.objects.first()
        if citizen:
            print(f'Citizen: {citizen.first_name} {citizen.last_name}')
            print(f'Citizen kebele field: {citizen.kebele}')
            print(f'Citizen kebele type: {type(citizen.kebele)}')
            
            # Check ID card data
            id_card = IDCard.objects.filter(citizen=citizen).first()
            if id_card:
                print(f'ID Card status: {id_card.status}')
                print(f'ID Card has kebele_tenant attr: {hasattr(id_card, "kebele_tenant")}')
                
                # Check all fields
                for field in id_card._meta.fields:
                    field_name = field.name
                    field_value = getattr(id_card, field_name, None)
                    print(f'ID Card {field_name}: {field_value} (type: {type(field_value)})')

if __name__ == '__main__':
    check_citizen_data()
