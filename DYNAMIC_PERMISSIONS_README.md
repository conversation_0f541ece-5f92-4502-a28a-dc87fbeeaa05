# Dynamic Role & Permission Management System

This document explains how to use the new dynamic role and permission management system that allows you to:
1. **Create custom roles** dynamically (e.g., "Emergency Coordinator", "Field Officer", "Senior Clerk")
2. **Grant specific permissions** to any user, regardless of their role
3. **Manage role hierarchies** and relationships
4. **Assign roles** flexibly to users based on their responsibilities

## Overview

The system now supports both:
1. **Role-based permissions** (existing system) - Default permissions based on user roles
2. **Granular permissions** (new system) - Individual permissions that can be granted to specific users

## Key Features

- **Backward Compatible**: Existing role-based permissions continue to work
- **Granular Control**: Grant specific permissions to individual users
- **Temporary Permissions**: Set expiration dates for permissions
- **Audit Trail**: Track who granted permissions and when
- **Hierarchical**: User permissions override role permissions
- **API-driven**: Full REST API for permission management

## Permission Categories

### Citizen Management
- `view_citizens` - View citizen records and details
- `create_citizens` - Register new citizens
- `edit_citizens` - Modify citizen information
- `delete_citizens` - Delete citizen records

### ID Card Management
- `view_id_cards` - View ID card applications and records
- `create_id_cards` - Create new ID card applications
- `edit_id_cards` - Modify ID card applications
- `approve_id_cards` - Approve or reject ID card applications
- `print_id_cards` - Print approved ID cards
- `issue_id_cards` - Mark ID cards as issued to citizens

### User Management
- `view_users` - View user accounts and profiles
- `create_users` - Create new user accounts
- `edit_users` - Modify user accounts and profiles
- `delete_users` - Delete user accounts
- `manage_permissions` - Grant or revoke permissions to/from users

### Other Categories
- Tenant Management (`view_tenants`, `create_tenants`, etc.)
- Reports & Analytics (`view_reports`, `export_data`)
- System Administration (`view_system_logs`, `manage_system_settings`)

## Setup Instructions

### 1. Run Database Migrations

```bash
cd backend
python manage.py migrate users
```

### 2. Setup Initial Permissions

```bash
python manage.py setup_permissions
```

This command creates all the permissions and assigns default role-based permissions.

### 3. Setup Dynamic Roles

```bash
python manage.py setup_dynamic_roles
```

This command:
- Creates built-in roles as Role objects
- Sets up role hierarchy relationships
- Optionally creates example custom roles

#### Additional Setup Options

```bash
# Create built-in roles only
python manage.py setup_dynamic_roles --create-built-in

# Setup role hierarchy only
python manage.py setup_dynamic_roles --setup-hierarchy

# Create example custom roles
python manage.py setup_dynamic_roles --create-examples

# Migrate legacy permissions to use Role objects
python manage.py setup_dynamic_roles --migrate-permissions
```

### 3. Update Your Views (Optional)

You can now use the new dynamic permission classes in your views:

```python
from common.dynamic_permissions import DynamicPermission, require_permissions

class MyViewSet(viewsets.ModelViewSet):
    permission_classes = [DynamicPermission]
    required_permissions = {
        'list': ['view_citizens'],
        'create': ['create_citizens'],
        'update': ['edit_citizens'],
        'destroy': ['delete_citizens']
    }

# Or use the decorator
@require_permissions(['print_id_cards'])
def print_card(self, request, pk=None):
    # Your code here
    pass
```

## Dynamic Role Examples

### Creating Custom Roles

#### Example 1: Emergency Coordinator Role

```python
# Create a custom role for emergency situations
from users.models import Role, RoleType, Permission, RolePermission

emergency_role = Role.objects.create(
    codename='emergency_coordinator',
    name='Emergency Coordinator',
    description='Special role for emergency situations with elevated permissions',
    role_type=RoleType.CUSTOM,
    level=50,  # Between kebele_admin (40) and subcity_admin (60)
    allowed_tenant_types=['kebele', 'subcity']
)

# Assign permissions
permissions = Permission.objects.filter(
    codename__in=['print_id_cards', 'approve_id_cards', 'create_id_cards', 'view_citizens']
)
for permission in permissions:
    RolePermission.objects.create(role_obj=emergency_role, permission=permission)

# Set hierarchy - emergency coordinator can manage clerks
clerk_role = Role.objects.get(codename='clerk')
emergency_role.can_manage_roles.add(clerk_role)
```

#### Example 2: Field Officer Role

```python
# Create a mobile field officer role
field_officer = Role.objects.create(
    codename='field_officer',
    name='Field Officer',
    description='Mobile officer for field operations and citizen registration',
    role_type=RoleType.OPERATIONAL,
    level=20,  # Above clerk (10) but below kebele_leader (30)
    allowed_tenant_types=['kebele']
)

# Assign specific permissions
permissions = Permission.objects.filter(
    codename__in=['view_citizens', 'create_citizens', 'edit_citizens', 'view_id_cards']
)
for permission in permissions:
    RolePermission.objects.create(role_obj=field_officer, permission=permission)
```

### Assigning Dynamic Roles to Users

#### Example 1: Assign Emergency Coordinator Role

```python
from users.models import User

# Get the user and assign the custom role
user = User.objects.get(email='<EMAIL>')
user.assign_role('emergency_coordinator')

# Now this user has emergency coordinator permissions!
print(user.effective_role)  # 'emergency_coordinator'
print(user.effective_role_name)  # 'Emergency Coordinator'
print(user.has_custom_permission('print_id_cards'))  # True
```

#### Example 2: Bulk Role Assignment via API

```bash
POST /api/auth/role-management/bulk_assign_roles/
{
    "user_emails": ["<EMAIL>", "<EMAIL>"],
    "role_codename": "field_officer",
    "reason": "Assigning field officers for mobile registration campaign"
}
```

## Usage Examples

### Example 1: Grant Print Permission to a Clerk (Individual Permission)

Let's say you want to allow a specific clerk to print ID cards (normally only subcity admins can print):

```python
# In Django shell or management command
from users.models import User

# Get the clerk user
clerk = User.objects.get(email='<EMAIL>')

# Grant print permission
clerk.grant_permission(
    'print_id_cards',
    granted_by=admin_user,
    reason='Special authorization for emergency printing'
)

# Now this clerk can print ID cards!
print(clerk.has_custom_permission('print_id_cards'))  # True
```

### Example 2: Grant Temporary Approval Permission

```python
from datetime import datetime, timedelta

# Grant approval permission for 30 days
clerk.grant_permission(
    'approve_id_cards',
    granted_by=admin_user,
    reason='Temporary approval authority while leader is away',
    expires_at=datetime.now() + timedelta(days=30)
)
```

### Example 3: Using the API

#### Grant Permission via API
```bash
POST /api/auth/user-permissions/grant_permission/
{
    "user_email": "<EMAIL>",
    "permission_codename": "print_id_cards",
    "reason": "Special authorization for emergency printing"
}
```

#### Bulk Grant Permissions
```bash
POST /api/auth/user-permissions/bulk_grant/
{
    "user_email": "<EMAIL>",
    "permission_codenames": ["print_id_cards", "approve_id_cards"],
    "reason": "Emergency authorization"
}
```

#### Check User Permissions
```bash
GET /api/auth/user-permission-summary/{user_id}/
```

#### Update Role Permissions
```bash
POST /api/auth/role-permissions/bulk_update/
{
    "role": "clerk",
    "permission_codenames": ["view_citizens", "create_citizens", "print_id_cards"]
}
```

## Permission Priority

The system checks permissions in this order:

1. **Superuser/Superadmin**: Always has all permissions
2. **Explicit User Permission**: Individual grants/denials override everything else
3. **Role-based Permission**: Default permissions for the user's role
4. **Default Deny**: If no permission found, access is denied

## Frontend Integration

A permission management interface is available at `/permissions` (you'll need to add the route):

```jsx
import PermissionManagement from './pages/permissions/PermissionManagement';

// Add to your routes
<Route path="/permissions" element={<PermissionManagement />} />
```

## API Endpoints

### Dynamic Roles
- `GET /api/auth/roles/` - List all dynamic roles
- `POST /api/auth/roles/` - Create a new custom role
- `GET /api/auth/roles/{id}/` - Get role details
- `PUT /api/auth/roles/{id}/` - Update role
- `DELETE /api/auth/roles/{id}/` - Soft delete role (mark as inactive)
- `GET /api/auth/roles/hierarchy/` - Get role hierarchy (legacy + dynamic)
- `POST /api/auth/roles/{id}/assign_permissions/` - Assign permissions to role
- `GET /api/auth/roles/{id}/permissions/` - Get role permissions
- `GET /api/auth/roles/{id}/users/` - Get users with this role
- `GET /api/auth/roles/available_types/` - Get available role types

### Role Management
- `POST /api/auth/role-management/assign_role/` - Assign role to user
- `POST /api/auth/role-management/bulk_assign_roles/` - Assign role to multiple users
- `GET /api/auth/role-management/user_role_summary/` - Get user role summary
- `GET /api/auth/role-management/available_roles/` - Get all available roles

### Permissions
- `GET /api/auth/permissions/` - List all available permissions
- `GET /api/auth/permissions/{id}/` - Get permission details

### Role Permissions (Legacy + Dynamic)
- `GET /api/auth/role-permissions/` - List role permissions
- `POST /api/auth/role-permissions/bulk_update/` - Update legacy role permissions
- `GET /api/auth/role-permissions/by_role/` - Get permissions grouped by role

### User Permissions (Individual Grants)
- `GET /api/auth/user-permissions/` - List user permissions
- `POST /api/auth/user-permissions/grant_permission/` - Grant permission to user
- `POST /api/auth/user-permissions/revoke_permission/` - Revoke permission from user
- `POST /api/auth/user-permissions/bulk_grant/` - Grant multiple permissions
- `GET /api/auth/user-permissions/by_user/` - Get permissions for specific user

### User Permission Summary
- `GET /api/auth/user-permission-summary/` - List users with permission summaries
- `GET /api/auth/user-permission-summary/{id}/` - Get user's complete permission summary
- `GET /api/auth/user-permission-summary/{id}/check_permission/` - Check if user has specific permission

## Migration from Existing System

The new system is fully backward compatible. Your existing permission classes will continue to work, but now they also check for dynamic permissions first.

### Updated Permission Classes

The following permission classes now support dynamic permissions:
- `CanPrintIDCards` - Checks `print_id_cards` permission
- `CanApproveIDCards` - Checks `approve_id_cards` permission  
- `CanManageIDCards` - Checks various ID card permissions based on action

### Gradual Migration

You can gradually migrate your views to use the new system:

1. **Phase 1**: Keep existing permission classes (they now support dynamic permissions)
2. **Phase 2**: Start granting specific permissions to users as needed
3. **Phase 3**: Optionally migrate to `DynamicPermission` class for new views

## Security Considerations

- Only users with `manage_permissions` can grant/revoke permissions
- All permission changes are logged with timestamps and reasons
- Temporary permissions automatically expire
- Explicit denials override role permissions
- Superusers always have all permissions (cannot be restricted)

## Troubleshooting

### Permission Not Working
1. Check if user has the permission: `user.has_custom_permission('permission_codename')`
2. Check if permission exists and is active
3. Check if permission has expired
4. Verify the permission class is checking the right permission

### API Errors
- Ensure user has `manage_permissions` to modify permissions
- Check permission codenames are correct
- Verify user emails exist in the system

## Examples in Practice

### Scenario: Emergency ID Card Printing

During an emergency, you need a clerk to be able to print ID cards:

1. **Via API**:
```bash
curl -X POST /api/auth/user-permissions/grant_permission/ \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_email": "<EMAIL>",
    "permission_codename": "print_id_cards",
    "reason": "Emergency printing authorization during system outage",
    "expires_at": "2024-12-31T23:59:59Z"
  }'
```

2. **Via Django Shell**:
```python
from users.models import User
clerk = User.objects.get(email='<EMAIL>')
clerk.grant_permission('print_id_cards', reason='Emergency authorization')
```

Now the clerk can print ID cards until the permission is revoked or expires!

This system gives you complete flexibility while maintaining security and audit trails.

---

# Tenant-Aware Role Management

## Overview

The system now supports **tenant-aware role management**, allowing subcity admins to manage roles and permissions for their child kebeles. This enables:

1. **Subcity admins** can create custom roles for their child kebeles
2. **Subcity admins** can assign roles to users in child kebeles
3. **Subcity admins** can grant additional permissions to users in child kebeles
4. **Tenant-specific roles** that are scoped to specific organizational units

## Tenant Hierarchy Support

### Role Management Permissions by Level

- **Superadmin**: Can manage all roles globally
- **City Admin**: Can manage roles in their city and all child subcities/kebeles
- **Subcity Admin**: Can manage roles in their subcity and child kebeles
- **Kebele Admin**: Can manage roles only in their own kebele

### Tenant-Specific vs Global Roles

#### Global Roles
- Available system-wide (e.g., built-in roles like 'clerk', 'kebele_leader')
- Can be assigned to users in any tenant
- Managed by superadmins and high-level administrators

#### Tenant-Specific Roles
- Created for and scoped to specific tenants
- Can only be assigned to users within the tenant's hierarchy
- Managed by administrators of the parent tenant
