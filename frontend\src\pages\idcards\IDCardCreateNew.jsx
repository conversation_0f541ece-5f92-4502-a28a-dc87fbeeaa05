import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  Grid,
  TextField,
  Button,
  Card,
  CardContent,
  InputAdornment,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Avatar,
  CircularProgress,
  Alert,
  Autocomplete,
  IconButton,
} from '@mui/material';
import {
  Search as SearchIcon,
  Person as PersonIcon,
  ArrowBack as ArrowBackIcon,
  CalendarToday as CalendarIcon,
  Badge as BadgeIcon,
} from '@mui/icons-material';
import FixedEthiopianDateAdapter from '../../utils/FixedEthiopianDateAdapter';
import EthiopianCalendarWidget from '../../components/common/EthiopianCalendarWidget';
import EnhancedEthiopianCalendar from '../../components/common/EnhancedEthiopianCalendar';
import { useAuth } from '../../contexts/AuthContext';
import { useLocalization } from '../../contexts/LocalizationContext';
import axios from '../../utils/axios';

const IDCardCreateNew = () => {
  const navigate = useNavigate();
  const { citizenId } = useParams();
  const { user } = useAuth();
  const { t } = useLocalization();

  // State management
  const [loading, setLoading] = useState(false);
  const [citizens, setCitizens] = useState([]);
  const [templates, setTemplates] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCitizen, setSelectedCitizen] = useState(null);
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [issueDate, setIssueDate] = useState(new Date());
  const [expiryDate, setExpiryDate] = useState(new Date(new Date().setFullYear(new Date().getFullYear() + 2)));
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    // Initialize templates first
    fetchTemplates();
  }, []);

  useEffect(() => {
    // If citizenId is provided in URL, fetch that citizen automatically
    // Use getTenantId() which has multiple fallback mechanisms
    if (citizenId) {
      const tenantId = getTenantId();
      if (tenantId) {
        console.log('🔄 Auto-fetching citizen for ID:', citizenId, 'with tenant:', tenantId);
        fetchCitizenById(citizenId);
      } else {
        // If no tenant ID available yet, retry after a short delay
        console.log('⏳ No tenant ID available yet, retrying in 500ms...');
        const retryTimer = setTimeout(() => {
          const retryTenantId = getTenantId();
          if (retryTenantId) {
            console.log('🔄 Retry: Auto-fetching citizen for ID:', citizenId, 'with tenant:', retryTenantId);
            fetchCitizenById(citizenId);
          } else {
            console.warn('❌ Still no tenant ID available after retry');
          }
        }, 500);

        return () => clearTimeout(retryTimer);
      }
    }
  }, [citizenId]);

  // Additional useEffect to handle user context changes
  useEffect(() => {
    // If user context becomes available and we have a citizenId but no selectedCitizen
    if (citizenId && user?.tenant_id && !selectedCitizen) {
      console.log('🔄 User context available, fetching citizen:', citizenId);
      fetchCitizenById(citizenId);
    }
  }, [user?.tenant_id, citizenId, selectedCitizen]);

  const fetchTemplates = async () => {
    try {
      const tenantId = getTenantId();

      if (!tenantId) {
        console.warn('No tenant ID found, using fallback templates');
        throw new Error('No tenant context available');
      }

      console.log('🔍 Fetching templates for tenant:', tenantId);
      console.log('🔍 Request URL:', `/api/tenants/${tenantId}/idcards/templates/`);
      console.log('🔍 Using tenant-specific template endpoint');

      const response = await axios.get(`/api/tenants/${tenantId}/idcards/templates/`);

      console.log('✅ Raw API response:', response);
      console.log('✅ Response status:', response.status);
      console.log('✅ Response data:', response.data);

      const templatesData = response.data.results || response.data;
      console.log('✅ Processed templates data:', templatesData);
      console.log('✅ Templates count:', templatesData?.length || 0);

      if (!templatesData || templatesData.length === 0) {
        throw new Error('No templates found in API response');
      }

      setTemplates(templatesData);

      // Set default template
      const defaultTemplate = templatesData.find(t => t.is_default);
      if (defaultTemplate) {
        setSelectedTemplate(defaultTemplate.id);
        console.log('✅ Default template selected:', defaultTemplate.name);
      } else if (templatesData.length > 0) {
        setSelectedTemplate(templatesData[0].id);
        console.log('✅ First template selected:', templatesData[0].name);
      }
    } catch (error) {
      console.error('❌ Error fetching templates:', error);
      console.error('❌ Template API error details:', error.response?.data);

      // Handle different error types
      if (error.response?.status === 404) {
        console.log('📝 Templates endpoint not found or no templates exist');
        // Try to create default templates via API
        await createDefaultTemplates();
      } else {
        console.log('🔄 Using fallback templates due to API error');

        // Create comprehensive fallback templates
        const fallbackTemplates = [
          {
            id: 'default',
            name: 'Default Template',
            description: 'Standard ID card template with official government styling',
            background_color: '#FFFFFF',
            text_color: '#000000',
            accent_color: '#1976D2',
            is_default: true,
            is_active: true
          },
          {
            id: 'modern',
            name: 'Modern Template',
            description: 'Modern ID card template with contemporary design',
            background_color: '#F5F5F5',
            text_color: '#212121',
            accent_color: '#2196F3',
            is_default: false,
            is_active: true
          },
          {
            id: 'classic',
            name: 'Classic Template',
            description: 'Traditional ID card template with classic styling',
            background_color: '#FAFAFA',
            text_color: '#333333',
            accent_color: '#4CAF50',
            is_default: false,
            is_active: true
          }
        ];

        setTemplates(fallbackTemplates);
        setSelectedTemplate('default');
      }
    }
  };

  const createDefaultTemplates = async () => {
    try {
      const tenantId = getTenantId();
      if (!tenantId) {
        console.warn('No tenant ID for creating templates');
        return;
      }

      console.log('🔧 Attempting to create default templates using seed endpoint...');

      // Use the new seed_defaults endpoint
      try {
        const response = await axios.post('/api/idcards/templates/seed_defaults/', {}, {
          headers: {
            'X-Tenant-ID': tenantId
          }
        });

        console.log('✅ Seed response:', response.data);

        if (response.data.templates && response.data.templates.length > 0) {
          setTemplates(response.data.templates);
          const defaultTemplate = response.data.templates.find(t => t.is_default);
          if (defaultTemplate) {
            setSelectedTemplate(defaultTemplate.id);
            console.log('✅ Default template auto-selected:', defaultTemplate.name);
          } else if (response.data.templates.length > 0) {
            // If no default template, select the first one
            setSelectedTemplate(response.data.templates[0].id);
            console.log('✅ First template auto-selected:', response.data.templates[0].name);
          }

          if (response.data.detail.includes('already exist')) {
            console.log(`✅ Using existing ${response.data.templates_count} templates`);
          } else {
            console.log(`✅ Successfully created ${response.data.templates_count} templates`);
          }

          // After successful seeding, try to fetch templates again to ensure they're available
          console.log('🔄 Attempting to refetch templates after seeding...');
          try {
            const refetchResponse = await axios.get(`/api/tenants/${tenantId}/idcards/templates/`);
            console.log('✅ Templates refetched successfully:', refetchResponse.data);
          } catch (refetchError) {
            console.warn('⚠️  Template refetch failed, but seeding was successful:', refetchError);
          }

        } else {
          throw new Error('No templates returned from seed endpoint');
        }
      } catch (seedError) {
        console.error('❌ Failed to seed templates:', seedError);

        // If seeding failed, use fallback templates
        console.log('🔄 Template seeding failed, using fallback templates');
        const fallbackTemplates = [
          {
            id: 'default',
            name: 'Default Template (Fallback)',
            description: 'Standard ID card template with official government styling',
            is_default: true,
            is_active: true
          }
        ];
        setTemplates(fallbackTemplates);
        setSelectedTemplate('default');
      }
    } catch (error) {
      console.error('❌ Error in createDefaultTemplates:', error);
      // Use fallback templates as last resort
      const fallbackTemplates = [
        {
          id: 'default',
          name: 'Default Template (Fallback)',
          description: 'Standard ID card template with official government styling',
          is_default: true,
          is_active: true
        }
      ];
      setTemplates(fallbackTemplates);
      setSelectedTemplate('default');
    }
  };

  const getTenantId = () => {
    console.log('🔍 Getting tenant ID...');

    // Get tenant ID from multiple sources for reliability
    let tenantId = user?.tenant_id;
    console.log('🔍 From user context:', tenantId);

    if (!tenantId) {
      // Fallback: Get from localStorage user object
      try {
        const storedUser = JSON.parse(localStorage.getItem('user') || '{}');
        tenantId = storedUser.tenant_id;
        console.log('🔍 From localStorage user:', tenantId);
      } catch (e) {
        console.warn('Could not parse stored user data');
      }
    }

    if (!tenantId) {
      // Fallback: Get from JWT token
      try {
        const accessToken = localStorage.getItem('accessToken');
        if (accessToken) {
          const base64Url = accessToken.split('.')[1];
          const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
          const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
          }).join(''));
          const tokenData = JSON.parse(jsonPayload);
          tenantId = tokenData.tenant_id;
          console.log('🔍 From JWT token:', tenantId);
        }
      } catch (e) {
        console.warn('Could not decode JWT token');
      }
    }

    console.log('✅ Final tenant ID:', tenantId);
    return tenantId;
  };

  const fetchCitizenById = async (id) => {
    try {
      console.log('🔍 Fetching citizen by ID:', id);
      setLoading(true);
      setError(''); // Clear any previous errors

      const tenantId = getTenantId();
      if (!tenantId) {
        console.error('❌ No tenant ID found');
        setError('No tenant context found. Please log in again.');
        setLoading(false);
        return;
      }

      console.log('🔍 Using tenant ID:', tenantId);
      const response = await axios.get(`/api/tenants/${tenantId}/citizens/${id}/`);
      const citizen = response.data;

      console.log('✅ Citizen fetched successfully:', citizen);
      setSelectedCitizen(citizen);

      // Auto-populate the search term with citizen's name
      const citizenName = `${citizen.first_name} ${citizen.middle_name || ''} ${citizen.last_name} (ID: ${citizen.digital_id})`.replace(/\s+/g, ' ').trim();
      setSearchTerm(citizenName);

      console.log('✅ Auto-populated search term:', citizenName);
      setLoading(false);
    } catch (error) {
      console.error('❌ Error fetching citizen:', error);
      console.error('❌ Error details:', error.response?.data);
      setError(`Citizen not found: ${error.response?.data?.detail || error.message}`);
      setLoading(false);
    }
  };

  const searchCitizens = async (searchValue) => {
    if (!searchValue || searchValue.length < 2) {
      setCitizens([]);
      return;
    }

    try {
      const tenantId = getTenantId();
      if (!tenantId) return;

      const response = await axios.get(`/api/tenants/${tenantId}/citizens/`, {
        params: {
          search: searchValue,
          page_size: 10
        }
      });
      setCitizens(response.data.results || response.data);
    } catch (error) {
      console.error('Error searching citizens:', error);
      setCitizens([]);
    }
  };

  const handleSearchChange = (event, value) => {
    setSearchTerm(value);
    searchCitizens(value);
  };

  const handleCitizenSelect = (event, citizen) => {
    setSelectedCitizen(citizen);
    setSearchTerm(citizen ? `${citizen.first_name} ${citizen.middle_name} ${citizen.last_name} (ID: ${citizen.digital_id})` : '');
  };

  const handleCancel = () => {
    navigate('/idcards');
  };

  const handleRegisterIDCard = async () => {
    if (!selectedCitizen) {
      setError('Please select a citizen');
      return;
    }

    if (!selectedTemplate) {
      setError('Please select a template');
      return;
    }

    try {
      setLoading(true);
      setError('');

      const tenantId = getTenantId();
      if (!tenantId) {
        setError('No tenant context found. Please log in again.');
        setLoading(false);
        return;
      }

      // Validate template exists in current templates list
      let templateId = null;
      if (selectedTemplate && selectedTemplate !== 'default') {
        const templateExists = templates.find(t => t.id === selectedTemplate);
        if (templateExists) {
          templateId = selectedTemplate;
          console.log('✅ Using template:', templateExists.name, 'ID:', templateId);
        } else {
          console.warn('⚠️  Selected template not found in templates list, using null');
        }
      }

      // Create Ethiopian date adapter for dual calendar handling
      const adapter = new FixedEthiopianDateAdapter({ locale: 'en' });

      // Get dual calendar information for both dates
      const issueDualInfo = adapter.getDualCalendarInfo(issueDate);
      const expiryDualInfo = adapter.getDualCalendarInfo(expiryDate);

      const idCardData = {
        citizen: selectedCitizen.id,
        template: templateId,
        // Gregorian dates for system storage (ISO format)
        issue_date: issueDate.toISOString().split('T')[0],
        expiry_date: expiryDate.toISOString().split('T')[0],
        // Ethiopian dates for user reference and display
        issue_date_ethiopian: issueDualInfo?.formatted.ethiopian,
        expiry_date_ethiopian: expiryDualInfo?.formatted.ethiopian,
        // Ethiopian date components for calculations
        issue_date_ethiopian_components: issueDualInfo?.ethiopian,
        expiry_date_ethiopian_components: expiryDualInfo?.ethiopian,
        status: 'draft'
      };

      console.log('🔍 ID Card Data being sent:', idCardData);
      console.log('🔍 Selected Citizen:', selectedCitizen);
      console.log('🔍 Selected Template:', selectedTemplate);
      console.log('🔍 Available Templates:', templates);
      console.log('🔍 Template ID being sent:', templateId);
      console.log('🔍 Citizen ID being sent:', selectedCitizen.id);
      console.log('🔍 Citizen Digital ID:', selectedCitizen.digital_id);

      // Dual Calendar Information
      console.log('📅 DUAL CALENDAR INFORMATION:');
      console.log('📅 Issue Date - Gregorian:', issueDate.toISOString().split('T')[0]);
      console.log('📅 Issue Date - Ethiopian:', issueDualInfo?.formatted.ethiopian);
      console.log('📅 Issue Date - Ethiopian Components:', issueDualInfo?.ethiopian);
      console.log('📅 Expiry Date - Gregorian:', expiryDate.toISOString().split('T')[0]);
      console.log('📅 Expiry Date - Ethiopian:', expiryDualInfo?.formatted.ethiopian);
      console.log('📅 Expiry Date - Ethiopian Components:', expiryDualInfo?.ethiopian);

      console.log('🔍 Tenant ID for endpoint:', tenantId);
      console.log('🔍 Request URL (tenant-specific):', `/api/tenants/${tenantId}/idcards/`);
      console.log('🔍 Using tenant-specific endpoint for proper schema context');

      // Check authentication before making request
      const accessToken = localStorage.getItem('accessToken');
      const refreshToken = localStorage.getItem('refreshToken');
      console.log('🔍 Access token exists:', !!accessToken);
      console.log('🔍 Refresh token exists:', !!refreshToken);
      console.log('🔍 Access token (first 50 chars):', accessToken ? accessToken.substring(0, 50) + '...' : 'null');

      // Use tenant-specific endpoint to ensure proper schema context
      const response = await axios.post(`/api/tenants/${tenantId}/idcards/`, idCardData);

      setSuccess('ID Card registered successfully!');
      setTimeout(() => {
        navigate(`/idcards/${response.data.id}`);
      }, 1500);

    } catch (error) {
      console.error('❌ Error registering ID card:', error);
      console.error('❌ Error status:', error.response?.status);
      console.error('❌ Error response data:', error.response?.data);
      console.error('❌ Error response headers:', error.response?.headers);
      console.error('❌ Full error object:', error);

      // Extract detailed error message
      let errorMessage = 'Failed to register ID card';
      if (error.response?.data) {
        console.log('🔍 Processing error response data:', error.response.data);

        if (error.response.data.detail) {
          errorMessage = error.response.data.detail;
          console.log('🔍 Using detail error:', errorMessage);
        } else if (error.response.data.citizen) {
          errorMessage = `Citizen error: ${Array.isArray(error.response.data.citizen) ? error.response.data.citizen.join(', ') : error.response.data.citizen}`;
          console.log('🔍 Using citizen error:', errorMessage);
        } else if (error.response.data.template) {
          errorMessage = `Template error: ${Array.isArray(error.response.data.template) ? error.response.data.template.join(', ') : error.response.data.template}`;
          console.log('🔍 Using template error:', errorMessage);
        } else if (typeof error.response.data === 'object') {
          // Handle field-specific errors
          const fieldErrors = Object.entries(error.response.data)
            .map(([field, errors]) => {
              const errorText = Array.isArray(errors) ? errors.join(', ') : errors;
              console.log(`🔍 Field error - ${field}:`, errorText);
              return `${field}: ${errorText}`;
            })
            .join('; ');
          errorMessage = fieldErrors || errorMessage;
          console.log('🔍 Using field errors:', errorMessage);
        }
      } else {
        console.log('🔍 No response data, using default error message');
      }

      console.log('🔍 Final error message:', errorMessage);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <IconButton onClick={handleCancel} sx={{ mr: 2 }}>
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="body2" color="text.secondary">
            {t('back_to_id_cards', 'Back to ID Cards')}
          </Typography>
          <Box sx={{ flexGrow: 1 }} />
          <Typography variant="body2" color="text.secondary">
            {t('required_fields_note', 'All fields marked with * are required')}
          </Typography>
        </Box>

        {/* Error/Success Messages */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}
        {success && (
          <Alert severity="success" sx={{ mb: 3 }}>
            {success}
          </Alert>
        )}

        <Grid container spacing={3}>
          {/* Left Side - Citizen Information */}
          <Grid item xs={12} md={6}>
            <Card sx={{ height: 'fit-content' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <PersonIcon sx={{ color: 'success.main', mr: 1 }} />
                  <Typography variant="h6" fontWeight="bold">
                    {t('citizen_information', 'Citizen Information')}
                  </Typography>
                </Box>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {citizenId ? (
                    selectedCitizen ?
                      t('citizen_auto_selected', 'Citizen automatically selected from the citizens list.') :
                      t('loading_citizen_info', 'Loading citizen information...')
                  ) : (
                    t('select_citizen_for_id_card', 'Select the citizen for whom you want to create an ID card.')
                  )}
                </Typography>

                {!citizenId && (
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {t('search_citizen_instruction', 'Search for a citizen by name, ID, or registration number')}
                  </Typography>
                )}

                <Autocomplete
                  options={citizens}
                  getOptionLabel={(option) =>
                    `${option.first_name} ${option.middle_name} ${option.last_name} (ID: ${option.digital_id})`
                  }
                  value={selectedCitizen}
                  onChange={handleCitizenSelect}
                  onInputChange={handleSearchChange}
                  disabled={!!citizenId} // Disable if citizen is auto-selected from URL
                  isOptionEqualToValue={(option, value) => option.id === value.id}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      placeholder={
                        citizenId ?
                          (selectedCitizen ? "Citizen auto-selected" : "Loading citizen...") :
                          "Type to search..."
                      }
                      InputProps={{
                        ...params.InputProps,
                        startAdornment: (
                          <InputAdornment position="start">
                            {citizenId && !selectedCitizen ? (
                              <CircularProgress size={20} />
                            ) : (
                              <SearchIcon />
                            )}
                          </InputAdornment>
                        ),
                      }}
                      sx={{ mb: 2 }}
                    />
                  )}
                  renderOption={(props, option) => (
                    <Box component="li" {...props}>
                      <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                        {option.first_name?.charAt(0)}
                      </Avatar>
                      <Box>
                        <Typography variant="body2" fontWeight="medium">
                          {`${option.first_name} ${option.middle_name} ${option.last_name}`}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          ID: {option.digital_id}
                        </Typography>
                      </Box>
                    </Box>
                  )}
                  loading={loading}
                  loadingText={t('searching', 'Searching...')}
                  noOptionsText="No citizens found"
                />

                {selectedCitizen && (
                  <Card variant="outlined" sx={{ mt: 2 }}>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                          {selectedCitizen.first_name?.charAt(0)}
                        </Avatar>
                        <Box>
                          <Typography variant="subtitle2" fontWeight="bold">
                            {`${selectedCitizen.first_name} ${selectedCitizen.middle_name} ${selectedCitizen.last_name}`}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {citizenId ?
                              t('auto_selected_from_list', 'Auto-selected from citizens list') :
                              t('select_citizen_for_id_card_creation', 'Select the citizen for whom this ID card is being created.')
                            }
                          </Typography>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Right Side - ID Card Details */}
          <Grid item xs={12} md={6}>
            <Card sx={{ height: 'fit-content' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <BadgeIcon sx={{ color: 'primary.main', mr: 1 }} />
                  <Typography variant="h6" fontWeight="bold">
                    {t('id_card_details', 'ID Card Details')}
                  </Typography>
                </Box>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  {t('provide_id_card_details', 'Provide the details for the ID card.')}
                </Typography>

                {/* Template Selection */}
                <FormControl fullWidth sx={{ mb: 3 }}>
                  <InputLabel>{t('id_card_template_required', 'ID Card Template *')}</InputLabel>
                  <Select
                    value={selectedTemplate}
                    onChange={(e) => setSelectedTemplate(e.target.value)}
                    label="ID Card Template *"
                  >
                    {templates.map((template) => (
                      <MenuItem key={template.id} value={template.id}>
                        {template.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    {templates.length > 0 && templates[0].name?.includes('Fallback')
                      ? t('using_fallback_templates', 'Using fallback templates (API templates not available)')
                      : t('select_template_instruction', 'Select the template to use for this ID card')
                    }
                  </Typography>
                  {templates.length > 0 && templates[0].name?.includes('Fallback') && (
                    <Button
                      size="small"
                      variant="outlined"
                      onClick={fetchTemplates}
                      sx={{ ml: 2 }}
                    >
                      {t('retry_templates', 'Retry Templates')}
                    </Button>
                  )}
                </Box>

                {/* Issue Date - Self-contained Ethiopian Calendar */}
                <Grid container spacing={2} sx={{ mb: 3 }}>
                  <Grid item xs={6}>
                    <EthiopianCalendarWidget
                      label="Issue Date *"
                      value={issueDate}
                      onChange={(newValue) => {
                        setIssueDate(newValue);
                        // Log the Ethiopian calendar info for debugging
                        if (newValue) {
                          const adapter = new FixedEthiopianDateAdapter({ locale: 'en' });
                          const dualInfo = adapter.getDualCalendarInfo(newValue);
                          console.log('Issue Date Selected (Ethiopian Calendar):', dualInfo);
                        }
                      }}
                      language="en"
                      required={true}
                      helperText="Select issue date using Ethiopian calendar (Year 2017 = Current)"
                      placeholder="Click to select issue date"
                      minYear={2010}
                      maxYear={2030}
                      showToggle={true}
                      showConversion={true}
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <EthiopianCalendarWidget
                      label="Expiry Date *"
                      value={expiryDate}
                      onChange={(newValue) => {
                        setExpiryDate(newValue);
                        // Log the Ethiopian calendar info for debugging
                        if (newValue) {
                          const adapter = new FixedEthiopianDateAdapter({ locale: 'en' });
                          const dualInfo = adapter.getDualCalendarInfo(newValue);
                          console.log('Expiry Date Selected (Ethiopian Calendar):', dualInfo);
                        }
                      }}
                      language="en"
                      required={true}
                      helperText="Select expiry date using Ethiopian calendar (Year 2017 = Current)"
                      placeholder="Click to select expiry date"
                      minYear={2010}
                      maxYear={2040}
                      showToggle={true}
                      showConversion={true}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Action Buttons */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
          <Button
            variant="outlined"
            onClick={handleCancel}
            disabled={loading}
          >
            {t('cancel', 'Cancel')}
          </Button>
          <Button
            variant="contained"
            color="success"
            onClick={handleRegisterIDCard}
            disabled={loading || !selectedCitizen}
            startIcon={loading ? <CircularProgress size={20} /> : <BadgeIcon />}
          >
            {loading ? t('registering', 'Registering...') : t('register_id_card', 'Register ID Card')}
          </Button>
        </Box>
      </Box>
  );
};

export default IDCardCreateNew;
