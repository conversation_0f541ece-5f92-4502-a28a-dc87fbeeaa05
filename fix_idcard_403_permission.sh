#!/bin/bash

# Fix ID Card 403 Permission Error <PERSON>t
# This script fixes the 403 Forbidden error when submitting ID cards for approval

echo "🔧 Fixing ID Card 403 Permission Error"
echo "======================================"

echo "📍 Issue Found:"
echo "- User role: superadmin"
echo "- Action: submit_for_approval"
echo "- Error: 403 Forbidden"
echo "- Cause: Code only allowed 'clerk' role for submit_for_approval"
echo ""
echo "📍 Fix Applied:"
echo "- Updated approval_action method in both:"
echo "  * backend/tenants/views/tenant_idcard_views.py"
echo "  * backend/idcards/views.py"
echo "- Now allows: ['clerk', 'superadmin'] and is_superuser"
echo "- <PERSON>ad<PERSON> can now submit ID cards for approval"
echo ""

# Step 1: Restart backend to apply permission changes
echo "📍 Step 1: Restarting backend to apply permission changes..."
docker-compose restart backend
sleep 30

# Step 2: Test the fixed permissions
echo ""
echo "📍 Step 2: Testing fixed ID card approval permissions..."

echo "Getting authentication token..."
AUTH_RESPONSE=$(curl -s -X POST http://10.139.8.141:8000/api/auth/token/ \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}')

if echo "$AUTH_RESPONSE" | grep -q "access"; then
    echo "✅ Authentication successful"
    
    # Extract token
    TOKEN=$(echo "$AUTH_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin)['access'])" 2>/dev/null)
    
    if [ ! -z "$TOKEN" ]; then
        echo ""
        echo "Testing ID card endpoints..."
        
        # First, let's check if there are any ID cards to test with
        echo "Checking available ID cards..."
        
        # Try to get tenant ID from context or use a default
        TENANT_CONTEXT_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
          http://10.139.8.141:8000/api/tenants/current_context/ 2>/dev/null)
        
        if echo "$TENANT_CONTEXT_RESPONSE" | grep -q '"current_tenant"'; then
            TENANT_ID=$(echo "$TENANT_CONTEXT_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(data['current_tenant']['id'])
except:
    print('1')
" 2>/dev/null)
            echo "Using tenant ID from context: $TENANT_ID"
        else
            echo "No tenant context, using default tenant ID: 1"
            TENANT_ID=1
        fi
        
        # Check ID cards in this tenant
        IDCARDS_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
          http://10.139.8.141:8000/api/tenants/${TENANT_ID}/idcards/ 2>/dev/null)
        
        if echo "$IDCARDS_RESPONSE" | grep -q '"id"'; then
            echo "✅ ID cards endpoint accessible"
            
            # Try to get the first ID card ID
            IDCARD_ID=$(echo "$IDCARDS_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, list) and len(data) > 0:
        print(data[0]['id'])
    elif isinstance(data, dict) and 'results' in data and len(data['results']) > 0:
        print(data['results'][0]['id'])
    else:
        print('none')
except:
    print('none')
" 2>/dev/null)
            
            if [ "$IDCARD_ID" != "none" ]; then
                echo "Found ID card to test with: $IDCARD_ID"
                
                # Test the approval action endpoint
                echo "Testing approval_action endpoint..."
                APPROVAL_RESPONSE=$(curl -s -X POST \
                  -H "Authorization: Bearer $TOKEN" \
                  -H "Content-Type: application/json" \
                  -d '{"action":"submit_for_approval","comment":"Test submission by superadmin"}' \
                  http://10.139.8.141:8000/api/tenants/${TENANT_ID}/idcards/${IDCARD_ID}/approval_action/ \
                  -w "HTTP_STATUS:%{http_code}")
                
                HTTP_STATUS=$(echo "$APPROVAL_RESPONSE" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
                RESPONSE_BODY=$(echo "$APPROVAL_RESPONSE" | sed 's/HTTP_STATUS:[0-9]*$//')
                
                echo "HTTP Status: $HTTP_STATUS"
                echo "Response: $RESPONSE_BODY"
                
                if [ "$HTTP_STATUS" = "200" ] || [ "$HTTP_STATUS" = "201" ]; then
                    echo "✅ SUCCESS: ID card approval action now works!"
                    echo "Superadmin can now submit ID cards for approval"
                elif [ "$HTTP_STATUS" = "403" ]; then
                    echo "❌ STILL 403: Permission fix may not have taken effect"
                    echo "Check if backend restart completed successfully"
                elif [ "$HTTP_STATUS" = "400" ]; then
                    echo "⚠️  400 Bad Request: Might be due to ID card status"
                    echo "This is expected if ID card is not in 'draft' status"
                    echo "Permission fix is working, but ID card status prevents action"
                else
                    echo "❌ Unexpected status: $HTTP_STATUS"
                fi
                
            else
                echo "⚠️  No ID cards found to test with"
                echo "Create an ID card first to test approval functionality"
            fi
            
        else
            echo "❌ Cannot access ID cards endpoint"
            echo "Response: $IDCARDS_RESPONSE"
        fi
        
    else
        echo "❌ Could not extract token"
    fi
else
    echo "❌ Authentication failed"
    echo "Response: $AUTH_RESPONSE"
fi

# Step 3: Check backend logs for any permission errors
echo ""
echo "📍 Step 3: Checking backend logs for permission info..."
docker-compose logs --tail=20 backend | grep -E "403|permission|approval|role" || echo "No relevant logs found"

echo ""
echo "✅ ID card 403 permission fix completed!"
echo ""
echo "🧪 Manual testing steps:"
echo "1. Open: http://10.139.8.141:3000/idcards"
echo "2. Click on any ID card to view details"
echo "3. If ID card is in 'draft' status, click 'Send for Approval'"
echo "4. Should now work without 403 error"
echo ""
echo "🔍 Expected results:"
echo "- ✅ No more 403 Forbidden errors"
echo "- ✅ Superadmin can submit ID cards for approval"
echo "- ✅ Approval workflow works correctly"
echo "- ✅ Status changes from 'draft' to 'pending_approval'"
echo ""
echo "💡 If still getting 403:"
echo "1. Check if backend restart completed successfully"
echo "2. Verify user role is 'superadmin' or 'clerk'"
echo "3. Check if ID card is in 'draft' status"
echo "4. Look for other permission classes that might be blocking"
echo ""
echo "🔧 Debug commands:"
echo "- Check user role: Decode JWT token"
echo "- Check ID card status: GET /api/tenants/{tenant_id}/idcards/{id}/"
echo "- View logs: docker-compose logs backend | grep approval"
echo ""
echo "📝 Permission Summary:"
echo "- submit_for_approval: clerk, superadmin, is_superuser"
echo "- approve: kebele_leader, kebele_admin, subcity_admin, superadmin"
echo "- reject: kebele_leader, kebele_admin, subcity_admin, superadmin"
