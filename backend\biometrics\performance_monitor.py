"""
Performance Monitoring for Duplicate Detection System

This module provides performance monitoring, metrics collection,
and optimization recommendations for the duplicate detection system.
"""

import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from django.db import connection
from django.core.cache import cache
from django.conf import settings
from contextlib import contextmanager

logger = logging.getLogger(__name__)


class PerformanceMonitor:
    """
    Monitor and track performance metrics for duplicate detection operations.
    """
    
    def __init__(self):
        self.metrics = {
            'total_checks': 0,
            'duplicates_found': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'processing_times': [],
            'fmatcher_calls': 0,
            'fmatcher_errors': 0,
            'database_queries': 0,
            'template_extractions': 0
        }
        self.start_time = time.time()
    
    @contextmanager
    def measure_operation(self, operation_name: str):
        """Context manager to measure operation performance"""
        start_time = time.time()
        query_count_before = len(connection.queries)
        
        try:
            yield
        finally:
            end_time = time.time()
            processing_time = end_time - start_time
            query_count_after = len(connection.queries)
            queries_executed = query_count_after - query_count_before
            
            self.metrics['processing_times'].append({
                'operation': operation_name,
                'duration': processing_time,
                'queries': queries_executed,
                'timestamp': datetime.now().isoformat()
            })
            
            logger.debug(f"⏱️ {operation_name}: {processing_time:.3f}s, {queries_executed} queries")
    
    def record_duplicate_check(self, has_duplicates: bool, processing_time: float, cache_hit: bool = False):
        """Record metrics for a duplicate check operation"""
        self.metrics['total_checks'] += 1
        
        if has_duplicates:
            self.metrics['duplicates_found'] += 1
        
        if cache_hit:
            self.metrics['cache_hits'] += 1
        else:
            self.metrics['cache_misses'] += 1
        
        self.metrics['processing_times'].append({
            'operation': 'duplicate_check',
            'duration': processing_time,
            'has_duplicates': has_duplicates,
            'cache_hit': cache_hit,
            'timestamp': datetime.now().isoformat()
        })
    
    def record_fmatcher_call(self, success: bool, processing_time: float):
        """Record metrics for FMatcher operations"""
        self.metrics['fmatcher_calls'] += 1
        
        if not success:
            self.metrics['fmatcher_errors'] += 1
        
        self.metrics['processing_times'].append({
            'operation': 'fmatcher_comparison',
            'duration': processing_time,
            'success': success,
            'timestamp': datetime.now().isoformat()
        })
    
    def record_template_extraction(self, success: bool, template_size: int):
        """Record metrics for template extraction operations"""
        self.metrics['template_extractions'] += 1
        
        self.metrics['processing_times'].append({
            'operation': 'template_extraction',
            'success': success,
            'template_size': template_size,
            'timestamp': datetime.now().isoformat()
        })
    
    def get_performance_summary(self) -> Dict:
        """Get a summary of performance metrics"""
        uptime = time.time() - self.start_time
        
        # Calculate averages
        processing_times = [p['duration'] for p in self.metrics['processing_times']]
        avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0
        
        duplicate_check_times = [
            p['duration'] for p in self.metrics['processing_times'] 
            if p['operation'] == 'duplicate_check'
        ]
        avg_duplicate_check_time = sum(duplicate_check_times) / len(duplicate_check_times) if duplicate_check_times else 0
        
        fmatcher_times = [
            p['duration'] for p in self.metrics['processing_times'] 
            if p['operation'] == 'fmatcher_comparison'
        ]
        avg_fmatcher_time = sum(fmatcher_times) / len(fmatcher_times) if fmatcher_times else 0
        
        # Calculate rates
        checks_per_second = self.metrics['total_checks'] / uptime if uptime > 0 else 0
        cache_hit_rate = (self.metrics['cache_hits'] / 
                         (self.metrics['cache_hits'] + self.metrics['cache_misses'])) if (self.metrics['cache_hits'] + self.metrics['cache_misses']) > 0 else 0
        
        duplicate_rate = (self.metrics['duplicates_found'] / self.metrics['total_checks']) if self.metrics['total_checks'] > 0 else 0
        
        fmatcher_error_rate = (self.metrics['fmatcher_errors'] / self.metrics['fmatcher_calls']) if self.metrics['fmatcher_calls'] > 0 else 0
        
        return {
            'uptime_seconds': uptime,
            'total_operations': len(self.metrics['processing_times']),
            'duplicate_checks': {
                'total': self.metrics['total_checks'],
                'duplicates_found': self.metrics['duplicates_found'],
                'duplicate_rate': duplicate_rate,
                'avg_processing_time': avg_duplicate_check_time,
                'checks_per_second': checks_per_second
            },
            'cache_performance': {
                'hits': self.metrics['cache_hits'],
                'misses': self.metrics['cache_misses'],
                'hit_rate': cache_hit_rate
            },
            'fmatcher_performance': {
                'total_calls': self.metrics['fmatcher_calls'],
                'errors': self.metrics['fmatcher_errors'],
                'error_rate': fmatcher_error_rate,
                'avg_processing_time': avg_fmatcher_time
            },
            'overall_performance': {
                'avg_processing_time': avg_processing_time,
                'total_template_extractions': self.metrics['template_extractions']
            }
        }
    
    def get_performance_recommendations(self) -> List[str]:
        """Generate performance optimization recommendations"""
        recommendations = []
        summary = self.get_performance_summary()
        
        # Cache performance recommendations
        cache_hit_rate = summary['cache_performance']['hit_rate']
        if cache_hit_rate < 0.8:
            recommendations.append(
                f"⚠️ Low cache hit rate ({cache_hit_rate:.1%}). Consider increasing cache timeout or size."
            )
        elif cache_hit_rate > 0.95:
            recommendations.append(
                f"✅ Excellent cache hit rate ({cache_hit_rate:.1%}). Cache is working well."
            )
        
        # Processing time recommendations
        avg_check_time = summary['duplicate_checks']['avg_processing_time']
        if avg_check_time > 5.0:
            recommendations.append(
                f"⚠️ Slow duplicate checks ({avg_check_time:.2f}s avg). Consider optimizing database queries or reducing batch size."
            )
        elif avg_check_time < 1.0:
            recommendations.append(
                f"✅ Fast duplicate checks ({avg_check_time:.2f}s avg). Performance is good."
            )
        
        # FMatcher performance recommendations
        fmatcher_error_rate = summary['fmatcher_performance']['error_rate']
        if fmatcher_error_rate > 0.1:
            recommendations.append(
                f"⚠️ High FMatcher error rate ({fmatcher_error_rate:.1%}). Check template quality and FMatcher configuration."
            )
        
        avg_fmatcher_time = summary['fmatcher_performance']['avg_processing_time']
        if avg_fmatcher_time > 0.5:
            recommendations.append(
                f"⚠️ Slow FMatcher comparisons ({avg_fmatcher_time:.2f}s avg). Consider template optimization or hardware upgrade."
            )
        
        # Duplicate rate recommendations
        duplicate_rate = summary['duplicate_checks']['duplicate_rate']
        if duplicate_rate > 0.05:  # More than 5% duplicates
            recommendations.append(
                f"🚨 High duplicate rate ({duplicate_rate:.1%}). Investigate potential fraud or system issues."
            )
        elif duplicate_rate < 0.001:  # Less than 0.1% duplicates
            recommendations.append(
                f"✅ Low duplicate rate ({duplicate_rate:.1%}). System is working well."
            )
        
        # General recommendations
        if summary['total_operations'] < 10:
            recommendations.append(
                "ℹ️ Limited performance data. Run more operations for better analysis."
            )
        
        return recommendations
    
    def export_metrics(self, format: str = 'json') -> str:
        """Export metrics in the specified format"""
        summary = self.get_performance_summary()
        recommendations = self.get_performance_recommendations()
        
        export_data = {
            'timestamp': datetime.now().isoformat(),
            'performance_summary': summary,
            'recommendations': recommendations,
            'raw_metrics': self.metrics
        }
        
        if format == 'json':
            import json
            return json.dumps(export_data, indent=2)
        elif format == 'csv':
            # Simple CSV export for processing times
            csv_lines = ['operation,duration,timestamp']
            for pt in self.metrics['processing_times']:
                csv_lines.append(f"{pt['operation']},{pt['duration']},{pt['timestamp']}")
            return '\n'.join(csv_lines)
        else:
            raise ValueError(f"Unsupported export format: {format}")
    
    def reset_metrics(self):
        """Reset all metrics to start fresh"""
        self.metrics = {
            'total_checks': 0,
            'duplicates_found': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'processing_times': [],
            'fmatcher_calls': 0,
            'fmatcher_errors': 0,
            'database_queries': 0,
            'template_extractions': 0
        }
        self.start_time = time.time()
        logger.info("🔄 Performance metrics reset")


class DatabasePerformanceAnalyzer:
    """
    Analyze database performance for biometric operations.
    """
    
    @staticmethod
    def analyze_query_performance() -> Dict:
        """Analyze database query performance"""
        with connection.cursor() as cursor:
            # Check index usage
            cursor.execute("""
                SELECT 
                    schemaname,
                    tablename,
                    indexname,
                    idx_scan,
                    idx_tup_read,
                    idx_tup_fetch
                FROM pg_stat_user_indexes 
                WHERE tablename LIKE '%biometric%' OR tablename LIKE '%citizen%'
                ORDER BY idx_scan DESC;
            """)
            
            index_stats = cursor.fetchall()
            
            # Check table sizes
            cursor.execute("""
                SELECT 
                    tablename,
                    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
                    n_tup_ins,
                    n_tup_upd,
                    n_tup_del
                FROM pg_stat_user_tables 
                WHERE tablename LIKE '%biometric%' OR tablename LIKE '%citizen%'
                ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
            """)
            
            table_stats = cursor.fetchall()
            
            return {
                'index_usage': index_stats,
                'table_statistics': table_stats,
                'analysis_timestamp': datetime.now().isoformat()
            }
    
    @staticmethod
    def get_slow_queries() -> List[Dict]:
        """Get slow queries related to biometric operations"""
        # This would require pg_stat_statements extension
        # For now, return placeholder
        return [
            {
                'query': 'SELECT * FROM citizens_biometric WHERE...',
                'avg_time': 1.5,
                'calls': 100,
                'recommendation': 'Add index on fingerprint fields'
            }
        ]
    
    @staticmethod
    def check_database_health() -> Dict:
        """Check overall database health for biometric operations"""
        with connection.cursor() as cursor:
            # Check connection count
            cursor.execute("SELECT count(*) FROM pg_stat_activity;")
            connection_count = cursor.fetchone()[0]
            
            # Check cache hit ratio
            cursor.execute("""
                SELECT 
                    sum(heap_blks_hit) / (sum(heap_blks_hit) + sum(heap_blks_read)) as cache_hit_ratio
                FROM pg_statio_user_tables;
            """)
            
            cache_hit_ratio = cursor.fetchone()[0] or 0
            
            return {
                'connection_count': connection_count,
                'cache_hit_ratio': float(cache_hit_ratio),
                'health_status': 'good' if cache_hit_ratio > 0.9 else 'needs_attention',
                'recommendations': [
                    'Monitor connection pool usage',
                    'Ensure adequate shared_buffers configuration'
                ] if cache_hit_ratio < 0.9 else ['Database performance is good']
            }


# Global performance monitor instance
performance_monitor = PerformanceMonitor()
