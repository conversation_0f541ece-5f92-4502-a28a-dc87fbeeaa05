#!/usr/bin/env python3
"""
Diagnostic Capture Service - Handles all result codes and provides detailed feedback
"""

import logging
import ctypes
from ctypes import cdll, c_int, c_uint, c_void_p, POINTER, byref
import base64
import json
from datetime import datetime
from flask import Flask, jsonify, request

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Constants
FTR_OK = 0
FTR_IMAGE_SIZE = 320 * 480

class DiagnosticFingerprintDevice:
    def __init__(self):
        self.scan_api = None
        self.device_handle = None
        self.connected = False
    
    def initialize(self):
        """Initialize device"""
        try:
            logger.info("🔧 Loading Futronic SDK...")
            self.scan_api = cdll.LoadLibrary('./ftrScanAPI.dll')
            
            # Setup function signatures
            self.scan_api.ftrScanOpenDevice.restype = c_void_p
            self.scan_api.ftrScanCloseDevice.argtypes = [c_void_p]
            self.scan_api.ftrScanCloseDevice.restype = c_int
            self.scan_api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(ctypes.c_ubyte), POINTER(c_uint)]
            self.scan_api.ftrScanGetFrame.restype = c_int
            
            logger.info("📱 Opening device...")
            self.device_handle = self.scan_api.ftrScanOpenDevice()
            
            if self.device_handle:
                logger.info(f"✅ Device opened! Handle: {self.device_handle}")
                self.connected = True
                return True
            else:
                logger.error("❌ Failed to open device")
                return False
                
        except Exception as e:
            logger.error(f"❌ Initialization error: {e}")
            return False
    
    def capture_with_diagnostics(self, thumb_type='left'):
        """Capture with detailed diagnostics"""
        try:
            if not self.connected:
                return {
                    'success': False,
                    'error': 'Device not connected'
                }
            
            logger.info(f"📸 Capturing {thumb_type} thumb...")
            
            # Create buffer
            image_buffer = (ctypes.c_ubyte * FTR_IMAGE_SIZE)()
            frame_size = c_uint(FTR_IMAGE_SIZE)
            
            # Attempt capture
            logger.info("🔍 Attempting frame capture...")
            result = self.scan_api.ftrScanGetFrame(self.device_handle, image_buffer, byref(frame_size))
            
            actual_size = frame_size.value
            logger.info(f"📊 Capture result: code={result}, size={actual_size}")
            
            # Detailed result code analysis
            result_info = self.analyze_result_code(result)
            
            # Always analyze the data we got, even if result code isn't 0
            data_analysis = self.analyze_frame_data(image_buffer, actual_size)
            
            # Determine if this is actually usable data
            is_usable = (actual_size > 1000 and data_analysis['data_percentage'] > 5)
            
            if result == FTR_OK or is_usable:
                # Create template even if result code isn't perfect
                template_data = {
                    'version': '1.0',
                    'thumb_type': thumb_type,
                    'frame_size': actual_size,
                    'quality_score': data_analysis['quality_score'],
                    'capture_time': datetime.now().isoformat(),
                    'device_info': {
                        'model': 'Futronic FS88H',
                        'interface': 'diagnostic',
                        'real_device': True,
                        'result_code': result
                    },
                    'image_hash': data_analysis['image_hash']
                }
                
                template_encoded = base64.b64encode(json.dumps(template_data).encode()).decode()
                
                logger.info(f"✅ Capture successful (result code {result})!")
                logger.info(f"📊 Frame size: {actual_size} bytes")
                logger.info(f"📊 Quality: {data_analysis['quality_score']}%")
                logger.info(f"📊 Data: {data_analysis['data_percentage']:.1f}%")
                
                return {
                    'success': True,
                    'data': {
                        'thumb_type': thumb_type,
                        'template_data': template_encoded,
                        'quality_score': data_analysis['quality_score'],
                        'minutiae_count': max(20, min(60, data_analysis['quality_score'] // 2)),
                        'capture_time': template_data['capture_time'],
                        'device_info': template_data['device_info'],
                        'frame_size': actual_size,
                        'data_percentage': data_analysis['data_percentage'],
                        'result_code': result,
                        'result_message': result_info['message'],
                        'diagnostics': {
                            'avg_pixel': data_analysis['avg_pixel'],
                            'non_zero_pixels': data_analysis['non_zero_pixels'],
                            'is_usable': is_usable
                        }
                    }
                }
            else:
                return {
                    'success': False,
                    'error': result_info['message'],
                    'diagnostics': {
                        'result_code': result,
                        'frame_size': actual_size,
                        'data_percentage': data_analysis['data_percentage'],
                        'avg_pixel': data_analysis['avg_pixel'],
                        'suggestion': result_info['suggestion']
                    }
                }
                
        except Exception as e:
            logger.error(f"❌ Capture error: {e}")
            return {
                'success': False,
                'error': f'Capture exception: {str(e)}'
            }
    
    def analyze_result_code(self, result_code):
        """Analyze Futronic result codes"""
        codes = {
            0: {
                'message': 'Success',
                'suggestion': 'Capture completed successfully'
            },
            1: {
                'message': 'Device error or finger not detected properly',
                'suggestion': 'Try placing finger more firmly on scanner, ensure scanner is clean'
            },
            2: {
                'message': 'No frame available',
                'suggestion': 'Place finger on scanner before capturing'
            },
            3: {
                'message': 'Empty frame',
                'suggestion': 'Ensure finger is properly placed and scanner surface is clean'
            },
            4: {
                'message': 'Finger moved during capture',
                'suggestion': 'Hold finger steady during capture'
            },
            5: {
                'message': 'Poor image quality',
                'suggestion': 'Clean finger and scanner, try different pressure'
            }
        }
        
        return codes.get(result_code, {
            'message': f'Unknown result code: {result_code}',
            'suggestion': 'Check device connection and try again'
        })
    
    def analyze_frame_data(self, image_buffer, frame_size):
        """Analyze captured frame data"""
        try:
            if frame_size < 100:
                return {
                    'quality_score': 0,
                    'data_percentage': 0,
                    'avg_pixel': 0,
                    'non_zero_pixels': 0,
                    'image_hash': 0
                }
            
            # Convert to bytes for analysis
            image_bytes = bytes(image_buffer[:frame_size])
            
            # Sample analysis
            sample_size = min(2000, frame_size)
            sample_pixels = list(image_bytes[:sample_size])
            
            # Calculate statistics
            non_zero_pixels = sum(1 for p in sample_pixels if p > 10)
            data_percentage = (non_zero_pixels / sample_size) * 100
            avg_pixel = sum(sample_pixels) / len(sample_pixels)
            
            # Calculate quality score
            quality_score = min(100, max(0, int(
                (data_percentage * 0.8) + 
                (min(avg_pixel / 3, 30)) +
                (10 if frame_size > 50000 else 0)
            )))
            
            return {
                'quality_score': quality_score,
                'data_percentage': data_percentage,
                'avg_pixel': round(avg_pixel, 1),
                'non_zero_pixels': non_zero_pixels,
                'image_hash': hash(image_bytes) & 0x7FFFFFFF
            }
            
        except Exception as e:
            logger.error(f"Data analysis error: {e}")
            return {
                'quality_score': 0,
                'data_percentage': 0,
                'avg_pixel': 0,
                'non_zero_pixels': 0,
                'image_hash': 0
            }
    
    def get_status(self):
        """Get device status"""
        return {
            'connected': self.connected,
            'handle': self.device_handle,
            'model': 'Futronic FS88H',
            'interface': 'diagnostic'
        }
    
    def close(self):
        """Close device"""
        if self.device_handle and self.scan_api:
            try:
                self.scan_api.ftrScanCloseDevice(self.device_handle)
                logger.info("🔒 Device closed")
            except Exception as e:
                logger.error(f"Close error: {e}")

# Global device
device = DiagnosticFingerprintDevice()

@app.route('/api/device/status', methods=['GET'])
def get_device_status():
    """Get device status"""
    return jsonify({
        'success': True,
        'data': device.get_status()
    })

@app.route('/api/capture/diagnostic', methods=['GET', 'POST'])
def capture_diagnostic():
    """Diagnostic capture with detailed feedback"""
    if request.method == 'GET':
        thumb_type = request.args.get('thumb_type', 'left')
    else:
        data = request.get_json() or {}
        thumb_type = data.get('thumb_type', 'left')
    
    result = device.capture_with_diagnostics(thumb_type)
    
    # Always return 200 OK with detailed diagnostics
    return jsonify(result)

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check"""
    return jsonify({
        'status': 'healthy',
        'service': 'Diagnostic Capture Service',
        'timestamp': datetime.now().isoformat(),
        'device_connected': device.connected
    })

@app.route('/', methods=['GET'])
def index():
    """Web interface"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Diagnostic Fingerprint Capture</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 700px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
            button { padding: 15px 30px; margin: 10px; font-size: 16px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
            button:hover { background: #0056b3; }
            .result { margin: 20px 0; padding: 15px; border-radius: 5px; }
            .success { background: #d4edda; color: #155724; }
            .error { background: #f8d7da; color: #721c24; }
            .warning { background: #fff3cd; color: #856404; }
            .instructions { background: #e7f3ff; color: #004085; padding: 15px; border-radius: 5px; margin: 20px 0; }
            .diagnostics { background: #f8f9fa; padding: 10px; border-radius: 3px; margin: 10px 0; font-family: monospace; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🔍 Diagnostic Fingerprint Capture</h1>
            
            <div class="instructions">
                <h3>📋 Instructions:</h3>
                <ol>
                    <li><strong>Clean the scanner</strong> surface with a soft cloth</li>
                    <li><strong>Clean your finger</strong> (dry, no lotion)</li>
                    <li><strong>Place finger firmly</strong> on the center of the scanner</li>
                    <li><strong>Click capture</strong> while finger is on scanner</li>
                    <li><strong>Hold still</strong> until capture completes</li>
                </ol>
            </div>
            
            <div style="text-align: center;">
                <button onclick="captureFingerprint('left')">👈 Capture Left Thumb</button>
                <button onclick="captureFingerprint('right')">👉 Capture Right Thumb</button>
            </div>
            
            <div id="result"></div>
        </div>
        
        <script>
            async function captureFingerprint(thumbType) {
                const resultDiv = document.getElementById('result');
                resultDiv.innerHTML = '<div class="result">📸 Capturing... Keep finger on scanner!</div>';
                
                try {
                    const response = await fetch(`/api/capture/diagnostic?thumb_type=${thumbType}`);
                    const data = await response.json();
                    
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="result success">
                                <h3>✅ Fingerprint Captured!</h3>
                                <p><strong>Quality:</strong> ${data.data.quality_score}%</p>
                                <p><strong>Frame Size:</strong> ${data.data.frame_size} bytes</p>
                                <p><strong>Data:</strong> ${data.data.data_percentage}%</p>
                                <p><strong>Result Code:</strong> ${data.data.result_code} (${data.data.result_message})</p>
                                <div class="diagnostics">
                                    <strong>Diagnostics:</strong><br>
                                    Average Pixel: ${data.data.diagnostics.avg_pixel}<br>
                                    Non-zero Pixels: ${data.data.diagnostics.non_zero_pixels}<br>
                                    Usable: ${data.data.diagnostics.is_usable ? 'Yes' : 'No'}
                                </div>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="result error">
                                <h3>❌ Capture Issue</h3>
                                <p><strong>Error:</strong> ${data.error}</p>
                                ${data.diagnostics ? `
                                    <div class="diagnostics">
                                        <strong>Diagnostics:</strong><br>
                                        Result Code: ${data.diagnostics.result_code}<br>
                                        Frame Size: ${data.diagnostics.frame_size} bytes<br>
                                        Data: ${data.diagnostics.data_percentage}%<br>
                                        <strong>Suggestion:</strong> ${data.diagnostics.suggestion}
                                    </div>
                                ` : ''}
                            </div>
                        `;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ Network Error</h3>
                            <p>${error.message}</p>
                        </div>
                    `;
                }
            }
        </script>
    </body>
    </html>
    """

if __name__ == '__main__':
    try:
        logger.info("🚀 Starting Diagnostic Capture Service")
        
        # Initialize device
        if device.initialize():
            logger.info("✅ Device ready for diagnostic capture")
        else:
            logger.error("❌ Device initialization failed")
            exit(1)
        
        # Start service
        logger.info("🌐 Service running at http://localhost:8001")
        logger.info("🔍 This service provides detailed diagnostics for troubleshooting")
        
        app.run(host='0.0.0.0', port=8001, debug=False, threaded=True)
        
    except KeyboardInterrupt:
        logger.info("🛑 Service stopped")
    except Exception as e:
        logger.error(f"❌ Service error: {e}")
    finally:
        device.close()
