#!/usr/bin/env python3
"""
GoID Biometric Service - System Tray Application
Professional deployment package for kebele workstations
"""

import sys
import os
import threading
import time
import subprocess
import logging
from datetime import datetime
import webbrowser
from pathlib import Path
import winreg
import ctypes
from ctypes import wintypes

try:
    import pystray
    from pystray import MenuItem as item
    from PIL import Image, ImageDraw
except ImportError:
    print("Installing required packages...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "pystray", "Pillow"])
    import pystray
    from pystray import MenuItem as item
    from PIL import Image, ImageDraw

# Configure logging
log_dir = Path("logs")
log_dir.mkdir(exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_dir / 'tray_service.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BiometricTrayService:
    def __init__(self):
        self.service_process = None
        self.running = False
        self.icon = None
        self.restart_count = 0
        self.start_time = datetime.now()
        self.startup_enabled = self.check_startup_status()
        
    def create_icon_image(self, color="green"):
        """Create system tray icon"""
        # Create a simple fingerprint-like icon
        image = Image.new('RGB', (64, 64), color='white')
        draw = ImageDraw.Draw(image)
        
        if color == "green":
            fill_color = (0, 128, 0)
        elif color == "red":
            fill_color = (255, 0, 0)
        else:
            fill_color = (128, 128, 128)
        
        # Draw fingerprint-like pattern
        draw.ellipse([10, 10, 54, 54], outline=fill_color, width=3)
        draw.ellipse([15, 15, 49, 49], outline=fill_color, width=2)
        draw.ellipse([20, 20, 44, 44], outline=fill_color, width=2)
        draw.ellipse([25, 25, 39, 39], outline=fill_color, width=2)
        
        return image

    def check_startup_status(self):
        """Check if service is set to start with Windows"""
        try:
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER,
                               r"Software\Microsoft\Windows\CurrentVersion\Run",
                               0, winreg.KEY_READ)
            try:
                winreg.QueryValueEx(key, "GoID_Biometric_Service")
                winreg.CloseKey(key)
                return True
            except FileNotFoundError:
                winreg.CloseKey(key)
                return False
        except Exception:
            return False

    def enable_startup(self):
        """Add service to Windows startup"""
        try:
            # Get the path to this script
            script_path = os.path.abspath(__file__)

            # If running as .py file, create a batch wrapper
            if script_path.endswith('.py'):
                batch_path = script_path.replace('.py', '_startup.bat')
                batch_content = f'''@echo off
cd /d "{os.path.dirname(script_path)}"
python "{script_path}"
'''
                with open(batch_path, 'w') as f:
                    f.write(batch_content)
                startup_path = batch_path
            else:
                startup_path = script_path

            # Add to registry
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER,
                               r"Software\Microsoft\Windows\CurrentVersion\Run",
                               0, winreg.KEY_SET_VALUE)
            winreg.SetValueEx(key, "GoID_Biometric_Service", 0, winreg.REG_SZ, startup_path)
            winreg.CloseKey(key)

            self.startup_enabled = True
            logger.info("✅ Added to Windows startup")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to add to startup: {e}")
            return False

    def disable_startup(self):
        """Remove service from Windows startup"""
        try:
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER,
                               r"Software\Microsoft\Windows\CurrentVersion\Run",
                               0, winreg.KEY_SET_VALUE)
            try:
                winreg.DeleteValue(key, "GoID_Biometric_Service")
                logger.info("✅ Removed from Windows startup")
            except FileNotFoundError:
                pass  # Already not in startup
            winreg.CloseKey(key)

            # Remove batch file if it exists
            script_path = os.path.abspath(__file__)
            batch_path = script_path.replace('.py', '_startup.bat')
            if os.path.exists(batch_path):
                os.remove(batch_path)

            self.startup_enabled = False
            return True
        except Exception as e:
            logger.error(f"❌ Failed to remove from startup: {e}")
            return False

    def toggle_startup(self):
        """Toggle Windows startup setting"""
        if self.startup_enabled:
            return self.disable_startup()
        else:
            return self.enable_startup()

    def start_service(self):
        """Start the biometric service"""
        try:
            if self.service_process and self.service_process.poll() is None:
                logger.info("Service already running")
                return True
            
            logger.info("Starting GoID Biometric Service...")
            
            # Get the directory where this script is located
            script_dir = Path(__file__).parent
            service_script = script_dir / "responsive_detection_service.py"
            
            if not service_script.exists():
                logger.error(f"Service script not found: {service_script}")
                return False
            
            # Start the service without console window
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE

            self.service_process = subprocess.Popen(
                [sys.executable, str(service_script)],
                cwd=str(script_dir),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                startupinfo=startupinfo,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            
            # Give service time to start
            time.sleep(3)
            
            if self.service_process.poll() is None:
                logger.info(f"Service started successfully (PID: {self.service_process.pid})")
                self.update_icon("green")
                return True
            else:
                logger.error("Service failed to start")
                self.update_icon("red")
                return False
                
        except Exception as e:
            logger.error(f"Failed to start service: {e}")
            self.update_icon("red")
            return False
    
    def stop_service(self):
        """Stop the biometric service"""
        try:
            if self.service_process:
                logger.info("Stopping GoID Biometric Service...")
                self.service_process.terminate()
                
                # Wait for graceful shutdown
                try:
                    self.service_process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    logger.warning("Force killing service...")
                    self.service_process.kill()
                    self.service_process.wait()
                
                self.service_process = None
                logger.info("Service stopped")
                self.update_icon("gray")
                return True
        except Exception as e:
            logger.error(f"Error stopping service: {e}")
        return False
    
    def restart_service(self):
        """Restart the biometric service"""
        logger.info("Restarting service...")
        self.stop_service()
        time.sleep(2)
        if self.start_service():
            self.restart_count += 1
            logger.info(f"Service restarted successfully (restart #{self.restart_count})")
        else:
            logger.error("Service restart failed")
    
    def is_service_running(self):
        """Check if service is running"""
        return self.service_process and self.service_process.poll() is None
    
    def update_icon(self, color):
        """Update tray icon color"""
        if self.icon:
            self.icon.icon = self.create_icon_image(color)
    
    def open_web_interface(self):
        """Open web interface in browser"""
        try:
            webbrowser.open("http://localhost:8001")
            logger.info("Opened web interface")
        except Exception as e:
            logger.error(f"Failed to open web interface: {e}")
    
    def show_status(self):
        """Show service status"""
        uptime = datetime.now() - self.start_time
        status = "Running" if self.is_service_running() else "Stopped"
        
        message = f"""GoID Biometric Service Status:
        
Status: {status}
Uptime: {str(uptime).split('.')[0]}
Restarts: {self.restart_count}
PID: {self.service_process.pid if self.service_process else 'N/A'}
Web Interface: http://localhost:8001

Service Location: {Path(__file__).parent}
Log File: {Path(__file__).parent / 'logs' / 'tray_service.log'}
        """
        
        # For Windows, show message box
        if os.name == 'nt':
            import ctypes
            ctypes.windll.user32.MessageBoxW(0, message, "GoID Biometric Service", 0)
        else:
            print(message)
    
    def monitor_service(self):
        """Monitor service and restart if crashed"""
        while self.running:
            try:
                if not self.is_service_running():
                    logger.warning("Service not running, attempting restart...")
                    self.start_service()
                
                time.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                logger.error(f"Monitor error: {e}")
                time.sleep(30)  # Wait longer on error
    
    def create_menu(self):
        """Create system tray menu"""
        startup_text = "✓ Start with Windows" if self.startup_enabled else "Start with Windows"

        return pystray.Menu(
            item('GoID Biometric Service', self.show_status, default=True),
            item('Open Web Interface', self.open_web_interface),
            pystray.Menu.SEPARATOR,
            item('Start Service', self.start_service),
            item('Stop Service', self.stop_service),
            item('Restart Service', self.restart_service),
            pystray.Menu.SEPARATOR,
            item(startup_text, self.toggle_startup),
            item('Show Status', self.show_status),
            item('Open Logs Folder', lambda: os.startfile(log_dir) if os.name == 'nt' else None),
            pystray.Menu.SEPARATOR,
            item('Exit', self.quit_application)
        )
    
    def quit_application(self):
        """Quit the tray application"""
        logger.info("Shutting down GoID Biometric Service...")
        self.running = False
        self.stop_service()
        if self.icon:
            self.icon.stop()
    
    def run(self):
        """Run the tray service"""
        try:
            logger.info("Starting GoID Biometric Tray Service")
            logger.info("=" * 50)
            
            self.running = True
            
            # Create system tray icon
            self.icon = pystray.Icon(
                "GoID Biometric",
                self.create_icon_image("gray"),
                "GoID Biometric Service",
                self.create_menu()
            )
            
            # Start service monitoring in background
            monitor_thread = threading.Thread(target=self.monitor_service, daemon=True)
            monitor_thread.start()
            
            # Start the service
            self.start_service()
            
            # Run the tray icon (this blocks)
            self.icon.run()
            
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
        except Exception as e:
            logger.error(f"Tray service error: {e}")
        finally:
            self.quit_application()

def main():
    """Main entry point"""
    # Ensure single instance
    import socket
    try:
        # Try to bind to a port to ensure single instance
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.bind(('localhost', 8002))
    except OSError:
        print("GoID Biometric Service is already running!")
        sys.exit(1)
    
    # Create and run tray service
    tray_service = BiometricTrayService()
    tray_service.run()

if __name__ == '__main__':
    main()
