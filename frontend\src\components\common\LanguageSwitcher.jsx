/**
 * Language Switcher Component
 * Allows users to switch between English and Amharic
 */

import React, { useState } from 'react';
import { useLocalization } from '../../contexts/LocalizationContext';

const LanguageSwitcher = ({ className = '', showLabel = true, size = 'md' }) => {
  const { 
    currentLanguage, 
    changeLanguage, 
    getAvailableLanguages, 
    isChangingLanguage,
    t 
  } = useLocalization();
  
  const [isOpen, setIsOpen] = useState(false);
  const availableLanguages = getAvailableLanguages();

  const handleLanguageChange = async (languageCode) => {
    setIsOpen(false);
    if (languageCode !== currentLanguage) {
      await changeLanguage(languageCode);
    }
  };

  // Handle keyboard navigation
  const handleKeyDown = (event) => {
    if (event.key === 'Escape') {
      setIsOpen(false);
    } else if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      setIsOpen(!isOpen);
    }
  };

  const currentLangInfo = availableLanguages.find(lang => lang.code === currentLanguage);

  // Size classes
  const sizeClasses = {
    sm: 'text-sm px-2 py-1',
    md: 'text-base px-3 py-2',
    lg: 'text-lg px-4 py-3'
  };

  const buttonSizeClass = sizeClasses[size] || sizeClasses.md;

  return (
    <div className={`relative inline-block text-left ${className}`}>
      {/* Language Switcher Button */}
      <button
        type="button"
        className={`
          inline-flex items-center justify-center w-full rounded-md border border-gray-300 
          shadow-sm bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 
          focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200
          ${buttonSizeClass}
          ${isChangingLanguage ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
        onClick={() => setIsOpen(!isOpen)}
        onKeyDown={handleKeyDown}
        disabled={isChangingLanguage}
        aria-haspopup="true"
        aria-expanded={isOpen}
        aria-label={`Current language: ${currentLangInfo?.nativeName || currentLanguage}. Click to change language.`}
      >
        {/* Language Icon */}
        <svg 
          className="w-4 h-4 mr-2 text-gray-500" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" 
          />
        </svg>

        {/* Current Language Display */}
        <span className="text-gray-700 font-medium">
          {showLabel && (
            <>
              <span className="hidden sm:inline">{t('language', 'Language')}: </span>
              <span className="sm:hidden">{t('language', 'Lang')}: </span>
            </>
          )}
          <span className="font-semibold">
            {currentLangInfo?.nativeName || currentLangInfo?.name || currentLanguage.toUpperCase()}
          </span>
        </span>

        {/* Loading Spinner or Dropdown Arrow */}
        {isChangingLanguage ? (
          <svg
            className="animate-spin ml-2 h-4 w-4 text-gray-500"
            fill="none"
            viewBox="0 0 24 24"
            role="status"
            aria-label="Changing language"
          >
            <circle 
              className="opacity-25" 
              cx="12" 
              cy="12" 
              r="10" 
              stroke="currentColor" 
              strokeWidth="4"
            />
            <path 
              className="opacity-75" 
              fill="currentColor" 
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        ) : (
          <svg 
            className="ml-2 h-4 w-4 text-gray-500" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M19 9l-7 7-7-7" 
            />
          </svg>
        )}
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown Panel */}
          <div className="absolute right-0 z-20 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none animate-in fade-in duration-200">
            <div className="py-1" role="menu" aria-label="Language selection menu">
              {availableLanguages.map((language) => (
                <button
                  key={language.code}
                  className={`
                    group flex items-center w-full px-4 py-2 text-sm transition-colors duration-150
                    ${language.code === currentLanguage 
                      ? 'bg-indigo-50 text-indigo-700 font-medium' 
                      : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                    }
                  `}
                  onClick={() => handleLanguageChange(language.code)}
                  role="menuitem"
                  aria-current={language.code === currentLanguage ? 'true' : 'false'}
                >
                  {/* Language Flag/Icon */}
                  <div className={`flex-shrink-0 w-6 h-6 mr-3 rounded-full flex items-center justify-center ${
                    language.code === currentLanguage
                      ? 'bg-gradient-to-br from-indigo-500 to-indigo-700'
                      : 'bg-gradient-to-br from-blue-400 to-blue-600'
                  }`}>
                    <span className="text-white text-xs font-bold">
                      {language.code.toUpperCase()}
                    </span>
                  </div>

                  {/* Language Names */}
                  <div className="flex-1 text-left">
                    <div className="font-medium">
                      {language.nativeName}
                    </div>
                    <div className="text-xs text-gray-500">
                      {language.name}
                    </div>
                  </div>

                  {/* Current Language Indicator */}
                  {language.code === currentLanguage && (
                    <div className="flex-shrink-0 ml-2">
                      <svg className="w-4 h-4 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  )}

                  {/* Current Language Indicator */}
                  {language.code === currentLanguage && (
                    <svg 
                      className="w-4 h-4 text-indigo-600" 
                      fill="currentColor" 
                      viewBox="0 0 20 20"
                    >
                      <path 
                        fillRule="evenodd" 
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" 
                        clipRule="evenodd" 
                      />
                    </svg>
                  )}
                </button>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default LanguageSwitcher;
