/**
 * Translation Error Boundary
 * Catches translation-related errors and provides fallback UI
 */

import React from 'react';

class TranslationErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error for debugging
    console.error('Translation Error Boundary caught an error:', error, errorInfo);
    
    this.setState({
      error: error,
      errorInfo: errorInfo
    });

    // Report to error tracking service if available
    if (window.reportError) {
      window.reportError(error, {
        component: 'TranslationErrorBoundary',
        errorInfo: errorInfo,
        context: 'translation'
      });
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  };

  render() {
    if (this.state.hasError) {
      // Fallback UI
      return (
        <div style={{
          padding: '20px',
          margin: '10px',
          border: '1px solid #dc3545',
          borderRadius: '4px',
          backgroundColor: '#f8d7da',
          color: '#721c24'
        }}>
          <h3 style={{ margin: '0 0 10px 0', fontSize: '16px' }}>
            🌍 Translation Error
          </h3>
          <p style={{ margin: '0 0 10px 0', fontSize: '14px' }}>
            Something went wrong with the translation system. The application is still functional, 
            but some text might not be translated correctly.
          </p>
          
          {process.env.NODE_ENV === 'development' && this.state.error && (
            <details style={{ marginBottom: '10px' }}>
              <summary style={{ cursor: 'pointer', fontSize: '12px' }}>
                Error Details (Development Only)
              </summary>
              <pre style={{
                fontSize: '11px',
                backgroundColor: '#fff',
                padding: '10px',
                borderRadius: '4px',
                overflow: 'auto',
                maxHeight: '200px',
                marginTop: '5px'
              }}>
                {this.state.error.toString()}
                {this.state.errorInfo.componentStack}
              </pre>
            </details>
          )}
          
          <button
            onClick={this.handleRetry}
            style={{
              padding: '8px 16px',
              fontSize: '14px',
              backgroundColor: '#dc3545',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            🔄 Retry
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Higher-order component to wrap components with translation error boundary
 */
export const withTranslationErrorBoundary = (WrappedComponent) => {
  const WithTranslationErrorBoundary = (props) => (
    <TranslationErrorBoundary>
      <WrappedComponent {...props} />
    </TranslationErrorBoundary>
  );

  WithTranslationErrorBoundary.displayName = 
    `withTranslationErrorBoundary(${WrappedComponent.displayName || WrappedComponent.name})`;

  return WithTranslationErrorBoundary;
};

export default TranslationErrorBoundary;
