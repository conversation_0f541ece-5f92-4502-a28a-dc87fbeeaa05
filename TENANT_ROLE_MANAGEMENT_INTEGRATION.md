# Tenant Role Management Integration

## Overview

The existing **Kebele User Management** page has been enhanced with comprehensive role management capabilities. Subcity admins can now manage both users and roles for their child kebeles from a single, integrated interface.

## Enhanced Features

### **1. Tabbed Interface**
The user management page now includes two tabs:
- **Users Tab**: Existing user management functionality
- **Roles Tab**: New role management capabilities

### **2. Role Management Actions**
Three main role management actions are available:

#### **Create Custom Role**
- Create tenant-specific roles for the selected kebele
- Assign specific permissions to the role
- Set hierarchy levels and role types

#### **Assign Role to User**
- Assign existing roles (both legacy and custom) to users
- Select from available roles for the tenant
- Provide reason for role assignment

#### **Grant Individual Permission**
- Grant additional permissions to specific users
- Override role-based permissions
- Temporary or permanent permission grants

### **3. Enhanced User Menu**
The user action menu now includes:
- Edit User (existing)
- Change Password (existing)
- **Assign Role** (new)
- **Grant Permission** (new)

### **4. Available Roles Display**
- Table showing all roles available for the selected kebele
- Distinguishes between global and tenant-specific roles
- Shows role hierarchy levels and types

## Usage Examples

### **Scenario 1: Emergency ID Card Printing**

1. **Select Kebele**: Choose "Kebele 1-A" from the kebele list
2. **Go to Roles Tab**: Click on the "Roles" tab
3. **Create Emergency Role**:
   - Click "Create Role"
   - Role Code: `emergency_coordinator`
   - Role Name: `Emergency Coordinator`
   - Description: `Special role for emergency ID card printing`
   - Level: `50`
   - Permissions: Select `print_id_cards`, `approve_id_cards`
   - Click "Create Role"

4. **Assign to Clerk**:
   - Go back to "Users" tab
   - Click the menu button (⋮) next to a clerk
   - Select "Assign Role"
   - Choose the clerk and the new "Emergency Coordinator" role
   - Provide reason: "Emergency printing authorization"
   - Click "Assign Role"

### **Scenario 2: Temporary Approval Permission**

1. **Select Kebele**: Choose the target kebele
2. **Grant Permission**:
   - Go to "Users" tab
   - Click the menu button (⋮) next to a senior clerk
   - Select "Grant Permission"
   - Choose "Approve ID Cards" permission
   - Provide reason: "Temporary approval authority while leader is away"
   - Click "Grant Permission"

### **Scenario 3: Field Officer Role**

1. **Create Field Officer Role**:
   - Select kebele and go to "Roles" tab
   - Click "Create Role"
   - Role Code: `field_officer`
   - Role Name: `Field Officer`
   - Permissions: `view_citizens`, `create_citizens`, `view_id_cards`
   - Click "Create Role"

2. **Assign to Multiple Users**:
   - Use the "Assign Role" action for each field staff member
   - Select the new "Field Officer" role

## API Integration

The enhanced component integrates with the following API endpoints:

### **Role Management APIs**
```javascript
// Fetch available roles for tenant
GET /auth/tenant-role-management/tenant_roles/?tenant_id=${tenantId}

// Create custom role for tenant
POST /auth/tenant-role-management/create_tenant_role/
{
  "tenant_id": 3,
  "role_data": {
    "codename": "emergency_coordinator",
    "name": "Emergency Coordinator",
    "description": "Special role for emergencies",
    "level": 50,
    "permission_codenames": ["print_id_cards", "approve_id_cards"]
  }
}

// Assign role to user in tenant
POST /auth/tenant-role-management/assign_role_in_tenant/
{
  "tenant_id": 3,
  "user_email": "<EMAIL>",
  "role_codename": "emergency_coordinator_kebele_3",
  "reason": "Emergency authorization"
}

// Grant permission to user in tenant
POST /auth/tenant-role-management/grant_permission_in_tenant/
{
  "tenant_id": 3,
  "user_email": "<EMAIL>",
  "permission_codename": "print_id_cards",
  "reason": "Temporary printing permission"
}
```

### **Permission APIs**
```javascript
// Fetch all available permissions
GET /auth/permissions/
```

## User Interface Flow

### **For Subcity Admins**

1. **Access**: Navigate to "Kebele User Management" page
2. **Select Kebele**: Click on a kebele card to select it
3. **Choose Action**:
   - **User Management**: Use "Users" tab for traditional user operations
   - **Role Management**: Use "Roles" tab for role operations

### **Role Management Workflow**

1. **View Available Roles**: See all roles that can be assigned in the selected kebele
2. **Create Custom Roles**: Create tenant-specific roles with tailored permissions
3. **Assign Roles**: Assign roles to users through the role tab or user menu
4. **Grant Permissions**: Grant individual permissions for specific needs

## Benefits

### **1. Centralized Management**
- Single interface for both user and role management
- No need to navigate between different pages
- Context-aware actions based on selected kebele

### **2. Flexible Role Creation**
- Create roles specific to organizational needs
- Assign precise permissions for each role
- Set appropriate hierarchy levels

### **3. Quick Permission Grants**
- Grant individual permissions without creating roles
- Temporary or permanent permission assignments
- Audit trail for all permission changes

### **4. Intuitive User Experience**
- Familiar interface for existing users
- Clear separation between user and role management
- Contextual actions based on selected tenant

## Security Features

### **1. Tenant Isolation**
- Roles created for one kebele cannot be assigned to users in other kebeles
- Subcity admins can only manage their child kebeles
- Proper validation of tenant hierarchy

### **2. Permission Validation**
- Only authorized users can create roles and assign permissions
- Role hierarchy is enforced
- All actions are logged for audit purposes

### **3. Scope Control**
- Tenant-specific roles are scoped to their tenant
- Global roles can be assigned with proper permissions
- Clear distinction between role types

This enhanced integration provides subcity admins with powerful, flexible role management capabilities while maintaining the familiar user management interface they already know.
