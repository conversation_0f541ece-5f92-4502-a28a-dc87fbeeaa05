/**
 * Localization Context for GoID System
 * Provides translation and language switching functionality
 */

import React, { createContext, useContext, useState, useEffect, useCallback, useMemo } from 'react';
import localizationService from '../services/localizationService';

const LocalizationContext = createContext();

export const useLocalization = () => {
  const context = useContext(LocalizationContext);
  if (!context) {
    throw new Error('useLocalization must be used within a LocalizationProvider');
  }
  return context;
};

export const LocalizationProvider = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState(localizationService.getCurrentLanguage());
  const [isLoading, setIsLoading] = useState(true);
  const [isChangingLanguage, setIsChangingLanguage] = useState(false);
  const [translations, setTranslations] = useState({}); // Store translations in state
  const [translationVersion, setTranslationVersion] = useState(0); // Force re-renders
  const [error, setError] = useState(null);
  const [retryCount, setRetryCount] = useState(0);

  // Cache for translation lookups to improve performance
  const translationCache = useMemo(() => new Map(), [translationVersion]);

  // Initialize localization on mount with retry logic
  useEffect(() => {
    const initializeLocalization = async () => {
      setIsLoading(true);
      setError(null);

      try {
        await localizationService.initialize();
        const language = localizationService.getCurrentLanguage();
        setCurrentLanguage(language);

        // Load current language translations into state
        const initialTranslations = localizationService.translations[language] || {};
        console.log(`🚀 Initializing with ${Object.keys(initialTranslations).length} translations for ${language}`);
        setTranslations(initialTranslations);
        setRetryCount(0); // Reset retry count on success
      } catch (error) {
        console.error('Failed to initialize localization:', error);
        setError(error.message || 'Failed to load translations');

        // Retry logic for network failures
        if (retryCount < 3 && (error.name === 'NetworkError' || error.code === 'NETWORK_ERROR')) {
          console.log(`🔄 Retrying localization initialization (attempt ${retryCount + 1}/3)`);
          setTimeout(() => {
            setRetryCount(prev => prev + 1);
          }, 2000 * (retryCount + 1)); // Exponential backoff
        }
      } finally {
        setIsLoading(false);
      }
    };

    initializeLocalization();
  }, [retryCount]);

  // Update document direction when language changes
  useEffect(() => {
    document.documentElement.dir = localizationService.getDirection();
    document.documentElement.lang = currentLanguage;
  }, [currentLanguage]);

  /**
   * Change language with enhanced error handling and caching
   */
  const changeLanguage = useCallback(async (languageCode) => {
    if (languageCode === currentLanguage) {
      return true;
    }

    console.log(`🔄 Changing language from ${currentLanguage} to ${languageCode}`);
    setIsChangingLanguage(true);
    setError(null);

    try {
      const success = await localizationService.setLanguage(languageCode);
      if (success || true) { // Accept both success and fallback
        setCurrentLanguage(languageCode);

        // Get translations from service after language change
        const newTranslations = localizationService.translations[languageCode] || {};
        console.log(`📚 Setting ${Object.keys(newTranslations).length} translations in state for ${languageCode}`);

        // Update translations in state immediately for re-rendering
        setTranslations(newTranslations);
        setTranslationVersion(prev => prev + 1); // Force re-render and clear cache

        console.log(`✅ Language changed to ${languageCode}, state updated with translations`);

        // Show success message
        if (window.showNotification) {
          window.showNotification(
            localizationService.t('language_changed', 'Language changed successfully'),
            'success'
          );
        }

        return true;
      } else {
        const errorMsg = 'Language change failed';
        console.warn(errorMsg);
        setError(errorMsg);
        return false;
      }
    } catch (error) {
      const errorMsg = `Failed to change language: ${error.message}`;
      console.error(errorMsg, error);
      setError(errorMsg);

      // Show error notification
      if (window.showNotification) {
        window.showNotification(errorMsg, 'error');
      }

      return false;
    } finally {
      setIsChangingLanguage(false);
    }
  }, [currentLanguage]);

  /**
   * Get translation for a key with caching and performance optimization
   */
  const t = useCallback((key, defaultValue = null) => {
    // Check cache first for performance
    const cacheKey = `${currentLanguage}:${key}`;
    if (translationCache.has(cacheKey)) {
      return translationCache.get(cacheKey);
    }

    // Get translation from state
    const translation = translations[key];
    let result;

    if (translation) {
      result = translation;
    } else {
      // Fallback to service if not in state (for backwards compatibility)
      const serviceTranslation = localizationService.t(key, null);
      if (serviceTranslation && serviceTranslation !== key) {
        result = serviceTranslation;
      } else {
        result = defaultValue || key;
        // Only log missing translations in development
        if (process.env.NODE_ENV === 'development') {
          console.warn(`❌ Translation missing: ${key} (lang: ${currentLanguage})`);
        }
      }
    }

    // Cache the result
    translationCache.set(cacheKey, result);
    return result;
  }, [translations, currentLanguage, translationCache]);

  /**
   * Get translation with parameters (enhanced with caching)
   */
  const tp = useCallback((key, params = {}, defaultValue = null) => {
    const baseTranslation = t(key, defaultValue);
    if (!params || Object.keys(params).length === 0) {
      return baseTranslation;
    }

    // Replace parameters in the translation
    let result = baseTranslation;
    Object.entries(params).forEach(([param, value]) => {
      const regex = new RegExp(`{${param}}`, 'g');
      result = result.replace(regex, value);
    });

    return result;
  }, [t]);

  /**
   * Get available languages with caching
   */
  const getAvailableLanguages = useCallback(() => {
    return localizationService.getAvailableLanguages();
  }, []);

  /**
   * Check if current language is RTL
   */
  const isRTL = useCallback(() => {
    return localizationService.isRTL();
  }, []);

  /**
   * Retry initialization manually
   */
  const retryInitialization = useCallback(() => {
    setRetryCount(prev => prev + 1);
  }, []);

  /**
   * Clear translation cache
   */
  const clearCache = useCallback(() => {
    translationCache.clear();
    setTranslationVersion(prev => prev + 1);
  }, [translationCache]);

  /**
   * Get translation statistics
   */
  const getTranslationStats = useCallback(() => {
    const totalKeys = Object.keys(translations).length;
    const translatedKeys = Object.values(translations).filter(v => v && v.trim()).length;

    return {
      total: totalKeys,
      translated: translatedKeys,
      missing: totalKeys - translatedKeys,
      percentage: totalKeys > 0 ? Math.round((translatedKeys / totalKeys) * 100) : 0
    };
  }, [translations]);

  /**
   * Get language direction
   */
  const getDirection = useCallback(() => {
    return localizationService.getDirection();
  }, []);

  /**
   * Get current language info
   */
  const getCurrentLanguageInfo = useCallback(() => {
    const languages = getAvailableLanguages();
    return languages.find(lang => lang.code === currentLanguage) || languages[0];
  }, [currentLanguage, getAvailableLanguages]);

  // Memoize the context value to prevent unnecessary re-renders
  const value = useMemo(() => ({
    // State
    currentLanguage,
    isLoading,
    isChangingLanguage,
    translationVersion,
    error,
    retryCount,

    // Functions
    changeLanguage,
    t,
    tp,
    getAvailableLanguages,
    isRTL,
    getDirection,
    getCurrentLanguageInfo,
    retryInitialization,
    clearCache,
    getTranslationStats,

    // Convenience properties
    isAmharic: currentLanguage === 'am',
    isEnglish: currentLanguage === 'en',
    direction: getDirection(),
    languageInfo: getCurrentLanguageInfo(),
    translationStats: getTranslationStats()
  }), [
    currentLanguage,
    isLoading,
    isChangingLanguage,
    translationVersion,
    error,
    retryCount,
    changeLanguage,
    t,
    tp,
    getAvailableLanguages,
    isRTL,
    getDirection,
    getCurrentLanguageInfo,
    retryInitialization,
    clearCache,
    getTranslationStats
  ]);

  return (
    <LocalizationContext.Provider value={value}>
      {children}
    </LocalizationContext.Provider>
  );
};

export default LocalizationContext;
