@echo off
echo ========================================
echo   Simple Reliable Futronic SDK Service
echo   Direct Integration - No Complexity
echo ========================================
echo.

echo Checking Python installation...
python --version
if errorlevel 1 (
    echo ERROR: Python not found. Please install Python 3.7+
    pause
    exit /b 1
)

echo.
echo Checking required files...
if not exist "ftrScanAPI.dll" (
    echo ERROR: ftrScanAPI.dll not found
    echo Please ensure Futronic SDK files are in the current directory
    pause
    exit /b 1
)

echo SUCCESS: ftrScanAPI.dll found

echo.
echo Installing required Python packages...
pip install flask flask-cors

echo.
echo Starting Simple Reliable Futronic SDK Service...
echo Service will be available at: http://localhost:8001
echo.
echo This service uses direct SDK integration for maximum reliability
echo Press Ctrl+C to stop the service
echo.

python simple_reliable_sdk_service.py

echo.
echo Service stopped.
pause
