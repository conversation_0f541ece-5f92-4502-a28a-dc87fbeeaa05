<!DOCTYPE html>
<html>
<head>
    <title>Test Group-Based Permissions</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .user-section { border: 1px solid #ccc; margin: 10px 0; padding: 15px; }
        .permission { margin: 5px 0; }
        .has-permission { color: green; }
        .no-permission { color: red; }
        .backend-permission { font-size: 0.8em; color: #666; margin-left: 20px; }
    </style>
</head>
<body>
    <h1>Group-Based Permission System Test</h1>
    
    <div class="user-section">
        <h2>Clerk User (<EMAIL>)</h2>
        <p><strong>Expected:</strong> Can view/create citizens and ID cards, cannot approve or manage users</p>
        <div id="clerk-permissions"></div>
    </div>
    
    <div class="user-section">
        <h2>Kebele Leader User (<EMAIL>)</h2>
        <p><strong>Expected:</strong> Can view/create/approve citizens and ID cards, cannot manage users</p>
        <div id="kebele-leader-permissions"></div>
    </div>

    <script>
        // Mock user data based on our API response
        const clerkUser = {
            id: 1,
            email: '<EMAIL>',
            role: 'clerk',
            permissions: [
                { codename: 'add_citizen', name: 'Can add Citizen' },
                { codename: 'change_citizen', name: 'Can change Citizen' },
                { codename: 'view_citizen', name: 'Can view Citizen' },
                { codename: 'add_idcard', name: 'Can add ID Card' },
                { codename: 'change_idcard', name: 'Can change ID Card' },
                { codename: 'view_idcard', name: 'Can view ID Card' },
                // Note: No delete_citizen, delete_idcard, add_user, view_tenant permissions
            ]
        };

        const kebeleLeaderUser = {
            id: 2,
            email: '<EMAIL>',
            role: 'kebele_leader',
            permissions: [
                { codename: 'add_citizen', name: 'Can add Citizen' },
                { codename: 'change_citizen', name: 'Can change Citizen' },
                { codename: 'delete_citizen', name: 'Can delete Citizen' },
                { codename: 'view_citizen', name: 'Can view Citizen' },
                { codename: 'add_idcard', name: 'Can add ID Card' },
                { codename: 'change_idcard', name: 'Can change ID Card' },
                { codename: 'delete_idcard', name: 'Can delete ID Card' },
                { codename: 'view_idcard', name: 'Can view ID Card' },
                // Note: No add_user, view_tenant permissions
            ]
        };

        // Permission mapping (same as frontend)
        const PERMISSION_MAPPING = {
            'view_dashboard': ['view_citizen', 'view_idcard', 'view_user'],
            'view_citizens': ['view_citizen'],
            'manage_citizens': ['add_citizen', 'change_citizen', 'delete_citizen'],
            'register_citizens': ['add_citizen'],
            'view_idcards': ['view_idcard'],
            'manage_idcards': ['add_idcard', 'change_idcard', 'delete_idcard'],
            'create_idcards': ['add_idcard'],
            'approve_idcards': ['delete_citizen', 'delete_idcard'],
            'print_idcards': ['change_idcard'],
            'view_tenants': ['view_tenant'],
            'manage_users': ['add_user', 'change_user', 'view_user'],
            'view_reports': ['delete_citizen', 'delete_idcard', 'view_tenant'],
        };

        function hasPermission(user, permission) {
            if (!user || !user.permissions) return false;
            
            const requiredPermissions = PERMISSION_MAPPING[permission] || [permission];
            const userPermissionCodes = user.permissions.map(p => p.codename);
            return requiredPermissions.some(reqPerm => userPermissionCodes.includes(reqPerm));
        }

        function testUserPermissions(user, containerId) {
            const container = document.getElementById(containerId);
            const testPermissions = [
                'view_dashboard',
                'view_citizens',
                'register_citizens',
                'manage_citizens',
                'view_idcards',
                'create_idcards',
                'approve_idcards',
                'manage_users',
                'view_reports',
                'view_tenants'
            ];

            let html = '<h3>Frontend Permission Tests:</h3>';
            
            testPermissions.forEach(permission => {
                const hasAccess = hasPermission(user, permission);
                const className = hasAccess ? 'has-permission' : 'no-permission';
                const status = hasAccess ? '✅ HAS ACCESS' : '❌ NO ACCESS';
                
                html += `<div class="permission ${className}">
                    <strong>${permission}:</strong> ${status}
                    <div class="backend-permission">Requires: ${PERMISSION_MAPPING[permission] ? PERMISSION_MAPPING[permission].join(', ') : permission}</div>
                </div>`;
            });

            html += '<h3>Backend Permissions:</h3>';
            user.permissions.forEach(perm => {
                html += `<div class="backend-permission">✅ ${perm.codename}: ${perm.name}</div>`;
            });

            container.innerHTML = html;
        }

        // Test both users
        testUserPermissions(clerkUser, 'clerk-permissions');
        testUserPermissions(kebeleLeaderUser, 'kebele-leader-permissions');
    </script>
</body>
</html>
