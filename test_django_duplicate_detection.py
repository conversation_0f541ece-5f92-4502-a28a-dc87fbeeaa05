#!/usr/bin/env python3
"""
Test Django duplicate detection directly
"""

import requests
import json

def test_django_duplicate_endpoint():
    """Test the Django duplicate detection endpoint"""
    print("🧪 Testing Django Duplicate Detection")
    print("=" * 50)
    
    # Sample fingerprint data (matches what frontend sends)
    test_data = {
        "left_thumb_fingerprint": json.dumps({
            "thumb_type": "left",
            "capture_method": "java_bridge", 
            "capture_time": "2025-06-26T14:36:17.256000",
            "ansi_iso_template": "VGVzdCBBTlNJL0lTTyB0ZW1wbGF0ZSBkYXRhIGZvciB0ZXN0aW5n",
            "fingerprint_image": "VGVzdCBpbWFnZSBkYXRh",
            "data_size": 35000,
            "device_info": {
                "model": "Futronic FS88H",
                "interface": "java_bridge",
                "real_device": True,
                "ansi_sdk": True
            }
        }),
        "right_thumb_fingerprint": None
    }
    
    try:
        print("📞 Calling Django duplicate check endpoint...")
        
        # Make request to Django (without auth to see what happens)
        response = requests.post(
            "http://localhost:8000/api/biometrics/check-duplicates/",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📊 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 401:
            print("⚠️ Authentication required - this is expected")
            print("🔍 Response:", response.text)
            print("\n✅ Django endpoint is reachable but requires auth")
            print("   This means the frontend should work with proper auth")
            return True
            
        elif response.status_code == 200:
            result = response.json()
            print("✅ Success! Response:", json.dumps(result, indent=2))
            
            if 'data' in result and 'has_duplicates' in result['data']:
                print(f"🔍 Duplicate check result: {result['data']['has_duplicates']}")
                print(f"🔍 Matcher used: {result['data'].get('matcher', 'Unknown')}")
                return True
            else:
                print("⚠️ Unexpected response format")
                return False
                
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            print("Response:", response.text)
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Django backend at http://localhost:8000")
        print("   Make sure Django is running")
        return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_fmatcher_files():
    """Check if FMatcher files exist in Django backend"""
    print("\n🔍 Checking FMatcher Files...")
    
    import os
    
    fmatcher_path = os.path.join("backend", "fpmatcher", "FMatcher.jar")
    lib_path = os.path.join("backend", "fpmatcher", "lib")
    
    if os.path.exists(fmatcher_path):
        print("✅ FMatcher.jar found in backend")
    else:
        print(f"❌ FMatcher.jar NOT found at {fmatcher_path}")
        
    if os.path.exists(lib_path):
        print("✅ FMatcher lib directory found")
    else:
        print(f"❌ FMatcher lib directory NOT found at {lib_path}")
        
    # Check if Java is available
    try:
        import subprocess
        result = subprocess.run(['java', '-version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Java runtime available")
        else:
            print("❌ Java runtime not available")
    except:
        print("❌ Java runtime not available")

if __name__ == "__main__":
    print("🔍 Debugging GoID Duplicate Detection")
    print("=" * 60)
    
    # Test 1: Check files
    test_fmatcher_files()
    
    # Test 2: Test Django endpoint
    endpoint_ok = test_django_duplicate_endpoint()
    
    print("\n" + "=" * 60)
    print("📋 Debugging Checklist:")
    print("1. ✅ Check Django backend logs for FMatcher messages")
    print("2. ✅ Verify there are existing fingerprints in database")
    print("3. ✅ Check if Django is using updated FMatcher code")
    print("4. ✅ Test with browser's Network tab to see exact API calls")
    
    if not endpoint_ok:
        print("\n⚠️ Django backend issues detected!")
        print("   Please check Django logs and restart with updated code")
    else:
        print("\n✅ Django endpoint is working")
        print("   The issue is likely in FMatcher integration or no existing data")
