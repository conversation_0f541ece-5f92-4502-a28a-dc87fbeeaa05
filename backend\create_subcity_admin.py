#!/usr/bin/env python
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from users.models import User, UserRole
from tenants.models import Tenant

def create_subcity_admin():
    print("=== CHECKING EXISTING USERS ===")
    
    users = User.objects.all()
    for user in users:
        print(f"Email: {user.email}, Role: {user.role}, Tenant: {user.tenant}")
    
    print("\n=== CHECKING TENANTS ===")
    
    tenants = Tenant.objects.all()
    for tenant in tenants:
        print(f"Name: {tenant.name}, Type: {tenant.type}, Schema: {tenant.schema_name}")
    
    # Find a subcity tenant
    subcity_tenant = Tenant.objects.filter(type='subcity').first()
    if not subcity_tenant:
        print("❌ No subcity tenant found!")
        return
    
    print(f"\n✅ Found subcity tenant: {subcity_tenant.name}")
    
    # Check if subcity admin already exists
    subcity_admin = User.objects.filter(role=UserRole.SUBCITY_ADMIN).first()
    if subcity_admin:
        print(f"✅ Subcity admin already exists: {subcity_admin.email}")
        return subcity_admin
    
    # Create subcity admin
    print("🔧 Creating subcity admin user...")
    
    subcity_admin = User.objects.create_user(
        username='subcity_admin',
        email='<EMAIL>',
        password='password123',
        role=UserRole.SUBCITY_ADMIN,
        tenant=subcity_tenant
    )
    
    print(f"✅ Created subcity admin: {subcity_admin.email}")
    return subcity_admin

if __name__ == '__main__':
    create_subcity_admin()
