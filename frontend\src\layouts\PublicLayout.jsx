import { Outlet, useLocation } from 'react-router-dom';
import { Box, CssBaseline, Container } from '@mui/material';
import {
  CreditCard as CardIcon,
  Apps as DefaultIcon
} from '@mui/icons-material';
import Navbar from '../components/navigation/Navbar';
import Banner from '../components/common/Banner';
import Footer from '../components/common/Footer';

const PublicLayout = () => {
  const location = useLocation();

  // Get page title based on current route
  const getPageInfo = () => {
    const path = location.pathname;

    if (path.startsWith('/services/idcard') || path.includes('idcards/services')) {
      return {
        title: 'ID Card Services',
        subtitle: 'Apply for ID card renewal, replacement, and reprint services',
        icon: <CardIcon fontSize="inherit" />
      };
    } else {
      return {
        title: 'GoID System',
        subtitle: 'Digital ID Card Management',
        icon: <DefaultIcon fontSize="inherit" />
      };
    }
  };

  const { title, subtitle, icon } = getPageInfo();

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      <CssBaseline />
      <Navbar />
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: '100%',
        }}
      >
        {/* Banner with no spacing from navbar - full width */}
        <Box
          sx={{
            mt: { xs: 7, sm: 8 }, // Adjusted to connect with navbar
            mb: 3,
            position: 'relative',
          }}
        >
          <Banner title={title} subtitle={subtitle} icon={icon} />
        </Box>

        {/* Main content */}
        <Container maxWidth="xl" sx={{ py: 3 }}>
          <Outlet />
        </Container>
      </Box>
      <Footer />
    </Box>
  );
};

export default PublicLayout;
