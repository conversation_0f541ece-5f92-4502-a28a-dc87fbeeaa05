#!/bin/bash

# Fix Tenant Endpoints 404 Error <PERSON>
# This script fixes the middleware to properly handle tenant management endpoints

echo "🔧 Fixing Tenant Endpoints 404 Errors"
echo "======================================"

# Step 1: Test current state
echo "📍 Step 1: Testing current tenant endpoints..."
python test_all_tenant_endpoints.py

# Step 2: Restart backend service to apply middleware changes
echo ""
echo "📍 Step 2: Restarting backend service to apply middleware changes..."
docker-compose restart backend

# Step 3: Wait for service to restart
echo "⏳ Waiting for backend to restart..."
sleep 30

# Step 4: Test again
echo ""
echo "📍 Step 3: Testing after restart..."
python test_all_tenant_endpoints.py

# Step 5: Check if we need to run migrations
echo ""
echo "📍 Step 4: Checking database and migrations..."
docker-compose exec backend python manage.py migrate_schemas --shared
docker-compose exec backend python manage.py migrate_schemas

# Step 6: Final test
echo ""
echo "📍 Step 5: Final test after migrations..."
python test_all_tenant_endpoints.py

echo ""
echo "✅ Tenant endpoints fix completed!"
echo ""
echo "🔍 Issues Fixed:"
echo "1. ✅ Updated middleware to properly handle tenant management endpoints"
echo "2. ✅ Added regex patterns to distinguish between:"
echo "   - Tenant management endpoints (use public schema)"
echo "   - Tenant-specific data endpoints (use tenant schema)"
echo ""
echo "🌐 Fixed Endpoints:"
echo "   Tenant List: /api/tenants/ (public schema)"
echo "   Subcities: /api/tenants/subcities/ (public schema)"
echo "   Kebeles: /api/tenants/kebeles/ (public schema)"
echo "   Tenant Data: /api/tenants/{id}/citizens/ (tenant schema)"
echo ""
echo "💡 Manual testing:"
echo "1. Open frontend: http://10.139.8.141:3000/"
echo "2. Navigate to Tenants section - should load without 404 errors"
echo "3. Check citizen registration forms - dropdowns should populate"
echo "4. Test API directly:"
echo "   curl http://10.139.8.141:8000/api/tenants/"
echo "   curl http://10.139.8.141:8000/api/tenants/subcities/"
echo ""
echo "💡 If still not working:"
echo "- Check Docker logs: docker-compose logs backend"
echo "- Verify middleware regex patterns are working"
echo "- Ensure public tenant exists with proper domains"
echo "- Check authentication for protected endpoints"
