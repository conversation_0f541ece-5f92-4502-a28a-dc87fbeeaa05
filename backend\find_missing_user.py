#!/usr/bin/env python3

import os
import sys
import django

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from tenants.models import Tenant
from django_tenants.utils import schema_context
from django.db import connection

User = get_user_model()

def find_all_users():
    """Find all users in all schemas."""
    
    print("🔍 Searching for all users...")
    
    # Check public schema first
    print("\n📋 Users in public schema:")
    public_users = User.objects.all()
    print(f"   Found {public_users.count()} users")
    
    for user in public_users:
        print(f"   - {user.username} ({user.email}) - Role: {user.role} - Tenant: {user.tenant}")
        if 'dibora' in user.username.lower() or 'dibora' in user.email.lower():
            print(f"     🎯 FOUND DIBORA USER!")
    
    # Check each tenant schema
    tenants = Tenant.objects.all()
    for tenant in tenants:
        if tenant.schema_name == 'public':
            continue
            
        print(f"\n🏢 Checking {tenant.name} schema ({tenant.schema_name}):")
        
        try:
            with schema_context(tenant.schema_name):
                tenant_users = User.objects.all()
                print(f"   Found {tenant_users.count()} users")
                
                for user in tenant_users:
                    print(f"   - {user.username} ({user.email}) - Role: {user.role}")
                    if 'dibora' in user.username.lower() or 'dibora' in user.email.lower():
                        print(f"     🎯 FOUND DIBORA USER IN {tenant.name}!")
                        
        except Exception as e:
            print(f"   ❌ Error accessing {tenant.schema_name}: {e}")

def search_by_email_pattern():
    """Search for users with email patterns."""
    
    print("\n🔍 Searching by email patterns...")
    
    # Search patterns
    patterns = ['dibora', 'kebele14', 'gmail']
    
    for pattern in patterns:
        print(f"\n🔍 Searching for pattern: {pattern}")
        
        # Public schema
        users = User.objects.filter(email__icontains=pattern)
        if users.exists():
            print(f"   📋 Found {users.count()} users in public schema:")
            for user in users:
                print(f"   - {user.username} ({user.email}) - Role: {user.role}")
        
        # Check tenant schemas
        tenants = Tenant.objects.exclude(schema_name='public')
        for tenant in tenants:
            try:
                with schema_context(tenant.schema_name):
                    tenant_users = User.objects.filter(email__icontains=pattern)
                    if tenant_users.exists():
                        print(f"   📋 Found {tenant_users.count()} users in {tenant.name}:")
                        for user in tenant_users:
                            print(f"   - {user.username} ({user.email}) - Role: {user.role}")
            except Exception as e:
                pass

def create_kebele_leader_user():
    """Create the missing kebele leader user."""
    
    print("\n🔧 Creating kebele leader user...")
    
    try:
        # Get Kebele14 tenant
        kebele14 = Tenant.objects.get(name='Kebele14')
        print(f"✅ Found Kebele14 tenant: {kebele14.name} (ID: {kebele14.id})")
        
        # Check if user already exists
        existing_user = User.objects.filter(email='<EMAIL>').first()
        if existing_user:
            print(f"⚠️ User <NAME_EMAIL> already exists: {existing_user.username}")
            return existing_user
        
        # Create the user
        user = User.objects.create_user(
            username='<EMAIL>',
            email='<EMAIL>',
            password='password123',  # Default password
            first_name='Dibora',
            last_name='Aragaw',
            role='kebele_leader',
            tenant=kebele14,
            is_active=True,
            is_staff=False
        )
        
        print(f"✅ Created kebele leader user:")
        print(f"   Username: {user.username}")
        print(f"   Email: {user.email}")
        print(f"   Role: {user.role}")
        print(f"   Tenant: {user.tenant}")
        print(f"   Is Active: {user.is_active}")
        
        return user
        
    except Tenant.DoesNotExist:
        print("❌ Kebele14 tenant not found")
        return None
    except Exception as e:
        print(f"❌ Error creating user: {e}")
        return None

if __name__ == '__main__':
    print("🚀 Starting user search...")
    
    # Find all users
    find_all_users()
    
    # Search by patterns
    search_by_email_pattern()
    
    # Create missing user
    user = create_kebele_leader_user()
    
    if user:
        print(f"\n✅ Kebele leader user ready: {user.username}")
        print(f"   You can now login with:")
        print(f"   Username: {user.username}")
        print(f"   Password: password123")
    
    print("\n✅ User search completed!")
