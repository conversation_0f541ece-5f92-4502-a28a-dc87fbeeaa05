# pgAdmin Setup Guide for GoID

## 🗄️ **Database Management with pgAdmin**

pgAdmin is included in the production deployment for easy database management.

### **Access Information:**
- **URL**: http://localhost:5050
- **Email**: <EMAIL>
- **Password**: admin123

## 🔧 **Initial Setup**

### **Step 1: Login to pgAdmin**
1. Open browser to: http://localhost:5050
2. Login with: <EMAIL> / admin123

### **Step 2: Add Database Server**
1. Right-click "Servers" in the left panel
2. Select "Register" > "Server..."
3. Configure connection:

**General Tab:**
- Name: `GoID Database`

**Connection Tab:**
- Host name/address: `db`
- Port: `5432`
- Maintenance database: `goid_db`
- Username: `goid_user`
- Password: `goid_password` (or your custom password from .env)

4. Click "Save"

## 📊 **Database Management Tasks**

### **View Tables:**
- Navigate to: Servers > GoID Database > Databases > goid_db > Schemas > public > Tables

### **Common Tables:**
- `tenants_tenant` - Tenant information
- `tenants_citizen` - Citizen records
- `tenants_biometric` - Biometric data
- `tenants_idcard` - ID card records
- `auth_user` - System users

### **Run Queries:**
1. Right-click on `goid_db`
2. Select "Query Tool"
3. Write SQL queries

### **Example Queries:**

**Count Citizens by Tenant:**
```sql
SELECT t.name as tenant_name, COUNT(c.id) as citizen_count
FROM tenants_tenant t
LEFT JOIN tenants_citizen c ON c.tenant_id = t.id
GROUP BY t.name;
```

**View Recent Registrations:**
```sql
SELECT first_name, last_name, digital_id, created_at
FROM tenants_citizen
ORDER BY created_at DESC
LIMIT 10;
```

**Check Biometric Data:**
```sql
SELECT c.first_name, c.last_name, 
       CASE WHEN b.left_thumb_fingerprint IS NOT NULL THEN 'Yes' ELSE 'No' END as left_thumb,
       CASE WHEN b.right_thumb_fingerprint IS NOT NULL THEN 'Yes' ELSE 'No' END as right_thumb
FROM tenants_citizen c
LEFT JOIN tenants_biometric b ON b.citizen_id = c.id;
```

## 🔒 **Security Notes**

### **Production Security:**
- Change default pgAdmin password
- Restrict network access to pgAdmin
- Use strong database passwords
- Enable SSL for database connections

### **Access Control:**
- pgAdmin should only be accessible by administrators
- Consider using VPN for remote access
- Regular backup of database

## 🛠️ **Maintenance Tasks**

### **Database Backup:**
1. Right-click on `goid_db`
2. Select "Backup..."
3. Choose format and location
4. Click "Backup"

### **Restore Database:**
1. Right-click on `goid_db`
2. Select "Restore..."
3. Select backup file
4. Click "Restore"

### **Monitor Performance:**
- Use "Dashboard" tab for real-time stats
- Check "Statistics" for table information
- Monitor "Sessions" for active connections

## 🚨 **Troubleshooting**

### **Cannot Connect to Database:**
- Check if database container is running: `docker-compose ps`
- Verify database credentials in .env file
- Ensure containers are on same network

### **pgAdmin Won't Start:**
- Check port 5050 is not in use
- Verify pgAdmin container logs: `docker-compose logs pgadmin`
- Restart containers: `docker-compose restart`

### **Permission Issues:**
- Check pgAdmin volume permissions
- Restart pgAdmin container
- Clear browser cache

## 📋 **Useful Commands**

### **Docker Commands:**
```bash
# View pgAdmin logs
docker-compose logs pgadmin

# Restart pgAdmin
docker-compose restart pgadmin

# Access pgAdmin container
docker-compose exec pgadmin /bin/bash

# Backup database from command line
docker-compose exec db pg_dump -U goid_user goid_db > backup.sql
```

### **Database Commands:**
```bash
# Connect to database directly
docker-compose exec db psql -U goid_user -d goid_db

# List all databases
docker-compose exec db psql -U goid_user -l

# Check database size
docker-compose exec db psql -U goid_user -d goid_db -c "SELECT pg_size_pretty(pg_database_size('goid_db'));"
```

---

**Note**: pgAdmin provides a powerful web interface for PostgreSQL management. Use it for database administration, monitoring, and maintenance tasks.
