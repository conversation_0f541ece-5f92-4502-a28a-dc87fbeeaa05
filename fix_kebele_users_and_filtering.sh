#!/bin/bash

# Fix Kebele Users and Filtering Issues Script
# This script fixes both the 404 users endpoint and the filtering issue

echo "🔧 Fixing Kebele Users and Filtering Issues"
echo "==========================================="

echo "📍 Issues Found and Fixed:"
echo "1. Users endpoint 404 - Added explicit URL patterns for tenant user actions"
echo "2. Filtering issue - Superuser now respects parent parameter filtering"
echo ""
echo "📍 Changes Applied:"
echo "- Added URL patterns: <int:pk>/users/, <int:pk>/create_user/, <int:pk>/create_admin/"
echo "- Fixed TenantViewSet.get_queryset() to apply filtering before superuser check"
echo "- Added debug logging to track filtering behavior"
echo ""

# Step 1: Restart backend to apply URL and filtering changes
echo "📍 Step 1: Restarting backend to apply URL and filtering changes..."
docker-compose restart backend
sleep 30

# Step 2: Test the fixed endpoints and filtering
echo ""
echo "📍 Step 2: Testing fixed endpoints and filtering..."

echo "Getting authentication token..."
AUTH_RESPONSE=$(curl -s -X POST http://10.139.8.141:8000/api/auth/token/ \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}')

if echo "$AUTH_RESPONSE" | grep -q "access"; then
    echo "✅ Authentication successful"
    
    # Extract token
    TOKEN=$(echo "$AUTH_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin)['access'])" 2>/dev/null)
    
    if [ ! -z "$TOKEN" ]; then
        echo ""
        echo "Testing tenant filtering..."
        
        # Test 1: Get all tenants (should show all)
        echo "1. Testing all tenants: /api/tenants/"
        ALL_TENANTS_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
          http://10.139.8.141:8000/api/tenants/)
        
        ALL_TENANTS_COUNT=$(echo "$ALL_TENANTS_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, list):
        print(len(data))
    elif isinstance(data, dict) and 'results' in data:
        print(len(data.get('results', [])))
    else:
        print('0')
except:
    print('0')
" 2>/dev/null)
        
        echo "   All tenants count: $ALL_TENANTS_COUNT"
        
        # Test 2: Get kebele tenants only (should show only kebeles)
        echo "2. Testing kebele tenants: /api/tenants/?type=kebele"
        KEBELE_TENANTS_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
          "http://10.139.8.141:8000/api/tenants/?type=kebele")
        
        KEBELE_TENANTS_COUNT=$(echo "$KEBELE_TENANTS_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, list):
        print(len(data))
    elif isinstance(data, dict) and 'results' in data:
        print(len(data.get('results', [])))
    else:
        print('0')
except:
    print('0')
" 2>/dev/null)
        
        echo "   Kebele tenants count: $KEBELE_TENANTS_COUNT"
        
        # Test 3: Get tenants with specific parent (should show only children of that parent)
        echo "3. Testing parent filtering: /api/tenants/?type=kebele&parent=1"
        PARENT_FILTERED_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
          "http://10.139.8.141:8000/api/tenants/?type=kebele&parent=1")
        
        PARENT_FILTERED_COUNT=$(echo "$PARENT_FILTERED_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, list):
        print(len(data))
    elif isinstance(data, dict) and 'results' in data:
        print(len(data.get('results', [])))
    else:
        print('0')
except:
    print('0')
" 2>/dev/null)
        
        echo "   Parent filtered count: $PARENT_FILTERED_COUNT"
        
        # Test 4: Test users endpoint for a specific tenant
        if [ "$KEBELE_TENANTS_COUNT" -gt "0" ]; then
            # Get the first kebele tenant ID
            KEBELE_ID=$(echo "$KEBELE_TENANTS_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, list) and len(data) > 0:
        print(data[0]['id'])
    elif isinstance(data, dict) and 'results' in data and len(data['results']) > 0:
        print(data['results'][0]['id'])
    else:
        print('1')
except:
    print('1')
" 2>/dev/null)
            
            echo "4. Testing users endpoint: /api/tenants/$KEBELE_ID/users/"
            USERS_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
              http://10.139.8.141:8000/api/tenants/$KEBELE_ID/users/ \
              -w "HTTP_STATUS:%{http_code}")
            
            HTTP_STATUS=$(echo "$USERS_RESPONSE" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
            RESPONSE_BODY=$(echo "$USERS_RESPONSE" | sed 's/HTTP_STATUS:[0-9]*$//')
            
            echo "   Users endpoint status: $HTTP_STATUS"
            
            if [ "$HTTP_STATUS" = "200" ]; then
                USERS_COUNT=$(echo "$RESPONSE_BODY" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, list):
        print(len(data))
    else:
        print('0')
except:
    print('0')
" 2>/dev/null)
                echo "   ✅ Users endpoint working - found $USERS_COUNT users"
            elif [ "$HTTP_STATUS" = "404" ]; then
                echo "   ❌ Users endpoint still returns 404"
            elif [ "$HTTP_STATUS" = "500" ]; then
                echo "   ⚠️  Users endpoint returns 500 - might be schema issue"
                echo "   Response: $RESPONSE_BODY"
            else
                echo "   ❌ Unexpected status: $HTTP_STATUS"
                echo "   Response: $RESPONSE_BODY"
            fi
            
            # Test 5: Test create_user endpoint
            echo "5. Testing create_user endpoint: /api/tenants/$KEBELE_ID/create_user/"
            CREATE_USER_RESPONSE=$(curl -s -X POST \
              -H "Authorization: Bearer $TOKEN" \
              -H "Content-Type: application/json" \
              -d '{}' \
              http://10.139.8.141:8000/api/tenants/$KEBELE_ID/create_user/ \
              -w "HTTP_STATUS:%{http_code}")
            
            CREATE_USER_STATUS=$(echo "$CREATE_USER_RESPONSE" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
            echo "   Create user endpoint status: $CREATE_USER_STATUS (400 expected for empty data)"
            
            if [ "$CREATE_USER_STATUS" = "400" ] || [ "$CREATE_USER_STATUS" = "403" ]; then
                echo "   ✅ Create user endpoint accessible"
            elif [ "$CREATE_USER_STATUS" = "404" ]; then
                echo "   ❌ Create user endpoint still returns 404"
            else
                echo "   ⚠️  Unexpected status: $CREATE_USER_STATUS"
            fi
        else
            echo "4. ⚠️  No kebele tenants found to test users endpoint"
        fi
        
        echo ""
        echo "📊 Results Summary:"
        echo "- All tenants: $ALL_TENANTS_COUNT"
        echo "- Kebele tenants: $KEBELE_TENANTS_COUNT"
        echo "- Parent filtered: $PARENT_FILTERED_COUNT"
        
        if [ "$PARENT_FILTERED_COUNT" -lt "$KEBELE_TENANTS_COUNT" ]; then
            echo "✅ Parent filtering is working correctly"
        else
            echo "❌ Parent filtering may not be working"
        fi
        
    else
        echo "❌ Could not extract token"
    fi
else
    echo "❌ Authentication failed"
    echo "Response: $AUTH_RESPONSE"
fi

# Step 3: Check backend logs for debug output
echo ""
echo "📍 Step 3: Checking backend logs for debug output..."
docker-compose logs --tail=30 backend | grep -E "TenantViewSet Debug|Query params|Superuser access|tenant" || echo "No debug logs found"

echo ""
echo "✅ Kebele users and filtering fix completed!"
echo ""
echo "🧪 Manual testing steps:"
echo "1. Open: http://10.139.8.141:3000/users/kebele-management"
echo "2. Should see only kebeles that are children of current user's tenant"
echo "3. Click on a kebele to select it"
echo "4. Should see users list for that kebele (not 404 error)"
echo "5. Try creating a new user"
echo ""
echo "🔍 Expected results:"
echo "- ✅ Kebeles list shows only child tenants (not all kebeles)"
echo "- ✅ Users endpoint returns 200 OK (not 404)"
echo "- ✅ Users list loads for selected kebele"
echo "- ✅ Create user functionality works"
echo ""
echo "💡 If still having issues:"
echo "1. Check if user has a tenant assigned"
echo "2. Verify tenant hierarchy (subcity -> kebele)"
echo "3. Check backend logs for debug output"
echo "4. Ensure kebele tenant has proper schema"
echo ""
echo "🔧 Debug commands:"
echo "- Check filtering: curl -H \"Authorization: Bearer TOKEN\" \"http://10.139.8.141:8000/api/tenants/?type=kebele&parent=1\""
echo "- Check users: curl -H \"Authorization: Bearer TOKEN\" http://10.139.8.141:8000/api/tenants/KEBELE_ID/users/"
echo "- View logs: docker-compose logs backend | grep -E 'TenantViewSet|Query params'"
