# Generated by Django 4.2.7 on 2025-06-16 12:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0008_add_allowed_tenant_types_to_tenantgroup'),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='groupmembership',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='groupmembership',
            name='assigned_by_email',
            field=models.EmailField(blank=True, help_text='Email of user who assigned this membership', max_length=254, null=True),
        ),
        migrations.AddField(
            model_name='groupmembership',
            name='user_email',
            field=models.EmailField(default='<EMAIL>', help_text='Email of the user', max_length=254),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='groupmembership',
            name='user_tenant_id',
            field=models.IntegerField(blank=True, help_text="ID of user's tenant", null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='groupmembership',
            name='user_tenant_schema',
            field=models.CharField(blank=True, help_text="Schema name of user's tenant", max_length=100),
        ),
        migrations.AlterUniqueTogether(
            name='groupmembership',
            unique_together={('user_email', 'group')},
        ),
        migrations.RemoveField(
            model_name='groupmembership',
            name='assigned_by',
        ),
        migrations.RemoveField(
            model_name='groupmembership',
            name='user',
        ),
    ]
