import { createContext, useContext, useState, useMemo, useEffect } from 'react';
import { createTheme, ThemeProvider as MuiThemeProvider, alpha } from '@mui/material/styles';
import { publicAxios } from '../utils/axios';

const ThemeContext = createContext();

export const useTheme = () => useContext(ThemeContext);

// Color utility functions
const hexToRgb = (hex) => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
};

const rgbToHex = (r, g, b) => {
  return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
};

const lightenColor = (hex, amount) => {
  const rgb = hexToRgb(hex);
  if (!rgb) return hex;

  const r = Math.min(255, Math.floor(rgb.r + (255 - rgb.r) * amount));
  const g = Math.min(255, Math.floor(rgb.g + (255 - rgb.g) * amount));
  const b = Math.min(255, Math.floor(rgb.b + (255 - rgb.b) * amount));

  return rgbToHex(r, g, b);
};

const darkenColor = (hex, amount) => {
  const rgb = hexToRgb(hex);
  if (!rgb) return hex;

  const r = Math.max(0, Math.floor(rgb.r * (1 - amount)));
  const g = Math.max(0, Math.floor(rgb.g * (1 - amount)));
  const b = Math.max(0, Math.floor(rgb.b * (1 - amount)));

  return rgbToHex(r, g, b);
};

// Define color palettes with dynamic tenant colors
const createLightPalette = (tenantColors) => ({
  primary: {
    main: tenantColors.primary,
    light: lightenColor(tenantColors.primary, 0.3),
    dark: darkenColor(tenantColors.primary, 0.2),
    contrastText: '#ffffff',
  },
  secondary: {
    main: tenantColors.secondary,
    light: lightenColor(tenantColors.secondary, 0.3),
    dark: darkenColor(tenantColors.secondary, 0.2),
    contrastText: '#ffffff',
  },
  success: {
    main: '#06d6a0', // Mint Green
    light: '#56f7d3',
    dark: '#00a571',
  },
  info: {
    main: '#3a86ff', // Bright Blue
    light: '#72aeff',
    dark: '#0061cb',
  },
  warning: {
    main: '#fb8500', // Vibrant Orange
    light: '#ffb347',
    dark: '#c25e00',
  },
  error: {
    main: '#ef476f', // Raspberry
    light: '#ff7a9c',
    dark: '#b91646',
  },
  background: {
    default: '#f8f9fc',
    paper: '#ffffff',
  },
  text: {
    primary: '#2b2d42',
    secondary: '#6c757d',
  },
});

const createDarkPalette = (tenantColors) => ({
  primary: {
    main: tenantColors.primary,
    light: lightenColor(tenantColors.primary, 0.3),
    dark: darkenColor(tenantColors.primary, 0.2),
    contrastText: '#ffffff',
  },
  secondary: {
    main: tenantColors.secondary,
    light: lightenColor(tenantColors.secondary, 0.3),
    dark: darkenColor(tenantColors.secondary, 0.2),
    contrastText: '#ffffff',
  },
  success: {
    main: '#10b981', // Emerald
    light: '#34d399',
    dark: '#059669',
  },
  info: {
    main: '#60a5fa', // Blue
    light: '#93c5fd',
    dark: '#3b82f6',
  },
  warning: {
    main: '#f59e0b', // Amber
    light: '#fbbf24',
    dark: '#d97706',
  },
  error: {
    main: '#f43f5e', // Rose
    light: '#fb7185',
    dark: '#e11d48',
  },
  background: {
    default: '#0f172a', // Slate 900
    paper: '#1e293b', // Slate 800
  },
  text: {
    primary: '#f1f5f9', // Slate 100
    secondary: '#cbd5e1', // Slate 300
  },
});

export const ThemeProvider = ({ children }) => {
  const [mode, setMode] = useState('light');
  const [tenantColors, setTenantColors] = useState({
    primary: '#ff8f00',
    secondary: '#ef6c00'
  });

  const toggleTheme = () => {
    setMode((prevMode) => (prevMode === 'light' ? 'dark' : 'light'));
  };

  // Update tenant colors function
  const updateTenantColors = (colors) => {
    console.log('🎨 Updating tenant colors:', colors);
    setTenantColors(colors);

    // Update CSS custom properties for Tailwind
    if (typeof document !== 'undefined') {
      const root = document.documentElement;
      root.style.setProperty('--primary-800', colors.primary);
      root.style.setProperty('--secondary-800', colors.secondary);

      // Generate lighter/darker variants
      const primaryRgb = hexToRgb(colors.primary);
      const secondaryRgb = hexToRgb(colors.secondary);

      if (primaryRgb) {
        // Generate primary color variants
        root.style.setProperty('--primary-50', lightenColor(colors.primary, 0.9));
        root.style.setProperty('--primary-100', lightenColor(colors.primary, 0.8));
        root.style.setProperty('--primary-200', lightenColor(colors.primary, 0.6));
        root.style.setProperty('--primary-300', lightenColor(colors.primary, 0.4));
        root.style.setProperty('--primary-400', lightenColor(colors.primary, 0.2));
        root.style.setProperty('--primary-500', colors.primary);
        root.style.setProperty('--primary-600', darkenColor(colors.primary, 0.1));
        root.style.setProperty('--primary-700', darkenColor(colors.primary, 0.2));
        root.style.setProperty('--primary-900', darkenColor(colors.primary, 0.4));
      }

      if (secondaryRgb) {
        // Generate secondary color variants
        root.style.setProperty('--secondary-50', lightenColor(colors.secondary, 0.9));
        root.style.setProperty('--secondary-100', lightenColor(colors.secondary, 0.8));
        root.style.setProperty('--secondary-200', lightenColor(colors.secondary, 0.6));
        root.style.setProperty('--secondary-300', lightenColor(colors.secondary, 0.4));
        root.style.setProperty('--secondary-400', lightenColor(colors.secondary, 0.2));
        root.style.setProperty('--secondary-500', colors.secondary);
        root.style.setProperty('--secondary-600', darkenColor(colors.secondary, 0.1));
        root.style.setProperty('--secondary-700', darkenColor(colors.secondary, 0.2));
        root.style.setProperty('--secondary-900', darkenColor(colors.secondary, 0.4));
      }
    }
  };

  // Load system colors on mount
  useEffect(() => {
    const loadSystemColors = async () => {
      try {
        const response = await publicAxios.get('/api/tenants/system-settings/');
        const systemSettings = response.data;

        const newColors = {
          primary: systemSettings.primary_color || '#ff8f00',
          secondary: systemSettings.secondary_color || '#ef6c00'
        };

        console.log('🎨 Loading system colors in ThemeContext:', newColors);
        updateTenantColors(newColors);
      } catch (error) {
        console.error('Error loading system colors:', error);
        // Keep default colors if loading fails
      }
    };

    loadSystemColors();
  }, []); // Run only once on mount

  const theme = useMemo(
    () => {
      const lightPalette = createLightPalette(tenantColors);
      const darkPalette = createDarkPalette(tenantColors);

      return createTheme({
        palette: {
          mode,
          ...(mode === 'light' ? lightPalette : darkPalette),
        },
        typography: {
          fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
          fontWeightLight: 300,
          fontWeightRegular: 400,
          fontWeightMedium: 500,
          fontWeightBold: 700,
          h1: {
            fontSize: '2.5rem',
            fontWeight: 800,
            letterSpacing: '-0.02em',
            lineHeight: 1.2,
          },
          h2: {
            fontSize: '2rem',
            fontWeight: 700,
            letterSpacing: '-0.01em',
            lineHeight: 1.2,
          },
          h3: {
            fontSize: '1.75rem',
            fontWeight: 700,
            letterSpacing: '-0.005em',
            lineHeight: 1.3,
          },
          h4: {
            fontSize: '1.5rem',
            fontWeight: 600,
            letterSpacing: '-0.005em',
            lineHeight: 1.3,
          },
          h5: {
            fontSize: '1.25rem',
            fontWeight: 600,
            letterSpacing: '-0.005em',
            lineHeight: 1.4,
          },
          h6: {
            fontSize: '1rem',
            fontWeight: 600,
            letterSpacing: '-0.005em',
            lineHeight: 1.5,
          },
          subtitle1: {
            fontSize: '1rem',
            fontWeight: 500,
            letterSpacing: '0em',
          },
          subtitle2: {
            fontSize: '0.875rem',
            fontWeight: 500,
            letterSpacing: '0em',
          },
          body1: {
            fontSize: '1rem',
            letterSpacing: '0em',
            lineHeight: 1.5,
          },
          body2: {
            fontSize: '0.875rem',
            letterSpacing: '0em',
            lineHeight: 1.5,
          },
          button: {
            fontSize: '0.875rem',
            fontWeight: 600,
            letterSpacing: '0.01em',
            textTransform: 'none',
          },
        },
        shape: {
          borderRadius: 12,
        },
        shadows: [
          'none',
          '0px 2px 1px -1px rgba(0,0,0,0.06),0px 1px 1px 0px rgba(0,0,0,0.04),0px 1px 3px 0px rgba(0,0,0,0.04)',
          '0px 3px 1px -2px rgba(0,0,0,0.06),0px 2px 2px 0px rgba(0,0,0,0.04),0px 1px 5px 0px rgba(0,0,0,0.04)',
          '0px 3px 3px -2px rgba(0,0,0,0.06),0px 3px 4px 0px rgba(0,0,0,0.04),0px 1px 8px 0px rgba(0,0,0,0.04)',
          '0px 2px 4px -1px rgba(0,0,0,0.06),0px 4px 5px 0px rgba(0,0,0,0.04),0px 1px 10px 0px rgba(0,0,0,0.04)',
          '0px 3px 5px -1px rgba(0,0,0,0.06),0px 5px 8px 0px rgba(0,0,0,0.04),0px 1px 14px 0px rgba(0,0,0,0.04)',
          '0px 3px 5px -1px rgba(0,0,0,0.08),0px 6px 10px 0px rgba(0,0,0,0.06),0px 1px 18px 0px rgba(0,0,0,0.06)',
          '0px 4px 5px -2px rgba(0,0,0,0.08),0px 7px 10px 1px rgba(0,0,0,0.06),0px 2px 16px 1px rgba(0,0,0,0.06)',
          '0px 5px 5px -3px rgba(0,0,0,0.08),0px 8px 10px 1px rgba(0,0,0,0.06),0px 3px 14px 2px rgba(0,0,0,0.06)',
          '0px 5px 6px -3px rgba(0,0,0,0.08),0px 9px 12px 1px rgba(0,0,0,0.06),0px 3px 16px 2px rgba(0,0,0,0.06)',
          '0px 6px 6px -3px rgba(0,0,0,0.08),0px 10px 14px 1px rgba(0,0,0,0.06),0px 4px 18px 3px rgba(0,0,0,0.06)',
          '0px 6px 7px -4px rgba(0,0,0,0.08),0px 11px 15px 1px rgba(0,0,0,0.06),0px 4px 20px 3px rgba(0,0,0,0.06)',
          '0px 7px 8px -4px rgba(0,0,0,0.08),0px 12px 17px 2px rgba(0,0,0,0.06),0px 5px 22px 4px rgba(0,0,0,0.06)',
          '0px 7px 8px -4px rgba(0,0,0,0.08),0px 13px 19px 2px rgba(0,0,0,0.06),0px 5px 24px 4px rgba(0,0,0,0.06)',
          '0px 7px 9px -4px rgba(0,0,0,0.08),0px 14px 21px 2px rgba(0,0,0,0.06),0px 5px 26px 4px rgba(0,0,0,0.06)',
          '0px 8px 9px -5px rgba(0,0,0,0.08),0px 15px 22px 2px rgba(0,0,0,0.06),0px 6px 28px 5px rgba(0,0,0,0.06)',
          '0px 8px 10px -5px rgba(0,0,0,0.08),0px 16px 24px 2px rgba(0,0,0,0.06),0px 6px 30px 5px rgba(0,0,0,0.06)',
          '0px 8px 11px -5px rgba(0,0,0,0.08),0px 17px 26px 2px rgba(0,0,0,0.06),0px 6px 32px 5px rgba(0,0,0,0.06)',
          '0px 9px 11px -5px rgba(0,0,0,0.08),0px 18px 28px 2px rgba(0,0,0,0.06),0px 7px 34px 6px rgba(0,0,0,0.06)',
          '0px 9px 12px -6px rgba(0,0,0,0.08),0px 19px 29px 2px rgba(0,0,0,0.06),0px 7px 36px 6px rgba(0,0,0,0.06)',
          '0px 10px 13px -6px rgba(0,0,0,0.08),0px 20px 31px 3px rgba(0,0,0,0.06),0px 8px 38px 7px rgba(0,0,0,0.06)',
          '0px 10px 13px -6px rgba(0,0,0,0.08),0px 21px 33px 3px rgba(0,0,0,0.06),0px 8px 40px 7px rgba(0,0,0,0.06)',
          '0px 10px 14px -6px rgba(0,0,0,0.08),0px 22px 35px 3px rgba(0,0,0,0.06),0px 8px 42px 7px rgba(0,0,0,0.06)',
          '0px 11px 14px -7px rgba(0,0,0,0.08),0px 23px 36px 3px rgba(0,0,0,0.06),0px 9px 44px 8px rgba(0,0,0,0.06)',
          '0px 11px 15px -7px rgba(0,0,0,0.08),0px 24px 38px 3px rgba(0,0,0,0.06),0px 9px 46px 8px rgba(0,0,0,0.06)',
        ],
        components: {
          MuiCssBaseline: {
            styleOverrides: {
              body: {
                scrollbarWidth: 'thin',
                '&::-webkit-scrollbar': {
                  width: '8px',
                  height: '8px',
                },
                '&::-webkit-scrollbar-track': {
                  background: mode === 'light' ? '#f1f1f1' : '#2d2d2d',
                },
                '&::-webkit-scrollbar-thumb': {
                  background: mode === 'light' ? '#c1c1c1' : '#5c5c5c',
                  borderRadius: '4px',
                },
                '&::-webkit-scrollbar-thumb:hover': {
                  background: mode === 'light' ? '#a8a8a8' : '#6e6e6e',
                },
              },
            },
          },
          MuiButton: {
            styleOverrides: {
              root: {
                borderRadius: 10,
                textTransform: 'none',
                fontWeight: 600,
                boxShadow: 'none',
                transition: 'all 0.2s ease-in-out',
                padding: '8px 20px',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: mode === 'light'
                    ? '0px 6px 15px rgba(67, 97, 238, 0.15), 0px 3px 8px rgba(67, 97, 238, 0.1)'
                    : '0px 6px 15px rgba(99, 102, 241, 0.25), 0px 3px 8px rgba(99, 102, 241, 0.15)',
                },
              },
              contained: {
                '&.Mui-disabled': {
                  backgroundColor: mode === 'light' ? 'rgba(0, 0, 0, 0.12)' : 'rgba(255, 255, 255, 0.12)',
                  color: mode === 'light' ? 'rgba(0, 0, 0, 0.26)' : 'rgba(255, 255, 255, 0.3)',
                },
              },
              outlined: {
                borderWidth: '2px',
                '&:hover': {
                  borderWidth: '2px',
                },
              },
            },
          },
          MuiCard: {
            styleOverrides: {
              root: {
                borderRadius: 16,
                boxShadow: mode === 'light'
                  ? '0px 10px 30px rgba(0, 0, 0, 0.04), 0px 6px 15px rgba(0, 0, 0, 0.03)'
                  : '0px 10px 30px rgba(0, 0, 0, 0.15), 0px 6px 15px rgba(0, 0, 0, 0.1)',
                overflow: 'hidden',
                transition: 'all 0.3s ease',
                border: mode === 'light' ? '1px solid rgba(230, 235, 245, 0.8)' : '1px solid rgba(30, 41, 59, 0.8)',
                '&:hover': {
                  transform: 'translateY(-5px)',
                  boxShadow: mode === 'light'
                    ? '0px 16px 40px rgba(0, 0, 0, 0.06), 0px 8px 20px rgba(0, 0, 0, 0.04)'
                    : '0px 16px 40px rgba(0, 0, 0, 0.25), 0px 8px 20px rgba(0, 0, 0, 0.15)',
                },
              },
            },
          },
          MuiCardHeader: {
            styleOverrides: {
              root: {
                padding: '16px 24px',
              },
              title: {
                fontSize: '1.125rem',
                fontWeight: 600,
              },
            },
          },
          MuiCardContent: {
            styleOverrides: {
              root: {
                padding: '24px',
                '&:last-child': {
                  paddingBottom: '24px',
                },
              },
            },
          },
          MuiPaper: {
            styleOverrides: {
              root: {
                backgroundImage: 'none',
              },
              elevation1: {
                boxShadow: mode === 'light'
                  ? '0px 2px 10px rgba(0, 0, 0, 0.05), 0px 1px 5px rgba(0, 0, 0, 0.035)'
                  : '0px 2px 10px rgba(0, 0, 0, 0.2), 0px 1px 5px rgba(0, 0, 0, 0.1)',
              },
              elevation2: {
                boxShadow: mode === 'light'
                  ? '0px 4px 15px rgba(0, 0, 0, 0.07), 0px 2px 8px rgba(0, 0, 0, 0.035)'
                  : '0px 4px 15px rgba(0, 0, 0, 0.25), 0px 2px 8px rgba(0, 0, 0, 0.15)',
              },
            },
          },
          MuiTableCell: {
            styleOverrides: {
              root: {
                borderBottom: `1px solid ${mode === 'light' ? 'rgba(224, 224, 224, 1)' : 'rgba(81, 81, 81, 1)'}`,
                padding: '16px 24px',
              },
              head: {
                fontWeight: 600,
                backgroundColor: mode === 'light' ? 'rgba(245, 245, 245, 0.6)' : 'rgba(66, 66, 66, 0.6)',
              },
            },
          },
          MuiTableRow: {
            styleOverrides: {
              root: {
                '&:last-child td': {
                  borderBottom: 0,
                },
                '&:hover': {
                  backgroundColor: mode === 'light' ? 'rgba(0, 0, 0, 0.02)' : 'rgba(255, 255, 255, 0.02)',
                },
              },
            },
          },
          MuiInputBase: {
            styleOverrides: {
              root: {
                borderRadius: 8,
                transition: 'all 0.2s ease-in-out',
              },
            },
          },
          MuiOutlinedInput: {
            styleOverrides: {
              root: {
                borderRadius: 8,
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: mode === 'light' ? 'rgba(0, 0, 0, 0.3)' : 'rgba(255, 255, 255, 0.3)',
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderWidth: 2,
                },
              },
              notchedOutline: {
                borderColor: mode === 'light' ? 'rgba(0, 0, 0, 0.2)' : 'rgba(255, 255, 255, 0.2)',
                transition: 'all 0.2s ease-in-out',
              },
            },
          },
          MuiAppBar: {
            styleOverrides: {
              root: {
                boxShadow: mode === 'light'
                  ? '0px 2px 10px rgba(0, 0, 0, 0.05), 0px 1px 5px rgba(0, 0, 0, 0.035)'
                  : '0px 2px 10px rgba(0, 0, 0, 0.2), 0px 1px 5px rgba(0, 0, 0, 0.1)',
              },
            },
          },
          MuiDrawer: {
            styleOverrides: {
              paper: {
                borderRight: 'none',
                boxShadow: mode === 'light'
                  ? '2px 0px 10px rgba(0, 0, 0, 0.05), 1px 0px 5px rgba(0, 0, 0, 0.035)'
                  : '2px 0px 10px rgba(0, 0, 0, 0.2), 1px 0px 5px rgba(0, 0, 0, 0.1)',
              },
            },
          },
          MuiListItemButton: {
            styleOverrides: {
              root: {
                borderRadius: 8,
                margin: '4px 8px',
                padding: '8px 16px',
                '&.Mui-selected': {
                  backgroundColor: mode === 'light' ? 'rgba(255, 143, 0, 0.1)' : 'rgba(255, 143, 0, 0.2)',
                  '&:hover': {
                    backgroundColor: mode === 'light' ? 'rgba(255, 143, 0, 0.15)' : 'rgba(255, 143, 0, 0.25)',
                  },
                },
                '&:hover': {
                  backgroundColor: mode === 'light'
                    ? 'rgba(0, 0, 0, 0.04)'
                    : 'rgba(255, 255, 255, 0.04)',
                },
              },
            },
          },
          MuiDivider: {
            styleOverrides: {
              root: {
                margin: '8px 0',
              },
            },
          },
          MuiAlert: {
            styleOverrides: {
              root: {
                borderRadius: 8,
                boxShadow: mode === 'light'
                  ? '0px 2px 10px rgba(0, 0, 0, 0.05), 0px 1px 5px rgba(0, 0, 0, 0.035)'
                  : '0px 2px 10px rgba(0, 0, 0, 0.2), 0px 1px 5px rgba(0, 0, 0, 0.1)',
              },
            },
          },
        },
      });
    },
    [mode, tenantColors]
  );

  const value = {
    theme,
    mode,
    toggleTheme,
    updateTenantColors,
    tenantColors,
  };

  return (
    <ThemeContext.Provider value={value}>
      <MuiThemeProvider theme={theme}>{children}</MuiThemeProvider>
    </ThemeContext.Provider>
  );
};
