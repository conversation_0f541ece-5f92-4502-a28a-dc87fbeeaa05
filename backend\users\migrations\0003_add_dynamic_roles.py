# Generated manually for dynamic roles system

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0002_add_dynamic_permissions'),
    ]

    operations = [
        migrations.CreateModel(
            name='Role',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('codename', models.CharField(help_text='Unique role identifier', max_length=50, unique=True)),
                ('name', models.Char<PERSON>ield(help_text='Human readable role name', max_length=100)),
                ('description', models.TextField(blank=True, help_text='Detailed description of the role')),
                ('role_type', models.CharField(choices=[('system', 'System Role'), ('administrative', 'Administrative Role'), ('operational', 'Operational Role'), ('custom', 'Custom Role')], default='custom', max_length=20)),
                ('level', models.IntegerField(default=0, help_text='Role hierarchy level (higher = more authority)')),
                ('allowed_tenant_types', models.JSONField(blank=True, default=list, help_text='List of tenant types this role can work in')),
                ('is_active', models.BooleanField(default=True)),
                ('is_built_in', models.BooleanField(default=False, help_text='True for system-defined roles')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('can_manage_roles', models.ManyToManyField(blank=True, help_text='Roles that this role can manage', related_name='managed_by_roles', to='users.role')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_roles', to='users.user')),
            ],
            options={
                'verbose_name': 'Role',
                'verbose_name_plural': 'Roles',
                'ordering': ['-level', 'name'],
            },
        ),
        migrations.AddField(
            model_name='user',
            name='role_obj',
            field=models.ForeignKey(blank=True, help_text='Dynamic role assignment', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='users', to='users.role'),
        ),
        migrations.AlterField(
            model_name='user',
            name='role',
            field=models.CharField(blank=True, choices=[('superadmin', 'Super Admin'), ('city_admin', 'City Admin'), ('subcity_admin', 'Sub City Admin'), ('kebele_admin', 'Kebele Admin'), ('kebele_leader', 'Kebele Leader'), ('clerk', 'Clerk')], default='clerk', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='rolepermission',
            name='role_obj',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='role_permissions', to='users.role'),
        ),
        migrations.AlterField(
            model_name='rolepermission',
            name='role',
            field=models.CharField(blank=True, choices=[('superadmin', 'Super Admin'), ('city_admin', 'City Admin'), ('subcity_admin', 'Sub City Admin'), ('kebele_admin', 'Kebele Admin'), ('kebele_leader', 'Kebele Leader'), ('clerk', 'Clerk')], max_length=50, null=True),
        ),
        migrations.RemoveConstraint(
            model_name='rolepermission',
            name='unique_role_permission',
        ),
        migrations.AddConstraint(
            model_name='rolepermission',
            constraint=models.CheckConstraint(check=models.Q(('role__isnull', False), ('role_obj__isnull', True), _connector='AND') | models.Q(('role__isnull', True), ('role_obj__isnull', False), _connector='AND'), name='role_xor_role_obj'),
        ),
    ]
