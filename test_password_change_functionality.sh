#!/bin/bash

# Test Password Change Functionality Script
# This script tests the complete password change feature for kebele users

echo "🔐 Testing Password Change Functionality"
echo "========================================"

echo "📍 Features Implemented:"
echo "1. ✅ Backend change_user_password action in TenantViewSet"
echo "2. ✅ URL pattern for /api/tenants/{id}/change_user_password/"
echo "3. ✅ Middleware support for password change endpoint"
echo "4. ✅ Frontend password change dialog with validation"
echo "5. ✅ User actions menu with Edit and Change Password options"
echo "6. ✅ Password validation (min 8 chars, confirmation match)"
echo ""

# Step 1: Restart backend to apply all changes
echo "📍 Step 1: Restarting backend to apply all changes..."
docker-compose restart backend
sleep 30

# Step 2: Restart frontend to apply UI changes
echo ""
echo "📍 Step 2: Restarting frontend to apply UI changes..."
docker-compose restart frontend
sleep 20

# Step 3: Test the backend API endpoints
echo ""
echo "📍 Step 3: Testing backend API endpoints..."

echo "Getting authentication token..."
AUTH_RESPONSE=$(curl -s -X POST http://10.139.8.141:8000/api/auth/token/ \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}')

if echo "$AUTH_RESPONSE" | grep -q "access"; then
    echo "✅ Authentication successful"
    
    # Extract token
    TOKEN=$(echo "$AUTH_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin)['access'])" 2>/dev/null)
    
    if [ ! -z "$TOKEN" ]; then
        echo ""
        echo "Testing password change endpoints..."
        
        # Get a kebele to test with
        echo "1. Getting kebele tenants..."
        KEBELE_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
          "http://10.139.8.141:8000/api/tenants/?type=kebele")
        
        KEBELE_ID=$(echo "$KEBELE_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, list) and len(data) > 0:
        print(data[0]['id'])
    elif isinstance(data, dict) and 'results' in data and len(data['results']) > 0:
        print(data['results'][0]['id'])
    else:
        print('none')
except:
    print('none')
" 2>/dev/null)
        
        if [ "$KEBELE_ID" != "none" ]; then
            echo "   Using kebele ID: $KEBELE_ID"
            
            # Get users in this kebele
            echo "2. Getting users in kebele $KEBELE_ID..."
            USERS_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
              http://10.139.8.141:8000/api/tenants/$KEBELE_ID/users/)
            
            USER_ID=$(echo "$USERS_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, list) and len(data) > 0:
        print(data[0]['id'])
    else:
        print('none')
except:
    print('none')
" 2>/dev/null)
            
            if [ "$USER_ID" != "none" ]; then
                echo "   Found user ID to test with: $USER_ID"
                
                # Test change_user_password endpoint
                echo "3. Testing change_user_password endpoint..."
                PASSWORD_RESPONSE=$(curl -s -X PATCH \
                  -H "Authorization: Bearer $TOKEN" \
                  -H "Content-Type: application/json" \
                  -d "{\"user_id\":$USER_ID,\"new_password\":\"newpassword123\",\"confirm_password\":\"newpassword123\"}" \
                  http://10.139.8.141:8000/api/tenants/$KEBELE_ID/change_user_password/ \
                  -w "HTTP_STATUS:%{http_code}")
                
                HTTP_STATUS=$(echo "$PASSWORD_RESPONSE" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
                RESPONSE_BODY=$(echo "$PASSWORD_RESPONSE" | sed 's/HTTP_STATUS:[0-9]*$//')
                
                echo "   Change password status: $HTTP_STATUS"
                
                if [ "$HTTP_STATUS" = "200" ]; then
                    echo "   ✅ Password change endpoint working!"
                    echo "   Response:"
                    echo "$RESPONSE_BODY" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE_BODY"
                elif [ "$HTTP_STATUS" = "403" ]; then
                    echo "   ❌ Permission denied - check user role and permissions"
                elif [ "$HTTP_STATUS" = "404" ]; then
                    echo "   ❌ Endpoint not found - check URL routing"
                elif [ "$HTTP_STATUS" = "400" ]; then
                    echo "   ⚠️  Bad request - check request data format"
                    echo "   Response: $RESPONSE_BODY"
                else
                    echo "   ❌ Unexpected status: $HTTP_STATUS"
                    echo "   Response: $RESPONSE_BODY"
                fi
                
                # Test password validation
                echo ""
                echo "4. Testing password validation..."
                
                # Test short password
                echo "   Testing short password..."
                SHORT_PASS_RESPONSE=$(curl -s -X PATCH \
                  -H "Authorization: Bearer $TOKEN" \
                  -H "Content-Type: application/json" \
                  -d "{\"user_id\":$USER_ID,\"new_password\":\"123\",\"confirm_password\":\"123\"}" \
                  http://10.139.8.141:8000/api/tenants/$KEBELE_ID/change_user_password/ \
                  -w "HTTP_STATUS:%{http_code}")
                
                SHORT_STATUS=$(echo "$SHORT_PASS_RESPONSE" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
                if [ "$SHORT_STATUS" = "400" ]; then
                    echo "   ✅ Short password validation working"
                else
                    echo "   ⚠️  Short password validation may not be working (status: $SHORT_STATUS)"
                fi
                
                # Test mismatched passwords
                echo "   Testing mismatched passwords..."
                MISMATCH_RESPONSE=$(curl -s -X PATCH \
                  -H "Authorization: Bearer $TOKEN" \
                  -H "Content-Type: application/json" \
                  -d "{\"user_id\":$USER_ID,\"new_password\":\"password123\",\"confirm_password\":\"different123\"}" \
                  http://10.139.8.141:8000/api/tenants/$KEBELE_ID/change_user_password/ \
                  -w "HTTP_STATUS:%{http_code}")
                
                MISMATCH_STATUS=$(echo "$MISMATCH_RESPONSE" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
                if [ "$MISMATCH_STATUS" = "400" ]; then
                    echo "   ✅ Password mismatch validation working"
                else
                    echo "   ⚠️  Password mismatch validation may not be working (status: $MISMATCH_STATUS)"
                fi
                
            else
                echo "   ⚠️  No users found in kebele to test with"
                echo "   Create a user first to test password change functionality"
            fi
            
        else
            echo "   ⚠️  No kebele tenants found to test with"
        fi
        
    else
        echo "❌ Could not extract token"
    fi
else
    echo "❌ Authentication failed"
    echo "Response: $AUTH_RESPONSE"
fi

# Step 4: Test URL resolution
echo ""
echo "📍 Step 4: Testing URL resolution..."

docker-compose exec -T backend python manage.py shell << 'EOF'
from django.urls import resolve

print("=== URL RESOLUTION TEST ===")

test_urls = [
    "/api/tenants/1/users/",
    "/api/tenants/1/create_user/",
    "/api/tenants/1/update_user/",
    "/api/tenants/1/change_user_password/",
    "/api/tenants/1/create_admin/",
]

for url in test_urls:
    try:
        match = resolve(url)
        print(f"✅ {url} -> {match.func.__name__}")
    except Exception as e:
        print(f"❌ {url} -> {e}")
EOF

# Step 5: Check backend logs for any errors
echo ""
echo "📍 Step 5: Checking backend logs for errors..."
docker-compose logs --tail=20 backend | grep -E "ERROR|Exception|Traceback|change_user_password" || echo "No relevant logs found"

echo ""
echo "✅ Password change functionality test completed!"
echo ""
echo "🧪 Manual testing steps:"
echo "1. Open: http://10.139.8.141:3000/users/kebele-management"
echo "2. Select a kebele from the left panel"
echo "3. In the users list, click the three dots (⋮) for any user"
echo "4. Select 'Change Password' from the menu"
echo "5. Change Password dialog should open"
echo "6. Enter new password (min 8 chars) and confirm"
echo "7. Click 'Change Password' button"
echo "8. Password should be updated successfully"
echo ""
echo "🔍 Expected results:"
echo "- ✅ Three dots menu appears with Edit and Change Password options"
echo "- ✅ Change Password dialog opens with password fields"
echo "- ✅ Password validation works (min 8 chars, confirmation match)"
echo "- ✅ Password change saves successfully"
echo "- ✅ No 404 or 403 errors"
echo "- ✅ User can login with new password"
echo ""
echo "💡 Features of password change functionality:"
echo "- 🔒 Separate action for security (not in regular edit)"
echo "- ✅ Password confirmation required"
echo "- 🔐 Minimum 8 character validation"
echo "- 🎯 Permission checks for subcity_admin and superadmin"
echo "- 🔄 Real-time validation feedback"
echo "- 🛡️  Secure password hashing with Django's set_password()"
echo ""
echo "🚨 Troubleshooting:"
echo "- If 404 error: Check URL routing and middleware"
echo "- If 403 error: Check user permissions and role"
echo "- If validation errors: Check password length and confirmation"
echo "- If UI not working: Clear browser cache and check console"
echo ""
echo "🔧 Debug commands:"
echo "- Test API: curl -X PATCH -H \"Authorization: Bearer TOKEN\" -H \"Content-Type: application/json\" -d '{\"user_id\":1,\"new_password\":\"newpass123\",\"confirm_password\":\"newpass123\"}' http://10.139.8.141:8000/api/tenants/KEBELE_ID/change_user_password/"
echo "- Check logs: docker-compose logs backend | grep change_user_password"
echo "- Test login: Try logging in with the new password"
