# 🔧 Citizen 403 Forbidden Error Solution

## Problem
Frontend citizen management is failing with 403 (Forbidden) errors on tenant-specific endpoints:
```
Failed to load resource: the server responded with a status of 403 (Forbidden)
************:8000/api/tenants/42/citizens/
************:8000/api/tenants/42/citizens/stats/
```

## Root Cause Analysis
The issue is caused by:
1. **Wrong permission class** - `TenantSpecificCitizenViewSet` was using `CanManageTenants` instead of `CanManageCitizens`
2. **Permission scope mismatch** - `CanManageTenants` is for tenant management (creating tenants), not for accessing citizen data
3. **Role restrictions** - Clerks and kebele leaders need access to citizen data but `CanManageTenants` doesn't allow this

## ✅ Complete Solution Applied

### 1. Fixed Permission Classes
Updated all tenant-specific citizen ViewSets in `backend/tenants/views/tenant_citizen_views.py`:

**Before:**
```python
permission_classes = [permissions.IsAuthenticated, CanManageTenants]
```

**After:**
```python
permission_classes = [permissions.IsAuthenticated, CanManageCitizens]
```

### 2. Updated ViewSets
Fixed permissions for all citizen-related ViewSets:
- `TenantSpecificCitizenViewSet`
- `TenantSpecificEmergencyContactViewSet`
- `TenantSpecificParentViewSet`
- `TenantSpecificChildViewSet`
- `TenantSpecificSpouseViewSet`

### 3. Permission Comparison

#### CanManageTenants (OLD - Wrong for citizen data)
- **Purpose**: Managing tenants (creating/editing tenant entities)
- **Allowed roles**: Superadmin, city_admin, subcity_admin
- **Scope**: Tenant management operations
- **Problem**: Doesn't allow clerks/kebele_leaders to access citizen data

#### CanManageCitizens (NEW - Correct for citizen data)
- **Purpose**: Managing citizen data within tenants
- **Allowed roles**: Superadmin, city_admin, subcity_admin, kebele_admin, kebele_leader, clerk
- **Scope**: Citizen data operations
- **Benefit**: Allows all appropriate roles to access citizen data

## 🚀 Quick Fix Instructions

### Option 1: Use the Fix Script
```bash
chmod +x fix_citizen_permissions.sh
./fix_citizen_permissions.sh
```

### Option 2: Manual Steps
```bash
# 1. Restart backend to apply permission changes
docker-compose restart backend

# 2. Wait for restart
sleep 25

# 3. Test the permissions
python test_citizen_permissions.py

# 4. If needed, reinitialize data
docker-compose exec backend python manage.py init_data
```

## 🧪 Testing the Fix

### 1. Test with Clerk Credentials
```bash
curl -X POST http://************:8000/api/auth/token/ \
  -H "Content-Type: application/json" \
  -d '{"username": "<EMAIL>", "password": "password123"}'
```

Then test citizen access:
```bash
# Replace TOKEN and TENANT_ID with values from login response
curl -H "Authorization: Bearer TOKEN" \
  http://************:8000/api/tenants/TENANT_ID/citizens/
```

Expected: **200 OK** with citizen data (not 403 Forbidden)

### 2. Test Frontend Access
1. Open `http://************:3000/`
2. Login with: `<EMAIL>` / `password123`
3. Navigate to Citizens section
4. Should load without 403 errors

## 🔐 User Roles and Expected Access

### Clerk (`clerk`)
- **Access**: Citizens in their assigned tenant
- **Permissions**: Read, Create citizens
- **Expected Result**: 200 OK for tenant-specific citizen endpoints

### Kebele Leader (`kebele_leader`)
- **Access**: Citizens in their kebele tenant
- **Permissions**: Read, Approve citizen applications
- **Expected Result**: 200 OK for tenant-specific citizen endpoints

### Kebele Admin (`kebele_admin`)
- **Access**: Citizens in their kebele tenant
- **Permissions**: Full CRUD operations
- **Expected Result**: 200 OK for tenant-specific citizen endpoints

### Subcity Admin (`subcity_admin`)
- **Access**: Citizens in their subcity and child kebeles
- **Permissions**: Full CRUD operations
- **Expected Result**: 200 OK for tenant-specific citizen endpoints

### Superadmin (`superadmin`)
- **Access**: All citizens across all tenants
- **Permissions**: Full access to everything
- **Expected Result**: 200 OK for all endpoints

## 🔍 Verification Steps

1. **Login Works**: Can login with tenant user credentials
2. **Citizen List Access**: `/api/tenants/{tenant_id}/citizens/` returns 200
3. **Citizen Stats Access**: `/api/tenants/{tenant_id}/citizens/stats/` returns 200
4. **Frontend Navigation**: Citizens section loads without errors
5. **Role-based Access**: Each role can access appropriate data

## 🆘 Troubleshooting

### Issue: Still getting 403 on citizen endpoints
**Solutions:**
1. Check Django logs: `docker-compose logs backend`
2. Verify permission changes: Look for `CanManageCitizens` in logs
3. Restart services: `docker-compose restart backend`
4. Check user role: Ensure user has appropriate role (clerk, kebele_leader, etc.)

### Issue: User has wrong role or no tenant
**Solutions:**
1. Check user in admin panel: `http://************:8000/admin/`
2. Verify user has correct role and tenant assignment
3. Run init_data: `docker-compose exec backend python manage.py init_data`
4. Check JWT token contains correct role and tenant_id

### Issue: Tenant ID mismatch
**Solutions:**
1. Verify user's tenant matches the tenant in URL
2. Check tenant hierarchy (city admin can access subcity/kebele data)
3. Ensure tenant exists and user has access to it
4. Check tenant assignment in user profile

### Issue: Permission class not updated
**Solutions:**
1. Verify file changes: Check `backend/tenants/views/tenant_citizen_views.py`
2. Restart Django: `docker-compose restart backend`
3. Check imports: Ensure `CanManageCitizens` is imported
4. Clear Python cache: `docker-compose build --no-cache backend`

## 📋 Expected Results After Fix

- ✅ **Clerk access** to citizens in their tenant (200, not 403)
- ✅ **Kebele leader access** to citizens in their kebele (200, not 403)
- ✅ **Admin access** to citizens in their hierarchy (200, not 403)
- ✅ **Frontend citizen list** loads without errors
- ✅ **Citizen stats** accessible to appropriate roles
- ✅ **Proper role-based filtering** of citizen data

## 🔧 Manual Debugging

If issues persist, debug manually:

```bash
# Enter backend container
docker-compose exec backend bash

# Test Django shell
python manage.py shell

# In shell, test permissions:
from users.models import User
from tenants.models import Tenant
from common.permissions import CanManageCitizens

# Get test user
user = User.objects.get(username='<EMAIL>')
print(f"User role: {user.role}")
print(f"User tenant: {user.tenant}")

# Test permission
from django.test import RequestFactory
from rest_framework.test import force_authenticate

factory = RequestFactory()
request = factory.get('/api/tenants/1/citizens/')
force_authenticate(request, user=user)

permission = CanManageCitizens()
has_perm = permission.has_permission(request, None)
print(f"Has permission: {has_perm}")
```

## 📞 Support

If citizen permissions are still not working after following this guide:
1. Provide output of `docker-compose logs backend`
2. Share results of `python test_citizen_permissions.py`
3. Include user role and tenant information from JWT token
4. Confirm which specific endpoint is returning 403

The solution addresses the core permission class mismatch that was preventing tenant users from accessing citizen data within their assigned tenants.
