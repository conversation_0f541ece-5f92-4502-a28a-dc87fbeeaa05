# GoID System Deployment Architecture

## 🏗️ **System Architecture**

### **SERVER MACHINE (Central/Cloud)**
```
┌─────────────────────────────────────────┐
│             SERVER                      │
├─────────────────────────────────────────┤
│ 🌐 React App (via Django/nginx)        │
│ 🗄️ Django Backend + PostgreSQL        │
│ 🔍 FMatcher.jar (duplicate detection)  │
│ 📊 All business logic & storage        │
└─────────────────────────────────────────┘
```

### **CLIENT MACHINES (Kebele PCs)**
```
┌─────────────────────────────────────────┐
│           KEBELE PC                     │
├─────────────────────────────────────────┤
│ 🖱️ Browser (accesses server web app)   │
│ 📱 GonderFingerPrint.jar + Futronic SDK│
│ 🔗 Minimal capture service (port 8002) │
│ 🖨️ Futronic FS88H device (USB)        │
└─────────────────────────────────────────┘
```

## 🔄 **Data Flow**

1. **User opens browser** → Accesses React app from **SERVER**
2. **Click "Capture Fingerprint"** → React calls **LOCAL capture service**
3. **Local service** → GonderFingerPrint.jar → Returns template to React
4. **React sends template** → **SERVER Django backend**
5. **Django** → FMatcher duplicate detection against **database**
6. **Django responds** → React shows duplicate alert or saves data

## 📂 **File Distribution**

### **SERVER Files:**
```
server/
├── frontend/ (React build files)
├── backend/
│   ├── django project files
│   ├── fpmatcher/
│   │   ├── FMatcher.jar
│   │   └── lib/
│   └── database (PostgreSQL)
└── requirements: Python, Django, PostgreSQL, Java
```

### **CLIENT Files:**
```
kebele-pc/
├── fingerPrint/
│   ├── GonderFingerPrint.jar
│   ├── ftrScanAPI.dll
│   ├── ftrAnsiSdk.dll
│   ├── ftrAnsiSDKJni.dll
│   └── lib/
├── minimal_capture_service.py
└── requirements: Python, Java, USB drivers
```

## 🚀 **Deployment Steps**

### **1. SERVER Setup:**
```bash
# Install dependencies
apt-get update
apt-get install python3 postgresql nginx java-11-openjdk

# Deploy Django + React
git clone <repo>
pip install -r requirements.txt
python manage.py migrate
python manage.py collectstatic

# Copy FMatcher to backend
cp -r fpmatcher/ backend/

# Start services
systemctl start postgresql
systemctl start nginx
python manage.py runserver
```

### **2. CLIENT Setup (Each Kebele PC):**
```bash
# Install dependencies
# - Python 3.x
# - Java Runtime
# - Futronic device drivers

# Copy client files
mkdir kebele-fingerprint-service
copy fingerPrint/ kebele-fingerprint-service/
copy minimal_capture_service.py kebele-fingerprint-service/

# Start capture service
cd kebele-fingerprint-service
python minimal_capture_service.py

# Service runs on http://localhost:8002
```

### **3. Browser Access:**
- Open browser on kebele PC
- Navigate to: `http://server-ip/` 
- Login and use normally
- Fingerprint capture uses local service automatically

## 🔧 **Configuration**

### **Frontend Environment (.env.production):**
```env
# React app served from server
VITE_API_BASE_URL=http://server-ip:8000
VITE_BIOMETRIC_SERVICE_URL=http://localhost:8002
```

### **Client Service (minimal_capture_service.py):**
- Port: 8002 (matches frontend config)
- Function: Capture only
- No duplicate detection logic
- Returns template data to browser

### **Server Django (settings.py):**
- FMatcher integration in fingerprint_processing.py
- Database stores all templates
- Handles all business logic

## 🔒 **Security Considerations**

1. **Client service** only accessible from localhost
2. **Server** handles authentication & authorization  
3. **Templates** sent via HTTPS to server
4. **No sensitive data** stored on client machines

## 📊 **Benefits**

✅ **Centralized Logic** - All business rules on server  
✅ **Offline Capable** - Client capture works without internet  
✅ **Scalable** - Add kebele PCs easily  
✅ **Secure** - No sensitive data on clients  
✅ **Maintainable** - Updates only on server  
✅ **Cross-Platform** - Browser-based access  

## 🧪 **Testing**

1. **Start server** with Django + React
2. **Start client service** on kebele PC
3. **Open browser** → server web app
4. **Register citizen** with fingerprint
5. **Try duplicate registration** → should detect fraud
6. **Check server logs** for duplicate detection messages

This architecture ensures your system works efficiently across multiple kebele locations while maintaining security and performance.
