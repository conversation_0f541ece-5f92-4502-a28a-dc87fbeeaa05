from django.core.management.base import BaseCommand
from django_tenants.utils import schema_context
from tenants.models import Tenant
from tenants.models.citizen import Citizen
from idcards.models import IDCard, IDCardTemplate
from datetime import date, timedelta


class Command(BaseCommand):
    help = 'Test direct ID card creation using Django ORM'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🧪 Testing direct ID card creation...'))
        
        try:
            # Get tenant 42
            tenant = Tenant.objects.get(id=42)
            self.stdout.write(f'📋 Tenant: {tenant.name} ({tenant.schema_name})')
            
            with schema_context(tenant.schema_name):
                self.stdout.write(f'🔍 Inside schema context: {tenant.schema_name}')
                
                # Try to get citizen 13 using ORM
                try:
                    citizen = Citizen.objects.get(id=13)
                    self.stdout.write(f'✅ Found citizen via ORM: {citizen.get_full_name()} (ID: {citizen.id})')
                except Citizen.DoesNotExist:
                    self.stdout.write(f'❌ Citizen 13 not found via ORM')
                    return
                
                # Try to get a template
                try:
                    template = IDCardTemplate.objects.filter(is_default=True).first()
                    if not template:
                        template = IDCardTemplate.objects.first()
                    
                    if template:
                        self.stdout.write(f'✅ Found template: {template.name} (ID: {template.id})')
                    else:
                        self.stdout.write(f'❌ No templates found')
                        return
                except Exception as e:
                    self.stdout.write(f'❌ Error getting template: {str(e)}')
                    return
                
                # Try to create ID card directly
                try:
                    self.stdout.write(f'🔧 Attempting to create ID card...')
                    
                    # Check if citizen already has an ID card
                    existing_cards = IDCard.objects.filter(citizen=citizen)
                    if existing_cards.exists():
                        self.stdout.write(f'⚠️ Citizen already has {existing_cards.count()} ID card(s)')
                        for card in existing_cards:
                            self.stdout.write(f'    - Card: {card.card_number} (Status: {card.status})')
                        
                        # Delete existing cards for testing
                        self.stdout.write(f'🗑️ Deleting existing cards for testing...')
                        existing_cards.delete()
                    
                    # Create new ID card
                    id_card = IDCard.objects.create(
                        citizen=citizen,
                        template=template,
                        issue_date=date.today(),
                        expiry_date=date.today() + timedelta(days=365*5),  # 5 years
                        status='draft'
                    )
                    
                    self.stdout.write(f'✅ ID card created successfully!')
                    self.stdout.write(f'    - Card Number: {id_card.card_number}')
                    self.stdout.write(f'    - Citizen: {id_card.citizen.get_full_name()}')
                    self.stdout.write(f'    - Template: {id_card.template.name}')
                    self.stdout.write(f'    - Status: {id_card.status}')
                    self.stdout.write(f'    - Issue Date: {id_card.issue_date}')
                    self.stdout.write(f'    - Expiry Date: {id_card.expiry_date}')
                    
                except Exception as e:
                    self.stdout.write(f'❌ Error creating ID card: {str(e)}')
                    import traceback
                    self.stdout.write(traceback.format_exc())
                    return
                
        except Tenant.DoesNotExist:
            self.stdout.write(self.style.ERROR('❌ Tenant 42 not found'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Error: {str(e)}'))
            import traceback
            self.stdout.write(traceback.format_exc())
        
        self.stdout.write(self.style.SUCCESS('\n🎉 Test completed!'))
