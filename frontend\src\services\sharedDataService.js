import axios from 'axios';

// Create a separate axios instance for public/shared data that doesn't include auth headers
const publicAxios = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:8000',
  headers: {
    'Content-Type': 'application/json',
  },
  // No auth interceptors - this is for public data
});

/**
 * Service for fetching shared data for dropdowns
 */
const sharedDataService = {
  /**
   * Fetch countries
   * @returns {Promise} Promise with countries data
   */
  getCountries: async () => {
    try {
      const response = await publicAxios.get('/api/shared/countries/');
      return response.data || [];
    } catch (error) {
      console.error('Error fetching countries:', error);
      throw error;
    }
  },

  /**
   * Fetch regions
   * @param {string} countryId - Optional country ID to filter regions
   * @returns {Promise} Promise with regions data
   */
  getRegions: async (countryId) => {
    try {
      const url = countryId
        ? `/api/shared/regions/?country=${countryId}`
        : '/api/shared/regions/';
      const response = await publicAxios.get(url);
      return response.data || [];
    } catch (error) {
      console.error('Error fetching regions:', error);
      throw error;
    }
  },

  /**
   * Fetch religions
   * @returns {Promise} Promise with religions data
   */
  getReligions: async () => {
    try {
      const response = await publicAxios.get('/api/shared/religions/');
      return response.data || [];
    } catch (error) {
      console.error('Error fetching religions:', error);
      throw error;
    }
  },

  /**
   * Fetch citizen statuses
   * @returns {Promise} Promise with citizen statuses data
   */
  getCitizenStatuses: async () => {
    try {
      const response = await publicAxios.get('/api/shared/citizen-statuses/');
      return response.data || [];
    } catch (error) {
      console.error('Error fetching citizen statuses:', error);
      throw error;
    }
  },

  /**
   * Fetch marital statuses
   * @returns {Promise} Promise with marital statuses data
   */
  getMaritalStatuses: async () => {
    try {
      const response = await publicAxios.get('/api/shared/marital-statuses/');
      return response.data || [];
    } catch (error) {
      console.error('Error fetching marital statuses:', error);
      throw error;
    }
  },

  /**
   * Fetch document types
   * @returns {Promise} Promise with document types data
   */
  getDocumentTypes: async () => {
    try {
      const response = await publicAxios.get('/api/shared/document-types/');
      return response.data || [];
    } catch (error) {
      console.error('Error fetching document types:', error);
      throw error;
    }
  },

  /**
   * Fetch employment types
   * @returns {Promise} Promise with employment types data
   */
  getEmploymentTypes: async () => {
    try {
      const response = await publicAxios.get('/api/shared/employment-types/');
      return response.data || [];
    } catch (error) {
      console.error('Error fetching employment types:', error);
      throw error;
    }
  },

  /**
   * Fetch relationships
   * @returns {Promise} Promise with relationships data
   */
  getRelationships: async () => {
    try {
      const response = await publicAxios.get('/api/shared/relationships/');
      return response.data || [];
    } catch (error) {
      console.error('Error fetching relationships:', error);
      throw error;
    }
  },

  /**
   * Fetch current statuses
   * @returns {Promise} Promise with current statuses data
   */
  getCurrentStatuses: async () => {
    try {
      const response = await publicAxios.get('/api/shared/current-statuses/');
      return response.data || [];
    } catch (error) {
      console.error('Error fetching current statuses:', error);
      throw error;
    }
  },

  /**
   * Fetch biometric types
   * @returns {Promise} Promise with biometric types data
   */
  getBiometricTypes: async () => {
    try {
      const response = await publicAxios.get('/api/shared/biometric-types/');
      return response.data || [];
    } catch (error) {
      console.error('Error fetching biometric types:', error);
      throw error;
    }
  },

  /**
   * Get subcities from JWT token (user's tenant hierarchy)
   * @returns {Promise} Promise with subcities data extracted from token
   */
  getSubcities: async () => {
    try {
      const token = localStorage.getItem('accessToken');
      if (!token) {
        console.log('🔍 No auth token available for subcities, returning empty array');
        return [];
      }

      // Decode JWT token to extract tenant information
      const payload = JSON.parse(atob(token.split('.')[1]));
      console.log('🔍 Token payload for subcities:', payload);

      const subcities = [];

      // If user is from a subcity or kebele, add their subcity
      if (payload.tenant_type === 'subcity') {
        subcities.push({
          id: payload.tenant_id,
          name: payload.tenant_name,
          tenant_type: 'subcity'
        });
      } else if (payload.tenant_type === 'kebele' && payload.parent_tenant_id) {
        subcities.push({
          id: payload.parent_tenant_id,
          name: payload.parent_tenant_name,
          tenant_type: 'subcity'
        });
      }

      console.log('🔍 Extracted subcities from token:', subcities);
      return subcities;
    } catch (error) {
      console.error('Error extracting subcities from token:', error);
      return [];
    }
  },

  /**
   * Get kebeles from JWT token (user's tenant hierarchy)
   * @returns {Promise} Promise with kebeles data extracted from token
   */
  getKebeles: async () => {
    try {
      const token = localStorage.getItem('accessToken');
      if (!token) {
        console.log('🔍 No auth token available for kebeles, returning empty array');
        return [];
      }

      // Decode JWT token to extract tenant information
      const payload = JSON.parse(atob(token.split('.')[1]));
      console.log('🔍 Token payload for kebeles:', payload);

      const kebeles = [];

      // If user is from a kebele, add their kebele
      if (payload.tenant_type === 'kebele') {
        kebeles.push({
          id: payload.tenant_id,
          name: payload.tenant_name,
          tenant_type: 'kebele',
          parent_id: payload.parent_tenant_id
        });
      }

      console.log('🔍 Extracted kebeles from token:', kebeles);
      return kebeles;
    } catch (error) {
      console.error('Error extracting kebeles from token:', error);
      return [];
    }
  },

  /**
   * Fetch ketenas
   * @returns {Promise} Promise with ketenas data
   */
  getKetenas: async () => {
    try {
      const response = await publicAxios.get('/api/shared/ketenas/');
      return response.data || [];
    } catch (error) {
      console.error('Error fetching ketenas:', error);
      throw error;
    }
  },

  /**
   * Fetch all shared data in parallel with individual error handling
   * @returns {Promise} Promise with all shared data
   */
  getAllSharedData: async function() {
    try {
      console.log('🔍 SharedDataService - Fetching all shared data...');

      // Fetch data with individual error handling to prevent one failure from breaking everything
      const results = await Promise.allSettled([
        this.getCountries(),
        this.getRegions(),
        this.getReligions(),
        this.getCitizenStatuses(),
        this.getMaritalStatuses(),
        this.getDocumentTypes(),
        this.getEmploymentTypes(),
        this.getRelationships(),
        this.getCurrentStatuses(),
        this.getBiometricTypes(),
        this.getSubcities(),
        this.getKebeles(),
        this.getKetenas()
      ]);

      const [
        countries,
        regions,
        religions,
        citizenStatuses,
        maritalStatuses,
        documentTypes,
        employmentTypes,
        relationships,
        currentStatuses,
        biometricTypes,
        subcities,
        kebeles,
        ketenas
      ] = results.map((result, index) => {
        if (result.status === 'fulfilled') {
          return result.value;
        } else {
          const dataTypes = [
            'countries', 'regions', 'religions', 'citizenStatuses', 'maritalStatuses',
            'documentTypes', 'employmentTypes', 'relationships', 'currentStatuses',
            'biometricTypes', 'subcities', 'kebeles', 'ketenas'
          ];
          console.warn(`Failed to fetch ${dataTypes[index]}:`, result.reason);
          return []; // Return empty array for failed requests
        }
      });

      return {
        countries,
        regions,
        religions,
        citizenStatuses,
        maritalStatuses,
        documentTypes,
        employmentTypes,
        relationships,
        currentStatuses,
        biometricTypes,
        subcities,
        kebeles,
        ketenas
      };
    } catch (error) {
      console.error('Error fetching all shared data:', error);
      throw error;
    }
  }
};

export default sharedDataService;
