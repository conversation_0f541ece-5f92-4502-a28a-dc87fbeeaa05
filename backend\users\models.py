from django.db import models
from django.contrib.auth.models import AbstractUser
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _


class UserRole(models.TextChoices):
    SUPERADMIN = 'superadmin', _('Super Admin')
    CITY_ADMIN = 'city_admin', _('City Admin')
    SUBCITY_ADMIN = 'subcity_admin', _('Sub City Admin')
    KEBELE_ADMIN = 'kebele_admin', _('Kebele Admin')
    KEBELE_LEADER = 'kebele_leader', _('Kebele Leader')
    CLERK = 'clerk', _('Clerk')


class RoleType(models.TextChoices):
    """Types of roles for organizational hierarchy"""
    SYSTEM = 'system', _('System Role')
    ADMINISTRATIVE = 'administrative', _('Administrative Role')
    OPERATIONAL = 'operational', _('Operational Role')
    CUSTOM = 'custom', _('Custom Role')


class Role(models.Model):
    """
    Dynamic role model that supports both built-in and custom roles.
    """
    codename = models.CharField(max_length=50, unique=True, help_text="Unique role identifier")
    name = models.Char<PERSON><PERSON>(max_length=100, help_text="Human readable role name")
    description = models.TextField(blank=True, help_text="Detailed description of the role")
    role_type = models.CharField(max_length=20, choices=RoleType.choices, default=RoleType.CUSTOM)

    # Hierarchy and permissions
    level = models.IntegerField(default=0, help_text="Role hierarchy level (higher = more authority)")
    can_manage_roles = models.ManyToManyField('self', blank=True, symmetrical=False,
                                              related_name='managed_by_roles',
                                              help_text="Roles that this role can manage")

    # Tenant restrictions and scope
    allowed_tenant_types = models.JSONField(default=list, blank=True,
                                           help_text="List of tenant types this role can work in")
    scope_tenant = models.ForeignKey('tenants.Tenant', on_delete=models.CASCADE, null=True, blank=True,
                                     help_text="Tenant that owns this role (for tenant-specific roles)")
    is_global = models.BooleanField(default=True, help_text="True for system-wide roles, False for tenant-specific roles")

    # Status and metadata
    is_active = models.BooleanField(default=True)
    is_built_in = models.BooleanField(default=False, help_text="True for system-defined roles")
    created_by = models.ForeignKey('User', on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='created_roles')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Role')
        verbose_name_plural = _('Roles')
        ordering = ['-level', 'name']

    def __str__(self):
        return f"{self.name} ({self.codename})"

    @property
    def is_system_role(self):
        """Check if this is a built-in system role"""
        return self.is_built_in or self.role_type == RoleType.SYSTEM

    def can_manage_role(self, other_role):
        """Check if this role can manage another role"""
        if self.codename == 'superadmin':
            return True
        return other_role in self.can_manage_roles.all() or self.level > other_role.level

    def get_permissions(self):
        """Get all permissions assigned to this role"""
        return Permission.objects.filter(
            rolepermission__role_obj=self,
            rolepermission__is_active=True,
            is_active=True
        )

    def can_be_managed_by_user(self, user):
        """Check if a user can manage this role based on tenant hierarchy"""
        # Superusers can manage all roles
        if user.is_superuser or user.effective_role == 'superadmin':
            return True

        # Global roles can only be managed by superadmins
        if self.is_global and not (user.is_superuser or user.effective_role == 'superadmin'):
            return False

        # Tenant-specific roles
        if self.scope_tenant:
            user_tenant = user.tenant
            if not user_tenant:
                return False

            # Check tenant hierarchy
            if user.effective_role == 'city_admin':
                # City admin can manage roles in their city and child subcities/kebeles
                if self.scope_tenant == user_tenant:  # Same city
                    return True
                if self.scope_tenant.type == 'subcity' and self.scope_tenant.parent == user_tenant:  # Child subcity
                    return True
                if (self.scope_tenant.type == 'kebele' and self.scope_tenant.parent and
                    self.scope_tenant.parent.parent == user_tenant):  # Child kebele
                    return True

            elif user.effective_role == 'subcity_admin':
                # Subcity admin can manage roles in their subcity and child kebeles
                if self.scope_tenant == user_tenant:  # Same subcity
                    return True
                if self.scope_tenant.type == 'kebele' and self.scope_tenant.parent == user_tenant:  # Child kebele
                    return True

            elif user.effective_role == 'kebele_admin':
                # Kebele admin can only manage roles in their own kebele
                if self.scope_tenant == user_tenant:
                    return True

        return False

    def can_be_assigned_in_tenant(self, tenant):
        """Check if this role can be assigned to users in a specific tenant"""
        # Global roles can be assigned anywhere
        if self.is_global:
            return True

        # Tenant-specific roles can only be assigned in their scope or child tenants
        if self.scope_tenant:
            if self.scope_tenant == tenant:
                return True

            # Check if target tenant is a child of the role's scope
            if tenant.type == 'kebele' and self.scope_tenant.type == 'subcity':
                return tenant.parent == self.scope_tenant

            if tenant.type in ['subcity', 'kebele'] and self.scope_tenant.type == 'city':
                if tenant.type == 'subcity':
                    return tenant.parent == self.scope_tenant
                elif tenant.type == 'kebele' and tenant.parent:
                    return tenant.parent.parent == self.scope_tenant

        return False

    @classmethod
    def get_manageable_roles_for_user(cls, user, target_tenant=None):
        """Get all roles that a user can manage, optionally filtered by target tenant"""
        if user.is_superuser or user.effective_role == 'superadmin':
            queryset = cls.objects.filter(is_active=True)
        else:
            # Get roles based on user's tenant hierarchy
            user_tenant = user.tenant
            if not user_tenant:
                return cls.objects.none()

            # Build filter conditions
            conditions = models.Q()

            if user.effective_role == 'city_admin':
                # City admin can manage global roles and roles in their hierarchy
                conditions |= models.Q(is_global=True, is_built_in=True)  # Built-in global roles
                conditions |= models.Q(scope_tenant=user_tenant)  # City-level roles
                conditions |= models.Q(scope_tenant__parent=user_tenant)  # Subcity roles
                conditions |= models.Q(scope_tenant__parent__parent=user_tenant)  # Kebele roles

            elif user.effective_role == 'subcity_admin':
                # Subcity admin can manage roles in their subcity and child kebeles
                conditions |= models.Q(scope_tenant=user_tenant)  # Subcity-level roles
                conditions |= models.Q(scope_tenant__parent=user_tenant)  # Kebele roles

            elif user.effective_role == 'kebele_admin':
                # Kebele admin can only manage roles in their kebele
                conditions |= models.Q(scope_tenant=user_tenant)

            else:
                return cls.objects.none()

            queryset = cls.objects.filter(conditions, is_active=True)

        # Filter by target tenant if specified
        if target_tenant:
            queryset = queryset.filter(
                models.Q(is_global=True) |
                models.Q(scope_tenant=target_tenant) |
                models.Q(scope_tenant__in=target_tenant.get_ancestor_tenants())
            )

        return queryset


class Permission(models.Model):
    """
    Custom permission model for granular permission management.
    """
    codename = models.CharField(max_length=100, unique=True, help_text="Unique permission code")
    name = models.CharField(max_length=255, help_text="Human readable permission name")
    description = models.TextField(blank=True, help_text="Detailed description of what this permission allows")
    category = models.CharField(max_length=50, help_text="Permission category (e.g., 'id_cards', 'citizens', 'users')")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Permission')
        verbose_name_plural = _('Permissions')
        ordering = ['category', 'name']

    def __str__(self):
        return f"{self.name} ({self.codename})"


class RolePermission(models.Model):
    """
    Permissions assigned to roles. Supports both legacy string roles and dynamic Role objects.
    """
    # Legacy role support (for backward compatibility)
    role = models.CharField(max_length=50, choices=UserRole.choices, null=True, blank=True)

    # Dynamic role support
    role_obj = models.ForeignKey(Role, on_delete=models.CASCADE, null=True, blank=True,
                                 related_name='role_permissions')

    permission = models.ForeignKey(Permission, on_delete=models.CASCADE)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = _('Role Permission')
        verbose_name_plural = _('Role Permissions')
        # Ensure either role or role_obj is set, but not both
        constraints = [
            models.CheckConstraint(
                check=models.Q(role__isnull=False, role_obj__isnull=True) |
                      models.Q(role__isnull=True, role_obj__isnull=False),
                name='role_xor_role_obj'
            )
        ]

    def __str__(self):
        role_name = self.get_role_display() if self.role else self.role_obj.name
        return f"{role_name} - {self.permission.name}"

    @property
    def role_codename(self):
        """Get the role codename regardless of whether it's legacy or dynamic"""
        return self.role if self.role else self.role_obj.codename

    @property
    def role_name(self):
        """Get the role display name regardless of whether it's legacy or dynamic"""
        return self.get_role_display() if self.role else self.role_obj.name

    def clean(self):
        """Ensure exactly one of role or role_obj is set"""
        if not self.role and not self.role_obj:
            raise ValidationError("Either role or role_obj must be set")
        if self.role and self.role_obj:
            raise ValidationError("Cannot set both role and role_obj")


class UserPermission(models.Model):
    """
    Individual user permissions. These override or extend role-based permissions.
    """
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='custom_permissions')
    permission = models.ForeignKey(Permission, on_delete=models.CASCADE)
    granted = models.BooleanField(default=True, help_text="True to grant permission, False to explicitly deny")
    granted_by = models.ForeignKey('User', on_delete=models.SET_NULL, null=True, blank=True, related_name='granted_permissions')
    granted_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(null=True, blank=True, help_text="Optional expiration date for temporary permissions")
    reason = models.TextField(blank=True, help_text="Reason for granting/denying this permission")

    class Meta:
        unique_together = ['user', 'permission']
        verbose_name = _('User Permission')
        verbose_name_plural = _('User Permissions')

    def __str__(self):
        status = "Granted" if self.granted else "Denied"
        return f"{self.user.email} - {self.permission.name} ({status})"

    @property
    def is_expired(self):
        if self.expires_at:
            from django.utils import timezone
            return timezone.now() > self.expires_at
        return False


class User(AbstractUser):
    email = models.EmailField(_('email address'), unique=True)

    # Legacy role support (for backward compatibility)
    role = models.CharField(max_length=50, choices=UserRole.choices, default=UserRole.CLERK,
                           null=True, blank=True)

    # Dynamic role support
    role_obj = models.ForeignKey(Role, on_delete=models.SET_NULL, null=True, blank=True,
                                 related_name='users', help_text="Dynamic role assignment")

    tenant = models.ForeignKey('tenants.Tenant', on_delete=models.CASCADE, null=True, blank=True)
    phone_number = models.CharField(max_length=15, blank=True, null=True)
    profile_picture = models.ImageField(upload_to='profile_pictures/', blank=True, null=True)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']

    def __str__(self):
        return self.email

    @property
    def effective_role(self):
        """Get the effective role (dynamic role takes precedence over legacy role)"""
        return self.role_obj.codename if self.role_obj else self.role

    @property
    def effective_role_name(self):
        """Get the effective role display name"""
        return self.role_obj.name if self.role_obj else self.get_role_display()

    @property
    def effective_role_level(self):
        """Get the effective role level for hierarchy checks"""
        if self.role_obj:
            return self.role_obj.level

        # Default levels for legacy roles
        legacy_levels = {
            'superadmin': 100,
            'city_admin': 80,
            'subcity_admin': 60,
            'kebele_admin': 40,
            'kebele_leader': 30,
            'clerk': 10
        }
        return legacy_levels.get(self.role, 0)

    @property
    def is_superadmin(self):
        return self.effective_role == 'superadmin' or self.is_superuser

    @property
    def is_city_admin(self):
        return self.effective_role == 'city_admin'

    @property
    def is_subcity_admin(self):
        return self.effective_role == 'subcity_admin'

    @property
    def is_kebele_admin(self):
        return self.effective_role == 'kebele_admin'

    def assign_role(self, role_identifier, assigned_by=None):
        """
        Assign a role to the user. Can be a legacy role string or Role object.
        """
        if isinstance(role_identifier, str):
            # Check if it's a legacy role
            if role_identifier in [choice[0] for choice in UserRole.choices]:
                self.role = role_identifier
                self.role_obj = None
            else:
                # Try to find a dynamic role
                try:
                    role_obj = Role.objects.get(codename=role_identifier, is_active=True)
                    self.role_obj = role_obj
                    self.role = None
                except Role.DoesNotExist:
                    raise ValueError(f"Role '{role_identifier}' does not exist")
        elif isinstance(role_identifier, Role):
            self.role_obj = role_identifier
            self.role = None
        else:
            raise ValueError("role_identifier must be a string or Role object")

        self.save()

    def can_manage_user(self, other_user):
        """Check if this user can manage another user based on role hierarchy"""
        if self.is_superuser or self.effective_role == 'superadmin':
            return True

        # If both users have dynamic roles, use role hierarchy
        if self.role_obj and other_user.role_obj:
            return self.role_obj.can_manage_role(other_user.role_obj)

        # Fall back to legacy hierarchy logic
        my_level = self.effective_role_level
        other_level = other_user.effective_role_level
        return my_level > other_level

    def has_custom_permission(self, permission_codename):
        """
        Check if user has a specific custom permission.
        This checks both role-based permissions and individual user permissions.

        Priority order:
        1. Superuser/superadmin always has all permissions
        2. Explicit user permission (granted/denied) overrides role permission
        3. Role-based permission
        4. Default deny
        """
        # Superusers and superadmins have all permissions
        if self.is_superuser or self.role == UserRole.SUPERADMIN:
            return True

        try:
            permission = Permission.objects.get(codename=permission_codename, is_active=True)
        except Permission.DoesNotExist:
            return False

        # Check for explicit user permission first
        user_permission = self.custom_permissions.filter(
            permission=permission
        ).first()

        if user_permission:
            # Check if permission is expired
            if user_permission.is_expired:
                return False
            return user_permission.granted

        # Check role-based permission (both legacy and dynamic)
        role_permission = None

        if self.role_obj:
            # Check dynamic role permissions
            role_permission = RolePermission.objects.filter(
                role_obj=self.role_obj,
                permission=permission,
                is_active=True
            ).first()
        elif self.role:
            # Check legacy role permissions
            role_permission = RolePermission.objects.filter(
                role=self.role,
                permission=permission,
                is_active=True
            ).first()

        if role_permission:
            return True

        # Default deny
        return False

    def get_all_permissions(self):
        """
        Get all permissions for this user (both role-based and individual).
        Returns a set of permission codenames.
        """
        if self.is_superuser or self.role == UserRole.SUPERADMIN:
            # Superusers have all active permissions
            return set(Permission.objects.filter(is_active=True).values_list('codename', flat=True))

        permissions = set()

        # Add role-based permissions (both legacy and dynamic)
        if self.role_obj:
            # Get permissions from dynamic role
            role_permissions = RolePermission.objects.filter(
                role_obj=self.role_obj,
                is_active=True,
                permission__is_active=True
            ).select_related('permission')
        elif self.role:
            # Get permissions from legacy role
            role_permissions = RolePermission.objects.filter(
                role=self.role,
                is_active=True,
                permission__is_active=True
            ).select_related('permission')
        else:
            role_permissions = RolePermission.objects.none()

        for rp in role_permissions:
            permissions.add(rp.permission.codename)

        # Add/remove individual user permissions
        user_permissions = self.custom_permissions.filter(
            permission__is_active=True
        ).select_related('permission')

        for up in user_permissions:
            if up.is_expired:
                continue

            if up.granted:
                permissions.add(up.permission.codename)
            else:
                # Explicit deny - remove from permissions
                permissions.discard(up.permission.codename)

        return permissions

    def grant_permission(self, permission_codename, granted_by=None, reason="", expires_at=None):
        """
        Grant a specific permission to this user.
        """
        try:
            permission = Permission.objects.get(codename=permission_codename, is_active=True)
        except Permission.DoesNotExist:
            raise ValueError(f"Permission '{permission_codename}' does not exist")

        user_permission, created = UserPermission.objects.update_or_create(
            user=self,
            permission=permission,
            defaults={
                'granted': True,
                'granted_by': granted_by,
                'reason': reason,
                'expires_at': expires_at
            }
        )
        return user_permission

    def revoke_permission(self, permission_codename, granted_by=None, reason=""):
        """
        Revoke a specific permission from this user.
        """
        try:
            permission = Permission.objects.get(codename=permission_codename, is_active=True)
        except Permission.DoesNotExist:
            raise ValueError(f"Permission '{permission_codename}' does not exist")

        user_permission, created = UserPermission.objects.update_or_create(
            user=self,
            permission=permission,
            defaults={
                'granted': False,
                'granted_by': granted_by,
                'reason': reason,
                'expires_at': None
            }
        )
        return user_permission

    @property
    def is_subcity_admin(self):
        return self.role == UserRole.SUBCITY_ADMIN

    @property
    def is_kebele_admin(self):
        return self.role == UserRole.KEBELE_ADMIN

    @property
    def is_kebele_leader(self):
        return self.role == UserRole.KEBELE_LEADER

    @property
    def is_clerk(self):
        return self.role == UserRole.CLERK
