#!/usr/bin/env python3
"""
Quick Test for Real Fingerprint Data
"""

import urllib.request
import json
import base64

def quick_test():
    print("=== QUICK REAL DATA TEST ===")
    print("Testing if actual fingerprint data is now included...")
    
    try:
        # Test capture
        capture_data = json.dumps({"thumb_type": "left"}).encode()
        
        req = urllib.request.Request(
            "http://localhost:8001/api/capture/fingerprint",
            data=capture_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print("📸 Capturing fingerprint...")
        response = urllib.request.urlopen(req, timeout=40)
        
        if response.getcode() == 200:
            result = json.loads(response.read().decode())
            
            if result.get('success'):
                data = result.get('data', {})
                
                print(f"✅ Capture successful!")
                
                # Check all the data fields
                template = data.get('template', '')
                raw_image_data = data.get('raw_image_data', '')
                image_hash = data.get('image_hash', '')
                frame_size = data.get('frame_size_actual', 0)
                minutiae_count = data.get('minutiae_count', 0)
                biometric_template = data.get('biometric_template', '')
                
                print(f"\n📊 DATA ANALYSIS:")
                print(f"   Template length: {len(template)} chars")
                print(f"   Raw image data: {len(raw_image_data)} chars")
                print(f"   Image hash: {image_hash[:20]}... (truncated)")
                print(f"   Frame size: {frame_size} bytes")
                print(f"   Minutiae count: {minutiae_count}")
                print(f"   Biometric template: {len(biometric_template)} chars")
                
                # Try to decode the main template
                if template:
                    try:
                        template_json = base64.b64decode(template).decode()
                        template_obj = json.loads(template_json)
                        
                        print(f"\n🔍 DECODED TEMPLATE:")
                        print(f"   Minutiae points: {len(template_obj.get('minutiae_points', []))}")
                        print(f"   Image dimensions: {template_obj.get('image_dimensions', {})}")
                        print(f"   Total pixels: {template_obj.get('total_pixels', 0)}")
                        print(f"   Data quality: {template_obj.get('data_quality', 0)}%")
                        print(f"   Template hash: {template_obj.get('template_hash', '')[:20]}...")
                        
                        # Count real data indicators
                        real_indicators = 0
                        
                        if len(template_obj.get('minutiae_points', [])) > 10:
                            real_indicators += 1
                            print(f"   ✅ Has minutiae points")
                        
                        if template_obj.get('total_pixels', 0) > 50000:
                            real_indicators += 1
                            print(f"   ✅ Realistic pixel count")
                        
                        if frame_size > 10000:
                            real_indicators += 1
                            print(f"   ✅ Large frame size")
                        
                        if len(image_hash) > 30:
                            real_indicators += 1
                            print(f"   ✅ Valid image hash")
                        
                        if minutiae_count > 10:
                            real_indicators += 1
                            print(f"   ✅ Good minutiae count")
                        
                        print(f"\n🎯 RESULT: {real_indicators}/5 real data indicators")
                        
                        if real_indicators >= 4:
                            print(f"🎉 SUCCESS: Contains REAL fingerprint data!")
                            return True
                        elif real_indicators >= 2:
                            print(f"⚠️ PARTIAL: Some real data present")
                            return False
                        else:
                            print(f"❌ FAILED: Still mostly metadata")
                            return False
                        
                    except Exception as decode_error:
                        print(f"❌ Template decode error: {decode_error}")
                        return False
                else:
                    print(f"❌ No template data")
                    return False
            else:
                print(f"❌ Capture failed: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP error: {response.getcode()}")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

if __name__ == '__main__':
    success = quick_test()
    
    if success:
        print(f"\n🎉 REAL DATA TEST PASSED!")
        print(f"✅ Fingerprint templates now contain actual biometric data")
    else:
        print(f"\n❌ REAL DATA TEST FAILED!")
        print(f"💡 May need further debugging")
    
    input("Press Enter to exit...")
