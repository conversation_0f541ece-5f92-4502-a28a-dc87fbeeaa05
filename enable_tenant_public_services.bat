@echo off
echo 🔧 Enabling Public ID Application for All Tenants...

echo.
echo 🔑 You need to be authenticated as a superuser to make these changes.
echo Please ensure you have the proper authentication token.
echo.

REM Enable public services for each tenant using PATCH requests
echo 📝 Enabling public ID application for tenant 1 (Public)...
curl -X PATCH "http://localhost:8000/api/tenants/1/" ^
     -H "Content-Type: application/json" ^
     -H "Authorization: Bearer YOUR_AUTH_TOKEN_HERE" ^
     -d "{\"public_id_application_enabled\": true}"

echo.
echo 📝 Enabling public ID application for tenant 2 (Addis Ababa)...
curl -X PATCH "http://localhost:8000/api/tenants/2/" ^
     -H "Content-Type: application/json" ^
     -H "Authorization: Bearer YOUR_AUTH_TOKEN_HERE" ^
     -d "{\"public_id_application_enabled\": true}"

echo.
echo 📝 Enabling public ID application for tenant 3 (Bole)...
curl -X PATCH "http://localhost:8000/api/tenants/3/" ^
     -H "Content-Type: application/json" ^
     -H "Authorization: Bearer YOUR_AUTH_TOKEN_HERE" ^
     -d "{\"public_id_application_enabled\": true}"

echo.
echo 📝 Enabling public ID application for tenant 4 (Bole 01)...
curl -X PATCH "http://localhost:8000/api/tenants/4/" ^
     -H "Content-Type: application/json" ^
     -H "Authorization: Bearer YOUR_AUTH_TOKEN_HERE" ^
     -d "{\"public_id_application_enabled\": true}"

echo.
echo 📝 Enabling public ID application for tenant 5 (Gondar)...
curl -X PATCH "http://localhost:8000/api/tenants/5/" ^
     -H "Content-Type: application/json" ^
     -H "Authorization: Bearer YOUR_AUTH_TOKEN_HERE" ^
     -d "{\"public_id_application_enabled\": true}"

echo.
echo 📝 Enabling public ID application for tenant 6 (Zoble)...
curl -X PATCH "http://localhost:8000/api/tenants/6/" ^
     -H "Content-Type: application/json" ^
     -H "Authorization: Bearer YOUR_AUTH_TOKEN_HERE" ^
     -d "{\"public_id_application_enabled\": true}"

echo.
echo 📝 Enabling public ID application for tenant 7 (Kebele14)...
curl -X PATCH "http://localhost:8000/api/tenants/7/" ^
     -H "Content-Type: application/json" ^
     -H "Authorization: Bearer YOUR_AUTH_TOKEN_HERE" ^
     -d "{\"public_id_application_enabled\": true}"

echo.
echo.
echo 🎉 All tenants should now have public ID application enabled!
echo 🔗 Visit the Public Services Portal to verify: http://localhost:3000/public-services
echo.
echo ⚠️ Note: Replace YOUR_AUTH_TOKEN_HERE with your actual authentication token
echo    You can get this from your browser's developer tools or login response.

pause
