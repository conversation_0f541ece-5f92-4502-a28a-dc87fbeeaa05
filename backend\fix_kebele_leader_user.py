#!/usr/bin/env python3

import os
import sys
import django

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from tenants.models import Tenant
from django_tenants.utils import schema_context

User = get_user_model()

def fix_kebele_leader_user():
    """Check and fix kebele leader user status."""
    
    print("🔍 Checking kebele leader users...")
    
    # Find all kebele tenants
    kebele_tenants = Tenant.objects.filter(type='kebele')
    print(f"📋 Found {kebele_tenants.count()} kebele tenants")
    
    for kebele in kebele_tenants:
        print(f"\n🏢 Checking {kebele.name} (ID: {kebele.id})")
        
        # Check users in this kebele
        kebele_users = User.objects.filter(tenant=kebele)
        print(f"   👥 Users in {kebele.name}: {kebele_users.count()}")
        
        for user in kebele_users:
            print(f"   - {user.username} ({user.email})")
            print(f"     Role: {user.role}")
            print(f"     Is Active: {user.is_active}")
            print(f"     Is Staff: {user.is_staff}")
            print(f"     Is Superuser: {user.is_superuser}")
            
            # Check if this is a kebele leader
            if user.role == 'kebele_leader':
                print(f"   🎯 Found kebele leader: {user.username}")
                
                # Check if user is inactive
                if not user.is_active:
                    print(f"   ❌ User {user.username} is INACTIVE - fixing...")
                    user.is_active = True
                    user.save()
                    print(f"   ✅ User {user.username} activated successfully")
                else:
                    print(f"   ✅ User {user.username} is already active")
                
                # Check if user has proper permissions
                print(f"   🔍 Checking user permissions...")
                
                # Test authentication
                try:
                    # Check if user can authenticate
                    if user.check_password('password123'):  # Default password
                        print(f"   ✅ User can authenticate with default password")
                    else:
                        print(f"   ⚠️ User cannot authenticate with default password")
                        print(f"   💡 Try resetting password if needed")
                except Exception as e:
                    print(f"   ❌ Error checking authentication: {e}")
                
                # Check tenant schema access
                try:
                    with schema_context(kebele.schema_name):
                        from citizens.models import Citizen
                        citizen_count = Citizen.objects.count()
                        print(f"   ✅ Can access tenant schema - {citizen_count} citizens found")
                except Exception as e:
                    print(f"   ❌ Error accessing tenant schema: {e}")

def check_specific_user():
    """Check the specific kebele leader user that's having issues."""
    
    print("\n🔍 Checking specific kebele leader user...")
    
    # Look for dibora user
    try:
        user = User.objects.get(email='<EMAIL>')
        print(f"✅ Found user: {user.username} ({user.email})")
        print(f"   Role: {user.role}")
        print(f"   Is Active: {user.is_active}")
        print(f"   Is Staff: {user.is_staff}")
        print(f"   Tenant: {user.tenant}")
        
        if not user.is_active:
            print(f"❌ User is INACTIVE - fixing...")
            user.is_active = True
            user.save()
            print(f"✅ User activated successfully")
        
        # Check tenant
        if user.tenant:
            print(f"   Tenant: {user.tenant.name} (Type: {user.tenant.type})")
            print(f"   Schema: {user.tenant.schema_name}")
        else:
            print(f"❌ User has no tenant assigned!")
            
    except User.DoesNotExist:
        print("❌ User <EMAIL> not found")
        
        # Try to find by username
        try:
            user = User.objects.get(username='<EMAIL>')
            print(f"✅ Found user by username: {user.username} ({user.email})")
            print(f"   Role: {user.role}")
            print(f"   Is Active: {user.is_active}")
            print(f"   Tenant: {user.tenant}")
            
            if not user.is_active:
                print(f"❌ User is INACTIVE - fixing...")
                user.is_active = True
                user.save()
                print(f"✅ User activated successfully")
                
        except User.DoesNotExist:
            print("❌ User <EMAIL> not found either")

def list_all_inactive_users():
    """List all inactive users."""
    
    print("\n🔍 Checking for inactive users...")
    
    inactive_users = User.objects.filter(is_active=False)
    print(f"📋 Found {inactive_users.count()} inactive users")
    
    for user in inactive_users:
        print(f"   - {user.username} ({user.email}) - Role: {user.role} - Tenant: {user.tenant}")

if __name__ == '__main__':
    print("🚀 Starting kebele leader user fix...")
    
    # Check all kebele leader users
    fix_kebele_leader_user()
    
    # Check specific user
    check_specific_user()
    
    # List inactive users
    list_all_inactive_users()
    
    print("\n✅ Kebele leader user check completed!")
