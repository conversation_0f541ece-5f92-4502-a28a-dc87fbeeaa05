# Generated by Django 4.2.7 on 2025-06-07 12:06

import citizens.models
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Citizen',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Universally unique identifier for cross-tenant operations', unique=True)),
                ('digital_id', models.CharField(blank=True, help_text='Human-readable digital ID in format: CITY+SUBCITY+KEBELE+RANDOM', max_length=50, null=True, unique=True)),
                ('first_name', models.Char<PERSON><PERSON>(max_length=100)),
                ('middle_name', models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ('last_name', models.CharField(max_length=100)),
                ('first_name_am', models.<PERSON>r<PERSON>ield(blank=True, max_length=100, null=True)),
                ('middle_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('date_of_birth', models.DateField(blank=True, null=True)),
                ('gender', models.CharField(blank=True, choices=[('male', 'Male'), ('female', 'Female')], max_length=10, null=True)),
                ('blood_type', models.CharField(blank=True, choices=[('A+', 'A+'), ('A-', 'A-'), ('B+', 'B+'), ('B-', 'B-'), ('AB+', 'AB+'), ('AB-', 'AB-'), ('O+', 'O+'), ('O-', 'O-'), ('unknown', 'Unknown')], max_length=10, null=True)),
                ('disability', models.CharField(blank=True, choices=[('none', 'None'), ('physical', 'Physical Disability'), ('visual', 'Visual Impairment'), ('hearing', 'Hearing Impairment'), ('intellectual', 'Intellectual Disability'), ('mental', 'Mental Health Condition'), ('multiple', 'Multiple Disabilities'), ('other', 'Other')], default='none', max_length=20, null=True)),
                ('house_number', models.CharField(blank=True, max_length=20, null=True)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('nationality', models.IntegerField(blank=True, null=True)),
                ('religion', models.IntegerField(blank=True, null=True)),
                ('status', models.IntegerField(blank=True, null=True)),
                ('marital_status', models.IntegerField(blank=True, null=True)),
                ('subcity', models.IntegerField(blank=True, null=True)),
                ('kebele', models.IntegerField(blank=True, null=True)),
                ('ketena', models.IntegerField(blank=True, null=True)),
                ('region', models.IntegerField(blank=True, null=True)),
                ('employment', models.CharField(blank=True, max_length=100, null=True)),
                ('employee_type', models.CharField(blank=True, max_length=100, null=True)),
                ('organization_name', models.CharField(blank=True, max_length=200, null=True)),
                ('photo', models.TextField(blank=True, null=True)),
                ('is_resident', models.BooleanField(default=True, help_text='Indicates whether the citizen is a resident of the city.')),
                ('is_active', models.BooleanField(default=True)),
                ('deactivation_reason', models.CharField(blank=True, choices=[('transfer_completed', 'Transfer Completed'), ('clearance_issued', 'Clearance Letter Issued'), ('deceased', 'Deceased'), ('other', 'Other')], help_text='Reason for deactivation', max_length=50, null=True)),
                ('deactivated_at', models.DateTimeField(blank=True, help_text='Date when citizen was deactivated', null=True)),
                ('transfer_status', models.CharField(choices=[('active', 'Active'), ('transferred', 'Transferred'), ('deceased', 'Deceased'), ('inactive', 'Inactive')], default='active', help_text='Current status of the citizen', max_length=20)),
                ('transfer_date', models.DateTimeField(blank=True, help_text='Date when citizen was transferred', null=True)),
                ('transfer_destination_kebele_id', models.IntegerField(blank=True, help_text='ID of kebele citizen was transferred to', null=True)),
                ('transfer_destination_kebele_name', models.CharField(blank=True, help_text='Name of kebele citizen was transferred to', max_length=100, null=True)),
                ('transfer_reason', models.CharField(blank=True, help_text='Reason for transfer', max_length=255, null=True)),
                ('transfer_request_id', models.CharField(blank=True, help_text='ID of the transfer request', max_length=50, null=True)),
                ('original_digital_id', models.CharField(blank=True, help_text='Original digital ID before transfer', max_length=50, null=True)),
            ],
            options={
                'verbose_name': 'Citizen',
                'verbose_name_plural': 'Citizens',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Photo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('photo', models.ImageField(blank=True, null=True, upload_to=citizens.models.citizen_photo_path)),
                ('upload_date', models.DateTimeField(auto_now_add=True)),
                ('citizen', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='photo_record', to='citizens.citizen')),
            ],
            options={
                'verbose_name': 'Photo',
                'verbose_name_plural': 'Photos',
            },
        ),
        migrations.CreateModel(
            name='Parent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('first_name', models.CharField(max_length=100)),
                ('middle_name', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name', models.CharField(max_length=100)),
                ('first_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('middle_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('gender', models.CharField(choices=[('male', 'Male'), ('female', 'Female')], max_length=10)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('nationality', models.IntegerField(blank=True, null=True)),
                ('is_resident', models.BooleanField(default=False, help_text='Indicates whether the parent is a resident of the city.')),
                ('is_active', models.BooleanField(default=True)),
                ('citizen', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='parents', to='citizens.citizen')),
                ('linked_citizen', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='as_parent', to='citizens.citizen')),
            ],
            options={
                'verbose_name': 'Parent',
                'verbose_name_plural': 'Parents',
            },
        ),
        migrations.CreateModel(
            name='Document',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('document_type', models.IntegerField(help_text='Reference to shared.models.DocumentType ID')),
                ('document_file', models.FileField(upload_to=citizens.models.citizen_document_path)),
                ('issue_date', models.DateField(blank=True, null=True)),
                ('expiry_date', models.DateField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('citizen', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='citizens.citizen')),
            ],
            options={
                'verbose_name': 'Document',
                'verbose_name_plural': 'Documents',
            },
        ),
        migrations.CreateModel(
            name='Child',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('first_name', models.CharField(max_length=100)),
                ('middle_name', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name', models.CharField(max_length=100)),
                ('first_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('middle_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('date_of_birth', models.DateField(blank=True, null=True)),
                ('gender', models.CharField(blank=True, choices=[('male', 'Male'), ('female', 'Female')], max_length=10, null=True)),
                ('nationality', models.IntegerField(blank=True, null=True)),
                ('is_resident', models.BooleanField(default=False, help_text='Indicates whether the child is a resident of the city.')),
                ('is_active', models.BooleanField(default=True)),
                ('citizen', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='children', to='citizens.citizen')),
                ('linked_citizen', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='as_child', to='citizens.citizen')),
            ],
            options={
                'verbose_name': 'Child',
                'verbose_name_plural': 'Children',
            },
        ),
        migrations.CreateModel(
            name='Biometric',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('left_hand_fingerprint', models.TextField(blank=True, null=True)),
                ('right_hand_fingerprint', models.TextField(blank=True, null=True)),
                ('left_thumb_fingerprint', models.TextField(blank=True, null=True)),
                ('right_thumb_fingerprint', models.TextField(blank=True, null=True)),
                ('left_eye_iris_scan', models.TextField(blank=True, null=True)),
                ('right_eye_iris_scan', models.TextField(blank=True, null=True)),
                ('citizen', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='biometric_record', to='citizens.citizen')),
            ],
            options={
                'verbose_name': 'Biometric',
                'verbose_name_plural': 'Biometrics',
            },
        ),
        migrations.CreateModel(
            name='Spouse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('first_name', models.CharField(max_length=100)),
                ('middle_name', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name', models.CharField(max_length=100)),
                ('first_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('middle_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('nationality', models.IntegerField(blank=True, null=True)),
                ('primary_contact', models.BooleanField(default=False, help_text='Indicates whether this is the primary emergency contact.')),
                ('is_active', models.BooleanField(default=True)),
                ('is_resident', models.BooleanField(default=False, help_text='Indicates whether the spouse is a resident of the city.')),
                ('citizen', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='spouse_records', to='citizens.citizen')),
                ('linked_citizen', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='as_spouse', to='citizens.citizen')),
            ],
            options={
                'verbose_name': 'Spouse',
                'verbose_name_plural': 'Spouses',
                'unique_together': {('citizen', 'phone')},
            },
        ),
        migrations.CreateModel(
            name='EmergencyContact',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('first_name', models.CharField(max_length=100)),
                ('middle_name', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name', models.CharField(max_length=100)),
                ('first_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('middle_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('relationship', models.CharField(blank=True, max_length=100, null=True)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('nationality', models.IntegerField(blank=True, null=True)),
                ('primary_contact', models.BooleanField(default=False, help_text='Indicates whether this is the primary emergency contact.')),
                ('is_active', models.BooleanField(default=True)),
                ('is_resident', models.BooleanField(default=False, help_text='Indicates whether the emergency contact is a resident of the city.')),
                ('citizen', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='emergency_contacts', to='citizens.citizen')),
                ('linked_citizen', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='as_emergency_contact', to='citizens.citizen')),
            ],
            options={
                'verbose_name': 'Emergency Contact',
                'verbose_name_plural': 'Emergency Contacts',
                'unique_together': {('citizen', 'phone')},
            },
        ),
    ]
