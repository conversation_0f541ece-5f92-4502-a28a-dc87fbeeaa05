import { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  LinearProgress,
  Chip,
  Paper,
} from '@mui/material';
import {
  Fingerprint as FingerprintIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Close as CloseIcon,
  Refresh as RefreshIcon,
  Usb as UsbIcon,
} from '@mui/icons-material';
import FingerprintCapture from '../../../components/biometrics/FingerprintCapture';
import biometricService from '../../../services/biometricService';
import localizationService from '../../../services/localizationService';

const BiometricCaptureStep = ({ formik, loading }) => {
  const [deviceConnected, setDeviceConnected] = useState(false);
  const [deviceError, setDeviceError] = useState('');
  const [captureDialog, setCaptureDialog] = useState(false);
  const [currentCapture, setCurrentCapture] = useState(null); // 'left' or 'right'
  const [captureProgress, setCaptureProgress] = useState(0);
  const [captureStatus, setCaptureStatus] = useState(''); // 'capturing', 'processing', 'complete', 'error'

  // Check device connection on component mount
  useEffect(() => {
    checkDeviceConnection();
  }, []);

  const checkDeviceConnection = useCallback(async () => {
    try {
      setDeviceError('');

      // Check if Web Serial API is supported
      if (!('serial' in navigator)) {
        console.warn('Web Serial API not supported, using mock device');
        setDeviceConnected(true); // Allow mock operation
        return;
      }

      // Check real device status from API
      try {
        const statusResult = await biometricService.getDeviceStatus();
        if (statusResult.success) {
          // Handle both direct response and nested data response
          const deviceStatus = statusResult.data || statusResult;

          // Check for device_connected (JAR bridge) or connected (other services)
          const isConnected = deviceStatus.device_connected || deviceStatus.connected || false;
          const isInitialized = deviceStatus.initialized !== false; // Default to true if not specified

          setDeviceConnected(isConnected && isInitialized);

          if (!isConnected) {
            // Try to initialize device
            try {
              const initResult = await biometricService.initializeDevice();
              if (initResult.success) {
                setDeviceConnected(true);
              } else {
                throw new Error(initResult.error || 'Device initialization failed');
              }
            } catch (initError) {
              console.warn('Device initialization failed:', initError.message);
              // Don't throw error here - device might still work for capture
            }
          }
        } else {
          throw new Error(statusResult.error || 'Failed to get device status');
        }
      } catch (apiError) {
        console.error('Device check failed:', apiError);
        setDeviceConnected(false);
        setDeviceError(`Device connection failed: ${apiError.message}`);
      }
    } catch (error) {
      console.error('Device connection error:', error);
      setDeviceError(error.message);
      setDeviceConnected(false);
    }
  }, []);

  const startCapture = useCallback((thumbType) => {
    setCurrentCapture(thumbType);
    setCaptureDialog(true);
    setCaptureProgress(0);
    setCaptureStatus('capturing');
  }, []);

  const handleCaptureComplete = useCallback(async (thumbType, fingerprintData) => {
    // Store the fingerprint data in formik
    const fieldName = `${thumbType}_thumb_fingerprint`;

    // Ensure fingerprintData is a string
    let processedData = fingerprintData;
    if (typeof fingerprintData !== 'string') {
      console.warn('Fingerprint data is not a string, converting:', typeof fingerprintData, fingerprintData);

      // Handle different data types properly
      if (fingerprintData && typeof fingerprintData === 'object') {
        // If it's an array-like object (like the one we were seeing), convert it properly
        if (fingerprintData.hasOwnProperty('0')) {
          // This is an array-like object, convert to actual string
          const keys = Object.keys(fingerprintData).sort((a, b) => parseInt(a) - parseInt(b));
          processedData = keys.map(key => fingerprintData[key]).join('');
          console.log('✅ Converted array-like object to string:', processedData.substring(0, 50) + '...');
        } else {
          // Try to extract template data or stringify
          processedData = fingerprintData.template_data || JSON.stringify(fingerprintData);
        }
      } else {
        processedData = String(fingerprintData);
      }
    }

    console.log(`🔍 Setting ${fieldName} with data:`, {
      type: typeof processedData,
      length: processedData?.length,
      preview: processedData?.substring(0, 50) + '...'
    });

    // 🚨 REAL-TIME DUPLICATE DETECTION
    console.log(`🔍 Checking for duplicate ${thumbType} thumb fingerprint...`);
    const duplicateDetected = await checkForDuplicates(thumbType, processedData);

    if (duplicateDetected) {
      // Don't store the fingerprint data if duplicate detected
      console.log(`❌ ${thumbType} thumb not stored due to duplicate detection`);
      setCaptureDialog(false);
      setCurrentCapture(null);
      setCaptureStatus('error');
      return;
    }

    formik.setFieldValue(fieldName, processedData);

    // Clear any existing errors for this field
    if (formik.errors[fieldName]) {
      formik.setFieldError(fieldName, undefined);
    }

    // Mark field as touched to trigger validation
    formik.setFieldTouched(fieldName, true);

    setCaptureDialog(false);
    setCurrentCapture(null);
    setCaptureStatus('complete');

    // Force validation and UI update after setting the field value
    setTimeout(() => {
      formik.validateForm();
      // Force component re-render to update UI state
      formik.setFieldTouched(fieldName, true, false);
    }, 100);

    console.log(`✅ ${thumbType} thumb captured successfully`, {
      fieldName,
      hasData: Boolean(processedData),
      dataType: typeof processedData,
      leftThumb: Boolean(formik.values.left_thumb_fingerprint),
      rightThumb: Boolean(formik.values.right_thumb_fingerprint)
    });
  }, [formik]);

  const checkForDuplicates = useCallback(async (thumbType, templateData) => {
    try {
      console.log(`🔍 Running duplicate check for ${thumbType} thumb...`);

      // Prepare data for duplicate checking
      const duplicateCheckData = {
        left_thumb_fingerprint: thumbType === 'left' ? templateData : formik.values.left_thumb_fingerprint,
        right_thumb_fingerprint: thumbType === 'right' ? templateData : formik.values.right_thumb_fingerprint
      };

      // Only check if we have at least one fingerprint
      if (!duplicateCheckData.left_thumb_fingerprint && !duplicateCheckData.right_thumb_fingerprint) {
        return false;
      }

      const duplicateResult = await biometricService.checkDuplicateFingerprints(duplicateCheckData);

      if (duplicateResult.success && duplicateResult.data.has_duplicates) {
        const matches = duplicateResult.data.matches || [];
        const totalMatches = duplicateResult.data.total_matches || 0;

        console.log(`⚠️ DUPLICATE DETECTED: Found ${totalMatches} potential duplicate(s)`);

        // Format duplicate information for display
        const duplicateInfo = matches.slice(0, 3).map(match =>
          `• ${match.citizen_name} (ID: ${match.citizen_digital_id}) in ${match.tenant_name}`
        ).join('\n');

        // Set error message for immediate display
        setDeviceError(
          `🚨 DUPLICATE FINGERPRINT DETECTED!\n\n` +
          `This ${thumbType} thumb appears to already be registered in the system.\n\n` +
          `Found ${totalMatches} potential duplicate(s):\n${duplicateInfo}\n\n` +
          `⚠️ This person may already have an ID card. Please verify their identity ` +
          `before proceeding with registration.`
        );

        console.log(`❌ ${thumbType} thumb duplicate detected - blocking capture`);
        return true; // Duplicate detected

      } else {
        console.log(`✅ No duplicates found for ${thumbType} thumb - capture is valid`);
        setDeviceError(''); // Clear any previous errors
        return false; // No duplicates
      }

    } catch (error) {
      console.error(`Error checking duplicates for ${thumbType} thumb:`, error);
      // Don't block capture on duplicate check errors, just log them
      console.log(`⚠️ Duplicate check failed, but allowing capture to proceed`);
      return false;
    }
  }, [formik.values]);

  const handleCaptureError = useCallback((error) => {
    console.error('Fingerprint capture error:', error);
    setCaptureStatus('error');
    setDeviceError(error.message);
  }, []);

  const retryCapture = useCallback(() => {
    if (currentCapture) {
      setCaptureProgress(0);
      setCaptureStatus('capturing');
      setDeviceError('');
    }
  }, [currentCapture]);

  const closeDialog = useCallback(() => {
    setCaptureDialog(false);
    setCurrentCapture(null);
    setCaptureStatus('');
    setCaptureProgress(0);
  }, []);

  const hasLeftThumb = Boolean(formik.values.left_thumb_fingerprint);
  const hasRightThumb = Boolean(formik.values.right_thumb_fingerprint);
  const allCaptured = hasLeftThumb && hasRightThumb;

  // Note: Validation is handled in handleCaptureComplete, no need for duplicate validation here

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <FingerprintIcon sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h6" component="h2">
            {localizationService.t('biometric_capture')}
          </Typography>
        </Box>

        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          {localizationService.t('biometric_capture_description')}
        </Typography>

        {/* Device Status */}
        <Paper sx={{ p: 2, mb: 3, bgcolor: deviceConnected ? 'success.light' : 'error.light' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <UsbIcon color={deviceConnected ? 'success' : 'error'} />
            <Typography variant="subtitle2">
              {localizationService.t('device_status')}: {deviceConnected ? localizationService.t('connected') : localizationService.t('disconnected')}
            </Typography>
            {!deviceConnected && (
              <Button
                size="small"
                startIcon={<RefreshIcon />}
                onClick={checkDeviceConnection}
                disabled={loading}
              >
                {localizationService.t('retry')}
              </Button>
            )}
          </Box>
          {deviceError && (
            <Alert severity="error" sx={{ mt: 1 }}>
              {deviceError}
            </Alert>
          )}
        </Paper>

        {/* Fingerprint Capture Grid */}
        <Grid container spacing={3}>
          {/* Left Thumb */}
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3, textAlign: 'center', height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                {localizationService.t('left_thumb')}
              </Typography>
              
              <Box sx={{ mb: 2 }}>
                <FingerprintIcon 
                  sx={{ 
                    fontSize: 80, 
                    color: hasLeftThumb ? 'success.main' : 'grey.400' 
                  }} 
                />
              </Box>

              {hasLeftThumb ? (
                <Box>
                  <Chip 
                    icon={<CheckIcon />} 
                    label={localizationService.t('captured')}
                    color="success"
                    sx={{ mb: 2 }}
                  />
                  <br />
                  <Button
                    variant="outlined"
                    onClick={() => startCapture('left')}
                    disabled={!deviceConnected || loading}
                    startIcon={<RefreshIcon />}
                  >
                    {localizationService.t('recapture')}
                  </Button>
                </Box>
              ) : (
                <Button
                  variant="contained"
                  onClick={() => startCapture('left')}
                  disabled={!deviceConnected || loading}
                  startIcon={<FingerprintIcon />}
                  size="large"
                >
                  {localizationService.t('capture_left_thumb')}
                </Button>
              )}
            </Paper>
          </Grid>

          {/* Right Thumb */}
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3, textAlign: 'center', height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                {localizationService.t('right_thumb')}
              </Typography>
              
              <Box sx={{ mb: 2 }}>
                <FingerprintIcon 
                  sx={{ 
                    fontSize: 80, 
                    color: hasRightThumb ? 'success.main' : 'grey.400' 
                  }} 
                />
              </Box>

              {hasRightThumb ? (
                <Box>
                  <Chip 
                    icon={<CheckIcon />} 
                    label={localizationService.t('captured')}
                    color="success"
                    sx={{ mb: 2 }}
                  />
                  <br />
                  <Button
                    variant="outlined"
                    onClick={() => startCapture('right')}
                    disabled={!deviceConnected || loading}
                    startIcon={<RefreshIcon />}
                  >
                    {localizationService.t('recapture')}
                  </Button>
                </Box>
              ) : (
                <Button
                  variant="contained"
                  onClick={() => startCapture('right')}
                  disabled={!deviceConnected || loading}
                  startIcon={<FingerprintIcon />}
                  size="large"
                >
                  {localizationService.t('capture_right_thumb')}
                </Button>
              )}
            </Paper>
          </Grid>
        </Grid>

        {/* Progress Summary */}
        {(hasLeftThumb || hasRightThumb) && (
          <Box sx={{ mt: 3 }}>
            <Typography variant="subtitle2" gutterBottom>
              {localizationService.tp('capture_progress', { completed: (hasLeftThumb ? 1 : 0) + (hasRightThumb ? 1 : 0) })}
            </Typography>
            <LinearProgress 
              variant="determinate" 
              value={((hasLeftThumb ? 1 : 0) + (hasRightThumb ? 1 : 0)) * 50} 
              sx={{ height: 8, borderRadius: 4 }}
            />
          </Box>
        )}

        {allCaptured && (
          <Alert severity="success" sx={{ mt: 3 }}>
            <Typography variant="subtitle2">
              {localizationService.t('all_fingerprints_captured')}
            </Typography>
          </Alert>
        )}

        {/* Validation Errors */}
        {formik.touched.left_thumb_fingerprint && formik.errors.left_thumb_fingerprint && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {formik.errors.left_thumb_fingerprint}
          </Alert>
        )}
        {formik.touched.right_thumb_fingerprint && formik.errors.right_thumb_fingerprint && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {formik.errors.right_thumb_fingerprint}
          </Alert>
        )}

        {/* Progress Indicator */}
        {!allCaptured && (
          <Alert severity="info" sx={{ mt: 2 }}>
            <Typography variant="body2">
              {localizationService.tp('thumbprints_captured', { completed: (hasLeftThumb ? 1 : 0) + (hasRightThumb ? 1 : 0) })}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {!hasLeftThumb && !hasRightThumb && localizationService.t('capture_both_thumbprints')}
              {hasLeftThumb && !hasRightThumb && localizationService.t('left_thumb_captured_need_right')}
              {!hasLeftThumb && hasRightThumb && localizationService.t('right_thumb_captured_need_left')}
            </Typography>
          </Alert>
        )}

        {/* Capture Dialog */}
        <Dialog
          open={captureDialog}
          onClose={closeDialog}
          maxWidth="md"
          fullWidth
          disableEscapeKeyDown={captureStatus === 'capturing'}
        >
          <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            {localizationService.tp('capturing_thumb_fingerprint', { thumb: currentCapture })}
            {captureStatus !== 'capturing' && (
              <IconButton onClick={closeDialog}>
                <CloseIcon />
              </IconButton>
            )}
          </DialogTitle>
          <DialogContent>
            <FingerprintCapture
              thumbType={currentCapture}
              onCaptureComplete={handleCaptureComplete}
              onCaptureError={handleCaptureError}
              onProgressUpdate={setCaptureProgress}
              onStatusUpdate={setCaptureStatus}
              deviceConnected={deviceConnected}
            />
          </DialogContent>
          <DialogActions>
            {captureStatus === 'error' && (
              <Button onClick={retryCapture} startIcon={<RefreshIcon />}>
                {localizationService.t('retry')}
              </Button>
            )}
            {captureStatus !== 'capturing' && (
              <Button onClick={closeDialog}>
                {localizationService.t('close')}
              </Button>
            )}
          </DialogActions>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default BiometricCaptureStep;
