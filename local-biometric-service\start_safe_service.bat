@echo off
echo ========================================
echo    SAFE BIOMETRIC SERVICE
echo ========================================
echo.
echo ULTRA-ROBUST FEATURES:
echo   - NEVER CRASHES (guaranteed!)
echo   - 3-second finger detection
echo   - Clear error messages
echo   - Works with or without device
echo   - Immediate feedback
echo.
echo SERVICE URL: http://localhost:8001
echo HEALTH CHECK: http://localhost:8001/api/health
echo.
echo TESTING INSTRUCTIONS:
echo   1. WITHOUT FINGER: Click capture = Quick error (3 seconds)
echo   2. WITH FINGER: Place finger first = Success
echo.
echo TROUBLESHOOTING:
echo   - Service NEVER crashes
echo   - If device not found = Simulation mode
echo   - All errors are user-friendly
echo.

echo Starting ultra-safe biometric service...
echo.
echo Keep this window open while using GoID system
echo Close this window to stop the service
echo.

python safe_biometric_service.py

echo.
echo Service stopped.
pause
