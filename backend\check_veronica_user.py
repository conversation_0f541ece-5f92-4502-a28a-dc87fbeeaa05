#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to check the <NAME_EMAIL> and fix role issues.
"""

import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from tenants.models import Tenant
from users.models import UserRole

User = get_user_model()

def check_veronica_user():
    """Check the <NAME_EMAIL>."""
    print("=== CHECKING VERONICA USER ===\n")
    
    # Try different ways to find the user
    search_criteria = [
        ('username', '<EMAIL>'),
        ('email', '<EMAIL>'),
        ('username__icontains', 'veronica'),
        ('email__icontains', 'veronica'),
    ]
    
    found_users = []
    
    for field, value in search_criteria:
        try:
            if '__icontains' in field:
                users = User.objects.filter(**{field: value})
                if users.exists():
                    found_users.extend(list(users))
            else:
                user = User.objects.get(**{field: value})
                found_users.append(user)
        except User.DoesNotExist:
            continue
        except User.MultipleObjectsReturned:
            users = User.objects.filter(**{field: value})
            found_users.extend(list(users))
    
    # Remove duplicates
    found_users = list(set(found_users))
    
    if not found_users:
        print("❌ No users found matching 'veronica'")
        print("\n=== ALL USERS IN SYSTEM ===")
        all_users = User.objects.all()
        for user in all_users:
            print(f"ID: {user.id}, Username: {user.username}, Email: {user.email}, Role: {user.role}, Tenant: {user.tenant}")
        return
    
    print(f"✅ Found {len(found_users)} user(s) matching 'veronica':\n")
    
    for i, user in enumerate(found_users, 1):
        print(f"{i}. User Details:")
        print(f"   ID: {user.id}")
        print(f"   Username: {user.username}")
        print(f"   Email: {user.email}")
        print(f"   Role: {user.role}")
        print(f"   Is Superuser: {user.is_superuser}")
        print(f"   Is Active: {user.is_active}")
        
        if user.tenant:
            print(f"   Tenant: {user.tenant.name} (ID: {user.tenant.id})")
            print(f"   Tenant Type: {user.tenant.type}")
            print(f"   Tenant Schema: {user.tenant.schema_name}")
            if user.tenant.parent:
                print(f"   Parent Tenant: {user.tenant.parent.name} (ID: {user.tenant.parent.id})")
        else:
            print(f"   Tenant: None")
        
        # Check if this is a role-tenant mismatch
        if user.tenant:
            valid_roles = {
                'city': ['city_admin'],
                'subcity': ['subcity_admin'],
                'kebele': ['kebele_leader', 'clerk', 'kebele_admin']
            }
            
            tenant_type = user.tenant.type
            if tenant_type in valid_roles and user.role not in valid_roles[tenant_type]:
                print(f"   ❌ ROLE MISMATCH: {user.role} not valid for {tenant_type} tenant")
                print(f"   Valid roles for {tenant_type}: {', '.join(valid_roles[tenant_type])}")
                
                # Offer to fix
                if user.role == 'subcity_admin' and tenant_type == 'kebele':
                    print(f"   🔧 SUGGESTED FIX: Change role to 'clerk'")
                    
                    response = input(f"   Fix this user's role to 'clerk'? (y/n): ")
                    if response.lower() == 'y':
                        old_role = user.role
                        user.role = 'clerk'
                        user.save()
                        print(f"   ✅ Fixed: {old_role} → {user.role}")
                    else:
                        print(f"   ⏭️  Skipped fixing this user")
            else:
                print(f"   ✅ Role is correct for tenant type")
        
        print()

def check_kebele14_tenant():
    """Check the Kebele14 tenant and its users."""
    print("=== CHECKING KEBELE14 TENANT ===\n")
    
    try:
        kebele14 = Tenant.objects.get(name='Kebele14')
        print(f"✅ Found Kebele14 tenant:")
        print(f"   ID: {kebele14.id}")
        print(f"   Name: {kebele14.name}")
        print(f"   Type: {kebele14.type}")
        print(f"   Schema: {kebele14.schema_name}")
        if kebele14.parent:
            print(f"   Parent: {kebele14.parent.name} (ID: {kebele14.parent.id})")
        
        # Check users in this tenant
        users_in_kebele14 = User.objects.filter(tenant=kebele14)
        print(f"\n📋 Users in Kebele14 tenant ({users_in_kebele14.count()}):")
        
        for user in users_in_kebele14:
            print(f"   - {user.username} ({user.email}) - Role: {user.role}")
            
            # Check for role mismatches
            if user.role not in ['kebele_leader', 'clerk', 'kebele_admin']:
                print(f"     ❌ INVALID ROLE: {user.role} not valid for kebele tenant")
        
    except Tenant.DoesNotExist:
        print("❌ Kebele14 tenant not found")
        print("\n=== ALL TENANTS ===")
        tenants = Tenant.objects.all()
        for tenant in tenants:
            print(f"   {tenant.name} (type: {tenant.type}, ID: {tenant.id})")

def main():
    """Main function."""
    print("GoID Veronica User Checker")
    print("=" * 30)
    
    check_veronica_user()
    check_kebele14_tenant()

if __name__ == '__main__':
    main()
