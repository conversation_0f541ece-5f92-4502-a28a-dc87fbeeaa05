import { useAuth } from '../contexts/AuthContext';

// Map frontend permission names to backend general permission codenames
const PERMISSION_MAPPING = {
  // Dashboard - based on role level
  'view_dashboard': ['view_kebele_dashboard', 'view_subcity_dashboard', 'view_city_dashboard', 'view_system_dashboard'],

  // Citizens - workflow-based permissions
  'view_citizens': ['view_citizens_list', 'view_own_kebele_data', 'view_child_kebeles_data', 'view_child_subcities_data', 'view_all_tenants_data'],
  'manage_citizens': ['view_citizens_list', 'view_citizen_details', 'view_child_kebeles_data'], // Subcity admins can manage (view/oversee) citizens
  'register_citizens': ['register_citizens'], // Only clerks can register

  // ID Cards - workflow-based permissions
  'view_idcards': ['view_id_cards_list', 'view_own_kebele_data', 'view_child_kebeles_data', 'view_child_subcities_data'],
  'manage_idcards': ['view_id_cards_list', 'approve_id_cards'], // Subcity admins can manage (view/approve) ID cards
  'create_idcards': ['generate_id_cards'], // Only clerks can generate
  'approve_idcards': ['approve_id_cards'],
  'print_idcards': ['generate_id_cards', 'approve_id_cards', 'print_id_cards'], // Users who can generate, approve, or have print permission

  // Tenants - system administration
  'view_tenants': ['manage_tenants', 'full_system_access'],
  'manage_tenants': ['manage_tenants', 'full_system_access'],
  'create_tenants': ['manage_tenants', 'full_system_access'],
  'edit_tenants': ['manage_tenants', 'full_system_access'],
  'delete_tenants': ['manage_tenants', 'full_system_access'],

  // Users - hierarchical user management
  'view_users': ['create_kebele_users', 'create_subcity_users', 'manage_all_users'],
  'manage_users': ['create_kebele_users', 'create_subcity_users', 'manage_all_users'],
  'create_users': ['create_kebele_users', 'create_subcity_users', 'manage_all_users'],

  // Reports - hierarchical reporting
  'view_reports': ['view_kebele_reports', 'view_subcity_reports', 'view_city_reports', 'view_all_reports'],

  // Workflows - document verification and processing
  'view_workflows': ['verify_documents', 'approve_id_cards'],

  // System settings (only for high-level admins)
  'system_settings': ['manage_system_settings', 'full_system_access'],

  // Cross-tenant views (hierarchical data access)
  'view_cross_kebele': ['view_child_kebeles_data'], // Subcity admins can view child kebeles
  'view_cross_subcity': ['view_child_subcities_data'], // City admins can view child subcities

  // Document verification
  'verify_documents': ['verify_documents'],

  // Transfer citizens
  'transfer_citizens': ['verify_documents', 'approve_id_cards'],

  // Clearances - flexible permissions for any group
  'create_clearances': ['verify_documents', 'view_citizens_list'], // Can create clearance requests
  'view_clearances': ['view_citizens_list'], // Can view clearance lists
  'manage_clearances': ['verify_documents', 'view_citizens_list'], // Can manage clearances

  // Transfers - flexible permissions for any group
  'create_transfers': ['verify_documents', 'view_citizens_list'], // Can create transfer requests
  'view_transfers': ['view_citizens_list'], // Can view transfer lists
  'manage_transfers': ['verify_documents', 'view_citizens_list'] // Can manage transfers
};

// Role-based permissions aligned with new general permission system
const ROLE_PERMISSIONS = {
  superadmin: [
    'view_dashboard',
    'manage_tenants',
    'view_tenants',
    'create_tenants',
    'edit_tenants',
    'delete_tenants',
    'view_citizens',
    'manage_citizens',
    'register_citizens',
    'view_idcards',
    'manage_idcards',
    'create_idcards',
    'approve_idcards',
    'print_idcards',
    'view_reports',
    'manage_users',
    'create_users',
    'view_workflows',
    'verify_documents',
    'system_settings',
    'view_cross_kebele',
    'view_cross_subcity'
  ],
  city_admin: [
    'view_dashboard',
    'view_citizens',
    'manage_citizens', // Can manage citizens city-wide
    'view_idcards',
    'manage_idcards', // Can manage ID cards city-wide
    'view_reports',
    'manage_users', // Can create subcity users
    'create_users',
    'view_workflows',
    'view_cross_subcity' // Can view data from child subcities
  ],
  subcity_admin: [
    'view_dashboard',
    'view_citizens',
    'manage_citizens', // Can manage (view/oversee) citizens from child kebeles
    'view_idcards',
    'manage_idcards', // Can manage (view/approve) ID cards
    'approve_idcards',
    'view_reports',
    'manage_users', // Can create kebele users
    'create_users',
    'view_workflows',
    'view_cross_kebele' // Can view data from child kebeles
  ],
  kebele_admin: [
    'view_dashboard',
    'view_citizens',
    'register_citizens',
    'view_idcards',
    'create_idcards',
    'approve_idcards',
    'view_reports',
    'manage_users', // Can manage users in their kebele
    'create_users',
    'view_workflows',
    'verify_documents'
  ],
  kebele_leader: [
    'view_dashboard',
    'view_citizens',
    'view_idcards',
    'approve_idcards',
    'transfer_citizens',
    'view_reports',
    'verify_documents',
    // Clearance permissions
    'create_clearances',
    'view_clearances',
    'manage_clearances',
    // Transfer permissions
    'create_transfers',
    'view_transfers',
    'manage_transfers'
    // Note: Kebele leaders can approve and verify but cannot create citizens/ID cards or manage users
  ],
  clerk: [
    'view_dashboard',
    'view_citizens',
    'register_citizens',
    'view_idcards',
    'create_idcards',
    // Clearance permissions (basic level)
    'create_clearances',
    'view_clearances',
    // Transfer permissions (basic level)
    'create_transfers',
    'view_transfers'
    // Note: Clerks have basic operational permissions:
    // - Can register citizens (full workflow)
    // - Can generate ID cards
    // - Can view their kebele dashboard
    // - Can create clearances and transfers (but not manage them)
    // - Cannot approve, manage users, or view reports
  ]
};

// Define navigation items with required permissions
export const NAVIGATION_ITEMS = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    path: '/dashboard',
    icon: 'Dashboard',
    permission: 'view_dashboard'
  },
  {
    id: 'citizens',
    label: 'Citizens',
    path: '/citizens',
    icon: 'People',
    permission: 'view_citizens',
    children: [
      {
        id: 'citizens-list',
        label: 'Citizens List',
        path: '/citizens',
        permission: 'view_citizens'
      },
      {
        id: 'citizens-register',
        label: 'Register Citizen',
        path: '/citizens/register',
        permission: 'register_citizens'
      }
    ]
  },
  {
    id: 'idcards',
    label: 'ID Cards',
    path: '/idcards',
    icon: 'Badge',
    permission: 'view_idcards',
    children: [
      {
        id: 'idcards-list',
        label: 'ID Cards List',
        path: '/idcards',
        permission: 'view_idcards'
      },
      {
        id: 'idcards-pending',
        label: 'Pending Approval',
        path: '/idcards/pending',
        permission: 'approve_idcards'
      },
      {
        id: 'idcards-printing',
        label: 'Printing Queue',
        path: '/idcards/printing-queue',
        permission: 'print_idcards'
      }
    ]
  },
  {
    id: 'tenants',
    label: 'Tenants',
    path: '/tenants',
    icon: 'Business',
    permission: 'view_tenants',
    children: [
      {
        id: 'tenants-list',
        label: 'Tenants List',
        path: '/tenants',
        permission: 'view_tenants'
      },
      {
        id: 'tenants-register',
        label: 'Register Tenant',
        path: '/tenants/register',
        permission: 'create_tenants'
      }
    ]
  },
  {
    id: 'reports',
    label: 'Reports',
    path: '/reports',
    icon: 'Assessment',
    permission: 'view_reports'
  },
  {
    id: 'users',
    label: 'Users',
    path: '/users',
    icon: 'SupervisorAccount',
    permission: 'manage_users',
    children: [
      {
        id: 'users-list',
        label: 'Users List',
        path: '/users',
        permission: 'manage_users'
      },
      {
        id: 'users-kebele',
        label: 'Kebele Management',
        path: '/users/kebele-management',
        permission: 'manage_users'
      },
      {
        id: 'users-permissions',
        label: 'Permissions',
        path: '/users/permissions',
        permission: 'manage_users'
      }
    ]
  },
  {
    id: 'workflows',
    label: 'Workflows',
    path: '/workflows',
    icon: 'Timeline',
    permission: 'view_workflows',
    children: [
      {
        id: 'workflows-verification',
        label: 'Document Verification',
        path: '/workflows/verification',
        permission: 'verify_documents'
      },
      {
        id: 'workflows-transfers',
        label: 'Citizen Transfers',
        path: '/workflows/transfers',
        permission: 'transfer_citizens'
      }
    ]
  },
  {
    id: 'settings',
    label: 'Settings',
    path: '/settings',
    icon: 'Settings',
    permission: 'system_settings'
  }
];

export const usePermissions = () => {
  const { user } = useAuth();

  const hasPermission = (permission) => {
    if (!user) {
      console.log(`❌ Permission check failed: No user`);
      return false;
    }

    // Super admin has all permissions
    if (user.is_superuser || user.role === 'superadmin') {
      console.log(`✅ Permission granted: ${permission} (superuser)`);
      return true;
    }

    // Use actual permissions from backend if available (GROUP-BASED SYSTEM)
    if (user.permissions && Array.isArray(user.permissions)) {
      const requiredPermissions = PERMISSION_MAPPING[permission] || [permission];

      // Check if user has any of the required backend permissions
      // user.permissions is already an array of permission codenames (strings)
      const userPermissionCodes = user.permissions;
      const hasAccess = requiredPermissions.some(reqPerm => userPermissionCodes.includes(reqPerm));

      console.log(`🔍 Group-based permission check: ${permission}`, {
        required: requiredPermissions,
        userHas: userPermissionCodes,
        userGroups: user.groups || [],
        result: hasAccess
      });

      return hasAccess;
    }

    // Fallback to role-based permissions if user.permissions not available (LEGACY SYSTEM)
    if (user.role) {
      const userPermissions = ROLE_PERMISSIONS[user.role] || [];
      const hasAccess = userPermissions.includes(permission);

      console.log(`🔍 Legacy role-based permission check: ${permission}`, {
        role: user.role,
        rolePermissions: userPermissions,
        result: hasAccess
      });

      return hasAccess;
    }

    console.log(`❌ Permission check failed: ${permission} (no role or permissions)`);
    return false;
  };

  const getCustomGroupPermissions = () => {
    if (!user || !user.groups) return [];

    // Filter out system groups to identify custom groups
    const systemGroups = ['clerk', 'kebele_leader', 'subcity_admin', 'city_admin', 'super_admin'];
    const customGroups = user.groups.filter(group => !systemGroups.includes(group));

    return customGroups;
  };

  const isUsingCustomGroups = () => {
    const customGroups = getCustomGroupPermissions();
    return customGroups.length > 0;
  };

  const hasAnyPermission = (permissions) => {
    return permissions.some(permission => hasPermission(permission));
  };

  const getFilteredNavigationItems = () => {
    return NAVIGATION_ITEMS.filter(item => {
      // Check if user has permission for main item
      if (!hasPermission(item.permission)) return false;

      // Filter children based on permissions
      if (item.children) {
        item.children = item.children.filter(child => hasPermission(child.permission));
      }

      return true;
    });
  };

  const getDynamicNavigationItems = () => {
    console.log('🔍 getDynamicNavigationItems called:', {
      user: user ? {
        username: user.username,
        role: user.role,
        permissions: user.permissions,
        groups: user.groups,
        is_superuser: user.is_superuser,
        tenant_type: user.tenant_type
      } : null,
      hasUser: Boolean(user),
      hasPermissions: Boolean(user?.permissions),
      permissionsCount: user?.permissions?.length || 0
    });

    // Return empty array if no user
    if (!user) return [];

    // Get user's groups for navigation determination
    const userGroups = user.groups || [];
    console.log('🔍 User groups:', userGroups);

    // Dashboard - always show for authenticated users
    const navigationItems = [{
      id: 'dashboard',
      label: 'Dashboard',
      path: '/dashboard',
      icon: 'Dashboard',
      permission: 'view_dashboard'
    }];

    // Check if user is in specific groups and build navigation accordingly
    const isInGroup = (groupName) => userGroups.includes(groupName);

    // Super Admin Navigation
    if (user.is_superuser || user.role === 'superadmin' || isInGroup('super_admin')) {
      navigationItems.push(
        {
          id: 'tenants',
          label: 'Tenants',
          path: '/tenants',
          icon: 'Business',
          permission: 'manage_tenants'
        },
        {
          id: 'users',
          label: 'System Users',
          path: '/users',
          icon: 'SupervisorAccount',
          permission: 'manage_all_users'
        },
        {
          id: 'settings',
          label: 'System Settings',
          path: '/system/settings',
          icon: 'Settings',
          permission: 'manage_system_settings'
        }
      );

      console.log('✅ Super admin navigation generated');
      return navigationItems.filter(item => hasPermission(item.permission));
    }

    // City Admin Navigation
    if (isInGroup('city_admin')) {
      console.log('🏢 Detected city_admin group - generating city admin navigation');
      navigationItems.push(
        {
          id: 'citizen_dictionary',
          label: 'Citizen Dictionary',
          path: '/citizens/citizen-book',
          icon: 'People',
          permission: 'view_cross_subcity'
        },
        {
          id: 'users',
          label: 'Subcity Users',
          path: '/users/city-management',
          icon: 'SupervisorAccount',
          permission: 'create_subcity_users'
        },
        {
          id: 'reports',
          label: 'Reports',
          path: '/reports',
          icon: 'Assessment',
          permission: 'view_city_reports'
        }
      );

      console.log('✅ City admin navigation generated with items:', navigationItems);
      return navigationItems.filter(item => hasPermission(item.permission));
    }

    // SubCity Admin Navigation
    if (isInGroup('subcity_admin')) {
      navigationItems.push(
        {
          id: 'citizens',
          label: 'Citizens',
          path: '/citizens/all-kebeles',
          icon: 'People',
          permission: 'view_child_kebeles_data'
        },
        {
          id: 'idcards',
          label: 'ID Cards',
          path: '/idcards/all-kebeles',
          icon: 'Badge',
          permission: 'view_id_cards_list'
        },
        {
          id: 'print-queue',
          label: 'Print Queue',
          path: '/idcards/printing-queue',
          icon: 'Print',
          permission: 'print_id_cards'
        },
        {
          id: 'service-requests',
          label: 'Service Requests',
          path: '/service-requests',
          icon: 'Assignment',
          permission: 'view_service_requests'
        },
        {
          id: 'users',
          label: 'Kebele Users',
          path: '/users/kebele-management',
          icon: 'SupervisorAccount',
          permission: 'create_kebele_users'
        },
        {
          id: 'reports',
          label: 'Reports',
          path: '/reports',
          icon: 'Assessment',
          permission: 'view_subcity_reports'
        }
      );

      console.log('✅ SubCity admin navigation generated');
      return navigationItems.filter(item => hasPermission(item.permission));
    }

    // Kebele Leader Navigation
    if (isInGroup('kebele_leader')) {
      navigationItems.push(
        {
          id: 'citizens',
          label: 'Citizens',
          path: '/citizens',
          icon: 'People',
          permission: 'view_citizens_list'
        },
        {
          id: 'idcards',
          label: 'ID Cards',
          path: '/idcards',
          icon: 'Badge',
          permission: 'view_id_cards_list'
        },
        {
          id: 'clearances',
          label: 'Clearance',
          path: '/clearances',
          icon: 'Assignment',
          permission: 'create_clearances'
        },
        {
          id: 'transfers',
          label: 'Transfer',
          path: '/transfers',
          icon: 'SwapHoriz',
          permission: 'create_transfers'
        }
      );

      console.log('✅ Kebele leader navigation generated');
      return navigationItems.filter(item => hasPermission(item.permission));
    }

    // Clerk Navigation
    if (isInGroup('clerk')) {
      navigationItems.push(
        {
          id: 'citizens',
          label: 'Citizens',
          path: '/citizens',
          icon: 'People',
          permission: 'register_citizens'
        },
        {
          id: 'idcards',
          label: 'ID Cards',
          path: '/idcards',
          icon: 'Badge',
          permission: 'generate_id_cards'
        }
      );

      console.log('✅ Clerk navigation generated');
      return navigationItems.filter(item => hasPermission(item.permission));
    }

    // Custom Groups - Dynamic Navigation
    // For users not in standard groups, build navigation dynamically based on permissions
    console.log('🔍 Building dynamic navigation for custom groups or unmatched users');

    // Citizens menu - show if user has citizen permissions
    if (hasPermission('register_citizens') || hasPermission('view_citizens_list') || hasPermission('view_citizen_details')) {
      const citizensItem = {
        id: 'citizens',
        label: 'Citizens',
        icon: 'People',
        permission: 'view_citizens_list'
      };

      // Determine path based on permissions (using same logic as working static navbar)
      if (hasPermission('view_cross_subcity')) {
        // City admins get citizen dictionary (read-only)
        console.log('🏢 City admin detected in fallback navigation - setting citizen dictionary');
        citizensItem.path = '/citizens/citizen-book';
        citizensItem.label = 'Citizen Dictionary';
        citizensItem.id = 'citizen_dictionary';
      } else if (hasPermission('view_cross_kebele')) {
        console.log('🏘️ Subcity admin detected in fallback navigation');
        citizensItem.path = '/citizens/all-kebeles';
      } else {
        console.log('🏠 Kebele user detected in fallback navigation');
        citizensItem.path = '/citizens';
      }

      navigationItems.push(citizensItem);
    }

    // ID Cards menu - show if user has ID card permissions
    if (hasPermission('generate_id_cards') || hasPermission('view_id_cards_list') || hasPermission('approve_id_cards') || hasPermission('print_id_cards')) {
      const idCardsItem = {
        id: 'idcards',
        label: 'ID Cards',
        icon: 'Badge',
        permission: 'view_id_cards_list'
      };

      // Determine path based on permissions (using same logic as working static navbar)
      if (hasPermission('view_cross_kebele') || hasPermission('view_cross_subcity')) {
        // Subcity and City admins get cross-kebele view
        idCardsItem.path = '/idcards/all-kebeles';
      } else {
        idCardsItem.path = '/idcards';
      }

      navigationItems.push(idCardsItem);
    }

    // Print Queue - show if user has printing permissions
    if (hasPermission('print_id_cards')) {
      navigationItems.push({
        id: 'print-queue',
        label: 'Print Queue',
        path: '/idcards/printing-queue',
        icon: 'Print',
        permission: 'print_id_cards'
      });
    }

    // Service Requests - show if user has service request permissions
    if (hasPermission('view_service_requests') || hasPermission('manage_service_requests')) {
      navigationItems.push({
        id: 'service-requests',
        label: 'Service Requests',
        path: '/service-requests',
        icon: 'Assignment',
        permission: 'view_service_requests'
      });
    }

    // Users menu - show if user has user management permissions
    if (hasPermission('manage_all_users') || hasPermission('create_subcity_users') || hasPermission('create_kebele_users')) {
      const usersItem = {
        id: 'users',
        label: 'Users',
        icon: 'SupervisorAccount'
      };

      if (hasPermission('manage_all_users')) {
        usersItem.path = '/users';
        usersItem.label = 'System Users';
        usersItem.permission = 'manage_all_users';
      } else if (hasPermission('create_subcity_users')) {
        usersItem.path = '/users/city-management';
        usersItem.label = 'Subcity Users';
        usersItem.permission = 'create_subcity_users';
      } else if (hasPermission('create_kebele_users')) {
        usersItem.path = '/users/kebele-management';
        usersItem.label = 'Kebele Users';
        usersItem.permission = 'create_kebele_users';
      }

      navigationItems.push(usersItem);
    }

    // Clearances menu - show if user has clearance permissions
    if (hasPermission('create_clearances') || hasPermission('view_clearances') || hasPermission('manage_clearances')) {
      navigationItems.push({
        id: 'clearances',
        label: 'Clearance',
        path: '/clearances',
        icon: 'Assignment',
        permission: 'create_clearances'
      });
    }

    // Transfers menu - show if user has transfer permissions
    if (hasPermission('create_transfers') || hasPermission('view_transfers') || hasPermission('manage_transfers')) {
      navigationItems.push({
        id: 'transfers',
        label: 'Transfer',
        path: '/transfers',
        icon: 'SwapHoriz',
        permission: 'create_transfers'
      });
    }

    // Reports menu - show if user has report permissions
    if (hasPermission('view_reports') || hasPermission('view_kebele_reports') || hasPermission('view_subcity_reports') || hasPermission('view_city_reports')) {
      navigationItems.push({
        id: 'reports',
        label: 'Reports',
        path: '/reports',
        icon: 'Assessment',
        permission: 'view_reports'
      });
    }

    // Filter navigation items based on permissions
    const filteredItems = navigationItems.filter(item => {
      // Always show dashboard
      if (item.id === 'dashboard') return true;

      // Check if user has the required permission for this item
      return hasPermission(item.permission);
    });

    console.log('✅ Generated dynamic navigation items:', filteredItems);
    console.log('🔍 User permissions:', user.permissions);
    console.log('🔍 User groups:', user.groups);
    console.log('🔍 User role:', user.role);

    return filteredItems;
  };

  const canAccess = (path) => {
    const item = NAVIGATION_ITEMS.find(nav =>
      nav.path === path ||
      (nav.children && nav.children.some(child => child.path === path))
    );

    if (!item) return true; // Allow access to unprotected routes

    if (item.children) {
      const childItem = item.children.find(child => child.path === path);
      return childItem ? hasPermission(childItem.permission) : hasPermission(item.permission);
    }

    return hasPermission(item.permission);
  };

  return {
    hasPermission,
    hasAnyPermission,
    getFilteredNavigationItems,
    getDynamicNavigationItems,
    canAccess,
    getCustomGroupPermissions,
    isUsingCustomGroups,
    userRole: user?.role,
    userPermissions: user?.permissions || [],
    userGroups: user?.groups || [],
    customGroups: getCustomGroupPermissions(),
    isAdmin: user?.role?.includes('admin') || user?.is_superuser,
    isSuperAdmin: user?.is_superuser || user?.role === 'superadmin'
  };
};
