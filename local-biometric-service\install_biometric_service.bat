@echo off
echo ========================================
echo GoID Local Biometric Service Installer
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo ✅ Python found
echo.

REM Create service directory
set SERVICE_DIR=C:\GoID\BiometricService
if not exist "%SERVICE_DIR%" (
    echo 📁 Creating service directory: %SERVICE_DIR%
    mkdir "%SERVICE_DIR%"
)

REM Copy service files
echo 📋 Copying service files...
copy biometric_service.py "%SERVICE_DIR%\"
copy requirements.txt "%SERVICE_DIR%\"
copy start_service.bat "%SERVICE_DIR%\"
copy futronic_sdk.py "%SERVICE_DIR%\"
copy setup_futronic_sdk.bat "%SERVICE_DIR%\"
copy test_futronic_sdk.py "%SERVICE_DIR%" 2>nul

REM Copy SDK DLL files if they exist
echo 📋 Copying SDK DLL files (if available)...
copy ftrScanAPI.dll "%SERVICE_DIR%\" 2>nul
copy ftrMathAPI.dll "%SERVICE_DIR%\" 2>nul
copy *.dll "%SERVICE_DIR%\" 2>nul

REM Install Python dependencies
echo 📦 Installing Python dependencies...
cd /d "%SERVICE_DIR%"
pip install -r requirements.txt

REM Create Windows service (optional)
echo 🔧 Setting up Windows service...
echo.
echo [Service Configuration]
echo Service Name: GoIDBiometricService
echo Service Path: %SERVICE_DIR%
echo Port: 8001
echo.

REM Create startup shortcut
echo 🚀 Creating startup shortcut...
set STARTUP_DIR=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup
echo @echo off > "%STARTUP_DIR%\GoID_Biometric_Service.bat"
echo cd /d "%SERVICE_DIR%" >> "%STARTUP_DIR%\GoID_Biometric_Service.bat"
echo start /min python biometric_service.py >> "%STARTUP_DIR%\GoID_Biometric_Service.bat"

REM Create desktop shortcut
echo 🖥️ Creating desktop shortcut...
echo @echo off > "%USERPROFILE%\Desktop\Start_GoID_Biometric.bat"
echo cd /d "%SERVICE_DIR%" >> "%USERPROFILE%\Desktop\Start_GoID_Biometric.bat"
echo python biometric_service.py >> "%USERPROFILE%\Desktop\Start_GoID_Biometric.bat"
echo pause >> "%USERPROFILE%\Desktop\Start_GoID_Biometric.bat"

REM Configure Windows Firewall
echo 🔥 Configuring Windows Firewall...
netsh advfirewall firewall add rule name="GoID Biometric Service" dir=in action=allow protocol=TCP localport=8001

echo.
echo ✅ Installation completed successfully!
echo.
echo 📋 Next Steps:
echo 1. Connect your Futronic FS88H device
echo 2. Install Futronic device drivers
echo 3. Start the service by running: start_service.bat
echo 4. Test the service at: http://localhost:8001/api/health
echo.
echo 🔧 The service will automatically start when Windows boots
echo 🌐 Access GoID system from: http://[SERVER_IP]:3000
echo.
pause
