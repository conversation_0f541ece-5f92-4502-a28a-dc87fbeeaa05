<!DOCTYPE html>
<html>
<head>
    <title>Debug Permission System</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .user-section { border: 1px solid #ccc; margin: 10px 0; padding: 15px; }
        .permission { margin: 5px 0; }
        .has-permission { color: green; }
        .no-permission { color: red; }
        .backend-permission { font-size: 0.8em; color: #666; margin-left: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <h1>Permission System Debug</h1>
    
    <div class="user-section">
        <h2>Subcity Admin User (<EMAIL>)</h2>
        <p><strong>Expected:</strong> Should have 60 permissions and see navigation menu</p>
        <div id="subcity-admin-test"></div>
    </div>
    
    <div class="user-section">
        <h2>Clerk User (<EMAIL>)</h2>
        <p><strong>Expected:</strong> Should have 47 permissions and see basic navigation</p>
        <div id="clerk-test"></div>
    </div>

    <script>
        // Mock user data based on our API response
        const subcityAdminUser = {
            id: 3,
            email: '<EMAIL>',
            role: 'subcity_admin',
            permissions: [
                // Key permissions for subcity admin
                { codename: 'view_citizen', name: 'Can view Citizen' },
                { codename: 'add_citizen', name: 'Can add Citizen' },
                { codename: 'change_citizen', name: 'Can change Citizen' },
                { codename: 'delete_citizen', name: 'Can delete Citizen' },
                { codename: 'view_idcard', name: 'Can view ID Card' },
                { codename: 'add_idcard', name: 'Can add ID Card' },
                { codename: 'change_idcard', name: 'Can change ID Card' },
                { codename: 'delete_idcard', name: 'Can delete ID Card' },
                { codename: 'view_user', name: 'Can view User' },
                { codename: 'add_user', name: 'Can add User' },
                { codename: 'change_user', name: 'Can change User' },
                // Note: No view_tenant permission (city-level access)
            ]
        };

        const clerkUser = {
            id: 1,
            email: '<EMAIL>',
            role: 'clerk',
            permissions: [
                { codename: 'add_citizen', name: 'Can add Citizen' },
                { codename: 'change_citizen', name: 'Can change Citizen' },
                { codename: 'view_citizen', name: 'Can view Citizen' },
                { codename: 'add_idcard', name: 'Can add ID Card' },
                { codename: 'change_idcard', name: 'Can change ID Card' },
                { codename: 'view_idcard', name: 'Can view ID Card' },
                // Note: No delete permissions, no user management
            ]
        };

        // Permission mapping (same as frontend)
        const PERMISSION_MAPPING = {
            'view_dashboard': ['view_citizen', 'view_idcard', 'view_user'],
            'view_citizens': ['view_citizen'],
            'manage_citizens': ['add_citizen', 'change_citizen', 'delete_citizen'],
            'register_citizens': ['add_citizen'],
            'view_idcards': ['view_idcard'],
            'manage_idcards': ['add_idcard', 'change_idcard', 'delete_idcard'],
            'create_idcards': ['add_idcard'],
            'approve_idcards': ['delete_citizen', 'delete_idcard'],
            'print_idcards': ['change_idcard'],
            'view_tenants': ['view_tenant'],
            'manage_users': ['add_user', 'change_user', 'view_user'],
            'view_reports': ['delete_citizen', 'delete_idcard', 'view_tenant'],
        };

        function hasPermission(user, permission) {
            if (!user || !user.permissions) return false;
            
            const requiredPermissions = PERMISSION_MAPPING[permission] || [permission];
            const userPermissionCodes = user.permissions.map(p => p.codename);
            return requiredPermissions.some(reqPerm => userPermissionCodes.includes(reqPerm));
        }

        function testNavigationPermissions(user, containerId) {
            const container = document.getElementById(containerId);
            const navigationTests = [
                'view_dashboard',
                'view_citizens',
                'view_idcards',
                'approve_idcards',
                'manage_users',
                'view_reports',
                'view_tenants'
            ];

            let html = '<h3>Navigation Permission Tests:</h3>';
            
            navigationTests.forEach(permission => {
                const hasAccess = hasPermission(user, permission);
                const className = hasAccess ? 'has-permission' : 'no-permission';
                const status = hasAccess ? '✅ SHOW' : '❌ HIDE';
                
                html += `<div class="permission ${className}">
                    <strong>${permission}:</strong> ${status}
                    <div class="backend-permission">Requires: ${PERMISSION_MAPPING[permission] ? PERMISSION_MAPPING[permission].join(', ') : permission}</div>
                </div>`;
            });

            // Test expected navigation items
            html += '<h3>Expected Navigation Items:</h3>';
            
            if (hasPermission(user, 'view_dashboard')) {
                html += '<div class="test-result success">✅ Dashboard should show</div>';
            } else {
                html += '<div class="test-result error">❌ Dashboard should show but permission missing</div>';
            }
            
            if (hasPermission(user, 'view_citizens')) {
                if (hasPermission(user, 'manage_users')) {
                    html += '<div class="test-result success">✅ Citizens (cross-kebele view) should show</div>';
                } else {
                    html += '<div class="test-result success">✅ Citizens (kebele view) should show</div>';
                }
            } else {
                html += '<div class="test-result error">❌ Citizens should show but permission missing</div>';
            }
            
            if (hasPermission(user, 'view_idcards')) {
                html += '<div class="test-result success">✅ ID Cards should show</div>';
            } else {
                html += '<div class="test-result error">❌ ID Cards should show but permission missing</div>';
            }
            
            if (hasPermission(user, 'manage_users')) {
                html += '<div class="test-result success">✅ Users should show</div>';
            } else {
                html += '<div class="test-result success">✅ Users should be hidden (correct for this role)</div>';
            }

            html += '<h3>Backend Permissions:</h3>';
            user.permissions.forEach(perm => {
                html += `<div class="backend-permission">✅ ${perm.codename}: ${perm.name}</div>`;
            });

            container.innerHTML = html;
        }

        // Test both users
        testNavigationPermissions(subcityAdminUser, 'subcity-admin-test');
        testNavigationPermissions(clerkUser, 'clerk-test');
    </script>
</body>
</html>
