from django.contrib import admin
from .models import Citizen


@admin.register(Citizen)
class CitizenAdmin(admin.ModelAdmin):
    list_display = ('get_full_name', 'gender', 'date_of_birth', 'phone', 'created_at')
    list_filter = ('gender', 'marital_status', 'is_active')
    search_fields = ('first_name', 'middle_name', 'last_name', 'phone', 'email')
    readonly_fields = ('created_at', 'updated_at', 'digital_id')
    fieldsets = (
        ('Personal Information', {
            'fields': ('first_name', 'middle_name', 'last_name', 'first_name_am', 'middle_name_am', 'last_name_am', 'date_of_birth', 'gender', 'photo')
        }),
        ('Contact Information', {
            'fields': ('phone', 'email', 'house_number')
        }),
        ('Additional Information', {
            'fields': ('nationality', 'religion', 'status', 'marital_status', 'employment', 'employee_type', 'organization_name')
        }),
        ('Location Information', {
            'fields': ('subcity', 'kebele', 'ketena', 'region')
        }),
        ('Status Information', {
            'fields': ('is_resident', 'is_active')
        }),
        ('System Information', {
            'fields': ('digital_id', 'created_at', 'updated_at')
        }),
    )
