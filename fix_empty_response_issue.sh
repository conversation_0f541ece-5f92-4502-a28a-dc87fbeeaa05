#!/bin/bash

# Fix ERR_EMPTY_RESPONSE Issue Script
# This script diagnoses and fixes backend connectivity issues

echo "🔧 Fixing ERR_EMPTY_RESPONSE Issue"
echo "=================================="

# Step 1: Check current container status
echo "📍 Step 1: Checking container status..."
echo "Container Status:"
docker-compose ps

echo ""
echo "📍 Step 2: Checking backend logs for errors..."
echo "Recent Backend Logs (last 50 lines):"
docker-compose logs --tail=50 backend

echo ""
echo "📍 Step 3: Testing basic connectivity..."

# Test if backend port is accessible
echo "Testing port 8000 connectivity..."
nc -zv ************ 8000 2>&1 || echo "Port 8000 not accessible"

# Test basic HTTP response
echo "Testing basic HTTP response..."
curl -v -m 10 http://************:8000/ 2>&1 | head -20

echo ""
echo "📍 Step 4: Checking for backend crashes..."
echo "Looking for error patterns in logs..."
docker-compose logs backend | grep -i "error\|exception\|traceback\|crash" | tail -10

echo ""
echo "📍 Step 5: Restarting backend service..."
echo "Stopping backend..."
docker-compose stop backend

echo "Starting backend..."
docker-compose up -d backend

echo "Waiting for backend to start..."
sleep 30

echo "Testing backend after restart..."
curl -I http://************:8000/admin/ 2>&1

echo ""
echo "📍 Step 6: Testing specific endpoints..."

# Test admin endpoint
echo "Testing admin endpoint..."
ADMIN_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://************:8000/admin/ 2>/dev/null || echo "000")
echo "Admin endpoint status: $ADMIN_STATUS"

# Test auth endpoint
echo "Testing auth endpoint..."
AUTH_RESPONSE=$(curl -s -X POST http://************:8000/api/auth/token/ \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}' 2>/dev/null || echo "FAILED")

if [ "$AUTH_RESPONSE" != "FAILED" ] && echo "$AUTH_RESPONSE" | grep -q "access"; then
    echo "✅ Auth endpoint working"
    
    # Extract token
    TOKEN=$(echo "$AUTH_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin)['access'])" 2>/dev/null)
    
    if [ ! -z "$TOKEN" ]; then
        echo "Testing tenants endpoint with auth..."
        TENANTS_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" http://************:8000/api/tenants/ 2>/dev/null || echo "FAILED")
        
        if [ "$TENANTS_RESPONSE" != "FAILED" ]; then
            echo "✅ Tenants endpoint responding"
            echo "Response: $TENANTS_RESPONSE"
        else
            echo "❌ Tenants endpoint failed"
        fi
    else
        echo "❌ Could not extract token"
    fi
else
    echo "❌ Auth endpoint failed"
    echo "Response: $AUTH_RESPONSE"
fi

echo ""
echo "📍 Step 7: Checking Django configuration..."
echo "Running Django system check..."
docker-compose exec backend python manage.py check 2>&1

echo ""
echo "📍 Step 8: Testing database connectivity..."
echo "Testing database connection..."
docker-compose exec backend python manage.py shell -c "
from django.db import connection
try:
    cursor = connection.cursor()
    cursor.execute('SELECT 1')
    print('✅ Database connection successful')
    
    # Test tenant model
    from tenants.models import Tenant
    tenant_count = Tenant.objects.count()
    print(f'✅ Tenant count: {tenant_count}')
    
except Exception as e:
    print(f'❌ Database error: {e}')
"

echo ""
echo "📍 Step 9: Checking middleware issues..."
echo "Testing middleware with simple request..."
docker-compose exec backend python manage.py shell -c "
from django.test import RequestFactory
from django.contrib.auth import get_user_model
from tenants.middleware import TenantMiddleware

try:
    factory = RequestFactory()
    request = factory.get('/api/tenants/')
    
    # Test middleware
    middleware = TenantMiddleware(lambda r: None)
    result = middleware.process_request(request)
    print(f'✅ Middleware test passed: {result}')
    
except Exception as e:
    print(f'❌ Middleware error: {e}')
    import traceback
    traceback.print_exc()
"

echo ""
echo "📍 Step 10: Checking URL routing..."
echo "Testing URL resolution..."
docker-compose exec backend python manage.py shell -c "
from django.urls import resolve
try:
    match = resolve('/api/tenants/')
    print(f'✅ URL resolves to: {match.func}')
    print(f'✅ View name: {match.view_name}')
except Exception as e:
    print(f'❌ URL resolution failed: {e}')
"

echo ""
echo "📍 Step 11: Final comprehensive restart..."
echo "Performing full service restart..."
docker-compose down
sleep 5
docker-compose up -d db
sleep 15
docker-compose up -d backend
sleep 30
docker-compose up -d frontend
sleep 20

echo "Testing after full restart..."
curl -I http://************:8000/admin/ 2>&1

echo ""
echo "✅ ERR_EMPTY_RESPONSE diagnosis completed!"
echo ""
echo "🔍 Common causes and solutions:"
echo "1. Backend container crashed:"
echo "   - Check logs above for crash details"
echo "   - Look for Python exceptions or memory issues"
echo ""
echo "2. Port binding issues:"
echo "   - Backend not binding to 0.0.0.0:8000"
echo "   - Check Docker network configuration"
echo ""
echo "3. Middleware errors:"
echo "   - Our tenant middleware changes causing crashes"
echo "   - Check middleware test results above"
echo ""
echo "4. Database connection issues:"
echo "   - PostgreSQL not responding"
echo "   - Connection pool exhausted"
echo ""
echo "5. URL routing problems:"
echo "   - Django URL patterns not matching"
echo "   - ViewSet registration issues"
echo ""
echo "💡 Next steps based on results:"
echo "- If containers not running: docker-compose up -d"
echo "- If middleware errors: Check tenant middleware code"
echo "- If database errors: docker-compose restart db"
echo "- If URL errors: Check tenants/urls.py"
echo "- If still failing: Check Docker logs and network"
echo ""
echo "🚨 If completely broken:"
echo "docker-compose down -v"
echo "docker-compose up -d"
echo "(Warning: This will delete all data)"
