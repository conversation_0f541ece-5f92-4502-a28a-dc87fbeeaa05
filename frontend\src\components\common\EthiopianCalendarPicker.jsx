import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Grid,
  Paper,
  Typography,
  IconButton,
  Chip,
  Tooltip
} from '@mui/material';
import {
  ChevronLeft,
  ChevronRight,
  Today as TodayIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import {
  gregorianToEthiopian,
  ethiopianToGregorian,
  formatEthiopianDate,
  getCurrentEthiopianDate,
  getDaysInEthiopianMonth,
  ETHIOPIAN_MONTHS,
  ETHIOPIAN_MONTHS_EN
} from '../../utils/ethiopianCalendar';

/**
 * Ethiopian Calendar Picker Component
 * 
 * This component provides a true Ethiopian calendar interface where:
 * - Users see Ethiopian years (e.g., 2017)
 * - Users see Ethiopian months (Meskerem, Tikimt, etc.)
 * - Users see Ethiopian days (1-30 for regular months, 1-5/6 for Pagume)
 * - Selected date is converted to Gregorian for system storage
 */
const EthiopianCalendarPicker = ({
  label = "Select Ethiopian Date",
  value, // Gregorian Date object
  onChange, // Callback with Gregorian Date object
  error = false,
  helperText = "",
  required = false,
  disabled = false,
  language = 'en', // 'en' or 'am'
  showConversion = true,
  minYear = 1900,
  maxYear = 2100,
  ...otherProps
}) => {
  const [selectedEthiopian, setSelectedEthiopian] = useState({
    year: null,
    month: null,
    day: null
  });
  const [currentEthiopian] = useState(() => getCurrentEthiopianDate());

  // Convert Gregorian value to Ethiopian for display
  useEffect(() => {
    if (value && value instanceof Date && !isNaN(value.getTime())) {
      const ethiopianDate = gregorianToEthiopian(value);
      if (ethiopianDate) {
        setSelectedEthiopian(ethiopianDate);
      }
    } else {
      setSelectedEthiopian({ year: null, month: null, day: null });
    }
  }, [value]);

  // Handle Ethiopian date component changes
  const handleEthiopianChange = (component, newValue) => {
    const newEthiopian = { ...selectedEthiopian, [component]: newValue };
    
    // Reset day if month changes and current day is invalid
    if (component === 'month' || component === 'year') {
      const maxDays = getDaysInEthiopianMonth(newEthiopian.month, newEthiopian.year);
      if (newEthiopian.day > maxDays) {
        newEthiopian.day = maxDays;
      }
    }
    
    setSelectedEthiopian(newEthiopian);
    
    // Convert to Gregorian and call onChange if all components are selected
    if (newEthiopian.year && newEthiopian.month && newEthiopian.day) {
      const gregorianDate = ethiopianToGregorian(
        newEthiopian.year,
        newEthiopian.month,
        newEthiopian.day
      );
      if (gregorianDate && onChange) {
        onChange(gregorianDate);
      }
    }
  };

  const setToday = () => {
    const today = new Date();
    const ethiopianToday = getCurrentEthiopianDate();
    setSelectedEthiopian(ethiopianToday);
    if (onChange) {
      onChange(today);
    }
  };

  const getMonthNames = () => {
    return language === 'am' ? ETHIOPIAN_MONTHS : ETHIOPIAN_MONTHS_EN;
  };

  const getDaysInSelectedMonth = () => {
    if (!selectedEthiopian.month || !selectedEthiopian.year) return 30;
    return getDaysInEthiopianMonth(selectedEthiopian.month, selectedEthiopian.year);
  };

  const getConversionInfo = () => {
    if (!selectedEthiopian.year || !selectedEthiopian.month || !selectedEthiopian.day) {
      return null;
    }
    
    const gregorianDate = ethiopianToGregorian(
      selectedEthiopian.year,
      selectedEthiopian.month,
      selectedEthiopian.day
    );
    
    if (!gregorianDate) return null;
    
    return {
      ethiopian: formatEthiopianDate(selectedEthiopian, 'DD MMM YYYY', language),
      gregorian: gregorianDate.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    };
  };

  const conversionInfo = getConversionInfo();
  const monthNames = getMonthNames();
  const maxDays = getDaysInSelectedMonth();

  return (
    <Box>
      <Typography variant="subtitle2" gutterBottom>
        {label}
        {required && <span style={{ color: 'red' }}> *</span>}
        <Tooltip title="Ethiopian Calendar - Select year, month, and day in Ethiopian calendar">
          <IconButton size="small" sx={{ ml: 1 }}>
            <InfoIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Typography>

      <Paper variant="outlined" sx={{ p: 2, bgcolor: error ? 'error.light' : 'background.paper' }}>
        <Grid container spacing={2} alignItems="center">
          {/* Year Selector */}
          <Grid item xs={12} sm={5}>
            <FormControl fullWidth size="small" sx={{ minWidth: 150 }}>
              <InputLabel>Ethiopian Year</InputLabel>
              <Select
                value={selectedEthiopian.year || ''}
                onChange={(e) => handleEthiopianChange('year', e.target.value)}
                label="Ethiopian Year"
                disabled={disabled}
              >
                {Array.from({ length: maxYear - minYear + 1 }, (_, i) => minYear + i).map(year => (
                  <MenuItem key={year} value={year}>
                    {year} {year === currentEthiopian.year && '(Current)'}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Month Selector */}
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Ethiopian Month</InputLabel>
              <Select
                value={selectedEthiopian.month || ''}
                onChange={(e) => handleEthiopianChange('month', e.target.value)}
                label="Ethiopian Month"
                disabled={disabled || !selectedEthiopian.year}
              >
                {monthNames.map((monthName, index) => (
                  <MenuItem key={index + 1} value={index + 1}>
                    {monthName} {index + 1 === currentEthiopian.month && selectedEthiopian.year === currentEthiopian.year && '(Current)'}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Day Selector */}
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth size="small">
              <InputLabel>Day</InputLabel>
              <Select
                value={selectedEthiopian.day || ''}
                onChange={(e) => handleEthiopianChange('day', e.target.value)}
                label="Day"
                disabled={disabled || !selectedEthiopian.year || !selectedEthiopian.month}
              >
                {Array.from({ length: maxDays }, (_, i) => i + 1).map(day => (
                  <MenuItem key={day} value={day}>
                    {day} {day === currentEthiopian.day && 
                         selectedEthiopian.month === currentEthiopian.month && 
                         selectedEthiopian.year === currentEthiopian.year && '(Today)'}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        {/* Today Button */}
        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Chip
            icon={<TodayIcon />}
            label={`Today: ${formatEthiopianDate(currentEthiopian, 'DD MMM YYYY', language)}`}
            onClick={setToday}
            clickable
            size="small"
            color="primary"
            variant="outlined"
          />
        </Box>

        {/* Conversion Display */}
        {showConversion && conversionInfo && (
          <Box sx={{ mt: 2, p: 1, bgcolor: 'grey.50', borderRadius: 1 }}>
            <Typography variant="caption" color="text.secondary">
              <strong>Selected Date:</strong>
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, mt: 0.5, flexWrap: 'wrap' }}>
              <Chip
                size="small"
                label={`Ethiopian: ${conversionInfo.ethiopian}`}
                color="primary"
              />
              <Chip
                size="small"
                label={`Gregorian: ${conversionInfo.gregorian}`}
                color="secondary"
              />
            </Box>
          </Box>
        )}

        {/* Error/Helper Text */}
        {(error || helperText) && (
          <Typography 
            variant="caption" 
            color={error ? 'error' : 'text.secondary'}
            sx={{ mt: 1, display: 'block' }}
          >
            {helperText}
          </Typography>
        )}
      </Paper>

      {/* Debug Info (Development Only) */}
      {process.env.NODE_ENV === 'development' && conversionInfo && (
        <Box sx={{ mt: 1, p: 1, bgcolor: 'info.light', borderRadius: 1 }}>
          <Typography variant="caption">
            <strong>Debug:</strong> Ethiopian {selectedEthiopian.year}/{selectedEthiopian.month}/{selectedEthiopian.day} 
            → Gregorian {value?.toISOString?.()?.split('T')[0]}
          </Typography>
        </Box>
      )}
    </Box>
  );
};

/**
 * Ethiopian Calendar Grid Component
 * Shows a full calendar grid with Ethiopian dates
 */
export const EthiopianCalendarGrid = ({
  selectedDate,
  onDateSelect,
  language = 'en',
  showCurrentMonth = true
}) => {
  const [viewDate, setViewDate] = useState(() => {
    if (selectedDate) {
      return gregorianToEthiopian(selectedDate);
    }
    return getCurrentEthiopianDate();
  });

  const monthNames = language === 'am' ? ETHIOPIAN_MONTHS : ETHIOPIAN_MONTHS_EN;
  const daysInMonth = getDaysInEthiopianMonth(viewDate.month, viewDate.year);
  const currentEthiopian = getCurrentEthiopianDate();
  const selectedEthiopian = selectedDate ? gregorianToEthiopian(selectedDate) : null;

  const navigateMonth = (direction) => {
    let newMonth = viewDate.month + direction;
    let newYear = viewDate.year;

    if (newMonth > 13) {
      newMonth = 1;
      newYear++;
    } else if (newMonth < 1) {
      newMonth = 13;
      newYear--;
    }

    setViewDate({ ...viewDate, month: newMonth, year: newYear });
  };

  const handleDayClick = (day) => {
    const ethiopianDate = { year: viewDate.year, month: viewDate.month, day };
    const gregorianDate = ethiopianToGregorian(ethiopianDate.year, ethiopianDate.month, ethiopianDate.day);
    if (gregorianDate && onDateSelect) {
      onDateSelect(gregorianDate);
    }
  };

  const isToday = (day) => {
    return currentEthiopian.year === viewDate.year &&
           currentEthiopian.month === viewDate.month &&
           currentEthiopian.day === day;
  };

  const isSelected = (day) => {
    return selectedEthiopian &&
           selectedEthiopian.year === viewDate.year &&
           selectedEthiopian.month === viewDate.month &&
           selectedEthiopian.day === day;
  };

  return (
    <Paper sx={{ p: 2 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" sx={{ textAlign: 'center', width: '100%' }}>
          {monthNames[viewDate.month - 1]} {viewDate.year}
        </Typography>
      </Box>

      {/* Calendar Grid */}
      <Grid container spacing={1}>
        {Array.from({ length: daysInMonth }, (_, i) => i + 1).map(day => (
          <Grid item xs={12/7} key={day}>
            <Box
              onClick={() => handleDayClick(day)}
              sx={{
                p: 1,
                textAlign: 'center',
                cursor: 'pointer',
                borderRadius: 1,
                minHeight: 40,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                bgcolor: isSelected(day) ? 'primary.main' :
                        isToday(day) ? 'primary.light' : 'transparent',
                color: isSelected(day) ? 'primary.contrastText' :
                       isToday(day) ? 'primary.contrastText' : 'text.primary',
                '&:hover': {
                  bgcolor: isSelected(day) ? 'primary.dark' : 'action.hover'
                }
              }}
            >
              {day}
            </Box>
          </Grid>
        ))}
      </Grid>

      {/* Footer Info */}
      <Box sx={{ mt: 2, textAlign: 'center' }}>
        <Typography variant="caption" color="text.secondary">
          Ethiopian Calendar • {daysInMonth} days in {monthNames[viewDate.month - 1]}
        </Typography>
      </Box>
    </Paper>
  );
};

export default EthiopianCalendarPicker;
