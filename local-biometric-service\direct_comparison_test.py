#!/usr/bin/env python3
"""
DIRECT COMPARISON TEST
Run the EXACT debug code vs service code side by side
Find the subtle difference causing crashes
"""

import ctypes
from ctypes import cdll, c_int, c_uint, c_void_p, c_ubyte, POINTER, byref
import time
import os

def test_debug_exact_approach():
    """EXACT copy of the debug code that worked"""
    
    print("=== TEST 1: EXACT DEBUG APPROACH ===")
    print("This is a DIRECT copy of the debug code that worked")
    
    try:
        # EXACT same as successful debug
        print("Step 1: Loading SDK...")
        dll_path = os.path.abspath('./ftrScanAPI.dll')
        scan_api = cdll.LoadLibrary(dll_path)
        print("✅ SDK loaded successfully")
        
        print("Step 2: Configuring function signatures...")
        scan_api.ftrScanOpenDevice.restype = c_void_p
        scan_api.ftrScanOpenDevice.argtypes = []
        
        scan_api.ftrScanCloseDevice.restype = c_int
        scan_api.ftrScanCloseDevice.argtypes = [c_void_p]
        
        scan_api.ftrScanGetFrame.restype = c_int
        scan_api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(c_ubyte), POINTER(c_uint)]
        print("✅ Function signatures configured")
        
        print("Step 3: Initializing device...")
        device_handle = scan_api.ftrScanOpenDevice()
        if not device_handle:
            print("❌ ERROR: Failed to open device")
            return False
        print(f"✅ Device initialized - Handle: {device_handle}")
        
        print("Step 4: Testing ftrScanGetFrame with different approaches...")
        
        print("\n--- Approach 1: Minimal Buffer (1KB) ---")
        buffer_size = 1024
        image_buffer = (c_ubyte * buffer_size)()
        frame_size = c_uint(buffer_size)
        
        print(f"Buffer created: {buffer_size} bytes")
        print(f"Initial frame_size: {frame_size.value}")
        print("Calling ftrScanGetFrame...")
        
        # EXACT same call as debug
        result = scan_api.ftrScanGetFrame(device_handle, image_buffer, byref(frame_size))
        
        print(f"✅ SUCCESS: ftrScanGetFrame returned {result}")
        print(f"Frame size after call: {frame_size.value}")
        
        if frame_size.value > 0:
            print(f"✅ Got {frame_size.value} bytes of data!")
            first_bytes = [image_buffer[i] for i in range(min(10, frame_size.value))]
            print(f"First 10 bytes: {first_bytes}")
            
            # Close device
            scan_api.ftrScanCloseDevice(device_handle)
            print("✅ Device closed")
            return True
        else:
            print("⚠️ Frame size is 0")
            scan_api.ftrScanCloseDevice(device_handle)
            return False
            
    except Exception as e:
        print(f"❌ DEBUG APPROACH FAILED: {e}")
        return False

def test_service_approach():
    """Test the service approach that's been crashing"""
    
    print("\n=== TEST 2: SERVICE APPROACH ===")
    print("This is the approach used in services that crash")
    
    try:
        # Load SDK
        dll_path = os.path.abspath('./ftrScanAPI.dll')
        scan_api = cdll.LoadLibrary(dll_path)
        print("✅ SDK loaded")
        
        # Configure signatures
        scan_api.ftrScanOpenDevice.restype = c_void_p
        scan_api.ftrScanOpenDevice.argtypes = []
        scan_api.ftrScanCloseDevice.restype = c_int
        scan_api.ftrScanCloseDevice.argtypes = [c_void_p]
        scan_api.ftrScanGetFrame.restype = c_int
        scan_api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(c_ubyte), POINTER(c_uint)]
        print("✅ Signatures configured")
        
        # Initialize device
        device_handle = scan_api.ftrScanOpenDevice()
        if not device_handle:
            print("❌ Failed to open device")
            return False
        print(f"✅ Device initialized - Handle: {device_handle}")
        
        # Frame capture
        print("Starting frame capture...")
        buffer_size = 1024
        image_buffer = (c_ubyte * buffer_size)()
        frame_size = c_uint(buffer_size)
        
        print("Calling ftrScanGetFrame...")
        result = scan_api.ftrScanGetFrame(device_handle, image_buffer, byref(frame_size))
        
        print(f"✅ SUCCESS: Result {result}, Size {frame_size.value}")
        
        scan_api.ftrScanCloseDevice(device_handle)
        return True
        
    except Exception as e:
        print(f"❌ SERVICE APPROACH FAILED: {e}")
        return False

def test_minimal_differences():
    """Test with minimal code differences to isolate the issue"""
    
    print("\n=== TEST 3: MINIMAL DIFFERENCES ===")
    print("Testing tiny variations to find the exact issue")
    
    # Test 1: Different variable names
    print("\n--- Test 3a: Different variable names ---")
    try:
        dll_path = os.path.abspath('./ftrScanAPI.dll')
        api = cdll.LoadLibrary(dll_path)
        
        api.ftrScanOpenDevice.restype = c_void_p
        api.ftrScanOpenDevice.argtypes = []
        api.ftrScanGetFrame.restype = c_int
        api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(c_ubyte), POINTER(c_uint)]
        api.ftrScanCloseDevice.restype = c_int
        api.ftrScanCloseDevice.argtypes = [c_void_p]
        
        handle = api.ftrScanOpenDevice()
        if handle:
            print(f"Device opened: {handle}")
            
            buf_size = 1024
            buf = (c_ubyte * buf_size)()
            size = c_uint(buf_size)
            
            print("Calling ftrScanGetFrame with different variable names...")
            res = api.ftrScanGetFrame(handle, buf, byref(size))
            print(f"Result: {res}, Size: {size.value}")
            
            api.ftrScanCloseDevice(handle)
            print("✅ Test 3a: SUCCESS")
        else:
            print("❌ Test 3a: Device open failed")
            
    except Exception as e:
        print(f"❌ Test 3a: FAILED - {e}")
    
    # Test 2: Different buffer creation
    print("\n--- Test 3b: Different buffer creation ---")
    try:
        dll_path = os.path.abspath('./ftrScanAPI.dll')
        scan_api = cdll.LoadLibrary(dll_path)
        
        scan_api.ftrScanOpenDevice.restype = c_void_p
        scan_api.ftrScanOpenDevice.argtypes = []
        scan_api.ftrScanGetFrame.restype = c_int
        scan_api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(c_ubyte), POINTER(c_uint)]
        scan_api.ftrScanCloseDevice.restype = c_int
        scan_api.ftrScanCloseDevice.argtypes = [c_void_p]
        
        device_handle = scan_api.ftrScanOpenDevice()
        if device_handle:
            print(f"Device opened: {device_handle}")
            
            # Create buffer differently
            buffer_array = c_ubyte * 1024
            image_buffer = buffer_array()
            frame_size = c_uint(1024)
            
            print("Calling ftrScanGetFrame with different buffer creation...")
            result = scan_api.ftrScanGetFrame(device_handle, image_buffer, byref(frame_size))
            print(f"Result: {result}, Size: {frame_size.value}")
            
            scan_api.ftrScanCloseDevice(device_handle)
            print("✅ Test 3b: SUCCESS")
        else:
            print("❌ Test 3b: Device open failed")
            
    except Exception as e:
        print(f"❌ Test 3b: FAILED - {e}")
    
    # Test 3: Immediate call without prints
    print("\n--- Test 3c: Immediate call without prints ---")
    try:
        dll_path = os.path.abspath('./ftrScanAPI.dll')
        scan_api = cdll.LoadLibrary(dll_path)
        scan_api.ftrScanOpenDevice.restype = c_void_p
        scan_api.ftrScanOpenDevice.argtypes = []
        scan_api.ftrScanGetFrame.restype = c_int
        scan_api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(c_ubyte), POINTER(c_uint)]
        scan_api.ftrScanCloseDevice.restype = c_int
        scan_api.ftrScanCloseDevice.argtypes = [c_void_p]
        
        device_handle = scan_api.ftrScanOpenDevice()
        image_buffer = (c_ubyte * 1024)()
        frame_size = c_uint(1024)
        result = scan_api.ftrScanGetFrame(device_handle, image_buffer, byref(frame_size))
        
        print(f"✅ Test 3c: SUCCESS - Result: {result}, Size: {frame_size.value}")
        scan_api.ftrScanCloseDevice(device_handle)
        
    except Exception as e:
        print(f"❌ Test 3c: FAILED - {e}")

def test_timing_differences():
    """Test if timing makes a difference"""
    
    print("\n=== TEST 4: TIMING DIFFERENCES ===")
    
    # Test with delays
    print("\n--- Test 4a: With delays between calls ---")
    try:
        dll_path = os.path.abspath('./ftrScanAPI.dll')
        scan_api = cdll.LoadLibrary(dll_path)
        
        scan_api.ftrScanOpenDevice.restype = c_void_p
        scan_api.ftrScanOpenDevice.argtypes = []
        time.sleep(0.1)
        
        scan_api.ftrScanGetFrame.restype = c_int
        scan_api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(c_ubyte), POINTER(c_uint)]
        time.sleep(0.1)
        
        scan_api.ftrScanCloseDevice.restype = c_int
        scan_api.ftrScanCloseDevice.argtypes = [c_void_p]
        time.sleep(0.1)
        
        device_handle = scan_api.ftrScanOpenDevice()
        time.sleep(0.5)  # Wait after device open
        
        if device_handle:
            image_buffer = (c_ubyte * 1024)()
            frame_size = c_uint(1024)
            time.sleep(0.1)
            
            result = scan_api.ftrScanGetFrame(device_handle, image_buffer, byref(frame_size))
            print(f"✅ Test 4a: SUCCESS - Result: {result}, Size: {frame_size.value}")
            
            scan_api.ftrScanCloseDevice(device_handle)
        
    except Exception as e:
        print(f"❌ Test 4a: FAILED - {e}")

if __name__ == '__main__':
    print("=== DIRECT COMPARISON TEST ===")
    print("Finding the exact difference between working debug and crashing services")
    print()
    
    # Run all tests
    test1_result = test_debug_exact_approach()
    test2_result = test_service_approach()
    test_minimal_differences()
    test_timing_differences()
    
    print("\n=== COMPARISON RESULTS ===")
    print(f"Debug approach: {'✅ WORKED' if test1_result else '❌ FAILED'}")
    print(f"Service approach: {'✅ WORKED' if test2_result else '❌ FAILED'}")
    
    if test1_result and not test2_result:
        print("\n🔍 CONCLUSION: There IS a difference between debug and service approaches")
        print("The debug approach works, but service approach fails")
        print("Check the test results above to identify the exact difference")
    elif test1_result and test2_result:
        print("\n🔍 CONCLUSION: Both approaches work in this test")
        print("The issue may be with the service framework context")
    elif not test1_result and not test2_result:
        print("\n🔍 CONCLUSION: Both approaches fail")
        print("There may be a fundamental SDK issue")
    else:
        print("\n🔍 CONCLUSION: Unexpected result pattern")
    
    print("\nPress Enter to exit...")
    try:
        input()
    except:
        pass
