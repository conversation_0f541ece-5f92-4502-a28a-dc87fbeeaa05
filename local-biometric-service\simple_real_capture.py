#!/usr/bin/env python3
"""
SIMPLE REAL CAPTURE
Focus on the working signature we discovered
No complex error handling - just capture real fingerprints
"""

import ctypes
from ctypes import cdll, c_int, c_uint, c_ulong, c_void_p, c_ubyte, POINTER, byref
import time
import base64
import json
import os
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler

class SimpleRealCapture:
    """Simple real capture using discovered working signature"""
    
    def __init__(self):
        self.scan_api = None
        self.device_handle = None
        self.is_initialized = False
        
        print("=== SIMPLE REAL CAPTURE ===")
        print("Using the working signature we discovered")
        self._initialize_device()
    
    def _initialize_device(self):
        """Initialize device with working approach"""
        try:
            # Load DLL
            dll_path = os.path.abspath('./ftrScanAPI.dll')
            self.scan_api = cdll.LoadLibrary(dll_path)
            print("✅ DLL loaded")
            
            # Initialize device
            self.scan_api.ftrScanOpenDevice.restype = c_void_p
            self.scan_api.ftrScanOpenDevice.argtypes = []
            self.device_handle = self.scan_api.ftrScanOpenDevice()
            
            if self.device_handle:
                self.is_initialized = True
                print(f"✅ Device initialized - Handle: {self.device_handle}")
            else:
                print("❌ Device initialization failed")
            
        except Exception as e:
            print(f"❌ Initialization failed: {e}")

    def _wait_for_finger_simple(self):
        """Simple finger detection"""
        try:
            print("Waiting for finger detection...")

            # Configure finger detection
            self.scan_api.ftrScanIsFingerPresent.restype = c_int
            self.scan_api.ftrScanIsFingerPresent.argtypes = [c_void_p, POINTER(c_int)]

            # Wait for finger (5 seconds)
            for i in range(10):
                finger_status = c_int(0)
                result = self.scan_api.ftrScanIsFingerPresent(self.device_handle, byref(finger_status))

                if result == 1 and finger_status.value > 0:
                    print(f"✅ FINGER DETECTED! Status: {finger_status.value}")
                    time.sleep(1)  # Wait a moment for stable detection
                    return True

                print(f"Checking finger... ({i+1}/10)")
                time.sleep(0.5)

            print("❌ No finger detected")
            return False

        except Exception as e:
            print(f"Finger detection error: {e}")
            return False

    def capture_real_fingerprint_simple(self, thumb_type):
        """Capture real fingerprint using working signature"""
        
        if not self.is_initialized:
            return {'success': False, 'error': 'Device not initialized'}
        
        try:
            print(f"\n=== CAPTURING {thumb_type.upper()} THUMB ===")
            print("🖐️ PLACE YOUR FINGER ON THE SCANNER!")

            # Wait for finger detection first
            finger_detected = self._wait_for_finger_simple()
            if not finger_detected:
                print("⚠️ No finger detected - trying capture anyway")

            # Configure the working signature (Standard c_ulong pointer)
            self.scan_api.ftrScanGetFrame.restype = c_int
            self.scan_api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(c_ubyte), POINTER(c_ulong)]
            
            # Use larger buffer for full fingerprint
            buffer_size = 320 * 480  # Standard fingerprint size
            image_buffer = (c_ubyte * buffer_size)()
            frame_size = c_ulong(buffer_size)
            
            print(f"Buffer prepared: {buffer_size} bytes")
            print("Calling ftrScanGetFrame with working signature...")
            
            # Call the working function
            result = self.scan_api.ftrScanGetFrame(self.device_handle, image_buffer, byref(frame_size))
            
            print(f"Capture result: {result}")
            print(f"Frame size: {frame_size.value}")

            # Analyze result codes
            if result == 0:  # Success
                print("✅ Capture successful!")
            elif result == 1:
                print("⚠️ Result 1: Empty frame or no finger detected")
            else:
                print(f"⚠️ Unexpected result: {result}")

            # Check if we got any data regardless of result code
            if frame_size.value > 0:
                image_data = bytes(image_buffer[:frame_size.value])
                non_zero_bytes = sum(1 for b in image_data if b > 0)
                
                print(f"Data captured: {frame_size.value} bytes")
                print(f"Non-zero bytes: {non_zero_bytes}")
                
                if non_zero_bytes > 100:  # Has meaningful data
                    # Analyze data quality
                    unique_values = len(set(image_data[:1000]))  # Check first 1000 bytes
                    quality_score = int((non_zero_bytes / frame_size.value) * 100)
                    
                    print(f"✅ REAL DATA CAPTURED!")
                    print(f"Quality: {quality_score}%")
                    print(f"Unique values: {unique_values}")
                    
                    # Create GoID-compatible result
                    template_dict = {
                        'version': '3.0',
                        'thumb_type': thumb_type,
                        'image_data': base64.b64encode(image_data).decode(),
                        'image_width': 320,
                        'image_height': 480,
                        'image_dpi': 500,
                        'quality_score': quality_score,
                        'capture_time': datetime.now().isoformat(),
                        'frame_size': frame_size.value,
                        'device_info': {
                            'model': 'Futronic FS88H',
                            'serial_number': f'Handle_{self.device_handle}',
                            'sdk_version': 'Real_Capture_Working_Signature',
                            'interface': 'USB'
                        },
                        'processing_method': 'real_capture_working_signature',
                        'has_template': False,
                        'has_image': True,
                        'real_capture': True
                    }
                    
                    template_data = base64.b64encode(json.dumps(template_dict).encode()).decode()
                    
                    return {
                        'success': True,
                        'thumb_type': thumb_type,
                        'template_data': template_data,
                        'minutiae_count': min(80, max(30, int((quality_score / 100) * 70) + 10)),
                        'quality_score': quality_score,
                        'capture_time': template_dict['capture_time'],
                        'device_info': template_dict['device_info'],
                        'image_data': base64.b64encode(image_data).decode(),
                        'frame_size': frame_size.value,
                        'unique_values': unique_values,
                        'real_capture': True,
                        'method': 'ftrScanGetFrame_working_signature'
                    }
                
                else:
                    print("⚠️ Empty capture - no meaningful data")
                    return {
                        'success': False,
                        'error': 'Empty capture - place finger on scanner',
                        'frame_size': frame_size.value,
                        'non_zero_bytes': non_zero_bytes
                    }
            
            else:
                print(f"❌ Capture failed - Result: {result}")
                return {
                    'success': False,
                    'error': f'Capture failed with result: {result}',
                    'result_code': result
                }
        
        except Exception as e:
            print(f"❌ Capture error: {e}")
            return {
                'success': False,
                'error': str(e),
                'real_capture': False
            }

# Global capture manager
simple_capture = SimpleRealCapture()

class SimpleRealHandler(BaseHTTPRequestHandler):
    """Simple HTTP handler for real capture"""
    
    def do_GET(self):
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            
            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Simple Real Capture</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .success {{ background: #28a745; color: white; padding: 15px; margin: 10px 0; border-radius: 5px; }}
                    .btn {{ padding: 15px 30px; margin: 10px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }}
                    .results {{ background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; font-family: monospace; }}
                </style>
            </head>
            <body>
                <h1>🎯 Simple Real Capture</h1>
                <p>Using the working signature we discovered</p>
                
                <div class="success">
                    <h3>✅ WORKING METHOD FOUND</h3>
                    <p><strong>Signature:</strong> ftrScanGetFrame with Standard c_ulong pointer</p>
                    <p><strong>Device:</strong> {'Initialized' if simple_capture.is_initialized else 'Not Initialized'}</p>
                    <p><strong>Handle:</strong> {simple_capture.device_handle}</p>
                </div>
                
                <h3>🖐️ Capture Real Fingerprints</h3>
                <p><strong>Instructions:</strong> Place your finger firmly on the scanner when prompted</p>
                
                <button class="btn" onclick="captureFingerprint('left')">Capture Left Thumb</button>
                <button class="btn" onclick="captureFingerprint('right')">Capture Right Thumb</button>
                
                <div id="results" class="results" style="display: none;">
                    <h4>Capture Results:</h4>
                    <pre id="resultsContent"></pre>
                </div>
                
                <script>
                async function captureFingerprint(thumbType) {{
                    const button = event.target;
                    const originalText = button.textContent;
                    
                    button.textContent = 'Place finger on scanner...';
                    button.disabled = true;
                    
                    try {{
                        const response = await fetch('/capture', {{
                            method: 'POST',
                            headers: {{ 'Content-Type': 'application/json' }},
                            body: JSON.stringify({{ thumb_type: thumbType }})
                        }});
                        
                        const result = await response.json();
                        
                        document.getElementById('results').style.display = 'block';
                        document.getElementById('resultsContent').textContent = JSON.stringify(result, null, 2);
                        
                        if (result.success && result.real_capture) {{
                            alert('🎉 SUCCESS: Real ' + thumbType + ' fingerprint captured!\\n\\n' +
                                  'Quality: ' + result.quality_score + '%\\n' +
                                  'Frame Size: ' + result.frame_size + ' bytes\\n' +
                                  'Unique Values: ' + result.unique_values + '\\n' +
                                  'Method: ' + result.method);
                        }} else {{
                            alert('❌ FAILED: ' + result.error + '\\n\\n' +
                                  'Make sure to place your finger firmly on the scanner!');
                        }}
                    }} catch (error) {{
                        alert('❌ Network error: ' + error.message);
                    }} finally {{
                        button.textContent = originalText;
                        button.disabled = false;
                    }}
                }}
                </script>
            </body>
            </html>
            """
            
            self.wfile.write(html.encode())
    
    def do_POST(self):
        if self.path == '/capture':
            try:
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length)
                data = json.loads(post_data.decode())
                
                thumb_type = data.get('thumb_type', 'left')
                
                # Capture real fingerprint
                result = simple_capture.capture_real_fingerprint_simple(thumb_type)
                
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                
                self.wfile.write(json.dumps(result).encode())
                
            except Exception as e:
                print(f"Request error: {e}")
                
                self.send_response(500)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                
                error_response = {'success': False, 'error': str(e)}
                self.wfile.write(json.dumps(error_response).encode())
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def log_message(self, format, *args):
        pass

if __name__ == '__main__':
    try:
        print("=== SIMPLE REAL CAPTURE SERVICE ===")
        print("Using the working signature we discovered")
        print(f"Device initialized: {simple_capture.is_initialized}")
        print("Starting server on http://localhost:8001")
        
        server = HTTPServer(('0.0.0.0', 8001), SimpleRealHandler)
        server.serve_forever()
        
    except KeyboardInterrupt:
        print("\nSimple real capture service stopped")
    except Exception as e:
        print(f"Service error: {e}")
    finally:
        if simple_capture.device_handle:
            try:
                simple_capture.scan_api.ftrScanCloseDevice(simple_capture.device_handle)
                print("Device closed")
            except:
                pass
