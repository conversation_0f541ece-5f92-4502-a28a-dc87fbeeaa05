from rest_framework import serializers
from .models import Role, RoleType, UserR<PERSON>, User, Permission, RolePermission


class RoleSerializer(serializers.ModelSerializer):
    created_by_email = serializers.CharField(source='created_by.email', read_only=True)
    permissions_count = serializers.SerializerMethodField()
    users_count = serializers.SerializerMethodField()
    can_manage_roles_names = serializers.SerializerMethodField()

    class Meta:
        model = Role
        fields = ['id', 'codename', 'name', 'description', 'role_type', 'level',
                  'allowed_tenant_types', 'is_active', 'is_built_in', 'created_by',
                  'created_by_email', 'created_at', 'updated_at', 'permissions_count',
                  'users_count', 'can_manage_roles_names']
        read_only_fields = ['created_at', 'updated_at', 'is_built_in', 'created_by']

    def get_permissions_count(self, obj):
        return obj.role_permissions.filter(is_active=True).count()

    def get_users_count(self, obj):
        return obj.users.filter(is_active=True).count()

    def get_can_manage_roles_names(self, obj):
        return [role.name for role in obj.can_manage_roles.all()]


class RoleCreateSerializer(serializers.ModelSerializer):
    can_manage_role_codenames = serializers.ListField(
        child=serializers.CharField(),
        write_only=True,
        required=False,
        help_text="List of role codenames this role can manage"
    )
    permission_codenames = serializers.ListField(
        child=serializers.CharField(),
        write_only=True,
        required=False,
        help_text="List of permission codenames to assign to this role"
    )
    scope_tenant_id = serializers.IntegerField(
        write_only=True,
        required=False,
        help_text="ID of tenant that owns this role (for tenant-specific roles)"
    )

    class Meta:
        model = Role
        fields = ['codename', 'name', 'description', 'role_type', 'level',
                  'allowed_tenant_types', 'is_global', 'scope_tenant_id',
                  'can_manage_role_codenames', 'permission_codenames']

    def validate_codename(self, value):
        # Check if codename conflicts with legacy roles
        legacy_roles = [choice[0] for choice in UserRole.choices]
        if value in legacy_roles:
            raise serializers.ValidationError(
                f"Codename '{value}' conflicts with built-in role. Choose a different codename."
            )
        return value

    def validate_can_manage_role_codenames(self, value):
        # Validate that all role codenames exist
        existing_roles = set(Role.objects.filter(codename__in=value, is_active=True).values_list('codename', flat=True))
        legacy_roles = set([choice[0] for choice in UserRole.choices])
        all_valid_roles = existing_roles | legacy_roles
        
        invalid_roles = set(value) - all_valid_roles
        if invalid_roles:
            raise serializers.ValidationError(
                f"The following roles do not exist: {', '.join(invalid_roles)}"
            )
        return value

    def validate_permission_codenames(self, value):
        # Validate that all permission codenames exist
        existing_permissions = set(
            Permission.objects.filter(codename__in=value, is_active=True).values_list('codename', flat=True)
        )
        invalid_permissions = set(value) - existing_permissions
        if invalid_permissions:
            raise serializers.ValidationError(
                f"The following permissions do not exist: {', '.join(invalid_permissions)}"
            )
        return value

    def validate_scope_tenant_id(self, value):
        if value:
            from tenants.models import Tenant
            try:
                tenant = Tenant.objects.get(id=value)
                # Check if current user can create roles for this tenant
                if 'request' in self.context:
                    user = self.context['request'].user
                    from users.permissions_tenant_roles import get_manageable_tenants_for_user
                    manageable_tenants = get_manageable_tenants_for_user(user)
                    if tenant not in manageable_tenants:
                        raise serializers.ValidationError(
                            "You don't have permission to create roles for this tenant"
                        )
                return value
            except Tenant.DoesNotExist:
                raise serializers.ValidationError("Tenant does not exist")
        return value

    def create(self, validated_data):
        can_manage_role_codenames = validated_data.pop('can_manage_role_codenames', [])
        permission_codenames = validated_data.pop('permission_codenames', [])
        scope_tenant_id = validated_data.pop('scope_tenant_id', None)

        # Set created_by from request context
        if 'request' in self.context:
            validated_data['created_by'] = self.context['request'].user

        # Set scope_tenant if provided
        if scope_tenant_id:
            from tenants.models import Tenant
            validated_data['scope_tenant'] = Tenant.objects.get(id=scope_tenant_id)
            validated_data['is_global'] = False

        role = Role.objects.create(**validated_data)

        # Set manageable roles
        if can_manage_role_codenames:
            manageable_roles = Role.objects.filter(codename__in=can_manage_role_codenames, is_active=True)
            role.can_manage_roles.set(manageable_roles)

        # Assign permissions
        if permission_codenames:
            permissions = Permission.objects.filter(codename__in=permission_codenames, is_active=True)
            role_permissions = [
                RolePermission(role_obj=role, permission=permission)
                for permission in permissions
            ]
            RolePermission.objects.bulk_create(role_permissions)

        return role


class RoleUpdateSerializer(serializers.ModelSerializer):
    can_manage_role_codenames = serializers.ListField(
        child=serializers.CharField(),
        write_only=True,
        required=False
    )

    class Meta:
        model = Role
        fields = ['name', 'description', 'level', 'allowed_tenant_types', 
                  'is_active', 'can_manage_role_codenames']

    def validate_can_manage_role_codenames(self, value):
        # Same validation as create
        existing_roles = set(Role.objects.filter(codename__in=value, is_active=True).values_list('codename', flat=True))
        legacy_roles = set([choice[0] for choice in UserRole.choices])
        all_valid_roles = existing_roles | legacy_roles
        
        invalid_roles = set(value) - all_valid_roles
        if invalid_roles:
            raise serializers.ValidationError(
                f"The following roles do not exist: {', '.join(invalid_roles)}"
            )
        return value

    def update(self, instance, validated_data):
        can_manage_role_codenames = validated_data.pop('can_manage_role_codenames', None)
        
        # Update basic fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # Update manageable roles if provided
        if can_manage_role_codenames is not None:
            manageable_roles = Role.objects.filter(codename__in=can_manage_role_codenames, is_active=True)
            instance.can_manage_roles.set(manageable_roles)
        
        return instance


class UserRoleAssignmentSerializer(serializers.Serializer):
    user_email = serializers.EmailField()
    role_codename = serializers.CharField()
    reason = serializers.CharField(required=False, allow_blank=True)

    def validate_user_email(self, value):
        try:
            User.objects.get(email=value)
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError(f"User with email '{value}' does not exist.")

    def validate_role_codename(self, value):
        # Check if it's a legacy role or dynamic role
        legacy_roles = [choice[0] for choice in UserRole.choices]
        if value in legacy_roles:
            return value
        
        try:
            Role.objects.get(codename=value, is_active=True)
            return value
        except Role.DoesNotExist:
            raise serializers.ValidationError(f"Role '{value}' does not exist or is not active.")

    def save(self):
        user_email = self.validated_data['user_email']
        role_codename = self.validated_data['role_codename']
        reason = self.validated_data.get('reason', '')
        
        user = User.objects.get(email=user_email)
        assigned_by = self.context.get('request').user if 'request' in self.context else None
        
        # Assign the role
        user.assign_role(role_codename, assigned_by=assigned_by)
        
        return {
            'user_email': user_email,
            'role_codename': role_codename,
            'role_name': user.effective_role_name,
            'assigned_by': assigned_by.email if assigned_by else None,
            'reason': reason
        }


class RoleHierarchySerializer(serializers.Serializer):
    """Serializer for displaying role hierarchy"""
    
    def to_representation(self, instance):
        # Get all roles (both legacy and dynamic)
        legacy_roles = []
        for role_choice in UserRole.choices:
            legacy_roles.append({
                'codename': role_choice[0],
                'name': role_choice[1],
                'type': 'legacy',
                'level': self._get_legacy_role_level(role_choice[0]),
                'is_built_in': True,
                'users_count': User.objects.filter(role=role_choice[0], is_active=True).count()
            })
        
        dynamic_roles = []
        for role in Role.objects.filter(is_active=True):
            dynamic_roles.append({
                'codename': role.codename,
                'name': role.name,
                'type': 'dynamic',
                'level': role.level,
                'is_built_in': role.is_built_in,
                'users_count': role.users.filter(is_active=True).count(),
                'can_manage': [r.codename for r in role.can_manage_roles.all()]
            })
        
        # Sort by level (highest first)
        all_roles = legacy_roles + dynamic_roles
        all_roles.sort(key=lambda x: x['level'], reverse=True)
        
        return {
            'roles': all_roles,
            'total_legacy_roles': len(legacy_roles),
            'total_dynamic_roles': len(dynamic_roles)
        }
    
    def _get_legacy_role_level(self, role_codename):
        """Get default level for legacy roles"""
        levels = {
            'superadmin': 100,
            'city_admin': 80,
            'subcity_admin': 60,
            'kebele_admin': 40,
            'kebele_leader': 30,
            'clerk': 10
        }
        return levels.get(role_codename, 0)


class RolePermissionAssignmentSerializer(serializers.Serializer):
    role_codename = serializers.CharField()
    permission_codenames = serializers.ListField(child=serializers.CharField())
    replace_existing = serializers.BooleanField(default=False, 
                                               help_text="If True, replace all existing permissions. If False, add to existing.")

    def validate_role_codename(self, value):
        # Check if it's a dynamic role (legacy roles use different endpoint)
        try:
            Role.objects.get(codename=value, is_active=True)
            return value
        except Role.DoesNotExist:
            raise serializers.ValidationError(f"Dynamic role '{value}' does not exist or is not active.")

    def validate_permission_codenames(self, value):
        existing_permissions = set(
            Permission.objects.filter(codename__in=value, is_active=True).values_list('codename', flat=True)
        )
        invalid_permissions = set(value) - existing_permissions
        if invalid_permissions:
            raise serializers.ValidationError(
                f"The following permissions do not exist: {', '.join(invalid_permissions)}"
            )
        return value

    def save(self):
        role_codename = self.validated_data['role_codename']
        permission_codenames = self.validated_data['permission_codenames']
        replace_existing = self.validated_data['replace_existing']
        
        role = Role.objects.get(codename=role_codename, is_active=True)
        permissions = Permission.objects.filter(codename__in=permission_codenames, is_active=True)
        
        if replace_existing:
            # Remove existing permissions
            RolePermission.objects.filter(role_obj=role).delete()
        
        # Add new permissions
        existing_permission_ids = set(
            RolePermission.objects.filter(role_obj=role).values_list('permission_id', flat=True)
        )
        
        new_role_permissions = []
        for permission in permissions:
            if permission.id not in existing_permission_ids:
                new_role_permissions.append(RolePermission(role_obj=role, permission=permission))
        
        RolePermission.objects.bulk_create(new_role_permissions)
        
        return {
            'role_codename': role_codename,
            'permissions_assigned': len(new_role_permissions),
            'total_permissions': RolePermission.objects.filter(role_obj=role, is_active=True).count()
        }


class TenantRoleManagementSerializer(serializers.Serializer):
    """Serializer for managing roles within a tenant context"""
    tenant_id = serializers.IntegerField()
    action = serializers.ChoiceField(choices=['create_role', 'assign_role', 'grant_permission'])

    # For create_role action
    role_data = serializers.DictField(required=False)

    # For assign_role action
    user_email = serializers.EmailField(required=False)
    role_codename = serializers.CharField(required=False)

    # For grant_permission action
    permission_codename = serializers.CharField(required=False)
    reason = serializers.CharField(required=False, allow_blank=True)

    def validate_tenant_id(self, value):
        from tenants.models import Tenant
        try:
            tenant = Tenant.objects.get(id=value)
            # Check if current user can manage this tenant
            if 'request' in self.context:
                user = self.context['request'].user
                from users.permissions_tenant_roles import get_manageable_tenants_for_user
                manageable_tenants = get_manageable_tenants_for_user(user)
                if tenant not in manageable_tenants:
                    raise serializers.ValidationError(
                        "You don't have permission to manage roles in this tenant"
                    )
            return value
        except Tenant.DoesNotExist:
            raise serializers.ValidationError("Tenant does not exist")

    def validate(self, data):
        action = data.get('action')

        if action == 'create_role':
            if not data.get('role_data'):
                raise serializers.ValidationError("role_data is required for create_role action")

            # Validate role_data structure
            role_data = data.get('role_data', {})
            required_fields = ['codename', 'name']
            for field in required_fields:
                if not role_data.get(field):
                    raise serializers.ValidationError(f"role_data.{field} is required")

            # Validate permission_codenames if provided
            permission_codenames = role_data.get('permission_codenames', [])
            if permission_codenames:
                existing_permissions = set(
                    Permission.objects.filter(
                        codename__in=permission_codenames,
                        is_active=True
                    ).values_list('codename', flat=True)
                )
                invalid_permissions = set(permission_codenames) - existing_permissions
                if invalid_permissions:
                    raise serializers.ValidationError(
                        f"The following permissions do not exist: {', '.join(invalid_permissions)}"
                    )

        if action == 'assign_role':
            if not data.get('user_email') or not data.get('role_codename'):
                raise serializers.ValidationError("user_email and role_codename are required for assign_role action")

        if action == 'grant_permission':
            if not data.get('user_email') or not data.get('permission_codename'):
                raise serializers.ValidationError("user_email and permission_codename are required for grant_permission action")

        return data

    def save(self):
        from tenants.models import Tenant
        from users.models import User, Role

        tenant = Tenant.objects.get(id=self.validated_data['tenant_id'])
        action = self.validated_data['action']
        user = self.context['request'].user if 'request' in self.context else None

        if action == 'create_role':
            return self._create_tenant_role(tenant, user)
        elif action == 'assign_role':
            return self._assign_role_to_user(tenant, user)
        elif action == 'grant_permission':
            return self._grant_permission_to_user(tenant, user)

    def _create_tenant_role(self, tenant, creator):
        try:
            role_data = self.validated_data['role_data'].copy()  # Make a copy to avoid modifying original

            # Extract permission_codenames before creating role
            permission_codenames = role_data.pop('permission_codenames', [])

            # Create role with tenant scope
            role_data['scope_tenant'] = tenant
            role_data['is_global'] = False
            role_data['created_by'] = creator

            # Generate unique codename for tenant-specific role
            base_codename = role_data.get('codename', 'custom_role')
            unique_codename = f"{base_codename}_{tenant.type}_{tenant.id}"

            # Check if role with this codename already exists
            if Role.objects.filter(codename=unique_codename).exists():
                raise serializers.ValidationError(
                    f"Role with codename '{unique_codename}' already exists for this tenant"
                )

            role_data['codename'] = unique_codename

            # Create the role
            role = Role.objects.create(**role_data)

            # Assign permissions if provided
            permissions_assigned = 0
            if permission_codenames:
                permissions = Permission.objects.filter(
                    codename__in=permission_codenames,
                    is_active=True
                )

                # Create RolePermission objects
                role_permissions = [
                    RolePermission(role_obj=role, permission=permission)
                    for permission in permissions
                ]
                RolePermission.objects.bulk_create(role_permissions)
                permissions_assigned = len(role_permissions)

            return {
                'action': 'create_role',
                'tenant': tenant.name,
                'role': {
                    'id': role.id,
                    'codename': role.codename,
                    'name': role.name,
                    'scope_tenant': tenant.name,
                    'permissions_assigned': permissions_assigned
                },
                'message': f"Role '{role.name}' created successfully with {permissions_assigned} permissions"
            }

        except Exception as e:
            # Log the error for debugging
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error creating tenant role: {e}", exc_info=True)

            # Re-raise as ValidationError for proper API response
            if isinstance(e, serializers.ValidationError):
                raise e
            else:
                raise serializers.ValidationError(f"Failed to create role: {str(e)}")

    def _assign_role_to_user(self, tenant, assigner):
        user_email = self.validated_data['user_email']
        role_codename = self.validated_data['role_codename']
        reason = self.validated_data.get('reason', '')

        try:
            target_user = User.objects.get(email=user_email)
        except User.DoesNotExist:
            raise serializers.ValidationError(f"User {user_email} not found")

        # Check if target user is in manageable tenant
        from users.permissions_tenant_roles import get_users_in_manageable_tenants
        manageable_users = get_users_in_manageable_tenants(assigner)
        if target_user not in manageable_users:
            raise serializers.ValidationError("You don't have permission to manage this user")

        # Assign role
        target_user.assign_role(role_codename, assigned_by=assigner)

        return {
            'action': 'assign_role',
            'tenant': tenant.name,
            'user_email': user_email,
            'role_codename': role_codename,
            'assigned_by': assigner.email if assigner else None,
            'reason': reason
        }

    def _grant_permission_to_user(self, tenant, granter):
        user_email = self.validated_data['user_email']
        permission_codename = self.validated_data['permission_codename']
        reason = self.validated_data.get('reason', '')

        try:
            target_user = User.objects.get(email=user_email)
        except User.DoesNotExist:
            raise serializers.ValidationError(f"User {user_email} not found")

        # Check if target user is in manageable tenant
        from users.permissions_tenant_roles import get_users_in_manageable_tenants
        manageable_users = get_users_in_manageable_tenants(granter)
        if target_user not in manageable_users:
            raise serializers.ValidationError("You don't have permission to manage this user")

        # Grant permission
        user_permission = target_user.grant_permission(
            permission_codename,
            granted_by=granter,
            reason=reason
        )

        return {
            'action': 'grant_permission',
            'tenant': tenant.name,
            'user_email': user_email,
            'permission_codename': permission_codename,
            'granted_by': granter.email if granter else None,
            'reason': reason
        }


class TenantUserRoleSerializer(serializers.ModelSerializer):
    """Serializer for users within a tenant context with role information"""
    effective_role = serializers.CharField(read_only=True)
    effective_role_name = serializers.CharField(read_only=True)
    tenant_name = serializers.CharField(source='tenant.name', read_only=True)
    custom_permissions_count = serializers.SerializerMethodField()
    can_be_managed = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['id', 'email', 'username', 'first_name', 'last_name',
                  'effective_role', 'effective_role_name', 'tenant', 'tenant_name',
                  'custom_permissions_count', 'can_be_managed', 'is_active']

    def get_custom_permissions_count(self, obj):
        return obj.custom_permissions.filter(granted=True, permission__is_active=True).count()

    def get_can_be_managed(self, obj):
        request = self.context.get('request')
        if request and request.user:
            from users.permissions_tenant_roles import get_users_in_manageable_tenants
            manageable_users = get_users_in_manageable_tenants(request.user)
            return obj in manageable_users
        return False
