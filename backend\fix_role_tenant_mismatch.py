#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix role-tenant mismatches in the GoID system.
This script identifies and corrects users who have roles that don't match their tenant types.
"""

import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from tenants.models import Tenant
from users.models import UserRole

User = get_user_model()

def analyze_role_tenant_mismatches():
    """Analyze and report role-tenant mismatches."""
    print("=== ANALYZING ROLE-TENANT MISMATCHES ===\n")

    # Define valid role-tenant combinations
    valid_combinations = {
        'city': ['city_admin'],
        'subcity': ['subcity_admin'],
        'kebele': ['kebele_leader', 'clerk', 'kebele_admin']
    }

    mismatches = []

    # Check all users
    users = User.objects.all()
    for user in users:
        if user.tenant:  # Skip users without tenants (like superadmin)
            tenant_type = user.tenant.type
            user_role = user.role

            if tenant_type in valid_combinations:
                if user_role not in valid_combinations[tenant_type]:
                    mismatches.append({
                        'user': user,
                        'email': user.email,
                        'username': user.username,
                        'current_role': user_role,
                        'tenant_name': user.tenant.name,
                        'tenant_type': tenant_type,
                        'valid_roles': valid_combinations[tenant_type]
                    })

    if mismatches:
        print(f"❌ Found {len(mismatches)} role-tenant mismatches:\n")
        for i, mismatch in enumerate(mismatches, 1):
            print(f"{i}. User: {mismatch['email']}")
            print(f"   Current Role: {mismatch['current_role']}")
            print(f"   Tenant: {mismatch['tenant_name']} (type: {mismatch['tenant_type']})")
            print(f"   Valid Roles: {', '.join(mismatch['valid_roles'])}")
            print()
    else:
        print("✅ No role-tenant mismatches found!")

    return mismatches

def fix_specific_user(email, new_role=None, new_tenant_id=None):
    """Fix a specific user's role or tenant assignment."""
    try:
        user = User.objects.get(email=email)
        print(f"\n=== FIXING USER: {email} ===")
        print(f"Current Role: {user.role}")
        print(f"Current Tenant: {user.tenant.name if user.tenant else 'None'} (type: {user.tenant.type if user.tenant else 'None'})")

        if new_role:
            old_role = user.role
            user.role = new_role
            user.save()
            print(f"✅ Updated role: {old_role} → {new_role}")

        if new_tenant_id:
            try:
                new_tenant = Tenant.objects.get(id=new_tenant_id)
                old_tenant = user.tenant
                user.tenant = new_tenant
                user.save()
                print(f"✅ Updated tenant: {old_tenant.name if old_tenant else 'None'} → {new_tenant.name}")
            except Tenant.DoesNotExist:
                print(f"❌ Tenant with ID {new_tenant_id} not found!")
                return False

        # Verify the fix
        user.refresh_from_db()
        print(f"\n✅ Final state:")
        print(f"   Role: {user.role}")
        print(f"   Tenant: {user.tenant.name} (type: {user.tenant.type})")

        return True

    except User.DoesNotExist:
        print(f"❌ User {email} not found!")
        return False

def suggest_fixes(mismatches):
    """Suggest fixes for role-tenant mismatches."""
    print("\n=== SUGGESTED FIXES ===\n")

    for i, mismatch in enumerate(mismatches, 1):
        print(f"{i}. User: {mismatch['email']}")
        print(f"   Problem: {mismatch['current_role']} role assigned to {mismatch['tenant_type']} tenant")

        # Suggest role change (most common fix)
        if mismatch['tenant_type'] == 'kebele':
            if 'admin' in mismatch['current_role'].lower():
                suggested_role = 'kebele_leader'
            else:
                suggested_role = 'clerk'
            print(f"   Suggestion 1: Change role to '{suggested_role}'")

        # Suggest tenant change (if user should be admin of parent tenant)
        if mismatch['current_role'] == 'subcity_admin' and mismatch['tenant_type'] == 'kebele':
            # Find parent subcity
            kebele_tenant = mismatch['user'].tenant
            if kebele_tenant.parent:
                print(f"   Suggestion 2: Move to parent subcity tenant '{kebele_tenant.parent.name}' (ID: {kebele_tenant.parent.id})")

        print()

def main():
    """Main function to analyze and optionally fix role-tenant mismatches."""
    print("GoID Role-Tenant Mismatch Fixer")
    print("=" * 40)

    # Analyze current state
    mismatches = analyze_role_tenant_mismatches()

    if not mismatches:
        return

    # Show suggestions
    suggest_fixes(mismatches)

    # Interactive fixing
    print("=== INTERACTIVE FIXING ===")
    print("You can now fix specific users. Examples:")
    print("1. Change role: fix_specific_user('<EMAIL>', new_role='clerk')")
    print("2. Change tenant: fix_specific_user('<EMAIL>', new_tenant_id=42)")
    print("3. Change both: fix_specific_user('<EMAIL>', new_role='subcity_admin', new_tenant_id=38)")
    print()

    # Fix the specific user mentioned in the issue
    # <NAME_EMAIL> was created as a clerk but role got escalated to subcity_admin
    veronica_username = '<EMAIL>'
    try:
        veronica_user = User.objects.get(username=veronica_username)
        if veronica_user.role == 'subcity_admin' and veronica_user.tenant and veronica_user.tenant.type == 'kebele':
            print(f"🔧 Fixing {veronica_username} - role escalation detected!")
            print(f"   Current: {veronica_user.role} in {veronica_user.tenant.name} ({veronica_user.tenant.type})")
            print(f"   This user should be a clerk in kebele tenant")

            # Fix: Change role back to clerk
            fix_specific_user(veronica_user.email, new_role='clerk')
        else:
            print(f"✅ {veronica_username} role is correct: {veronica_user.role}")
    except User.DoesNotExist:
        print(f"ℹ️  User {veronica_username} not found")

if __name__ == '__main__':
    main()
