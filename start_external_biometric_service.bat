@echo off
echo ========================================
echo   GoID External Biometric Service
echo ========================================
echo.
echo This service provides fingerprint capture
echo capabilities for the GoID system running
echo in Docker containers.
echo.
echo 🔍 Pre-flight checks...
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)
echo ✅ Python found

REM Check if we're in the right directory
if not exist "local-biometric-service" (
    echo ❌ local-biometric-service directory not found
    echo Please run this script from the GoID root directory
    pause
    exit /b 1
)
echo ✅ Service directory found

REM Check for DLL files
cd local-biometric-service
if exist "ftrScanAPI.dll" (
    echo ✅ ftrScanAPI.dll found
) else (
    echo ⚠️ ftrScanAPI.dll not found in local-biometric-service directory
    echo Please copy Futronic SDK DLL files to this directory
)

echo.
echo 🚀 Starting service on port 8001...
echo 📱 Service URL: http://localhost:8001
echo 🌐 Health Check: http://localhost:8001/api/health
echo 🔄 Reconnect: http://localhost:8001/api/device/reconnect
echo.
echo ⚠️  IMPORTANT:
echo    - Keep this window open while using GoID
echo    - Make sure your Futronic FS88H device is connected
echo    - The service will be accessible from Docker containers
echo    - If capture fails, try the reconnect endpoint
echo.
echo ❌ Close this window to stop the service
echo ⌨️  Press Ctrl+C to stop the service gracefully
echo.

python working_fingerprint_service.py

echo.
echo Service stopped.
echo.
echo If the service crashed unexpectedly:
echo 1. Check that the Futronic device is connected
echo 2. Verify DLL files are in the correct location
echo 3. Ensure no other software is using the device
echo 4. Try restarting the service
echo.
pause
