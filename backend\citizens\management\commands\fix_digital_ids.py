from django.core.management.base import BaseCommand
from django.db import connection
import random
import string
import re
import uuid


class Command(BaseCommand):
    help = 'Fix digital_id values that are in UUID format instead of custom format'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be changed without making changes',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']

        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))

        # Get all tenant schemas
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT schema_name
                FROM information_schema.schemata
                WHERE schema_name LIKE 'kebele_%'
            """)
            schemas = [row[0] for row in cursor.fetchall()]

        self.stdout.write(f'Found {len(schemas)} tenant schemas')

        total_fixed_digital_id = 0
        total_fixed_uuid = 0

        for schema in schemas:
            fixed_digital_id, fixed_uuid = self.fix_schema_citizens(schema, dry_run)
            total_fixed_digital_id += fixed_digital_id
            total_fixed_uuid += fixed_uuid

        self.stdout.write(
            self.style.SUCCESS(f'Total digital_ids fixed: {total_fixed_digital_id}')
        )
        self.stdout.write(
            self.style.SUCCESS(f'Total UUIDs fixed: {total_fixed_uuid}')
        )

    def fix_schema_citizens(self, schema_name, dry_run=False):
        """Fix digital_id and UUID values in a specific schema"""

        with connection.cursor() as cursor:
            # Check if citizens_citizen table exists in this schema
            cursor.execute(f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = '{schema_name}'
                    AND table_name = 'citizens_citizen'
                )
            """)

            if not cursor.fetchone()[0]:
                return 0, 0

            # Get citizens with UUID-format digital_id or null digital_id
            cursor.execute(f"""
                SELECT id, digital_id, uuid, kebele
                FROM {schema_name}.citizens_citizen
                WHERE digital_id IS NULL
                   OR digital_id ~ '^[0-9a-f]{{8}}-[0-9a-f]{{4}}-[0-9a-f]{{4}}-[0-9a-f]{{4}}-[0-9a-f]{{12}}$'
                   OR uuid IS NULL
            """)

            citizens_to_fix = cursor.fetchall()

            if not citizens_to_fix:
                return 0, 0

            self.stdout.write(f'Schema {schema_name}: Found {len(citizens_to_fix)} citizens to fix')

            fixed_digital_id_count = 0
            fixed_uuid_count = 0

            for citizen_id, old_digital_id, old_uuid, kebele in citizens_to_fix:
                updates = []
                params = []

                # Fix digital_id if needed
                if not old_digital_id or re.match(r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$', old_digital_id):
                    new_digital_id = self.generate_digital_id(kebele)

                    # Ensure uniqueness within this schema
                    attempts = 0
                    while attempts < 10:
                        cursor.execute(f"""
                            SELECT COUNT(*) FROM {schema_name}.citizens_citizen
                            WHERE digital_id = %s
                        """, [new_digital_id])

                        if cursor.fetchone()[0] == 0:
                            break

                        attempts += 1
                        new_digital_id = self.generate_digital_id(kebele)

                    updates.append("digital_id = %s")
                    params.append(new_digital_id)
                    fixed_digital_id_count += 1

                    if dry_run:
                        self.stdout.write(f'  Would fix digital_id for citizen {citizen_id}: {old_digital_id} -> {new_digital_id}')

                # Fix UUID if needed
                if not old_uuid:
                    import uuid
                    new_uuid = str(uuid.uuid4())
                    updates.append("uuid = %s")
                    params.append(new_uuid)
                    fixed_uuid_count += 1

                    if dry_run:
                        self.stdout.write(f'  Would fix UUID for citizen {citizen_id}: NULL -> {new_uuid}')

                # Apply updates
                if updates and not dry_run:
                    params.append(citizen_id)
                    update_sql = f"""
                        UPDATE {schema_name}.citizens_citizen
                        SET {', '.join(updates)}
                        WHERE id = %s
                    """
                    cursor.execute(update_sql, params)

                    self.stdout.write(f'  Fixed citizen {citizen_id}')

            return fixed_digital_id_count, fixed_uuid_count

    def generate_digital_id(self, kebele_id=None):
        """Generate digital_id in correct format"""
        city_code = "GO"  # Default
        subcity_code = "ZO"  # Default
        kebele_number = "01"  # Default
        
        if kebele_id:
            kebele_number = str(kebele_id).zfill(2)
        
        # Generate 10 random digits
        random_digits = ''.join(random.choices(string.digits, k=10))
        
        return f"{city_code}{subcity_code}{kebele_number}{random_digits}"
