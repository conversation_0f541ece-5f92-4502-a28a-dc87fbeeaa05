# General Workflow-Based Permissions System

## 🔄 **System Redesign Overview**

The GoID system has been updated from granular, specific permissions to **general workflow-based permissions** that align with actual user roles and responsibilities. This makes the system more intuitive and easier to manage.

## 🎯 **New Permission Structure**

### **📋 General Permissions Created**

#### **Citizen Management (Clerk Level)**
- `register_citizens` - Complete citizen registration workflow (includes parents, children, emergency contacts, spouse, photo, biometrics)
- `view_citizens_list` - View list of citizens
- `view_citizen_details` - View detailed citizen information

#### **ID Card Management**
- `generate_id_cards` - Generate ID cards for citizens
- `view_id_cards_list` - View list of ID cards
- `send_id_cards_for_approval` - Send ID cards to kebele leader for approval
- `approve_id_cards` - Approve ID cards
- `send_id_cards_to_higher_level` - Send ID cards to subcity admin

#### **Document Management**
- `verify_documents` - Verify citizen documents

#### **User Management (Admin Levels)**
- `create_kebele_users` - Create kebele users and assign roles
- `create_subcity_users` - Create subcity users and assign roles
- `manage_all_users` - Manage all users across all tenants

#### **Data Access (Hierarchical)**
- `view_own_kebele_data` - View own kebele data
- `view_child_kebeles_data` - View data from child kebeles
- `view_child_subcities_data` - View data from child subcities
- `view_all_tenants_data` - View data from all tenants

#### **Dashboard Access**
- `view_kebele_dashboard` - View kebele dashboard
- `view_subcity_dashboard` - View subcity dashboard (aggregated)
- `view_city_dashboard` - View city dashboard (aggregated)
- `view_system_dashboard` - View system-wide dashboard

#### **Reports**
- `view_kebele_reports` - View reports for own kebele
- `view_subcity_reports` - View cumulative reports for subcity
- `view_city_reports` - View cumulative reports for city
- `view_all_reports` - View all reports across system

#### **System Administration**
- `manage_tenants` - Manage tenants (create, edit, delete)
- `manage_system_settings` - Manage system-wide settings
- `full_system_access` - Full access to all system features

## 👥 **Updated Group Roles**

### **🔧 Clerk**
**Role**: Kebele clerk with citizen registration and basic ID card management
**Permissions**:
- ✅ Register citizens (full workflow including parents, children, emergency contacts, spouse, photo, biometrics)
- ✅ View citizens list
- ✅ View citizen details
- ✅ Generate ID cards
- ✅ View ID cards list
- ✅ Send ID cards to kebele leader for approval
- ✅ View own kebele dashboard

### **👨‍💼 Kebele Leader**
**Role**: Kebele leader with approval and verification responsibilities
**Permissions**:
- ✅ View citizens list (if only sent from the clerk)
- ✅ View citizen details
- ✅ View ID cards list (if only sent from the clerk)
- ✅ Approve ID cards
- ✅ Send ID cards to subcity admin
- ✅ Verify documents
- ✅ View reports for their kebele
- ✅ View own kebele dashboard

### **🏢 Subcity Admin**
**Role**: Subcity administrator managing multiple kebeles
**Permissions**:
- ✅ Create kebele users and assign roles
- ✅ View citizens list from child kebeles
- ✅ View ID cards list
- ✅ Approve ID cards
- ✅ View reports for their subcity (cumulative reports from child kebeles)
- ✅ View own subcity dashboard (aggregated from all child kebeles)

### **🏛️ City Admin**
**Role**: City administrator managing multiple subcities
**Permissions**:
- ✅ Create subcity users and assign roles
- ✅ View citizens list from child subcity tenants
- ✅ View reports for their city (cumulative reports from child subcities)
- ✅ View own city dashboard (aggregated from all child subcities → kebeles)

### **⚡ Super Admin**
**Role**: System super administrator with full access
**Permissions**:
- ✅ Full access to all features
- ✅ Manage all users, roles, and tenants
- ✅ View all data across all tenants
- ✅ View dashboard (aggregated from all cities → subcities → kebeles)

## 🔧 **Technical Implementation**

### **Permission Classes Updated**
All permission classes have been updated to use the new general permissions:

- `CanManageCitizensGroup` - Uses `register_citizens`, `view_citizens_list`, etc.
- `CanManageUsersGroup` - Uses `create_kebele_users`, `create_subcity_users`, etc.
- `CanManageIDCardsGroup` - Uses `generate_id_cards`, `approve_id_cards`, etc.
- `CanManageGroupsPermission` - Integrated with user creation workflow
- `IsClerkOrAdminGroup` - Uses basic operational permissions
- `IsKebeleAdminOrHigherGroup` - Uses administrative permissions

### **Workflow-Based Logic**
```python
# Example: Citizen registration includes everything
if user.has_group_permission('register_citizens'):
    # User can:
    # - Add/edit personal information
    # - Add/edit parents
    # - Add/edit children
    # - Add/edit emergency contacts
    # - Add/edit spouse
    # - Capture photo
    # - Capture biometrics
    # - Submit for processing
```

### **Hierarchical Data Access**
```python
# Subcity admin can view data from child kebeles
if user.has_group_permission('view_child_kebeles_data'):
    # Access citizens, ID cards, reports from all child kebeles

# City admin can view data from child subcities
if user.has_group_permission('view_child_subcities_data'):
    # Access aggregated data from all child subcities and their kebeles
```

## 📊 **Benefits of New System**

### **✅ Simplified Management**
- **Before**: 50+ granular permissions (add_citizen, edit_citizen, add_parent, edit_parent, etc.)
- **After**: 27 workflow-based permissions that match actual job functions

### **✅ Intuitive Role Assignment**
- **Before**: Complex permission combinations needed for each role
- **After**: Clear, role-based permission sets that match real-world responsibilities

### **✅ Easier Onboarding**
- **Before**: Administrators needed to understand technical permission details
- **After**: Permissions match job descriptions and workflows

### **✅ Reduced Errors**
- **Before**: Easy to miss required permissions when creating roles
- **After**: Comprehensive workflow permissions reduce configuration errors

### **✅ Better Security**
- **Before**: Over-permissioning due to complexity
- **After**: Clear permission boundaries aligned with job functions

## 🚀 **How to Use**

### **1. Access Group Management**
- Navigate to `/users/kebele-management`
- Select the kebele to manage
- View existing groups and their permissions

### **2. Create New Groups**
- Use system group templates (clerk, kebele_leader, etc.)
- Or create custom groups with specific permission combinations
- Assign users to appropriate groups

### **3. Verify Permissions**
- Check user group memberships
- Test access to different system features
- Monitor permission usage through logs

### **4. Migration from Old System**
- Existing users will automatically use new permission system
- Old granular permissions are still supported for backward compatibility
- Gradually migrate custom groups to use new general permissions

## 🔍 **Testing the New System**

### **Test Scenarios**
1. **Clerk User**: Should be able to complete full citizen registration workflow
2. **Kebele Leader**: Should be able to approve ID cards and verify documents
3. **Subcity Admin**: Should be able to create kebele users and view aggregated data
4. **City Admin**: Should be able to manage subcity users and view city-wide reports
5. **Super Admin**: Should have access to all system features

### **Verification Commands**
```bash
# Check user permissions
docker-compose exec backend python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
user = User.objects.first()
print('User groups:', [g.name for g in user.get_user_groups()])
print('Has register_citizens:', user.has_group_permission('register_citizens'))
"

# Check group permissions
docker-compose exec backend python manage.py shell -c "
from django.contrib.auth.models import Group
clerk = Group.objects.get(name='clerk')
print('Clerk permissions:', [p.codename for p in clerk.permissions.all()])
"
```

## 📈 **Future Enhancements**

### **Planned Improvements**
- **Permission Templates**: Pre-configured permission sets for common scenarios
- **Dynamic Permissions**: Context-aware permissions based on tenant hierarchy
- **Audit Logging**: Track permission usage and changes
- **Permission Analytics**: Monitor which permissions are most/least used

### **Custom Group Creation**
- **Specialized Roles**: Create groups for specific functions (e.g., "Document Verifier", "ID Card Processor")
- **Temporary Permissions**: Time-limited access for specific tasks
- **Cross-Tenant Groups**: Groups that work across multiple tenants

---

**The new general permission system provides a more intuitive, manageable, and secure approach to access control that aligns with real-world job functions and workflows!** 🎉
