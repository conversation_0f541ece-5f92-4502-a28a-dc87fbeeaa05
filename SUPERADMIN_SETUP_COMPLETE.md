# GoID Superadmin Setup - Complete Configuration

## ✅ What Was Accomplished

### 1. **Enhanced Setup Script** (`backend/setup_general_permissions.py`)
- ✅ **Automatic Superadmin Creation**: Creates `<EMAIL>` user with `admin123` password
- ✅ **Group Assignment**: Automatically assigns superadmin to `super_admin` group
- ✅ **Permission Configuration**: Ensures all 28 system permissions are properly configured
- ✅ **Database Schema Fix**: Fixed `user_tenant_schema` field to allow NULL values for superadmin

### 2. **System Initialization Script** (`backend/init_system.py`)
- ✅ **Complete System Setup**: Runs migrations, permissions, groups, and verification
- ✅ **Verification System**: Checks that superadmin user and permissions are properly configured
- ✅ **Startup Integration**: Automatically runs during container startup

### 3. **Frontend Navigation Update** (`frontend/src/hooks/usePermissions.js`)
- ✅ **Superadmin-Specific Navigation**: Shows only relevant menus for superadmin users
- ✅ **Clean Interface**: Removes unnecessary menus (Citizens, ID Cards, Clearances, Transfers)
- ✅ **Focused Menu Items**:
  - 📊 **Dashboard** - System overview
  - 🏢 **Tenants** - Tenant management (list, create, edit)
  - 👥 **System Users** - User management (list, create)
  - ⚙️ **System Settings** - System configuration
  - 🎨 **Theme Manager** - Tenant theme customization

### 4. **Permission System** 
- ✅ **28 Granular Permissions**: Complete workflow-based permission system
- ✅ **5 System Groups**: clerk, kebele_leader, subcity_admin, city_admin, super_admin
- ✅ **Cross-Schema Architecture**: Groups in public schema, users in tenant schemas
- ✅ **JWT Integration**: All permissions included in authentication tokens

## 🎯 Superadmin Navigation Menu

The superadmin user now sees **only** these navigation items:

```
📊 Dashboard
   └── System overview and statistics

🏢 Tenants  
   ├── Tenants List
   └── Register Tenant

👥 System Users
   ├── Users List  
   └── Create User

⚙️ System Settings
   └── System configuration

🎨 Theme Manager
   └── Tenant theme customization
```

**Removed menus for superadmin:**
- ❌ Citizens (not needed for system administration)
- ❌ ID Cards (not needed for system administration) 
- ❌ Clearances (operational feature)
- ❌ Transfers (operational feature)
- ❌ Reports (can be added later if needed)

## 🔐 Login Credentials

**Superadmin Access:**
- **Email**: `<EMAIL>`
- **Password**: `admin123`
- **Role**: `superadmin`
- **Permissions**: All 28 system permissions

## 🚀 How to Use

### 1. **Automatic Setup** (Recommended)
The system now automatically configures itself during startup:

```bash
docker-compose up -d
# System automatically runs init_system.py during startup
```

### 2. **Manual Setup** (If needed)
```bash
# Run the complete setup script
docker-compose exec backend python init_system.py

# Or just permissions and groups
docker-compose exec backend python setup_general_permissions.py
```

### 3. **Login to Frontend**
1. Go to: http://localhost:3000
2. Login with: `<EMAIL>` / `admin123`
3. You should see the clean superadmin navigation menu

## 🔍 Verification

### Check Superadmin Setup:
```bash
docker-compose exec backend python -c "
from django.contrib.auth import get_user_model
from users.models_groups import GroupMembership
from django_tenants.utils import schema_context, get_public_schema_name

User = get_user_model()
user = User.objects.get(email='<EMAIL>')
print(f'User: {user.email}')
print(f'Role: {user.role}')
print(f'Is superuser: {user.is_superuser}')

with schema_context(get_public_schema_name()):
    memberships = GroupMembership.objects.filter(user_email=user.email, is_active=True)
    print(f'Groups: {[m.group.name for m in memberships]}')
"
```

### Check JWT Token:
```bash
# Login and check token permissions
curl -X POST http://localhost:8000/api/auth/token/ \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'
```

## 📁 Files Modified

### Backend:
- ✅ `backend/setup_general_permissions.py` - Enhanced with superadmin creation
- ✅ `backend/init_system.py` - New complete initialization script
- ✅ `backend/startup.sh` - Updated to run system initialization
- ✅ `backend/users/models_groups.py` - Fixed schema field for superadmin

### Frontend:
- ✅ `frontend/src/hooks/usePermissions.js` - Updated navigation for superadmin
- ✅ `frontend/src/App.jsx` - Fixed system settings permission

## 🎉 Result

The GoID system now has:
- ✅ **Automatic superadmin setup** during container startup
- ✅ **Clean, focused navigation** for superadmin users
- ✅ **Proper permission system** with 28 granular permissions
- ✅ **Robust initialization** with verification
- ✅ **Ready-to-use system** with no manual configuration needed

**The superadmin user can now access:**
- Tenant management (create, edit, delete tenants)
- System user management (create, manage all users)
- System settings and configuration
- Theme management for tenants
- System dashboard and overview

**No longer cluttered with operational features** like citizen registration, ID card processing, clearances, or transfers that are not relevant for system administration.
