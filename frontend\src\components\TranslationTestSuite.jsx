import React, { useState } from 'react';
import { useLocalization } from '../contexts/LocalizationContext';
import LanguageSwitcher from './common/LanguageSwitcher';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  Chip,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Divider
} from '@mui/material';

const TranslationTestSuite = () => {
  const { t, currentLanguage, getAvailableLanguages, translationStats } = useLocalization();
  const [testResults, setTestResults] = useState(null);

  // Test keys to validate
  const testKeys = [
    // Dashboard
    'dashboard', 'total_citizens', 'new_registrations', 'pending_approvals',
    'flagged_cases', 'approved_cards', 'expiring_soon', 'expired_ids',
    'migration_activity', 'this_month', 'awaiting_review', 'require_attention',
    'ready_for_printing', 'next_30_days', 'over_30_days',
    
    // Charts and Analytics
    'population_by_ketena', 'age_group_distribution', 'monthly_registration_trends',
    'top_3_populated_ketenas', 'gender_ratio', 'id_status_summary',
    'recent_citizens', 'recent_id_card_activity', 'no_data_available',
    
    // Common Actions
    'save', 'cancel', 'submit', 'edit', 'delete', 'view', 'search', 'filter',
    'retry', 'back', 'next', 'previous', 'approve', 'reject', 'print', 'close',
    'confirm', 'reset', 'refresh',
    
    // Specific Actions
    'generate_printable_book', 'search_citizens', 'clear_filters',
    'submit_for_approval', 'kebele_approve', 'subcity_approve',
    'reset_to_draft', 'print_preview', 'print_front', 'print_back',
    
    // Status and States
    'active', 'inactive', 'pending', 'approved', 'rejected', 'draft',
    'completed', 'in_progress', 'fully_approved', 'kebele_approved',
    'pending_kebele_approval', 'printed', 'issued',
    
    // Form Labels
    'all_sub_cities', 'all_genders', 'all_ages', 'sub_city', 'age_group',
    'photo', 'name', 'id_number', 'date_of_birth', 'gender', 'phone', 'location',
    
    // Basic terms
    'male', 'female', 'loading', 'success', 'error', 'yes', 'no'
  ];

  const runTranslationTest = () => {
    const results = {
      language: currentLanguage,
      totalKeys: testKeys.length,
      translatedKeys: 0,
      missingKeys: [],
      emptyTranslations: [],
      defaultFallbacks: []
    };

    testKeys.forEach(key => {
      const translation = t(key, `DEFAULT_${key.toUpperCase()}`);
      
      if (translation === key) {
        results.missingKeys.push(key);
      } else if (translation === `DEFAULT_${key.toUpperCase()}`) {
        results.defaultFallbacks.push(key);
      } else if (translation.trim() === '') {
        results.emptyTranslations.push(key);
      } else {
        results.translatedKeys++;
      }
    });

    results.coverage = ((results.translatedKeys / results.totalKeys) * 100).toFixed(1);
    setTestResults(results);
  };

  const getStatusColor = (coverage) => {
    if (coverage >= 90) return 'success';
    if (coverage >= 70) return 'warning';
    return 'error';
  };

  return (
    <Box sx={{ p: 3 }}>
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h4" component="h1" gutterBottom>
              🌐 Translation Test Suite
            </Typography>
            <LanguageSwitcher size="lg" />
          </Box>
          
          <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
            Test and validate translations across all supported languages. 
            Current language: <strong>{currentLanguage}</strong>
          </Typography>

          <Button 
            variant="contained" 
            onClick={runTranslationTest}
            size="large"
            sx={{ mr: 2 }}
          >
            Run Translation Test
          </Button>

          {translationStats && (
            <Chip 
              label={`${translationStats.loadedKeys || 0} keys loaded`}
              color="info"
              sx={{ ml: 2 }}
            />
          )}
        </CardContent>
      </Card>

      {testResults && (
        <Grid container spacing={3}>
          {/* Summary Cards */}
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h3" color="primary.main">
                  {testResults.totalKeys}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Keys Tested
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h3" color="success.main">
                  {testResults.translatedKeys}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Successfully Translated
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h3" color={getStatusColor(testResults.coverage) + '.main'}>
                  {testResults.coverage}%
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Translation Coverage
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h3" color="error.main">
                  {testResults.missingKeys.length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Missing Translations
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Detailed Results */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Translation Test Results for {testResults.language.toUpperCase()}
                </Typography>
                
                <Alert 
                  severity={getStatusColor(testResults.coverage)} 
                  sx={{ mb: 2 }}
                >
                  Translation coverage: {testResults.coverage}% 
                  ({testResults.translatedKeys}/{testResults.totalKeys} keys)
                </Alert>

                {testResults.missingKeys.length > 0 && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" color="error" gutterBottom>
                      Missing Translations ({testResults.missingKeys.length}):
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                      {testResults.missingKeys.map(key => (
                        <Chip key={key} label={key} size="small" color="error" variant="outlined" />
                      ))}
                    </Box>
                  </Box>
                )}

                {testResults.defaultFallbacks.length > 0 && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" color="warning.main" gutterBottom>
                      Using Default Fallbacks ({testResults.defaultFallbacks.length}):
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                      {testResults.defaultFallbacks.map(key => (
                        <Chip key={key} label={key} size="small" color="warning" variant="outlined" />
                      ))}
                    </Box>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Sample Translations */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Sample Translations
                </Typography>
                <TableContainer component={Paper} variant="outlined">
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell><strong>Key</strong></TableCell>
                        <TableCell><strong>Translation</strong></TableCell>
                        <TableCell><strong>Status</strong></TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {testKeys.slice(0, 10).map(key => {
                        const translation = t(key, `DEFAULT_${key.toUpperCase()}`);
                        let status = 'success';
                        let statusText = 'Translated';
                        
                        if (translation === key) {
                          status = 'error';
                          statusText = 'Missing';
                        } else if (translation === `DEFAULT_${key.toUpperCase()}`) {
                          status = 'warning';
                          statusText = 'Fallback';
                        }
                        
                        return (
                          <TableRow key={key}>
                            <TableCell>{key}</TableCell>
                            <TableCell>{translation}</TableCell>
                            <TableCell>
                              <Chip label={statusText} size="small" color={status} />
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default TranslationTestSuite;
