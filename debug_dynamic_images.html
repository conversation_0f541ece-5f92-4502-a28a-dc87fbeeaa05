<!DOCTYPE html>
<html>
<head>
    <title>Debug Dynamic Images</title>
</head>
<body>
    <h1>Debug Dynamic Images</h1>
    <div id="results"></div>

    <script>
        async function debugImages() {
            const resultsDiv = document.getElementById('results');
            
            try {
                // Test the exact API call the frontend makes
                console.log('Testing Kebele14 API call...');
                const response = await fetch('http://localhost:8000/api/tenants/7/', {
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                const data = await response.json();
                
                console.log('Full API Response:', data);
                
                resultsDiv.innerHTML += `<h2>API Response Debug:</h2>`;
                resultsDiv.innerHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                
                const profileData = data.profile_data;
                if (profileData) {
                    resultsDiv.innerHTML += `<h2>Profile Data Found:</h2>`;
                    
                    // Test each image field
                    const fields = ['logo', 'mayor_signature', 'pattern_image'];
                    fields.forEach(field => {
                        const value = profileData[field];
                        resultsDiv.innerHTML += `<h3>${field}:</h3>`;
                        resultsDiv.innerHTML += `<p>Raw value: ${value}</p>`;
                        
                        if (value) {
                            const fullUrl = value.startsWith('http') ? value : `http://localhost:8000${value}`;
                            resultsDiv.innerHTML += `<p>Full URL: ${fullUrl}</p>`;
                            
                            // Test if image loads
                            const img = new Image();
                            img.onload = () => {
                                resultsDiv.innerHTML += `<p style="color: green;">✅ ${field} loads successfully</p>`;
                                resultsDiv.innerHTML += `<img src="${fullUrl}" style="max-width: 200px; border: 1px solid #ccc;">`;
                            };
                            img.onerror = () => {
                                resultsDiv.innerHTML += `<p style="color: red;">❌ ${field} failed to load</p>`;
                            };
                            img.src = fullUrl;
                        } else {
                            resultsDiv.innerHTML += `<p style="color: orange;">⚠️ ${field} is empty</p>`;
                        }
                    });
                } else {
                    resultsDiv.innerHTML += `<p style="color: red;">❌ No profile_data found!</p>`;
                }
                
                // Test the exact logic the frontend uses
                resultsDiv.innerHTML += `<h2>Frontend Logic Test:</h2>`;
                
                // Simulate the frontend logic
                const kebeleData = profileData || {};
                let logoUrl = null;
                let patternImageUrl = null;
                let signatureUrl = null;
                
                // Logo logic (should always work)
                if (kebeleData?.logo) {
                    logoUrl = kebeleData.logo.startsWith('http') ? kebeleData.logo : `http://localhost:8000${kebeleData.logo}`;
                    resultsDiv.innerHTML += `<p>✅ Logo URL determined: ${logoUrl}</p>`;
                } else {
                    resultsDiv.innerHTML += `<p>❌ No logo found in kebeleData</p>`;
                }
                
                // Pattern logic (requires approval)
                const isApprovedForPrinting = true; // Simulating printed status
                if (isApprovedForPrinting) {
                    if (kebeleData?.pattern_image) {
                        patternImageUrl = kebeleData.pattern_image.startsWith('http') ? kebeleData.pattern_image : `http://localhost:8000${kebeleData.pattern_image}`;
                        resultsDiv.innerHTML += `<p>✅ Pattern URL determined: ${patternImageUrl}</p>`;
                    } else {
                        resultsDiv.innerHTML += `<p>❌ No pattern_image found in kebeleData</p>`;
                    }
                } else {
                    resultsDiv.innerHTML += `<p>❌ Not approved for printing</p>`;
                }
                
                // Signature logic (requires approval)
                if (isApprovedForPrinting) {
                    if (kebeleData?.mayor_signature) {
                        signatureUrl = kebeleData.mayor_signature.startsWith('http') ? kebeleData.mayor_signature : `http://localhost:8000${kebeleData.mayor_signature}`;
                        resultsDiv.innerHTML += `<p>✅ Signature URL determined: ${signatureUrl}</p>`;
                    } else {
                        resultsDiv.innerHTML += `<p>❌ No mayor_signature found in kebeleData</p>`;
                    }
                } else {
                    resultsDiv.innerHTML += `<p>❌ Not approved for printing</p>`;
                }
                
            } catch (error) {
                console.error('Error:', error);
                resultsDiv.innerHTML += `<p style="color: red;">Error: ${error.message}</p>`;
            }
        }
        
        // Run test when page loads
        debugImages();
    </script>
</body>
</html>
