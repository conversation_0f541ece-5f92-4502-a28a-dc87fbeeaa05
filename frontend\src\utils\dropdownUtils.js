/**
 * Utility functions for dropdown components
 */

/**
 * Calculate safe max height for dropdown based on viewport
 */
export const getSafeMaxHeight = (defaultHeight = 200) => {
  if (typeof window === 'undefined') return defaultHeight;

  // Get viewport height and leave some padding for safety
  const viewportHeight = window.innerHeight;
  const safeHeight = Math.min(defaultHeight, viewportHeight * 0.4); // Max 40% of viewport

  return Math.max(safeHeight, 120); // Minimum 120px to show at least 3 items
};

/**
 * Standard MenuProps configuration for Material-UI Select components
 * This prevents the popover height warning and provides consistent styling
 */
export const getStandardMenuProps = (options = {}) => {
  const {
    maxHeight = getSafeMaxHeight(200),
    maxWidth = 400,
    disablePortal = false,
    variant = 'menu',
    ...customProps
  } = options;

  const safeMaxHeight = getSafeMaxHeight(maxHeight);

  return {
    PaperProps: {
      sx: {
        maxHeight: safeMaxHeight,
        maxWidth,
        overflow: 'auto',
        '& .MuiMenuItem-root': {
          fontSize: '0.875rem',
          minHeight: 40,
        },
        ...customProps.paperSx,
      },
    },
    anchorOrigin: {
      vertical: 'bottom',
      horizontal: 'left',
    },
    transformOrigin: {
      vertical: 'top',
      horizontal: 'left',
    },
    disablePortal,
    variant,
    ...customProps,
  };
};

/**
 * MenuProps specifically optimized for dropdowns with many options
 */
export const getLargeDropdownMenuProps = () => {
  return getStandardMenuProps({
    maxHeight: getSafeMaxHeight(250),
    maxWidth: 500,
  });
};

/**
 * MenuProps for small/compact dropdowns
 */
export const getCompactMenuProps = () => {
  return getStandardMenuProps({
    maxHeight: getSafeMaxHeight(150),
    maxWidth: 300,
    paperSx: {
      '& .MuiMenuItem-root': {
        fontSize: '0.8125rem',
        minHeight: 36,
      },
    },
  });
};

/**
 * Creates a proper onChange handler for Material-UI Select components
 * This ensures the event object has the correct structure for form handlers like Formik
 *
 * @param {string} name - The name of the field
 * @param {function} onChange - The original onChange handler (e.g., formik.handleChange)
 * @returns {function} A properly structured onChange handler
 */
export const createSelectChangeHandler = (name, onChange) => {
  return (event) => {
    // Ensure the event object has the correct structure for Formik and other form handlers
    const syntheticEvent = {
      ...event,
      target: {
        ...event.target,
        name: name,
        value: event.target.value
      }
    };
    onChange(syntheticEvent);
  };
};
