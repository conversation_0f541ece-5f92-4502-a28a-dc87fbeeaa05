// Force Frontend Refresh Script
// Run this in browser console to force refresh the kebele data

console.log('🔄 Forcing frontend data refresh...');

// Clear localStorage
localStorage.clear();
console.log('✅ Cleared localStorage');

// Clear sessionStorage  
sessionStorage.clear();
console.log('✅ Cleared sessionStorage');

// Clear any cached data in memory
if (window.location.pathname.includes('kebele-management')) {
    console.log('📍 On kebele management page - forcing component refresh...');
    
    // Force a hard refresh of the page
    window.location.reload(true);
} else {
    console.log('📍 Navigate to kebele management page and run this script again');
    console.log('Or manually navigate to: /users/kebele-management');
}

console.log('🎯 Frontend refresh completed!');
console.log('Expected: UI should now show same number of kebeles as API response');
