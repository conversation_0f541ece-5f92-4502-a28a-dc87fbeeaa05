# Generated by Django 4.2.7 on 2025-06-11 15:07

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tenants', '0007_tenant_name_am'),
    ]

    operations = [
        migrations.AddField(
            model_name='kebele',
            name='mayor_signature',
            field=models.ImageField(blank=True, help_text='Signature image for ID cards (recommended: 200x80px, PNG/JPG)', null=True, upload_to='signatures/kebele/', verbose_name='Mayor/Leader Signature'),
        ),
        migrations.AddField(
            model_name='kebele',
            name='pattern_image',
            field=models.ImageField(blank=True, help_text='Custom security pattern for ID cards (recommended: 500x300px, PNG with transparency)', null=True, upload_to='patterns/kebele/', verbose_name='Security Pattern Image'),
        ),
        migrations.AddField(
            model_name='subcity',
            name='mayor_signature',
            field=models.ImageField(blank=True, help_text='Signature image for ID cards (recommended: 200x80px, PNG/JPG)', null=True, upload_to='signatures/subcity/', verbose_name='Mayor/Admin Signature'),
        ),
        migrations.AddField(
            model_name='subcity',
            name='pattern_image',
            field=models.ImageField(blank=True, help_text='Custom security pattern for ID cards (recommended: 500x300px, PNG with transparency)', null=True, upload_to='patterns/subcity/', verbose_name='Security Pattern Image'),
        ),
    ]
