from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.http import JsonResponse
from ..models.tenant import Tenant
from ..models.city import CityAdministration


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_amharic_names(request):
    """
    Get tenant names with Amharic data for ID card headers.
    Fetches Amharic names directly from the Tenant table where they are stored.

    Query Parameters:
    - city_tenant_id: ID of the city tenant
    - subcity_tenant_id: ID of the subcity tenant
    - kebele_tenant_id: ID of the kebele tenant
    - citizen_kebele_id: ID of citizen's kebele (for subcity context)
    - citizen_subcity_id: ID of citizen's subcity (for city context)

    Returns:
    - city_name_am: City name in Amharic from Tenant table
    - subcity_name_am: Subcity name in Amharic from Tenant table
    - kebele_name_am: Kebele name in Amharic from Tenant table
    - mayor_signature: Mayor signature URL (if available and approved)
    """
    try:
        # Get tenant IDs from query parameters
        city_tenant_id = request.query_params.get('city_tenant_id')
        subcity_tenant_id = request.query_params.get('subcity_tenant_id')
        kebele_tenant_id = request.query_params.get('kebele_tenant_id')

        # Additional parameters for cross-tenant context (subcity viewing kebele cards)
        citizen_kebele_id = request.query_params.get('citizen_kebele_id')
        citizen_subcity_id = request.query_params.get('citizen_subcity_id')

        print(f"🔍 Amharic names request:")
        print(f"  City tenant: {city_tenant_id}")
        print(f"  Subcity tenant: {subcity_tenant_id}")
        print(f"  Kebele tenant: {kebele_tenant_id}")
        print(f"  Citizen kebele: {citizen_kebele_id}")
        print(f"  Citizen subcity: {citizen_subcity_id}")

        result = {}

        # Get city Amharic name and mayor signature
        if city_tenant_id:
            try:
                city_tenant = Tenant.objects.get(id=city_tenant_id, type='city')
                result['city_name_am'] = city_tenant.name_am or city_tenant.name
                print(f"✅ City tenant found: {city_tenant.name} (Amharic: {result['city_name_am']})")

                # Get mayor signature from city administration
                try:
                    city_admin = CityAdministration.objects.get(tenant_id=city_tenant_id)
                    result['mayor_signature'] = city_admin.mayor_signature.url if city_admin.mayor_signature else None
                    print(f"✅ Mayor signature: {result['mayor_signature']}")
                except CityAdministration.DoesNotExist:
                    result['mayor_signature'] = None
                    print(f"⚠️ No city administration found for tenant_id: {city_tenant_id}")

            except Tenant.DoesNotExist:
                print(f"❌ City tenant not found for id: {city_tenant_id}")
                result['city_name_am'] = None
                result['mayor_signature'] = None

        # Get subcity Amharic name
        subcity_id_to_use = subcity_tenant_id or citizen_subcity_id
        if subcity_id_to_use:
            try:
                subcity_tenant = Tenant.objects.get(id=subcity_id_to_use, type='subcity')
                result['subcity_name_am'] = subcity_tenant.name_am or subcity_tenant.name
                print(f"✅ Subcity tenant found: {subcity_tenant.name} (Amharic: {result['subcity_name_am']})")
            except Tenant.DoesNotExist:
                print(f"❌ Subcity tenant not found for id: {subcity_id_to_use}")
                result['subcity_name_am'] = None

        # Get kebele Amharic name
        kebele_id_to_use = kebele_tenant_id or citizen_kebele_id
        if kebele_id_to_use:
            try:
                kebele_tenant = Tenant.objects.get(id=kebele_id_to_use, type='kebele')
                result['kebele_name_am'] = kebele_tenant.name_am or kebele_tenant.name
                print(f"✅ Kebele tenant found: {kebele_tenant.name} (Amharic: {result['kebele_name_am']})")
            except Tenant.DoesNotExist:
                print(f"❌ Kebele tenant not found for id: {kebele_id_to_use}")
                result['kebele_name_am'] = None

        print(f"✅ Amharic names result: {result}")
        return JsonResponse(result)

    except Exception as e:
        print(f"❌ Error fetching Amharic names: {e}")
        return JsonResponse(
            {'error': f'Failed to fetch Amharic names: {str(e)}'},
            status=500
        )
