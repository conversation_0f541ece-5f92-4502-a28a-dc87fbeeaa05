# Official Futronic SDK Integration

This is a professional implementation of Futronic's official SDK integration for the GoID biometric system.

## Architecture Overview

The service follows Futronic's recommended architecture:

```
┌─────────────────────────────────────────────────────────────┐
│                    GoID Web Application                     │
└─────────────────────┬───────────────────────────────────────┘
                      │ HTTP API Calls
┌─────────────────────▼───────────────────────────────────────┐
│              Official Futronic SDK Service                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   Flask API     │  │  SDK Manager    │  │   Device    │ │
│  │   Endpoints     │  │                 │  │  Manager    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │ Native SDK Calls
┌─────────────────────▼───────────────────────────────────────┐
│                 Futronic SDK DLLs                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │  ftrScanAPI.dll │  │ ftrMathAPI.dll  │  │ LiveFinger2 │ │
│  │  (Required)     │  │   (Optional)    │  │   (Optional)│ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │ USB Communication
┌─────────────────────▼───────────────────────────────────────┐
│                 Futronic FS88H Device                      │
└─────────────────────────────────────────────────────────────┘
```

## Features

### ✅ Core Functionality
- **Native SDK Integration**: Direct integration with ftrScanAPI.dll
- **Device Management**: Professional device initialization and monitoring
- **Image Capture**: High-quality fingerprint image acquisition (320x480, 500 DPI)
- **Quality Validation**: Automatic image quality assessment
- **Live Finger Detection**: Real-time finger presence detection
- **Professional Logging**: Comprehensive logging with file and console output

### ✅ Advanced Features (with ftrMathAPI.dll)
- **Template Extraction**: Convert images to minutiae-based templates
- **Template Matching**: 1-to-1 fingerprint comparison with confidence scores
- **Duplicate Detection**: Prevent multiple registrations of the same person

### ✅ Service Layer
- **RESTful API**: Clean HTTP endpoints for web integration
- **CORS Support**: Cross-origin requests for web applications
- **Error Handling**: Comprehensive error reporting and recovery
- **Professional UI**: Web-based status and testing interface

## Required Files

### Essential SDK Files
- `ftrScanAPI.dll` - **Required** - Core scanning functionality
- `official_futronic_sdk_service.py` - Main service implementation

### Optional SDK Files
- `ftrMathAPI.dll` - **Optional** - Template extraction and matching
- `LiveFinger2.dll` - **Optional** - Enhanced live finger detection

### Service Files
- `start_official_futronic_sdk_service.bat` - Service launcher
- `futronic_sdk_service.log` - Service log file (auto-created)

## Installation & Setup

### 1. Prerequisites
```bash
# Python 3.7 or higher
python --version

# Required Python packages
pip install flask flask-cors
```

### 2. SDK Files Setup
1. Copy Futronic SDK DLL files to the service directory:
   - `ftrScanAPI.dll` (required)
   - `ftrMathAPI.dll` (optional, for template matching)
   - `LiveFinger2.dll` (optional, for enhanced detection)

2. Ensure your Futronic FS88H device is connected via USB

### 3. Start the Service
```bash
# Windows
start_official_futronic_sdk_service.bat

# Or directly with Python
python official_futronic_sdk_service.py
```

### 4. Verify Installation
- Open browser to `http://localhost:8001`
- Check device status and test capture functionality
- Review logs in `futronic_sdk_service.log`

## API Endpoints

### Device Management
```http
GET /api/device/status
POST /api/device/initialize
```

### Fingerprint Operations
```http
POST /api/capture/fingerprint
POST /api/match/templates
```

### Service Health
```http
GET /api/health
GET /
```

## Usage Examples

### Capture Fingerprint
```javascript
const response = await fetch('http://localhost:8001/api/capture/fingerprint', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ thumb_type: 'left' })
});

const result = await response.json();
if (result.success) {
    console.log('Template data:', result.data.template_data);
    console.log('Quality score:', result.data.quality_score);
}
```

### Match Templates
```javascript
const response = await fetch('http://localhost:8001/api/match/templates', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        template1: 'base64_encoded_template_1',
        template2: 'base64_encoded_template_2'
    })
});

const result = await response.json();
if (result.success) {
    console.log('Match score:', result.data.match_score);
    console.log('Is match:', result.data.is_match);
}
```

## Integration with GoID System

The service is designed to work seamlessly with your existing GoID biometric integration:

1. **Frontend Integration**: Update `biometricService.js` to use the official SDK endpoints
2. **Backend Integration**: The service provides the same API interface as your existing services
3. **Database Storage**: Template data is compatible with your existing biometric data models

## Troubleshooting

### Common Issues

**Device Not Found**
- Ensure Futronic FS88H is connected via USB
- Check Windows Device Manager for device recognition
- Verify USB cable and port functionality

**SDK DLL Errors**
- Ensure `ftrScanAPI.dll` is in the service directory
- Check DLL architecture (32-bit vs 64-bit) matches Python installation
- Verify DLL files are not corrupted

**Template Matching Disabled**
- Install `ftrMathAPI.dll` for full template functionality
- Service will work with image-based templates if Math API unavailable

**Service Won't Start**
- Check Python installation and version
- Install required packages: `pip install flask flask-cors`
- Review `futronic_sdk_service.log` for detailed error information

## Production Deployment

For production deployment:

1. **Service Management**: Use Windows Service or process manager
2. **Auto-Start**: Configure service to start with system boot
3. **Monitoring**: Implement health checks and alerting
4. **Security**: Configure firewall rules for port 8001
5. **Logging**: Set up log rotation and monitoring

## Support

This implementation follows Futronic's official SDK documentation and best practices. For SDK-specific issues, refer to Futronic's official documentation or contact their support team.

For GoID system integration questions, refer to the main project documentation.
