<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Full Subcity Pattern Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }
        
        .card-container {
            width: 800px;
            height: 500px;
            margin: 20px auto;
            position: relative;
            border: 2px solid #333;
            border-radius: 10px;
            overflow: hidden;
            background-color: white;
        }
        
        /* New SVG Pattern Background */
        .svg-pattern {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0.8;
            background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='800' height='500' viewBox='0 0 800 500' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3ClinearGradient id='base' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' stop-color='%23fefcf5'/%3E%3Cstop offset='50%25' stop-color='%23f2e9b3'/%3E%3Cstop offset='100%25' stop-color='%23bfa945'/%3E%3C/linearGradient%3E%3ClinearGradient id='headerWhite' x1='0%25' y1='0%25' x2='0%25' y2='100%25'%3E%3Cstop offset='0%25' stop-color='%23fffefb' stop-opacity='0.97'/%3E%3Cstop offset='100%25' stop-color='%23e9e7df' stop-opacity='0.2'/%3E%3C/linearGradient%3E%3Cpattern id='guilloche' width='120' height='120' patternUnits='userSpaceOnUse'%3E%3Cpath d='M60,0 C80,30 40,90 60,120 M0,60 C30,80 90,40 120,60 M30,0 C45,30 75,30 90,0 M30,120 C45,90 75,90 90,120' stroke='%23c9a034' fill='none' stroke-width='1.4' opacity='0.7'/%3E%3Ccircle cx='60' cy='60' r='20' stroke='%23d9b94b' fill='none' stroke-width='1.1' opacity='0.55'/%3E%3C/pattern%3E%3Crect width='100%25' height='100%25' fill='url(%23base)'/%3E%3Crect width='100%25' height='120' fill='url(%23headerWhite)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23guilloche)'/%3E%3C/svg%3E");
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
            z-index: 1;
        }
        
        /* Simple Full Subcity Pattern */
        .subcity-pattern {
            position: absolute;
            top: 0; /* Cover entire card */
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url('https://via.placeholder.com/800x500/4a90e2/ffffff?text=FULL+SUBCITY+PATTERN');
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
            opacity: 0.15; /* Clear visibility without overwhelming */
            z-index: 2;
            pointer-events: none;
        }
        
        .content {
            position: relative;
            z-index: 10;
            padding: 20px;
            color: #000;
            font-weight: bold;
        }
        
        .title {
            text-align: center;
            font-size: 24px;
            margin-bottom: 20px;
            color: #2c3e50;
        }
        
        .description {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .features {
            list-style: none;
            padding: 0;
        }
        
        .features li {
            margin: 10px 0;
            padding: 5px 0;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }
        
        .comparison {
            display: flex;
            gap: 20px;
            margin-top: 30px;
        }
        
        .comparison-item {
            flex: 1;
            text-align: center;
        }
        
        .comparison-card {
            width: 300px;
            height: 200px;
            position: relative;
            border: 1px solid #ccc;
            border-radius: 8px;
            overflow: hidden;
            margin: 0 auto 10px;
        }
        
        /* Before - Partial pattern */
        .before-pattern {
            position: absolute;
            top: 60px; /* Only from golden line */
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url('https://via.placeholder.com/300x140/4a90e2/ffffff?text=PARTIAL+PATTERN');
            background-size: cover;
            opacity: 0.08;
            z-index: 1;
        }
        
        /* After - Full pattern */
        .after-pattern {
            position: absolute;
            top: 0; /* Full card coverage */
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url('https://via.placeholder.com/300x200/4a90e2/ffffff?text=FULL+PATTERN');
            background-size: cover;
            opacity: 0.15;
            z-index: 1;
        }
        
        .comparison-content {
            position: relative;
            z-index: 10;
            padding: 10px;
            color: #000;
            font-size: 14px;
            font-weight: bold;
        }
        
        .svg-bg {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #fefcf5 0%, #f2e9b3 50%, #bfa945 100%);
            opacity: 0.8;
            z-index: 0;
        }
    </style>
</head>
<body>
    <h1 style="text-align: center; color: #2c3e50;">Simple Full Subcity Pattern Implementation</h1>
    
    <div class="card-container">
        <div class="svg-pattern"></div>
        <div class="subcity-pattern"></div>
        <div class="content">
            <div class="title">Clean Full Pattern Coverage</div>
            <div class="description">
                The subcity pattern now covers the entire card with simple, clean implementation:
            </div>
            <ul class="features">
                <li>✅ <strong>Full Card Coverage:</strong> Pattern covers entire card (top: 0)</li>
                <li>✅ <strong>Clear Visibility:</strong> 0.15 opacity for front, 0.12 for back</li>
                <li>✅ <strong>No Artistic Effects:</strong> Removed all blend modes and filters</li>
                <li>✅ <strong>No Animation:</strong> Static pattern for professional appearance</li>
                <li>✅ <strong>Simple Implementation:</strong> Single layer, straightforward CSS</li>
                <li>✅ <strong>Proper Layering:</strong> Z-index 2 (above SVG, below content)</li>
                <li>✅ <strong>Cover Background Size:</strong> Pattern scales to fill entire area</li>
                <li>✅ <strong>Centered Position:</strong> Pattern centered for optimal display</li>
            </ul>
        </div>
    </div>
    
    <div class="comparison">
        <div class="comparison-item">
            <h3>Before: Partial Coverage</h3>
            <div class="comparison-card">
                <div class="svg-bg"></div>
                <div class="before-pattern"></div>
                <div class="comparison-content">
                    Partial coverage<br>
                    From golden line only<br>
                    Limited visibility
                </div>
            </div>
        </div>
        
        <div class="comparison-item">
            <h3>After: Full Coverage</h3>
            <div class="comparison-card">
                <div class="svg-bg"></div>
                <div class="after-pattern"></div>
                <div class="comparison-content">
                    Full card coverage<br>
                    Clean implementation<br>
                    Clear visibility
                </div>
            </div>
        </div>
    </div>
    
    <p style="text-align: center; color: #666; margin-top: 20px;">
        The pattern now provides full card coverage with clean, simple implementation - no artistic effects, no animations, just clear visibility of the complete subcity pattern image.
    </p>
</body>
</html>
