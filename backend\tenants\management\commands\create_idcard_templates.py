from django.core.management.base import BaseCommand
from django_tenants.utils import schema_context
from tenants.models import Tenant
from idcards.models import IDCardTemplate


class Command(BaseCommand):
    help = 'Create default ID card templates for all tenant schemas'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force recreation of templates even if they already exist',
        )

    def handle(self, *args, **options):
        force = options.get('force', False)
        
        self.stdout.write(self.style.SUCCESS('🌱 Creating ID card templates for all tenants...'))
        
        # Get all tenants
        tenants = Tenant.objects.all()
        self.stdout.write(f'📋 Found {tenants.count()} tenants')
        
        success_count = 0
        error_count = 0
        
        for tenant in tenants:
            self.stdout.write(f'\n🏢 Processing tenant: {tenant.name} ({tenant.schema_name})')
            
            try:
                with schema_context(tenant.schema_name):
                    # Check if templates already exist
                    existing_count = IDCardTemplate.objects.count()
                    if existing_count > 0 and not force:
                        self.stdout.write(f'  ✅ Templates already exist ({existing_count} templates), skipping...')
                        continue
                    
                    if force and existing_count > 0:
                        self.stdout.write(f'  🗑️  Deleting {existing_count} existing templates...')
                        IDCardTemplate.objects.all().delete()
                    
                    # Create default template
                    default_template = IDCardTemplate.objects.create(
                        name="Default Template",
                        description="Standard ID card template with official government styling",
                        background_color="#FFFFFF",
                        text_color="#000000",
                        accent_color="#1976D2",
                        logo_position="top_left",
                        photo_position="left",
                        header_text="Federal Democratic Republic of Ethiopia",
                        subtitle_text="National ID Card",
                        footer_text="",
                        is_active=True,
                        is_default=True
                    )
                    self.stdout.write(f'  ✅ Created: {default_template.name}')
                    
                    # Create modern template (Alternative)
                    modern_template = IDCardTemplate.objects.create(
                        name="Modern Digital Template",
                        description="Modern digital ID card template with contemporary design",
                        background_color="#F8F9FA",
                        text_color="#212121",
                        accent_color="#2196F3",
                        logo_position="top_center",
                        photo_position="right",
                        header_text="Federal Democratic Republic of Ethiopia",
                        subtitle_text="Digital Identity Card",
                        footer_text="Digitally Verified",
                        is_active=True,
                        is_default=False
                    )
                    self.stdout.write(f'  ✅ Created: {modern_template.name}')
                    
                    # Create classic template
                    classic_template = IDCardTemplate.objects.create(
                        name="Classic Template",
                        description="Traditional ID card template with classic styling",
                        background_color="#FAFAFA",
                        text_color="#333333",
                        accent_color="#4CAF50",
                        logo_position="top_left",
                        photo_position="left",
                        header_text="Federal Democratic Republic of Ethiopia",
                        subtitle_text="Official ID Card",
                        footer_text="Government of Ethiopia",
                        is_active=True,
                        is_default=False
                    )
                    self.stdout.write(f'  ✅ Created: {classic_template.name}')
                    
                    total_created = IDCardTemplate.objects.count()
                    self.stdout.write(f'  🎉 Successfully created {total_created} templates for {tenant.name}')
                    success_count += 1
                    
            except Exception as e:
                self.stdout.write(f'  ❌ Error processing tenant {tenant.name}: {str(e)}')
                error_count += 1
                continue
        
        self.stdout.write(f'\n📊 Summary:')
        self.stdout.write(f'  ✅ Successfully processed: {success_count} tenants')
        if error_count > 0:
            self.stdout.write(f'  ❌ Failed processing: {error_count} tenants')
        
        # Print summary for each tenant
        self.stdout.write(f'\n📋 Template Summary:')
        for tenant in tenants:
            try:
                with schema_context(tenant.schema_name):
                    count = IDCardTemplate.objects.count()
                    default_template = IDCardTemplate.objects.filter(is_default=True).first()
                    self.stdout.write(f'  {tenant.name}: {count} templates (Default: {default_template.name if default_template else "None"})')
            except Exception as e:
                self.stdout.write(f'  {tenant.name}: Error - {str(e)}')
        
        self.stdout.write(self.style.SUCCESS('\n✅ Template creation completed!'))
