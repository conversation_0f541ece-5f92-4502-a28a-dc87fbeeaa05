# Generated by Django 4.2.7 on 2025-06-16 22:47

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('tenants', '0010_alter_biometric_left_thumb_fingerprint_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='TenantWorkflowConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('workflow_type', models.CharField(choices=[('centralized', 'Centralized (Standard hierarchy)'), ('autonomous', 'Autonomous (Self-sufficient)'), ('hybrid', 'Hybrid (Mixed approach)'), ('custom', 'Custom configuration')], default='centralized', help_text='Type of workflow configuration', max_length=20)),
                ('id_card_processing', models.J<PERSON><PERSON>ield(default=dict, help_text='ID card processing workflow configuration')),
                ('citizen_registration', models.J<PERSON><PERSON>ield(default=dict, help_text='Citizen registration workflow configuration')),
                ('approval_workflow', models.J<PERSON><PERSON>ield(default=dict, help_text='Approval workflow configuration')),
                ('document_verification', models.JSO<PERSON>ield(default=dict, help_text='Document verification workflow configuration')),
                ('custom_permissions', models.JSONField(default=dict, help_text='Custom permissions for this tenant')),
                ('settings', models.JSONField(default=dict, help_text='Additional workflow settings')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('tenant', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='workflow_config', to='tenants.tenant')),
            ],
            options={
                'verbose_name': 'Tenant Workflow Configuration',
                'verbose_name_plural': 'Tenant Workflow Configurations',
                'db_table': 'tenant_workflow_config',
            },
        ),
        migrations.CreateModel(
            name='TenantPermissionOverride',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('group_name', models.CharField(help_text='Name of the group to override permissions for', max_length=100)),
                ('additional_permissions', models.JSONField(default=list, help_text='Additional permissions to grant to this group')),
                ('removed_permissions', models.JSONField(default=list, help_text='Permissions to remove from this group')),
                ('reason', models.TextField(blank=True, help_text='Reason for permission override')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.CharField(blank=True, max_length=255)),
                ('tenant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='permission_overrides', to='tenants.tenant')),
            ],
            options={
                'verbose_name': 'Tenant Permission Override',
                'verbose_name_plural': 'Tenant Permission Overrides',
                'db_table': 'tenant_permission_override',
                'unique_together': {('tenant', 'group_name')},
            },
        ),
    ]
