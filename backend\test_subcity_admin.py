#!/usr/bin/env python
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append('/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from django_tenants.utils import schema_context
from users.serializers import UserSerializer

User = get_user_model()

print('=== Assigning subcity_admin <NAME_EMAIL> ===')

from django_tenants.utils import get_public_schema_name
from users.models_groups import TenantGroup

# Get the subcity_admin group from public schema
with schema_context(get_public_schema_name()):
    try:
        subcity_admin_group = TenantGroup.objects.get(group__name='subcity_admin')
        print(f'Found subcity_admin group: {subcity_admin_group.group.name} (ID: {subcity_admin_group.id})')
    except TenantGroup.DoesNotExist:
        print('subcity_admin group not found!')
        exit()

# Find and update barbara user
with schema_context('subcity_zoble'):
    try:
        user = User.objects.get(email='<EMAIL>')
        print(f'Found user: {user.email} (Role: {user.role})')

        # Add user to subcity_admin group
        user.add_to_group(subcity_admin_group, is_primary=True, reason='Manual assignment for testing')
        print(f'✅ Added {user.email} to {subcity_admin_group.group.name} group')

        # Verify assignment
        groups = user.get_user_groups()
        print(f'User now has {len(groups)} groups:')
        for group in groups:
            try:
                print(f'  - {group.group.name} (ID: {group.id})')
            except:
                print(f'  - Group ID {group.id} (name access error)')

        # Test permissions
        serializer = UserSerializer(user)
        data = serializer.data
        permissions = data.get('permissions', [])
        print(f'User now has {len(permissions)} permissions')
        if permissions:
            print('First 10 permissions:')
            for perm in permissions[:10]:
                print(f'  - {perm["codename"]}: {perm["name"]}')

    except User.DoesNotExist:
        print('<EMAIL> not found')
    except Exception as e:
        print(f'Error: {e}')
        import traceback
        traceback.print_exc()
