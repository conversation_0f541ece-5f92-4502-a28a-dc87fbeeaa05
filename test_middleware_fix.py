#!/usr/bin/env python3
"""
Test script to verify middleware fix and backend connectivity.
"""

import requests
import time

def test_middleware_fix():
    """Test if the middleware fix resolves the ERR_EMPTY_RESPONSE issue."""
    
    base_url = "http://************:8000"
    
    print("🧪 Testing Middleware Fix")
    print("=" * 30)
    
    # Step 1: Test basic connectivity
    print("📍 Step 1: Testing basic connectivity...")
    try:
        response = requests.get(f"{base_url}/admin/", timeout=10)
        print(f"Admin endpoint: {response.status_code}")
        if response.status_code in [200, 302]:
            print("✅ Backend is responding")
        else:
            print("❌ Backend not responding properly")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to backend: {e}")
        return False
    
    # Step 2: Test authentication
    print("\n📍 Step 2: Testing authentication...")
    try:
        auth_response = requests.post(
            f"{base_url}/api/auth/token/",
            json={"email": "<EMAIL>", "password": "admin123"},
            timeout=10
        )
        
        print(f"Auth status: {auth_response.status_code}")
        
        if auth_response.status_code == 200:
            auth_data = auth_response.json()
            access_token = auth_data.get('access')
            print("✅ Authentication successful")
        else:
            print(f"❌ Authentication failed: {auth_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return False
    
    # Step 3: Test tenant endpoint (the problematic one)
    print("\n📍 Step 3: Testing tenant endpoint...")
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    try:
        # Test the exact endpoint that was failing
        response = requests.get(f"{base_url}/api/tenants/", headers=headers, timeout=10)
        
        print(f"Tenant endpoint status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        print(f"Response size: {len(response.content)} bytes")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("✅ Tenant endpoint working!")
                
                if isinstance(data, list):
                    print(f"Tenant count: {len(data)}")
                elif isinstance(data, dict) and 'results' in data:
                    print(f"Paginated response - count: {data.get('count', 'unknown')}")
                    print(f"Results: {len(data.get('results', []))}")
                
                return True
                
            except Exception as e:
                print(f"❌ JSON parsing error: {e}")
                print(f"Raw response: {response.text[:200]}")
                return False
        else:
            print(f"❌ Tenant endpoint failed: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ ERR_EMPTY_RESPONSE - Backend not responding to tenant endpoint")
        return False
    except Exception as e:
        print(f"❌ Tenant endpoint error: {e}")
        return False
    
    # Step 4: Test other endpoints
    print("\n📍 Step 4: Testing other endpoints...")
    
    endpoints = [
        "/api/tenants/subcities/",
        "/api/tenants/kebeles/",
        "/api/shared/countries/"
    ]
    
    all_working = True
    for endpoint in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", headers=headers, timeout=10)
            print(f"{endpoint}: {response.status_code}")
            
            if response.status_code != 200:
                all_working = False
                
        except Exception as e:
            print(f"{endpoint}: ERROR - {e}")
            all_working = False
    
    return all_working

if __name__ == "__main__":
    print("🚀 Testing Middleware Fix for ERR_EMPTY_RESPONSE")
    print("=" * 50)
    
    success = test_middleware_fix()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ SUCCESS: Middleware fix resolved the issue!")
        print("")
        print("🎯 Next steps:")
        print("1. Test frontend: http://************:3000/tenants")
        print("2. Login with: <EMAIL> / admin123")
        print("3. Verify tenant list loads without ERR_EMPTY_RESPONSE")
    else:
        print("❌ FAILED: Issue still persists")
        print("")
        print("🔧 Additional troubleshooting needed:")
        print("1. Check backend logs: docker-compose logs backend")
        print("2. Restart backend: docker-compose restart backend")
        print("3. Run full diagnostic: ./fix_empty_response_issue.sh")
        print("4. Check for middleware crashes or database issues")
    
    print("")
    print("💡 If ERR_EMPTY_RESPONSE persists:")
    print("- Backend container might be crashing on specific requests")
    print("- Check Docker logs for Python exceptions")
    print("- Verify database connectivity")
    print("- Consider full restart: docker-compose down && docker-compose up -d")
