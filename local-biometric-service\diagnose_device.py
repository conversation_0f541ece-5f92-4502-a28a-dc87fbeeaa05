#!/usr/bin/env python3
"""
Comprehensive device diagnosis for Futronic FS88H
"""

import os
import sys
import ctypes
import logging
import subprocess

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_usb_devices():
    """Check if device appears in USB devices"""
    logger.info("=== USB Device Check ===")
    try:
        # Try to list USB devices using Windows commands
        result = subprocess.run(['wmic', 'path', 'Win32_USBHub', 'get', 'DeviceID,Description'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            output = result.stdout
            logger.info("USB Devices found:")
            for line in output.split('\n'):
                if line.strip() and 'Description' not in line:
                    logger.info(f"  {line.strip()}")
            
            # Look for Futronic-related devices
            if 'futronic' in output.lower() or 'fs88' in output.lower():
                logger.info("✅ Futronic device found in USB list!")
                return True
            else:
                logger.warning("❌ No Futronic device found in USB list")
                return False
        else:
            logger.warning("Could not list USB devices")
            return False
            
    except Exception as e:
        logger.error(f"USB check failed: {e}")
        return False

def check_device_manager():
    """Check device manager for Futronic device"""
    logger.info("\n=== Device Manager Check ===")
    try:
        result = subprocess.run(['wmic', 'path', 'Win32_PnPEntity', 'get', 'Name,Status'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            output = result.stdout
            futronic_found = False
            
            for line in output.split('\n'):
                if 'futronic' in line.lower() or 'fs88' in line.lower():
                    logger.info(f"✅ Found: {line.strip()}")
                    futronic_found = True
            
            if not futronic_found:
                logger.warning("❌ No Futronic device in Device Manager")
                logger.info("This could mean:")
                logger.info("  - Device not connected")
                logger.info("  - Driver not installed")
                logger.info("  - Device not powered")
            
            return futronic_found
        else:
            logger.warning("Could not check Device Manager")
            return False
            
    except Exception as e:
        logger.error(f"Device Manager check failed: {e}")
        return False

def check_dll_files():
    """Check if SDK DLL files exist and are accessible"""
    logger.info("\n=== SDK DLL Check ===")
    
    dll_files = ['ftrScanAPI.dll', 'ftrMathAPI.dll']
    all_found = True
    
    for dll in dll_files:
        if os.path.exists(dll):
            try:
                # Try to get file size
                size = os.path.getsize(dll)
                logger.info(f"✅ {dll} found ({size} bytes)")
                
                # Try to load the DLL
                try:
                    lib = ctypes.cdll.LoadLibrary(f'./{dll}')
                    logger.info(f"✅ {dll} loads successfully")
                except Exception as load_error:
                    logger.warning(f"⚠️ {dll} found but cannot load: {load_error}")
                    
            except Exception as e:
                logger.warning(f"⚠️ {dll} found but cannot access: {e}")
        else:
            logger.error(f"❌ {dll} not found")
            all_found = False
    
    return all_found

def test_sdk_connection():
    """Test SDK connection without device operations"""
    logger.info("\n=== SDK Connection Test ===")
    
    try:
        if not os.path.exists('./ftrScanAPI.dll'):
            logger.error("❌ ftrScanAPI.dll not found")
            return False
        
        # Load SDK
        scan_api = ctypes.cdll.LoadLibrary('./ftrScanAPI.dll')
        logger.info("✅ SDK loaded successfully")
        
        # Setup function signature
        scan_api.ftrScanOpenDevice.restype = ctypes.c_void_p
        logger.info("✅ Function signature set")
        
        # Try to open device
        device_handle = scan_api.ftrScanOpenDevice()
        logger.info(f"Device handle result: {device_handle}")
        
        if device_handle and device_handle != 0:
            logger.info("✅ Device opened successfully!")
            logger.info("This means:")
            logger.info("  - SDK can communicate with device")
            logger.info("  - Device is connected and responding")
            logger.info("  - Issue may be with LED power or visibility")
            
            # Close device
            scan_api.ftrScanCloseDevice.argtypes = [ctypes.c_void_p]
            scan_api.ftrScanCloseDevice(device_handle)
            logger.info("✅ Device closed properly")
            return True
        else:
            logger.error("❌ Device not found or cannot open")
            logger.info("This means:")
            logger.info("  - Device not connected")
            logger.info("  - Device in use by another application")
            logger.info("  - Driver issues")
            logger.info("  - Hardware problem")
            return False
            
    except Exception as e:
        logger.error(f"❌ SDK connection test failed: {e}")
        return False

def main():
    """Run comprehensive device diagnosis"""
    logger.info("🔍 FUTRONIC FS88H DEVICE DIAGNOSIS")
    logger.info("=" * 50)
    
    # Check 1: USB devices
    usb_ok = check_usb_devices()
    
    # Check 2: Device Manager
    dm_ok = check_device_manager()
    
    # Check 3: DLL files
    dll_ok = check_dll_files()
    
    # Check 4: SDK connection
    sdk_ok = test_sdk_connection()
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("🔍 DIAGNOSIS SUMMARY")
    logger.info("=" * 50)
    
    logger.info(f"USB Detection:     {'✅ PASS' if usb_ok else '❌ FAIL'}")
    logger.info(f"Device Manager:    {'✅ PASS' if dm_ok else '❌ FAIL'}")
    logger.info(f"SDK DLL Files:     {'✅ PASS' if dll_ok else '❌ FAIL'}")
    logger.info(f"SDK Connection:    {'✅ PASS' if sdk_ok else '❌ FAIL'}")
    
    logger.info("\n🔧 RECOMMENDATIONS:")
    
    if not usb_ok and not dm_ok:
        logger.info("❌ DEVICE NOT DETECTED:")
        logger.info("  1. Check USB cable connection")
        logger.info("  2. Try different USB port")
        logger.info("  3. Check if device needs external power")
        logger.info("  4. Install/reinstall Futronic drivers")
        
    elif usb_ok and dm_ok and not sdk_ok:
        logger.info("⚠️ DEVICE DETECTED BUT SDK CANNOT CONNECT:")
        logger.info("  1. Device may be in use by another application")
        logger.info("  2. Try restarting the computer")
        logger.info("  3. Check if Futronic software is running")
        
    elif sdk_ok and not usb_ok:
        logger.info("✅ SDK WORKS BUT USB DETECTION UNCLEAR:")
        logger.info("  1. Device is working properly")
        logger.info("  2. LED issue may be hardware-related")
        logger.info("  3. Some devices have very dim LEDs")
        logger.info("  4. Try testing in darker room")
        
    elif sdk_ok:
        logger.info("✅ DEVICE WORKING PROPERLY:")
        logger.info("  1. Device connection is good")
        logger.info("  2. SDK communication works")
        logger.info("  3. LED issue may be:")
        logger.info("     - LEDs very dim/hard to see")
        logger.info("     - LEDs on different side of device")
        logger.info("     - Device model without visible LEDs")
        logger.info("  4. Biometric service should work for finger detection")
        
    else:
        logger.info("❌ MULTIPLE ISSUES DETECTED:")
        logger.info("  1. Check all physical connections")
        logger.info("  2. Reinstall drivers")
        logger.info("  3. Try different computer to test device")

if __name__ == '__main__':
    main()
