"""
Management command to configure tenant workflows.
Allows setting up different workflow types for different organizational needs.
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from tenants.models import Tenant
from tenants.models_workflow import TenantWorkflowConfig, TenantPermissionOverride, WORKFLOW_TEMPLATES
from users.models_groups import TenantGroup, GroupMembership
from django.contrib.auth.models import Group, Permission
from django.contrib.auth import get_user_model

User = get_user_model()


class Command(BaseCommand):
    help = 'Configure tenant workflow settings'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant',
            type=str,
            required=True,
            help='Tenant name or schema name',
        )
        parser.add_argument(
            '--workflow-type',
            type=str,
            choices=['autonomous', 'centralized', 'hybrid', 'custom'],
            required=True,
            help='Type of workflow to configure',
        )
        parser.add_argument(
            '--template',
            type=str,
            choices=list(WORKFLOW_TEMPLATES.keys()),
            help='Workflow template to use',
        )
        parser.add_argument(
            '--enable-local-printing',
            action='store_true',
            help='Enable local ID card printing for this tenant',
        )
        parser.add_argument(
            '--printing-authority',
            type=str,
            choices=['kebele', 'subcity', 'city'],
            default='subcity',
            help='Authority level for ID card printing',
        )
        parser.add_argument(
            '--approval-levels',
            type=str,
            nargs='+',
            default=['kebele_leader'],
            help='Required approval levels',
        )
        parser.add_argument(
            '--create-custom-groups',
            action='store_true',
            help='Create custom groups with additional permissions',
        )

    def handle(self, *args, **options):
        tenant_name = options['tenant']
        workflow_type = options['workflow_type']
        template = options.get('template')
        enable_local_printing = options['enable_local_printing']
        printing_authority = options['printing_authority']
        approval_levels = options['approval_levels']
        create_custom_groups = options['create_custom_groups']

        self.stdout.write(
            self.style.SUCCESS(f'🔧 Configuring workflow for tenant: {tenant_name}')
        )

        try:
            # Get the tenant
            tenant = self.get_tenant(tenant_name)
            
            with transaction.atomic():
                # Configure workflow
                config = self.configure_workflow(
                    tenant, workflow_type, template, enable_local_printing,
                    printing_authority, approval_levels
                )
                
                # Create custom groups if requested
                if create_custom_groups:
                    self.create_custom_groups(tenant, workflow_type, template)
                
                self.stdout.write(
                    self.style.SUCCESS(f'✅ Successfully configured {workflow_type} workflow for {tenant.name}')
                )
                self.print_configuration_summary(config)

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Failed to configure workflow: {e}')
            )

    def get_tenant(self, tenant_identifier):
        """Get tenant by name or schema name."""
        try:
            # Try by name first
            return Tenant.objects.get(name=tenant_identifier)
        except Tenant.DoesNotExist:
            try:
                # Try by schema name
                return Tenant.objects.get(schema_name=tenant_identifier)
            except Tenant.DoesNotExist:
                raise Exception(f"Tenant '{tenant_identifier}' not found")

    def configure_workflow(self, tenant, workflow_type, template, enable_local_printing,
                          printing_authority, approval_levels):
        """Configure workflow for the tenant."""
        
        # Create or update workflow configuration
        config, created = TenantWorkflowConfig.objects.get_or_create(
            tenant=tenant,
            defaults={'workflow_type': workflow_type}
        )
        
        if not created:
            config.workflow_type = workflow_type
        
        # Apply template if specified
        if template and template in WORKFLOW_TEMPLATES:
            template_config = WORKFLOW_TEMPLATES[template]
            config.workflow_type = template_config['workflow_type']
            
            # Set configuration based on template
            if workflow_type == 'autonomous' or template == 'autonomous_kebele':
                config.id_card_processing = {
                    'can_print_locally': True,
                    'requires_higher_approval': False,
                    'approval_levels': ['kebele_leader'],
                    'printing_authority': 'kebele',
                    'quality_control': 'local'
                }
            elif workflow_type == 'centralized' or template == 'centralized_hierarchy':
                config.id_card_processing = {
                    'can_print_locally': False,
                    'requires_higher_approval': True,
                    'approval_levels': ['kebele_leader', 'subcity_admin'],
                    'printing_authority': 'subcity',
                    'quality_control': 'centralized'
                }
            elif workflow_type == 'hybrid' or template == 'hybrid_workflow':
                config.id_card_processing = {
                    'can_print_locally': True,
                    'requires_higher_approval': True,
                    'approval_levels': ['kebele_leader'],
                    'printing_authority': 'kebele',
                    'quality_control': 'hybrid'
                }
        else:
            # Custom configuration
            config.id_card_processing = {
                'can_print_locally': enable_local_printing,
                'requires_higher_approval': not enable_local_printing,
                'approval_levels': approval_levels,
                'printing_authority': printing_authority,
                'quality_control': 'custom'
            }
        
        # Set other workflow configurations
        config.citizen_registration = {
            'full_workflow_local': True,
            'requires_external_verification': workflow_type == 'centralized',
            'biometric_processing': 'local',
            'document_verification': 'local' if workflow_type != 'centralized' else 'hierarchical'
        }
        
        config.approval_workflow = {
            'final_approval_level': approval_levels[-1] if approval_levels else 'kebele_leader',
            'escalation_required': workflow_type == 'centralized',
            'auto_approve_threshold': 95 if workflow_type == 'hybrid' else None
        }
        
        config.save()
        
        action = 'Created' if created else 'Updated'
        self.stdout.write(f'   ✅ {action} workflow configuration')
        
        return config

    def create_custom_groups(self, tenant, workflow_type, template):
        """Create custom groups with workflow-specific permissions."""
        
        if template and template in WORKFLOW_TEMPLATES:
            template_config = WORKFLOW_TEMPLATES[template]
            permissions_config = template_config.get('permissions', {})
            
            for group_name, permissions in permissions_config.items():
                self.create_or_update_group(tenant, group_name, permissions, workflow_type)
        
        # Create workflow-specific groups
        if workflow_type == 'autonomous':
            # Create autonomous clerk with printing permissions
            self.create_or_update_group(
                tenant, 'autonomous_clerk',
                [
                    'register_citizens', 'view_citizens_list', 'view_citizen_details',
                    'generate_id_cards', 'view_id_cards_list', 'print_id_cards',
                    'view_kebele_dashboard'
                ],
                workflow_type
            )
            
            # Create autonomous leader with full local authority
            self.create_or_update_group(
                tenant, 'autonomous_leader',
                [
                    'view_citizens_list', 'view_citizen_details', 'view_id_cards_list',
                    'approve_id_cards', 'print_id_cards', 'verify_documents',
                    'view_kebele_dashboard', 'view_kebele_reports'
                ],
                workflow_type
            )

    def create_or_update_group(self, tenant, group_name, permissions, workflow_type):
        """Create or update a custom group with specific permissions."""
        
        # Create tenant-specific group name
        tenant_group_name = f"{tenant.schema_name}_{group_name}"
        
        # Create Django Group
        django_group, created = Group.objects.get_or_create(name=tenant_group_name)
        
        # Get permissions
        permission_objects = []
        for perm_codename in permissions:
            try:
                permission = Permission.objects.get(codename=perm_codename)
                permission_objects.append(permission)
            except Permission.DoesNotExist:
                self.stdout.write(
                    self.style.WARNING(f'   ⚠️ Permission not found: {perm_codename}')
                )
        
        # Assign permissions
        django_group.permissions.set(permission_objects)
        
        # Create TenantGroup
        tenant_group, created = TenantGroup.objects.get_or_create(
            group=django_group,
            defaults={
                'description': f'{workflow_type.title()} workflow group for {tenant.name}',
                'is_system_group': False,
                'group_type': 'custom',
                'level': 25,
            }
        )
        
        # Create permission override
        override, created = TenantPermissionOverride.objects.get_or_create(
            tenant=tenant,
            group_name=group_name,
            defaults={
                'additional_permissions': permissions,
                'reason': f'{workflow_type.title()} workflow configuration'
            }
        )
        
        if not created:
            override.additional_permissions = permissions
            override.save()
        
        action = 'Created' if created else 'Updated'
        self.stdout.write(f'   ✅ {action} custom group: {tenant_group_name} ({len(permission_objects)} permissions)')

    def print_configuration_summary(self, config):
        """Print a summary of the configuration."""
        self.stdout.write('\n📋 CONFIGURATION SUMMARY:')
        self.stdout.write(f'   Workflow Type: {config.workflow_type}')
        self.stdout.write(f'   Local Printing: {config.can_print_id_cards_locally()}')
        self.stdout.write(f'   Printing Authority: {config.get_printing_authority()}')
        self.stdout.write(f'   Approval Levels: {", ".join(config.get_approval_levels())}')
        self.stdout.write(f'   Higher Approval Required: {config.requires_higher_approval()}')


# Example usage commands
EXAMPLE_COMMANDS = """
# Configure autonomous kebele (self-sufficient)
python manage.py configure_tenant_workflow \\
    --tenant "Bole 01" \\
    --workflow-type autonomous \\
    --template autonomous_kebele \\
    --enable-local-printing \\
    --printing-authority kebele \\
    --approval-levels kebele_leader \\
    --create-custom-groups

# Configure centralized workflow (standard hierarchy)
python manage.py configure_tenant_workflow \\
    --tenant "kebele_bole_02" \\
    --workflow-type centralized \\
    --template centralized_hierarchy \\
    --printing-authority subcity \\
    --approval-levels kebele_leader subcity_admin

# Configure hybrid workflow (local printing with oversight)
python manage.py configure_tenant_workflow \\
    --tenant "kebele_addis_ketema_01" \\
    --workflow-type hybrid \\
    --template hybrid_workflow \\
    --enable-local-printing \\
    --printing-authority kebele \\
    --approval-levels kebele_leader \\
    --create-custom-groups

# Configure custom workflow
python manage.py configure_tenant_workflow \\
    --tenant "special_kebele" \\
    --workflow-type custom \\
    --enable-local-printing \\
    --printing-authority kebele \\
    --approval-levels clerk kebele_leader \\
    --create-custom-groups
"""
