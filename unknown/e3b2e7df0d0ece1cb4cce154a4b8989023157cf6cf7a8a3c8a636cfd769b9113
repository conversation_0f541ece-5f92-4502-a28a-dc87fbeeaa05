"""
Tenant workflow configuration models.
Allows tenants to customize their operational workflows.
"""

from django.db import models
from .models import Tenant


class TenantWorkflowConfig(models.Model):
    """
    Configuration for tenant-specific workflows.
    Allows customization of operational processes.
    """
    
    WORKFLOW_TYPES = [
        ('centralized', 'Centralized (Standard hierarchy)'),
        ('autonomous', 'Autonomous (Self-sufficient)'),
        ('hybrid', 'Hybrid (Mixed approach)'),
        ('custom', 'Custom configuration'),
    ]
    
    tenant = models.OneToOneField(
        Tenant,
        on_delete=models.CASCADE,
        related_name='workflow_config'
    )
    
    workflow_type = models.CharField(
        max_length=20,
        choices=WORKFLOW_TYPES,
        default='centralized',
        help_text='Type of workflow configuration'
    )
    
    # ID Card Processing Configuration
    id_card_processing = models.JSONField(
        default=dict,
        help_text='ID card processing workflow configuration'
    )

    # Citizen Registration Configuration
    citizen_registration = models.JSONField(
        default=dict,
        help_text='Citizen registration workflow configuration'
    )

    # Approval Workflow Configuration
    approval_workflow = models.JSONField(
        default=dict,
        help_text='Approval workflow configuration'
    )

    # Document Verification Configuration
    document_verification = models.JSO<PERSON>ield(
        default=dict,
        help_text='Document verification workflow configuration'
    )

    # Custom Permissions Configuration
    custom_permissions = models.JSONField(
        default=dict,
        help_text='Custom permissions for this tenant'
    )

    # Workflow Settings
    settings = models.JSONField(
        default=dict,
        help_text='Additional workflow settings'
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'tenant_workflow_config'
        verbose_name = 'Tenant Workflow Configuration'
        verbose_name_plural = 'Tenant Workflow Configurations'
    
    def __str__(self):
        return f"{self.tenant.name} - {self.get_workflow_type_display()}"
    
    def get_default_config(self):
        """Get default configuration based on tenant type and workflow type."""
        if self.workflow_type == 'autonomous':
            return self.get_autonomous_config()
        elif self.workflow_type == 'centralized':
            return self.get_centralized_config()
        elif self.workflow_type == 'hybrid':
            return self.get_hybrid_config()
        else:
            return self.get_custom_config()
    
    def get_autonomous_config(self):
        """Configuration for autonomous (self-sufficient) kebeles."""
        return {
            'id_card_processing': {
                'can_print_locally': True,
                'requires_higher_approval': False,
                'approval_levels': ['kebele_leader'],
                'printing_authority': 'kebele',
                'quality_control': 'local'
            },
            'citizen_registration': {
                'full_workflow_local': True,
                'requires_external_verification': False,
                'biometric_processing': 'local',
                'document_verification': 'local'
            },
            'approval_workflow': {
                'final_approval_level': 'kebele_leader',
                'escalation_required': False,
                'auto_approve_threshold': None
            },
            'document_verification': {
                'verification_authority': 'kebele',
                'external_verification_required': False,
                'verification_levels': ['clerk', 'kebele_leader']
            }
        }
    
    def get_centralized_config(self):
        """Configuration for centralized (standard hierarchy) workflow."""
        return {
            'id_card_processing': {
                'can_print_locally': False,
                'requires_higher_approval': True,
                'approval_levels': ['kebele_leader', 'subcity_admin'],
                'printing_authority': 'subcity',
                'quality_control': 'centralized'
            },
            'citizen_registration': {
                'full_workflow_local': True,
                'requires_external_verification': True,
                'biometric_processing': 'local',
                'document_verification': 'hierarchical'
            },
            'approval_workflow': {
                'final_approval_level': 'subcity_admin',
                'escalation_required': True,
                'auto_approve_threshold': None
            },
            'document_verification': {
                'verification_authority': 'hierarchical',
                'external_verification_required': True,
                'verification_levels': ['kebele_leader', 'subcity_admin']
            }
        }
    
    def get_hybrid_config(self):
        """Configuration for hybrid workflow (mixed approach)."""
        return {
            'id_card_processing': {
                'can_print_locally': True,
                'requires_higher_approval': True,
                'approval_levels': ['kebele_leader'],
                'printing_authority': 'kebele',
                'quality_control': 'hybrid'
            },
            'citizen_registration': {
                'full_workflow_local': True,
                'requires_external_verification': False,
                'biometric_processing': 'local',
                'document_verification': 'local'
            },
            'approval_workflow': {
                'final_approval_level': 'kebele_leader',
                'escalation_required': False,
                'auto_approve_threshold': 95  # Auto-approve high-quality applications
            },
            'document_verification': {
                'verification_authority': 'kebele',
                'external_verification_required': False,
                'verification_levels': ['kebele_leader']
            }
        }
    
    def get_custom_config(self):
        """Configuration for custom workflow."""
        return {
            'id_card_processing': self.id_card_processing,
            'citizen_registration': self.citizen_registration,
            'approval_workflow': self.approval_workflow,
            'document_verification': self.document_verification
        }
    
    def can_print_id_cards_locally(self):
        """Check if this tenant can print ID cards locally."""
        config = self.get_default_config()
        return config.get('id_card_processing', {}).get('can_print_locally', False)
    
    def get_printing_authority(self):
        """Get the authority level for ID card printing."""
        config = self.get_default_config()
        return config.get('id_card_processing', {}).get('printing_authority', 'subcity')
    
    def get_approval_levels(self):
        """Get required approval levels for this tenant."""
        config = self.get_default_config()
        return config.get('approval_workflow', {}).get('approval_levels', ['kebele_leader'])
    
    def requires_higher_approval(self):
        """Check if this tenant requires higher-level approval."""
        config = self.get_default_config()
        return config.get('id_card_processing', {}).get('requires_higher_approval', True)


class TenantPermissionOverride(models.Model):
    """
    Allows tenants to override default permissions for specific groups.
    """
    
    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='permission_overrides'
    )
    
    group_name = models.CharField(
        max_length=100,
        help_text='Name of the group to override permissions for'
    )
    
    additional_permissions = models.JSONField(
        default=list,
        help_text='Additional permissions to grant to this group'
    )

    removed_permissions = models.JSONField(
        default=list,
        help_text='Permissions to remove from this group'
    )
    
    reason = models.TextField(
        blank=True,
        help_text='Reason for permission override'
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.CharField(max_length=255, blank=True)
    
    class Meta:
        db_table = 'tenant_permission_override'
        unique_together = ['tenant', 'group_name']
        verbose_name = 'Tenant Permission Override'
        verbose_name_plural = 'Tenant Permission Overrides'
    
    def __str__(self):
        return f"{self.tenant.name} - {self.group_name} override"


# Predefined workflow templates
WORKFLOW_TEMPLATES = {
    'autonomous_kebele': {
        'name': 'Autonomous Kebele',
        'description': 'Self-sufficient kebele handling all processes locally',
        'workflow_type': 'autonomous',
        'suitable_for': ['kebele'],
        'permissions': {
            'clerk': [
                'register_citizens',
                'view_citizens_list',
                'view_citizen_details',
                'generate_id_cards',
                'view_id_cards_list',
                'print_id_cards',  # Additional permission
                'view_kebele_dashboard'
            ],
            'kebele_leader': [
                'view_citizens_list',
                'view_citizen_details',
                'view_id_cards_list',
                'approve_id_cards',
                'print_id_cards',  # Additional permission
                'verify_documents',
                'view_kebele_dashboard',
                'view_kebele_reports'
            ]
        }
    },
    'centralized_hierarchy': {
        'name': 'Centralized Hierarchy',
        'description': 'Standard hierarchical workflow with centralized printing',
        'workflow_type': 'centralized',
        'suitable_for': ['kebele', 'subcity', 'city'],
        'permissions': {
            'clerk': [
                'register_citizens',
                'view_citizens_list',
                'view_citizen_details',
                'generate_id_cards',
                'view_id_cards_list',
                'send_id_cards_for_approval',
                'view_kebele_dashboard'
            ],
            'kebele_leader': [
                'view_citizens_list',
                'view_citizen_details',
                'view_id_cards_list',
                'approve_id_cards',
                'send_id_cards_to_higher_level',
                'verify_documents',
                'view_kebele_dashboard',
                'view_kebele_reports'
            ],
            'subcity_admin': [
                'create_kebele_users',
                'view_citizens_list',
                'view_id_cards_list',
                'approve_id_cards',
                'print_id_cards',  # Printing authority
                'view_child_kebeles_data',
                'view_subcity_dashboard',
                'view_subcity_reports'
            ]
        }
    },
    'hybrid_workflow': {
        'name': 'Hybrid Workflow',
        'description': 'Mixed approach with local printing but hierarchical oversight',
        'workflow_type': 'hybrid',
        'suitable_for': ['kebele'],
        'permissions': {
            'clerk': [
                'register_citizens',
                'view_citizens_list',
                'view_citizen_details',
                'generate_id_cards',
                'view_id_cards_list',
                'view_kebele_dashboard'
            ],
            'kebele_leader': [
                'view_citizens_list',
                'view_citizen_details',
                'view_id_cards_list',
                'approve_id_cards',
                'print_id_cards',  # Local printing after approval
                'verify_documents',
                'view_kebele_dashboard',
                'view_kebele_reports'
            ]
        }
    }
}
