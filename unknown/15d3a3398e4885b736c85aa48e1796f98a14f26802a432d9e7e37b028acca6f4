import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
  Paper,
  Chip,
  Divider
} from '@mui/material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  LineChart,
  Line,
  AreaChart,
  Area,
  ResponsiveContainer
} from 'recharts';
import {
  People as PeopleIcon,
  PersonAdd as PersonAddIcon,
  Badge as BadgeIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  SwapHoriz as MigrationIcon,
  LocationCity as SubcityIcon,
  LocationOn as LocationIcon,
  Timeline as TimelineIcon,
  Assessment as AssessmentIcon,
  Business as CityIcon,
  Dashboard as DashboardIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useLocalization } from '../../contexts/LocalizationContext';
import axios from '../../utils/axios';
import '../../styles/dashboard.css';
import { useTheme as useAppTheme } from '../../contexts/ThemeContext';

// Chart colors - diverse palette for better data visualization
const COLORS = [
  '#3B82F6', '#10B981', '#F59E0B', '#EF4444',
  '#8B5CF6', '#06B6D4', '#84CC16', '#F97316',
  '#EC4899', '#6366F1', '#14B8A6', '#F43F5E'
];

const CityDashboard = () => {
  const { user } = useAuth();
  const { t } = useLocalization();
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { tenantColors } = useAppTheme();

  // Dynamic color definitions based on theme
  const PRIMARY_COLOR = tenantColors.primary;
  const SECONDARY_COLOR = tenantColors.secondary;

  const GRADIENTS = {
    primary: `linear-gradient(135deg, ${PRIMARY_COLOR} 0%, ${SECONDARY_COLOR} 100%)`,
    success: 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)',
    warning: `linear-gradient(135deg, ${PRIMARY_COLOR} 0%, #f57c00 100%)`,
    error: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
    info: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
  };

  useEffect(() => {
    // Only fetch data if user is loaded
    if (user) {
      fetchDashboardData();
    }
  }, [user]);

  const fetchDashboardData = async () => {
    try {
      console.log('🚨 CityDashboard: Starting to fetch dashboard data...');
      setLoading(true);

      // Try multiple ways to get tenant ID
      let tenantId = user?.tenant_id;
      
      // If still no tenant ID, extract from JWT token directly
      if (!tenantId) {
        try {
          const token = localStorage.getItem('accessToken');
          if (token) {
            const payload = JSON.parse(atob(token.split('.')[1]));
            tenantId = payload.tenant_id;
            console.log('🚨 CityDashboard: Extracted tenant_id from JWT:', tenantId);
            console.log('🚨 CityDashboard: Full JWT payload:', payload);
          }
        } catch (error) {
          console.error('🚨 CityDashboard: Error extracting tenant_id from JWT:', error);
        }
      }

      console.log('🚨 CityDashboard: Final tenant ID sources:');
      console.log('  - user?.tenant_id:', user?.tenant_id);
      console.log('  - Final tenantId:', tenantId);

      if (!tenantId) {
        throw new Error('No tenant ID available');
      }

      console.log('🚨 CityDashboard: Using tenant ID:', tenantId);
      console.log('🚨 CityDashboard: Calling city dashboard endpoint...');
      const response = await axios.get(`/api/tenants/${tenantId}/citizens/city-dashboard/reports/`);
      console.log('🚨 CityDashboard: Response received:', response.data);
      setDashboardData(response.data);
      setError(null);
    } catch (error) {
      console.error('🚨 CityDashboard: Error fetching dashboard data:', error);
      console.error('🚨 CityDashboard: Error response:', error.response);
      setError(`Failed to load dashboard data: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Show loading if user is not loaded yet or if we're fetching data
  if (!user || loading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 text-lg">
            {!user ? 'Loading user data...' : 'Loading city dashboard data...'}
          </p>
          {/* Debug info */}
          <p className="text-gray-500 text-sm mt-2">
            User: {user ? 'Loaded' : 'Not loaded'}, Loading: {loading ? 'Yes' : 'No'}
          </p>
          {user && (
            <p className="text-gray-500 text-sm">
              Tenant: {user.tenant_name} (ID: {user.tenant_id}), Type: {user.tenant_type}
            </p>
          )}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="m-4 p-4 bg-red-50 border border-red-200 rounded-lg">
        <div className="flex items-center">
          <ErrorIcon className="text-red-500 mr-2" />
          <p className="text-red-700">{error}</p>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="m-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-center">
          <BadgeIcon className="text-blue-500 mr-2" />
          <p className="text-blue-700">No dashboard data available</p>
        </div>
      </div>
    );
  }

  // Enhanced Big Number Card Component with Tailwind CSS and Animations
  const BigNumberCard = ({ title, value, icon, color = 'primary', trend = null }) => {
    const colorClasses = {
      primary: `bg-gradient-to-br from-[${PRIMARY_COLOR}] to-[${SECONDARY_COLOR}] hover:from-[#e65100] hover:to-[#d84315]`,
      success: 'bg-gradient-to-br from-green-500 to-green-700 hover:from-green-600 hover:to-green-800',
      warning: `bg-gradient-to-br from-[${PRIMARY_COLOR}] to-yellow-600 hover:from-[#e65100] hover:to-yellow-700`,
      error: 'bg-gradient-to-br from-red-500 to-red-700 hover:from-red-600 hover:to-red-800',
      info: 'bg-gradient-to-br from-blue-500 to-blue-700 hover:from-blue-600 hover:to-blue-800',
    };

    return (
      <div className={`
        relative overflow-hidden rounded-2xl p-6 text-white shadow-lg transition-all duration-500 ease-in-out
        transform hover:-translate-y-2 hover:shadow-2xl cursor-pointer group animate-scale-in
        ${colorClasses[color] || colorClasses.primary}
      `}
      style={{
        background: color === 'primary' ? `linear-gradient(135deg, ${PRIMARY_COLOR} 0%, ${SECONDARY_COLOR} 100%)` :
                   color === 'warning' ? `linear-gradient(135deg, ${PRIMARY_COLOR} 0%, #f57c00 100%)` : undefined
      }}>
        {/* Animated Background Pattern */}
        <div className="absolute inset-0 bg-white opacity-5 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1)_1px,transparent_1px)] bg-[length:20px_20px] animate-pulse"></div>

        {/* Floating Animation Elements */}
        <div className="absolute top-2 right-2 w-4 h-4 bg-white/20 rounded-full animate-bounce" style={{ animationDelay: '0.5s' }}></div>
        <div className="absolute bottom-4 left-4 w-2 h-2 bg-white/30 rounded-full animate-ping" style={{ animationDelay: '1s' }}></div>

        <div className="relative flex items-center justify-between">
          <div className="flex-1">
            <h3 className="text-sm font-medium text-white/90 mb-2 tracking-wide animate-fade-in">
              {title}
            </h3>
            <p className="text-3xl font-bold text-white mb-1 group-hover:scale-110 transition-transform duration-300 animate-slide-up">
              {value?.toLocaleString() || 0}
            </p>
            {trend && (
              <div className="flex items-center mt-2 text-white/80 animate-fade-in" style={{ animationDelay: '0.3s' }}>
                <TrendingUpIcon className="w-4 h-4 mr-1 animate-bounce" />
                <span className="text-sm">{trend}</span>
              </div>
            )}
          </div>
          <div className="text-white/70 text-5xl group-hover:text-white/90 group-hover:rotate-12 transition-all duration-300 animate-scale-in" style={{ animationDelay: '0.2s' }}>
            {icon}
          </div>
        </div>

        {/* Enhanced Hover Effect */}
        <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-15 transition-opacity duration-500"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
      </div>
    );
  };

  // Enhanced Chart Card Component with Tailwind CSS and Animations
  const ChartCard = ({ title, children, height = 400, icon = null, gradient = null }) => (
    <div className="bg-white rounded-2xl shadow-soft hover:shadow-large transition-all duration-700 ease-in-out transform hover:-translate-y-3 hover:scale-[1.02] border border-gray-100 overflow-hidden group animate-fade-in">
      {/* Header with gradient background */}
      <div
        className={`p-6 pb-4 border-b border-gray-100 relative overflow-hidden`}
        style={{
          background: gradient ? `linear-gradient(to right, ${PRIMARY_COLOR}10, ${SECONDARY_COLOR}10)` : '#f9fafb'
        }}
      >
        {/* Animated background elements */}
        <div
          className="absolute top-0 right-0 w-20 h-20 rounded-full -translate-y-10 translate-x-10 group-hover:scale-150 transition-transform duration-1000"
          style={{
            background: `linear-gradient(to bottom right, ${PRIMARY_COLOR}20, ${SECONDARY_COLOR}20)`
          }}
        ></div>

        <div className="flex items-center justify-between relative z-10">
          <div className="flex items-center">
            {icon && (
              <div className="mr-3 p-2 rounded-lg group-hover:scale-110 transition-all duration-300 animate-bounce"
                   style={{
                     backgroundColor: `${PRIMARY_COLOR}20`,
                     color: PRIMARY_COLOR,
                     animationDelay: '0.2s'
                   }}>
                {icon}
              </div>
            )}
            <h3 className="text-lg font-bold mb-0 transition-colors duration-300 animate-slide-up"
                style={{ color: PRIMARY_COLOR }}>
              {title}
            </h3>
          </div>
          <div className="flex space-x-1">
            <div className="w-2 h-2 rounded-full animate-pulse" style={{ backgroundColor: PRIMARY_COLOR }}></div>
            <div className="w-2 h-2 rounded-full animate-pulse" style={{ backgroundColor: SECONDARY_COLOR, animationDelay: '0.5s' }}></div>
          </div>
        </div>
      </div>

      {/* Chart content */}
      <div className="p-6 pt-4 relative">
        {/* Animated background pattern */}
        <div className="absolute inset-0 opacity-5 bg-[radial-gradient(circle_at_50%_50%,rgba(255,143,0,0.1)_1px,transparent_1px)] bg-[length:20px_20px] animate-pulse"></div>

        <div
          style={{ height: `${height}px` }}
          className="w-full relative"
        >
          {children}
        </div>
      </div>
    </div>
  );

  return (
    <div className="flex-1 min-h-screen" style={{ backgroundColor: '#fef7ed' }}>
      {/* Enhanced Header with city-specific gradient and animations */}
      <div className="mb-8 p-6 rounded-2xl text-white shadow-large animate-fade-in relative overflow-hidden"
           style={{ background: `linear-gradient(135deg, ${PRIMARY_COLOR} 0%, ${SECONDARY_COLOR} 50%, #e65100 100%)` }}>

        {/* Animated background elements */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-white opacity-10 rounded-full -translate-y-16 translate-x-16 animate-pulse"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-white opacity-5 rounded-full translate-y-12 -translate-x-12 animate-bounce" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 left-1/2 w-40 h-40 bg-white opacity-5 rounded-full -translate-x-1/2 -translate-y-1/2 animate-spin" style={{ animationDuration: '20s' }}></div>

        <div className="relative z-10">
          <div className="flex items-center mb-2">
            <CityIcon className="mr-3 text-4xl animate-bounce" />
            <h1 className="text-3xl font-bold animate-slide-up">
              {dashboardData.tenant_info?.name} {t('city_dashboard', 'City Dashboard')}
            </h1>
          </div>
          <p className="text-white/80 text-lg animate-fade-in" style={{ animationDelay: '0.3s' }}>
            {t('city_wide_strategic_insights', 'City-wide strategic insights across')} {dashboardData.tenant_info?.child_subcities_count} {t('sub_cities', 'sub-cities')} {t('and', 'and')} {dashboardData.tenant_info?.total_kebeles_count} {t('kebeles', 'kebeles')}
          </p>
          <p className="text-white/80 text-sm animate-fade-in" style={{ animationDelay: '0.5s' }}>
            {t('last_updated', 'Last updated')}: {new Date(dashboardData.last_updated).toLocaleString()}
          </p>

          {/* Status indicator */}
          <div className="mt-4 flex items-center animate-slide-up" style={{ animationDelay: '0.7s' }}>
            <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse mr-2"></div>
            <span className="text-white/80 text-sm">{t('live_city_wide_data', 'Live City-wide Data')}</span>
          </div>
        </div>
      </div>

      <div className="space-y-8">
        {/* Row 1: Big Number Cards - 3 Columns */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 animate-slide-up">
          <BigNumberCard
            title={t('total_registered_citizens_18_plus', 'Total Registered Citizens (18+)')}
            value={dashboardData.total_citizens_18_plus}
            icon={<PeopleIcon className="w-12 h-12" />}
            color="primary"
          />

          <BigNumberCard
            title={t('new_registrations', 'New This Month')}
            value={dashboardData.new_registrations_this_month || 0}
            icon={<PersonAddIcon className="w-12 h-12" />}
            color="success"
          />

          <BigNumberCard
            title={t('sub_cities', 'Sub-Cities')}
            value={dashboardData.tenant_info?.child_subcities_count || 0}
            icon={<SubcityIcon className="w-12 h-12" />}
            color="info"
          />
        </div>

        {/* Row 1.5: Additional Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 animate-slide-up" style={{ animationDelay: '0.1s' }}>
          <BigNumberCard
            title={t('expiring_ids', 'Expiring IDs')}
            value={dashboardData.expiring_ids_next_30_days || 0}
            icon={<WarningIcon className="w-12 h-12" />}
            color="warning"
          />

          <BigNumberCard
            title={t('expired_ids', 'Expired IDs')}
            value={dashboardData.expired_ids_over_30_days || 0}
            icon={<ErrorIcon className="w-12 h-12" />}
            color="error"
          />

          <BigNumberCard
            title={t('migration_balance', 'Migration Balance')}
            value={dashboardData.migration_data?.net_migration || 0}
            icon={<TrendingUpIcon className="w-12 h-12" />}
            color={dashboardData.migration_data?.net_migration >= 0 ? "success" : "error"}
          />
        </div>

        {/* Row 2: Monthly Registration Trends */}
        <div className="animate-slide-up" style={{ animationDelay: '0.2s' }}>
          <ChartCard
            title={t('new_registrations_monthly_trends', 'New Registrations (Monthly Trends)')}
            height={400}
            icon={<TimelineIcon />}
            gradient={true}
          >
            <ResponsiveContainer width="100%" height={340}>
              <LineChart
                data={dashboardData.monthly_trends || []}
                margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
              >
                <defs>
                  <linearGradient id="colorRegistrations" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.3}/>
                    <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.05}/>
                  </linearGradient>
                  <linearGradient id="colorRenewals" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#10b981" stopOpacity={0.3}/>
                    <stop offset="95%" stopColor="#10b981" stopOpacity={0.05}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" strokeOpacity={0.5} />
                <XAxis
                  dataKey="month"
                  stroke="#64748b"
                  fontSize={12}
                  tick={{ fill: '#64748b' }}
                  axisLine={{ stroke: '#e2e8f0' }}
                />
                <YAxis
                  stroke="#64748b"
                  fontSize={12}
                  tick={{ fill: '#64748b' }}
                  axisLine={{ stroke: '#e2e8f0' }}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    border: 'none',
                    borderRadius: '12px',
                    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                    backdropFilter: 'blur(10px)'
                  }}
                  labelStyle={{ color: '#374151', fontWeight: 'bold' }}
                />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="registrations"
                  stroke="#3b82f6"
                  strokeWidth={3}
                  name="New Registrations"
                  dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2, fill: '#ffffff' }}
                />
                <Line
                  type="monotone"
                  dataKey="renewals"
                  stroke="#10b981"
                  strokeWidth={3}
                  name="ID Renewals"
                  dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: '#10b981', strokeWidth: 2, fill: '#ffffff' }}
                />
              </LineChart>
            </ResponsiveContainer>
            <div className="mt-4 text-center">
              <div
                className="inline-flex items-center px-4 py-2 rounded-full animate-pulse"
                style={{ backgroundColor: `${PRIMARY_COLOR}10` }}
              >
                <div className="w-3 h-3 rounded-full mr-2 animate-ping" style={{ backgroundColor: PRIMARY_COLOR }}></div>
                <span className="text-sm font-medium" style={{ color: SECONDARY_COLOR }}>{t('city_wide_monthly_trends', 'City-wide Monthly Trends')}</span>
              </div>
            </div>
          </ChartCard>
        </div>

        {/* Row 3: Population by Sub-City and Gender Distribution */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 animate-slide-up" style={{ animationDelay: '0.3s' }}>
          <div className="lg:col-span-2">
            <ChartCard
              title={t('population_by_sub_city_top_5_full_list', 'Population by Sub-City (Top 5 & Full List)')}
              height={400}
              icon={<SubcityIcon />}
              gradient={true}
            >
              <ResponsiveContainer width="100%" height={340}>
                <BarChart
                  data={dashboardData.population_by_subcity || []}
                  layout="horizontal"
                  margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                >
                  <defs>
                    <linearGradient id="colorSubcity" x1="0" y1="0" x2="1" y2="0">
                      <stop offset="5%" stopColor="#8b5cf6" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#7c3aed" stopOpacity={0.9}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" strokeOpacity={0.5} />
                  <XAxis
                    type="number"
                    stroke="#64748b"
                    fontSize={12}
                    tick={{ fill: '#64748b' }}
                    axisLine={{ stroke: '#e2e8f0' }}
                  />
                  <YAxis
                    dataKey="name"
                    type="category"
                    width={120}
                    stroke="#64748b"
                    fontSize={12}
                    tick={{ fill: '#64748b' }}
                    axisLine={{ stroke: '#e2e8f0' }}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(255, 255, 255, 0.95)',
                      border: 'none',
                      borderRadius: '12px',
                      boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                      backdropFilter: 'blur(10px)'
                    }}
                    labelStyle={{ color: '#374151', fontWeight: 'bold' }}
                  />
                  <Bar
                    dataKey="population"
                    fill="url(#colorSubcity)"
                    radius={[0, 8, 8, 0]}
                    stroke="#7c3aed"
                    strokeWidth={1}
                  />
                </BarChart>
              </ResponsiveContainer>
              <div className="mt-4 text-center">
                <div className="inline-flex items-center px-4 py-2 bg-purple-50 rounded-full animate-pulse">
                  <div className="w-3 h-3 bg-purple-500 rounded-full mr-2 animate-ping"></div>
                  <span className="text-sm font-medium text-purple-700">{t('population_distribution_by_sub_city', 'Population Distribution by Sub-City')}</span>
                </div>
              </div>
            </ChartCard>
          </div>

          <div>
            <ChartCard
              title={t('gender_distribution_18_plus', 'Gender Distribution (18+)')}
              height={400}
              icon={<PeopleIcon />}
              gradient={true}
            >
              <ResponsiveContainer width="100%" height={280}>
                <PieChart>
                  <defs>
                    <linearGradient id="genderGradient0" x1="0" y1="0" x2="1" y2="1">
                      <stop offset="0%" stopColor="#3B82F6" stopOpacity={0.8}/>
                      <stop offset="100%" stopColor="#3B82F6" stopOpacity={1}/>
                    </linearGradient>
                    <linearGradient id="genderGradient1" x1="0" y1="0" x2="1" y2="1">
                      <stop offset="0%" stopColor="#10B981" stopOpacity={0.8}/>
                      <stop offset="100%" stopColor="#10B981" stopOpacity={1}/>
                    </linearGradient>
                  </defs>
                  <Pie
                    data={[
                      { name: t('male', 'Male'), value: dashboardData.gender_ratio?.male || 0 },
                      { name: t('female', 'Female'), value: dashboardData.gender_ratio?.female || 0 }
                    ]}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ percent }) => `${(percent * 100).toFixed(0)}%`}
                    outerRadius={90}
                    innerRadius={30}
                    paddingAngle={2}
                    dataKey="value"
                  >
                    <Cell fill="url(#genderGradient0)" stroke="#ffffff" strokeWidth={2} />
                    <Cell fill="url(#genderGradient1)" stroke="#ffffff" strokeWidth={2} />
                  </Pie>
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(255, 255, 255, 0.95)',
                      border: 'none',
                      borderRadius: '12px',
                      boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                      backdropFilter: 'blur(10px)'
                    }}
                    labelStyle={{ color: '#374151', fontWeight: 'bold' }}
                  />
                </PieChart>
              </ResponsiveContainer>
              <div className="mt-4 flex flex-wrap justify-center gap-3">
                <div className="flex items-center bg-gray-50 px-3 py-2 rounded-full">
                  <div className="w-4 h-4 rounded-full mr-2 shadow-sm" style={{ backgroundColor: '#3B82F6' }}></div>
                  <span className="text-sm font-medium text-gray-700">{t('male', 'Male')} ({dashboardData.gender_ratio?.male || 0})</span>
                </div>
                <div className="flex items-center bg-gray-50 px-3 py-2 rounded-full">
                  <div className="w-4 h-4 rounded-full mr-2 shadow-sm" style={{ backgroundColor: '#10B981' }}></div>
                  <span className="text-sm font-medium text-gray-700">{t('female', 'Female')} ({dashboardData.gender_ratio?.female || 0})</span>
                </div>
              </div>
            </ChartCard>
          </div>
        </div>

        {/* Row 4: Top 10 Most Populated Kebeles City-Wide and Age Range Distribution */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 animate-slide-up" style={{ animationDelay: '0.4s' }}>
          <ChartCard
            title="Top 10 Most Populated Kebeles City-Wide"
            height={400}
            icon={<LocationIcon />}
            gradient={true}
          >
            <ResponsiveContainer width="100%" height={340}>
              <BarChart
                data={dashboardData.top_kebeles_citywide || []}
                margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
              >
                <defs>
                  <linearGradient id="colorTopKebeles" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#06b6d4" stopOpacity={0.8}/>
                    <stop offset="95%" stopColor="#0891b2" stopOpacity={0.9}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" strokeOpacity={0.5} />
                <XAxis
                  dataKey="kebele_name"
                  stroke="#64748b"
                  fontSize={10}
                  tick={{ fill: '#64748b' }}
                  axisLine={{ stroke: '#e2e8f0' }}
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis
                  stroke="#64748b"
                  fontSize={12}
                  tick={{ fill: '#64748b' }}
                  axisLine={{ stroke: '#e2e8f0' }}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    border: 'none',
                    borderRadius: '12px',
                    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                    backdropFilter: 'blur(10px)'
                  }}
                  labelStyle={{ color: '#374151', fontWeight: 'bold' }}
                  formatter={(value, name, props) => [
                    value,
                    `Population (${props.payload.subcity})`
                  ]}
                />
                <Bar
                  dataKey="population"
                  fill="url(#colorTopKebeles)"
                  radius={[8, 8, 0, 0]}
                  stroke="#0891b2"
                  strokeWidth={1}
                />
              </BarChart>
            </ResponsiveContainer>
            <div className="mt-4 text-center">
              <div className="inline-flex items-center px-4 py-2 bg-cyan-50 rounded-full animate-pulse">
                <div className="w-3 h-3 bg-cyan-500 rounded-full mr-2 animate-ping"></div>
                <span className="text-sm font-medium text-cyan-700">Most Populated Areas City-Wide</span>
              </div>
            </div>
          </ChartCard>

          <ChartCard
            title="Age Range Distribution (18–24, 25–34, 35–44, 45–59, 60+)"
            height={400}
            icon={<AssessmentIcon />}
            gradient={true}
          >
            <ResponsiveContainer width="100%" height={280}>
              <PieChart>
                <defs>
                  {COLORS.map((color, index) => (
                    <linearGradient key={index} id={`ageGradient${index}`} x1="0" y1="0" x2="1" y2="1">
                      <stop offset="0%" stopColor={color} stopOpacity={0.8}/>
                      <stop offset="100%" stopColor={color} stopOpacity={1}/>
                    </linearGradient>
                  ))}
                </defs>
                <Pie
                  data={dashboardData.age_group_distribution || []}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ percent }) => `${(percent * 100).toFixed(0)}%`}
                  outerRadius={90}
                  innerRadius={30}
                  paddingAngle={2}
                  dataKey="count"
                  nameKey="name"
                >
                  {(dashboardData.age_group_distribution || []).map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={`url(#ageGradient${index % COLORS.length})`}
                      stroke="#ffffff"
                      strokeWidth={2}
                    />
                  ))}
                </Pie>
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    border: 'none',
                    borderRadius: '12px',
                    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                    backdropFilter: 'blur(10px)'
                  }}
                  labelStyle={{ color: '#374151', fontWeight: 'bold' }}
                />
              </PieChart>
            </ResponsiveContainer>
            <div className="mt-4 flex flex-wrap justify-center gap-2">
              {(dashboardData.age_group_distribution || []).map((entry, index) => (
                <div key={entry.name} className="flex items-center bg-gray-50 px-2 py-1 rounded-full text-xs">
                  <div
                    className="w-3 h-3 rounded-full mr-1 shadow-sm"
                    style={{ backgroundColor: COLORS[index % COLORS.length] }}
                  ></div>
                  <span className="font-medium text-gray-700">{entry.name}</span>
                </div>
              ))}
            </div>
          </ChartCard>
        </div>

        {/* Row 5: ID Status Summary and Migration Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 animate-slide-up" style={{ animationDelay: '0.5s' }}>
          <ChartCard
            title="ID Status Summary: Active / Expired / Pending Renewal"
            height={400}
            icon={<BadgeIcon />}
            gradient={true}
          >
            <ResponsiveContainer width="100%" height={280}>
              <PieChart>
                <defs>
                  <linearGradient id="statusGradient0" x1="0" y1="0" x2="1" y2="1">
                    <stop offset="0%" stopColor="#10B981" stopOpacity={0.8}/>
                    <stop offset="100%" stopColor="#10B981" stopOpacity={1}/>
                  </linearGradient>
                  <linearGradient id="statusGradient1" x1="0" y1="0" x2="1" y2="1">
                    <stop offset="0%" stopColor="#F59E0B" stopOpacity={0.8}/>
                    <stop offset="100%" stopColor="#F59E0B" stopOpacity={1}/>
                  </linearGradient>
                  <linearGradient id="statusGradient2" x1="0" y1="0" x2="1" y2="1">
                    <stop offset="0%" stopColor="#EF4444" stopOpacity={0.8}/>
                    <stop offset="100%" stopColor="#EF4444" stopOpacity={1}/>
                  </linearGradient>
                </defs>
                <Pie
                  data={dashboardData.id_status_summary || []}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ percent }) => `${(percent * 100).toFixed(0)}%`}
                  outerRadius={90}
                  innerRadius={30}
                  paddingAngle={2}
                  dataKey="value"
                  nameKey="name"
                >
                  {(dashboardData.id_status_summary || []).map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={`url(#statusGradient${index % 3})`}
                      stroke="#ffffff"
                      strokeWidth={2}
                    />
                  ))}
                </Pie>
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    border: 'none',
                    borderRadius: '12px',
                    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                    backdropFilter: 'blur(10px)'
                  }}
                  labelStyle={{ color: '#374151', fontWeight: 'bold' }}
                />
              </PieChart>
            </ResponsiveContainer>
            <div className="mt-4 flex flex-wrap justify-center gap-3">
              {(dashboardData.id_status_summary || []).map((entry, index) => (
                <div key={entry.name} className="flex items-center bg-gray-50 px-3 py-2 rounded-full">
                  <div
                    className="w-4 h-4 rounded-full mr-2 shadow-sm"
                    style={{ backgroundColor: ['#10B981', '#F59E0B', '#EF4444'][index % 3] }}
                  ></div>
                  <span className="text-sm font-medium text-gray-700">{entry.name}</span>
                </div>
              ))}
            </div>
          </ChartCard>

          <ChartCard
            title="Migration Overview (City-wide Inbound/Outbound)"
            height={400}
            icon={<MigrationIcon />}
            gradient={true}
          >
            <ResponsiveContainer width="100%" height={280}>
              <BarChart
                data={[
                  { name: 'Inbound', value: dashboardData.migration_data?.into || 0, color: '#10B981' },
                  { name: 'Outbound', value: dashboardData.migration_data?.outOf || 0, color: '#EF4444' },
                  { name: 'Net Migration', value: dashboardData.migration_data?.net_migration || 0, color: '#3B82F6' }
                ]}
                margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
              >
                <defs>
                  <linearGradient id="migrationInbound" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#10B981" stopOpacity={0.8}/>
                    <stop offset="95%" stopColor="#10B981" stopOpacity={0.9}/>
                  </linearGradient>
                  <linearGradient id="migrationOutbound" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#EF4444" stopOpacity={0.8}/>
                    <stop offset="95%" stopColor="#EF4444" stopOpacity={0.9}/>
                  </linearGradient>
                  <linearGradient id="migrationNet" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.8}/>
                    <stop offset="95%" stopColor="#3B82F6" stopOpacity={0.9}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" strokeOpacity={0.5} />
                <XAxis
                  dataKey="name"
                  stroke="#64748b"
                  fontSize={12}
                  tick={{ fill: '#64748b' }}
                  axisLine={{ stroke: '#e2e8f0' }}
                />
                <YAxis
                  stroke="#64748b"
                  fontSize={12}
                  tick={{ fill: '#64748b' }}
                  axisLine={{ stroke: '#e2e8f0' }}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    border: 'none',
                    borderRadius: '12px',
                    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                    backdropFilter: 'blur(10px)'
                  }}
                  labelStyle={{ color: '#374151', fontWeight: 'bold' }}
                />
                <Bar
                  dataKey="value"
                  fill={(entry) => {
                    if (entry.name === 'Inbound') return 'url(#migrationInbound)';
                    if (entry.name === 'Outbound') return 'url(#migrationOutbound)';
                    return 'url(#migrationNet)';
                  }}
                  radius={[8, 8, 0, 0]}
                  stroke="#ffffff"
                  strokeWidth={1}
                />
              </BarChart>
            </ResponsiveContainer>
            <div className="mt-4 text-center">
              <div className="inline-flex items-center px-4 py-2 bg-indigo-50 rounded-full animate-pulse">
                <div className="w-3 h-3 bg-indigo-500 rounded-full mr-2 animate-ping"></div>
                <span className="text-sm font-medium text-indigo-700">City-wide Migration Activity</span>
              </div>
            </div>
          </ChartCard>
        </div>
      </div>
    </div>
  );
};

export default CityDashboard;
