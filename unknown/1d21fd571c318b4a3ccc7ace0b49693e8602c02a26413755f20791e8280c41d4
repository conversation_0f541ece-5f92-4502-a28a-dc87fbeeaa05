#!/usr/bin/env python
"""
Test script to verify group-based permission system is working correctly.
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from django_tenants.utils import schema_context

User = get_user_model()

def test_group_permissions():
    print('=== Testing Group-Based Permission System ===')

    # <NAME_EMAIL> who has kebele_leader group
    with schema_context('kebele_kebele15'):
        try:
            user = User.objects.get(email='<EMAIL>')
            print(f'\nUser: {user.email}')
            primary_group = user.get_primary_group()
            if primary_group:
                try:
                    print(f'Primary Group: {primary_group.group.name}')
                except:
                    print(f'Primary Group: {primary_group} (error accessing group.name)')
            else:
                print('Primary Group: None')
            
            # Test various permissions
            test_permissions = [
                'add_citizen', 'change_citizen', 'delete_citizen',
                'add_idcard', 'change_idcard', 'delete_idcard',
                'add_user', 'change_user', 'view_tenant'
            ]
            
            print('Permission Tests:')
            for perm in test_permissions:
                has_perm = user.has_group_permission(perm)
                status = 'YES' if has_perm else 'NO'
                print(f'  {perm}: {status}')
        except User.DoesNotExist:
            print('<EMAIL> not found')

    # <NAME_EMAIL> who has clerk group
    with schema_context('kebele_kebele15'):
        try:
            user = User.objects.get(email='<EMAIL>')
            print(f'\nUser: {user.email}')
            primary_group = user.get_primary_group()
            if primary_group:
                try:
                    print(f'Primary Group: {primary_group.group.name}')
                except:
                    print(f'Primary Group: {primary_group} (error accessing group.name)')
            else:
                print('Primary Group: None')
            
            print('Permission Tests:')
            for perm in test_permissions:
                has_perm = user.has_group_permission(perm)
                status = 'YES' if has_perm else 'NO'
                print(f'  {perm}: {status}')
        except User.DoesNotExist:
            print('<EMAIL> not found')

if __name__ == '__main__':
    test_group_permissions()
