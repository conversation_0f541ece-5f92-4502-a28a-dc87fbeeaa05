# Navigation Menu Permissions Guide

## 🔄 **Updated Navigation System**

The navigation menu has been updated to work with the new general workflow-based permission system. Menu items now appear based on the user's actual permissions rather than hardcoded role checks.

## 🎯 **Permission-Based Navigation**

### **📋 How It Works**

The navigation system uses the `usePermissions` hook which:
1. **Checks Backend Permissions**: Uses actual user permissions from the backend API
2. **Falls Back to Role-Based**: Uses role-based permissions if backend permissions unavailable
3. **Hierarchical Logic**: Shows appropriate views based on user's hierarchical level

### **🔧 Permission Mapping**

#### **Dashboard Access**
```javascript
'view_dashboard': [
  'view_kebele_dashboard',      // Clerk, Kebele Leader
  'view_subcity_dashboard',     // Subcity Admin
  'view_city_dashboard',        // City Admin
  'view_system_dashboard'       // Super Admin
]
```

#### **Citizens Management**
```javascript
'view_citizens': [
  'view_citizens_list',         // Basic viewing
  'view_own_kebele_data',       // Kebele-level access
  'view_child_kebeles_data',    // Subcity admin access
  'view_child_subcities_data',  // City admin access
  'view_all_tenants_data'       // Super admin access
]

'register_citizens': ['register_citizens']  // Full registration workflow
```

#### **ID Cards Management**
```javascript
'view_idcards': [
  'view_id_cards_list',         // Basic viewing
  'view_own_kebele_data',       // Kebele-level access
  'view_child_kebeles_data',    // Subcity admin access
  'view_child_subcities_data'   // City admin access
]

'create_idcards': ['generate_id_cards']     // Generate ID cards
'approve_idcards': ['approve_id_cards']     // Approve ID cards
'print_idcards': ['generate_id_cards', 'approve_id_cards']  // Print capability
```

#### **User Management**
```javascript
'manage_users': [
  'create_kebele_users',        // Subcity admin capability
  'create_subcity_users',       // City admin capability
  'manage_all_users'            // Super admin capability
]
```

#### **Reports Access**
```javascript
'view_reports': [
  'view_kebele_reports',        // Kebele-level reports
  'view_subcity_reports',       // Subcity-level reports
  'view_city_reports',          // City-level reports
  'view_all_reports'            // System-wide reports
]
```

## 👥 **Role-Based Navigation**

### **🔧 Clerk**
**Navigation Items**:
- ✅ **Dashboard** - Kebele dashboard
- ✅ **Citizens** - Kebele citizens list
  - ✅ Citizens List
  - ✅ Register Citizen (full workflow)
- ✅ **ID Cards** - Kebele ID cards
  - ✅ ID Cards List
  - ✅ Generate ID Cards

**Hidden Items**:
- ❌ Reports (no permission)
- ❌ Users (no permission)
- ❌ Tenants (no permission)
- ❌ Cross-kebele views (no permission)

### **👨‍💼 Kebele Leader**
**Navigation Items**:
- ✅ **Dashboard** - Kebele dashboard
- ✅ **Citizens** - Kebele citizens list
  - ✅ Citizens List
- ✅ **ID Cards** - Kebele ID cards
  - ✅ ID Cards List
  - ✅ Pending Approval
- ✅ **Reports** - Kebele reports
- ✅ **Workflows** - Document verification
  - ✅ Document Verification
  - ✅ Citizen Transfers

**Hidden Items**:
- ❌ Register Citizen (clerks only)
- ❌ Generate ID Cards (clerks only)
- ❌ Users (no permission)
- ❌ Cross-kebele views (no permission)

### **🏢 Subcity Admin**
**Navigation Items**:
- ✅ **Dashboard** - Subcity dashboard (aggregated)
- ✅ **Citizens** - Cross-kebele view
  - ✅ All Kebeles Citizens
- ✅ **ID Cards** - Cross-kebele view
  - ✅ All Kebeles ID Cards
  - ✅ Pending Approval
- ✅ **Reports** - Subcity reports (cumulative)
- ✅ **Users** - Kebele user management
  - ✅ Kebele Management
  - ✅ User Permissions
- ✅ **Workflows** - Administrative workflows

**Hidden Items**:
- ❌ Register Citizen (delegated to kebeles)
- ❌ Tenants (city admin and above only)

### **🏛️ City Admin**
**Navigation Items**:
- ✅ **Dashboard** - City dashboard (aggregated)
- ✅ **Citizens** - City-wide directory
- ✅ **ID Cards** - City-wide view
- ✅ **Reports** - City reports (cumulative)
- ✅ **Users** - City user management
  - ✅ City Management
  - ✅ Subcity Management

**Hidden Items**:
- ❌ Register Citizen (delegated to kebeles)
- ❌ Tenants (super admin only)

### **⚡ Super Admin**
**Navigation Items**:
- ✅ **Dashboard** - System dashboard
- ✅ **Citizens** - System-wide directory
- ✅ **ID Cards** - System-wide view
- ✅ **Reports** - All reports
- ✅ **Users** - Full user management
- ✅ **Tenants** - Tenant management
  - ✅ Tenants List
  - ✅ Register Tenant
- ✅ **Workflows** - All workflows
- ✅ **Settings** - System settings

**No Hidden Items**: Full access to all features

## 🔄 **Dynamic Navigation Logic**

### **Hierarchical Views**
```javascript
// Citizens navigation logic
if (hasPermission('view_cross_subcity')) {
  // City admin: City-wide citizen directory
  path = '/citizens/citizen-book'
} else if (hasPermission('view_cross_kebele')) {
  // Subcity admin: Cross-kebele citizens
  path = '/citizens/all-kebeles'
} else {
  // Kebele users: Own kebele citizens
  path = '/citizens'
}
```

### **User Management Navigation**
```javascript
// User management navigation logic
if (hasPermission('view_tenants')) {
  // Super admin: Full user management
  path = '/users'
} else if (hasPermission('view_cross_subcity')) {
  // City admin: City-wide user management
  path = '/users/city-management'
} else {
  // Subcity admin: Kebele user management
  path = '/users/kebele-management'
}
```

## 🎯 **Navigation Features**

### **✅ Responsive Design**
- **Desktop**: Horizontal navigation bar with dropdowns
- **Mobile**: Collapsible drawer navigation
- **Tablet**: Adaptive layout based on screen size

### **✅ Permission-Aware**
- **Dynamic Menus**: Only show items user can access
- **Contextual Paths**: Route to appropriate view based on permissions
- **Fallback Handling**: Graceful degradation if permissions unavailable

### **✅ User Experience**
- **Clear Hierarchy**: Visual indication of user's access level
- **Intuitive Grouping**: Related items grouped together
- **Quick Access**: Most-used items prominently displayed

## 🔧 **Technical Implementation**

### **Permission Hook**
```javascript
const { hasPermission, getFilteredNavigationItems } = usePermissions();

// Check specific permission
if (hasPermission('register_citizens')) {
  // Show citizen registration
}

// Get filtered navigation items
const navItems = getFilteredNavigationItems();
```

### **Navigation Component**
```javascript
// Dynamic menu building
const menuItems = [];

if (hasPermission('view_citizens')) {
  menuItems.push({
    text: 'Citizens',
    icon: <PersonIcon />,
    path: getAppropriatePathForUser()
  });
}
```

## 📊 **Benefits**

### **✅ Security**
- **Permission-Based**: Only show accessible features
- **Role Separation**: Clear boundaries between user types
- **Hierarchical Access**: Appropriate data scope for each level

### **✅ User Experience**
- **Simplified Interface**: Users only see relevant options
- **Intuitive Navigation**: Matches user's job responsibilities
- **Consistent Behavior**: Same logic across all components

### **✅ Maintainability**
- **Centralized Logic**: Permission checking in one place
- **Easy Updates**: Add new permissions without changing navigation
- **Flexible System**: Supports custom roles and permissions

---

**The updated navigation system provides a secure, intuitive, and maintainable way to control access to different parts of the application based on the user's actual permissions and role in the organizational hierarchy!** 🚀
