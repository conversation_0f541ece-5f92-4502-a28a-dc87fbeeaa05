from rest_framework import serializers
from django.contrib.auth import get_user_model
from tenants.models import Tenant
from workflows.models import CitizenTransferRequest, CitizenTransferHistory

User = get_user_model()


class TenantBasicSerializer(serializers.ModelSerializer):
    """Basic tenant information for transfer requests."""

    class Meta:
        model = Tenant
        fields = ['id', 'name', 'type']


class UserBasicSerializer(serializers.ModelSerializer):
    """Basic user information for transfer requests."""
    
    class Meta:
        model = User
        fields = ['id', 'email', 'first_name', 'last_name', 'role']


class CitizenTransferRequestSerializer(serializers.ModelSerializer):
    """Serializer for citizen transfer requests."""
    
    source_kebele_info = TenantBasicSerializer(source='source_kebele', read_only=True)
    destination_kebele_info = TenantBasicSerializer(source='destination_kebele', read_only=True)
    requested_by_info = UserBasicSerializer(source='requested_by', read_only=True)
    reviewed_by_info = UserBasicSerializer(source='reviewed_by', read_only=True)
    completed_by_info = UserBasicSerializer(source='completed_by', read_only=True)
    
    # Status display
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    transfer_reason_display = serializers.CharField(source='get_transfer_reason_display', read_only=True)
    
    # Workflow status checks
    can_be_accepted = serializers.BooleanField(read_only=True)
    can_be_rejected = serializers.BooleanField(read_only=True)
    can_be_completed = serializers.BooleanField(read_only=True)
    can_be_cancelled = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = CitizenTransferRequest
        fields = [
            'id', 'transfer_id', 'citizen_id', 'citizen_name', 'citizen_digital_id',
            'source_kebele', 'source_kebele_info', 'destination_kebele', 'destination_kebele_info',
            'transfer_reason', 'transfer_reason_display', 'reason_description',
            'status', 'status_display', 'requested_by', 'requested_by_info',
            'reviewed_by', 'reviewed_by_info', 'completed_by', 'completed_by_info',
            'created_at', 'reviewed_at', 'completed_at', 'review_notes', 'new_citizen_id',
            'can_be_accepted', 'can_be_rejected', 'can_be_completed', 'can_be_cancelled'
        ]
        read_only_fields = [
            'id', 'transfer_id', 'requested_by', 'reviewed_by', 'completed_by',
            'created_at', 'reviewed_at', 'completed_at', 'new_citizen_id'
        ]


class CitizenTransferRequestCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating transfer requests."""
    
    class Meta:
        model = CitizenTransferRequest
        fields = [
            'citizen_id', 'citizen_name', 'citizen_digital_id',
            'destination_kebele', 'transfer_reason', 'reason_description'
        ]
    
    def validate_destination_kebele(self, value):
        """Validate that destination kebele is different from source."""
        request = self.context.get('request')
        if request and hasattr(request.user, 'tenant'):
            if value.id == request.user.tenant.id:
                raise serializers.ValidationError(
                    "Cannot transfer citizen to the same kebele."
                )
        return value
    
    def validate_citizen_id(self, value):
        """Validate that citizen exists in the source kebele."""
        request = self.context.get('request')
        if request and hasattr(request.user, 'tenant'):
            # This validation will be done in the view with proper schema context
            pass
        return value


class CitizenTransferRequestReviewSerializer(serializers.ModelSerializer):
    """Serializer for reviewing transfer requests (accept/reject)."""
    
    action = serializers.ChoiceField(
        choices=[('accept', 'Accept'), ('reject', 'Reject')],
        write_only=True
    )
    
    class Meta:
        model = CitizenTransferRequest
        fields = ['action', 'review_notes']
    
    def validate(self, attrs):
        """Validate that the request can be reviewed."""
        if not self.instance.can_be_accepted and not self.instance.can_be_rejected:
            raise serializers.ValidationError(
                "This transfer request cannot be reviewed."
            )
        return attrs


class CitizenTransferRequestCompleteSerializer(serializers.ModelSerializer):
    """Serializer for completing transfer requests."""
    
    class Meta:
        model = CitizenTransferRequest
        fields = []  # No additional fields needed for completion
    
    def validate(self, attrs):
        """Validate that the request can be completed."""
        if not self.instance.can_be_completed:
            raise serializers.ValidationError(
                "This transfer request cannot be completed."
            )
        return attrs


class CitizenTransferHistorySerializer(serializers.ModelSerializer):
    """Serializer for transfer history records."""
    
    transfer_request_info = CitizenTransferRequestSerializer(source='transfer_request', read_only=True)
    
    class Meta:
        model = CitizenTransferHistory
        fields = [
            'id', 'transfer_request', 'transfer_request_info',
            'original_citizen_data', 'transfer_date',
            'source_kebele_name', 'destination_kebele_name',
            'transfer_year', 'transfer_month'
        ]
        read_only_fields = ['id', 'transfer_date', 'transfer_year', 'transfer_month']


class TransferStatsSerializer(serializers.Serializer):
    """Serializer for transfer statistics."""
    
    total_requests = serializers.IntegerField()
    pending_requests = serializers.IntegerField()
    accepted_requests = serializers.IntegerField()
    rejected_requests = serializers.IntegerField()
    completed_transfers = serializers.IntegerField()
    
    # Monthly breakdown
    monthly_stats = serializers.ListField(
        child=serializers.DictField(),
        read_only=True
    )
    
    # By reason breakdown
    reason_stats = serializers.ListField(
        child=serializers.DictField(),
        read_only=True
    )


class KebeleTransferSummarySerializer(serializers.Serializer):
    """Serializer for kebele-specific transfer summary."""
    
    kebele_id = serializers.IntegerField()
    kebele_name = serializers.CharField()
    
    # Outgoing transfers (citizens leaving this kebele)
    outgoing_total = serializers.IntegerField()
    outgoing_pending = serializers.IntegerField()
    outgoing_completed = serializers.IntegerField()
    
    # Incoming transfers (citizens coming to this kebele)
    incoming_total = serializers.IntegerField()
    incoming_pending = serializers.IntegerField()
    incoming_completed = serializers.IntegerField()
    
    # Net transfer (incoming - outgoing)
    net_transfer = serializers.IntegerField()
