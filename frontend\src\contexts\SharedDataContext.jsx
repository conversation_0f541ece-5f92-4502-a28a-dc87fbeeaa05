import { createContext, useContext, useState, useEffect } from 'react';
import sharedDataService from '../services/sharedDataService';

// Create context
const SharedDataContext = createContext();

// Custom hook to use the shared data context
export const useSharedData = () => {
  const context = useContext(SharedDataContext);
  if (!context) {
    throw new Error('useSharedData must be used within a SharedDataProvider');
  }
  return context;
};

// Provider component
export const SharedDataProvider = ({ children }) => {
  const [sharedData, setSharedData] = useState({
    countries: [],
    regions: [],
    religions: [],
    citizenStatuses: [],
    maritalStatuses: [],
    documentTypes: [],
    employmentTypes: [],
    relationships: [],
    currentStatuses: [],
    biometricTypes: [],
    subcities: [],
    kebeles: [],
    ketenas: [],
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Function to fetch all shared data
  const fetchAllSharedData = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await sharedDataService.getAllSharedData();
      console.log('🔍 SharedDataContext - Fetched data:', {
        subcitiesCount: data.subcities?.length || 0,
        kebelesCount: data.kebeles?.length || 0,
        countriesCount: data.countries?.length || 0,
        religionsCount: data.religions?.length || 0
      });
      setSharedData(data);

      // Check if any critical data is missing and warn the user
      const criticalData = ['countries', 'religions', 'citizenStatuses', 'maritalStatuses', 'subcities', 'kebeles'];
      const missingData = criticalData.filter(key => !data[key] || data[key].length === 0);

      if (missingData.length > 0) {
        console.warn('Some shared data is missing:', missingData);
        setError(`Some data could not be loaded: ${missingData.join(', ')}. Some features may not work properly.`);
      }
    } catch (err) {
      console.error('Error fetching shared data:', err);
      setError('Failed to load shared data. Please check your connection and try again.');
    } finally {
      setLoading(false);
    }
  };

  // Function to refresh specific data type
  const refreshData = async (dataType) => {
    try {
      setLoading(true);
      let data;

      switch (dataType) {
        case 'countries':
          data = await sharedDataService.getCountries();
          setSharedData(prev => ({ ...prev, countries: data }));
          break;
        case 'regions':
          data = await sharedDataService.getRegions();
          setSharedData(prev => ({ ...prev, regions: data }));
          break;
        case 'religions':
          data = await sharedDataService.getReligions();
          setSharedData(prev => ({ ...prev, religions: data }));
          break;
        case 'citizenStatuses':
          data = await sharedDataService.getCitizenStatuses();
          setSharedData(prev => ({ ...prev, citizenStatuses: data }));
          break;
        case 'maritalStatuses':
          data = await sharedDataService.getMaritalStatuses();
          setSharedData(prev => ({ ...prev, maritalStatuses: data }));
          break;
        case 'documentTypes':
          data = await sharedDataService.getDocumentTypes();
          setSharedData(prev => ({ ...prev, documentTypes: data }));
          break;
        case 'employmentTypes':
          data = await sharedDataService.getEmploymentTypes();
          setSharedData(prev => ({ ...prev, employmentTypes: data }));
          break;
        case 'relationships':
          data = await sharedDataService.getRelationships();
          setSharedData(prev => ({ ...prev, relationships: data }));
          break;
        case 'currentStatuses':
          data = await sharedDataService.getCurrentStatuses();
          setSharedData(prev => ({ ...prev, currentStatuses: data }));
          break;
        case 'biometricTypes':
          data = await sharedDataService.getBiometricTypes();
          setSharedData(prev => ({ ...prev, biometricTypes: data }));
          break;
        case 'subcities':
          data = await sharedDataService.getSubcities();
          setSharedData(prev => ({ ...prev, subcities: data }));
          break;
        case 'kebeles':
          data = await sharedDataService.getKebeles();
          setSharedData(prev => ({ ...prev, kebeles: data }));
          break;
        case 'ketenas':
          data = await sharedDataService.getKetenas();
          setSharedData(prev => ({ ...prev, ketenas: data }));
          break;
        default:
          await fetchAllSharedData();
      }
    } catch (err) {
      console.error(`Error refreshing ${dataType}:`, err);
      setError(`Failed to refresh ${dataType}. Please try again later.`);
    } finally {
      setLoading(false);
    }
  };

  // Fetch regions for a specific country
  const fetchRegionsByCountry = async (countryId) => {
    try {
      const regions = await sharedDataService.getRegions(countryId);
      setSharedData(prev => ({ ...prev, regions }));
      return regions;
    } catch (err) {
      console.error('Error fetching regions by country:', err);
      throw err;
    }
  };

  // Load all shared data on component mount
  useEffect(() => {
    fetchAllSharedData();
  }, []);

  // Function to retry fetching shared data
  const retryFetchSharedData = async () => {
    console.log('Retrying to fetch shared data...');
    await fetchAllSharedData();
  };

  // Value to be provided by the context
  const value = {
    ...sharedData,
    loading,
    error,
    refreshData,
    fetchAllSharedData,
    fetchRegionsByCountry,
    retryFetchSharedData,
  };

  return (
    <SharedDataContext.Provider value={value}>
      {children}
    </SharedDataContext.Provider>
  );
};

export default SharedDataContext;
