import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { usePermissions } from '../../hooks/usePermissions';
import { Box, Typography, Alert, Button } from '@mui/material';
import { ArrowBack as BackIcon } from '@mui/icons-material';

/**
 * Component to protect routes based on user permissions
 */
const RoleProtectedRoute = ({ children, requiredPermission, fallbackPath = '/dashboard' }) => {
  const { hasPermission, userRole } = usePermissions();
  const location = useLocation();

  // If no permission is required, allow access
  if (!requiredPermission) {
    return children;
  }

  // Check if user has the required permission
  if (!hasPermission(requiredPermission)) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '60vh',
          textAlign: 'center',
          p: 3
        }}
      >
        <Alert severity="warning" sx={{ mb: 3, maxWidth: 600 }}>
          <Typography variant="h6" gutterBottom>
            Access Denied
          </Typography>
          <Typography variant="body1" gutterBottom>
            You don't have permission to access this page.
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Your role: <strong>{userRole}</strong>
            <br />
            Required permission: <strong>{requiredPermission}</strong>
          </Typography>
        </Alert>
        
        <Button
          variant="contained"
          startIcon={<BackIcon />}
          onClick={() => window.history.back()}
          sx={{ mr: 2 }}
        >
          Go Back
        </Button>
        
        <Button
          variant="outlined"
          onClick={() => window.location.href = fallbackPath}
        >
          Go to Dashboard
        </Button>
      </Box>
    );
  }

  return children;
};

/**
 * Higher-order component for role-based route protection
 */
export const withRoleProtection = (Component, requiredPermission, fallbackPath) => {
  return (props) => (
    <RoleProtectedRoute requiredPermission={requiredPermission} fallbackPath={fallbackPath}>
      <Component {...props} />
    </RoleProtectedRoute>
  );
};

/**
 * Route definitions with required permissions
 */
export const PROTECTED_ROUTES = {
  // Tenant management - only superadmin
  '/tenants': 'view_tenants',
  '/tenants/create': 'create_tenants',
  '/tenants/register': 'create_tenants',
  '/tenants/:id/details': 'view_tenants',
  '/tenants/:id/edit': 'edit_tenants',
  '/tenants/:id': 'edit_tenants',

  // User management - only admins
  '/users': 'manage_users',
  '/users/create': 'manage_users',
  '/users/kebeles': 'manage_users',
  '/users/:id': 'manage_users',

  // Cross-tenant views - only subcity admins and above
  '/citizens/all-kebeles': 'manage_citizens',
  '/idcards/all-kebeles': 'manage_idcards',
  
  // Approval workflows - only leaders and admins
  '/idcards/pending-subcity-approval': 'approve_idcards',
  
  // Printing - only subcity admins and above
  '/idcards/printing-queue': 'print_idcards',
  
  // Reports - only leaders and admins
  '/reports': 'view_reports',
  
  // Basic access
  '/dashboard': 'view_dashboard',
  '/citizens': 'view_citizens',
  '/citizens/create': 'register_citizens',
  '/idcards': 'view_idcards',
  '/idcards/create': 'create_idcards',
  '/profile': null, // No permission required for profile
};

/**
 * Get required permission for a given path
 */
export const getRequiredPermission = (path) => {
  // Direct match
  if (PROTECTED_ROUTES[path]) {
    return PROTECTED_ROUTES[path];
  }

  // Pattern matching for dynamic routes
  for (const [route, permission] of Object.entries(PROTECTED_ROUTES)) {
    if (route.includes(':')) {
      const pattern = route.replace(/:[^/]+/g, '[^/]+');
      const regex = new RegExp(`^${pattern}$`);
      if (regex.test(path)) {
        return permission;
      }
    }
  }

  return null; // No permission required
};

export default RoleProtectedRoute;
