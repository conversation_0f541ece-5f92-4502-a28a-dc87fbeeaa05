@echo off
echo ========================================
echo   DIAGNOSTIC Futronic SDK Service
echo   Step-by-Step Analysis and Testing
echo ========================================
echo.

echo Checking Python installation...
python --version
if errorlevel 1 (
    echo ERROR: Python not found. Please install Python 3.7+
    pause
    exit /b 1
)

echo.
echo Checking required DLL files...
if not exist "ftrScanAPI.dll" (
    echo ERROR: ftrScanAPI.dll not found
    echo Please ensure Futronic SDK files are in the current directory
    pause
    exit /b 1
)

echo SUCCESS: ftrScanAPI.dll found

echo.
echo Installing required Python packages...
pip install flask flask-cors

echo.
echo Starting DIAGNOSTIC Futronic SDK Service...
echo.
echo DIAGNOSTIC FEATURES:
echo - Detailed step-by-step logging
echo - Individual test functions for each SDK operation
echo - Thread-based timeout protection
echo - Comprehensive error analysis
echo - Interactive web interface for testing
echo.
echo Service will be available at: http://localhost:8001
echo Open the web interface to run diagnostic tests
echo.
echo Press Ctrl+C to stop the service
echo.

python diagnostic_sdk_service.py

echo.
echo Service stopped.
pause
