import os
import sys
import django
from django.db import connection

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from tenants.models import Tenant
from django_tenants.utils import schema_context
from idcards.models import IDCardTemplate

def create_default_templates():
    """Create default ID card templates for all tenants"""
    
    # Get all tenants
    tenants = Tenant.objects.all()
    
    for tenant in tenants:
        print(f"Creating templates for tenant: {tenant.name} ({tenant.schema_name})")
        
        with schema_context(tenant.schema_name):
            # Check if templates already exist
            if IDCardTemplate.objects.exists():
                print(f"  Templates already exist for {tenant.name}, skipping...")
                continue
            
            # Create default template
            default_template = IDCardTemplate.objects.create(
                name="Default Template",
                description="Standard ID card template with official government styling",
                background_color="#FFFFFF",
                text_color="#000000",
                accent_color="#1976D2",
                logo_position="top_left",
                photo_position="left",
                header_text="Federal Democratic Republic of Ethiopia",
                subtitle_text="National ID Card",
                footer_text="",
                is_active=True,
                is_default=True
            )
            print(f"  Created default template: {default_template.name}")
            
            # Create modern template
            modern_template = IDCardTemplate.objects.create(
                name="Modern Template",
                description="Modern design with blue accent colors",
                background_color="#F8F9FA",
                text_color="#212529",
                accent_color="#0D6EFD",
                logo_position="top_center",
                photo_position="right",
                header_text="Federal Democratic Republic of Ethiopia",
                subtitle_text="Digital ID Card",
                footer_text="Digitally Verified",
                is_active=True,
                is_default=False
            )
            print(f"  Created modern template: {modern_template.name}")
            
            # Create special edition template
            special_template = IDCardTemplate.objects.create(
                name="Special Edition",
                description="Special edition template with green accents",
                background_color="#FFFFFF",
                text_color="#000000",
                accent_color="#198754",
                logo_position="top_right",
                photo_position="center",
                header_text="Federal Democratic Republic of Ethiopia",
                subtitle_text="Special Edition ID Card",
                footer_text="Limited Edition",
                is_active=True,
                is_default=False
            )
            print(f"  Created special template: {special_template.name}")
            
    print("\nTemplate creation completed!")

if __name__ == "__main__":
    create_default_templates()
