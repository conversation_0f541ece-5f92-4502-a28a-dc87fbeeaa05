import React, { useState, useEffect } from 'react';
import {
  TextField,
  InputAdornment,
  IconButton,
  Tooltip,
  Box,
  ToggleButtonGroup,
  ToggleButton,
  Paper,
  Popover
} from '@mui/material';
import { CalendarToday, Info } from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import FixedEthiopianDateAdapter from '../../utils/FixedEthiopianDateAdapter';
import { EthiopianCalendarWidget } from './EthiopianCalendarWidget';
import {
  gregorianToEthiopian,
  ethiopianToGregorian,
  formatEthiopianDate,
  parseEthiopianDate,
  isValidEthiopianDate
} from '../../utils/ethiopianCalendar';

/**
 * Enhanced Ethiopian Date Picker Component with GC/EC Toggle
 * Allows users to input dates in Ethiopian calendar format while storing Gregorian dates
 * Now includes toggle between Ethiopian Calendar (EC) and Gregorian Calendar (GC)
 */
const EthiopianDatePicker = ({
  label = "Date",
  value,
  onChange,
  error = false,
  helperText = "",
  required = false,
  fullWidth = true,
  disabled = false,
  showToggle = true,
  showConversion = true,
  ...props
}) => {
  const [ethiopianDateString, setEthiopianDateString] = useState('');
  const [showGregorianPicker, setShowGregorianPicker] = useState(false);
  const [internalError, setInternalError] = useState('');
  const [calendarType, setCalendarType] = useState('EC'); // 'EC' or 'GC'
  const [anchorEl, setAnchorEl] = useState(null);
  const [showCalendarWidget, setShowCalendarWidget] = useState(false);

  // Debug log to verify enhanced component is loading
  console.log('🔥 EthiopianDatePicker ENHANCED LOADED 🔥 - showToggle:', showToggle, 'calendarType:', calendarType);

  // Convert Gregorian date to Ethiopian string when value changes
  useEffect(() => {
    if (value && value instanceof Date && !isNaN(value.getTime())) {
      const ethiopianDate = gregorianToEthiopian(value);
      if (ethiopianDate) {
        const formatted = formatEthiopianDate(ethiopianDate, 'DD/MM/YYYY', 'en');
        setEthiopianDateString(formatted);
      }
    } else {
      setEthiopianDateString('');
    }
  }, [value]);

  // Handle Ethiopian date string input
  const handleEthiopianDateChange = (event) => {
    const inputValue = event.target.value;
    setEthiopianDateString(inputValue);
    setInternalError('');

    if (!inputValue.trim()) {
      onChange && onChange(null);
      return;
    }

    // Try to parse the Ethiopian date
    const ethiopianDate = parseEthiopianDate(inputValue);
    if (ethiopianDate && isValidEthiopianDate(ethiopianDate)) {
      try {
        const gregorianDate = ethiopianToGregorian(
          ethiopianDate.year, 
          ethiopianDate.month, 
          ethiopianDate.day
        );
        if (gregorianDate) {
          onChange && onChange(gregorianDate);
        } else {
          setInternalError('Invalid Ethiopian date');
        }
      } catch (err) {
        setInternalError('Invalid Ethiopian date');
      }
    } else if (inputValue.length >= 8) { // Only show error for reasonably complete inputs
      setInternalError('Please use DD/MM/YYYY format');
    }
  };

  // Handle Gregorian date picker change
  const handleGregorianDateChange = (newDate) => {
    if (newDate && !isNaN(newDate.getTime())) {
      onChange && onChange(newDate);
      setShowGregorianPicker(false);
    }
  };

  // Handle calendar type change
  const handleCalendarTypeChange = (event, newType) => {
    if (newType !== null) {
      setCalendarType(newType);
    }
  };

  // Handle calendar widget open
  const handleCalendarClick = (event) => {
    setAnchorEl(event.currentTarget);
    setShowCalendarWidget(true);
  };

  // Handle calendar widget close
  const handleCalendarClose = () => {
    setAnchorEl(null);
    setShowCalendarWidget(false);
  };

  // Handle calendar widget date change
  const handleCalendarWidgetChange = (newDate) => {
    if (newDate) {
      onChange && onChange(newDate);
      handleCalendarClose();
    }
  };

  const displayError = error || !!internalError;
  const displayHelperText = internalError || helperText;

  return (
    <Box>

      <TextField
        label={`${label} (Ethiopian Calendar)`}
        value={ethiopianDateString}
        onChange={handleEthiopianDateChange}
        error={displayError}
        helperText={displayHelperText}
        required={required}
        fullWidth={fullWidth}
        disabled={disabled}
        placeholder="DD/MM/YYYY"
        InputProps={{
          endAdornment: (
            <InputAdornment position="end">
              <Tooltip title="Open Ethiopian calendar picker">
                <IconButton
                  onClick={handleCalendarClick}
                  edge="end"
                  size="small"
                  disabled={disabled}
                >
                  <CalendarToday />
                </IconButton>
              </Tooltip>
              <Tooltip title="Ethiopian calendar format: DD/MM/YYYY">
                <IconButton edge="end" size="small">
                  <Info />
                </IconButton>
              </Tooltip>
            </InputAdornment>
          )
        }}
        {...props}
      />

      {/* Enhanced Calendar Widget Popover */}
      <Popover
        open={showCalendarWidget}
        anchorEl={anchorEl}
        onClose={handleCalendarClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
      >
        <Paper sx={{ p: 1, minWidth: 300, maxWidth: 350 }}>
          <EthiopianCalendarWidget
            label=""
            value={value}
            onChange={handleCalendarWidgetChange}
            showToggle={true}
            showConversion={false}
            language="en"
            disableFuture={props.disableFuture}
            minYear={props.minYear}
            maxYear={props.maxYear}
          />
        </Paper>
      </Popover>

      {/* Gregorian Date Picker Dialog */}
      {showGregorianPicker && (
        <LocalizationProvider dateAdapter={FixedEthiopianDateAdapter}>
          <DatePicker
            label="Select Date (Gregorian Calendar)"
            value={value}
            onChange={handleGregorianDateChange}
            open={showGregorianPicker}
            onClose={() => setShowGregorianPicker(false)}
            slotProps={{
              textField: {
                style: { display: 'none' } // Hide the text field, only show the picker
              },
              popper: {
                placement: 'bottom-start'
              }
            }}
          />
        </LocalizationProvider>
      )}

      {/* Display Ethiopian date info */}
      {showConversion && value && ethiopianDateString && (
        <div style={{ fontSize: '0.75rem', color: '#666', marginTop: '4px' }}>
          Ethiopian: {ethiopianDateString} | Gregorian: {value.toLocaleDateString()}
        </div>
      )}
    </Box>
  );
};

export default EthiopianDatePicker;
