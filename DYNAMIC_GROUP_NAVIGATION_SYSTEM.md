# Dynamic Group-Based Navigation System

## 🎯 **Overview**

The GoID system now features a **fully dynamic, group-based navigation system** that:

1. **Works with Group Permissions**: Uses actual group permissions instead of hardcoded roles
2. **Supports Custom Groups**: Automatically adapts to new custom groups created in the future
3. **Dynamic Menu Generation**: Navigation items appear based on user's actual permissions
4. **Hierarchical Navigation**: Shows appropriate views based on user's organizational level
5. **Future-Proof**: Supports any custom group configuration without code changes

## 🔧 **Key Components**

### **1. Enhanced usePermissions Hook**

**File**: `frontend/src/hooks/usePermissions.js`

#### **New Features**:
- ✅ **Group-based permission checking** with detailed logging
- ✅ **Custom group detection** and management
- ✅ **Dynamic navigation generation** based on actual permissions
- ✅ **Hierarchical permission logic** for different organizational levels

#### **New Functions**:
```javascript
const {
  hasPermission,                    // Check if user has specific permission
  getDynamicNavigationItems,        // Generate navigation based on permissions
  getCustomGroupPermissions,        // Get user's custom groups
  isUsingCustomGroups,             // Check if user has custom groups
  userPermissions,                 // Array of user's permissions
  userGroups,                      // Array of user's groups
  customGroups                     // Array of custom groups only
} = usePermissions();
```

### **2. Dynamic Navigation Component**

**File**: `frontend/src/components/navigation/DynamicNavbar.jsx`

#### **Features**:
- ✅ **Permission-based menu items** - Only shows accessible features
- ✅ **Hierarchical navigation** - Different views based on user level
- ✅ **Custom group support** - Works with any group configuration
- ✅ **Mobile responsive** - Collapsible navigation for mobile devices
- ✅ **User info display** - Shows permissions and group count
- ✅ **Icon mapping** - Dynamic icons for navigation items

### **3. Updated MainLayout**

**File**: `frontend/src/layouts/MainLayout.jsx`

- ✅ **Uses DynamicNavbar** instead of static Navbar
- ✅ **Maintains all existing functionality**
- ✅ **Seamless integration** with existing layout system

## 🎯 **How It Works**

### **Permission-Based Navigation Generation**

```javascript
const getDynamicNavigationItems = () => {
  const dynamicItems = [];

  // Dashboard - always first if user has access
  if (hasPermission('view_dashboard')) {
    dynamicItems.push({
      id: 'dashboard',
      label: 'Dashboard',
      path: '/dashboard',
      icon: 'Dashboard'
    });
  }

  // Citizens - dynamic based on hierarchical level
  if (hasPermission('view_citizens')) {
    const citizensItem = {
      id: 'citizens',
      label: 'Citizens',
      icon: 'People',
      children: []
    };

    // Determine path based on user's level
    if (hasPermission('view_cross_subcity')) {
      citizensItem.path = '/citizens/citizen-book';  // City admin
    } else if (hasPermission('view_cross_kebele')) {
      citizensItem.path = '/citizens/all-kebeles';   // Subcity admin
    } else {
      citizensItem.path = '/citizens';               // Kebele level
    }

    // Add children based on permissions
    if (hasPermission('register_citizens')) {
      citizensItem.children.push({
        id: 'citizens-register',
        label: 'Register Citizen',
        path: '/citizens/create'
      });
    }

    dynamicItems.push(citizensItem);
  }

  // ... continues for all navigation items
};
```

### **Custom Group Detection**

```javascript
const getCustomGroupPermissions = () => {
  if (!user || !user.groups) return [];
  
  // Filter out system groups to identify custom groups
  const systemGroups = ['clerk', 'kebele_leader', 'subcity_admin', 'city_admin', 'super_admin'];
  const customGroups = user.groups.filter(group => !systemGroups.includes(group));
  
  return customGroups;
};
```

## 📊 **Navigation Examples by User Type**

### **🔧 Clerk (System Group)**
**Permissions**: `register_citizens`, `view_citizens_list`, `generate_id_cards`, `view_kebele_dashboard`

**Navigation Items**:
- ✅ **Dashboard** → `/dashboard`
- ✅ **Citizens** → `/citizens`
  - ✅ Register Citizen → `/citizens/create`
- ✅ **ID Cards** → `/idcards`

### **👨‍💼 Kebele Leader (System Group)**
**Permissions**: `view_citizens_list`, `approve_id_cards`, `verify_documents`, `view_kebele_reports`

**Navigation Items**:
- ✅ **Dashboard** → `/dashboard`
- ✅ **Citizens** → `/citizens`
- ✅ **ID Cards** → `/idcards`
  - ✅ Pending Approval → `/idcards/pending`
- ✅ **Reports** → `/reports`
- ✅ **Workflows** → `/workflows`
  - ✅ Document Verification → `/workflows/verification`

### **🏢 Subcity Admin (System Group)**
**Permissions**: `create_kebele_users`, `view_child_kebeles_data`, `approve_id_cards`, `view_subcity_reports`

**Navigation Items**:
- ✅ **Dashboard** → `/dashboard`
- ✅ **Citizens** → `/citizens/all-kebeles` (Cross-kebele view)
- ✅ **ID Cards** → `/idcards/all-kebeles`
  - ✅ Pending Approval → `/idcards/pending`
- ✅ **Kebele Users** → `/users/kebele-management`
- ✅ **Reports** → `/reports`

### **🎯 Autonomous Clerk (Custom Group)**
**Permissions**: `register_citizens`, `generate_id_cards`, `print_id_cards`, `view_kebele_dashboard`

**Navigation Items**:
- ✅ **Dashboard** → `/dashboard`
- ✅ **Citizens** → `/citizens`
  - ✅ Register Citizen → `/citizens/create`
- ✅ **ID Cards** → `/idcards`
  - ✅ **Printing Queue** → `/idcards/printing-queue` (🎯 **Custom permission!**)

### **📋 Document Specialist (Custom Group)**
**Permissions**: `verify_documents`, `view_citizens_list`, `view_citizen_details`

**Navigation Items**:
- ✅ **Dashboard** → `/dashboard`
- ✅ **Citizens** → `/citizens` (View only)
- ✅ **Workflows** → `/workflows`
  - ✅ **Document Verification** → `/workflows/verification` (🎯 **Specialized role!**)

## 🚀 **Benefits of Dynamic System**

### **✅ Future-Proof**
- **No Code Changes**: New custom groups work automatically
- **Permission-Based**: Navigation adapts to any permission combination
- **Scalable**: Supports unlimited custom groups and permissions

### **✅ User Experience**
- **Relevant Navigation**: Users only see what they can access
- **Hierarchical Views**: Appropriate data scope for each user level
- **Intuitive Interface**: Navigation matches job responsibilities

### **✅ Security**
- **Permission-Based Access**: Only shows accessible features
- **Real-Time Updates**: Navigation updates when permissions change
- **Granular Control**: Fine-grained permission checking

### **✅ Maintenance**
- **Self-Updating**: No manual navigation updates needed
- **Consistent Logic**: Same permission system across all components
- **Easy Debugging**: Comprehensive logging for troubleshooting

## 🔧 **Creating Custom Groups**

### **Example: Create Printing Officer**
```bash
python manage.py create_custom_group \
    --group-name "printing_officer" \
    --description "Officer responsible for ID card printing" \
    --permissions "print_id_cards" "view_id_cards_list" "approve_id_cards" \
    --tenant "your_tenant" \
    --group-type "functional"
```

**Result**: User with this group will automatically see:
- ✅ **Dashboard**
- ✅ **ID Cards** with **Printing Queue** submenu
- ✅ **Pending Approval** functionality

### **Example: Create Senior Clerk**
```bash
python manage.py create_custom_group \
    --group-name "senior_clerk" \
    --description "Senior clerk with enhanced capabilities" \
    --base-group "clerk" \
    --permissions "verify_documents" "approve_id_cards" \
    --group-type "operational"
```

**Result**: User gets all clerk permissions PLUS:
- ✅ **Workflows** → **Document Verification**
- ✅ **ID Cards** → **Pending Approval**

## 🔍 **Debug Features**

### **Console Logging**
The system provides detailed logging for troubleshooting:

```javascript
🔍 Group-based permission check: view_citizens
  required: ["view_citizens_list", "view_child_kebeles_data", ...]
  userHas: ["view_citizens_list", "create_kebele_users", ...]
  userGroups: ["subcity_admin", "custom_printing_officer"]
  result: true
```

### **User Info Display**
The navigation shows:
- **Username** and **Role**
- **Number of Groups** user belongs to
- **Number of Permissions** user has
- **Custom Groups** indicator

## 📈 **Migration from Old System**

### **Backward Compatibility**
- ✅ **Legacy roles still work** as fallback
- ✅ **Existing users unaffected** during transition
- ✅ **Gradual migration** possible

### **Migration Steps**
1. **Deploy new navigation system** (✅ Done)
2. **Assign users to groups** using management commands
3. **Test navigation** with different user types
4. **Create custom groups** as needed
5. **Remove legacy role dependencies** gradually

## 🎯 **Testing the System**

### **Test Different User Types**
1. **Login as Clerk**: Should see basic operational navigation
2. **Login as Subcity Admin**: Should see cross-kebele navigation
3. **Login as Custom Group User**: Should see custom permissions in navigation

### **Test Custom Groups**
1. **Create custom group** with specific permissions
2. **Assign user to custom group**
3. **Login as that user**
4. **Verify navigation** shows custom permissions

### **Test Permission Changes**
1. **Add permission to group**
2. **User navigation** should update automatically
3. **Remove permission from group**
4. **Navigation item** should disappear

---

**The dynamic group-based navigation system provides a flexible, future-proof solution that automatically adapts to any custom group configuration while maintaining security and user experience!** 🎉
