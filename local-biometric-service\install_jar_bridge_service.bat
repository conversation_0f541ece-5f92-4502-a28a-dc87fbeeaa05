@echo off
echo ========================================
echo   INSTALL JAR BRIDGE AS WINDOWS SERVICE
echo   Auto-Start with Windows
echo ========================================
echo.

REM Check for admin privileges
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: This script requires administrator privileges.
    echo Please run as administrator.
    pause
    exit /b 1
)

echo Installing JAR Bridge as Windows Service...
echo.

REM Get current directory
set SERVICE_DIR=%~dp0
set SERVICE_NAME=GoIDJarBridge
set SERVICE_DISPLAY=GoID JAR Bridge Service
set SERVICE_DESC=Biometric service for GoID using GonderFingerPrint.jar

echo Service Details:
echo   Name: %SERVICE_NAME%
echo   Display: %SERVICE_DISPLAY%
echo   Directory: %SERVICE_DIR%
echo.

REM Create service wrapper script
echo Creating service wrapper...
(
echo @echo off
echo cd /d "%SERVICE_DIR%"
echo python working_jar_bridge.py
) > "%SERVICE_DIR%service_wrapper.bat"

REM Install the service using sc command
echo Installing Windows service...
sc create "%SERVICE_NAME%" ^
    binPath= "\"%SERVICE_DIR%service_wrapper.bat\"" ^
    DisplayName= "%SERVICE_DISPLAY%" ^
    description= "%SERVICE_DESC%" ^
    start= auto

if %errorlevel% == 0 (
    echo ✅ Service installed successfully!
    echo.
    
    echo Starting service...
    sc start "%SERVICE_NAME%"
    
    if %errorlevel% == 0 (
        echo ✅ Service started successfully!
        echo.
        echo The JAR Bridge service will now start automatically with Windows.
        echo Service URL: http://localhost:8001
    ) else (
        echo ⚠️ Service installed but failed to start.
        echo You can start it manually from Services.msc
    )
) else (
    echo ❌ Failed to install service.
    echo Please check permissions and try again.
)

echo.
echo Installation complete!
pause
