import {
  Box,
  Typography,
  Grid,
  Paper,
  Divider,
  List,
  ListItem,
  ListItemText,
  useTheme,
  alpha,
} from '@mui/material';
import {
  LocationCity as CityIcon,
  Apartment as SubcityIcon,
  Home as KebeleIcon,
  Person as PersonIcon,
} from '@mui/icons-material';

const ReviewSubmit = ({ tenantType, tenantData, adminData, parentOptions }) => {
  const theme = useTheme();

  const getParentName = () => {
    if (tenantType === 'subcity') {
      if (!tenantData.city_id) return 'None';
      const parent = parentOptions.find(p => p.id === tenantData.city_id);
      return parent ? parent.name : 'Unknown';
    } else if (tenantType === 'kebele') {
      if (!tenantData.subcity_id) return 'None';
      const parent = parentOptions.find(p => p.id === tenantData.subcity_id);
      return parent ? parent.name : 'Unknown';
    }
    return 'None';
  };

  const getTenantIcon = () => {
    switch (tenantType) {
      case 'city':
        return <CityIcon fontSize="large" />;
      case 'subcity':
        return <SubcityIcon fontSize="large" />;
      case 'kebele':
        return <KebeleIcon fontSize="large" />;
      default:
        return null;
    }
  };

  const getTenantTypeLabel = () => {
    switch (tenantType) {
      case 'city':
        return 'City Administration';
      case 'subcity':
        return 'Subcity Administration';
      case 'kebele':
        return 'Kebele Administration';
      default:
        return 'Tenant';
    }
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom fontWeight="bold">
        Review & Submit
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Please review the information below before submitting. You can go back to make changes if needed.
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper
            elevation={2}
            sx={{
              p: 3,
              height: '100%',
              borderRadius: 2,
              position: 'relative',
              overflow: 'hidden',
            }}
          >
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                right: 0,
                width: 100,
                height: 100,
                background: `radial-gradient(circle at top right, ${alpha(theme.palette.primary.main, 0.1)}, transparent 70%)`,
                zIndex: 0,
              }}
            />

            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, position: 'relative', zIndex: 1 }}>
              <Box
                sx={{
                  mr: 2,
                  p: 1,
                  borderRadius: '50%',
                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                  color: theme.palette.primary.main,
                }}
              >
                {getTenantIcon()}
              </Box>
              <Box>
                <Typography variant="h6" fontWeight="bold">
                  {tenantType === 'city'
                    ? (tenantData.city_name || 'Unnamed City')
                    : (tenantData.name || 'Unnamed Tenant')}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {getTenantTypeLabel()} - {tenantData.domain_name}
                </Typography>
              </Box>
            </Box>

            <Divider sx={{ my: 2 }} />

            <List disablePadding>
              {tenantType !== 'city' && (
                <ListItem disablePadding sx={{ py: 1 }}>
                  <ListItemText
                    primary="Parent Tenant"
                    secondary={getParentName()}
                    primaryTypographyProps={{ variant: 'subtitle2', fontWeight: 'medium' }}
                  />
                </ListItem>
              )}

              <ListItem disablePadding sx={{ py: 1 }}>
                <ListItemText
                  primary="Domain Name"
                  secondary={tenantData.domain_name || 'Not provided'}
                  primaryTypographyProps={{ variant: 'subtitle2', fontWeight: 'medium' }}
                />
              </ListItem>

              {tenantType === 'city' ? (
                <ListItem disablePadding sx={{ py: 1 }}>
                  <ListItemText
                    primary="Contact Email"
                    secondary={tenantData.contact_email || 'Not provided'}
                    primaryTypographyProps={{ variant: 'subtitle2', fontWeight: 'medium' }}
                  />
                </ListItem>
              ) : (
                <ListItem disablePadding sx={{ py: 1 }}>
                  <ListItemText
                    primary="Code"
                    secondary={tenantData.code || 'Auto-generated'}
                    primaryTypographyProps={{ variant: 'subtitle2', fontWeight: 'medium' }}
                  />
                </ListItem>
              )}

              {tenantType === 'city' && (
                <ListItem disablePadding sx={{ py: 1 }}>
                  <ListItemText
                    primary="Contact Phone"
                    secondary={tenantData.contact_phone || 'Not provided'}
                    primaryTypographyProps={{ variant: 'subtitle2', fontWeight: 'medium' }}
                  />
                </ListItem>
              )}

              {tenantType === 'city' && (
                <>
                  <ListItem disablePadding sx={{ py: 1 }}>
                    <ListItemText
                      primary="Mayor"
                      secondary={tenantData.mayor_name || 'Not provided'}
                      primaryTypographyProps={{ variant: 'subtitle2', fontWeight: 'medium' }}
                    />
                  </ListItem>
                  <ListItem disablePadding sx={{ py: 1 }}>
                    <ListItemText
                      primary="Deputy Mayor"
                      secondary={tenantData.deputy_mayor || 'Not provided'}
                      primaryTypographyProps={{ variant: 'subtitle2', fontWeight: 'medium' }}
                    />
                  </ListItem>
                </>
              )}

              {tenantType === 'city' && (
                <ListItem disablePadding sx={{ py: 1 }}>
                  <ListItemText
                    primary="Status"
                    secondary={tenantData.is_active !== false ? 'Active' : 'Inactive'}
                    primaryTypographyProps={{ variant: 'subtitle2', fontWeight: 'medium' }}
                  />
                </ListItem>
              )}

              {(tenantType === 'subcity' || tenantType === 'kebele') && (
                <ListItem disablePadding sx={{ py: 1 }}>
                  <ListItemText
                    primary="Status"
                    secondary={tenantData.is_active !== false ? 'Active' : 'Inactive'}
                    primaryTypographyProps={{ variant: 'subtitle2', fontWeight: 'medium' }}
                  />
                </ListItem>
              )}

              {tenantType === 'city' && (
                <ListItem disablePadding sx={{ py: 1 }}>
                  <ListItemText
                    primary="Headquarter Address"
                    secondary={tenantData.headquarter_address || 'Not provided'}
                    primaryTypographyProps={{ variant: 'subtitle2', fontWeight: 'medium' }}
                  />
                </ListItem>
              )}

              {tenantType === 'city' && (
                <ListItem disablePadding sx={{ py: 1 }}>
                  <ListItemText
                    primary="Website"
                    secondary={tenantData.website || 'Not provided'}
                    primaryTypographyProps={{ variant: 'subtitle2', fontWeight: 'medium' }}
                  />
                </ListItem>
              )}
            </List>
          </Paper>
        </Grid>

        {/* Only show admin section for city and subcity tenants */}
        {tenantType !== 'kebele' && (
          <Grid item xs={12} md={6}>
            <Paper
              elevation={2}
              sx={{
                p: 3,
                height: '100%',
                borderRadius: 2,
                position: 'relative',
                overflow: 'hidden',
              }}
            >
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  right: 0,
                  width: 100,
                  height: 100,
                  background: `radial-gradient(circle at top right, ${alpha(theme.palette.secondary.main, 0.1)}, transparent 70%)`,
                  zIndex: 0,
                }}
              />

              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, position: 'relative', zIndex: 1 }}>
                <Box
                  sx={{
                    mr: 2,
                    p: 1,
                    borderRadius: '50%',
                    bgcolor: alpha(theme.palette.secondary.main, 0.1),
                    color: theme.palette.secondary.main,
                  }}
                >
                  <PersonIcon fontSize="large" />
                </Box>
                <Box>
                  <Typography variant="h6" fontWeight="bold">
                    {adminData.firstName && adminData.lastName
                      ? `${adminData.firstName} ${adminData.lastName}`
                      : 'Administrator Account'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {tenantType === 'city' ? 'City Admin' : 'Subcity Admin'}
                    {adminData.username && tenantData.domain_name && !adminData.username.includes('@') &&
                     ` - ${adminData.username}@${tenantData.domain_name}`}
                  </Typography>
                </Box>
              </Box>

              <Divider sx={{ my: 2 }} />

              <List disablePadding>
                <ListItem disablePadding sx={{ py: 1 }}>
                  <ListItemText
                    primary="Username"
                    secondary={adminData.username || 'Not provided'}
                    primaryTypographyProps={{ variant: 'subtitle2', fontWeight: 'medium' }}
                  />
                </ListItem>

                <ListItem disablePadding sx={{ py: 1 }}>
                  <ListItemText
                    primary="Email"
                    secondary={adminData.email || 'Not provided'}
                    primaryTypographyProps={{ variant: 'subtitle2', fontWeight: 'medium' }}
                  />
                </ListItem>

                <ListItem disablePadding sx={{ py: 1 }}>
                  <ListItemText
                    primary="Phone"
                    secondary={adminData.phone || 'Not provided'}
                    primaryTypographyProps={{ variant: 'subtitle2', fontWeight: 'medium' }}
                  />
                </ListItem>

                <ListItem disablePadding sx={{ py: 1 }}>
                  <ListItemText
                    primary="Password"
                    secondary="********"
                    primaryTypographyProps={{ variant: 'subtitle2', fontWeight: 'medium' }}
                  />
                </ListItem>
              </List>
            </Paper>
          </Grid>
        )}

        {/* For kebele tenants, show a note about user management */}
        {tenantType === 'kebele' && (
          <Grid item xs={12} md={6}>
            <Paper
              elevation={2}
              sx={{
                p: 3,
                height: '100%',
                borderRadius: 2,
                position: 'relative',
                overflow: 'hidden',
                bgcolor: alpha(theme.palette.info.main, 0.05),
                border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`,
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Box
                  sx={{
                    mr: 2,
                    p: 1,
                    borderRadius: '50%',
                    bgcolor: alpha(theme.palette.info.main, 0.1),
                    color: theme.palette.info.main,
                  }}
                >
                  <PersonIcon fontSize="large" />
                </Box>
                <Box>
                  <Typography variant="h6" fontWeight="bold">
                    User Management
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Kebele User Creation
                  </Typography>
                </Box>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Typography variant="body2" color="text.secondary" paragraph>
                For kebele tenants, users (clerks and kebele leaders) will be created by the parent subcity administrator after the kebele is registered.
              </Typography>

              <Typography variant="body2" color="text.secondary">
                The subcity admin will have access to create and manage users for this kebele through the user management interface.
              </Typography>
            </Paper>
          </Grid>
        )}
      </Grid>

      <Box sx={{ mt: 4 }}>
        <Typography variant="body2" color="text.secondary">
          {tenantType === 'kebele'
            ? 'By submitting this form, you are creating a new kebele tenant in the GoID system. Users for this kebele will be created by the parent subcity administrator.'
            : 'By submitting this form, you are creating a new tenant in the GoID system along with an administrator account. The administrator will have full access to manage this tenant.'
          }
        </Typography>
      </Box>
    </Box>
  );
};

export default ReviewSubmit;
