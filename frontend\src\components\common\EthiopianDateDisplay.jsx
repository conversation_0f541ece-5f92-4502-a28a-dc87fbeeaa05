import React from 'react';
import { Typography, Box } from '@mui/material';
import { 
  gregorianToEthiopian, 
  formatEthiopianDate 
} from '../../utils/ethiopianCalendar';

/**
 * Component to display dates in Ethiopian calendar format
 */
const EthiopianDateDisplay = ({ 
  date, 
  format = 'DD MMM YYYY', 
  language = 'en',
  showGregorian = false,
  variant = 'body1',
  ...props 
}) => {
  if (!date) {
    return <Typography variant={variant} {...props}>N/A</Typography>;
  }

  let gregorianDate;
  if (typeof date === 'string') {
    gregorianDate = new Date(date);
  } else if (date instanceof Date) {
    gregorianDate = date;
  } else {
    return <Typography variant={variant} {...props}>Invalid Date</Typography>;
  }

  if (isNaN(gregorianDate.getTime())) {
    return <Typography variant={variant} {...props}>Invalid Date</Typography>;
  }

  const ethiopianDate = gregorianToEthiopian(gregorianDate);
  if (!ethiopianDate) {
    return <Typography variant={variant} {...props}>Invalid Date</Typography>;
  }

  const ethiopianDateString = formatEthiopianDate(ethiopianDate, format, language);
  const gregorianDateString = gregorianDate.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  if (showGregorian) {
    return (
      <Box {...props}>
        <Typography variant={variant} component="div">
          {ethiopianDateString} (Ethiopian)
        </Typography>
        <Typography variant="caption" color="text.secondary" component="div">
          {gregorianDateString} (Gregorian)
        </Typography>
      </Box>
    );
  }

  return (
    <Typography variant={variant} {...props}>
      {ethiopianDateString}
    </Typography>
  );
};

export default EthiopianDateDisplay;
