@echo off
echo ========================================
echo    GoID JAR Bridge Service Manager
echo ========================================
echo.

REM Change to the directory where this batch file is located
cd /d "%~dp0"

:menu
echo Service Management Options:
echo.
echo 1. Check Service Status
echo 2. Start Service
echo 3. Stop Service
echo 4. Restart Service
echo 5. View Service Logs
echo 6. Test Service (Health Check)
echo 7. Exit
echo.
set /p choice="Enter your choice (1-7): "

if "%choice%"=="1" goto status
if "%choice%"=="2" goto start
if "%choice%"=="3" goto stop
if "%choice%"=="4" goto restart
if "%choice%"=="5" goto logs
if "%choice%"=="6" goto test
if "%choice%"=="7" goto exit
goto menu

:status
echo.
echo Checking service status...
sc query GoIDJarBridge
echo.
pause
goto menu

:start
echo.
echo Starting service...
python "%~dp0install_windows_service.py" start
echo.
pause
goto menu

:stop
echo.
echo Stopping service...
python "%~dp0install_windows_service.py" stop
echo.
pause
goto menu

:restart
echo.
echo Restarting service...
python "%~dp0install_windows_service.py" restart
echo.
pause
goto menu

:logs
echo.
echo Opening log directory...
if exist "logs" (
    explorer logs
) else (
    echo ❌ Log directory not found
)
echo.
pause
goto menu

:test
echo.
echo Testing service health...
echo Checking: http://localhost:8001/api/device/status
echo.
curl -s http://localhost:8001/api/device/status
if %errorlevel% == 0 (
    echo.
    echo ✅ Service is responding
) else (
    echo.
    echo ❌ Service is not responding
)
echo.
pause
goto menu

:exit
echo.
echo Goodbye!
exit /b 0
