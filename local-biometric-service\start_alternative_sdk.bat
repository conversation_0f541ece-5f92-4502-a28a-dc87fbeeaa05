@echo off
echo ========================================
echo   ALTERNATIVE SDK Service
echo   Using ftrScanGetImage() Instead
echo ========================================
echo.

echo ROOT CAUSE IDENTIFIED: ftrScanGetFrame() causes access violations
echo.
echo PROBLEM ANALYSIS:
echo    - ftrScanGetFrame() exit code 3221225477 = 0xC0000005 (Access Violation)
echo    - This happens in ALL contexts: services, external processes, etc.
echo    - The function has fundamental compatibility issues
echo.
echo SOLUTION: Use ftrScanGetImage() instead
echo    - ftrScanGetImage() is the higher-level, safer alternative
echo    - Designed for application integration (not low-level access)
echo    - Handles buffer management and error checking internally
echo    - More stable and reliable for production use
echo.

echo Checking Python installation...
python --version
if errorlevel 1 (
    echo ERROR: Python not found. Please install Python 3.7+
    pause
    exit /b 1
)

echo.
echo Checking required DLL files...
if not exist "ftrScanAPI.dll" (
    echo ERROR: ftrScanAPI.dll not found
    echo Please ensure Futronic SDK files are in the current directory
    pause
    exit /b 1
)

echo SUCCESS: ftrScanAPI.dll found

echo.
echo ========================================
echo   ALTERNATIVE APPROACH DETAILS
echo ========================================
echo.
echo FUNCTIONS TO TRY:
echo   1. ftrScanGetImage() - Primary alternative (safer)
echo   2. ftrScanGetImageEx() - Extended version if available
echo   3. ftrScanCaptureImage() - Alternative capture function
echo   4. ftrScanAcquireImage() - Alternative acquire function
echo   5. Minimal buffer approach - If others fail
echo.
echo ADVANTAGES:
echo   - Higher-level functions (less prone to access violations)
echo   - Better error handling and buffer management
echo   - Designed for application integration
echo   - More stable than low-level ftrScanGetFrame()
echo.
echo EXPECTED RESULTS:
echo   - Service starts without crashes
echo   - Alternative functions capture real fingerprint data
echo   - Stable operation for production use
echo   - Compatible with GoID system requirements
echo.

echo Starting ALTERNATIVE SDK Service...
echo Service will be available at: http://localhost:8001
echo.
echo This service uses ftrScanGetImage() and other safer alternatives
echo instead of the problematic ftrScanGetFrame() function.
echo.
echo Press Ctrl+C to stop the service
echo.

python alternative_sdk_service.py

echo.
echo Service stopped.
pause
