#!/usr/bin/env python
"""
Test script to check if the citizen_clearance_requests table exists
and if the CitizenClearanceRequest model is working properly.
"""

import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append('backend')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.db import connection
from django_tenants.utils import schema_context
from tenants.models import Tenant

def test_clearance_table():
    """Test if the citizen_clearance_requests table exists in tenant schemas."""
    
    print("🔍 Testing citizen_clearance_requests table...")
    
    # Test in public schema first
    print("\n📋 Testing in public schema:")
    try:
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'citizen_clearance_requests'
            """)
            result = cursor.fetchone()
            if result:
                print("✅ citizen_clearance_requests table exists in public schema")
            else:
                print("❌ citizen_clearance_requests table does NOT exist in public schema")
    except Exception as e:
        print(f"❌ Error checking public schema: {e}")
    
    # Test in tenant schemas
    print("\n📋 Testing in tenant schemas:")
    try:
        tenants = Tenant.objects.filter(schema_name__in=['city_addis_ababa', 'subcity_bole', 'kebele_bole_01'])
        
        for tenant in tenants:
            print(f"\n🏢 Testing tenant: {tenant.name} (schema: {tenant.schema_name})")
            
            try:
                with schema_context(tenant.schema_name):
                    with connection.cursor() as cursor:
                        cursor.execute(f"""
                            SELECT table_name 
                            FROM information_schema.tables 
                            WHERE table_schema = '{tenant.schema_name}' 
                            AND table_name = 'citizen_clearance_requests'
                        """)
                        result = cursor.fetchone()
                        if result:
                            print(f"✅ citizen_clearance_requests table exists in {tenant.schema_name}")
                            
                            # Test if we can query the table
                            cursor.execute(f"SELECT COUNT(*) FROM {tenant.schema_name}.citizen_clearance_requests")
                            count = cursor.fetchone()[0]
                            print(f"📊 Table has {count} records")
                            
                        else:
                            print(f"❌ citizen_clearance_requests table does NOT exist in {tenant.schema_name}")
                            
            except Exception as e:
                print(f"❌ Error testing {tenant.schema_name}: {e}")
                
    except Exception as e:
        print(f"❌ Error getting tenants: {e}")

def test_model_import():
    """Test if the CitizenClearanceRequest model can be imported and used."""
    
    print("\n🔍 Testing CitizenClearanceRequest model import...")
    
    try:
        from workflows.models import CitizenClearanceRequest
        print("✅ CitizenClearanceRequest model imported successfully")
        
        # Test model meta information
        print(f"📋 Model table name: {CitizenClearanceRequest._meta.db_table}")
        print(f"📋 Model app label: {CitizenClearanceRequest._meta.app_label}")
        
        # Test if we can create a query (without executing it)
        queryset = CitizenClearanceRequest.objects.all()
        print(f"✅ Queryset created successfully: {queryset.query}")
        
    except Exception as e:
        print(f"❌ Error importing or using CitizenClearanceRequest model: {e}")

if __name__ == "__main__":
    print("🚀 Starting citizen clearance table test...")
    test_model_import()
    test_clearance_table()
    print("\n✅ Test completed!")
