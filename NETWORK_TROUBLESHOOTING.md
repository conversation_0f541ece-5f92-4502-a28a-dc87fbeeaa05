# 🌐 Network Access & Login Troubleshooting Guide

## Problem
- Can access frontend at `http://************:3000`
- Cannot login with correct credentials
- Getting CORS or network errors

## Solution Applied

### 1. ✅ CORS Configuration Updated
Added your IP address to the allowed origins in `backend/goid/settings.py`:
```python
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://************:3000",  # Your IP
    "http://************",       # In case frontend is on port 80
]
```

### 2. ✅ Frontend API Configuration Updated
Updated `frontend/src/utils/axios.js` to automatically detect the correct API URL based on the current host.

### 3. ✅ Docker Configuration Updated
Updated Docker Compose and Dockerfile to use the correct API URL.

## Quick Fix Steps

### Step 1: Restart Services
```bash
# Stop current containers
docker-compose down

# Rebuild and start with new configuration
docker-compose up --build
```

### Step 2: Verify Network Access
Test if the backend is accessible:
```bash
# Test from your machine at ************
curl http://************:8000/admin/
```

### Step 3: Test Login Endpoint
```bash
# Test login endpoint
curl -X POST http://************:8000/api/auth/token/ \
  -H "Content-Type: application/json" \
  -d '{"username": "your_username", "password": "your_password"}'
```

## Common Issues & Solutions

### Issue 1: CORS Errors in Browser Console
**Symptoms:**
- "Access to XMLHttpRequest at '...' from origin '...' has been blocked by CORS policy"

**Solution:**
- Restart Django server to pick up new CORS settings
- Check that your IP is in `CORS_ALLOWED_ORIGINS`

### Issue 2: Network Unreachable
**Symptoms:**
- "ERR_NETWORK" or "Failed to fetch" errors

**Solution:**
- Verify backend is running on `0.0.0.0:8000` (not just `127.0.0.1:8000`)
- Check firewall settings on the host machine
- Ensure port 8000 is accessible from your network

### Issue 3: Wrong API URL
**Symptoms:**
- Requests going to `localhost:8000` instead of `************:8000`

**Solution:**
- Clear browser cache and localStorage
- Restart frontend development server
- Check browser network tab to see actual request URLs

### Issue 4: Authentication Issues
**Symptoms:**
- "Invalid credentials" with correct username/password

**Solution:**
- Check if you're using the correct login format:
  - Superadmin: `<EMAIL>`
  - Tenant users: `<EMAIL>`
- Verify user exists in the database
- Check Django logs for authentication errors

## Manual Configuration

If the automatic configuration doesn't work, manually update these files:

### 1. Frontend Environment
Create/update `frontend/.env.development`:
```env
VITE_API_URL=http://************:8000
NODE_ENV=development
```

### 2. Django Settings
Update `backend/goid/settings.py`:
```python
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://************:3000",
    "http://************",
]
```

### 3. Docker Compose
Update `docker-compose.yml`:
```yaml
frontend:
  environment:
    - VITE_API_URL=http://************:8000
```

## Testing Commands

### Test Backend Accessibility
```bash
# Test if backend is running
curl http://************:8000/admin/

# Test API endpoint
curl http://************:8000/api/shared/countries/
```

### Test Frontend API Calls
Open browser console on `http://************:3000` and run:
```javascript
// Check current API URL
console.log('API Base URL:', axios.defaults.baseURL);

// Test API call
fetch('/api/shared/countries/')
  .then(response => response.json())
  .then(data => console.log('API Response:', data))
  .catch(error => console.error('API Error:', error));
```

## Network Configuration Script

Use the provided script for easy configuration:
```bash
chmod +x configure-network.sh
./configure-network.sh
```

## Verification Checklist

- [ ] Backend accessible at `http://************:8000/admin/`
- [ ] Frontend loads at `http://************:3000`
- [ ] No CORS errors in browser console
- [ ] API calls go to correct URL (check Network tab)
- [ ] Login form submits to correct endpoint
- [ ] JWT tokens are received and stored

## Still Having Issues?

1. **Check Django logs** for detailed error messages
2. **Check browser console** for JavaScript errors
3. **Check network tab** to see actual request URLs
4. **Verify firewall settings** on the host machine
5. **Test with curl** to isolate frontend vs backend issues

## Contact Support

If issues persist, provide:
- Browser console errors
- Django server logs
- Network tab screenshots
- Output of `curl http://************:8000/admin/`
