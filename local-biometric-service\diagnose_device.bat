@echo off
echo ========================================
echo    FUTRONIC FS88H DEVICE DIAGNOSIS
echo ========================================
echo.
echo This tool will check:
echo   1. USB device detection
echo   2. Device Manager status
echo   3. SDK DLL files
echo   4. SDK connection test
echo.
echo BEFORE RUNNING:
echo   - Make sure Futronic device is connected
echo   - Close any other biometric software
echo   - Run as Administrator if possible
echo.
echo Press any key to start diagnosis...
pause > nul
echo.

python diagnose_device.py

echo.
echo Diagnosis completed.
echo.
echo NEXT STEPS based on results:
echo   - If SDK connection works: Device is fine, LED issue only
echo   - If no USB detection: Check physical connection
echo   - If no SDK connection: Driver or software conflict
echo.
pause
