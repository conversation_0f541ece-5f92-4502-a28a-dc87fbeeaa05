#!/usr/bin/env python3
"""
ALTERNATIVE SDK SERVICE
Uses ftrScanGetImage() instead of ftrScanGetFrame()
This is the higher-level, safer alternative function
"""

import ctypes
from ctypes import cdll, c_int, c_uint, c_void_p, c_ubyte, c_bool, POINTER, byref
import time
import base64
import json
import os
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler

class AlternativeSDKManager:
    """SDK manager using ftrScanGetImage() instead of ftrScanGetFrame()"""
    
    def __init__(self):
        self.scan_api = None
        self.device_handle = None
        self.is_initialized = False
        
        print("Initializing ALTERNATIVE SDK approach...")
        print("Using ftrScanGetImage() instead of ftrScanGetFrame()")
        self._load_sdk_alternative()
    
    def _load_sdk_alternative(self):
        """Load SDK with alternative function approach"""
        try:
            # Load SDK
            print("Step 1: Loading SDK...")
            dll_path = os.path.abspath('./ftrScanAPI.dll')
            self.scan_api = cdll.LoadLibrary(dll_path)
            print("SUCCESS: SDK loaded successfully")
            
            print("Step 2: Configuring ALTERNATIVE function signatures...")
            
            # Device management - standard
            self.scan_api.ftrScanOpenDevice.restype = c_void_p
            self.scan_api.ftrScanOpenDevice.argtypes = []
            
            self.scan_api.ftrScanCloseDevice.restype = c_int
            self.scan_api.ftrScanCloseDevice.argtypes = [c_void_p]
            
            # ALTERNATIVE: Use ftrScanGetImage instead of ftrScanGetFrame
            try:
                self.scan_api.ftrScanGetImage.restype = c_int
                self.scan_api.ftrScanGetImage.argtypes = [c_void_p, POINTER(c_ubyte), POINTER(c_uint)]
                print("SUCCESS: ftrScanGetImage configured (safer alternative)")
                self.has_get_image = True
            except AttributeError:
                print("WARNING: ftrScanGetImage not available")
                self.has_get_image = False
            
            # Finger detection - working signature
            self.scan_api.ftrScanIsFingerPresent.restype = c_int
            self.scan_api.ftrScanIsFingerPresent.argtypes = [c_void_p, POINTER(c_int)]
            
            # Try other alternative functions
            alternative_functions = [
                'ftrScanGetImageEx',
                'ftrScanCaptureImage', 
                'ftrScanAcquireImage',
                'ftrScanTakeImage'
            ]
            
            self.alternative_functions = []
            for func_name in alternative_functions:
                try:
                    func = getattr(self.scan_api, func_name)
                    func.restype = c_int
                    func.argtypes = [c_void_p, POINTER(c_ubyte), POINTER(c_uint)]
                    self.alternative_functions.append(func_name)
                    print("SUCCESS: Found alternative function: " + func_name)
                except AttributeError:
                    pass
            
            print("SUCCESS: Alternative function signatures configured")
            
            print("Step 3: Initializing device...")
            self.device_handle = self.scan_api.ftrScanOpenDevice()
            if self.device_handle:
                self.is_initialized = True
                print("SUCCESS: Device initialized - Handle: " + str(self.device_handle))
            else:
                print("ERROR: Failed to initialize device")
            
        except Exception as e:
            print("ERROR: SDK loading failed: " + str(e))
    
    def is_finger_present_safe(self):
        """Safe finger detection using working signature"""
        if not self.is_initialized:
            return False
        
        try:
            finger_status = c_int(0)
            result = self.scan_api.ftrScanIsFingerPresent(self.device_handle, byref(finger_status))
            return result == 1 and finger_status.value > 0
        except:
            return False
    
    def capture_image_alternative(self):
        """Capture image using alternative functions"""
        if not self.is_initialized:
            return None
        
        # Try ftrScanGetImage first (safer than ftrScanGetFrame)
        if self.has_get_image:
            try:
                print("=== TRYING ftrScanGetImage (SAFER ALTERNATIVE) ===")
                
                buffer_size = 320 * 480  # Standard image size
                image_buffer = (c_ubyte * buffer_size)()
                frame_size = c_uint(buffer_size)
                
                print("Buffer created: " + str(buffer_size) + " bytes")
                print("Calling ftrScanGetImage (safer alternative)...")
                
                result = self.scan_api.ftrScanGetImage(self.device_handle, image_buffer, byref(frame_size))
                
                print("ftrScanGetImage result: " + str(result))
                print("Image size: " + str(frame_size.value))
                
                if result == 0 and frame_size.value > 0:  # Success
                    print("SUCCESS: ftrScanGetImage captured " + str(frame_size.value) + " bytes")
                    
                    image_data = bytes(image_buffer[:frame_size.value])
                    non_zero_bytes = sum(1 for b in image_data if b > 0)
                    quality_score = int((non_zero_bytes / frame_size.value) * 100) if frame_size.value > 0 else 0
                    
                    return {
                        'success': True,
                        'image_data': image_data,
                        'frame_size': frame_size.value,
                        'quality_score': quality_score,
                        'result_code': result,
                        'method': 'ftrScanGetImage',
                        'alternative_approach': True
                    }
                else:
                    print("ftrScanGetImage failed or empty: result=" + str(result) + ", size=" + str(frame_size.value))
                    
            except Exception as e:
                print("ERROR: ftrScanGetImage failed: " + str(e))
        
        # Try other alternative functions
        for func_name in self.alternative_functions:
            try:
                print("=== TRYING " + func_name + " ===")
                
                func = getattr(self.scan_api, func_name)
                buffer_size = 320 * 480
                image_buffer = (c_ubyte * buffer_size)()
                frame_size = c_uint(buffer_size)
                
                print("Calling " + func_name + "...")
                result = func(self.device_handle, image_buffer, byref(frame_size))
                
                print(func_name + " result: " + str(result))
                print("Image size: " + str(frame_size.value))
                
                if result == 0 and frame_size.value > 0:
                    print("SUCCESS: " + func_name + " captured " + str(frame_size.value) + " bytes")
                    
                    image_data = bytes(image_buffer[:frame_size.value])
                    non_zero_bytes = sum(1 for b in image_data if b > 0)
                    quality_score = int((non_zero_bytes / frame_size.value) * 100) if frame_size.value > 0 else 0
                    
                    return {
                        'success': True,
                        'image_data': image_data,
                        'frame_size': frame_size.value,
                        'quality_score': quality_score,
                        'result_code': result,
                        'method': func_name,
                        'alternative_approach': True
                    }
                    
            except Exception as e:
                print("ERROR: " + func_name + " failed: " + str(e))
        
        # If all alternatives fail, try minimal buffer approach
        try:
            print("=== TRYING MINIMAL BUFFER APPROACH ===")
            
            if self.has_get_image:
                buffer_size = 1024  # Minimal buffer like debug
                image_buffer = (c_ubyte * buffer_size)()
                frame_size = c_uint(buffer_size)
                
                print("Trying ftrScanGetImage with minimal buffer...")
                result = self.scan_api.ftrScanGetImage(self.device_handle, image_buffer, byref(frame_size))
                
                if frame_size.value > 0:
                    print("SUCCESS: Minimal buffer captured " + str(frame_size.value) + " bytes")
                    
                    image_data = bytes(image_buffer[:frame_size.value])
                    non_zero_bytes = sum(1 for b in image_data if b > 0)
                    quality_score = int((non_zero_bytes / frame_size.value) * 100) if frame_size.value > 0 else 0
                    
                    return {
                        'success': True,
                        'image_data': image_data,
                        'frame_size': frame_size.value,
                        'quality_score': quality_score,
                        'result_code': result,
                        'method': 'ftrScanGetImage_minimal',
                        'alternative_approach': True
                    }
                    
        except Exception as e:
            print("ERROR: Minimal buffer approach failed: " + str(e))
        
        print("ERROR: All alternative capture methods failed")
        return None
    
    def capture_fingerprint_alternative(self, thumb_type):
        """Complete fingerprint capture using alternative approach"""
        print("=== CAPTURING " + thumb_type.upper() + " THUMB - ALTERNATIVE APPROACH ===")
        
        try:
            # Wait for finger
            print("Waiting for finger placement...")
            finger_detected = False
            
            for i in range(30):  # 15 seconds
                if self.is_finger_present_safe():
                    finger_detected = True
                    print("SUCCESS: Finger detected")
                    break
                time.sleep(0.5)
            
            if not finger_detected:
                print("WARNING: No finger detected - proceeding anyway")
            
            # Capture using alternative methods
            capture_result = self.capture_image_alternative()
            
            if not capture_result or not capture_result.get('success'):
                return {
                    'success': False,
                    'error': 'Alternative capture methods failed',
                    'alternative_approach': True
                }
            
            # Create template data
            image_data = capture_result['image_data']
            quality_score = capture_result['quality_score']
            frame_size = capture_result['frame_size']
            method = capture_result['method']
            
            template_dict = {
                'version': '3.0',
                'thumb_type': thumb_type,
                'image_data': base64.b64encode(image_data).decode(),
                'image_width': 320,
                'image_height': 480,
                'image_dpi': 500,
                'quality_score': quality_score,
                'capture_time': datetime.now().isoformat(),
                'frame_size': frame_size,
                'result_code': capture_result['result_code'],
                'capture_method': method,
                'device_info': {
                    'model': 'Futronic FS88H',
                    'serial_number': 'Handle_' + str(self.device_handle),
                    'sdk_version': 'Alternative Approach',
                    'interface': 'USB'
                },
                'processing_method': 'alternative_sdk_approach',
                'has_template': False,
                'has_image': True,
                'alternative_approach': True
            }
            
            template_data = base64.b64encode(json.dumps(template_dict).encode()).decode()
            
            result = {
                'success': True,
                'thumb_type': thumb_type,
                'template_data': template_data,
                'minutiae_count': min(80, max(20, int((quality_score / 100) * 60) + 20)),
                'quality_score': quality_score,
                'capture_time': template_dict['capture_time'],
                'device_info': template_dict['device_info'],
                'image_data': base64.b64encode(image_data).decode(),
                'frame_size': frame_size,
                'capture_method': method,
                'alternative_approach': True
            }
            
            print("SUCCESS: " + thumb_type + " captured using " + method + " - " + str(frame_size) + " bytes, Quality: " + str(quality_score) + "%")
            return result
            
        except Exception as e:
            print("ERROR: Alternative capture failed: " + str(e))
            return {
                'success': False,
                'error': str(e),
                'alternative_approach': True
            }

# Global SDK manager
sdk_manager = AlternativeSDKManager()

class AlternativeHandler(BaseHTTPRequestHandler):
    """HTTP handler using alternative SDK approach"""
    
    def do_GET(self):
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            
            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Alternative SDK Service</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .info {{ background: #d4edda; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745; }}
                    .btn {{ padding: 10px 20px; margin: 5px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; }}
                    .results {{ background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; font-family: monospace; }}
                </style>
            </head>
            <body>
                <h1>Alternative SDK Service</h1>
                <p>Uses ftrScanGetImage() instead of ftrScanGetFrame()</p>
                
                <div class="info">
                    <h3>Alternative Approach</h3>
                    <p><strong>Problem:</strong> ftrScanGetFrame() causes access violations</p>
                    <p><strong>Solution:</strong> Use ftrScanGetImage() - safer, higher-level function</p>
                    <p><strong>Device Handle:</strong> {sdk_manager.device_handle}</p>
                    <p><strong>Initialized:</strong> {sdk_manager.is_initialized}</p>
                    <p><strong>Has GetImage:</strong> {sdk_manager.has_get_image}</p>
                    <p><strong>Alternative Functions:</strong> {len(sdk_manager.alternative_functions)}</p>
                </div>
                
                <h3>Test Alternative Capture</h3>
                <button class="btn" onclick="testCapture('left')">Test Left Thumb (Alternative)</button>
                <button class="btn" onclick="testCapture('right')">Test Right Thumb (Alternative)</button>
                
                <div id="results" class="results" style="display: none;">
                    <h4>Results:</h4>
                    <pre id="resultsContent"></pre>
                </div>
                
                <script>
                async function testCapture(thumbType) {{
                    const button = event.target;
                    const originalText = button.textContent;
                    
                    button.textContent = 'Capturing with alternative method...';
                    button.disabled = true;
                    
                    try {{
                        const response = await fetch('/capture', {{
                            method: 'POST',
                            headers: {{ 'Content-Type': 'application/json' }},
                            body: JSON.stringify({{ thumb_type: thumbType }})
                        }});
                        
                        const result = await response.json();
                        
                        document.getElementById('results').style.display = 'block';
                        document.getElementById('resultsContent').textContent = JSON.stringify(result, null, 2);
                        
                        if (result.success) {{
                            alert('SUCCESS: ' + thumbType + ' thumb captured with alternative method!\\n\\nMethod: ' + result.capture_method + '\\nFrame Size: ' + result.frame_size + ' bytes\\nQuality: ' + result.quality_score + '%');
                        }} else {{
                            alert('ERROR: Alternative capture failed: ' + result.error);
                        }}
                    }} catch (error) {{
                        alert('ERROR: Network error: ' + error.message);
                    }} finally {{
                        button.textContent = originalText;
                        button.disabled = false;
                    }}
                }}
                </script>
            </body>
            </html>
            """
            
            self.wfile.write(html.encode())
    
    def do_POST(self):
        if self.path == '/capture':
            try:
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length)
                data = json.loads(post_data.decode())
                
                thumb_type = data.get('thumb_type', 'left')
                
                print("\n=== ALTERNATIVE CAPTURE REQUEST: " + thumb_type + " ===")
                
                # Use alternative capture
                result = sdk_manager.capture_fingerprint_alternative(thumb_type)
                
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                
                self.wfile.write(json.dumps(result).encode())
                
            except Exception as e:
                print("ERROR: Request handling error: " + str(e))
                
                self.send_response(500)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                
                error_response = {
                    'success': False,
                    'error': str(e),
                    'alternative_approach': True
                }
                self.wfile.write(json.dumps(error_response).encode())
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def log_message(self, format, *args):
        pass

if __name__ == '__main__':
    try:
        print("=== ALTERNATIVE SDK SERVICE ===")
        print("Using ftrScanGetImage() instead of ftrScanGetFrame()")
        print("Device initialized: " + str(sdk_manager.is_initialized))
        print("Starting server on http://localhost:8001")
        
        server = HTTPServer(('0.0.0.0', 8001), AlternativeHandler)
        server.serve_forever()
        
    except KeyboardInterrupt:
        print("\nAlternative SDK service stopped")
    except Exception as e:
        print("Service error: " + str(e))
    finally:
        if sdk_manager.device_handle:
            try:
                sdk_manager.scan_api.ftrScanCloseDevice(sdk_manager.device_handle)
                print("Device closed")
            except:
                pass
