#!/usr/bin/env python3
"""
Simple script to fix database sequence issues.
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
sys.path.append('/app')
django.setup()

from django.db import connection

def fix_sequences():
    """Fix database sequences."""
    cursor = connection.cursor()
    
    try:
        # Check current sequence values
        cursor.execute("SELECT last_value FROM tenants_subcity_id_seq;")
        subcity_seq = cursor.fetchone()[0]
        print(f"SubCity sequence current value: {subcity_seq}")
        
        cursor.execute("SELECT last_value FROM tenants_kebele_id_seq;")
        kebele_seq = cursor.fetchone()[0]
        print(f"Kebele sequence current value: {kebele_seq}")
        
        # Reset sequences to 1 since there are no records
        cursor.execute("SELECT setval('tenants_subcity_id_seq', 1, false);")
        print("✅ SubCity sequence reset to start from 1")
        
        cursor.execute("SELECT setval('tenants_kebele_id_seq', 1, false);")
        print("✅ Kebele sequence reset to start from 1")
        
        print("\n🎉 All sequences have been fixed!")
        
    except Exception as e:
        print(f"❌ Error fixing sequences: {e}")
        return False
    
    return True

if __name__ == "__main__":
    fix_sequences()
