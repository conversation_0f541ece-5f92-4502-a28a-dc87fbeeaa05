#!/usr/bin/env python3
"""
Transparent Biometric Service Manager
Automatically restarts the biometric service when it crashes without user intervention
"""

import subprocess
import time
import logging
import os
import sys
import signal
import threading
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('service_manager.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BiometricServiceManager:
    def __init__(self):
        self.service_process = None
        self.running = True
        self.restart_count = 0
        self.max_restarts_per_hour = 20  # Prevent infinite restart loops
        self.restart_times = []
        
    def start_service(self):
        """Start the biometric service"""
        try:
            logger.info("Starting biometric service...")

            # Start the service as a subprocess with visible output
            self.service_process = subprocess.Popen(
                [sys.executable, 'improved_biometric_service.py'],
                cwd=os.path.dirname(os.path.abspath(__file__)),
                # Remove stdout/stderr redirection to see service output
                universal_newlines=True
            )

            logger.info(f"Service started with PID: {self.service_process.pid}")

            # Give the service a moment to initialize
            time.sleep(3)

            # Check if service is still running after initialization
            if self.service_process.poll() is None:
                logger.info("Service initialization appears successful")
                return True
            else:
                logger.error("Service failed during initialization")
                return False

        except Exception as e:
            logger.error(f"Failed to start service: {e}")
            return False
    
    def monitor_service(self):
        """Monitor the service and restart if it crashes"""
        while self.running:
            if self.service_process is None:
                # Service not started yet
                if not self.start_service():
                    logger.error("❌ Failed to start service, retrying in 5 seconds...")
                    time.sleep(5)
                    continue
            
            # Check if service is still running
            return_code = self.service_process.poll()
            
            if return_code is not None:
                # Service has stopped
                logger.warning(f"Service stopped with return code: {return_code}")

                # Check restart rate limiting
                if self.should_restart():
                    logger.info("Auto-restarting service...")
                    self.restart_count += 1
                    self.restart_times.append(datetime.now())

                    # Clean up old process
                    self.service_process = None

                    # Brief delay before restart
                    time.sleep(2)

                    # Start new service instance
                    if self.start_service():
                        logger.info(f"Service restarted successfully (restart #{self.restart_count})")
                    else:
                        logger.error("Failed to restart service")
                        time.sleep(10)  # Longer delay on failure
                else:
                    logger.error("Too many restarts, stopping manager")
                    self.running = False
                    break
            
            # Check every 2 seconds
            time.sleep(2)
    
    def should_restart(self):
        """Check if we should restart based on rate limiting"""
        now = datetime.now()
        
        # Remove restart times older than 1 hour
        self.restart_times = [t for t in self.restart_times if (now - t).seconds < 3600]
        
        # Check if we've exceeded the restart limit
        if len(self.restart_times) >= self.max_restarts_per_hour:
            logger.error(f"Restart limit exceeded: {len(self.restart_times)} restarts in the last hour")
            return False
        
        return True
    
    def stop_service(self):
        """Stop the service gracefully"""
        logger.info("Stopping biometric service...")
        self.running = False

        if self.service_process:
            try:
                # Try graceful shutdown first
                self.service_process.terminate()

                # Wait up to 10 seconds for graceful shutdown
                try:
                    self.service_process.wait(timeout=10)
                    logger.info("Service stopped gracefully")
                except subprocess.TimeoutExpired:
                    # Force kill if graceful shutdown fails
                    logger.warning("Forcing service shutdown...")
                    self.service_process.kill()
                    self.service_process.wait()
                    logger.info("Service force-stopped")

            except Exception as e:
                logger.error(f"Error stopping service: {e}")

        logger.info("Service manager stopped")
    
    def get_status(self):
        """Get current service status"""
        if self.service_process is None:
            return "Not running"
        
        return_code = self.service_process.poll()
        if return_code is None:
            return f"Running (PID: {self.service_process.pid})"
        else:
            return f"Stopped (exit code: {return_code})"

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    logger.info(f"Received signal {signum}, shutting down...")
    if 'manager' in globals():
        manager.stop_service()
    sys.exit(0)

def main():
    """Main service manager loop"""
    global manager

    logger.info("Starting Transparent Biometric Service Manager")
    logger.info("=" * 60)
    logger.info("This manager automatically restarts the biometric service")
    logger.info("Transparent operation - no user intervention needed")
    logger.info("Press Ctrl+C to stop the service manager")
    logger.info("=" * 60)
    
    # Set up signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Create and start the service manager
    manager = BiometricServiceManager()
    
    try:
        # Start monitoring in the main thread
        manager.monitor_service()
        
    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received")
        manager.stop_service()
    except Exception as e:
        logger.error(f"Service manager error: {e}")
        manager.stop_service()

    logger.info("Service manager exiting")

if __name__ == '__main__':
    main()
