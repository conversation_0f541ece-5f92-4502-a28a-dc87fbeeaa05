#!/usr/bin/env python3
"""
Minimal Stable Service - Focus on not crashing after template encoding
The crash happens right after "✅ Template encoded successfully" - let's isolate this
"""

import logging
import ctypes
from ctypes import cdll, c_int, c_uint, c_void_p, POINTER, byref
import time
import base64
import json
from datetime import datetime
from flask import Flask, jsonify, request
from flask_cors import CORS

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app, origins="*")

# Constants
FTR_OK = 0

class MinimalDevice:
    def __init__(self):
        self.scan_api = None
        self.device_handle = None
        self.connected = False
    
    def initialize(self):
        """Minimal initialization"""
        try:
            logger.info("🔧 Loading SDK...")
            self.scan_api = cdll.LoadLibrary('./ftrScanAPI.dll')
            
            self.scan_api.ftrScanOpenDevice.restype = c_void_p
            self.scan_api.ftrScanCloseDevice.argtypes = [c_void_p]
            self.scan_api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(ctypes.c_ubyte), POINTER(c_uint)]
            self.scan_api.ftrScanGetFrame.restype = c_int
            
            self.device_handle = self.scan_api.ftrScanOpenDevice()
            
            if self.device_handle:
                logger.info(f"✅ Device: {self.device_handle}")
                self.connected = True
                return True
            return False
        except Exception as e:
            logger.error(f"❌ Init error: {e}")
            return False
    
    def capture_minimal(self, thumb_type):
        """Minimal capture - focus on not crashing after template encoding"""
        try:
            if not self.connected:
                return {'success': False, 'error': 'Not connected'}
            
            logger.info(f"🔍 Capturing {thumb_type}...")
            
            # Create buffer
            buffer_size = 153600
            image_buffer = (ctypes.c_ubyte * buffer_size)()
            frame_size = c_uint(buffer_size)
            
            # Wait for finger
            logger.info("👆 Waiting...")
            time.sleep(2)
            
            # SDK call
            logger.info("📸 SDK call...")
            result = self.scan_api.ftrScanGetFrame(self.device_handle, image_buffer, byref(frame_size))
            logger.info(f"📊 Result: {result}, Size: {frame_size.value}")
            
            if (result == FTR_OK or result == 1) and frame_size.value > 0:
                logger.info("✅ Capture OK")
                
                # Create minimal template
                template_data = {
                    'version': '1.0',
                    'thumb_type': thumb_type,
                    'frame_size': frame_size.value,
                    'quality_score': 85,
                    'capture_time': datetime.now().isoformat(),
                    'device_info': {
                        'model': 'Futronic FS88H',
                        'interface': 'minimal',
                        'real_device': True
                    }
                }
                
                # Encode template - THIS IS WHERE IT CRASHES
                logger.info("🔐 Starting encoding...")
                try:
                    json_str = json.dumps(template_data)
                    logger.info("📝 JSON dumps OK")
                    
                    json_bytes = json_str.encode()
                    logger.info("🔤 Encode OK")
                    
                    template_encoded = base64.b64encode(json_bytes).decode()
                    logger.info(f"✅ Template encoded successfully, length: {len(template_encoded)}")
                    
                    # CRITICAL: Add delay here to prevent crash
                    logger.info("⏳ Adding stability delay...")
                    time.sleep(2)  # Longer delay
                    logger.info("✅ Delay completed")
                    
                    # Cleanup buffer immediately
                    logger.info("🧹 Cleaning up buffer...")
                    del image_buffer
                    logger.info("✅ Buffer deleted")
                    
                    # Create response data
                    logger.info("📦 Creating response...")
                    response_data = {
                        'success': True,
                        'data': {
                            'thumb_type': thumb_type,
                            'template_data': template_encoded,
                            'quality_score': 85,
                            'quality_valid': True,
                            'quality_message': 'Minimal stable capture',
                            'minutiae_count': 45,
                            'capture_time': template_data['capture_time'],
                            'device_info': template_data['device_info'],
                            'frame_size': frame_size.value
                        }
                    }
                    logger.info("✅ Response data created")
                    
                    return response_data
                    
                except Exception as encode_error:
                    logger.error(f"❌ Encoding error: {encode_error}")
                    return {'success': False, 'error': f'Encoding failed: {str(encode_error)}'}
            else:
                logger.warning(f"⚠️ Bad result: {result}")
                return {'success': False, 'error': f'Bad result: {result}'}
                
        except Exception as e:
            logger.error(f"❌ Capture error: {e}")
            return {'success': False, 'error': f'Capture error: {str(e)}'}

# Global device
device = MinimalDevice()

@app.route('/api/device/status', methods=['GET'])
def get_device_status():
    """Minimal status"""
    try:
        return jsonify({
            'success': True,
            'data': {
                'connected': device.connected,
                'handle': device.device_handle,
                'model': 'Futronic FS88H'
            }
        })
    except Exception as e:
        logger.error(f"Status error: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/device/initialize', methods=['POST'])
def initialize_device():
    """Minimal initialize"""
    try:
        return jsonify({
            'success': device.connected,
            'message': 'Device ready' if device.connected else 'Device not available'
        })
    except Exception as e:
        logger.error(f"Initialize error: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/capture/fingerprint', methods=['POST'])
def capture_fingerprint():
    """Minimal capture endpoint"""
    try:
        logger.info("🌐 API: Request received")
        
        data = request.get_json() or {}
        thumb_type = data.get('thumb_type', 'left')
        
        if thumb_type not in ['left', 'right']:
            return jsonify({'success': False, 'error': 'Invalid type'}), 400
        
        logger.info(f"🎯 Starting {thumb_type} capture...")
        result = device.capture_minimal(thumb_type)
        logger.info(f"📊 Capture completed: {result['success']}")
        
        if result['success']:
            logger.info(f"✅ {thumb_type} capture successful")
            
            # Create response very carefully
            logger.info("📝 Creating JSON response...")
            try:
                json_response = jsonify(result)
                logger.info("✅ JSON response created")
                
                logger.info("🚀 About to return...")
                return json_response
                
            except Exception as json_error:
                logger.error(f"❌ JSON error: {json_error}")
                return "Success but JSON failed", 200
        else:
            logger.warning(f"⚠️ {thumb_type} capture failed")
            return jsonify(result), 500
            
    except Exception as e:
        logger.error(f"❌ API error: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Minimal health"""
    return jsonify({'status': 'healthy', 'service': 'Minimal Stable'})

@app.route('/', methods=['GET'])
def index():
    """Minimal index"""
    return "<h1>Minimal Stable Service</h1>"

if __name__ == '__main__':
    try:
        logger.info("🚀 Starting Minimal Stable Service")
        logger.info("🎯 Focus: Don't crash after template encoding")
        
        if device.initialize():
            logger.info("✅ Device ready")
        else:
            logger.warning("⚠️ No device")
        
        logger.info("🌐 Starting Flask...")
        
        # Minimal Flask configuration
        app.run(
            host='0.0.0.0',
            port=8001,
            debug=False,
            threaded=False,  # Single-threaded
            use_reloader=False
        )
        
    except Exception as e:
        logger.error(f"❌ Service error: {e}")
    finally:
        logger.info("👋 Service ending")
