import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableRow
} from '@mui/material';

const TransferPrintView = ({ transfer }) => {
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const printStyles = {
    '@media print': {
      body: { margin: 0 },
      '@page': { margin: '1in' }
    }
  };

  return (
    <Box sx={{ p: 4, maxWidth: '8.5in', margin: '0 auto', ...printStyles }}>
      {/* Header */}
      <Box sx={{ textAlign: 'center', mb: 4 }}>
        <Typography variant="h4" fontWeight="bold" gutterBottom>
          FEDERAL DEMOCRATIC REPUBLIC OF ETHIOPIA
        </Typography>
        <Typography variant="h5" fontWeight="bold" gutterBottom>
          CITIZEN TRANSFER REQUEST
        </Typography>
        <Typography variant="h6" color="text.secondary">
          Transfer ID: {transfer.transfer_id}
        </Typography>
      </Box>

      <Divider sx={{ mb: 3 }} />

      {/* Transfer Information */}
      <Paper elevation={0} sx={{ p: 3, mb: 3, border: '1px solid #ddd' }}>
        <Typography variant="h6" fontWeight="bold" gutterBottom>
          TRANSFER INFORMATION
        </Typography>
        
        <Table size="small">
          <TableBody>
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold', width: '30%' }}>Transfer ID:</TableCell>
              <TableCell>{transfer.transfer_id}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold' }}>Status:</TableCell>
              <TableCell>{transfer.status_display}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold' }}>From Kebele:</TableCell>
              <TableCell>{transfer.source_kebele_info?.name || 'Unknown'}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold' }}>To Kebele:</TableCell>
              <TableCell>{transfer.destination_kebele_info?.name || 'Unknown'}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold' }}>Transfer Reason:</TableCell>
              <TableCell>{transfer.transfer_reason_display}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold' }}>Request Date:</TableCell>
              <TableCell>{formatDate(transfer.created_at)}</TableCell>
            </TableRow>
          </TableBody>
        </Table>

        {transfer.reason_description && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="subtitle2" fontWeight="bold">
              Detailed Reason:
            </Typography>
            <Typography variant="body2" sx={{ mt: 1, p: 2, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
              {transfer.reason_description}
            </Typography>
          </Box>
        )}
      </Paper>

      {/* Citizen Information */}
      <Paper elevation={0} sx={{ p: 3, mb: 3, border: '1px solid #ddd' }}>
        <Typography variant="h6" fontWeight="bold" gutterBottom>
          CITIZEN INFORMATION
        </Typography>
        
        <Table size="small">
          <TableBody>
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold', width: '30%' }}>Full Name:</TableCell>
              <TableCell>{transfer.citizen_name}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold' }}>Digital ID:</TableCell>
              <TableCell>{transfer.citizen_digital_id || transfer.citizen_id}</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </Paper>

      {/* Documents Status */}
      <Paper elevation={0} sx={{ p: 3, mb: 3, border: '1px solid #ddd' }}>
        <Typography variant="h6" fontWeight="bold" gutterBottom>
          REQUIRED DOCUMENTS
        </Typography>
        
        <Table size="small">
          <TableBody>
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold', width: '30%' }}>Application Letter:</TableCell>
              <TableCell>
                {transfer.application_letter ? '✓ Submitted' : '✗ Not Submitted'}
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold' }}>Current Kebele ID:</TableCell>
              <TableCell>
                {transfer.current_kebele_id ? '✓ Submitted' : '✗ Not Submitted'}
              </TableCell>
            </TableRow>
            {transfer.documents_uploaded_at && (
              <TableRow>
                <TableCell sx={{ fontWeight: 'bold' }}>Documents Uploaded:</TableCell>
                <TableCell>{formatDate(transfer.documents_uploaded_at)}</TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </Paper>

      {/* Review Information */}
      {(transfer.reviewed_at || transfer.review_notes) && (
        <Paper elevation={0} sx={{ p: 3, mb: 3, border: '1px solid #ddd' }}>
          <Typography variant="h6" fontWeight="bold" gutterBottom>
            REVIEW INFORMATION
          </Typography>
          
          <Table size="small">
            <TableBody>
              {transfer.reviewed_by_username && (
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold', width: '30%' }}>Reviewed By:</TableCell>
                  <TableCell>{transfer.reviewed_by_username}</TableCell>
                </TableRow>
              )}
              {transfer.reviewed_at && (
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold' }}>Review Date:</TableCell>
                  <TableCell>{formatDate(transfer.reviewed_at)}</TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>

          {transfer.review_notes && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" fontWeight="bold">
                Review Notes:
              </Typography>
              <Typography variant="body2" sx={{ mt: 1, p: 2, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
                {transfer.review_notes}
              </Typography>
            </Box>
          )}
        </Paper>
      )}

      {/* Signatures */}
      <Box sx={{ mt: 4 }}>
        <Grid container spacing={4}>
          <Grid item xs={6}>
            <Box sx={{ textAlign: 'center' }}>
              <Box sx={{ height: '60px', borderBottom: '1px solid #000', mb: 1 }} />
              <Typography variant="body2" fontWeight="bold">
                Source Kebele Leader Signature
              </Typography>
              <Typography variant="caption">
                {transfer.source_kebele_info?.name}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box sx={{ textAlign: 'center' }}>
              <Box sx={{ height: '60px', borderBottom: '1px solid #000', mb: 1 }} />
              <Typography variant="body2" fontWeight="bold">
                Destination Kebele Leader Signature
              </Typography>
              <Typography variant="caption">
                {transfer.destination_kebele_info?.name}
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Box>

      {/* Footer */}
      <Box sx={{ mt: 4, textAlign: 'center' }}>
        <Typography variant="caption" color="text.secondary">
          This document was generated on {new Date().toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          })}
        </Typography>
      </Box>
    </Box>
  );
};

export default TransferPrintView;
