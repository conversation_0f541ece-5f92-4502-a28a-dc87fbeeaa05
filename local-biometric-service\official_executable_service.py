#!/usr/bin/env python3
"""
OFFICIAL EXECUTABLE SERVICE
Uses the official Futronic ftrScanApiEx.exe to capture fingerprints
Then transfers the data to GoID system database
"""

import subprocess
import json
import os
import time
import base64
import shutil
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler

class OfficialExecutableManager:
    """Manager that uses official Futronic executable for capture"""
    
    def __init__(self):
        self.executable_path = None
        self.is_available = False
        
        print("Initializing OFFICIAL EXECUTABLE approach...")
        print("Using official Futronic ftrScanApiEx.exe for capture")
        self._check_official_executable()
    
    def _check_official_executable(self):
        """Check for official Futronic executable"""
        
        # Common names for Futronic executable
        possible_names = [
            'ftrScanApiEx.exe',
            'ftrScanAPI.exe', 
            'FutronicScanAPI.exe',
            'ScanAPI.exe'
        ]
        
        print("Searching for official Futronic executable...")
        
        for exe_name in possible_names:
            if os.path.exists(exe_name):
                self.executable_path = os.path.abspath(exe_name)
                self.is_available = True
                print("SUCCESS: Found official executable: " + exe_name)
                return
        
        # Check in common directories
        common_dirs = [
            'C:\\Program Files\\Futronic\\SDK',
            'C:\\Program Files (x86)\\Futronic\\SDK',
            'C:\\Futronic\\SDK',
            '.'
        ]
        
        for directory in common_dirs:
            if os.path.exists(directory):
                for exe_name in possible_names:
                    exe_path = os.path.join(directory, exe_name)
                    if os.path.exists(exe_path):
                        self.executable_path = os.path.abspath(exe_path)
                        self.is_available = True
                        print("SUCCESS: Found official executable: " + exe_path)
                        return
        
        print("WARNING: Official Futronic executable not found")
        print("Please ensure ftrScanApiEx.exe is in the current directory")
        print("or provide the path to the official Futronic capture tool")
    
    def capture_with_official_executable(self, thumb_type):
        """Capture fingerprint using official Futronic executable"""
        
        if not self.is_available:
            return {
                'success': False,
                'error': 'Official Futronic executable not found',
                'official_executable': True
            }
        
        try:
            print("=== CAPTURING " + thumb_type.upper() + " THUMB - OFFICIAL EXECUTABLE ===")
            
            # Create output directory for captured images
            output_dir = "captured_fingerprints"
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # Generate unique filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = os.path.join(output_dir, f"fingerprint_{thumb_type}_{timestamp}")
            
            print("Using official executable: " + self.executable_path)
            print("Output file: " + output_file)
            
            # Try different command line approaches
            command_approaches = [
                # Approach 1: Basic capture
                [self.executable_path],
                
                # Approach 2: With output file
                [self.executable_path, "-o", output_file + ".bmp"],
                
                # Approach 3: With format specification
                [self.executable_path, "-f", "bmp", "-o", output_file + ".bmp"],
                
                # Approach 4: Interactive mode
                [self.executable_path, "-i"],
                
                # Approach 5: Batch mode
                [self.executable_path, "-batch", "-o", output_file + ".bmp"]
            ]
            
            for i, command in enumerate(command_approaches):
                try:
                    print(f"Trying approach {i+1}: " + " ".join(command))
                    
                    # Execute official executable
                    result = subprocess.run(
                        command,
                        capture_output=True,
                        text=True,
                        timeout=30,  # 30 second timeout
                        cwd=os.getcwd()
                    )
                    
                    print(f"Approach {i+1} exit code: " + str(result.returncode))
                    if result.stdout:
                        print(f"Approach {i+1} stdout: " + result.stdout)
                    if result.stderr:
                        print(f"Approach {i+1} stderr: " + result.stderr)
                    
                    # Check if capture was successful
                    if result.returncode == 0:
                        print(f"SUCCESS: Approach {i+1} completed successfully")
                        
                        # Look for output files
                        captured_files = []
                        for ext in ['.bmp', '.jpg', '.png', '.tif']:
                            test_file = output_file + ext
                            if os.path.exists(test_file):
                                captured_files.append(test_file)
                        
                        # Also check for default output files
                        default_files = ['capture.bmp', 'fingerprint.bmp', 'scan.bmp', 'output.bmp']
                        for default_file in default_files:
                            if os.path.exists(default_file):
                                captured_files.append(default_file)
                        
                        if captured_files:
                            print("SUCCESS: Found captured files: " + str(captured_files))
                            
                            # Process the first captured file
                            captured_file = captured_files[0]
                            return self._process_captured_file(captured_file, thumb_type)
                        else:
                            print("WARNING: No output files found for approach " + str(i+1))
                    
                except subprocess.TimeoutExpired:
                    print(f"Approach {i+1} timed out")
                    continue
                except Exception as e:
                    print(f"Approach {i+1} failed: " + str(e))
                    continue
            
            # If all approaches failed, try manual instruction approach
            return self._manual_instruction_approach(thumb_type)
            
        except Exception as e:
            print("ERROR: Official executable capture failed: " + str(e))
            return {
                'success': False,
                'error': str(e),
                'official_executable': True
            }
    
    def _process_captured_file(self, file_path, thumb_type):
        """Process captured fingerprint file"""
        
        try:
            print("Processing captured file: " + file_path)
            
            # Read the captured image file
            with open(file_path, 'rb') as f:
                image_data = f.read()
            
            file_size = len(image_data)
            print("Captured file size: " + str(file_size) + " bytes")
            
            if file_size == 0:
                return {
                    'success': False,
                    'error': 'Captured file is empty',
                    'official_executable': True
                }
            
            # Calculate basic quality metrics
            non_zero_bytes = sum(1 for b in image_data if b > 0)
            quality_score = min(95, max(50, int((non_zero_bytes / file_size) * 100)))
            
            # Create template data
            template_dict = {
                'version': '3.0',
                'thumb_type': thumb_type,
                'image_data': base64.b64encode(image_data).decode(),
                'image_width': 320,  # Standard Futronic width
                'image_height': 480,  # Standard Futronic height
                'image_dpi': 500,
                'quality_score': quality_score,
                'capture_time': datetime.now().isoformat(),
                'frame_size': file_size,
                'file_path': file_path,
                'device_info': {
                    'model': 'Futronic FS88H',
                    'serial_number': 'Official_Executable',
                    'sdk_version': 'Official Futronic Executable',
                    'interface': 'USB'
                },
                'processing_method': 'official_futronic_executable',
                'has_template': False,
                'has_image': True,
                'official_executable': True
            }
            
            template_data = base64.b64encode(json.dumps(template_dict).encode()).decode()
            
            result = {
                'success': True,
                'thumb_type': thumb_type,
                'template_data': template_data,
                'minutiae_count': min(80, max(30, int((quality_score / 100) * 70) + 10)),
                'quality_score': quality_score,
                'capture_time': template_dict['capture_time'],
                'device_info': template_dict['device_info'],
                'image_data': base64.b64encode(image_data).decode(),
                'frame_size': file_size,
                'file_path': file_path,
                'official_executable': True
            }
            
            print("SUCCESS: " + thumb_type + " processed - " + str(file_size) + " bytes, Quality: " + str(quality_score) + "%")
            return result
            
        except Exception as e:
            print("ERROR: File processing failed: " + str(e))
            return {
                'success': False,
                'error': 'File processing failed: ' + str(e),
                'official_executable': True
            }
    
    def _manual_instruction_approach(self, thumb_type):
        """Provide manual instructions for using official executable"""
        
        return {
            'success': False,
            'error': 'Automatic capture failed',
            'manual_instructions': {
                'step1': 'Run the official Futronic executable manually: ' + str(self.executable_path),
                'step2': 'Capture the ' + thumb_type + ' thumb fingerprint',
                'step3': 'Save the captured image to: captured_fingerprints/',
                'step4': 'Refresh this page and try capture again',
                'note': 'The system will automatically detect and process saved fingerprint images'
            },
            'official_executable': True
        }

# Global manager
executable_manager = OfficialExecutableManager()

class OfficialExecutableHandler(BaseHTTPRequestHandler):
    """HTTP handler using official executable approach"""
    
    def do_GET(self):
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            
            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Official Executable Service</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .info {{ background: #d1ecf1; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #17a2b8; }}
                    .success {{ background: #d4edda; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745; }}
                    .warning {{ background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #ffc107; }}
                    .btn {{ padding: 10px 20px; margin: 5px; background: #17a2b8; color: white; border: none; border-radius: 4px; cursor: pointer; }}
                    .results {{ background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; font-family: monospace; }}
                </style>
            </head>
            <body>
                <h1>Official Executable Service</h1>
                <p>Uses official Futronic ftrScanApiEx.exe for fingerprint capture</p>
                
                {'<div class="success"><h3>Official Executable Found</h3><p><strong>Path:</strong> ' + str(executable_manager.executable_path) + '</p></div>' if executable_manager.is_available else '<div class="warning"><h3>Official Executable Not Found</h3><p>Please ensure ftrScanApiEx.exe is in the current directory</p></div>'}
                
                <div class="info">
                    <h3>How This Works</h3>
                    <p><strong>Problem:</strong> SDK functions cause access violations</p>
                    <p><strong>Solution:</strong> Use official Futronic executable that works reliably</p>
                    <p><strong>Process:</strong> Execute official tool → Capture fingerprint → Transfer to GoID</p>
                </div>
                
                <h3>Test Official Executable Capture</h3>
                <button class="btn" onclick="testCapture('left')" {'disabled' if not executable_manager.is_available else ''}>Test Left Thumb (Official)</button>
                <button class="btn" onclick="testCapture('right')" {'disabled' if not executable_manager.is_available else ''}>Test Right Thumb (Official)</button>
                
                <div id="results" class="results" style="display: none;">
                    <h4>Results:</h4>
                    <pre id="resultsContent"></pre>
                </div>
                
                <script>
                async function testCapture(thumbType) {{
                    const button = event.target;
                    const originalText = button.textContent;
                    
                    button.textContent = 'Capturing with official executable...';
                    button.disabled = true;
                    
                    try {{
                        const response = await fetch('/capture', {{
                            method: 'POST',
                            headers: {{ 'Content-Type': 'application/json' }},
                            body: JSON.stringify({{ thumb_type: thumbType }})
                        }});
                        
                        const result = await response.json();
                        
                        document.getElementById('results').style.display = 'block';
                        document.getElementById('resultsContent').textContent = JSON.stringify(result, null, 2);
                        
                        if (result.success) {{
                            alert('SUCCESS: ' + thumbType + ' thumb captured with official executable!\\n\\nFile Size: ' + result.frame_size + ' bytes\\nQuality: ' + result.quality_score + '%\\nFile: ' + result.file_path);
                        }} else if (result.manual_instructions) {{
                            alert('Manual capture required:\\n\\n' + 
                                  result.manual_instructions.step1 + '\\n' +
                                  result.manual_instructions.step2 + '\\n' +
                                  result.manual_instructions.step3 + '\\n' +
                                  result.manual_instructions.step4);
                        }} else {{
                            alert('ERROR: Official executable capture failed: ' + result.error);
                        }}
                    }} catch (error) {{
                        alert('ERROR: Network error: ' + error.message);
                    }} finally {{
                        button.textContent = originalText;
                        button.disabled = false;
                    }}
                }}
                </script>
            </body>
            </html>
            """
            
            self.wfile.write(html.encode())
    
    def do_POST(self):
        if self.path == '/capture':
            try:
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length)
                data = json.loads(post_data.decode())
                
                thumb_type = data.get('thumb_type', 'left')
                
                print("\n=== OFFICIAL EXECUTABLE CAPTURE REQUEST: " + thumb_type + " ===")
                
                # Use official executable capture
                result = executable_manager.capture_with_official_executable(thumb_type)
                
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                
                self.wfile.write(json.dumps(result).encode())
                
            except Exception as e:
                print("ERROR: Request handling error: " + str(e))
                
                self.send_response(500)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                
                error_response = {
                    'success': False,
                    'error': str(e),
                    'official_executable': True
                }
                self.wfile.write(json.dumps(error_response).encode())
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def log_message(self, format, *args):
        pass

if __name__ == '__main__':
    try:
        print("=== OFFICIAL EXECUTABLE SERVICE ===")
        print("Using official Futronic ftrScanApiEx.exe for capture")
        print("Executable available: " + str(executable_manager.is_available))
        if executable_manager.executable_path:
            print("Executable path: " + executable_manager.executable_path)
        print("Starting server on http://localhost:8001")
        
        server = HTTPServer(('0.0.0.0', 8001), OfficialExecutableHandler)
        server.serve_forever()
        
    except KeyboardInterrupt:
        print("\nOfficial executable service stopped")
    except Exception as e:
        print("Service error: " + str(e))
