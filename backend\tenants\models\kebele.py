from django.db import models
from django.conf import settings as django_settings
from ..utils import Timestamp
from .subcity import SubCity

class Kebele(Timestamp):
    # Kebele model to represent a neighborhood within a sub-city
    sub_city = models.ForeignKey(SubCity, on_delete=models.CASCADE, related_name="kebeles", null=True)  # Relating to the SubCity model
    name = models.CharField(max_length=100, default="Kebele")
    name_am = models.CharField(max_length=100, blank=True, null=True, verbose_name="Name (Amharic)")
    code = models.CharField(max_length=10, unique=True, blank=True, null=True)
    is_active = models.BooleanField(default=True)  # Boolean field for activity status
    tenant = models.OneToOneField('tenants.Tenant', on_delete=models.CASCADE, related_name='kebele_profile')

    # ID Card customization fields
    logo = models.ImageField(
        upload_to='logos/kebele/',
        blank=True,
        null=True,
        verbose_name="Kebele Logo",
        help_text="Official logo for kebele administration (recommended: 200x200px, PNG/JPG)"
    )
    mayor_signature = models.ImageField(
        upload_to='signatures/kebele/',
        blank=True,
        null=True,
        verbose_name="Mayor/Leader Signature",
        help_text="Signature image for ID cards (recommended: 200x80px, PNG/JPG)"
    )
    pattern_image = models.ImageField(
        upload_to='patterns/kebele/',
        blank=True,
        null=True,
        verbose_name="Security Pattern Image",
        help_text="Custom security pattern for ID cards (recommended: 500x300px, PNG with transparency)"
    )

    # Auto-generated code logic for kebele
    def save(self, *args, **kwargs):
        # Generate code only if it doesn't exist
        if not self.code:
            if not self.id:
                # For new instances, save first to get ID, then update code
                super().save(*args, **kwargs)
                self.code = f"GD-KB{self.id:02d}"  # Generates code like GD-KB01, GD-KB02, etc.
                # Update only the code field to avoid ID conflicts
                super().save(update_fields=['code'])
                return
            else:
                # For existing instances, just set the code
                self.code = f"GD-KB{self.id:02d}"

        # Normal save for all other cases
        super().save(*args, **kwargs)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Kebele"
        verbose_name_plural = "Kebeles"

class Ketena(Timestamp):
    # Ketena model to represent a smaller administrative division within a kebele
    kebele = models.ForeignKey(Kebele, on_delete=models.CASCADE, related_name="ketenes")  # Relating to the Kebele model
    name = models.CharField(max_length=100)
    name_am = models.CharField(max_length=100, blank=True, null=True, verbose_name="Name (Amharic)")
    code = models.CharField(max_length=10, unique=True, blank=True, null=True)
    is_active = models.BooleanField(default=True)  # Boolean field for activity status

    # Auto-generated code logic for ketena
    def save(self, *args, **kwargs):
        # Generate code only if it doesn't exist
        if not self.code:
            if not self.id:
                # For new instances, save first to get ID, then update code
                super().save(*args, **kwargs)
                self.code = f"GD-KT{self.id:02d}"  # Generates code like GD-KT01, GD-KT02, etc.
                # Update only the code field to avoid ID conflicts
                super().save(update_fields=['code'])
                return
            else:
                # For existing instances, just set the code
                self.code = f"GD-KT{self.id:02d}"

        # Normal save for all other cases
        super().save(*args, **kwargs)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Ketena"
        verbose_name_plural = "Ketenes"
