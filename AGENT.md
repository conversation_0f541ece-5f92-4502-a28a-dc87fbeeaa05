# GoID Agent Guidelines

## Commands
- **Backend**: `cd backend && python manage.py runserver` | `python manage.py test` | `python manage.py test <app_name>`
- **Frontend**: `cd frontend && npm run dev` | `npm run build` | `npm run lint`
- **Docker**: `docker-compose up` | `docker-compose -f docker-compose.production.yml up`
- **Migrations**: `python manage.py makemigrations` | `python manage.py migrate`
- **Test single app**: `cd backend && python manage.py test citizens` | `python manage.py test biometrics.tests`

## Architecture
- **Multi-tenant Django app** with PostgreSQL using `django-tenants`
- **Backend**: Django REST Framework (DRF) with JWT auth, tenant-aware middleware
- **Frontend**: React with Vite, Material-UI, TailwindCSS
- **Key apps**: `citizens`, `idcards`, `workflows`, `biometrics`, `users`, `tenants`
- **Database**: PostgreSQL with tenant schemas, shared apps vs tenant apps
- **Biometric devices**: USB device integration for fingerprint capture

## Code Style
- **Python**: Django conventions, snake_case variables, PascalCase classes
- **Imports**: Standard library, third-party, local (with blank lines between)
- **Strings**: Use `_('text')` for translation, f-strings for formatting
- **Error handling**: Custom exception handler in `common.utils`
- **API responses**: DRF Response with proper status codes
- **JavaScript**: ES6+, camelCase, arrow functions, async/await

## Notes
- Use tenant-aware authentication/permissions
- Ethiopian calendar system support
- Bilingual (English/Amharic) interface
- Biometric capture with quality validation
