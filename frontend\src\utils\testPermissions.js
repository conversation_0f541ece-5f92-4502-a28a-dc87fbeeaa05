/**
 * Test utility to verify permission mappings work correctly
 */

import { PERMISSION_MAPPING, ROLE_PERMISSIONS } from '../hooks/usePermissions';

export const testPermissionMappings = () => {
  console.log('🧪 Testing Permission Mappings...');
  
  // Test role-based permissions
  const roles = ['clerk', 'kebele_leader', 'subcity_admin', 'city_admin', 'superadmin'];
  
  roles.forEach(role => {
    console.log(`\n👤 ${role.toUpperCase()} PERMISSIONS:`);
    const permissions = ROLE_PERMISSIONS[role] || [];
    
    permissions.forEach(permission => {
      const backendPerms = PERMISSION_MAPPING[permission] || [];
      console.log(`  ✅ ${permission} → [${backendPerms.join(', ')}]`);
    });
  });
  
  // Test specific scenarios
  console.log('\n🎯 TESTING SPECIFIC SCENARIOS:');
  
  // Clerk should have basic permissions
  const clerkPerms = ROLE_PERMISSIONS.clerk || [];
  console.log(`\n📋 Clerk Navigation Items:`);
  clerkPerms.forEach(perm => {
    console.log(`  - ${perm}`);
  });
  
  // Subcity admin should have cross-kebele access
  const subcityPerms = ROLE_PERMISSIONS.subcity_admin || [];
  console.log(`\n🏢 Subcity Admin Navigation Items:`);
  subcityPerms.forEach(perm => {
    console.log(`  - ${perm}`);
  });
  
  // Test permission hierarchy
  console.log('\n📊 PERMISSION HIERARCHY TEST:');
  console.log('Clerk can register citizens:', clerkPerms.includes('register_citizens'));
  console.log('Kebele leader can approve ID cards:', (ROLE_PERMISSIONS.kebele_leader || []).includes('approve_idcards'));
  console.log('Subcity admin can manage users:', subcityPerms.includes('manage_users'));
  console.log('City admin can view cross-subcity:', (ROLE_PERMISSIONS.city_admin || []).includes('view_cross_subcity'));
  
  return {
    roles,
    permissionMappings: PERMISSION_MAPPING,
    rolePermissions: ROLE_PERMISSIONS
  };
};

export const getNavigationForRole = (role) => {
  const permissions = ROLE_PERMISSIONS[role] || [];
  const navigation = [];
  
  // Dashboard
  if (permissions.includes('view_dashboard')) {
    navigation.push('Dashboard');
  }
  
  // Citizens
  if (permissions.includes('view_citizens')) {
    const citizenItems = ['Citizens List'];
    if (permissions.includes('register_citizens')) {
      citizenItems.push('Register Citizen');
    }
    navigation.push(`Citizens (${citizenItems.join(', ')})`);
  }
  
  // ID Cards
  if (permissions.includes('view_idcards')) {
    const idCardItems = ['ID Cards List'];
    if (permissions.includes('create_idcards')) {
      idCardItems.push('Generate ID Cards');
    }
    if (permissions.includes('approve_idcards')) {
      idCardItems.push('Approve ID Cards');
    }
    navigation.push(`ID Cards (${idCardItems.join(', ')})`);
  }
  
  // Reports
  if (permissions.includes('view_reports')) {
    navigation.push('Reports');
  }
  
  // Users
  if (permissions.includes('manage_users')) {
    navigation.push('Users');
  }
  
  // Workflows
  if (permissions.includes('view_workflows')) {
    const workflowItems = [];
    if (permissions.includes('verify_documents')) {
      workflowItems.push('Document Verification');
    }
    if (permissions.includes('transfer_citizens')) {
      workflowItems.push('Citizen Transfers');
    }
    navigation.push(`Workflows (${workflowItems.join(', ')})`);
  }
  
  // Tenants (Super admin only)
  if (permissions.includes('manage_tenants')) {
    navigation.push('Tenants');
  }
  
  return navigation;
};

export const compareRoleNavigation = () => {
  console.log('\n🔍 ROLE NAVIGATION COMPARISON:');
  
  const roles = ['clerk', 'kebele_leader', 'subcity_admin', 'city_admin', 'superadmin'];
  
  roles.forEach(role => {
    console.log(`\n👤 ${role.toUpperCase()}:`);
    const navigation = getNavigationForRole(role);
    navigation.forEach(item => {
      console.log(`  ✅ ${item}`);
    });
  });
};

// Export for console testing
if (typeof window !== 'undefined') {
  window.testPermissions = testPermissionMappings;
  window.compareRoleNavigation = compareRoleNavigation;
  window.getNavigationForRole = getNavigationForRole;
}
