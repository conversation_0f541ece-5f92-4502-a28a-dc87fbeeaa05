#!/usr/bin/env python3
"""
Django management command to debug navigation issues for admin users
"""
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django_tenants.utils import schema_context, get_public_schema_name
from users.models_groups import TenantGroup, GroupMembership
from tenants.models import Tenant, TenantType
import json

User = get_user_model()

class Command(BaseCommand):
    help = 'Debug navigation issues for admin users'

    def add_arguments(self, parser):
        parser.add_argument(
            '--email',
            type=str,
            help='Email of specific user to debug',
        )
        parser.add_argument(
            '--fix',
            action='store_true',
            help='Fix group assignments for users',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🔍 Starting Navigation Debug'))
        
        # Debug specific user or all admin users
        if options['email']:
            self.debug_specific_user(options['email'], options.get('fix', False))
        else:
            self.debug_all_admin_users(options.get('fix', False))

    def debug_specific_user(self, email, fix_issues=False):
        """Debug a specific user"""
        self.stdout.write(f'\n🔍 Debugging user: {email}')
        
        # Find user across all schemas
        user = None
        user_tenant = None
        
        # Check public schema first
        with schema_context(get_public_schema_name()):
            try:
                user = User.objects.get(email=email)
                self.stdout.write(f'  ✅ Found user in public schema')
            except User.DoesNotExist:
                pass
        
        # Check tenant schemas
        if not user:
            for tenant in Tenant.objects.all():
                try:
                    with schema_context(tenant.schema_name):
                        user = User.objects.get(email=email)
                        user_tenant = tenant
                        self.stdout.write(f'  ✅ Found user in {tenant.name} ({tenant.schema_name})')
                        break
                except User.DoesNotExist:
                    continue
        
        if not user:
            self.stdout.write(self.style.ERROR(f'  ❌ User {email} not found'))
            return
        
        # Debug user details
        self.stdout.write(f'  📋 User Details:')
        self.stdout.write(f'    - Email: {user.email}')
        self.stdout.write(f'    - Username: {user.username}')
        self.stdout.write(f'    - Role: {user.role}')
        self.stdout.write(f'    - Is Superuser: {user.is_superuser}')
        self.stdout.write(f'    - Is Active: {user.is_active}')
        self.stdout.write(f'    - Tenant: {user_tenant.name if user_tenant else "None"}')
        
        # Check group memberships
        with schema_context(get_public_schema_name()):
            memberships = GroupMembership.objects.filter(
                user_email=user.email,
                is_active=True
            ).select_related('group')
            
            self.stdout.write(f'  👥 Group Memberships ({memberships.count()}):')
            for membership in memberships:
                self.stdout.write(f'    - {membership.group.name} (Primary: {membership.is_primary})')
            
            if not memberships.exists():
                self.stdout.write(f'    ❌ No group memberships found!')
                
                if fix_issues:
                    self.fix_user_group_assignment(user, user_tenant)
            
            # Check available groups for user's role
            expected_group = self.get_expected_group_for_role(user.role)
            if expected_group:
                try:
                    group = TenantGroup.objects.get(group__name=expected_group)
                    has_membership = memberships.filter(group=group).exists()
                    self.stdout.write(f'  🎯 Expected Group: {expected_group} (Has: {has_membership})')
                    
                    if not has_membership and fix_issues:
                        self.stdout.write(f'  🔧 Assigning user to {expected_group} group...')
                        self.assign_user_to_group(user, group, user_tenant)
                        
                except TenantGroup.DoesNotExist:
                    self.stdout.write(f'  ❌ Expected group {expected_group} not found!')

    def debug_all_admin_users(self, fix_issues=False):
        """Debug all admin users"""
        self.stdout.write('\n🔍 Debugging all admin users')
        
        admin_roles = ['city_admin', 'subcity_admin', 'superadmin']
        
        for tenant in Tenant.objects.all():
            self.stdout.write(f'\n📍 Checking tenant: {tenant.name} ({tenant.type})')
            
            with schema_context(tenant.schema_name):
                admin_users = User.objects.filter(role__in=admin_roles)
                
                for user in admin_users:
                    self.stdout.write(f'  👤 {user.email} ({user.role})')
                    
                    # Check group membership
                    with schema_context(get_public_schema_name()):
                        memberships = GroupMembership.objects.filter(
                            user_email=user.email,
                            is_active=True
                        ).select_related('group')
                        
                        if memberships.exists():
                            groups = [m.group.name for m in memberships]
                            self.stdout.write(f'    ✅ Groups: {", ".join(groups)}')
                        else:
                            self.stdout.write(f'    ❌ No groups assigned!')
                            
                            if fix_issues:
                                self.fix_user_group_assignment(user, tenant)

    def get_expected_group_for_role(self, role):
        """Get expected group name for a role"""
        role_to_group = {
            'superadmin': 'super_admin',
            'city_admin': 'city_admin',
            'subcity_admin': 'subcity_admin',
            'kebele_leader': 'kebele_leader',
            'clerk': 'clerk'
        }
        return role_to_group.get(role)

    def fix_user_group_assignment(self, user, tenant):
        """Fix group assignment for a user"""
        expected_group_name = self.get_expected_group_for_role(user.role)
        
        if not expected_group_name:
            self.stdout.write(f'    ⚠️ No expected group for role: {user.role}')
            return
        
        with schema_context(get_public_schema_name()):
            try:
                group = TenantGroup.objects.get(group__name=expected_group_name)
                self.assign_user_to_group(user, group, tenant)
                self.stdout.write(f'    ✅ Assigned {user.email} to {expected_group_name} group')
            except TenantGroup.DoesNotExist:
                self.stdout.write(f'    ❌ Group {expected_group_name} not found!')

    def assign_user_to_group(self, user, group, tenant):
        """Assign user to a group"""
        with schema_context(get_public_schema_name()):
            membership, created = GroupMembership.objects.get_or_create(
                user_email=user.email,
                group=group,
                defaults={
                    'user_tenant_id': tenant.id if tenant else None,
                    'user_tenant_schema': tenant.schema_name if tenant else None,
                    'assigned_by_email': '<EMAIL>',
                    'reason': 'Automatic assignment during debug/fix',
                    'is_primary': True,
                    'is_active': True
                }
            )
            
            if created:
                self.stdout.write(f'    ✅ Created group membership')
            else:
                # Update existing membership to be active
                membership.is_active = True
                membership.is_primary = True
                membership.save()
                self.stdout.write(f'    ✅ Updated existing group membership')

    def test_jwt_token_generation(self, user):
        """Test JWT token generation for user"""
        self.stdout.write(f'\n🔐 Testing JWT token for {user.email}')
        
        try:
            from users.serializers import CustomTokenObtainPairSerializer
            from rest_framework_simplejwt.tokens import RefreshToken
            
            # Create refresh token
            refresh = RefreshToken.for_user(user)
            
            # Get access token
            access_token = refresh.access_token
            
            # Decode token to check contents
            import jwt
            from django.conf import settings
            
            decoded = jwt.decode(
                str(access_token), 
                settings.SECRET_KEY, 
                algorithms=['HS256']
            )
            
            self.stdout.write(f'  📋 Token Contents:')
            self.stdout.write(f'    - User ID: {decoded.get("user_id")}')
            self.stdout.write(f'    - Email: {decoded.get("email")}')
            self.stdout.write(f'    - Role: {decoded.get("role")}')
            self.stdout.write(f'    - Groups: {decoded.get("groups", [])}')
            self.stdout.write(f'    - Permissions: {len(decoded.get("permissions", []))} permissions')
            self.stdout.write(f'    - Is Superuser: {decoded.get("is_superuser")}')
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ JWT token test failed: {e}'))
