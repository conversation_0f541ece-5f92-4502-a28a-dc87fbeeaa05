#!/usr/bin/env python3
"""
Fix Duplicate Detection Issues
"""

import urllib.request
import json
import base64
import time

def test_both_thumbs_capture():
    """Test capturing both thumbs to ensure real templates"""
    
    print("🔍 Testing Both Thumbs Capture...")
    print("=" * 50)
    
    results = {}
    
    for thumb_type in ['left', 'right']:
        print(f"\n📸 Testing {thumb_type} thumb capture...")
        
        try:
            # Capture fingerprint
            capture_data = json.dumps({"thumb_type": thumb_type}).encode()
            
            req = urllib.request.Request(
                "http://localhost:8001/api/capture/fingerprint",
                data=capture_data,
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"💡 JAR application will open for {thumb_type} thumb")
            print("💡 Please capture your fingerprint and close the application")
            
            response = urllib.request.urlopen(req, timeout=45)
            
            if response.getcode() == 200:
                result = json.loads(response.read().decode())
                
                if result.get('success'):
                    data = result.get('data', {})
                    template = data.get('template', '')
                    
                    # Analyze template
                    if template:
                        try:
                            template_json = base64.b64decode(template).decode()
                            template_obj = json.loads(template_json)
                            
                            minutiae_count = len(template_obj.get('minutiae_points', []))
                            frame_size = template_obj.get('frame_size', 0)
                            data_quality = template_obj.get('data_quality', 0)
                            extraction_method = template_obj.get('extraction_method', '')
                            
                            print(f"✅ {thumb_type} thumb captured successfully!")
                            print(f"   Template size: {len(template)} chars")
                            print(f"   Minutiae points: {minutiae_count}")
                            print(f"   Frame size: {frame_size} bytes")
                            print(f"   Data quality: {data_quality}%")
                            print(f"   Extraction method: {extraction_method}")
                            
                            # Check if it's real data
                            is_real = (
                                minutiae_count >= 15 and
                                frame_size > 5000 and
                                data_quality > 70 and
                                'real_capture' in extraction_method
                            )
                            
                            results[thumb_type] = {
                                'success': True,
                                'template': template,
                                'minutiae_count': minutiae_count,
                                'frame_size': frame_size,
                                'data_quality': data_quality,
                                'is_real': is_real
                            }
                            
                            if is_real:
                                print(f"   🎉 REAL fingerprint data confirmed!")
                            else:
                                print(f"   ⚠️ May be placeholder or low-quality data")
                            
                        except Exception as decode_error:
                            print(f"   ❌ Template decode error: {decode_error}")
                            results[thumb_type] = {'success': False, 'error': 'decode_failed'}
                    else:
                        print(f"   ❌ No template data received")
                        results[thumb_type] = {'success': False, 'error': 'no_template'}
                else:
                    print(f"   ❌ Capture failed: {result.get('error')}")
                    results[thumb_type] = {'success': False, 'error': result.get('error')}
            else:
                print(f"   ❌ HTTP error: {response.getcode()}")
                results[thumb_type] = {'success': False, 'error': f'http_{response.getcode()}'}
                
        except Exception as e:
            print(f"   ❌ Capture error: {e}")
            results[thumb_type] = {'success': False, 'error': str(e)}
    
    return results

def test_duplicate_detection(left_template, right_template):
    """Test duplicate detection with real templates"""
    
    print(f"\n🔍 Testing Duplicate Detection...")
    print("=" * 40)
    
    if not left_template or not right_template:
        print("❌ Cannot test duplicate detection - missing templates")
        return False
    
    try:
        # Prepare duplicate check request
        duplicate_data = {
            "left_thumb_fingerprint": left_template,
            "right_thumb_fingerprint": right_template
        }
        
        req_data = json.dumps(duplicate_data).encode()
        
        req = urllib.request.Request(
            "http://localhost:8000/api/biometrics/check-duplicates/",
            data=req_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print("📡 Sending duplicate check request to GoID backend...")
        response = urllib.request.urlopen(req, timeout=15)
        
        if response.getcode() == 200:
            result = json.loads(response.read().decode())
            
            print(f"✅ Duplicate check response received")
            print(f"   Success: {result.get('success')}")
            
            if result.get('success'):
                data = result.get('data', {})
                has_duplicates = data.get('has_duplicates', False)
                matches = data.get('matches', [])
                
                print(f"   Has duplicates: {has_duplicates}")
                print(f"   Total matches: {len(matches)}")
                
                if has_duplicates:
                    print(f"\n🚨 DUPLICATES FOUND:")
                    for i, match in enumerate(matches, 1):
                        print(f"   Match {i}:")
                        print(f"     Citizen: {match.get('citizen_name', 'Unknown')}")
                        print(f"     Kebele: {match.get('tenant_name', 'Unknown')}")
                        print(f"     Match score: {match.get('match_score', 0):.2f}")
                        print(f"     Digital ID: {match.get('citizen_digital_id', 'Unknown')}")
                else:
                    print(f"\n✅ NO DUPLICATES FOUND")
                    print("   This fingerprint combination is unique in the system")
                
                return True
            else:
                print(f"   ❌ Duplicate check failed: {result.get('error')}")
                return False
        else:
            print(f"   ❌ HTTP error: {response.getcode()}")
            return False
            
    except urllib.error.URLError as e:
        print(f"❌ Connection error: {e}")
        print("💡 Make sure GoID backend is running on localhost:8000")
        return False
    except Exception as e:
        print(f"❌ Duplicate check error: {e}")
        return False

def analyze_database_template(template_b64):
    """Analyze a template from the database"""
    
    print(f"\n🔍 Analyzing database template...")
    
    if not template_b64:
        print("❌ No template provided")
        return False
    
    try:
        # Try to decode as JSON first
        try:
            template_json = base64.b64decode(template_b64).decode()
            template_obj = json.loads(template_json)
            
            minutiae_count = len(template_obj.get('minutiae_points', []))
            frame_size = template_obj.get('frame_size', 0)
            data_quality = template_obj.get('data_quality', 0)
            
            print(f"✅ Valid JSON template:")
            print(f"   Minutiae points: {minutiae_count}")
            print(f"   Frame size: {frame_size} bytes")
            print(f"   Data quality: {data_quality}%")
            
            return minutiae_count > 0 and frame_size > 0
            
        except json.JSONDecodeError:
            # Try as plain text
            decoded_text = base64.b64decode(template_b64).decode()
            print(f"❌ Plain text template: '{decoded_text}'")
            
            if decoded_text == "placeholder_template_data":
                print("   This is placeholder data - NOT a real fingerprint!")
                return False
            else:
                print("   Unknown template format")
                return False
                
    except Exception as e:
        print(f"❌ Template analysis error: {e}")
        return False

def main():
    """Main function"""
    
    print("GoID Duplicate Detection Fixer")
    print("=" * 35)
    
    print("This will:")
    print("1. Test both thumb captures to ensure real templates")
    print("2. Analyze the database template issue")
    print("3. Test duplicate detection functionality")
    print()
    
    # Analyze the problematic right thumb template
    print("🔍 ANALYZING PROBLEMATIC RIGHT THUMB TEMPLATE:")
    right_thumb_db = "cGxhY2Vob2xkZXJfdGVtcGxhdGVfZGF0YQ=="
    is_real_db = analyze_database_template(right_thumb_db)
    
    if not is_real_db:
        print("\n💡 The right thumb template in your database is placeholder data.")
        print("💡 This explains why duplicate detection isn't working.")
        print("💡 We need to capture real templates for both thumbs.")
    
    print("\n" + "="*50)
    print("TESTING REAL FINGERPRINT CAPTURE")
    print("="*50)
    
    # Test both thumbs capture
    capture_results = test_both_thumbs_capture()
    
    # Check results
    left_success = capture_results.get('left', {}).get('success', False)
    right_success = capture_results.get('right', {}).get('success', False)
    
    if left_success and right_success:
        left_template = capture_results['left']['template']
        right_template = capture_results['right']['template']
        
        print(f"\n✅ Both thumbs captured successfully!")
        print(f"   Left thumb: {capture_results['left']['minutiae_count']} minutiae, {capture_results['left']['frame_size']} bytes")
        print(f"   Right thumb: {capture_results['right']['minutiae_count']} minutiae, {capture_results['right']['frame_size']} bytes")
        
        # Test duplicate detection
        print(f"\n" + "="*50)
        print("TESTING DUPLICATE DETECTION")
        print("="*50)
        
        duplicate_success = test_duplicate_detection(left_template, right_template)
        
        if duplicate_success:
            print(f"\n🎉 SUCCESS!")
            print(f"✅ Both thumbs capture real biometric data")
            print(f"✅ Duplicate detection system is working")
            print(f"✅ Templates are compatible with GoID backend")
        else:
            print(f"\n⚠️ PARTIAL SUCCESS")
            print(f"✅ Both thumbs capture real biometric data")
            print(f"❌ Duplicate detection may have backend issues")
            print(f"💡 Check if GoID backend is running and configured correctly")
    else:
        print(f"\n❌ CAPTURE ISSUES DETECTED")
        if not left_success:
            print(f"   ❌ Left thumb capture failed")
        if not right_success:
            print(f"   ❌ Right thumb capture failed")
        
        print(f"\n💡 Troubleshooting:")
        print(f"1. Ensure JAR bridge service is running")
        print(f"2. Check Futronic device connection")
        print(f"3. Verify Java is installed")
        print(f"4. Test manual startup: python working_jar_bridge.py")
    
    print(f"\n" + "="*50)
    print("RECOMMENDATIONS")
    print("="*50)
    
    if not is_real_db:
        print(f"🔧 DATABASE ISSUE:")
        print(f"   • Right thumb template in database is placeholder data")
        print(f"   • Need to re-capture right thumb with real biometric data")
        print(f"   • Update database with real template for duplicate detection")
    
    if left_success and right_success:
        print(f"🎉 BIOMETRIC CAPTURE:")
        print(f"   • Both thumbs now capture real biometric data")
        print(f"   • Templates contain actual minutiae points")
        print(f"   • Ready for production duplicate detection")
    
    print(f"\n💡 Next steps:")
    print(f"1. Use the real templates captured in this test")
    print(f"2. Update database with real biometric data")
    print(f"3. Test duplicate detection with real citizen data")
    print(f"4. Verify cross-kebele duplicate prevention")
    
    input("\nPress Enter to exit...")

if __name__ == '__main__':
    main()
