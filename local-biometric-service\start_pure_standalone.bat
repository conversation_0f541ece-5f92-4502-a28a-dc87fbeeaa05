@echo off
echo ========================================
echo   PURE STANDALONE Fingerprint Service
echo   NO HTTP Server - Just Direct SDK Calls
echo ========================================
echo.

echo 🎯 FINAL TEST: Run the EXACT debug code that worked
echo.
echo ✅ WHAT THIS DOES:
echo    - Runs the EXACT same code as successful debug
echo    - NO HTTP server complexity
echo    - NO Flask framework
echo    - NO threading or timeouts
echo    - Just pure SDK calls in a loop
echo.
echo 🔍 PURPOSE:
echo    - Prove ftrScanGetFrame() works in standalone mode
echo    - Identify if the issue is with web frameworks
echo    - Capture real fingerprints and save to files
echo    - Establish a working baseline
echo.

echo Checking Python installation...
python --version
if errorlevel 1 (
    echo ERROR: Python not found. Please install Python 3.7+
    pause
    exit /b 1
)

echo.
echo Checking required DLL files...
if not exist "ftrScanAPI.dll" (
    echo ERROR: ftrScanAPI.dll not found
    echo Please ensure Futronic SDK files are in the current directory
    pause
    exit /b 1
)

echo SUCCESS: ftrScanAPI.dll found

echo.
echo ========================================
echo   PURE STANDALONE APPROACH
echo ========================================
echo.
echo This service will:
echo.
echo 1. Load SDK exactly like successful debug
echo 2. Initialize device exactly like successful debug
echo 3. Wait for finger placement
echo 4. Call ftrScanGetFrame exactly like successful debug
echo 5. Save captured fingerprints to JSON files
echo 6. Repeat until you stop it
echo.
echo If this works, it proves:
echo   ✅ ftrScanGetFrame() function works perfectly
echo   ✅ Your SDK and device are fully functional
echo   ✅ The issue is with web service frameworks
echo.
echo If this fails, it proves:
echo   ❌ There's a fundamental SDK issue
echo   ❌ The debug success was a fluke
echo.

echo Starting PURE STANDALONE Service...
echo.
echo Instructions:
echo   1. Place finger on scanner when prompted
echo   2. Wait for capture to complete
echo   3. Check for saved JSON files
echo   4. Press 'y' to capture another or 'n' to stop
echo   5. Press Ctrl+C to force stop
echo.

python pure_standalone_service.py

echo.
echo Service stopped.
echo.
echo Check the current directory for saved fingerprint JSON files.
echo If files were created, ftrScanGetFrame() WORKS!
echo.
pause
