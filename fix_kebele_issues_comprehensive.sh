#!/bin/bash

# Comprehensive Fix for Kebele Issues Script
# This script fixes both the middleware routing and filtering issues

echo "🔧 Comprehensive Fix for Kebele Issues"
echo "======================================"

echo "📍 Issues Identified and Fixed:"
echo "1. Middleware was treating /api/tenants/{id}/users/ as tenant-specific endpoint"
echo "2. URL patterns needed explicit registration"
echo "3. Filtering logic needed adjustment"
echo ""
echo "📍 Changes Applied:"
echo "- Updated middleware to recognize user management endpoints"
echo "- Added explicit URL patterns for users, create_user, create_admin"
echo "- Fixed filtering to respect query parameters for superusers"
echo ""

# Step 1: Restart backend to apply all changes
echo "📍 Step 1: Restarting backend to apply all changes..."
docker-compose restart backend
sleep 30

# Step 2: Run detailed diagnostics
echo ""
echo "📍 Step 2: Running detailed diagnostics..."
python debug_kebele_issues_detailed.py

# Step 3: Test specific scenarios
echo ""
echo "📍 Step 3: Testing specific scenarios..."

echo "Getting authentication token..."
AUTH_RESPONSE=$(curl -s -X POST http://10.139.8.141:8000/api/auth/token/ \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}')

if echo "$AUTH_RESPONSE" | grep -q "access"; then
    echo "✅ Authentication successful"
    
    # Extract token
    TOKEN=$(echo "$AUTH_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin)['access'])" 2>/dev/null)
    
    if [ ! -z "$TOKEN" ]; then
        echo ""
        echo "Testing comprehensive scenarios..."
        
        # Scenario 1: Test filtering with different parameters
        echo "Scenario 1: Testing filtering behavior"
        
        echo "  1a. All tenants:"
        ALL_COUNT=$(curl -s -H "Authorization: Bearer $TOKEN" \
          http://10.139.8.141:8000/api/tenants/ | \
          python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, list): print(len(data))
    elif isinstance(data, dict) and 'results' in data: print(len(data.get('results', [])))
    else: print('0')
except: print('0')
" 2>/dev/null)
        echo "     Count: $ALL_COUNT"
        
        echo "  1b. Kebele tenants only:"
        KEBELE_COUNT=$(curl -s -H "Authorization: Bearer $TOKEN" \
          "http://10.139.8.141:8000/api/tenants/?type=kebele" | \
          python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, list): print(len(data))
    elif isinstance(data, dict) and 'results' in data: print(len(data.get('results', [])))
    else: print('0')
except: print('0')
" 2>/dev/null)
        echo "     Count: $KEBELE_COUNT"
        
        echo "  1c. Kebeles with parent=1:"
        PARENT_COUNT=$(curl -s -H "Authorization: Bearer $TOKEN" \
          "http://10.139.8.141:8000/api/tenants/?type=kebele&parent=1" | \
          python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, list): print(len(data))
    elif isinstance(data, dict) and 'results' in data: print(len(data.get('results', [])))
    else: print('0')
except: print('0')
" 2>/dev/null)
        echo "     Count: $PARENT_COUNT"
        
        # Check if filtering is working
        if [ "$PARENT_COUNT" -lt "$KEBELE_COUNT" ] && [ "$PARENT_COUNT" -ge "0" ]; then
            echo "  ✅ Filtering is working correctly"
            FILTERING_WORKS=true
        else
            echo "  ❌ Filtering is not working"
            FILTERING_WORKS=false
        fi
        
        # Scenario 2: Test users endpoint with different approaches
        echo ""
        echo "Scenario 2: Testing users endpoint"
        
        # Get a kebele ID to test with
        KEBELE_ID=$(curl -s -H "Authorization: Bearer $TOKEN" \
          "http://10.139.8.141:8000/api/tenants/?type=kebele" | \
          python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, list) and len(data) > 0:
        print(data[0]['id'])
    elif isinstance(data, dict) and 'results' in data and len(data['results']) > 0:
        print(data['results'][0]['id'])
    else:
        print('1')
except:
    print('1')
" 2>/dev/null)
        
        echo "  Using kebele ID: $KEBELE_ID"
        
        # Test users endpoint
        echo "  2a. Testing /api/tenants/$KEBELE_ID/users/"
        USERS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" \
          -H "Authorization: Bearer $TOKEN" \
          http://10.139.8.141:8000/api/tenants/$KEBELE_ID/users/)
        echo "     Status: $USERS_STATUS"
        
        if [ "$USERS_STATUS" = "200" ]; then
            echo "  ✅ Users endpoint is working"
            USERS_WORKS=true
            
            # Get actual users data
            USERS_DATA=$(curl -s -H "Authorization: Bearer $TOKEN" \
              http://10.139.8.141:8000/api/tenants/$KEBELE_ID/users/)
            
            USERS_COUNT=$(echo "$USERS_DATA" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, list): print(len(data))
    else: print('0')
except: print('0')
" 2>/dev/null)
            echo "     Users found: $USERS_COUNT"
            
        else
            echo "  ❌ Users endpoint failed with status $USERS_STATUS"
            USERS_WORKS=false
            
            # Try alternative endpoints
            echo "  2b. Testing alternative: /api/tenants/$KEBELE_ID/users (no trailing slash)"
            ALT_STATUS=$(curl -s -o /dev/null -w "%{http_code}" \
              -H "Authorization: Bearer $TOKEN" \
              http://10.139.8.141:8000/api/tenants/$KEBELE_ID/users)
            echo "     Status: $ALT_STATUS"
        fi
        
        # Test create_user endpoint
        echo "  2c. Testing /api/tenants/$KEBELE_ID/create_user/"
        CREATE_USER_STATUS=$(curl -s -o /dev/null -w "%{http_code}" \
          -H "Authorization: Bearer $TOKEN" \
          -H "Content-Type: application/json" \
          -X POST \
          -d '{}' \
          http://10.139.8.141:8000/api/tenants/$KEBELE_ID/create_user/)
        echo "     Status: $CREATE_USER_STATUS (400/403 expected for empty data)"
        
        if [ "$CREATE_USER_STATUS" = "400" ] || [ "$CREATE_USER_STATUS" = "403" ]; then
            echo "  ✅ Create user endpoint is accessible"
            CREATE_USER_WORKS=true
        else
            echo "  ❌ Create user endpoint failed with status $CREATE_USER_STATUS"
            CREATE_USER_WORKS=false
        fi
        
        # Summary
        echo ""
        echo "📊 Test Results Summary:"
        echo "========================"
        
        if [ "$FILTERING_WORKS" = true ]; then
            echo "✅ Tenant filtering: WORKING"
        else
            echo "❌ Tenant filtering: FAILED"
        fi
        
        if [ "$USERS_WORKS" = true ]; then
            echo "✅ Users endpoint: WORKING"
        else
            echo "❌ Users endpoint: FAILED"
        fi
        
        if [ "$CREATE_USER_WORKS" = true ]; then
            echo "✅ Create user endpoint: WORKING"
        else
            echo "❌ Create user endpoint: FAILED"
        fi
        
        # Overall status
        if [ "$FILTERING_WORKS" = true ] && [ "$USERS_WORKS" = true ]; then
            echo ""
            echo "🎉 SUCCESS: Both issues are now fixed!"
            echo ""
            echo "✅ Kebele filtering works correctly"
            echo "✅ Users endpoint returns 200 OK"
            echo "✅ Frontend should now work properly"
        else
            echo ""
            echo "❌ ISSUES REMAIN: Some problems are not yet resolved"
            echo ""
            if [ "$FILTERING_WORKS" = false ]; then
                echo "🔧 Filtering issue: Check TenantViewSet.get_queryset() logic"
            fi
            if [ "$USERS_WORKS" = false ]; then
                echo "🔧 Users endpoint issue: Check URL patterns and middleware"
            fi
        fi
        
    else
        echo "❌ Could not extract token"
    fi
else
    echo "❌ Authentication failed"
    echo "Response: $AUTH_RESPONSE"
fi

# Step 4: Check backend logs for any errors
echo ""
echo "📍 Step 4: Checking backend logs for errors..."
docker-compose logs --tail=20 backend | grep -E "ERROR|Exception|Traceback|404|500" || echo "No errors found in recent logs"

echo ""
echo "✅ Comprehensive kebele fix completed!"
echo ""
echo "🧪 Manual testing steps:"
echo "1. Clear browser cache: Ctrl+Shift+R"
echo "2. Open: http://10.139.8.141:3000/users/kebele-management"
echo "3. Should see filtered kebeles (not all kebeles)"
echo "4. Click on a kebele to select it"
echo "5. Should see users list load (not 404 error)"
echo ""
echo "🔍 Expected results:"
echo "- ✅ Only relevant kebeles shown in dropdown"
echo "- ✅ Users list loads when kebele selected"
echo "- ✅ No 404 errors in browser console"
echo "- ✅ Create user functionality works"
echo ""
echo "💡 If issues persist:"
echo "1. Check browser console for specific errors"
echo "2. Verify backend logs: docker-compose logs backend"
echo "3. Test API directly with curl commands above"
echo "4. Check if user has proper tenant assignment"
