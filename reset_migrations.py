#!/usr/bin/env python3
"""
<PERSON><PERSON>t to remove all migration files from Django apps and create fresh migrations.
This will help resolve migration conflicts and create a clean database schema.
"""

import os
import shutil
import glob

def remove_migration_files():
    """Remove all migration files except __init__.py from Django apps."""
    
    apps = ['users', 'tenants', 'citizens', 'idcards', 'workflows', 'shared']
    
    print("🗑️  Removing migration files...")
    
    for app in apps:
        migrations_dir = f"backend/{app}/migrations"
        
        if os.path.exists(migrations_dir):
            print(f"\n📁 Processing {app} app...")
            
            # Remove all .py files except __init__.py
            for file in glob.glob(f"{migrations_dir}/*.py"):
                if not file.endswith("__init__.py"):
                    print(f"   🗑️  Removing {os.path.basename(file)}")
                    os.remove(file)
            
            # Remove __pycache__ directory
            pycache_dir = f"{migrations_dir}/__pycache__"
            if os.path.exists(pycache_dir):
                print(f"   🗑️  Removing __pycache__ directory")
                shutil.rmtree(pycache_dir)
        else:
            print(f"⚠️  {migrations_dir} does not exist")
    
    print("\n✅ Migration files removed successfully!")

def create_init_files():
    """Ensure __init__.py files exist in migrations directories."""
    
    apps = ['users', 'tenants', 'citizens', 'idcards', 'workflows', 'shared']
    
    print("\n📝 Ensuring __init__.py files exist...")
    
    for app in apps:
        migrations_dir = f"backend/{app}/migrations"
        init_file = f"{migrations_dir}/__init__.py"
        
        if not os.path.exists(init_file):
            os.makedirs(migrations_dir, exist_ok=True)
            with open(init_file, 'w') as f:
                f.write("# Django migrations\n")
            print(f"   ✅ Created {init_file}")
        else:
            print(f"   ✅ {init_file} already exists")

def main():
    print("🚀 Django Migration Reset Script")
    print("=" * 50)
    
    # Change to the script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # Check if we're in the right directory
    if not os.path.exists("backend"):
        print("❌ Error: backend directory not found!")
        print("   Please run this script from the project root directory.")
        return
    
    # Remove migration files
    remove_migration_files()
    
    # Create __init__.py files
    create_init_files()
    
    print("\n" + "=" * 50)
    print("✅ Migration reset complete!")
    print("\nNext steps:")
    print("1. Stop Docker containers: docker-compose down -v")
    print("2. Rebuild backend: docker-compose build backend")
    print("3. Start containers: docker-compose up -d")
    print("4. Django will create fresh migrations automatically")

if __name__ == "__main__":
    main()
