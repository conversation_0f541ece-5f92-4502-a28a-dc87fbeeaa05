# 🔧 Shared Data 404 Error Solution

## Problem
Frontend shared data service is failing with 404 errors due to malformed URLs:
```
Failed to load resource: the server responded with a status of 404 (Not Found)
************:8000/api/tenants/tenants/subcities/
************:8000/api/tenants/tenants/kebeles/
```

Notice the duplicate "tenants" in the URLs.

## Root Cause Analysis
The issue is caused by:
1. **Duplicate path segments** - URLs contain `/api/tenants/tenants/` instead of `/api/tenants/`
2. **Frontend URL construction error** - `sharedDataService.js` has incorrect hardcoded URLs
3. **Copy-paste error** - Likely occurred when creating the service functions

## ✅ Complete Solution Applied

### 1. Fixed URL Paths
Updated `frontend/src/services/sharedDataService.js`:

**Before (Incorrect):**
```javascript
// Line 157
const response = await axios.get('/api/tenants/tenants/subcities/');

// Line 175  
const response = await axios.get('/api/tenants/tenants/kebeles/');
```

**After (Correct):**
```javascript
// Line 157
const response = await axios.get('/api/tenants/subcities/');

// Line 175
const response = await axios.get('/api/tenants/kebeles/');
```

### 2. URL Pattern Verification
Confirmed correct backend URL patterns in `backend/tenants/urls.py`:
- ✅ `/api/tenants/subcities/` - Registered correctly
- ✅ `/api/tenants/kebeles/` - Registered correctly

### 3. Impact Assessment
These endpoints are used for:
- **Citizen registration forms** - Subcity and kebele dropdowns
- **User management forms** - Location selection
- **Administrative interfaces** - Hierarchical data display

## 🚀 Quick Fix Instructions

### Option 1: Use the Fix Script
```bash
chmod +x fix_shared_data_urls.sh
./fix_shared_data_urls.sh
```

### Option 2: Manual Steps
```bash
# 1. Restart frontend to apply URL changes
docker-compose restart frontend

# 2. Wait for restart
sleep 20

# 3. Test the endpoints
python test_shared_data_endpoints.py

# 4. Clear browser cache (IMPORTANT!)
# Press Ctrl+Shift+R or Cmd+Shift+R for hard refresh
```

## 🧪 Testing the Fix

### 1. Test Backend Endpoints Directly
```bash
# Test subcities endpoint
curl http://************:8000/api/tenants/subcities/

# Test kebeles endpoint  
curl http://************:8000/api/tenants/kebeles/
```

Expected: **200 OK** with JSON data (not 404)

### 2. Test Frontend Integration
1. Open `http://************:3000/`
2. Open browser console (F12)
3. Navigate to citizen registration or user management
4. Check that dropdown data loads without 404 errors

### 3. Verify Network Requests
1. Open Developer Tools (F12) → Network tab
2. Reload the page
3. Look for requests to subcities/kebeles endpoints
4. Verify URLs are `/api/tenants/subcities/` (not `/api/tenants/tenants/subcities/`)

## 🔍 Verification Steps

1. **URLs Fixed**: No more double "tenants" in requests
2. **Backend Responds**: Endpoints return 200 OK with data
3. **Frontend Loads**: Dropdowns populate without errors
4. **Console Clean**: No 404 errors in browser console
5. **Forms Work**: Citizen registration and user forms function properly

## 🆘 Troubleshooting

### Issue: Still seeing 404 errors after fix
**Solutions:**
1. **Clear browser cache**: Hard refresh with Ctrl+Shift+R
2. **Restart frontend**: `docker-compose restart frontend`
3. **Check browser console**: Look for cached requests
4. **Incognito mode**: Test in private/incognito window

### Issue: Backend endpoints return 404
**Solutions:**
1. **Check URL patterns**: Verify `backend/tenants/urls.py` has correct patterns
2. **Restart backend**: `docker-compose restart backend`
3. **Check middleware**: Ensure tenant middleware handles these endpoints
4. **Verify permissions**: Check if authentication is required

### Issue: Frontend still makes old requests
**Solutions:**
1. **Hard refresh**: Ctrl+Shift+R to bypass cache
2. **Clear all cache**: Browser settings → Clear browsing data
3. **Restart browser**: Close and reopen browser completely
4. **Check service worker**: Disable service worker if present

### Issue: Dropdowns still empty
**Solutions:**
1. **Check authentication**: Some endpoints may require login
2. **Verify data exists**: Check if subcities/kebeles exist in database
3. **Check permissions**: User may need appropriate role
4. **Test API directly**: Use curl to verify backend returns data

## 📋 Expected Results After Fix

- ✅ **Correct URLs**: `/api/tenants/subcities/` and `/api/tenants/kebeles/`
- ✅ **No 404 errors**: All shared data endpoints return 200 OK
- ✅ **Dropdowns work**: Subcity and kebele dropdowns populate
- ✅ **Forms functional**: Citizen registration and user management work
- ✅ **Clean console**: No URL-related errors in browser console

## 🔧 Manual Verification

Test the specific URLs that were fixed:

```bash
# Test the corrected URLs
curl http://************:8000/api/tenants/subcities/
curl http://************:8000/api/tenants/kebeles/

# Test other shared endpoints for comparison
curl http://************:8000/api/shared/countries/
curl http://************:8000/api/shared/regions/
```

All should return 200 OK with JSON data.

## 📞 Support

If shared data URLs are still not working after following this guide:
1. Provide output of `python test_shared_data_endpoints.py`
2. Share browser console errors (F12 → Console)
3. Include network tab screenshots showing the actual URLs being called
4. Confirm which specific endpoints are still failing

## 🎯 Prevention

To prevent similar issues in the future:
1. **Use constants** for API base URLs instead of hardcoding
2. **Test endpoints** immediately after creating them
3. **Use URL builders** or template literals for dynamic URLs
4. **Code review** to catch copy-paste errors

The solution addresses the URL construction error that was causing the duplicate "tenants" path segments in the shared data service requests.
