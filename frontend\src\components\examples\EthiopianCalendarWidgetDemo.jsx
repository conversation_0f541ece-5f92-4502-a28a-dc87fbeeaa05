import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Grid,
  Alert,
  <PERSON>,
  Divider,
  <PERSON>ton,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  CalendarToday as CalendarIcon,
  Today as TodayIcon,
  Event as EventIcon,
  CheckCircle as CheckIcon,
  Build as BuildIcon
} from '@mui/icons-material';
import EthiopianCalendarWidget from '../common/EthiopianCalendarWidget';
import {
  getCurrentEthiopianDate,
  formatEthiopianDate,
  gregorianToEthiopian
} from '../../utils/ethiopianCalendar';

/**
 * Self-contained Ethiopian Calendar Widget Demo
 * 
 * Demonstrates the custom Ethiopian calendar widget that:
 * - Has no external package dependencies
 * - Provides true Ethiopian calendar interface
 * - Works reliably in Docker environments
 * - Shows Ethiopian Year 2017, Ethiopian months, etc.
 */
const EthiopianCalendarWidgetDemo = () => {
  const [selectedDate, setSelectedDate] = useState(null);
  const [language, setLanguage] = useState('en');
  const [showConversion, setShowConversion] = useState(true);
  
  const currentEthiopian = getCurrentEthiopianDate();
  const today = new Date();
  const todayEthiopian = gregorianToEthiopian(today);

  const handleDateChange = (gregorianDate) => {
    setSelectedDate(gregorianDate);
    
    if (gregorianDate) {
      const ethiopianDate = gregorianToEthiopian(gregorianDate);
      console.log('Ethiopian Calendar Widget Selection:', {
        implementation: 'Self-contained (no external packages)',
        userInterface: 'Ethiopian Calendar',
        userSees: formatEthiopianDate(ethiopianDate, 'DD MMM YYYY', language),
        systemReceives: gregorianDate.toISOString(),
        ethiopianYear: ethiopianDate.year,
        currentEthiopianYear: currentEthiopian.year,
        dockerCompatible: true
      });
    }
  };

  const setToday = () => {
    setSelectedDate(new Date());
  };

  const getSelectedInfo = () => {
    if (!selectedDate) return null;
    
    const ethiopianDate = gregorianToEthiopian(selectedDate);
    return {
      gregorian: {
        date: selectedDate,
        formatted: selectedDate.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }),
        iso: selectedDate.toISOString().split('T')[0]
      },
      ethiopian: {
        date: ethiopianDate,
        formatted: formatEthiopianDate(ethiopianDate, 'DD MMM YYYY', language),
        formattedShort: formatEthiopianDate(ethiopianDate, 'DD/MM/YYYY', language),
        formattedAmharic: formatEthiopianDate(ethiopianDate, 'DD MMM YYYY', 'am')
      }
    };
  };

  const selectedInfo = getSelectedInfo();

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Self-contained Ethiopian Calendar Widget
      </Typography>
      
      <Alert severity="success" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>Docker-Compatible Solution:</strong> Self-contained Ethiopian calendar widget with no external package dependencies. 
          Users see Ethiopian Year {currentEthiopian.year}, {formatEthiopianDate(currentEthiopian, 'MMM', 'en')} month, etc.
        </Typography>
      </Alert>

      <Grid container spacing={3}>
        {/* Implementation Benefits */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <BuildIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Implementation Benefits
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <CheckIcon color="success" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="subtitle2">No Dependencies</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Self-contained component with no external package dependencies
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={4}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <CheckIcon color="success" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="subtitle2">Docker Compatible</Typography>
                    <Typography variant="body2" color="text.secondary">
                      No peer dependency conflicts, builds reliably in Docker
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={4}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <CheckIcon color="success" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="subtitle2">True Ethiopian Calendar</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Authentic Ethiopian calendar with Year 2017, Ethiopian months
                    </Typography>
                  </Box>
                </Grid>
              </Grid>

              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', mt: 2 }}>
                <Chip
                  icon={<CalendarIcon />}
                  label={`Today (Ethiopian): ${formatEthiopianDate(todayEthiopian, 'DD MMM YYYY', 'en')}`}
                  color="primary"
                  size="medium"
                />
                <Chip
                  icon={<CalendarIcon />}
                  label={`Today (Gregorian): ${today.toLocaleDateString('en-US')}`}
                  color="secondary"
                  size="medium"
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Controls */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Demo Controls
              </Typography>
              
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'center' }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={language === 'am'}
                      onChange={(e) => setLanguage(e.target.checked ? 'am' : 'en')}
                    />
                  }
                  label={`Language: ${language === 'am' ? 'Amharic' : 'English'}`}
                />
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={showConversion}
                      onChange={(e) => setShowConversion(e.target.checked)}
                    />
                  }
                  label="Show Conversion"
                />

                <Button
                  variant="outlined"
                  startIcon={<TodayIcon />}
                  onClick={setToday}
                  size="small"
                >
                  Set to Today
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Ethiopian Calendar Widget */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <EventIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Ethiopian Calendar Widget
              </Typography>
              
              <EthiopianCalendarWidget
                label="Select Date using Ethiopian Calendar"
                value={selectedDate}
                onChange={handleDateChange}
                language={language}
                showConversion={showConversion}
                helperText="Self-contained Ethiopian calendar - Year 2017 = Current"
                placeholder="Click to open Ethiopian calendar"
                minYear={2010}
                maxYear={2025}
              />
            </CardContent>
          </Card>
        </Grid>

        {/* Selected Date Information */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Selected Date Information
              </Typography>

              {selectedInfo ? (
                <Box>
                  <Typography variant="subtitle2" color="primary" gutterBottom>
                    Ethiopian Calendar (User Interface):
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 1 }}>
                    {selectedInfo.ethiopian.formatted}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Short: {selectedInfo.ethiopian.formattedShort}<br />
                    Amharic: {selectedInfo.ethiopian.formattedAmharic}
                  </Typography>

                  <Divider sx={{ my: 2 }} />

                  <Typography variant="subtitle2" color="secondary" gutterBottom>
                    Gregorian Calendar (System Storage):
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 1 }}>
                    {selectedInfo.gregorian.formatted}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    ISO: {selectedInfo.gregorian.iso}
                  </Typography>

                  <Divider sx={{ my: 2 }} />

                  <Typography variant="subtitle2" gutterBottom>
                    Implementation Details:
                  </Typography>
                  <Box sx={{ p: 1, bgcolor: 'grey.50', borderRadius: 1 }}>
                    <Typography variant="caption" color="text.secondary">
                      <strong>Component:</strong> EthiopianCalendarWidget<br />
                      <strong>Dependencies:</strong> None (self-contained)<br />
                      <strong>User Experience:</strong> Ethiopian Year {selectedInfo.ethiopian.date.year}<br />
                      <strong>System Storage:</strong> {selectedInfo.gregorian.iso}<br />
                      <strong>Docker Compatible:</strong> Yes<br />
                      <strong>Language:</strong> {language === 'am' ? 'Amharic' : 'English'}
                    </Typography>
                  </Box>
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  Select a date using the Ethiopian calendar to see the conversion details.
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Technical Implementation */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Technical Implementation
              </Typography>
              
              <Box sx={{ p: 2, bgcolor: 'grey.100', borderRadius: 1, fontFamily: 'monospace' }}>
                <Typography variant="body2" component="pre">
{`// Self-contained Ethiopian Calendar Widget
import EthiopianCalendarWidget from '../components/common/EthiopianCalendarWidget';

const MyComponent = () => {
  const [selectedDate, setSelectedDate] = useState(null);

  const handleDateChange = (gregorianDate) => {
    setSelectedDate(gregorianDate);
    // gregorianDate is a standard Date object
    // User saw Ethiopian calendar interface
  };

  return (
    <EthiopianCalendarWidget
      value={selectedDate}
      onChange={handleDateChange}
      language="${language}" // 'en' or 'am'
      showConversion={true}
      placeholder="Select Ethiopian date"
      minYear={2010}
      maxYear={2025}
      disableFuture={false}
    />
  );
};

// Benefits:
// ✅ No external package dependencies
// ✅ Docker compatible (no peer dependency conflicts)
// ✅ True Ethiopian calendar interface
// ✅ Returns standard Gregorian Date objects
// ✅ Self-contained and maintainable`}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Comparison */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Solution Comparison
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ p: 2, bgcolor: 'error.light', borderRadius: 1 }}>
                    <Typography variant="subtitle2" color="error.dark">
                      ❌ External Package (et-calendar-react)
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      • Peer dependency conflicts with React 18<br />
                      • Docker build failures<br />
                      • External maintenance dependency<br />
                      • Version compatibility issues
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <Box sx={{ p: 2, bgcolor: 'success.light', borderRadius: 1 }}>
                    <Typography variant="subtitle2" color="success.dark">
                      ✅ Self-contained Widget
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      • No external dependencies<br />
                      • Docker compatible<br />
                      • Full control over implementation<br />
                      • No version conflicts
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default EthiopianCalendarWidgetDemo;
