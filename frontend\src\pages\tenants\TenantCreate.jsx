import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Typography,
  TextField,
  Grid,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  Alert,
  CircularProgress,
} from '@mui/material';
import { useFormik } from 'formik';
import * as yup from 'yup';
import axios from '../../utils/axios';
import { getStandardMenuProps, createSelectChangeHandler } from '../../utils/dropdownUtils';

// Mock data for parent tenants
const mockParentTenants = [
  {
    id: 1,
    name: 'Addis Ababa',
    type: 'city',
  },
  {
    id: 2,
    name: '<PERSON><PERSON>',
    type: 'subcity',
    parent: 1,
  },
];

const validationSchema = yup.object({
  name: yup.string().required('Name is required'),
  type: yup.string().required('Type is required'),
  parent: yup.number().when('type', {
    is: (type) => type === 'subcity' || type === 'kebele',
    then: () => yup.number().required('Parent tenant is required'),
    otherwise: () => yup.number().nullable(),
  }),
});

const TenantCreate = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [parentTenants, setParentTenants] = useState([]);
  const [availableParents, setAvailableParents] = useState([]);

  useEffect(() => {
    // In a real implementation, this would fetch data from the API
    // For now, we'll just use mock data
    setParentTenants(mockParentTenants);
  }, []);

  const formik = useFormik({
    initialValues: {
      name: '',
      type: '',
      parent: '',
    },
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      try {
        setLoading(true);
        setError('');

        // In a real implementation, this would call an API endpoint
        // For now, we'll just simulate a successful request
        await new Promise(resolve => setTimeout(resolve, 1000));

        setSuccess(true);
        setTimeout(() => {
          navigate('/tenants');
        }, 1500);
      } catch (err) {
        setError(err.response?.data?.detail || 'Failed to create tenant. Please try again.');
      } finally {
        setLoading(false);
      }
    },
  });

  // Update available parent tenants based on selected type
  useEffect(() => {
    if (formik.values.type === 'subcity') {
      // Only cities can be parents of subcities
      setAvailableParents(parentTenants.filter(tenant => tenant.type === 'city'));
    } else if (formik.values.type === 'kebele') {
      // Only subcities can be parents of kebeles
      setAvailableParents(parentTenants.filter(tenant => tenant.type === 'subcity'));
    } else {
      setAvailableParents([]);
      formik.setFieldValue('parent', '');
    }
  }, [formik.values.type, parentTenants]);

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Create New Tenant
        </Typography>
        <Button
          variant="outlined"
          onClick={() => navigate('/tenants')}
        >
          Cancel
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          Tenant created successfully!
        </Alert>
      )}

      <form onSubmit={formik.handleSubmit}>
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Tenant Information
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="name"
                  name="name"
                  label="Tenant Name"
                  value={formik.values.name}
                  onChange={formik.handleChange}
                  error={formik.touched.name && Boolean(formik.errors.name)}
                  helperText={formik.touched.name && formik.errors.name}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth error={formik.touched.type && Boolean(formik.errors.type)}>
                  <InputLabel id="type-label">Tenant Type</InputLabel>
                  <Select
                    labelId="type-label"
                    id="type"
                    name="type"
                    value={formik.values.type}
                    label="Tenant Type"
                    onChange={createSelectChangeHandler('type', formik.handleChange)}
                    MenuProps={getStandardMenuProps()}
                  >
                    <MenuItem value="city">City</MenuItem>
                    <MenuItem value="subcity">Sub City</MenuItem>
                    <MenuItem value="kebele">Kebele</MenuItem>
                  </Select>
                  {formik.touched.type && formik.errors.type && (
                    <FormHelperText>{formik.errors.type}</FormHelperText>
                  )}
                </FormControl>
              </Grid>
              {(formik.values.type === 'subcity' || formik.values.type === 'kebele') && (
                <Grid item xs={12}>
                  <FormControl fullWidth error={formik.touched.parent && Boolean(formik.errors.parent)}>
                    <InputLabel id="parent-label">Parent Tenant</InputLabel>
                    <Select
                      labelId="parent-label"
                      id="parent"
                      name="parent"
                      value={formik.values.parent}
                      label="Parent Tenant"
                      onChange={createSelectChangeHandler('parent', formik.handleChange)}
                      MenuProps={getStandardMenuProps()}
                    >
                      {availableParents.map((parent) => (
                        <MenuItem key={parent.id} value={parent.id}>
                          {parent.name}
                        </MenuItem>
                      ))}
                    </Select>
                    {formik.touched.parent && formik.errors.parent && (
                      <FormHelperText>{formik.errors.parent}</FormHelperText>
                    )}
                  </FormControl>
                </Grid>
              )}
            </Grid>
          </CardContent>
        </Card>

        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
          <Button
            type="submit"
            variant="contained"
            size="large"
            disabled={loading}
            sx={{ minWidth: 150 }}
          >
            {loading ? <CircularProgress size={24} /> : 'Create Tenant'}
          </Button>
        </Box>
      </form>
    </Box>
  );
};

export default TenantCreate;
