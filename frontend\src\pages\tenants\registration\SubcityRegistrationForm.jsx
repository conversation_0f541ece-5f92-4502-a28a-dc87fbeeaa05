import { useState } from 'react';
import {
  Box,
  Typography,
  Grid,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  Divider,
  Switch,
  FormControlLabel,
  Button,
  Avatar,
  Paper,
} from '@mui/material';
import { CloudUpload as CloudUploadIcon } from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { getStandardMenuProps } from '../../../utils/dropdownUtils';

const SubcityRegistrationForm = ({ data, onChange, parentOptions }) => {
  const handleChange = (e) => {
    const { name, value } = e.target;
    onChange({ ...data, [name]: value });
  };

  const handleSwitchChange = (e) => {
    const { name, checked } = e.target;
    onChange({ ...data, [name]: checked });
  };

  const handleDateChange = (date) => {
    onChange({ ...data, established_date: date });
  };

  const handleImageUpload = (fieldName) => (event) => {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      if (!allowedTypes.includes(file.type)) {
        alert('Please select a valid image file (JPEG, JPG, or PNG)');
        return;
      }

      // Validate file size (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        alert('File size must be less than 5MB');
        return;
      }

      // Create preview URL
      const previewUrl = URL.createObjectURL(file);

      onChange({
        ...data,
        [fieldName]: file,
        [`${fieldName}_preview`]: previewUrl
      });
    }
  };

  const handleImageRemove = (fieldName) => () => {
    // Revoke the preview URL to free memory
    if (data[`${fieldName}_preview`]) {
      URL.revokeObjectURL(data[`${fieldName}_preview`]);
    }

    onChange({
      ...data,
      [fieldName]: null,
      [`${fieldName}_preview`]: null
    });
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom fontWeight="bold">
        SubCity Information
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Enter the details for the subcity administration.
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Divider sx={{ my: 2 }}>Basic Information</Divider>
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            required
            id="name"
            name="name"
            label="SubCity Name (English)"
            value={data.name || ''}
            onChange={handleChange}
            placeholder="e.g., Bole"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            id="name_am"
            name="name_am"
            label="SubCity Name (Amharic)"
            value={data.name_am || ''}
            onChange={handleChange}
            placeholder="e.g., ቦሌ"
            helperText="SubCity name in Amharic script"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            required
            id="tenant_name"
            name="tenant_name"
            label="Tenant Name"
            value={data.tenant_name || data.name || ''}
            onChange={handleChange}
            placeholder="e.g., Bole SubCity Administration"
            helperText="Name used for the tenant schema"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            required
            id="domain_name"
            name="domain_name"
            label="Domain Name"
            value={data.domain_name || ''}
            onChange={handleChange}
            placeholder="e.g., bole.addisababa.goid.gov.et"
            helperText="Domain name for tenant access (without http://)"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControl fullWidth required>
            <InputLabel id="city-label">Parent City</InputLabel>
            <Select
              labelId="city-label"
              id="city_id"
              name="city_id"
              value={data.city_id || data.parentId || ''}
              label="Parent City"
              onChange={handleChange}
              MenuProps={getStandardMenuProps()}
            >
              {parentOptions.map((city) => (
                <MenuItem key={city.id} value={city.id}>
                  {city.name}
                </MenuItem>
              ))}
            </Select>
            <FormHelperText>Select the city this subcity belongs to</FormHelperText>
          </FormControl>
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            id="code"
            name="code"
            label="SubCity Code"
            value={data.code || ''}
            onChange={handleChange}
            placeholder="e.g., SC-01"
            helperText="Leave blank for auto-generation"
          />
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: 2 }}>Branding & Customization</Divider>
        </Grid>

        {/* Logo Upload */}
        <Grid item xs={12} md={6}>
          <Typography variant="subtitle2" gutterBottom>
            Subcity Logo
          </Typography>
          <Paper
            variant="outlined"
            sx={{
              p: 2,
              textAlign: 'center',
              backgroundColor: '#fafafa',
              border: '2px dashed #ddd',
              '&:hover': { borderColor: '#bbb' }
            }}
          >
            {data.logo_preview ? (
              <Box>
                <img
                  src={data.logo_preview}
                  alt="Logo Preview"
                  style={{
                    maxWidth: '120px',
                    maxHeight: '120px',
                    objectFit: 'contain',
                    marginBottom: '8px'
                  }}
                />
                <Box>
                  <Button
                    variant="outlined"
                    color="error"
                    size="small"
                    onClick={handleImageRemove('logo')}
                  >
                    Remove
                  </Button>
                </Box>
              </Box>
            ) : (
              <Box>
                <CloudUploadIcon sx={{ fontSize: 48, color: '#bbb', mb: 1 }} />
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Upload Subcity Logo
                </Typography>
                <Typography variant="caption" color="text.secondary" display="block" gutterBottom>
                  Recommended: 200x200px, PNG/JPG, Max 5MB
                </Typography>
                <Button
                  variant="contained"
                  component="label"
                  startIcon={<CloudUploadIcon />}
                  size="small"
                >
                  Choose File
                  <input
                    type="file"
                    hidden
                    accept="image/jpeg,image/jpg,image/png"
                    onChange={handleImageUpload('logo')}
                  />
                </Button>
              </Box>
            )}
          </Paper>
          <FormHelperText>
            Official logo for subcity administration
          </FormHelperText>
        </Grid>

        {/* Mayor Signature Upload */}
        <Grid item xs={12} md={6}>
          <Typography variant="subtitle2" gutterBottom>
            Mayor/Admin Signature
          </Typography>
          <Paper
            variant="outlined"
            sx={{
              p: 2,
              textAlign: 'center',
              backgroundColor: '#fafafa',
              border: '2px dashed #ddd',
              '&:hover': { borderColor: '#bbb' }
            }}
          >
            {data.mayor_signature_preview ? (
              <Box>
                <img
                  src={data.mayor_signature_preview}
                  alt="Mayor Signature Preview"
                  style={{
                    maxWidth: '200px',
                    maxHeight: '80px',
                    objectFit: 'contain',
                    marginBottom: '8px'
                  }}
                />
                <Box>
                  <Button
                    variant="outlined"
                    color="error"
                    size="small"
                    onClick={handleImageRemove('mayor_signature')}
                  >
                    Remove
                  </Button>
                </Box>
              </Box>
            ) : (
              <Box>
                <CloudUploadIcon sx={{ fontSize: 48, color: '#bbb', mb: 1 }} />
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Upload Mayor/Admin Signature
                </Typography>
                <Typography variant="caption" color="text.secondary" display="block" gutterBottom>
                  Recommended: 200x80px, PNG/JPG, Max 5MB
                </Typography>
                <Button
                  variant="contained"
                  component="label"
                  startIcon={<CloudUploadIcon />}
                  size="small"
                >
                  Choose File
                  <input
                    type="file"
                    hidden
                    accept="image/jpeg,image/jpg,image/png"
                    onChange={handleImageUpload('mayor_signature')}
                  />
                </Button>
              </Box>
            )}
          </Paper>
          <FormHelperText>
            Signature image that will appear on approved ID cards
          </FormHelperText>
        </Grid>

        {/* Pattern Image Upload */}
        <Grid item xs={12} md={6}>
          <Typography variant="subtitle2" gutterBottom>
            Security Pattern Image
          </Typography>
          <Paper
            variant="outlined"
            sx={{
              p: 2,
              textAlign: 'center',
              backgroundColor: '#fafafa',
              border: '2px dashed #ddd',
              '&:hover': { borderColor: '#bbb' }
            }}
          >
            {data.pattern_image_preview ? (
              <Box>
                <img
                  src={data.pattern_image_preview}
                  alt="Pattern Image Preview"
                  style={{
                    maxWidth: '200px',
                    maxHeight: '120px',
                    objectFit: 'contain',
                    marginBottom: '8px'
                  }}
                />
                <Box>
                  <Button
                    variant="outlined"
                    color="error"
                    size="small"
                    onClick={handleImageRemove('pattern_image')}
                  >
                    Remove
                  </Button>
                </Box>
              </Box>
            ) : (
              <Box>
                <CloudUploadIcon sx={{ fontSize: 48, color: '#bbb', mb: 1 }} />
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Upload Security Pattern
                </Typography>
                <Typography variant="caption" color="text.secondary" display="block" gutterBottom>
                  Recommended: 500x300px, PNG with transparency, Max 5MB
                </Typography>
                <Button
                  variant="contained"
                  component="label"
                  startIcon={<CloudUploadIcon />}
                  size="small"
                >
                  Choose File
                  <input
                    type="file"
                    hidden
                    accept="image/jpeg,image/jpg,image/png"
                    onChange={handleImageUpload('pattern_image')}
                  />
                </Button>
              </Box>
            )}
          </Paper>
          <FormHelperText>
            Custom security pattern for ID card background
          </FormHelperText>
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: 2 }}>Status</Divider>
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControlLabel
            control={
              <Switch
                checked={data.is_active !== false}
                onChange={handleSwitchChange}
                name="is_active"
                color="primary"
              />
            }
            label="Is Active"
          />
          <FormHelperText>Indicates whether the subcity is active</FormHelperText>
        </Grid>
      </Grid>
    </Box>
  );
};

export default SubcityRegistrationForm;
