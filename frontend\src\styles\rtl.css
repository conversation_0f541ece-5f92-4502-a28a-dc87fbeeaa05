/**
 * RTL (Right-to-Left) Language Support Styles
 * Provides comprehensive RTL styling for the GoID application
 */

/* Base RTL styles */
[dir="rtl"] {
  text-align: right;
}

[dir="ltr"] {
  text-align: left;
}

/* RTL-aware utility classes */
.text-start {
  text-align: left;
}

[dir="rtl"] .text-start {
  text-align: right;
}

.text-end {
  text-align: right;
}

[dir="rtl"] .text-end {
  text-align: left;
}

/* Margin utilities */
.ms-1 { margin-left: 0.25rem; }
.ms-2 { margin-left: 0.5rem; }
.ms-3 { margin-left: 1rem; }
.ms-4 { margin-left: 1.5rem; }
.ms-5 { margin-left: 3rem; }

[dir="rtl"] .ms-1 { margin-left: 0; margin-right: 0.25rem; }
[dir="rtl"] .ms-2 { margin-left: 0; margin-right: 0.5rem; }
[dir="rtl"] .ms-3 { margin-left: 0; margin-right: 1rem; }
[dir="rtl"] .ms-4 { margin-left: 0; margin-right: 1.5rem; }
[dir="rtl"] .ms-5 { margin-left: 0; margin-right: 3rem; }

.me-1 { margin-right: 0.25rem; }
.me-2 { margin-right: 0.5rem; }
.me-3 { margin-right: 1rem; }
.me-4 { margin-right: 1.5rem; }
.me-5 { margin-right: 3rem; }

[dir="rtl"] .me-1 { margin-right: 0; margin-left: 0.25rem; }
[dir="rtl"] .me-2 { margin-right: 0; margin-left: 0.5rem; }
[dir="rtl"] .me-3 { margin-right: 0; margin-left: 1rem; }
[dir="rtl"] .me-4 { margin-right: 0; margin-left: 1.5rem; }
[dir="rtl"] .me-5 { margin-right: 0; margin-left: 3rem; }

/* Padding utilities */
.ps-1 { padding-left: 0.25rem; }
.ps-2 { padding-left: 0.5rem; }
.ps-3 { padding-left: 1rem; }
.ps-4 { padding-left: 1.5rem; }
.ps-5 { padding-left: 3rem; }

[dir="rtl"] .ps-1 { padding-left: 0; padding-right: 0.25rem; }
[dir="rtl"] .ps-2 { padding-left: 0; padding-right: 0.5rem; }
[dir="rtl"] .ps-3 { padding-left: 0; padding-right: 1rem; }
[dir="rtl"] .ps-4 { padding-left: 0; padding-right: 1.5rem; }
[dir="rtl"] .ps-5 { padding-left: 0; padding-right: 3rem; }

.pe-1 { padding-right: 0.25rem; }
.pe-2 { padding-right: 0.5rem; }
.pe-3 { padding-right: 1rem; }
.pe-4 { padding-right: 1.5rem; }
.pe-5 { padding-right: 3rem; }

[dir="rtl"] .pe-1 { padding-right: 0; padding-left: 0.25rem; }
[dir="rtl"] .pe-2 { padding-right: 0; padding-left: 0.5rem; }
[dir="rtl"] .pe-3 { padding-right: 0; padding-left: 1rem; }
[dir="rtl"] .pe-4 { padding-right: 0; padding-left: 1.5rem; }
[dir="rtl"] .pe-5 { padding-right: 0; padding-left: 3rem; }

/* Border utilities */
.border-start {
  border-left: 1px solid #dee2e6;
}

[dir="rtl"] .border-start {
  border-left: none;
  border-right: 1px solid #dee2e6;
}

.border-end {
  border-right: 1px solid #dee2e6;
}

[dir="rtl"] .border-end {
  border-right: none;
  border-left: 1px solid #dee2e6;
}

/* Float utilities */
.float-start {
  float: left;
}

[dir="rtl"] .float-start {
  float: right;
}

.float-end {
  float: right;
}

[dir="rtl"] .float-end {
  float: left;
}

/* Position utilities */
.start-0 { left: 0; }
.start-50 { left: 50%; }
.start-100 { left: 100%; }

[dir="rtl"] .start-0 { left: auto; right: 0; }
[dir="rtl"] .start-50 { left: auto; right: 50%; }
[dir="rtl"] .start-100 { left: auto; right: 100%; }

.end-0 { right: 0; }
.end-50 { right: 50%; }
.end-100 { right: 100%; }

[dir="rtl"] .end-0 { right: auto; left: 0; }
[dir="rtl"] .end-50 { right: auto; left: 50%; }
[dir="rtl"] .end-100 { right: auto; left: 100%; }

/* Form controls */
.form-control,
.form-select,
.form-check-input {
  direction: inherit;
}

[dir="rtl"] .form-control,
[dir="rtl"] .form-select {
  text-align: right;
}

/* Navigation and menus */
.nav-pills .nav-link {
  text-align: inherit;
}

[dir="rtl"] .dropdown-menu {
  left: auto;
  right: 0;
}

/* Cards and panels */
.card-header,
.card-body,
.card-footer {
  text-align: inherit;
}

/* Tables */
[dir="rtl"] .table th,
[dir="rtl"] .table td {
  text-align: right;
}

[dir="rtl"] .table th:first-child,
[dir="rtl"] .table td:first-child {
  border-right: none;
}

[dir="rtl"] .table th:last-child,
[dir="rtl"] .table td:last-child {
  border-left: none;
}

/* Buttons and button groups */
.btn-group > .btn:not(:first-child) {
  margin-left: -1px;
}

[dir="rtl"] .btn-group > .btn:not(:first-child) {
  margin-left: 0;
  margin-right: -1px;
}

/* Icons and arrows */
.icon-arrow-left::before {
  content: "←";
}

.icon-arrow-right::before {
  content: "→";
}

[dir="rtl"] .icon-arrow-left::before {
  content: "→";
}

[dir="rtl"] .icon-arrow-right::before {
  content: "←";
}

/* Breadcrumbs */
.breadcrumb-item + .breadcrumb-item::before {
  content: "/";
  padding: 0 0.5rem;
}

[dir="rtl"] .breadcrumb-item + .breadcrumb-item::before {
  content: "\\";
}

/* Tooltips and popovers */
[dir="rtl"] .tooltip,
[dir="rtl"] .popover {
  text-align: right;
}

/* Custom GoID specific styles */
.goid-sidebar {
  left: 0;
  right: auto;
}

[dir="rtl"] .goid-sidebar {
  left: auto;
  right: 0;
}

.goid-main-content {
  margin-left: 250px;
  margin-right: 0;
}

[dir="rtl"] .goid-main-content {
  margin-left: 0;
  margin-right: 250px;
}

/* Language switcher */
.language-switcher {
  text-align: left;
}

[dir="rtl"] .language-switcher {
  text-align: right;
}

/* Search and filters */
.search-input {
  text-align: left;
}

[dir="rtl"] .search-input {
  text-align: right;
}

/* Data tables */
.data-table th,
.data-table td {
  text-align: left;
}

[dir="rtl"] .data-table th,
[dir="rtl"] .data-table td {
  text-align: right;
}

/* Responsive RTL adjustments */
@media (max-width: 768px) {
  [dir="rtl"] .goid-sidebar {
    transform: translateX(100%);
  }
  
  [dir="rtl"] .goid-sidebar.show {
    transform: translateX(0);
  }
}

/* Animation adjustments for RTL */
@keyframes slideInLeft {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

@keyframes slideInRight {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

[dir="rtl"] .slide-in-left {
  animation: slideInRight 0.3s ease-out;
}

[dir="rtl"] .slide-in-right {
  animation: slideInLeft 0.3s ease-out;
}

/* Print styles for RTL */
@media print {
  [dir="rtl"] * {
    text-align: right !important;
  }
}
