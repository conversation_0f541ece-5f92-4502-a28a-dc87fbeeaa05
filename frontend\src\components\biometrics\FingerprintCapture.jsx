import { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Typography,
  Button,
  Alert,
  CircularProgress,
  LinearProgress,
  Paper,
  Stepper,
  Step,
  StepLabel,
  StepContent,
} from '@mui/material';
import {
  Fingerprint as FingerprintIcon,
  TouchApp as TouchIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';
import biometricService from '../../services/biometricService';

const FingerprintCapture = ({
  thumbType = 'left',
  onCaptureComplete,
  onCaptureError,
  onProgressUpdate,
  onStatusUpdate,
  deviceConnected = true
}) => {
  const [activeStep, setActiveStep] = useState(0);
  const [capturing, setCapturing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState('');
  const [fingerprintData, setFingerprintData] = useState(null);
  const [quality, setQuality] = useState(0);
  const captureTimeoutRef = useRef(null);
  const progressIntervalRef = useRef(null);

  const steps = [
    'Place finger on sensor',
    'Capturing fingerprint',
    'Processing template',
    'Validation complete'
  ];

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (captureTimeoutRef.current) {
        clearTimeout(captureTimeoutRef.current);
      }
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    };
  }, []);

  const startCapture = useCallback(async () => {
    try {
      setCapturing(true);
      setError('');
      setActiveStep(0);
      setProgress(0);
      onStatusUpdate('capturing');
      onProgressUpdate(0);

      // Step 1: Wait for finger placement
      setActiveStep(1);
      await simulateFingerPlacement();

      // Step 2: Capture fingerprint
      setActiveStep(2);
      const capturedData = await captureFingerprintFromDevice();

      // Step 3: Process template
      setActiveStep(3);
      // Skip the template processing simulation since we already have the template data
      await new Promise(resolve => setTimeout(resolve, 1000)); // Just show processing step

      // Step 4: Complete
      setActiveStep(4);
      setFingerprintData(capturedData);
      onStatusUpdate('complete');
      onCaptureComplete(thumbType, capturedData);

    } catch (err) {
      console.error('Fingerprint capture error:', err);
      setError(err.message);
      onStatusUpdate('error');
      onCaptureError(err);
    } finally {
      setCapturing(false);
    }
  }, [thumbType, onCaptureComplete, onCaptureError, onStatusUpdate, onProgressUpdate]);

  // Start capture when component mounts
  useEffect(() => {
    if (deviceConnected && thumbType) {
      startCapture();
    }
  }, [deviceConnected, thumbType, startCapture]);

  const simulateFingerPlacement = useCallback(() => {
    return new Promise((resolve, reject) => {
      // Simulate waiting for finger placement
      let waitTime = 0;
      const maxWaitTime = 10000; // 10 seconds timeout

      const checkInterval = setInterval(() => {
        waitTime += 100;
        const progressPercent = Math.min((waitTime / 3000) * 100, 100);
        setProgress(progressPercent);
        onProgressUpdate(progressPercent);

        if (waitTime >= 3000) {
          clearInterval(checkInterval);
          resolve();
        } else if (waitTime >= maxWaitTime) {
          clearInterval(checkInterval);
          reject(new Error('Timeout waiting for finger placement'));
        }
      }, 100);

      captureTimeoutRef.current = setTimeout(() => {
        clearInterval(checkInterval);
        reject(new Error('Timeout waiting for finger placement'));
      }, maxWaitTime);
    });
  }, [onProgressUpdate]);

  const captureFingerprintFromDevice = useCallback(async () => {
    try {
      // Use real biometric device API only
      const result = await biometricService.captureFingerprint(thumbType);

      if (result.success) {
        const template = result.data;
        setQuality(template.quality_score);

        if (!template.quality_valid) {
          throw new Error(template.quality_message || 'Fingerprint quality validation failed');
        }

        return template.template_data;
      } else {
        throw new Error(result.error || 'Fingerprint capture failed');
      }
    } catch (apiError) {
      console.error('Fingerprint capture failed:', apiError);
      throw new Error(`Device capture failed: ${apiError.message}`);
    }
  }, [thumbType]);

  const simulateTemplateProcessing = useCallback((rawData) => {
    return new Promise((resolve) => {
      // Simulate template processing
      setTimeout(() => {
        const processedTemplate = {
          ...rawData,
          processed: true,
          timestamp: new Date().toISOString(),
          deviceId: 'FS88H_001',
          templateVersion: '1.0'
        };
        resolve(processedTemplate);
      }, 1000);
    });
  }, []);



  const getStepIcon = (stepIndex) => {
    if (stepIndex < activeStep) {
      return <CheckIcon color="success" />;
    } else if (stepIndex === activeStep && capturing) {
      return <CircularProgress size={24} />;
    } else if (stepIndex === 0) {
      return <TouchIcon />;
    } else if (stepIndex === 1 || stepIndex === 2) {
      return <FingerprintIcon />;
    } else {
      return <CheckIcon />;
    }
  };

  const getStepContent = (stepIndex) => {
    switch (stepIndex) {
      case 0:
        return (
          <Box sx={{ py: 2 }}>
            <Typography variant="body2" color="text.secondary">
              Please place your {thumbType || 'finger'} thumb firmly on the fingerprint sensor.
              Make sure your finger is clean and dry for best results.
            </Typography>
          </Box>
        );
      case 1:
        return (
          <Box sx={{ py: 2 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Capturing fingerprint... Keep your finger steady on the sensor.
            </Typography>
            <LinearProgress variant="determinate" value={progress} sx={{ mt: 1 }} />
          </Box>
        );
      case 2:
        return (
          <Box sx={{ py: 2 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Processing fingerprint template and validating quality...
            </Typography>
            {quality > 0 && (
              <Typography variant="body2" color="success.main">
                Quality Score: {quality}%
              </Typography>
            )}
          </Box>
        );
      case 3:
        return (
          <Box sx={{ py: 2 }}>
            <Typography variant="body2" color="success.main">
              ✅ Fingerprint captured successfully with {quality}% quality!
            </Typography>
          </Box>
        );
      default:
        return null;
    }
  };

  if (!deviceConnected) {
    return (
      <Alert severity="error">
        Fingerprint device is not connected. Please check the device connection and try again.
      </Alert>
    );
  }

  return (
    <Box sx={{ p: 2 }}>
      {/* Fingerprint Visual */}
      <Paper sx={{ p: 3, textAlign: 'center', mb: 3, bgcolor: 'grey.50' }}>
        <FingerprintIcon 
          sx={{ 
            fontSize: 120, 
            color: activeStep >= 3 ? 'success.main' : 'primary.main',
            mb: 2
          }} 
        />
        <Typography variant="h6" gutterBottom>
          {thumbType ? `${thumbType.charAt(0).toUpperCase() + thumbType.slice(1)} Thumb Capture` : 'Fingerprint Capture'}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {capturing ? 'Capturing in progress...' : 'Ready to capture'}
        </Typography>
      </Paper>

      {/* Error Display */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Capture Steps */}
      <Stepper activeStep={activeStep} orientation="vertical">
        {steps.map((label, index) => (
          <Step key={label}>
            <StepLabel icon={getStepIcon(index)}>
              {label}
            </StepLabel>
            <StepContent>
              {getStepContent(index)}
            </StepContent>
          </Step>
        ))}
      </Stepper>

      {/* Retry Button */}
      {error && (
        <Box sx={{ mt: 3, textAlign: 'center' }}>
          <Button
            variant="contained"
            onClick={startCapture}
            startIcon={<FingerprintIcon />}
          >
            Retry Capture
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default FingerprintCapture;
