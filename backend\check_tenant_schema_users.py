#!/usr/bin/env python3
"""
Script to check users within tenant schemas and fix role issues.
"""

import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from tenants.models import Tenant
from users.models import UserRole
from django_tenants.utils import schema_context

User = get_user_model()

def check_users_in_tenant_schema(tenant):
    """Check users within a specific tenant's schema."""
    print(f"\n=== CHECKING USERS IN {tenant.name.upper()} SCHEMA ===")
    print(f"Schema: {tenant.schema_name}")
    
    try:
        with schema_context(tenant.schema_name):
            users = User.objects.all()
            print(f"Found {users.count()} users in {tenant.name} schema:\n")
            
            role_mismatches = []
            
            for user in users:
                print(f"👤 User: {user.username}")
                print(f"   Email: {user.email}")
                print(f"   Role: {user.role}")
                print(f"   Active: {user.is_active}")
                print(f"   Superuser: {user.is_superuser}")
                if hasattr(user, 'tenant') and user.tenant:
                    print(f"   Tenant: {user.tenant.name} (ID: {user.tenant.id})")
                else:
                    print(f"   Tenant: None")
                
                # Check for role-tenant mismatches
                valid_roles = {
                    'city': ['city_admin'],
                    'subcity': ['subcity_admin'],
                    'kebele': ['kebele_leader', 'clerk', 'kebele_admin']
                }
                
                if tenant.type in valid_roles and user.role not in valid_roles[tenant.type]:
                    print(f"   ❌ ROLE MISMATCH: {user.role} not valid for {tenant.type} tenant")
                    print(f"   Valid roles: {', '.join(valid_roles[tenant.type])}")
                    role_mismatches.append((user, tenant))
                else:
                    print(f"   ✅ Role is appropriate for {tenant.type} tenant")
                
                print()
            
            return role_mismatches
            
    except Exception as e:
        print(f"❌ Error checking {tenant.name} schema: {e}")
        return []

def fix_user_role_in_schema(tenant, username, new_role):
    """Fix a user's role within a tenant schema."""
    print(f"\n🔧 FIXING USER ROLE IN {tenant.name.upper()} SCHEMA")
    
    try:
        with schema_context(tenant.schema_name):
            user = User.objects.get(username=username)
            old_role = user.role
            user.role = new_role
            user.save()
            
            print(f"✅ Fixed {username}: {old_role} → {new_role}")
            return True
            
    except User.DoesNotExist:
        print(f"❌ User {username} not found in {tenant.name} schema")
        return False
    except Exception as e:
        print(f"❌ Error fixing user: {e}")
        return False

def main():
    """Main function to check all tenant schemas for users and role mismatches."""
    print("GoID Tenant Schema User Checker")
    print("=" * 35)
    
    # Get all tenants
    tenants = Tenant.objects.all()
    print(f"Found {tenants.count()} tenants to check:\n")
    
    all_mismatches = []
    
    for tenant in tenants:
        print(f"🏢 Tenant: {tenant.name} (type: {tenant.type}, ID: {tenant.id})")
        
        # Skip public tenant
        if tenant.schema_name == 'public':
            print("   ⏭️  Skipping public schema")
            continue
        
        mismatches = check_users_in_tenant_schema(tenant)
        all_mismatches.extend(mismatches)
    
    # Summary of mismatches
    if all_mismatches:
        print("\n" + "=" * 50)
        print("🚨 ROLE MISMATCH SUMMARY")
        print("=" * 50)
        
        for user, tenant in all_mismatches:
            print(f"❌ {user.username} ({user.email})")
            print(f"   Current role: {user.role}")
            print(f"   Tenant: {tenant.name} (type: {tenant.type})")
            
            # Suggest fix for specific case
            if user.role == 'subcity_admin' and tenant.type == 'kebele':
                print(f"   🔧 SUGGESTED FIX: Change role to 'clerk'")
                
                # Auto-fix for veronica user
                if 'veronica' in user.username.lower() or 'veronica' in user.email.lower():
                    print(f"   🤖 AUTO-FIXING veronica user...")
                    fix_user_role_in_schema(tenant, user.username, 'clerk')
            
            print()
    else:
        print("\n✅ No role mismatches found across all tenant schemas!")

if __name__ == '__main__':
    main()
