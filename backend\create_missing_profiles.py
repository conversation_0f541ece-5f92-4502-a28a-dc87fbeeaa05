#!/usr/bin/env python3
"""
Create missing tenant profiles for Bole 01 kebele
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
sys.path.append('/app')
django.setup()

from tenants.models import Tenant, Kebele, SubCity, CityAdministration

def create_missing_profiles():
    # Get the Bole 01 tenant and its hierarchy
    bole_tenant = Tenant.objects.get(name='Bole 01')
    bole_subcity_tenant = bole_tenant.parent
    city_tenant = bole_subcity_tenant.parent if bole_subcity_tenant else None

    print(f'Kebele: {bole_tenant.name} (ID: {bole_tenant.id})')
    print(f'Subcity: {bole_subcity_tenant.name} (ID: {bole_subcity_tenant.id})')
    if city_tenant:
        print(f'City: {city_tenant.name} (ID: {city_tenant.id})')

    # Create CityAdministration profile if needed
    city_profile = None
    if city_tenant:
        city_profile, created = CityAdministration.objects.get_or_create(
            tenant=city_tenant,
            defaults={
                'city_name': city_tenant.name,
                'city_name_am': 'አዲስ አበባ',
                'is_active': True
            }
        )
        status = "created" if created else "exists"
        print(f'City profile: {status} - {city_profile.city_name}')

    # Create SubCity profile
    subcity_profile, created = SubCity.objects.get_or_create(
        tenant=bole_subcity_tenant,
        defaults={
            'name': bole_subcity_tenant.name,
            'name_am': 'ቦሌ',
            'city_administration': city_profile if city_tenant else None,
            'is_active': True
        }
    )
    status = "created" if created else "exists"
    print(f'Subcity profile: {status} - {subcity_profile.name}')

    # Create Kebele profile
    kebele_profile, created = Kebele.objects.get_or_create(
        tenant=bole_tenant,
        defaults={
            'name': bole_tenant.name,
            'name_am': 'ቦሌ 01',
            'sub_city': subcity_profile,
            'is_active': True
        }
    )
    status = "created" if created else "exists"
    print(f'Kebele profile: {status} - {kebele_profile.name}')

if __name__ == '__main__':
    create_missing_profiles()
    print("Profile creation complete!")
