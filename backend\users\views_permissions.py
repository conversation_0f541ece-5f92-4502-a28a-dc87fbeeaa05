from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from django.db import transaction
from .models import Permission, RolePermission, UserPermission, UserRole
from .serializers_permissions import (
    PermissionSerializer, RolePermissionSerializer, UserPermissionSerializer,
    UserPermissionCreateSerializer, UserPermissionSummarySerializer,
    RolePermissionBulkUpdateSerializer
)
from common.dynamic_permissions import require_permissions

User = get_user_model()


class PermissionViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for viewing available permissions.
    """
    queryset = Permission.objects.filter(is_active=True).order_by('category', 'name')
    serializer_class = PermissionSerializer
    permission_classes = [permissions.IsAuthenticated]
    filterset_fields = ['category', 'is_active']
    search_fields = ['name', 'codename', 'description']

    def get_permissions(self):
        """
        Only users with manage_permissions can view permissions.
        """
        if self.action in ['list', 'retrieve']:
            return [permissions.IsAuthenticated()]
        return [permissions.IsAuthenticated()]

    def has_permission(self, request, view):
        # Allow superusers and users with manage_permissions
        if request.user.is_superuser:
            return True
        return request.user.has_custom_permission('manage_permissions')


class RolePermissionViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing role-based permissions.
    """
    queryset = RolePermission.objects.select_related('permission').order_by('role', 'permission__category')
    serializer_class = RolePermissionSerializer
    permission_classes = [permissions.IsAuthenticated]
    filterset_fields = ['role', 'is_active']

    def get_permissions(self):
        """
        Only users with manage_permissions can modify role permissions.
        """
        if self.action in ['list', 'retrieve']:
            return [permissions.IsAuthenticated()]
        return [permissions.IsAuthenticated()]

    def has_permission(self, request, view):
        if request.user.is_superuser:
            return True
        return request.user.has_custom_permission('manage_permissions')

    @action(detail=False, methods=['post'])
    def bulk_update(self, request):
        """
        Bulk update permissions for a role.
        """
        serializer = RolePermissionBulkUpdateSerializer(data=request.data)
        if serializer.is_valid():
            result = serializer.save()
            return Response(result, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def by_role(self, request):
        """
        Get permissions grouped by role.
        """
        role_permissions = {}
        for role_choice in UserRole.choices:
            role_code = role_choice[0]
            role_name = role_choice[1]
            
            permissions = RolePermission.objects.filter(
                role=role_code,
                is_active=True,
                permission__is_active=True
            ).select_related('permission')
            
            role_permissions[role_code] = {
                'role_name': role_name,
                'permissions': [
                    {
                        'codename': rp.permission.codename,
                        'name': rp.permission.name,
                        'category': rp.permission.category
                    }
                    for rp in permissions
                ]
            }
        
        return Response(role_permissions)


class UserPermissionViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing individual user permissions.
    """
    queryset = UserPermission.objects.select_related('user', 'permission', 'granted_by').order_by('-granted_at')
    serializer_class = UserPermissionSerializer
    permission_classes = [permissions.IsAuthenticated]
    filterset_fields = ['user', 'permission', 'granted']

    def get_serializer_class(self):
        if self.action == 'create':
            return UserPermissionCreateSerializer
        return UserPermissionSerializer

    def get_permissions(self):
        """
        Only users with manage_permissions can modify user permissions.
        """
        if self.action in ['list', 'retrieve']:
            return [permissions.IsAuthenticated()]
        return [permissions.IsAuthenticated()]

    def has_permission(self, request, view):
        if request.user.is_superuser:
            return True
        return request.user.has_custom_permission('manage_permissions')

    @action(detail=False, methods=['post'])
    def grant_permission(self, request):
        """
        Grant a permission to a user.
        """
        serializer = UserPermissionCreateSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            user_permission = serializer.save()
            response_serializer = UserPermissionSerializer(user_permission)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def revoke_permission(self, request):
        """
        Revoke a permission from a user.
        """
        data = request.data.copy()
        data['granted'] = False
        
        serializer = UserPermissionCreateSerializer(data=data, context={'request': request})
        if serializer.is_valid():
            user_permission = serializer.save()
            response_serializer = UserPermissionSerializer(user_permission)
            return Response(response_serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def by_user(self, request):
        """
        Get permissions for a specific user.
        """
        user_id = request.query_params.get('user_id')
        user_email = request.query_params.get('user_email')
        
        if not user_id and not user_email:
            return Response(
                {'error': 'Either user_id or user_email parameter is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            if user_id:
                user = User.objects.get(id=user_id)
            else:
                user = User.objects.get(email=user_email)
        except User.DoesNotExist:
            return Response(
                {'error': 'User not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        serializer = UserPermissionSummarySerializer(user)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def bulk_grant(self, request):
        """
        Grant multiple permissions to a user.
        """
        user_email = request.data.get('user_email')
        permission_codenames = request.data.get('permission_codenames', [])
        reason = request.data.get('reason', '')
        expires_at = request.data.get('expires_at')
        
        if not user_email or not permission_codenames:
            return Response(
                {'error': 'user_email and permission_codenames are required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            user = User.objects.get(email=user_email)
        except User.DoesNotExist:
            return Response(
                {'error': 'User not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        granted_permissions = []
        errors = []
        
        with transaction.atomic():
            for codename in permission_codenames:
                try:
                    user_permission = user.grant_permission(
                        codename, 
                        granted_by=request.user, 
                        reason=reason,
                        expires_at=expires_at
                    )
                    granted_permissions.append(codename)
                except ValueError as e:
                    errors.append(str(e))
        
        return Response({
            'granted_permissions': granted_permissions,
            'errors': errors,
            'total_granted': len(granted_permissions)
        })


class UserPermissionSummaryViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for viewing user permission summaries.
    """
    queryset = User.objects.select_related('tenant').prefetch_related('custom_permissions__permission')
    serializer_class = UserPermissionSummarySerializer
    permission_classes = [permissions.IsAuthenticated]
    filterset_fields = ['role', 'tenant', 'is_active']
    search_fields = ['email', 'username', 'first_name', 'last_name']

    def get_permissions(self):
        """
        Only users with view_users permission can view user permission summaries.
        """
        return [permissions.IsAuthenticated()]

    def has_permission(self, request, view):
        if request.user.is_superuser:
            return True
        return request.user.has_custom_permission('view_users')

    @action(detail=True, methods=['get'])
    def check_permission(self, request, pk=None):
        """
        Check if a user has a specific permission.
        """
        user = self.get_object()
        permission_codename = request.query_params.get('permission')
        
        if not permission_codename:
            return Response(
                {'error': 'permission parameter is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        has_permission = user.has_custom_permission(permission_codename)
        
        return Response({
            'user_email': user.email,
            'permission': permission_codename,
            'has_permission': has_permission
        })
