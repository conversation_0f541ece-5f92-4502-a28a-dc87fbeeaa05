#!/usr/bin/env python3
"""
IMMORTAL Fingerprint Service - ABSOLUTELY CANNOT DIE
Uses process monitoring and auto-restart to ensure the service NEVER stops
"""

import subprocess
import time
import logging
import os
import sys
import threading
import signal

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ImmortalServiceManager:
    def __init__(self):
        self.service_process = None
        self.restart_count = 0
        self.running = True
        self.monitor_thread = None
    
    def start_service(self):
        """Start the bulletproof service"""
        try:
            script_path = os.path.join(os.path.dirname(__file__), 'java_bridge_service.py')
            python_exe = sys.executable
            
            logger.info(f"🚀 Starting service: {python_exe} {script_path}")
            
            self.service_process = subprocess.Popen(
                [python_exe, script_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                cwd=os.path.dirname(__file__),
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
            )
            
            self.restart_count += 1
            logger.info(f"✅ Service started with PID: {self.service_process.pid}")
            logger.info(f"🔄 Restart count: {self.restart_count}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start service: {e}")
            return False
    
    def monitor_service(self):
        """Monitor the service and restart if it dies"""
        while self.running:
            try:
                if self.service_process is None:
                    logger.warning("⚠️ Service process is None, starting...")
                    self.start_service()
                    time.sleep(5)
                    continue
                
                # Check if process is still running
                poll_result = self.service_process.poll()
                
                if poll_result is not None:
                    # Process has died
                    logger.error(f"💀 Service died with exit code: {poll_result}")
                    if poll_result == 3221225477:
                        logger.error("💀 Exit code 3221225477 = Memory access violation (likely SDK crash)")
                        logger.info("🔄 This is expected during fingerprint captures - restarting immediately...")
                        time.sleep(1)  # Faster restart for SDK crashes
                    else:
                        logger.info("🔄 Auto-restarting service in 3 seconds...")
                        time.sleep(3)
                    
                    self.service_process = None
                    if not self.start_service():
                        logger.error("❌ Failed to restart service, retrying in 10 seconds...")
                        time.sleep(10)
                    
                else:
                    # Process is running - check less frequently to reduce spam
                    if self.restart_count % 5 == 1:  # Log every 5th check
                        logger.info(f"💓 Service heartbeat - PID: {self.service_process.pid}, Restarts: {self.restart_count}")
                
                time.sleep(10)  # Check every 10 seconds for faster recovery
                
            except Exception as monitor_error:
                logger.error(f"❌ Monitor error: {monitor_error}")
                time.sleep(5)
    
    def start_monitoring(self):
        """Start the immortal monitoring system"""
        logger.info("🧬 Starting IMMORTAL Service Manager")
        logger.info("🛡️ This service manager CANNOT be killed except by Task Manager")
        
        # Install signal handlers to ignore termination attempts
        def ignore_signal(signum, frame):
            logger.warning(f"⚠️ Ignoring termination signal {signum} - service is IMMORTAL")
        
        try:
            signal.signal(signal.SIGTERM, ignore_signal)
            signal.signal(signal.SIGINT, ignore_signal)
            if hasattr(signal, 'SIGBREAK'):
                signal.signal(signal.SIGBREAK, ignore_signal)
            logger.info("🛡️ Immortal signal handlers installed")
        except Exception as signal_error:
            logger.warning(f"⚠️ Could not install all signal handlers: {signal_error}")
        
        # Start initial service
        if not self.start_service():
            logger.error("❌ Failed to start initial service")
            return False
        
        # Start monitoring thread
        self.monitor_thread = threading.Thread(target=self.monitor_service, daemon=False)
        self.monitor_thread.start()
        
        logger.info("🧬 IMMORTAL monitoring started")
        
        # Keep main thread alive
        main_heartbeat_count = 0
        try:
            while self.running:
                time.sleep(60)  # Main thread heartbeat every minute
                main_heartbeat_count += 1
                if main_heartbeat_count % 5 == 1:  # Log every 5 minutes
                    logger.info(f"🧬 IMMORTAL Manager running - Service PID: {self.service_process.pid if self.service_process else 'None'}, Restarts: {self.restart_count}")
                    logger.info(f"🌐 Service available at: http://localhost:8001")
        except KeyboardInterrupt:
            logger.info("🛑 Received Ctrl+C - but service is IMMORTAL, ignoring...")
            # Continue running anyway
            while True:
                time.sleep(60)
                logger.info("🧬 IMMORTAL Manager continues despite Ctrl+C")
    
    def stop(self):
        """Stop the immortal service (only for emergency use)"""
        logger.info("🛑 Emergency stop requested")
        self.running = False
        
        if self.service_process:
            try:
                self.service_process.terminate()
                time.sleep(5)
                if self.service_process.poll() is None:
                    self.service_process.kill()
            except Exception as stop_error:
                logger.error(f"❌ Error stopping service: {stop_error}")

def main():
    """Main immortal service manager"""
    logger.info("🧬 IMMORTAL Fingerprint Service Manager Starting")
    logger.info("=" * 80)
    logger.info("🛡️ This service is IMMORTAL - it will restart automatically")
    logger.info("🔄 If the service dies, it will be restarted within seconds")
    logger.info("💀 The service CANNOT die permanently")
    logger.info("🧬 Only Task Manager or system shutdown can stop this")
    logger.info("🌐 Service will be available at: http://localhost:8001")
    logger.info("📱 Futronic FS88H ANSI/ISO SDK fingerprint service")
    logger.info("☕ Using Java bridge for maximum stability")
    logger.info("=" * 80)
    
    manager = ImmortalServiceManager()
    
    try:
        manager.start_monitoring()
    except Exception as critical_error:
        logger.error(f"💀 CRITICAL ERROR in immortal manager: {critical_error}")
        # Even if the manager fails, try to restart it
        time.sleep(10)
        logger.info("🧬 Attempting to restart immortal manager...")
        main()  # Recursive restart

if __name__ == '__main__':
    main()
