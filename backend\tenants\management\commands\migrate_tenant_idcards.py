from django.core.management.base import BaseCommand
from django.db import connection
from django_tenants.utils import schema_context
from django.core.management import call_command
from tenants.models import Tenant


class Command(BaseCommand):
    help = 'Run ID card migrations on all tenant schemas'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🔧 Running ID card migrations on all tenant schemas...'))
        
        # Get all tenants
        tenants = Tenant.objects.all()
        self.stdout.write(f'📋 Found {tenants.count()} tenants')
        
        success_count = 0
        error_count = 0
        
        for tenant in tenants:
            self.stdout.write(f'\n🏢 Migrating tenant: {tenant.name} ({tenant.schema_name})')
            
            try:
                with schema_context(tenant.schema_name):
                    # Run migrations for this tenant schema
                    call_command('migrate', verbosity=0)
                    self.stdout.write(f'  ✅ Successfully migrated {tenant.name}')
                    success_count += 1
            except Exception as e:
                self.stdout.write(f'  ❌ Error migrating {tenant.name}: {str(e)}')
                error_count += 1
        
        self.stdout.write(f'\n📊 Summary:')
        self.stdout.write(f'  ✅ Successfully migrated: {success_count} tenants')
        if error_count > 0:
            self.stdout.write(f'  ❌ Errors: {error_count} tenants')
        
        # Verify ID card tables were created
        self.stdout.write(f'\n🔍 Verification:')
        cursor = connection.cursor()
        
        for tenant in tenants:
            try:
                # Check for ID card tables
                cursor.execute(f"SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = '{tenant.schema_name}' AND table_name = 'idcards_idcard');")
                has_idcard_table = cursor.fetchone()[0]
                
                # Check for ID card template tables
                cursor.execute(f"SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = '{tenant.schema_name}' AND table_name = 'idcards_idcardtemplate');")
                has_template_table = cursor.fetchone()[0]
                
                status_icon = '✅' if (has_idcard_table and has_template_table) else '❌'
                self.stdout.write(f'  {status_icon} {tenant.name}: idcards_idcard={has_idcard_table}, idcards_idcardtemplate={has_template_table}')
                
            except Exception as e:
                self.stdout.write(f'  ❌ {tenant.name}: Error checking tables - {str(e)}')
        
        self.stdout.write(self.style.SUCCESS('\n🎉 ID card migration completed!'))
