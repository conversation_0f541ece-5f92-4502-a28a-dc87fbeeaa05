#!/usr/bin/env python3
"""
Test Real Fingerprint Capture + Duplicate Detection
"""

import urllib.request
import urllib.parse
import json
import base64
import time

def capture_real_fingerprint(thumb_type):
    """Capture a real fingerprint using JAR bridge"""
    
    print(f"\n📸 Capturing {thumb_type} thumb...")
    print("💡 JAR application will open - please capture your fingerprint")
    
    try:
        capture_data = json.dumps({"thumb_type": thumb_type}).encode()
        
        req = urllib.request.Request(
            "http://localhost:8001/api/capture/fingerprint",
            data=capture_data,
            headers={'Content-Type': 'application/json'}
        )
        
        start_time = time.time()
        response = urllib.request.urlopen(req, timeout=40)
        elapsed = time.time() - start_time
        
        if response.getcode() == 200:
            result = json.loads(response.read().decode())
            
            if result.get('success'):
                data = result.get('data', {})
                print(f"✅ {thumb_type} thumb captured in {elapsed:.1f}s")
                print(f"   Quality: {data.get('quality_score')}%")
                print(f"   Real capture: {data.get('real_capture')}")
                print(f"   Template size: {len(data.get('template', ''))}")
                
                return data.get('template')
            else:
                print(f"❌ Capture failed: {result.get('error')}")
                return None
        else:
            print(f"❌ HTTP error: {response.getcode()}")
            return None
            
    except Exception as e:
        print(f"❌ Capture error: {e}")
        return None

def test_duplicate_detection(left_template, right_template):
    """Test duplicate detection with captured templates"""
    
    print(f"\n🔍 Testing duplicate detection...")
    print("💡 This will check if fingerprints already exist in the system")
    
    try:
        # Prepare duplicate check request
        duplicate_data = {
            "left_thumb_fingerprint": left_template,
            "right_thumb_fingerprint": right_template
        }
        
        req_data = json.dumps(duplicate_data).encode()
        
        req = urllib.request.Request(
            "http://localhost:8000/api/biometrics/check-duplicates/",
            data=req_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print("📡 Sending duplicate check request to GoID backend...")
        response = urllib.request.urlopen(req, timeout=10)
        
        if response.getcode() == 200:
            result = json.loads(response.read().decode())
            
            if result.get('success'):
                data = result.get('data', {})
                has_duplicates = data.get('has_duplicates', False)
                matches = data.get('matches', [])
                
                print(f"✅ Duplicate check completed")
                print(f"   Has duplicates: {has_duplicates}")
                print(f"   Total matches: {len(matches)}")
                
                if has_duplicates:
                    print(f"\n🚨 DUPLICATES FOUND:")
                    for i, match in enumerate(matches, 1):
                        print(f"   Match {i}:")
                        print(f"     Citizen: {match.get('citizen_name')}")
                        print(f"     Kebele: {match.get('tenant_name')}")
                        print(f"     Match score: {match.get('match_score', 0):.2f}")
                        print(f"     Digital ID: {match.get('citizen_digital_id')}")
                else:
                    print(f"\n✅ NO DUPLICATES FOUND")
                    print("   This fingerprint is unique in the system")
                
                return has_duplicates, matches
            else:
                print(f"❌ Duplicate check failed: {result.get('error')}")
                return False, []
        else:
            print(f"❌ HTTP error: {response.getcode()}")
            return False, []
            
    except Exception as e:
        print(f"❌ Duplicate check error: {e}")
        print("💡 Make sure GoID backend is running on localhost:8000")
        return False, []

def test_fraud_prevention(left_template, right_template):
    """Test comprehensive fraud prevention system"""
    
    print(f"\n🛡️ Testing fraud prevention system...")
    
    try:
        # Prepare fraud check request
        fraud_data = {
            "citizen_data": {
                "first_name": "Test",
                "last_name": "User",
                "date_of_birth": "1990-01-01",
                "phone_number": "+251911123456"
            },
            "biometric_data": {
                "left_thumb_fingerprint": left_template,
                "right_thumb_fingerprint": right_template
            }
        }
        
        req_data = json.dumps(fraud_data).encode()
        
        req = urllib.request.Request(
            "http://localhost:8000/api/biometrics/fraud-prevention/check/",
            data=req_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print("📡 Sending fraud prevention check...")
        response = urllib.request.urlopen(req, timeout=10)
        
        if response.getcode() == 200:
            result = json.loads(response.read().decode())
            
            if result.get('success'):
                data = result.get('data', {})
                fraud_detected = data.get('fraud_detected', False)
                risk_level = data.get('risk_level', 'unknown')
                indicators = data.get('indicators', [])
                
                print(f"✅ Fraud prevention check completed")
                print(f"   Fraud detected: {fraud_detected}")
                print(f"   Risk level: {risk_level.upper()}")
                
                if fraud_detected:
                    print(f"\n🚨 FRAUD DETECTED:")
                    print(f"   Risk level: {risk_level.upper()}")
                    for indicator in indicators:
                        print(f"   - {indicator}")
                else:
                    print(f"\n✅ NO FRAUD DETECTED")
                    print("   Registration can proceed safely")
                
                return fraud_detected, risk_level
            else:
                print(f"❌ Fraud check failed: {result.get('error')}")
                return False, 'unknown'
        else:
            print(f"❌ HTTP error: {response.getcode()}")
            return False, 'unknown'
            
    except Exception as e:
        print(f"❌ Fraud check error: {e}")
        return False, 'unknown'

def main():
    print("=" * 60)
    print("  REAL FINGERPRINT CAPTURE + DUPLICATE DETECTION TEST")
    print("=" * 60)
    
    print("\nThis test will:")
    print("1. Capture REAL fingerprints using your JAR application")
    print("2. Test duplicate detection across all kebeles")
    print("3. Test comprehensive fraud prevention")
    print("4. Verify the complete biometric security system")
    
    print("\nPrerequisites:")
    print("✅ JAR Bridge service running (localhost:8001)")
    print("✅ GoID backend running (localhost:8000)")
    print("✅ Futronic device connected")
    
    input("\nPress Enter to start the test...")
    
    # Step 1: Capture real fingerprints
    print("\n" + "="*50)
    print("STEP 1: REAL FINGERPRINT CAPTURE")
    print("="*50)
    
    left_template = capture_real_fingerprint("left")
    if not left_template:
        print("❌ Left thumb capture failed - aborting test")
        return
    
    right_template = capture_real_fingerprint("right")
    if not right_template:
        print("❌ Right thumb capture failed - aborting test")
        return
    
    print(f"\n✅ REAL FINGERPRINTS CAPTURED:")
    print(f"   Left thumb template: {len(left_template)} characters")
    print(f"   Right thumb template: {len(right_template)} characters")
    print(f"   Templates are different: {left_template != right_template}")
    
    # Step 2: Test duplicate detection
    print("\n" + "="*50)
    print("STEP 2: DUPLICATE DETECTION")
    print("="*50)
    
    has_duplicates, matches = test_duplicate_detection(left_template, right_template)
    
    # Step 3: Test fraud prevention
    print("\n" + "="*50)
    print("STEP 3: FRAUD PREVENTION")
    print("="*50)
    
    fraud_detected, risk_level = test_fraud_prevention(left_template, right_template)
    
    # Final summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    print(f"✅ Real fingerprint capture: SUCCESS")
    print(f"✅ Left thumb: {len(left_template)} chars")
    print(f"✅ Right thumb: {len(right_template)} chars")
    print(f"✅ Unique templates: {left_template != right_template}")
    
    print(f"\n🔍 Duplicate detection: {'DUPLICATES FOUND' if has_duplicates else 'NO DUPLICATES'}")
    if has_duplicates:
        print(f"   Found {len(matches)} matching citizens")
    
    print(f"\n🛡️ Fraud prevention: {'FRAUD DETECTED' if fraud_detected else 'NO FRAUD'}")
    print(f"   Risk level: {risk_level.upper()}")
    
    if not has_duplicates and not fraud_detected:
        print(f"\n🎉 SYSTEM WORKING PERFECTLY!")
        print(f"✅ Real fingerprints captured")
        print(f"✅ No duplicates found")
        print(f"✅ No fraud detected")
        print(f"✅ Registration would be allowed")
    else:
        print(f"\n🚨 SECURITY SYSTEM ACTIVE!")
        print(f"✅ Real fingerprints captured")
        print(f"✅ Duplicate/fraud detection working")
        print(f"✅ System protecting against fraud")
    
    print("\nTest completed.")
    input("Press Enter to exit...")

if __name__ == '__main__':
    main()
