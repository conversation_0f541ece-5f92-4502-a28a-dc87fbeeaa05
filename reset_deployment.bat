@echo off
echo 🔄 Resetting GoID Deployment
echo ============================
echo.

REM Stop all containers
echo 🛑 Stopping all containers...
docker-compose -f docker-compose.production.yml down

REM Remove containers and volumes to start fresh
echo 🧹 Removing containers and volumes...
docker-compose -f docker-compose.production.yml down -v

REM Remove any orphaned containers
echo 🧹 Cleaning up orphaned containers...
docker container prune -f

REM Remove unused volumes
echo 🧹 Cleaning up unused volumes...
docker volume prune -f

REM Copy environment file
echo 📋 Setting up environment...
if not exist .env (
    copy .env.production .env
    echo ✅ Environment file created
) else (
    echo ✅ Environment file already exists
)

echo.
echo 🔄 Reset complete! Now run deploy_production.bat
echo.
pause
