# Role Creation 500 Error Fix

## Issue Identified

The 500 Internal Server Error was caused by missing permission assignment logic in the `TenantRoleManagementSerializer._create_tenant_role()` method.

## Root Cause

The original implementation was:
1. ✅ Creating the role successfully
2. ❌ **Missing permission assignment** - The `permission_codenames` from the frontend were not being processed and assigned to the role

## Fix Applied

### 1. **Enhanced Permission Assignment Logic**

```python
def _create_tenant_role(self, tenant, creator):
    try:
        role_data = self.validated_data['role_data'].copy()
        
        # Extract permission_codenames before creating role
        permission_codenames = role_data.pop('permission_codenames', [])
        
        # Create role with tenant scope
        role_data['scope_tenant'] = tenant
        role_data['is_global'] = False
        role_data['created_by'] = creator
        
        # Generate unique codename
        base_codename = role_data.get('codename', 'custom_role')
        unique_codename = f"{base_codename}_{tenant.type}_{tenant.id}"
        
        # Check for existing role
        if Role.objects.filter(codename=unique_codename).exists():
            raise serializers.ValidationError(
                f"Role with codename '{unique_codename}' already exists"
            )
        
        role_data['codename'] = unique_codename
        
        # Create the role
        role = Role.objects.create(**role_data)
        
        # ✅ NEW: Assign permissions if provided
        permissions_assigned = 0
        if permission_codenames:
            permissions = Permission.objects.filter(
                codename__in=permission_codenames, 
                is_active=True
            )
            
            # Create RolePermission objects
            role_permissions = [
                RolePermission(role_obj=role, permission=permission)
                for permission in permissions
            ]
            RolePermission.objects.bulk_create(role_permissions)
            permissions_assigned = len(role_permissions)
        
        return {
            'action': 'create_role',
            'tenant': tenant.name,
            'role': {
                'id': role.id,
                'codename': role.codename,
                'name': role.name,
                'scope_tenant': tenant.name,
                'permissions_assigned': permissions_assigned  # ✅ NEW
            },
            'message': f"Role '{role.name}' created successfully with {permissions_assigned} permissions"
        }
        
    except Exception as e:
        # Enhanced error handling
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error creating tenant role: {e}", exc_info=True)
        
        if isinstance(e, serializers.ValidationError):
            raise e
        else:
            raise serializers.ValidationError(f"Failed to create role: {str(e)}")
```

### 2. **Enhanced Validation**

```python
def validate(self, data):
    action = data.get('action')
    
    if action == 'create_role':
        if not data.get('role_data'):
            raise serializers.ValidationError("role_data is required for create_role action")
        
        # ✅ NEW: Validate role_data structure
        role_data = data.get('role_data', {})
        required_fields = ['codename', 'name']
        for field in required_fields:
            if not role_data.get(field):
                raise serializers.ValidationError(f"role_data.{field} is required")
        
        # ✅ NEW: Validate permission_codenames if provided
        permission_codenames = role_data.get('permission_codenames', [])
        if permission_codenames:
            existing_permissions = set(
                Permission.objects.filter(
                    codename__in=permission_codenames, 
                    is_active=True
                ).values_list('codename', flat=True)
            )
            invalid_permissions = set(permission_codenames) - existing_permissions
            if invalid_permissions:
                raise serializers.ValidationError(
                    f"The following permissions do not exist: {', '.join(invalid_permissions)}"
                )
    
    return data
```

### 3. **Better Error Handling**

- **Logging**: Added proper logging for debugging
- **Validation**: Enhanced validation for role data and permissions
- **Unique Codenames**: Check for existing roles to prevent conflicts
- **Detailed Responses**: Return more information about created roles

## Testing the Fix

### 1. **Backend Test**

```python
# In Django shell
from users.models import User, Role, Permission, RolePermission
from tenants.models import Tenant
from users.serializers_roles import TenantRoleManagementSerializer

# Get test data
test_tenant = Tenant.objects.first()
test_user = User.objects.first()

# Test role creation
test_data = {
    'tenant_id': test_tenant.id,
    'action': 'create_role',
    'role_data': {
        'codename': 'emergency_coordinator',
        'name': 'Emergency Coordinator',
        'description': 'Emergency response coordinator',
        'role_type': 'custom',
        'level': 50,
        'permission_codenames': ['view_citizens', 'create_citizens', 'print_id_cards']
    }
}

# Mock request
class MockRequest:
    def __init__(self, user):
        self.user = user

context = {'request': MockRequest(test_user)}

# Test serializer
serializer = TenantRoleManagementSerializer(data=test_data, context=context)
if serializer.is_valid():
    result = serializer.save()
    print("Success:", result)
else:
    print("Errors:", serializer.errors)
```

### 2. **Frontend Test**

The enhanced permission checkbox interface should now work correctly:

```javascript
// Frontend role creation request
const roleData = {
  tenant_id: selectedKebele.id,
  role_data: {
    codename: 'emergency_coordinator',
    name: 'Emergency Coordinator',
    description: 'Emergency response role',
    role_type: 'custom',
    level: 50,
    permission_codenames: ['view_citizens', 'create_citizens', 'print_id_cards']
  }
};

// This should now work without 500 errors
const response = await axios.post('/api/auth/tenant-role-management/create_tenant_role/', roleData);
```

## Expected Results

### ✅ **Successful Role Creation**

```json
{
  "action": "create_role",
  "tenant": "Kebele 1-A",
  "role": {
    "id": 123,
    "codename": "emergency_coordinator_kebele_7",
    "name": "Emergency Coordinator",
    "scope_tenant": "Kebele 1-A",
    "permissions_assigned": 3
  },
  "message": "Role 'Emergency Coordinator' created successfully with 3 permissions"
}
```

### ✅ **Database Verification**

```python
# Check created role
role = Role.objects.get(codename='emergency_coordinator_kebele_7')
print(f"Role: {role.name}")
print(f"Tenant: {role.scope_tenant.name}")
print(f"Permissions: {role.role_permissions.count()}")

# Check assigned permissions
for rp in role.role_permissions.all():
    print(f"  - {rp.permission.name} ({rp.permission.codename})")
```

## Additional Improvements

### 1. **Enhanced Frontend Error Handling**

The frontend now shows better error messages:
- **Server errors**: Detailed troubleshooting information
- **Validation errors**: Specific field-level feedback
- **API status**: Real-time connection monitoring

### 2. **Permission Checkbox Enhancements**

- **Smart categorization**: Permissions grouped by functional area
- **Visual feedback**: Selection counters and status indicators
- **Bulk actions**: Select/clear entire categories
- **Enhanced validation**: Real-time form validation

### 3. **Role Filtering**

- **Kebele-appropriate roles**: Only relevant roles shown
- **Administrative exclusion**: High-level roles filtered out
- **Scope indicators**: Clear distinction between global and tenant-specific roles

## Troubleshooting

If you still encounter issues:

1. **Check Django Logs**: Look for detailed error messages
2. **Verify Permissions**: Ensure permissions exist in database
3. **Test API Directly**: Use Django shell to test serializer
4. **Check Migrations**: Ensure all migrations are applied
5. **Verify User Permissions**: Ensure user can manage the tenant

## Summary

The 500 error has been fixed by:
- ✅ **Adding missing permission assignment logic**
- ✅ **Enhanced validation and error handling**
- ✅ **Better logging for debugging**
- ✅ **Improved frontend error messages**
- ✅ **Enhanced permission selection interface**

Role creation should now work correctly with proper permission assignment!
