#!/bin/bash

# Fix Backend 500 Errors Script
# This script attempts to fix common backend issues causing 500 errors

echo "🔧 Fixing Backend 500 Errors"
echo "============================="

# Step 1: Stop all services
echo "📍 Step 1: Stopping all services..."
docker-compose down

# Step 2: Start database first
echo "📍 Step 2: Starting database..."
docker-compose up -d db

echo "Waiting for database to be ready..."
sleep 15

# Step 3: Start backend
echo "📍 Step 3: Starting backend..."
docker-compose up -d backend

echo "Waiting for backend to initialize..."
sleep 20

# Step 4: Run migrations
echo "📍 Step 4: Running database migrations..."
echo "Running shared migrations..."
docker-compose exec backend python manage.py migrate_schemas --shared

echo "Running tenant migrations..."
docker-compose exec backend python manage.py migrate_schemas

# Step 5: Collect static files
echo "📍 Step 5: Collecting static files..."
docker-compose exec backend python manage.py collectstatic --noinput

# Step 6: Create superuser
echo "📍 Step 6: Creating superuser..."
docker-compose exec -T backend python manage.py shell << 'EOF'
from django.contrib.auth import get_user_model
User = get_user_model()

try:
    # Check if superuser exists
    if not User.objects.filter(email='<EMAIL>').exists():
        print("Creating superuser...")
        user = User.objects.create_superuser(
            email='<EMAIL>',
            username='admin',
            password='admin123',
            first_name='Super',
            last_name='Admin'
        )
        print(f"✅ Superuser created: {user.email}")
    else:
        print("✅ Superuser already exists")
        # Ensure the user is properly configured
        user = User.objects.get(email='<EMAIL>')
        user.is_superuser = True
        user.is_staff = True
        user.is_active = True
        user.save()
        print("✅ Superuser configuration updated")
        
except Exception as e:
    print(f"❌ Error creating superuser: {e}")
EOF

# Step 7: Start frontend
echo "📍 Step 7: Starting frontend..."
docker-compose up -d frontend

# Step 8: Wait for all services
echo "📍 Step 8: Waiting for all services to be ready..."
sleep 30

# Step 9: Test the services
echo "📍 Step 9: Testing services..."

echo "Testing backend health..."
curl -I http://10.139.8.141:8000/admin/ 2>/dev/null | head -1

echo "Testing authentication..."
AUTH_RESPONSE=$(curl -s -X POST http://10.139.8.141:8000/api/auth/token/ \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}')

if echo "$AUTH_RESPONSE" | grep -q "access"; then
    echo "✅ Authentication working"
    
    # Extract token and test tenants endpoint
    TOKEN=$(echo "$AUTH_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin)['access'])" 2>/dev/null)
    
    if [ ! -z "$TOKEN" ]; then
        echo "Testing tenants endpoint..."
        TENANTS_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" http://10.139.8.141:8000/api/tenants/)
        
        if echo "$TENANTS_RESPONSE" | grep -q -E '\[|\{'; then
            echo "✅ Tenants endpoint working"
        else
            echo "❌ Tenants endpoint failed"
            echo "Response: $TENANTS_RESPONSE"
        fi
    else
        echo "❌ Could not extract token"
    fi
else
    echo "❌ Authentication failed"
    echo "Response: $AUTH_RESPONSE"
fi

# Step 10: Show final status
echo ""
echo "📍 Step 10: Final service status..."
docker-compose ps

echo ""
echo "✅ Backend recovery completed!"
echo ""
echo "🔍 Test the following:"
echo "1. Backend admin: http://10.139.8.141:8000/admin/"
echo "2. Frontend: http://10.139.8.141:3000/"
echo "3. Login with: <EMAIL> / admin123"
echo ""
echo "💡 If still having issues:"
echo "1. Check logs: docker-compose logs backend"
echo "2. Check database: docker-compose logs db"
echo "3. Restart everything: docker-compose restart"
echo "4. Full reset: docker-compose down -v && docker-compose up -d"
echo ""
echo "🚨 If ERR_EMPTY_RESPONSE persists:"
echo "- Check if port 8000 is blocked by firewall"
echo "- Verify Docker network configuration"
echo "- Try: docker-compose down && docker-compose up -d"
echo "- Check: netstat -tlnp | grep 8000"
