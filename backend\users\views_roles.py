from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from django.db import transaction
from .models import Role, RoleType, UserRole, RolePermission
from .serializers_roles import (
    RoleSerializer, RoleCreateSerializer, RoleUpdateSerializer,
    UserRoleAssignmentSerializer, RoleHierarchySerializer,
    RolePermissionAssignmentSerializer, TenantRoleManagementSerializer,
    TenantUserRoleSerializer
)
from .permissions_tenant_roles import (
    CanManageTenantRoles, CanAssignTenantRoles, get_manageable_tenants_for_user,
    get_users_in_manageable_tenants, get_assignable_roles_for_user_in_tenant
)
from common.dynamic_permissions import require_permissions

User = get_user_model()


class RoleViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing dynamic roles.
    """
    queryset = Role.objects.all().order_by('-level', 'name')
    permission_classes = [permissions.IsAuthenticated]
    filterset_fields = ['role_type', 'is_active', 'is_built_in', 'level']
    search_fields = ['name', 'codename', 'description']

    def get_serializer_class(self):
        if self.action == 'create':
            return RoleCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return RoleUpdateSerializer
        return RoleSerializer

    def get_permissions(self):
        """
        Only users with manage_permissions can modify roles.
        """
        if self.action in ['list', 'retrieve']:
            return [permissions.IsAuthenticated()]
        return [permissions.IsAuthenticated()]

    def has_permission(self, request, view):
        if request.user.is_superuser:
            return True
        return request.user.has_custom_permission('manage_permissions')

    def perform_destroy(self, instance):
        """
        Soft delete - mark as inactive instead of deleting.
        Cannot delete built-in roles.
        """
        if instance.is_built_in:
            raise serializers.ValidationError("Cannot delete built-in roles")
        
        # Check if any users have this role
        if instance.users.filter(is_active=True).exists():
            raise serializers.ValidationError(
                "Cannot delete role that is assigned to active users. "
                "Please reassign users to different roles first."
            )
        
        instance.is_active = False
        instance.save()

    @action(detail=False, methods=['get'])
    def hierarchy(self, request):
        """
        Get role hierarchy showing all roles (legacy and dynamic) with their relationships.
        """
        serializer = RoleHierarchySerializer()
        data = serializer.to_representation(None)
        return Response(data)

    @action(detail=True, methods=['post'])
    def assign_permissions(self, request, pk=None):
        """
        Assign permissions to a role.
        """
        role = self.get_object()
        serializer = RolePermissionAssignmentSerializer(data=request.data)
        
        if serializer.is_valid():
            # Override role_codename with current role
            serializer.validated_data['role_codename'] = role.codename
            result = serializer.save()
            return Response(result, status=status.HTTP_200_OK)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['get'])
    def permissions(self, request, pk=None):
        """
        Get all permissions assigned to this role.
        """
        role = self.get_object()
        permissions = role.get_permissions()
        
        permission_data = [
            {
                'id': perm.id,
                'codename': perm.codename,
                'name': perm.name,
                'category': perm.category,
                'description': perm.description
            }
            for perm in permissions
        ]
        
        return Response({
            'role': role.name,
            'permissions': permission_data,
            'total_permissions': len(permission_data)
        })

    @action(detail=True, methods=['get'])
    def users(self, request, pk=None):
        """
        Get all users assigned to this role.
        """
        role = self.get_object()
        users = role.users.filter(is_active=True)
        
        user_data = [
            {
                'id': user.id,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'tenant': user.tenant.name if user.tenant else None,
                'date_joined': user.date_joined
            }
            for user in users
        ]
        
        return Response({
            'role': role.name,
            'users': user_data,
            'total_users': len(user_data)
        })

    @action(detail=False, methods=['post'])
    def create_custom_role(self, request):
        """
        Create a custom role with specific permissions and hierarchy.
        """
        serializer = RoleCreateSerializer(data=request.data, context={'request': request})
        
        if serializer.is_valid():
            role = serializer.save()
            response_serializer = RoleSerializer(role)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def available_types(self, request):
        """
        Get available role types.
        """
        return Response({
            'role_types': [
                {'value': choice[0], 'label': choice[1]}
                for choice in RoleType.choices
            ]
        })


class UserRoleManagementViewSet(viewsets.ViewSet):
    """
    API endpoint for managing user role assignments.
    """
    permission_classes = [permissions.IsAuthenticated]

    def has_permission(self, request, view):
        if request.user.is_superuser:
            return True
        return request.user.has_custom_permission('manage_permissions')

    @action(detail=False, methods=['post'])
    def assign_role(self, request):
        """
        Assign a role to a user.
        """
        serializer = UserRoleAssignmentSerializer(data=request.data, context={'request': request})
        
        if serializer.is_valid():
            result = serializer.save()
            return Response(result, status=status.HTTP_200_OK)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def bulk_assign_roles(self, request):
        """
        Assign roles to multiple users.
        """
        user_emails = request.data.get('user_emails', [])
        role_codename = request.data.get('role_codename')
        reason = request.data.get('reason', '')
        
        if not user_emails or not role_codename:
            return Response(
                {'error': 'user_emails and role_codename are required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        results = []
        errors = []
        
        with transaction.atomic():
            for email in user_emails:
                try:
                    serializer = UserRoleAssignmentSerializer(
                        data={'user_email': email, 'role_codename': role_codename, 'reason': reason},
                        context={'request': request}
                    )
                    if serializer.is_valid():
                        result = serializer.save()
                        results.append(result)
                    else:
                        errors.append({'email': email, 'errors': serializer.errors})
                except Exception as e:
                    errors.append({'email': email, 'error': str(e)})
        
        return Response({
            'successful_assignments': results,
            'errors': errors,
            'total_assigned': len(results),
            'total_errors': len(errors)
        })

    @action(detail=False, methods=['get'])
    def user_role_summary(self, request):
        """
        Get summary of users and their roles.
        """
        user_email = request.query_params.get('user_email')

        if user_email:
            try:
                user = User.objects.get(email=user_email)
                return Response({
                    'user': {
                        'email': user.email,
                        'name': f"{user.first_name} {user.last_name}",
                        'effective_role': user.effective_role,
                        'effective_role_name': user.effective_role_name,
                        'role_level': user.effective_role_level,
                        'legacy_role': user.role,
                        'dynamic_role': user.role_obj.codename if user.role_obj else None,
                        'tenant': user.tenant.name if user.tenant else None
                    }
                })
            except User.DoesNotExist:
                return Response(
                    {'error': 'User not found'},
                    status=status.HTTP_404_NOT_FOUND
                )

        # Return summary of all users
        users = User.objects.filter(is_active=True).select_related('role_obj', 'tenant')

        user_summaries = []
        for user in users:
            user_summaries.append({
                'email': user.email,
                'name': f"{user.first_name} {user.last_name}",
                'effective_role': user.effective_role,
                'effective_role_name': user.effective_role_name,
                'role_level': user.effective_role_level,
                'tenant': user.tenant.name if user.tenant else None
            })

        return Response({
            'users': user_summaries,
            'total_users': len(user_summaries)
        })

    @action(detail=False, methods=['get'])
    def user_details_with_roles(self, request):
        """
        Get detailed user information including all roles and permissions.
        """
        user_id = request.query_params.get('user_id')
        user_email = request.query_params.get('user_email')

        if not user_id and not user_email:
            return Response(
                {'error': 'user_id or user_email parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            if user_id:
                user = User.objects.get(id=user_id, is_active=True)
            else:
                user = User.objects.get(email=user_email, is_active=True)

            # Check if current user can view this user's details
            manageable_users = get_users_in_manageable_tenants(request.user)
            if user not in manageable_users and not request.user.is_superuser:
                return Response(
                    {'error': 'You do not have permission to view this user\'s details'},
                    status=status.HTTP_403_FORBIDDEN
                )

            # Get all roles for this user
            roles_info = []

            # Legacy role
            if user.role:
                legacy_role_name = dict(UserRole.choices).get(user.role, user.role)
                roles_info.append({
                    'type': 'legacy',
                    'codename': user.role,
                    'name': legacy_role_name,
                    'level': self._get_legacy_role_level(user.role),
                    'is_primary': True,
                    'is_built_in': True,
                    'assigned_date': user.date_joined,
                    'scope': 'Global',
                    'permissions': self._get_legacy_role_permissions(user.role)
                })

            # Dynamic role
            if user.role_obj:
                role_permissions = user.role_obj.get_permissions()
                permissions_list = [
                    {
                        'codename': perm.codename,
                        'name': perm.name,
                        'category': perm.category,
                        'description': perm.description
                    }
                    for perm in role_permissions
                ]

                roles_info.append({
                    'type': 'dynamic',
                    'id': user.role_obj.id,
                    'codename': user.role_obj.codename,
                    'name': user.role_obj.name,
                    'level': user.role_obj.level,
                    'is_primary': not user.role,  # Primary if no legacy role
                    'is_built_in': user.role_obj.is_built_in,
                    'assigned_date': user.role_obj.created_at,
                    'scope': user.role_obj.scope_tenant.name if user.role_obj.scope_tenant else 'Global',
                    'description': user.role_obj.description,
                    'permissions': permissions_list,
                    'permissions_count': len(permissions_list)
                })

            # Get effective permissions (combination of all roles)
            effective_permissions = user.get_all_permissions()
            effective_permissions_list = [
                {
                    'codename': perm.codename,
                    'name': perm.name,
                    'category': perm.category,
                    'description': perm.description
                }
                for perm in effective_permissions
            ]

            return Response({
                'user': {
                    'id': user.id,
                    'email': user.email,
                    'username': user.username,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'full_name': f"{user.first_name} {user.last_name}".strip(),
                    'is_active': user.is_active,
                    'is_staff': user.is_staff,
                    'is_superuser': user.is_superuser,
                    'date_joined': user.date_joined,
                    'last_login': user.last_login,
                    'tenant': {
                        'id': user.tenant.id if user.tenant else None,
                        'name': user.tenant.name if user.tenant else None,
                        'type': user.tenant.type if user.tenant else None,
                        'parent': user.tenant.parent.name if user.tenant and user.tenant.parent else None
                    } if user.tenant else None
                },
                'roles': {
                    'assigned_roles': roles_info,
                    'total_roles': len(roles_info),
                    'effective_role': user.effective_role,
                    'effective_role_name': user.effective_role_name,
                    'effective_level': user.effective_role_level
                },
                'permissions': {
                    'effective_permissions': effective_permissions_list,
                    'total_permissions': len(effective_permissions_list),
                    'permissions_by_category': self._group_permissions_by_category(effective_permissions_list)
                }
            })

        except User.DoesNotExist:
            return Response(
                {'error': 'User not found'},
                status=status.HTTP_404_NOT_FOUND
            )

    def _get_legacy_role_permissions(self, role_codename):
        """Get permissions for legacy roles (simplified)"""
        # This is a simplified version - you might want to implement actual permission mapping
        role_permissions = {
            'superadmin': ['All permissions'],
            'city_admin': ['Manage city', 'Manage subcities', 'Manage users'],
            'subcity_admin': ['Manage subcity', 'Manage kebeles', 'Manage users'],
            'kebele_admin': ['Manage kebele', 'Manage users'],
            'kebele_leader': ['View citizens', 'Create citizens', 'View ID cards'],
            'clerk': ['View citizens', 'Create citizens']
        }

        permissions = role_permissions.get(role_codename, [])
        return [{'name': perm, 'codename': perm.lower().replace(' ', '_')} for perm in permissions]

    def _group_permissions_by_category(self, permissions_list):
        """Group permissions by category"""
        categories = {}
        for perm in permissions_list:
            category = perm.get('category', 'Other')
            if category not in categories:
                categories[category] = []
            categories[category].append(perm)
        return categories

    @action(detail=False, methods=['get'])
    def available_roles(self, request):
        """
        Get all available roles (legacy and dynamic) for assignment.
        """
        # Legacy roles
        legacy_roles = [
            {
                'codename': choice[0],
                'name': choice[1],
                'type': 'legacy',
                'is_built_in': True
            }
            for choice in UserRole.choices
        ]

        # Dynamic roles
        dynamic_roles = []
        for role in Role.objects.filter(is_active=True):
            dynamic_roles.append({
                'codename': role.codename,
                'name': role.name,
                'type': 'dynamic',
                'is_built_in': role.is_built_in,
                'level': role.level
            })

        return Response({
            'legacy_roles': legacy_roles,
            'dynamic_roles': dynamic_roles,
            'all_roles': legacy_roles + dynamic_roles
        })

    @action(detail=False, methods=['get'])
    def roles_with_users(self, request):
        """
        Get all roles with their assigned users.
        """
        tenant_id = request.query_params.get('tenant_id')

        # Get manageable tenants for filtering
        manageable_tenants = get_manageable_tenants_for_user(request.user)
        manageable_tenant_ids = [t.id for t in manageable_tenants]

        # Base user queryset - only users in manageable tenants
        base_users = User.objects.filter(
            is_active=True,
            tenant_id__in=manageable_tenant_ids
        ).select_related('tenant', 'role_obj')

        if tenant_id:
            base_users = base_users.filter(tenant_id=tenant_id)

        roles_data = []

        # Legacy roles
        for role_choice in UserRole.choices:
            role_codename = role_choice[0]
            role_name = role_choice[1]

            users_with_role = base_users.filter(role=role_codename)

            user_list = []
            for user in users_with_role:
                user_list.append({
                    'id': user.id,
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'full_name': f"{user.first_name} {user.last_name}".strip(),
                    'tenant': {
                        'id': user.tenant.id if user.tenant else None,
                        'name': user.tenant.name if user.tenant else None,
                        'type': user.tenant.type if user.tenant else None
                    },
                    'date_joined': user.date_joined,
                    'last_login': user.last_login
                })

            if users_with_role.exists() or not tenant_id:  # Show all roles if no tenant filter
                roles_data.append({
                    'codename': role_codename,
                    'name': role_name,
                    'type': 'legacy',
                    'level': self._get_legacy_role_level(role_codename),
                    'is_built_in': True,
                    'is_global': True,
                    'scope_tenant': None,
                    'users': user_list,
                    'users_count': len(user_list),
                    'description': f"Built-in {role_name} role"
                })

        # Dynamic roles
        dynamic_roles = Role.objects.filter(is_active=True).prefetch_related('users')

        for role in dynamic_roles:
            # Filter users by manageable tenants
            users_with_role = role.users.filter(
                is_active=True,
                tenant_id__in=manageable_tenant_ids
            )

            if tenant_id:
                users_with_role = users_with_role.filter(tenant_id=tenant_id)

            user_list = []
            for user in users_with_role:
                user_list.append({
                    'id': user.id,
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'full_name': f"{user.first_name} {user.last_name}".strip(),
                    'tenant': {
                        'id': user.tenant.id if user.tenant else None,
                        'name': user.tenant.name if user.tenant else None,
                        'type': user.tenant.type if user.tenant else None
                    },
                    'date_joined': user.date_joined,
                    'last_login': user.last_login
                })

            if users_with_role.exists() or not tenant_id:  # Show all roles if no tenant filter
                roles_data.append({
                    'id': role.id,
                    'codename': role.codename,
                    'name': role.name,
                    'type': 'dynamic',
                    'level': role.level,
                    'is_built_in': role.is_built_in,
                    'is_global': role.is_global,
                    'scope_tenant': {
                        'id': role.scope_tenant.id if role.scope_tenant else None,
                        'name': role.scope_tenant.name if role.scope_tenant else None,
                        'type': role.scope_tenant.type if role.scope_tenant else None
                    } if role.scope_tenant else None,
                    'users': user_list,
                    'users_count': len(user_list),
                    'description': role.description,
                    'permissions_count': role.role_permissions.filter(is_active=True).count()
                })

        # Sort by level (highest first) then by name
        roles_data.sort(key=lambda x: (-x['level'], x['name']))

        return Response({
            'roles': roles_data,
            'total_roles': len(roles_data),
            'total_users': sum(role['users_count'] for role in roles_data),
            'tenant_filter': tenant_id
        })

    def _get_legacy_role_level(self, role_codename):
        """Get default level for legacy roles"""
        levels = {
            'superadmin': 100,
            'city_admin': 80,
            'subcity_admin': 60,
            'kebele_admin': 40,
            'kebele_leader': 30,
            'clerk': 10
        }
        return levels.get(role_codename, 0)


class TenantRoleManagementViewSet(viewsets.ViewSet):
    """
    API endpoint for tenant-aware role management.
    Allows subcity admins to manage roles for their child kebeles.
    """
    permission_classes = [CanManageTenantRoles]

    @action(detail=False, methods=['get'])
    def manageable_tenants(self, request):
        """
        Get all tenants that the current user can manage.
        """
        manageable_tenants = get_manageable_tenants_for_user(request.user)

        tenant_data = []
        for tenant in manageable_tenants:
            tenant_data.append({
                'id': tenant.id,
                'name': tenant.name,
                'type': tenant.type,
                'parent': tenant.parent.name if tenant.parent else None,
                'users_count': tenant.users.filter(is_active=True).count(),
                'can_create_roles': True  # User can create roles for manageable tenants
            })

        return Response({
            'manageable_tenants': tenant_data,
            'total_tenants': len(tenant_data)
        })

    @action(detail=False, methods=['get'])
    def tenant_users(self, request):
        """
        Get all users in tenants that the current user can manage.
        """
        tenant_id = request.query_params.get('tenant_id')

        if tenant_id:
            try:
                from tenants.models import Tenant
                tenant = Tenant.objects.get(id=tenant_id)
                manageable_tenants = get_manageable_tenants_for_user(request.user)

                if tenant not in manageable_tenants:
                    return Response(
                        {'error': 'You do not have permission to view users in this tenant'},
                        status=status.HTTP_403_FORBIDDEN
                    )

                users = User.objects.filter(tenant=tenant, is_active=True)
            except Tenant.DoesNotExist:
                return Response(
                    {'error': 'Tenant not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
        else:
            users = get_users_in_manageable_tenants(request.user)

        serializer = TenantUserRoleSerializer(users, many=True, context={'request': request})
        return Response({
            'users': serializer.data,
            'total_users': len(serializer.data)
        })

    @action(detail=False, methods=['get'])
    def tenant_roles(self, request):
        """
        Get all roles available for a specific tenant.
        """
        tenant_id = request.query_params.get('tenant_id')

        if not tenant_id:
            return Response(
                {'error': 'tenant_id parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            from tenants.models import Tenant
            tenant = Tenant.objects.get(id=tenant_id)
            manageable_tenants = get_manageable_tenants_for_user(request.user)

            if tenant not in manageable_tenants:
                return Response(
                    {'error': 'You do not have permission to view roles for this tenant'},
                    status=status.HTTP_403_FORBIDDEN
                )

            assignable_roles = get_assignable_roles_for_user_in_tenant(request.user, tenant)

            role_data = []
            for role in assignable_roles:
                role_data.append({
                    'id': role.id if hasattr(role, 'id') else None,
                    'codename': role.codename if hasattr(role, 'codename') else role,
                    'name': role.name if hasattr(role, 'name') else role.replace('_', ' ').title(),
                    'type': 'dynamic' if hasattr(role, 'role_type') else 'legacy',
                    'level': role.level if hasattr(role, 'level') else 0,
                    'is_global': role.is_global if hasattr(role, 'is_global') else True,
                    'scope_tenant': role.scope_tenant.name if hasattr(role, 'scope_tenant') and role.scope_tenant else None
                })

            return Response({
                'tenant': {
                    'id': tenant.id,
                    'name': tenant.name,
                    'type': tenant.type
                },
                'assignable_roles': role_data,
                'total_roles': len(role_data)
            })

        except Tenant.DoesNotExist:
            return Response(
                {'error': 'Tenant not found'},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=False, methods=['post'])
    def manage_tenant_role(self, request):
        """
        Unified endpoint for tenant role management actions.
        Supports: create_role, assign_role, grant_permission
        """
        serializer = TenantRoleManagementSerializer(data=request.data, context={'request': request})

        if serializer.is_valid():
            result = serializer.save()
            return Response(result, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def create_tenant_role(self, request):
        """
        Create a custom role for a specific tenant.
        """
        tenant_id = request.data.get('tenant_id')
        role_data = request.data.get('role_data', {})

        if not tenant_id or not role_data:
            return Response(
                {'error': 'tenant_id and role_data are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Use the unified management endpoint
        management_data = {
            'tenant_id': tenant_id,
            'action': 'create_role',
            'role_data': role_data
        }

        serializer = TenantRoleManagementSerializer(data=management_data, context={'request': request})

        if serializer.is_valid():
            result = serializer.save()
            return Response(result, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def assign_role_in_tenant(self, request):
        """
        Assign a role to a user within a specific tenant context.
        """
        tenant_id = request.data.get('tenant_id')
        user_email = request.data.get('user_email')
        role_codename = request.data.get('role_codename')
        reason = request.data.get('reason', '')

        if not all([tenant_id, user_email, role_codename]):
            return Response(
                {'error': 'tenant_id, user_email, and role_codename are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Use the unified management endpoint
        management_data = {
            'tenant_id': tenant_id,
            'action': 'assign_role',
            'user_email': user_email,
            'role_codename': role_codename,
            'reason': reason
        }

        serializer = TenantRoleManagementSerializer(data=management_data, context={'request': request})

        if serializer.is_valid():
            result = serializer.save()
            return Response(result, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def grant_permission_in_tenant(self, request):
        """
        Grant additional permission to a user within a specific tenant context.
        """
        tenant_id = request.data.get('tenant_id')
        user_email = request.data.get('user_email')
        permission_codename = request.data.get('permission_codename')
        reason = request.data.get('reason', '')

        if not all([tenant_id, user_email, permission_codename]):
            return Response(
                {'error': 'tenant_id, user_email, and permission_codename are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Use the unified management endpoint
        management_data = {
            'tenant_id': tenant_id,
            'action': 'grant_permission',
            'user_email': user_email,
            'permission_codename': permission_codename,
            'reason': reason
        }

        serializer = TenantRoleManagementSerializer(data=management_data, context={'request': request})

        if serializer.is_valid():
            result = serializer.save()
            return Response(result, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
