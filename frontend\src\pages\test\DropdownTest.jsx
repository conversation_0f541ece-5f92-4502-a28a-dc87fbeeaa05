import { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Button,
  Alert,
  CircularProgress,
} from '@mui/material';
import { useSharedData } from '../../contexts/SharedDataContext';
import { useAuth } from '../../contexts/AuthContext';
import SharedDataDropdown from '../../components/common/SharedDataDropdown';
import { getStandardMenuProps, getSafeMaxHeight } from '../../utils/dropdownUtils';

const DropdownTest = () => {
  const [formData, setFormData] = useState({
    nationality: '',
    religion: '',
    citizen_status: '',
    marital_status: '',
    occupation: '',
    ketena: '',
    country: '',
    region: '',
    test_dropdown: '',
    simple_test: '',
    direct_test: '',
    hardcoded_test: '',
    api_test: '',
  });

  const [apiTestData, setApiTestData] = useState([]);
  const [apiTestLoading, setApiTestLoading] = useState(false);
  const [apiTestError, setApiTestError] = useState(null);

  const sharedData = useSharedData();
  const { user, isAuthenticated } = useAuth();

  // Direct API test
  const testDirectAPI = async () => {
    try {
      setApiTestLoading(true);
      setApiTestError(null);

      // Test direct fetch without axios instance
      const response = await fetch('http://localhost:8000/api/shared/countries/');
      const data = await response.json();

      console.log('Direct API test result:', data);
      setApiTestData(data || []);
    } catch (error) {
      console.error('Direct API test error:', error);
      setApiTestError(error.message);
    } finally {
      setApiTestLoading(false);
    }
  };

  useEffect(() => {
    testDirectAPI();
  }, []);

  const handleChange = (event) => {
    const { name, value } = event.target;
    console.log('Dropdown changed:', { name, value, event });
    console.log('Current formData before update:', formData);
    setFormData(prev => {
      const newData = {
        ...prev,
        [name]: value
      };
      console.log('New formData after update:', newData);
      return newData;
    });
  };



  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Dropdown Test Page
      </Typography>

      {sharedData.error && (
        <Alert
          severity="warning"
          sx={{ mb: 3 }}
          action={
            <Button
              color="inherit"
              size="small"
              onClick={sharedData.retryFetchSharedData}
              disabled={sharedData.loading}
            >
              {sharedData.loading ? <CircularProgress size={16} /> : 'Retry'}
            </Button>
          }
        >
          {sharedData.error}
        </Alert>
      )}

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Shared Data Status
          </Typography>
          <Typography variant="body2">
            Authenticated: {isAuthenticated ? 'Yes' : 'No'}
          </Typography>
          <Typography variant="body2">
            User: {user?.username || 'None'}
          </Typography>
          <Typography variant="body2">
            Loading: {sharedData.loading ? 'Yes' : 'No'}
          </Typography>
          <Typography variant="body2">
            Error: {sharedData.error || 'None'}
          </Typography>
          <Typography variant="body2">
            Countries: {sharedData.countries?.length || 0}
          </Typography>
          <Typography variant="body2">
            Religions: {sharedData.religions?.length || 0}
          </Typography>
          <Typography variant="body2">
            Citizen Statuses: {sharedData.citizenStatuses?.length || 0}
          </Typography>
          <Typography variant="body2">
            Marital Statuses: {sharedData.maritalStatuses?.length || 0}
          </Typography>
          <Typography variant="body2">
            Employment Types: {sharedData.employmentTypes?.length || 0}
          </Typography>
          <Typography variant="body2">
            Ketenas: {sharedData.ketenas?.length || 0}
          </Typography>
          <Typography variant="body2">
            Relationships: {sharedData.relationships?.length || 0}
          </Typography>
          <Typography variant="body2">
            Document Types: {sharedData.documentTypes?.length || 0}
          </Typography>
          <Typography variant="body2">
            Biometric Types: {sharedData.biometricTypes?.length || 0}
          </Typography>
          <Typography variant="body2">
            Current Statuses: {sharedData.currentStatuses?.length || 0}
          </Typography>

          <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 1 }}>
              DEBUG: Raw SharedData Object
            </Typography>
            <Typography variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.75rem' }}>
              {JSON.stringify(sharedData, null, 2)}
            </Typography>
          </Box>

          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" sx={{ mb: 1 }}>
              Sample Countries: {JSON.stringify(sharedData.countries?.slice(0, 2) || [])}
            </Typography>
            <Typography variant="body2" sx={{ mb: 1 }}>
              Sample Religions: {JSON.stringify(sharedData.religions?.slice(0, 2) || [])}
            </Typography>
            <Typography variant="body2" sx={{ mb: 1 }}>
              Data Loading Debug: Loading={String(sharedData.loading)}, Error={sharedData.error || 'None'}
            </Typography>
            <Typography variant="body2" sx={{ mb: 1 }}>
              Auth Debug: Authenticated={String(isAuthenticated)}, User={user?.username || 'None'}
            </Typography>
            <Button
              variant="outlined"
              onClick={sharedData.retryFetchSharedData}
              disabled={sharedData.loading}
              size="small"
            >
              {sharedData.loading ? <CircularProgress size={16} /> : 'Refresh Data'}
            </Button>
          </Box>
        </CardContent>
      </Card>

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Form Data
          </Typography>
          <pre>{JSON.stringify(formData, null, 2)}</pre>
        </CardContent>
      </Card>

      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Dropdown Tests
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <SharedDataDropdown
                dataType="countries"
                label="Nationality"
                name="nationality"
                value={formData.nationality}
                onChange={handleChange}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                Direct Test
              </Typography>
              <FormControl fullWidth>
                <InputLabel>Direct Countries Test</InputLabel>
                <Select
                  name="direct_test"
                  value={formData.direct_test || ''}
                  onChange={handleChange}
                  label="Direct Countries Test"
                >
                  {sharedData.countries?.map((country) => (
                    <MenuItem key={country.id} value={country.id}>
                      {country.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>



            <Grid item xs={12} md={6}>
              <SharedDataDropdown
                dataType="religions"
                label="Religion"
                name="religion"
                id="religion"
                value={formData.religion}
                onChange={handleChange}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <SharedDataDropdown
                dataType="citizenStatuses"
                label="Citizen Status"
                name="citizen_status"
                id="citizen_status"
                value={formData.citizen_status}
                onChange={handleChange}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <SharedDataDropdown
                dataType="maritalStatuses"
                label="Marital Status"
                name="marital_status"
                id="marital_status"
                value={formData.marital_status}
                onChange={handleChange}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <SharedDataDropdown
                dataType="employmentTypes"
                label="Occupation"
                name="occupation"
                id="occupation"
                value={formData.occupation}
                onChange={handleChange}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <SharedDataDropdown
                dataType="ketenas"
                label="Ketena"
                name="ketena"
                id="ketena"
                value={formData.ketena}
                onChange={handleChange}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <SharedDataDropdown
                dataType="countries"
                label="Country"
                name="country"
                id="country"
                value={formData.country}
                onChange={handleChange}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <SharedDataDropdown
                dataType="regions"
                label="Region"
                name="region"
                id="region"
                value={formData.region}
                onChange={handleChange}
                filterBy="country"
                filterValue={formData.country}
                disabled={!formData.country}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                Hardcoded Test
              </Typography>
              <FormControl fullWidth>
                <InputLabel>Hardcoded Test</InputLabel>
                <Select
                  name="hardcoded_test"
                  value={formData.hardcoded_test || ''}
                  onChange={handleChange}
                  label="Hardcoded Test"
                >
                  <MenuItem value="option1">Option 1</MenuItem>
                  <MenuItem value="option2">Option 2</MenuItem>
                  <MenuItem value="option3">Option 3</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                Direct API Test
              </Typography>
              <FormControl fullWidth>
                <InputLabel>Direct API Countries</InputLabel>
                <Select
                  name="api_test"
                  value={formData.api_test || ''}
                  onChange={handleChange}
                  label="Direct API Countries"
                  disabled={apiTestLoading}
                >
                  {apiTestLoading ? (
                    <MenuItem disabled>Loading...</MenuItem>
                  ) : apiTestError ? (
                    <MenuItem disabled>Error: {apiTestError}</MenuItem>
                  ) : (
                    apiTestData.map((country) => (
                      <MenuItem key={country.id} value={country.id}>
                        {country.name}
                      </MenuItem>
                    ))
                  )}
                </Select>
              </FormControl>
            </Grid>




          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
};

export default DropdownTest;
