# Generated manually for tenant-aware roles system

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0003_add_dynamic_roles'),
        ('tenants', '0001_initial'),  # Assuming tenants app has initial migration
    ]

    operations = [
        migrations.AddField(
            model_name='role',
            name='scope_tenant',
            field=models.ForeignKey(blank=True, help_text='Tenant that owns this role (for tenant-specific roles)', null=True, on_delete=django.db.models.deletion.CASCADE, to='tenants.tenant'),
        ),
        migrations.AddField(
            model_name='role',
            name='is_global',
            field=models.BooleanField(default=True, help_text='True for system-wide roles, False for tenant-specific roles'),
        ),
        migrations.AlterField(
            model_name='role',
            name='codename',
            field=models.CharField(help_text='Unique role identifier', max_length=100, unique=True),
        ),
    ]
