@echo off
echo ========================================
echo   Installing Waitress WSGI Server
echo ========================================
echo.
echo Waitress is a production-ready WSGI server
echo that should be more stable than Flask's
echo development server for biometric capture.
echo.

echo 🔧 Installing waitress...
pip install waitress

echo.
echo ✅ Waitress installation complete!
echo.
echo Now you can run the kebele service with:
echo start_kebele_biometric_service.bat
echo.
pause
