#!/usr/bin/env python
"""
Simple script to seed ID card templates for all tenants.
Run this script from the backend directory: python seed_templates.py
"""

import os
import sys
import django

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django_tenants.utils import schema_context
from tenants.models import Tenant
from idcards.models import IDCardTemplate


def seed_templates(force=False):
    """Seed ID card templates for all tenants."""
    print("🌱 Starting template seeding for all tenants...")

    if force:
        print("⚠️  Force mode enabled - will recreate existing templates")

    # Get all tenants
    tenants = Tenant.objects.all()
    print(f"📋 Found {tenants.count()} tenants")

    success_count = 0
    error_count = 0

    for tenant in tenants:
        print(f"\n🏢 Processing tenant: {tenant.name} ({tenant.schema_name})")

        try:
            with schema_context(tenant.schema_name):
                # Check if templates already exist
                existing_count = IDCardTemplate.objects.count()
                if existing_count > 0 and not force:
                    print(f"  ✅ Templates already exist ({existing_count} templates), skipping...")
                    continue

                if force and existing_count > 0:
                    print(f"  🗑️  Deleting {existing_count} existing templates...")
                    IDCardTemplate.objects.all().delete()

                # Create default template (Government Standard)
                default_template = IDCardTemplate.objects.create(
                    name="Government Standard Template",
                    description="Official government ID card template with standard styling",
                    background_color="#FFFFFF",
                    text_color="#000000",
                    accent_color="#1976D2",
                    logo_position="top_left",
                    photo_position="left",
                    header_text="Federal Democratic Republic of Ethiopia",
                    subtitle_text="National ID Card",
                    footer_text="Ministry of Interior",
                    is_active=True,
                    is_default=True
                )
                print(f"  ✅ Created: {default_template.name}")

                # Create modern template (Alternative)
                modern_template = IDCardTemplate.objects.create(
                    name="Modern Digital Template",
                    description="Modern digital ID card template with contemporary design",
                    background_color="#F8F9FA",
                    text_color="#212121",
                    accent_color="#2196F3",
                    logo_position="top_center",
                    photo_position="right",
                    header_text="Federal Democratic Republic of Ethiopia",
                    subtitle_text="Digital Identity Card",
                    footer_text="Digitally Verified",
                    is_active=True,
                    is_default=False
                )
                print(f"  ✅ Created: {modern_template.name}")

                total_created = IDCardTemplate.objects.count()
                print(f"  🎉 Successfully created {total_created} templates for {tenant.name}")
                success_count += 1

        except Exception as e:
            print(f"  ❌ Error processing tenant {tenant.name}: {str(e)}")
            error_count += 1
            continue

    print(f"\n📊 Summary:")
    print(f"  ✅ Successfully processed: {success_count} tenants")
    if error_count > 0:
        print(f"  ❌ Errors: {error_count} tenants")

    print("\n🎉 Template seeding completed!")

    # Print detailed summary for each tenant
    print("\n📋 Template Status by Tenant:")
    for tenant in tenants:
        try:
            with schema_context(tenant.schema_name):
                count = IDCardTemplate.objects.count()
                default_template = IDCardTemplate.objects.filter(is_default=True).first()
                print(f"  {tenant.name}: {count} templates (Default: {default_template.name if default_template else 'None'})")
        except Exception as e:
            print(f"  {tenant.name}: Error - {str(e)}")

    print("\n✅ All tenants now have default ID card templates!")


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='Seed ID card templates for all tenants')
    parser.add_argument('--force', action='store_true', help='Force recreation of templates even if they already exist')

    args = parser.parse_args()
    seed_templates(force=args.force)
