#!/usr/bin/env python3
"""
Simple Reliable Futronic SDK Service
Direct SDK integration with robust error handling
"""

import logging
import ctypes
from ctypes import cdll, c_int, c_uint, c_void_p, c_ubyte, POINTER, byref
import time
import base64
import json
import os
import sys
from datetime import datetime
from typing import Optional, Dict, Any
from flask import Flask, jsonify, request
from flask_cors import CORS

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('simple_reliable_sdk_service.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app, origins="*")

# Futronic SDK Constants
FTR_OK = 0
FTR_ERROR_EMPTY_FRAME = 1
FTR_ERROR_FAKE_FINGER = 2
FTR_ERROR_NO_FRAME = 3

# Image specifications
FTR_IMAGE_WIDTH = 320
FTR_IMAGE_HEIGHT = 480
FTR_IMAGE_SIZE = FTR_IMAGE_WIDTH * FTR_IMAGE_HEIGHT

class SimpleReliableSDKManager:
    """Simple and reliable SDK manager"""
    
    def __init__(self):
        self.scan_api = None
        self.device_handle = None
        self.is_initialized = False
        self.last_error = ""
        
        logger.info("Initializing Simple Reliable SDK Manager")
        self._load_sdk()
    
    def _load_sdk(self):
        """Load SDK with comprehensive error handling"""
        try:
            # Check if DLL exists
            dll_path = os.path.abspath('./ftrScanAPI.dll')
            if not os.path.exists(dll_path):
                self.last_error = f"ftrScanAPI.dll not found at: {dll_path}"
                logger.error(f"ERROR: {self.last_error}")
                return False
            
            logger.info(f"Loading SDK from: {dll_path}")
            
            # Load the DLL
            self.scan_api = cdll.LoadLibrary(dll_path)
            
            # Configure function signatures
            self._configure_api()
            
            logger.info("SUCCESS: SDK loaded successfully")
            return True
            
        except Exception as e:
            self.last_error = f"SDK loading failed: {e}"
            logger.error(f"ERROR: {self.last_error}")
            return False
    
    def _configure_api(self):
        """Configure API function signatures"""
        try:
            # Device management
            self.scan_api.ftrScanOpenDevice.restype = c_void_p
            self.scan_api.ftrScanOpenDevice.argtypes = []
            
            self.scan_api.ftrScanCloseDevice.restype = c_int
            self.scan_api.ftrScanCloseDevice.argtypes = [c_void_p]
            
            # Image capture
            self.scan_api.ftrScanGetFrame.restype = c_int
            self.scan_api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(c_ubyte), POINTER(c_uint)]
            
            logger.info("SUCCESS: API configured successfully")
            
        except Exception as e:
            logger.error(f"ERROR: API configuration failed: {e}")
            raise
    
    def initialize_device(self) -> bool:
        """Initialize device with retry logic"""
        try:
            if not self.scan_api:
                if not self._load_sdk():
                    return False
            
            logger.info("Initializing device...")
            
            # Close any existing connection
            if self.device_handle:
                try:
                    self.scan_api.ftrScanCloseDevice(self.device_handle)
                except:
                    pass
                self.device_handle = None
            
            # Open new connection
            self.device_handle = self.scan_api.ftrScanOpenDevice()
            
            if self.device_handle:
                self.is_initialized = True
                logger.info(f"SUCCESS: Device initialized - Handle: {self.device_handle}")
                return True
            else:
                self.last_error = "Failed to open device connection"
                logger.error(f"ERROR: {self.last_error}")
                return False
                
        except Exception as e:
            self.last_error = f"Device initialization error: {e}"
            logger.error(f"ERROR: {self.last_error}")
            return False
    
    def get_device_status(self) -> Dict[str, Any]:
        """Get device status"""
        return {
            'connected': self.is_initialized,
            'initialized': self.is_initialized,
            'device_available': self.is_initialized,
            'handle': self.device_handle,
            'model': 'Futronic FS88H',
            'serial_number': f'Handle_{self.device_handle}' if self.device_handle else '',
            'firmware_version': '',
            'sdk_version': 'Simple Reliable',
            'interface': 'USB',
            'status': 'Connected' if self.is_initialized else 'Disconnected',
            'math_api_available': False,
            'last_error': self.last_error,
            'capabilities': {
                'image_capture': True,
                'template_extraction': False,
                'template_matching': False,
                'live_finger_detection': False
            }
        }
    
    def capture_fingerprint(self, thumb_type: str) -> Optional[Dict[str, Any]]:
        """Capture fingerprint with multiple attempts"""
        try:
            logger.info(f"Capturing {thumb_type} thumb fingerprint...")
            
            # Ensure device is initialized
            if not self.is_initialized:
                if not self.initialize_device():
                    return None
            
            # Multiple capture attempts
            max_attempts = 5
            for attempt in range(max_attempts):
                logger.info(f"Capture attempt {attempt + 1}/{max_attempts}")
                
                try:
                    # Create buffer
                    buffer_size = FTR_IMAGE_SIZE * 2
                    image_buffer = (c_ubyte * buffer_size)()
                    frame_size = c_uint(buffer_size)
                    
                    # Attempt capture
                    result = self.scan_api.ftrScanGetFrame(self.device_handle, image_buffer, byref(frame_size))
                    
                    logger.info(f"Capture result: {result}, Frame size: {frame_size.value}")
                    
                    if result == FTR_OK:
                        actual_size = frame_size.value
                        if actual_size > 0:
                            # Extract image data
                            image_data = bytes(image_buffer[:actual_size])
                            
                            # Calculate quality
                            quality_score = self._calculate_quality(image_data)
                            
                            # Estimate minutiae
                            minutiae_count = self._estimate_minutiae(image_data)
                            
                            # Create template data
                            template_dict = {
                                'version': '3.0',
                                'thumb_type': thumb_type,
                                'image_data': base64.b64encode(image_data).decode(),
                                'image_width': FTR_IMAGE_WIDTH,
                                'image_height': FTR_IMAGE_HEIGHT,
                                'image_dpi': 500,
                                'quality_score': quality_score,
                                'capture_time': datetime.now().isoformat(),
                                'device_info': {
                                    'model': 'Futronic FS88H',
                                    'serial_number': f'Handle_{self.device_handle}',
                                    'sdk_version': 'Simple Reliable',
                                    'interface': 'USB'
                                },
                                'processing_method': 'simple_reliable_sdk',
                                'has_template': False,
                                'has_image': True
                            }
                            
                            template_data = base64.b64encode(json.dumps(template_dict).encode()).decode()
                            
                            result_data = {
                                'thumb_type': thumb_type,
                                'template_data': template_data,
                                'minutiae_count': minutiae_count,
                                'quality_score': quality_score,
                                'capture_time': datetime.now().isoformat(),
                                'device_info': {
                                    'model': 'Futronic FS88H',
                                    'serial_number': f'Handle_{self.device_handle}',
                                    'sdk_version': 'Simple Reliable',
                                    'interface': 'USB'
                                },
                                'image_data': base64.b64encode(image_data).decode()
                            }
                            
                            logger.info(f"SUCCESS: {thumb_type} thumb captured - Quality: {quality_score}%, Size: {actual_size} bytes")
                            return result_data
                        else:
                            logger.warning(f"Attempt {attempt + 1}: Empty frame")
                    
                    elif result == FTR_ERROR_EMPTY_FRAME:
                        logger.info(f"Attempt {attempt + 1}: No finger detected")
                        time.sleep(1)
                        
                    elif result == FTR_ERROR_NO_FRAME:
                        logger.info(f"Attempt {attempt + 1}: No frame available")
                        time.sleep(0.5)
                        
                    else:
                        logger.warning(f"Attempt {attempt + 1}: Capture failed with code {result}")
                        time.sleep(0.5)
                
                except Exception as capture_error:
                    logger.warning(f"Attempt {attempt + 1} exception: {capture_error}")
                    if attempt < max_attempts - 1:
                        time.sleep(1)
                        continue
            
            # All attempts failed
            self.last_error = "All capture attempts failed - please place finger firmly on scanner"
            logger.error(f"ERROR: {self.last_error}")
            return None
            
        except Exception as e:
            self.last_error = f"Fingerprint capture error: {e}"
            logger.error(f"ERROR: {self.last_error}")
            return None
    
    def _calculate_quality(self, image_data: bytes) -> int:
        """Calculate basic image quality"""
        try:
            if not image_data:
                return 0
            
            non_zero_pixels = sum(1 for b in image_data if b > 10)
            total_pixels = len(image_data)
            
            if total_pixels == 0:
                return 0
            
            meaningful_ratio = non_zero_pixels / total_pixels
            avg_intensity = sum(image_data) / total_pixels
            quality_score = int((meaningful_ratio * 50) + (avg_intensity / 255 * 50))
            
            return max(0, min(100, quality_score))
            
        except Exception:
            return 50
    
    def _estimate_minutiae(self, image_data: bytes) -> int:
        """Estimate minutiae count"""
        try:
            if not image_data:
                return 0
            
            unique_values = len(set(image_data))
            complexity_ratio = unique_values / 256
            estimated_count = int(complexity_ratio * 60) + 20
            
            return min(80, max(20, estimated_count))
            
        except Exception:
            return 40
    
    def close_device(self):
        """Close device connection"""
        try:
            if self.device_handle and self.scan_api:
                logger.info("Closing device connection...")
                self.scan_api.ftrScanCloseDevice(self.device_handle)
                self.device_handle = None
                self.is_initialized = False
                logger.info("SUCCESS: Device closed")
        except Exception as e:
            logger.error(f"ERROR: Error closing device: {e}")

# Global SDK manager
sdk_manager = SimpleReliableSDKManager()

# Flask API Endpoints

@app.route('/api/device/status', methods=['GET'])
def get_device_status():
    """Get device status"""
    try:
        status = sdk_manager.get_device_status()
        logger.info(f"Device status requested - Connected: {status['connected']}")
        
        return jsonify({
            'success': True,
            'data': status
        })
        
    except Exception as e:
        logger.error(f"ERROR: Status check error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/device/initialize', methods=['POST'])
def initialize_device():
    """Initialize device"""
    try:
        logger.info("Device initialization requested")
        
        success = sdk_manager.initialize_device()
        
        if success:
            return jsonify({
                'success': True,
                'message': 'Device initialized successfully',
                'data': sdk_manager.get_device_status()
            })
        else:
            return jsonify({
                'success': False,
                'error': sdk_manager.last_error
            }), 500
            
    except Exception as e:
        logger.error(f"ERROR: Device initialization error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/capture/fingerprint', methods=['POST'])
def capture_fingerprint():
    """Capture fingerprint"""
    try:
        data = request.get_json() or {}
        thumb_type = data.get('thumb_type', 'left')

        if thumb_type not in ['left', 'right']:
            return jsonify({
                'success': False,
                'error': 'thumb_type must be "left" or "right"'
            }), 400

        logger.info(f"API: {thumb_type} thumb capture request")

        # Ensure device is initialized
        if not sdk_manager.is_initialized:
            if not sdk_manager.initialize_device():
                return jsonify({
                    'success': False,
                    'error': 'Device initialization failed'
                }), 500

        # Capture fingerprint
        template = sdk_manager.capture_fingerprint(thumb_type)

        if template:
            response_data = {
                'success': True,
                'data': {
                    'thumb_type': template['thumb_type'],
                    'template_data': template['template_data'],
                    'quality_score': template['quality_score'],
                    'quality_valid': template['quality_score'] >= 75,
                    'quality_message': f'Quality score: {template["quality_score"]}%',
                    'minutiae_count': template['minutiae_count'],
                    'capture_time': template['capture_time'],
                    'device_info': template['device_info'],
                    'has_real_fingerprint': True,
                    'processing_method': 'simple_reliable_sdk'
                }
            }

            logger.info(f"SUCCESS: {thumb_type} thumb capture successful")
            return jsonify(response_data)
        else:
            logger.warning(f"WARNING: {thumb_type} thumb capture failed")
            return jsonify({
                'success': False,
                'error': sdk_manager.last_error or 'Fingerprint capture failed'
            }), 500

    except Exception as e:
        logger.error(f"ERROR: API capture error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Service health check"""
    return jsonify({
        'status': 'healthy',
        'service': 'Simple Reliable Futronic SDK Service',
        'version': '1.0.0',
        'timestamp': datetime.now().isoformat(),
        'sdk_status': {
            'sdk_loaded': sdk_manager.scan_api is not None,
            'device_initialized': sdk_manager.is_initialized,
            'simple_reliable': True
        }
    })

@app.route('/', methods=['GET'])
def index():
    """Service status page"""
    device_status = sdk_manager.get_device_status()

    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Simple Reliable Futronic SDK Service</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }}
            .container {{ max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }}
            .status-card {{ background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 5px; }}
            .btn {{ padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; background: #007bff; color: white; }}
            .success {{ color: #28a745; }}
            .error {{ color: #dc3545; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>Simple Reliable Futronic SDK Service</h1>
            <p>Direct SDK integration with robust error handling</p>

            <div class="status-card">
                <h3>Device Status</h3>
                <p>Connection: <span class="{'success' if device_status['connected'] else 'error'}">{'Connected' if device_status['connected'] else 'Disconnected'}</span></p>
                <p>Model: {device_status['model']}</p>
                <p>Handle: {device_status['handle'] or 'N/A'}</p>
                <p>SDK Version: {device_status['sdk_version']}</p>
            </div>

            <div class="status-card">
                <h3>Service Information</h3>
                <p>SDK Loaded: <span class="success">Yes</span></p>
                <p>Math API: <span class="error">Not Required</span></p>
                <p>Image Capture: <span class="success">Supported</span></p>
                <p>Last Error: {device_status['last_error'] or 'None'}</p>
            </div>

            <div style="text-align: center;">
                <h3>Test Fingerprint Capture</h3>
                <button class="btn" onclick="testCapture('left')">Test Left Thumb</button>
                <button class="btn" onclick="testCapture('right')">Test Right Thumb</button>
                <button class="btn" onclick="initializeDevice()">Initialize Device</button>
            </div>
        </div>

        <script>
            async function testCapture(thumbType) {{
                const button = event.target;
                const originalText = button.textContent;

                button.textContent = 'Capturing...';
                button.disabled = true;

                try {{
                    const response = await fetch('/api/capture/fingerprint', {{
                        method: 'POST',
                        headers: {{ 'Content-Type': 'application/json' }},
                        body: JSON.stringify({{ thumb_type: thumbType }})
                    }});

                    const result = await response.json();

                    if (result.success) {{
                        alert('SUCCESS: ' + thumbType + ' thumb captured!\\n\\nQuality Score: ' + result.data.quality_score + '%\\nMinutiae Count: ' + result.data.minutiae_count);
                    }} else {{
                        alert('ERROR: Capture failed\\n\\n' + result.error);
                    }}
                }} catch (error) {{
                    alert('ERROR: Network error\\n\\n' + error.message);
                }} finally {{
                    button.textContent = originalText;
                    button.disabled = false;
                }}
            }}

            async function initializeDevice() {{
                const button = event.target;
                button.textContent = 'Initializing...';
                button.disabled = true;

                try {{
                    const response = await fetch('/api/device/initialize', {{
                        method: 'POST',
                        headers: {{ 'Content-Type': 'application/json' }}
                    }});

                    const result = await response.json();

                    if (result.success) {{
                        alert('SUCCESS: Device initialized successfully!');
                        location.reload();
                    }} else {{
                        alert('ERROR: Device initialization failed\\n\\n' + result.error);
                    }}
                }} catch (error) {{
                    alert('ERROR: Network error\\n\\n' + error.message);
                }} finally {{
                    button.textContent = 'Initialize Device';
                    button.disabled = false;
                }}
            }}
        </script>
    </body>
    </html>
    """

    return html_content

def cleanup_service():
    """Cleanup service resources"""
    try:
        logger.info("Cleaning up service resources...")
        sdk_manager.close_device()
        logger.info("SUCCESS: Service cleanup completed")
    except Exception as e:
        logger.error(f"ERROR: Cleanup error: {e}")

if __name__ == '__main__':
    try:
        logger.info("Starting Simple Reliable Futronic SDK Service")
        logger.info("Direct SDK integration with robust error handling")

        # Initialize device on startup
        if sdk_manager.initialize_device():
            logger.info("SUCCESS: Device ready for operation")
        else:
            logger.warning("WARNING: Device not available - service will attempt initialization on first request")

        logger.info("Service available at http://localhost:8001")
        logger.info("Simple Reliable SDK integration active")

        # Start Flask service
        app.run(host='0.0.0.0', port=8001, debug=False, threaded=True, use_reloader=False)

    except KeyboardInterrupt:
        logger.info("Service shutdown requested")
    except Exception as e:
        logger.error(f"ERROR: Service error: {e}")
    finally:
        cleanup_service()
        logger.info("Simple Reliable SDK Service stopped")
