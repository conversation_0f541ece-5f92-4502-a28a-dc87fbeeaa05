@echo off
echo 🚀 GoID Production Deployment
echo ============================
echo.

REM Check if Docker is running
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running. Please start Docker Desktop.
    pause
    exit /b 1
)

echo ✅ Docker is running
echo.

REM Copy environment file if it doesn't exist
if not exist .env (
    echo 📋 Creating environment file...
    copy .env.production .env
    echo ✅ Environment file created with default settings
    echo.
) else (
    echo ✅ Using existing .env file
)

REM Stop any existing containers
echo 🛑 Stopping existing containers...
docker-compose -f docker-compose.production.yml down

REM Build and start services
echo 🔨 Building and starting services...
docker-compose -f docker-compose.production.yml up -d --build

REM Wait for services to start
echo ⏳ Waiting for services to start...
timeout /t 30 /nobreak >nul

REM Run database migrations
echo 🗄️ Running database migrations...
docker-compose -f docker-compose.production.yml exec -T backend python manage.py migrate

REM Create superuser (optional)
echo 👤 Creating superuser (if not exists)...
docker-compose -f docker-compose.production.yml exec -T backend python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(email='<EMAIL>').exists():
    User.objects.create_superuser('<EMAIL>', 'admin123')
    print('Superuser created: <EMAIL> / admin123')
else:
    print('Superuser already exists')
"

REM Show status
echo 🔍 Service Status:
docker-compose -f docker-compose.production.yml ps

echo.
echo ✅ Deployment Complete!
echo ======================
echo.
echo 🌐 Frontend: http://localhost:3000
echo 🔧 Backend API: http://localhost:8000
echo 🗄️ pgAdmin: http://localhost:5050
echo 👤 Admin: <EMAIL> / admin123
echo 👤 pgAdmin: <EMAIL> / admin123
echo.
echo 📋 Next Steps:
echo 1. Install biometric service on workstations
echo 2. Test citizen registration with biometric capture
echo 3. Change default admin password
echo 4. Configure database connection in pgAdmin
echo.
pause
