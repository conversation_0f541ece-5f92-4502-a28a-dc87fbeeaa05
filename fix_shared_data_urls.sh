#!/bin/bash

# Fix Shared Data URLs 404 Error <PERSON>
# This script fixes the duplicate "tenants" in shared data service URLs

echo "🔧 Fixing Shared Data URLs 404 Errors"
echo "======================================"

# Step 1: Test current shared data endpoints
echo "📍 Step 1: Testing current shared data endpoints..."
python test_shared_data_endpoints.py

# Step 2: Restart frontend service to apply URL changes
echo ""
echo "📍 Step 2: Restarting frontend service to apply URL changes..."
docker-compose restart frontend

# Step 3: Wait for service to restart
echo "⏳ Waiting for frontend to restart..."
sleep 20

# Step 4: Test again
echo ""
echo "📍 Step 3: Testing after frontend restart..."
python test_shared_data_endpoints.py

# Step 5: Clear browser cache recommendation
echo ""
echo "📍 Step 4: Browser cache clearing recommendation..."
echo "⚠️  Important: Clear your browser cache and reload the page"
echo "   - Press Ctrl+Shift+R (or Cmd+Shift+R on Mac) to hard refresh"
echo "   - Or open Developer Tools (F12) and right-click refresh button → 'Empty Cache and Hard Reload'"

echo ""
echo "✅ Shared data URLs fix completed!"
echo ""
echo "🔍 Manual testing:"
echo "1. Open frontend: http://10.139.8.141:3000/"
echo "2. Check browser console (F12) for any remaining URL errors"
echo "3. Navigate to forms that use dropdowns (citizen registration, etc.)"
echo "4. Verify dropdowns load data without 404 errors"
echo ""
echo "🌐 Fixed URLs:"
echo "   Before: /api/tenants/tenants/subcities/ (❌ double tenants)"
echo "   After:  /api/tenants/subcities/ (✅ correct)"
echo ""
echo "   Before: /api/tenants/tenants/kebeles/ (❌ double tenants)"
echo "   After:  /api/tenants/kebeles/ (✅ correct)"
echo ""
echo "💡 If still seeing 404 errors:"
echo "- Check browser console for cached requests"
echo "- Clear browser cache completely"
echo "- Restart both frontend and backend: docker-compose restart"
echo "- Verify backend URL patterns are correct"
