@echo off
echo ========================================
echo    REAL FINGERPRINT CAPTURE TEST
echo ========================================
echo.
echo GOAL: CAPTURE ACTUAL FINGERPRINT DATA
echo.
echo WHAT THIS TESTS:
echo   - Real fingerprint image capture using ftrScanGetFrame
echo   - Actual fingerprint data (not simulation)
echo   - Save captured data to file for verification
echo   - Determine if real capture is possible in Python
echo.
echo INSTRUCTIONS:
echo   1. Place your finger on the Futronic FS88H scanner
echo   2. Press Enter when ready
echo   3. Keep finger steady during 3-second countdown
echo   4. Check if real fingerprint data is captured
echo.
echo POSSIBLE OUTCOMES:
echo   SUCCESS: Real fingerprint data captured and saved to file
echo   CRASH: ftrScanGetFrame crashes Python (compatibility issue)
echo   ERROR: Function fails with error code (finger placement issue)
echo.

python real_capture_test.py

echo.
echo Real capture test completed.
pause
